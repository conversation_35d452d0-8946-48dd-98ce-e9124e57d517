<template>
	<div class="comparison_box">
		<div class="container_box">
			<div class="table_main">
				<div class="table_">
					<div class="top_boxFirst">
						<div class="tag_boxTitle">对比资产一</div>
						<div class="tag_boxCenter">
							<div class="tag_boxRight" v-if="tableDatao[0]" @click="clearDate(tableDatao[0], 1)">× 清空</div>
							<!-- <div class="tag_boxLeft" v-if="tableDatao.length > 0" @click="handleDownload(tableDatao[0].id, 'location')">
								<el-icon style="margin-right: 2px"><Download /></el-icon>市调报告
							</div>
							<div class="tag_boxLeft" v-if="tableDatao.length > 0" @click="handleDownload(tableDatao[0].id, 'populationtwo')">
								<el-icon style="margin-right: 2px"><Download /></el-icon>社区人口报告
							</div> -->
						</div>
					</div>
					<div class="table_1" v-if="tableDatao[0]">
						<el-table border :data="tableDatao" height="80px" style="width: 100%">
							<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
							<el-table-column prop="buildingType" label="资产类型" width="" />
							<el-table-column prop="street" label="地址" show-overflow-tooltip>
								<template #default="scope">
									<!-- {{ scope.row.province ? scope.row.province : '' }} -->
									{{ scope.row?.city ? scope.row.city : '' }}
									{{ scope.row?.district ? scope.row.district : '' }}
									{{ scope.row?.street ? scope.row.street : '' }}
								</template>
							</el-table-column>
							<el-table-column prop="buildingSize" label="建筑面积" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
									</div>
								</template>
							</el-table-column>
							<el-table-column prop="maintenance" label="维护情况" width="" />
							<el-table-column prop="absoluteValue" label="单价" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="add active" @click="choose(1)" v-else>+ 选择对比资产</div>
				</div>
				<div class="table_">
					<div class="top_box">
						<div class="top_boxFirst">
							<div class="tag_boxTitle">对比资产二</div>
							<div class="tag_boxCenter">
								<div class="tag_boxRight" v-if="tableDatat[0]" @click="clearDate(tableDatat[0], 2)">× 清空</div>
								<!-- <div class="tag_boxLeft" v-if="tableDatat.length > 0" @click="handleDownload(tableDatat[0].id, 'location')">
									<el-icon style="margin-right: 2px"><Download /></el-icon>市调报告
								</div>
								<div class="tag_boxLeft" v-if="tableDatat.length > 0" @click="handleDownload(tableDatat[0].id, 'populationtwo')">
									<el-icon style="margin-right: 2px"><Download /></el-icon>社区人口报告
								</div> -->
							</div>
						</div>
						<div class="table_1" v-if="tableDatat[0]">
							<el-table :data="tableDatat" border height="80px" style="width: 100%">
								<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
								<el-table-column prop="buildingType" label="资产类型" width="" />
								<el-table-column prop="street" label="地址" show-overflow-tooltip>
									<template #default="scope">
										<!-- {{ scope.row.province ? scope.row.province : '' }} -->
										{{ scope.row?.city ? scope.row.city : '' }}
										{{ scope.row?.district ? scope.row.district : '' }}
										{{ scope.row?.street ? scope.row.street : '' }}
									</template>
								</el-table-column>
								<el-table-column prop="buildingSize" label="建筑面积" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="maintenance" label="维护情况" width="" />
								<el-table-column prop="absoluteValue" label="单价" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="add active" @click="choose(2)" v-else>+ 选择对比资产</div>
					</div>
				</div>
			</div>

			<!-- 对比图 -->
			<div class="echars_box">
				<div class="echars_main">
					<div class="title1">位置分析</div>
					<div class="box_">
						<div class="tag_box">周边设施</div>
						<div style="display: flex">
							<div
								class="box_left"
								v-for="(item, index) in sixRingDateList"
								:style="`${sixRingDateList.length > 1 ? 'width: 50%' : 'width: 100%'}`"
								:key="index"
							>
								<div class="img_box">
									<div class="img_left">
										<rat
											class="rat"
											v-if="tableDatao[0]?.id == item?.id || tableDatat[0]?.id == item?.id"
											:containerIds="'contianers' + item?.id + index"
											:polygonArr="item.districtsList.businessCoordinateList"
											style="width: 100%; height: 180px; border-radius: 4px"
										></rat>
										<div
											style="position: absolute; top: 10px; left: 10px; font-weight: 700"
											:style="`${index == 0 ? 'color: rgb(24, 144, 255);' : 'color: rgb(47, 194, 91)'}`"
										>
											{{ item.districtsList?.businessDistrictName }}
										</div>
									</div>

									<div class="faciltyClass">
										<div
											v-for="(infoItem, infoIndex) in item.districtsList?.info"
											:class="item.districtsList.infoActiveIndex === infoIndex ? 'faciltyItemActive faciltyItem' : 'faciltyItem'"
											@click="handleFacilty(infoIndex, item.districtsList)"
											:key="infoIndex"
										>
											<div class="faciltyImg">
												<img v-if="item.districtsList.infoActiveIndex === infoIndex" src="@/assets/faciltyImg.png" alt="" />
											</div>
											<div>{{ infoItem.type }}</div>
										</div>
									</div>

									<div class="btnBoxPriceStatisticsDoms">
										<div style="width: -webkit-fill-available" class="btn_boxDetails">
											{{ item.districtsList.info?.[item.districtsList.infoActiveIndex]?.allName }}
										</div>
										<div class="box_copyContent" style="margin-top: -2px">
											<div
												@click="handlerCopy(item.districtsList.info?.[item.districtsList.infoActiveIndex]?.allName)"
												v-if="item.districtsList.info?.[item.districtsList.infoActiveIndex]?.allName"
											>
												复制
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="box_">
						<div class="tag_box">商圈介绍</div>
						<div style="display: flex">
							<div
								class="box_left"
								:style="`${sixRingDateList.length > 1 ? 'width: 50%' : 'width: 100%'}`"
								v-for="(item, index) in sixRingDateList"
								:key="index"
							>
								<div class="img_box btnBoxPriceStatisticsDoms">
									<div style="width: -webkit-fill-available" class="btn_boxDetails">
										{{ item.districtsList.range }}
									</div>
									<div class="box_copyContent">
										<div @click="handlerCopy(item.districtsList.range)" v-if="item.districtsList.range">复制</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="box_">
						<div class="tag_box">商圈实拍</div>
						<div style="display: flex">
							<div
								class="box_left"
								:style="`${sixRingDateList.length > 1 ? 'width: 50%' : 'width: 100%'}`"
								v-for="(item, index) in sixRingDateList"
								:key="index"
							>
								<!-- <div class="boxTitle">
								<div class="titlesq">商圈实拍</div>
							</div> -->

								<div class="img_box btnBoxPriceStatisticsDoms">
									<div class="img_left">
										<el-image
											style="width: 100%; border-radius: 4px; height: 280px"
											:preview-src-list="item.districtsList.activeUrl"
											ref="imageDom"
											:src="item.districtsList.activeUrl[0]"
											fit="cover"
										/>

										<el-image
											v-for="(url, index) in item.districtsList.imgList"
											@click="handleActive(index, url, item.districtsList)"
											:key="url"
											:src="url"
											:class="item.districtsList.imageActiveIndex === index ? 'elImageActive' : ''"
											class="elImage"
											fit="cover"
										/>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="tag_box">商圈评估表</div>
					<div class="table_main" style="height: auto">
						<div
							class="table_ table_line"
							style="height: fit-content; margin: 0 10px"
							:style="`${sixRingDateList.length > 1 ? 'width: 50%' : 'width: 100%'}`"
							v-for="(item, index) in sixRingDateList"
							:key="index"
						>
							<div class="textContent" v-for="(itemsColumn, indexsColumn) in labelInfo" :key="indexsColumn">
								<div class="left_text">{{ itemsColumn.label }}</div>
								<div class="right_text">
									{{ item.districtsList?.businessEvaluateInfo?.[itemsColumn.value] }}
								</div>
							</div>

							<div class="line_bottom_border" v-for="(itemRows, indexRows) in labelInfoRow" :key="indexRows">
								<div
									class="table_ table_line_border"
									style="height: auto; border-radius: 0px"
									v-for="(itemRow, indexRow) in itemRows"
									:key="indexRow"
								>
									<div class="text_f">{{ itemRow.label }}</div>
									<div class="text_t">{{ item.districtsList?.businessEvaluateInfo?.[itemRow.value] }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- <div class="echars_box" style="height: auto; padding: 14px 0 0 0; width: calc(100% - 0px)">
					<div class="echars_main">
						<div class="box_" v-for="(item, index) in sixRingDateList" :key="index" style="border: 0px solid red; padding: 0">
							<div class="box_left" style="padding: 0">
								<div class="boxTitle">
									<div class="titlesq">商圈实拍</div>
								</div>

								<div class="img_box btnBoxPriceStatisticsDoms">
									<div class="img_left">
										<el-image
											style="width: 100%; border-radius: 4px; height: 280px"
											:preview-src-list="item.districtsList.activeUrl"
											ref="imageDom"
											:src="item.districtsList.activeUrl[0]"
											fit="cover"
										/>

										<el-image
											v-for="(url, index) in item.districtsList.imgList"
											@click="handleActive(index, url, item.districtsList)"
											:key="url"
											:src="url"
											:class="item.districtsList.imageActiveIndex === index ? 'elImageActive' : ''"
											class="elImage"
											fit="cover"
										/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div> -->
			</div>
		</div>
	</div>
	<!-- 对话框 -->
	<el-dialog v-model="dialogTableVisible" width="800" title="选择对比资产" :close-on-click-modal="false">
		<div class="title_box">
			<div class="tab" v-for="(item, index) in multipleSelection" :key="index">
				<div style="text-wrap: nowrap">对比资产</div>
				{{ $utils.chineseNumber(index) }}：
				<div :title="item.buildingName" style="text-wrap: nowrap; width: 116px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
					{{ item.buildingName }}
				</div>
				<div class="det" @click="clearDate(item, 0, index)">×</div>
			</div>
		</div>
		<div class="search_box">
			<div class="box_1">
				<div class="label">城市</div>
				<el-cascader
					placeholder="请选择城市"
					:options="$vuexStore.state.cityArray"
					v-model="selectedCity"
					@change="handleChange"
					:props="{ value: 'label' }"
				>
				</el-cascader>
			</div>
			<div class="box_1">
				<div class="label">资产类型</div>
				<el-select v-model="buildValue" placeholder="全部资产">
					<el-option v-for="item in buildingTypes" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">关键词</div>
				<el-input v-model="essential" placeholder="请输入关键字"></el-input>
			</div>
			<div class="box_2">
				<el-button type="primary" @click="Compareds()">查询</el-button>
				<el-button type="primary" @click="reset()">重置</el-button>
			</div>
		</div>
		<div class="table_2">
			<el-table
				:data="tableData"
				style="width: 100%"
				height="308px"
				border
				ref="multipleTableRef"
				@selection-change="(selection) => handleSelectionChange(selection)"
				stripe
			>
				<el-table-column type="selection" width="55" />
				<el-table-column
					v-for="(column, index) in tableColumns"
					:key="index"
					:label="column.label"
					:prop="column.prop"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table>
		</div>

		<el-pagination
			@current-change="handleCurrentChange"
			:current-page="currentPage"
			small
			background
			layout="prev, pager, next"
			class="mt-4"
			:total="total"
		/>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogTableVisible = false">取消</el-button>
				<el-button type="primary" @click="save()"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import getReport from '../../../../component/getReport/index.vue';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index'; //处理数值
import rat from '@/RatMap.vue'; //地图
import { getBuildingListByMultiCondition, getBuildingLocation, getDictList } from '@/api/syt.js';
import { useStore } from '../../../../store';
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const loading = ref();
const store = useStore();
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const getReportRef = ref(); //获取报告
const dialogTableVisible = ref(false); //对话框显示
const province = ref(''); //省
const city = ref(''); //市
const buildingTypes = ref([]); //资产类型
const buildValue = ref(''); //资产类型
const essential = ref(''); //关键词
const currentPage = ref(1); //当前页
const total = ref(0); //总条数
const multipleTableRef = ref(null); //多选
const tableData = ref([]); //表格数据
const multipleSelection = ref([]); //选中的数据
const selectedCity = ref([]); //选中的城市

const facilityList = ref([{ name: '餐饮服务' }, { name: '住宿服务' }, { name: '公共设施' }, { name: '摩托车服务' }]);
const faciltyListActive = ref(0);

const sixRingDateList = ref([
	//商圈数据
	{
		districtsList: {
			// //商圈数据
			businessDistrictName: '',
			range: '',
			activeUrl: [],
			imageActiveIndex: 0,
			businessEvaluateInfo: {},
			imgList: [],
			businessCoordinateList: [],
			info: [],
			infoActiveIndex: 0,
		},
		buildBusinessDistrictAvgList: [],
		buildBusinessDistrictList: [],
	},
	// {
	// 	districtsList: {
	// 		//商圈数据
	// 		businessDistrictName: '',
	// 		range: '',
	// 		activeUrl: [],
	// 		imageActiveIndex: 0,
	// 		businessEvaluateInfo: {},
	// 		imgList: [],
	// 		businessCoordinateList: [],
	// 		info: [],
	// 		infoActiveIndex: 0,
	// 	},
	// 	buildBusinessDistrictAvgList: [],
	// 	buildBusinessDistrictList: [],
	// },
]);
// 商圈评估表横向
const labelInfoRow = ref([
	[
		{
			label: '商业设施齐全程度',
			value: 'facilityLevel',
		},
		{
			label: '商业覆盖度',
			value: 'businessCover',
		},
	],
	[
		{
			label: '商业氛围',
			value: 'businessAtmo',
		},
		{
			label: '交通网络',
			value: 'trafficNetwork',
		},
	],
	[
		{
			label: '交通拥堵情况',
			value: 'trafficJam',
		},
		{
			label: '人流量',
			value: 'peopleTraffic',
		},
	],
	[
		{
			label: '消费人群特征',
			value: 'spendPeopleFeature',
		},
		{
			label: '配套设施',
			value: 'facilitySupport',
		},
	],
	[
		{
			label: '商圈管理',
			value: 'businessManage',
		},
		{
			label: '竟争品牌数量',
			value: 'brandCompeteNum',
		},
	],
	[
		{
			label: '竞争形式',
			value: 'businessCompete',
		},
		{
			label: '总分',
			value: 'totalScore',
		},
	],
]);
// 商圈评估表纵向
const labelInfo = ref([
	{
		label: '商圈亮点',
		value: 'businessHighlight',
	},
	{
		label: '商圈不足',
		value: 'businessDefect',
	},
	{
		label: '商圈评价',
		value: 'businessEvaluateDesc',
	},
]);
// 表格
const tableColumns = [
	{
		label: '商圈名称',
		prop: 'buildingName',
		width: '200',
		showOverflowTooltip: true,
	},
	{
		label: '资产类型',
		prop: 'buildingType',
		showOverflowTooltip: true,
	},
	{
		showOverflowTooltip: true,
		label: '地址',
		prop: 'street',
		width: '300',
	},
];
const tableDatao = ref([]); //商圈一
const tableDatat = ref([]); //商圈二
const reset = () => {
	selectedCity.value = []; //选中的城市
	province.value = ''; //省
	city.value = ''; //市
	buildValue.value = '';
	essential.value = ''; //关键词
	currentPage.value = 1; //当前页
	Compared(); //查询
};

//周边设施选中项切换
function handleFacilty(index, item) {
	item.infoActiveIndex = index;
}
function handleActive(index, url, item) {
	item.imageActiveIndex = index;
	item.activeUrl = [url];
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}

//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}
// 选中
const handleSelectionChange = (val) => {
	setTimeout(() => {
		let mergedSet = new Set([...multipleSelection.value, ...val]);
		let mergedArray = Array.from(mergedSet);
		let uniqueById = Array.from(new Set(mergedArray.map((item) => item.id))).map((id) => {
			return mergedArray.find((item) => item.id === id);
		});
		// 当前页 tableData.value
		// 当前页选中 val
		// 当前页选中和之前选中的重复的去掉的 uniqueById
		tableData.value.map((item) => {
			uniqueById.map((uniqueItem, index) => {
				if (item.id == uniqueItem.id) {
					const foundInVal = val.some((v) => v.id === uniqueItem.id);
					if (!foundInVal) {
						uniqueById.splice(index, 1);
					}
				}
			});
		});
		multipleSelection.value = uniqueById;
	}, 100);
};

// 点击选择对比资产
const choose = (item) => {
	dialogTableVisible.value = true;
};

// 复制
function handlerCopy(name) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(name)
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = name;
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

// 修改城市
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const queryParams = computed(() => {
	return {
		city: province.value,
		district: city.value,
		keyword: essential.value,
		buildingType: buildValue.value,
		currentPage: currentPage.value,
		// year: 2024,
		pageSize: 10,
	};
});
onMounted(() => {
	getDict();
	Compared();
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
});

//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getBuildingLocation({ buildingIds: obj.ids }).then((res) => {
		sixRingDateList.value = [];
		tableDatao.value = [];
		tableDatat.value = [];
		if (res.code === 200) {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			multipleSelection.value = obj.arr;
			sixRingDateList.value = res.data;
			handleUpdate(1);
			tableDatao.value.push(obj.arr[0]);
			tableDatat.value.push(obj.arr[1]);
			loading.value.close();
		}
	});
}
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
//对比资产分页查询
const handleCurrentChange = (val) => {
	currentPage.value = val;
	Compared();
};

function Compareds() {
	currentPage.value = 1;
	Compared();
}
// 查询
const Compared = async () => {
	await getBuildingListByMultiCondition(queryParams.value)
		.then((res) => {
			tableData.value = [];
			tableData.value = res.data.rows;
			nextTick(() => {
				tableData.value.map((v) => {
					multipleSelection.value.map((i) => {
						if (v.id == i.id) {
							multipleTableRef.value.toggleRowSelection(v, true);
						}
					});
				});
			});
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
// 全选
const toggleSelection = (rows, isSelect) => {
	if (rows) {
		rows.forEach((row) => {
			multipleTableRef.value.toggleRowSelection(row, undefined, isSelect);
		});
	} else {
		multipleTableRef.value.clearSelection();
	}
};

// 清空
function handelClear(row) {
	multipleSelection.value.forEach((item, indexs) => {
		if (item.id == row.id) {
			multipleSelection.value.splice(indexs, 1);
		}
	});
	save();
}

//清空资产选项
const clearDate = (row, type, index) => {
	if (type === 1) {
		//资产一清空
		tableDatao.value = [];
		handelClear(row);
	} else if (type === 2) {
		//资产二清空
		tableDatat.value = [];
		handelClear(row);
	} else {
		// 弹窗内资产清空
		multipleSelection.value.splice(index, 1);
	}
	if (tableData.value?.length > 0) {
		// 删除table选中的数据后，清空table选中的数据
		tableData.value.forEach((item) => {
			if (item.id == row.id) {
				toggleSelection([row]);
			}
		});
	}
	if (sixRingDateList.value.length > 0) {
		sixRingDateList.value.map((item, inde) => {
			if (item.id == row.id) {
				sixRingDateList.value.splice(inde, 1);
			}
		});
	}
};

//组装数据
function handleUpdate(type) {
	sixRingDateList.value.forEach((element, index) => {
		if (!type) {
			multipleSelection.value.forEach((item) => {
				if (item.id === element.id && index === 0) {
					tableDatao.value.push(item);
				}
				if (item.id === element.id && index === 1) {
					tableDatat.value.push(item);
				}
			});
		}

		let businessCoordinateLists = [];
		let imgUrlList = [];
		let imgUrl = element.locationAgg.buildingLocation.businessEvaluateInfo.businessDistrictPics.split(',');
		imgUrl.forEach((item) => {
			imgUrlList.push(`${store.imagePathPrefix}${item}`);
		});
		element.locationAgg.buildingLocation.businessCoordinateList.forEach((items) => {
			businessCoordinateLists.push([items.lng, items.lat]);
		});
		element.districtsList = {
			businessDistrictName: element.locationAgg.buildingLocation.businessEvaluateInfo?.businessDistrictName || '', // 资产名称
			range: element.locationAgg.buildingLocation.businessEvaluateInfo.businessDistrictInfo || '', // 商圈介绍
			businessEvaluateInfo: element.locationAgg.buildingLocation.businessEvaluateInfo, // 商圈评估表
			imgList: imgUrlList || [], // 商圈图片
			imageActiveIndex: 0, // 商圈图片下标
			activeUrl: [imgUrlList[0]] || [], // 商圈图片
			businessCoordinateList: businessCoordinateLists || [], // 商圈坐标
			info: element.locationAgg.info || [], // 商圈信息
			infoActiveIndex: 0, // 商圈信息下标
		};
	});
}
// 确定
const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		ElMessage({
			message: multipleSelection.value.length == 0 ? '至少选一个商圈' : '最多选择两个商圈',
			type: 'error',
		});
	} else {
		let ids = multipleSelection.value.map((item) => item.id).join(',');
		getBuildingLocation({ buildingIds: ids }).then((res) => {
			sixRingDateList.value = [];
			tableDatao.value = [];
			tableDatat.value = [];
			if (res.code === 200) {
				emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
				sixRingDateList.value = res.data;
				handleUpdate();
			}
			dialogTableVisible.value = false;
		});
	}
};
</script>
<style scoped lang="less">
.comparison_box {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;

		.table_main {
			width: 100%;
			// height: 162px;
			display: flex;
			justify-content: space-between;
			.table_ {
				width: calc(50% - 8px);
				height: 162px;
				border-radius: 6px;
				background-color: rgba(255, 255, 255, 1);
				position: relative;

				.tag_box {
					width: auto;
					height: 16px;
					position: absolute;
					left: 0;
					top: 20px;
					font-size: 14px;
					font-weight: bold;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&::before {
						content: '';
						width: 4px;
						height: 16px;
						background-color: rgba(24, 104, 241, 1);
						margin-right: 10px;
					}
				}

				.table_1 {
					width: 96%;
					position: absolute;
					bottom: 10px;
					left: 2%;
					&::v-deep .el-table--fit {
						border-radius: 8px;
					}

					&::v-deep .el-table th {
						background-color: rgba(245, 245, 245, 1);
					}
				}

				.add {
					width: 96%;
					height: 90px;
					position: absolute;
					bottom: 10px;
					left: 2%;
					border-radius: 6px;
					border: 1px solid rgba(231, 231, 231, 1);
					display: flex;
					justify-content: center;
					align-items: center;
					color: rgba(3, 93, 255, 1);
					font-size: 14px;
					font-weight: bold;
				}

				.clear {
					width: auto;
					height: 20px;
					position: absolute;
					top: 15px;
					right: 15px;
					font-size: 14px;
					color: rgba(24, 104, 241, 1);
				}
			}
		}
		.echars_box {
			width: calc(100% - 32px);
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			padding: 24px 16px;
			border-radius: 6px;
			.tag_box {
				width: auto;
				height: 24px;
				padding: 16px 0;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
				span {
					margin-left: 10px;
					font-size: 12px;
					display: flex;
					line-height: normal;
					// justify-content: flex-start;
					// align-items: center;
					&:first-child {
						&::after {
							content: '';
							width: 8px;
							height: 12px;
							margin-top: 2.5px;
							margin-left: 10px;
							background-color: rgba(4, 80, 218, 1);
						}
					}
					&:last-child {
						&::after {
							content: '';
							width: 8px;
							margin-top: 2.5px;
							height: 12px;
							margin-left: 10px;
							background-color: rgba(30, 170, 117, 1);
						}
					}
				}
			}
			.echars_main {
				width: 100%;
				box-sizing: border-box;
				border: 1px solid rgba(231, 231, 231, 1);
				padding-bottom: 20px;
				// display: flex;
				// justify-content: space-between;
				height: 100%;
				.title1 {
					width: 100%;
					height: 44px;
					display: flex;
					justify-content: center;
					align-items: center;
					font-size: 12px;
					background-color: rgba(245, 245, 245, 1);
					border-bottom: 1px solid rgba(231, 231, 231, 1);
				}
				.box_ {
					width: 100%;
					// border: 1px solid rgba(231, 231, 231, 1);
					border-radius: 6px;
					box-sizing: border-box;
					height: 100%;
					padding-bottom: 24px;
					.title1 {
						width: 100%;
						height: 44px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 12px;
						background-color: rgba(245, 245, 245, 1);
						border-bottom: 1px solid rgba(231, 231, 231, 1);
					}

					.box_left {
						// padding: 24px 24px 0 24px;
						padding: 0 10px;
						// height: 380px;
						.boxTitle {
							display: flex;
							padding: 11.5px 18px;
							justify-content: center;
							align-items: center;
							border-radius: 3px;
							border-bottom-left-radius: 0px;
							border-bottom-right-radius: 0px;
							position: relative;
							border: 1px solid #e7e7e7;
							border-bottom: 0px;
							.titlesq {
								width: 48px;
								margin-right: 16px;
								font-size: 12px;
								font-weight: 400;
								line-height: 20px;
								color: #4e5969;
							}
						}

						.img_box {
							padding: 16px 18px;
							border-radius: 3px;
							border-top-left-radius: 0px;
							border-top-right-radius: 0px;
							border: 1px solid #e7e7e7;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							.img_leftTitle {
								font-size: 12px;
								font-weight: 400;
								line-height: 24px;
								color: #1d2129;
							}
							.img_left {
								width: calc(100%);
								position: relative;

								.img_boxNum {
									position: absolute;
									bottom: 6px;
									left: 6px;
									font-size: 12px;
									padding: 2px 8px;
									font-weight: 500;
									line-height: 20px;
									color: #fff;
									background: #00000099;
									border-radius: 3px;
								}
							}
						}
					}
				}
			}
		}
	}
}
.title_box {
	width: 100%;
	max-height: 100px;
	overflow: scroll;
	border-top: 1px solid rgba(231, 231, 231, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	box-sizing: border-box;
	display: flex;
	flex-wrap: wrap;
	.tab {
		width: 31%;
		height: 32px;
		margin: 8px 15px 8px 0;
		background-color: rgba(245, 246, 247, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
		position: relative;
		text-wrap: nowrap;
		.det {
			width: 10px;
			height: 10px;
			position: absolute;
			right: 10px;
			font-size: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: rgba(201, 205, 212, 1);
			cursor: pointer;
		}
	}
}
.search_box {
	width: 100%;
	height: auto;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
	.box_1 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		border: 1px solid rgba(231, 231, 231, 1);
		box-sizing: border-box;

		::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
			box-shadow: 0;
		}

		.label {
			width: 50%;
			height: 100%;
			font-size: 14px;
			color: rgba(134, 144, 156, 1);
			background-color: rgba(245, 246, 247, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.box_2 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		box-sizing: border-box;
	}
}
.table_2 {
	width: 100%;
	height: 308px;
	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}
.btn_box {
	border: 1px solid #e7e7e7;
	width: calc(100% - 20px);
	display: flex;
	justify-content: flex-start;
	align-items: center;
	border-radius: 6px;
	background-color: #ffffff;
	margin-top: 10px;
	font-size: 14px;
	color: #555555;
	line-height: 20px;
	position: relative;
	padding: 10px 10px 15px 10px;
	.box_copy {
		position: absolute;
		right: 12px;
		bottom: 7px;
		cursor: pointer;
		color: #1868f1;
	}
}

.table_Content {
	width: 100% !important;
	position: unset !important;
	height: auto;
}

.table_line {
	// width: 49.2% !important;
	border: 1px solid #e7e7e7;
	.textContent {
		display: flex;
		// height: 60px;
		align-items: center;
		padding: 8px 0;
		border-top: 1px solid #e7e7e7;
		.left_text {
			width: 120px;
			padding: 0 16px;
			font-size: 12px;
			line-height: 60px;
			font-weight: 400;
			color: #4e5969;
		}
		.right_text {
			width: calc(100% - 168px);
			font-size: 12px;
			font-weight: 500;
			line-height: 60px;
			padding-right: 16px;
			line-height: 20px;
			color: #4e5969;
			display: flex;
		}
	}
}

.table_line > :nth-child(1) {
	border-top: 0px solid #e7e7e7;
}

.table_line_border {
	display: flex;
	height: 44px !important;
	align-items: center;
	border-right: 1px solid #e7e7e7;
	& > :nth-child(n) {
		width: 50%;
		padding: 0 16px;
	}

	.text_f {
		font-size: 12px;
		font-weight: 400;
		line-height: 44px;
		color: #4e5969;
	}
	.text_t {
		font-size: 16px;
		font-weight: 700;
		line-height: 44px;
		letter-spacing: -0.04em;
		color: #1868f1;
	}
}

.line_bottom_border {
	display: flex;
	border-top: 1px solid #e7e7e7;

	> :nth-last-child(1) {
		border-right: 0px solid #e7e7e7;
	}
}

::v-deep .rat {
	.amap-logo {
		display: none !important;
	}
	.amap-copyright {
		display: none !important;
	}
}

.btnBoxPriceStatisticsDoms {
	display: flex;
	flex-direction: column;
}
.box_copyContent {
	width: 100%;
	display: flex;
	justify-content: end;
	margin-bottom: -10px;
}

.box_copyContent > :nth-child(n) {
	cursor: pointer;
	color: #1868f1;
	font-size: 12px;
	font-weight: 400;
	line-height: 20px;
}

.btn_boxDetails {
	font-size: 12px;
	font-weight: 400;
	line-height: 20px;
	color: #4e5969;
}

.elImage {
	width: 40px;
	height: 40px;
	object-fit: fill;
	margin-right: 8px;
	margin-top: 3.1px;
	border-radius: 2.5px;
	cursor: pointer;
	filter: grayscale(10%) opacity(0.6);
}

.elImageActive {
	filter: grayscale(0%) !important;
}

.faciltyClass {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	margin: 16px 0;
	.faciltyItem {
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		color: #86909c;
		height: 20px;
		padding: 4px 8px;
		margin: 0 8px;
		display: flex;
		justify-content: center;
		cursor: pointer;
		.faciltyImg {
			line-height: 23px;
			img {
				margin-right: 4px;
				width: 12px;
				height: 12px;
			}
		}
	}
	.faciltyItemActive {
		background: #f5f6f7;
		border-radius: 6px;
		color: #1868f1;
	}
}

.top_boxFirst {
	display: flex;
	justify-content: space-between;
	height: 56px;
	align-items: center;
	.tag_boxTitle {
		width: auto;
		height: 16px;
		font-size: 14px;
		font-weight: bold;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		&::before {
			content: '';
			width: 4px;
			height: 16px;
			background-color: #1868f1;
			margin-right: 10px;
		}
	}

	.tag_boxCenter {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		font-size: 14px;
		color: #1868f1;
		padding-right: 16px;
		> :nth-child(n) {
			cursor: pointer;
		}
		.tag_boxLeft {
			display: flex;
			align-items: center;
			margin: 0 0px 0 24px;
		}
	}
}
</style>
