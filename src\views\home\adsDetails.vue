<template>
	<div>
		<div class="container">
			<div class="guanggao_img" v-for="(item, index) in AdvertLists" :key="index" @click="goUrl(item.id)">
				<img :src="`${proxyAddress}${item.displayPic}`" />
				<div class="shade">
					{{ item.title }}
					<div>{{ item.address }}</div>
				</div>
			</div>
		</div>
		<div class="pagination">
			<el-pagination
				@change="handdleChangePage"
				:page-size="param.pageSize"
				:currnet-page="param.pageNo || 1"
				layout="prev, pager, next"
				:total="total"
			/>
		</div>
	</div>
</template>
<script setup>
import { Fragment, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { AdvertList } from '../../../src/api/home.js';

const router = useRouter();
const AdvertLists = ref([]);
const total = ref(0);
let param = {
	pageNo: 1,
	pageSize: 12,
};
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const goUrl = (url) => {
	router.push({ path: '/advertising', query: { id: url } });
};
const handdleChangePage = (e) => {
	param = {
		...param,
		pageNo: e,
	};
	getAdvertList(param);
};
const getAdvertList = async (params) => {
	await AdvertList(params)
		.then((res) => {
			console.log('广告', res);
			total.value = res.data.total;
			AdvertLists.value = res.data.rows;
			console.log(AdvertLists.value, 88812);
		})
		.catch((err) => {
			console.log('请求失败！');
		});
};
onMounted(() => {
	getAdvertList(param); //广告位
});
</script>
<style scoped lang="less">
.pagination {
	display: flex;
	justify-content: end;
	margin: 0 20px 20px 0;
	.el-pagination {
		margin-top: 0px;
	}
}
.container {
	display: flex;
	flex-wrap: wrap;
}
.guanggao_img {
	flex-basis: 23%;
	width: 100%;
	height: auto;
	margin: 1%;
	overflow: hidden;
	// height: 115px;
	// margin-bottom: 10px;
	// height: auto;
	position: relative;

	img {
		width: 100%;
		height: 100%;
		transition: transform 0.3s ease;
		cursor: pointer;
	}
	img:hover {
		transform: scale(1.1);
	}
	.shade {
		color: rgb(255, 255, 255);
		font-family: 微软雅黑;
		font-weight: bold;
		font-size: 18px;
		letter-spacing: 0px;
		text-align: center;
		width: 100%;
		text-align: center;
		background: rgba(0, 0, 0, 0.59);
		position: absolute;
		top: 0;
		left: 0;
		z-index: 100;
		min-height: 80px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
}
.btn {
	height: 50px;
	text-align: center;
	a {
		cursor: pointer;
	}
}
@media screen and (max-width: 991px) {
	.guanggao_img {
		flex-basis: 48%;
	}
}
@media screen and (max-width: 768px) {
	.guanggao_img {
		flex-basis: 98%;
	}
}
</style>
