<template>
	<el-dialog v-model="visible" @close="handleClose" :close-on-click-modal="false" width="1080" title="商报auto服务协议" append-to-body>
		<div class="agreement">
			<!-- <h1>商报auto服务协议</h1> -->
			<p>
				欢迎您使用商报auto。我们的服务由术木智能科技发展（青岛）有限公司（下称“术木智能科技”）基于以下条款而提供，请您仔细阅读。如果您对本服务条款表示异议，您可以选择不进入商报auto或不使用bbzhun.com网站。
			</p>
			<p>
				本服务条款（以下称服务条款或本协议）由“术木智能科技”与您共同缔结，对双方具有同等法律效力。当您注册或登录商报auto（包括网页端及其他端口），或浏览或使用商报auto的任何功能，或是在商报auto上发布任何内容（即「内容」），均意味着您（即「用户」）完全接受本协议项下的全部条款。
			</p>
			<p>
				协议中存在相关免除或者限制责任的相应条款（以下称“免责声明”）、对用户权利进行限制的条款（以下称“使用限制”）、约定争议解决方式和司法管辖的条款。前述该等免责、限制及争议解决方式和管辖条款请您重点阅读。您对该等条款的确认将可能导致您在特定情况下的被动、不便、损失，请您在确认同意本协议之前或在使用商报auto服务之前再次阅读前述条款。
			</p>
			<h2>一、使用规则</h2>
			<p>1、用户注册成功后，用户应当对以其用户帐号进行的所有活动和事件负法律责任。</p>
			<p>
				2、用户须对在商报auto的注册信息的真实性、合法性、有效性承担全部责任，用户不得冒充他人；不得利用他人的名义发布任何信息；不得恶意使用注册帐号导致其他用户误认；否则商报auto有权立即停止提供服务，收回其帐号并由用户独自承担由此而产生的一切法律责任。
			</p>
			<p>3、因用户行为造成商报auto或第三方损失的，用户应承担赔偿责任。</p>
			<p>
				4、用户直接或通过各类方式间接使用商报auto服务和数据的行为，都将被视作已无条件接受本协议全部内容；若用户对本协议的任何条款存在异议，请停止使用商报auto所提供的全部服务。
			</p>
			<p>5、用户承诺不得以任何方式利用商报auto直接或间接从事违反中国法律、以及社会公德的行为，商报auto有权对违反上述承诺的内容予以删除。</p>
			<p>6、用户不得利用商报auto服务制作、上载、复制、发布、传播或者转载如下内容：</p>
			<ul>
				<li>反对宪法所确定的基本原则的；</li>
				<li>危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；</li>
				<li>损害国家荣誉和利益的；</li>
				<li>煽动民族仇恨、民族歧视，破坏民族团结的；</li>
				<li>破坏国家宗教政策，宣扬邪教和封建迷信的；</li>
				<li>散布谣言，扰乱社会秩序，破坏社会稳定的；</li>
				<li>散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；</li>
				<li>侮辱或者诽谤他人，侵害他人合法权益的；</li>
				<li>侵犯他人著作权、信息网络传播权等、商业秘密等合法权益的；</li>
				<li>侵犯他人著作权、信息网络传播权等、商业秘密等合法权益。</li>
			</ul>
			<p>
				7、商报auto有权对用户使用商报auto的情况进行审查和监督，如用户在使用商报auto时违反任何上述规定，商报auto或其授权的人有权要求用户改正或直接采取一切必要的措施（包括但不限于更改或删除用户张贴、上传的内容、暂停或终止用户使用商报auto的权利）以减轻用户不当行为造成的影响。
			</p>
			<p>
				8、用户如果与商报auto的其它用户联系、沟通或达成交易（无论是否收费），应由用户自行协商并履行各方的约定，商报auto对此不提倡亦不禁止，不对此承担连带或担保的责任。
			</p>
			<p>9、如果商报auto向用户提供了收费服务，则双方的权利义务按相应服务条款处理。</p>
			<h2>二、不正当竞争</h2>
			<p>
				用户了解商报auto及作者对报告整理付出巨大劳动，具有商业价值；任何用户以网络爬虫技术或其他技术获取商报auto文本或网站其他内容，均属于不正当竞争行为，商报auto或文档作者将追究其法律责任。
			</p>
			<h2>三、使用限制</h2>
			<p>
				1、用户知晓并确认，在商报auto以付费下载或使用优惠券下载方式获得的报告或文档，不可用于建设与商报auto同类的报告分享平台或中心，或者将从商报auto下载获得的报告或文档上传至前述平台或中心。不可以微信群、微信公众号、淘宝等方式向不特定公众出售或赠送，否则属于违反用户条款，商报auto或报告作者均可要求用户支付人民币20万元违约金，违约金不足以弥补商报auto损失或用户获利超过违约金的，商报auto有权另行追究其赔偿责任。商报auto有权在用户存在不正常使用行为时，或发现用户以任何渠道向不特定第三方赠送或出售从商报auto下载的报告时，取消其用户权限，包括但不限于禁止其下载报告或文档，停止其账号使用权限，终止向该用户提供其已经下载的报告或文档的使用授权，或终止和限制其使用功能和权限。特别强调，因用户上述行为使得第三方遭受损害的，商报auto概不负责，若有第三方因此向商报auto追责的，用户应积极协商解决，承担一切法律责任，并赔偿因此给商报auto或任何第三方造成的全部经济损失，包括直接经济损失与间接经济损失。
			</p>
			<p>
				2、用户不得移动、遮掩或修改报告或文档中的任何版权或其他通知标志，亦不得移动、遮掩或修改任何与报告或文档相关的原数据或数字版权智能管理信息。
			</p>
			<p>
				3、用户应合理使用商报auto提供的报告/文档/数据等任何产品，同时应遵守《数据安全法》、《网络安全法》及其相关法律规定，尊重社会公德和伦理，遵守商业道德和职业道德，诚实守信，履行数据安全保护义务，承担社会责任。非经中华人民共和国有关机关批准、授权或有明文规定，不得向外国任何组织、个人提供任何存储于中华人民共和国境内的数据，不得危害国家安全、公共利益，不得损害个人、组织的合法权益。商报auto作为产品提供方已尽到告知义务，若用户未能按照上述目的、用途、规定合理使用，用户应自行承担法律责任。若商报auto因此被追责，用户应尽一切努力解决并承担商报auto的一切损失，该损失包括但不限于商报auto的经济损失、行政罚款、商报auto为维护权益而支出的律师费、诉讼费等全部费用。
			</p>
			<p>
				4、用户承诺：未经商报auto书面同意或授权，不会将自商报auto获得的报告/文档等作为对外交易、宣传、沟通的依据、基础或以此进行任何商业或其他行为；不会将报告/文档当以任何形式擅自发送给任意第三方，更不会对报告/文档等作任何有悖原意的引用、删减、更改。
			</p>
			<h2>四、知识产权</h2>
			<p>
				1、用户下载商报auto的公开报告（含成套报告）时，该文本的知识产权仍属于商报auto所有，用户仅可以自行使用，但不得向公开渠道或不特定第三方传播（无论是否营利）。
			</p>
			<p>2、任何人发现商报auto的任何内容涉嫌侵权时，均可通过商报auto的投诉渠道进行投诉。</p>
			<p>
				3、商报auto提供的网络信息服务中包含的标识、版面设计、排版方式、文本、图片、图形等均受著作权、商标权及其它法律保护，未经（含商报auto及其他原始权利人）同意，上述内容均不得在任何平台被直接或间接发布、使用、出于发布或使用目的的改写或再发行，或被用于其他任何商业目的。
			</p>
			<h2>五、发票</h2>
			<p>
				1、用户如需要开具发票，请联系平台客服，并提交：订单号（或充值单号）、发票抬头（单位名称）、税务登记证号（或统一社会信用代码）、基本开户银行及银行账号、注册地址、注册固定电话、收发票邮箱。
			</p>
			<p>
				2、商报auto将为您开具正规有效发票，开票类型为增值税普通发票/增值税专用发票，发票货物或服务名称为：信息技术服务，税金由您（单位用户）承担（大约6%左右，具体以发票实际税额为准）。
			</p>
			<p>3、商报auto平台为单位（机构）用户每月集中到税务部门开具发票，商报auto平台不承担发票邮寄费用。</p>
			<h2>六、特别说明</h2>
			<p>您清楚的知道，就本次报告出售事宜，商报auto是该报告作出单位，是与您达成交易订单的合同主体。</p>
			<p>1、报告类作品属于特殊商品，每个人对于报告的看法可能很不相同。如您不喜欢该报告，商报auto及（或）该单位不承担退款责任；</p>
			<p>2、以电子形式交付的产品不适用七天无理由退货的规定；</p>
			<p>3、请您仔细阅读了解商报auto的交易流程，注意有关的知识产权约定及授权范围，切勿损害知识产权人的合法权益；</p>
			<p>4、用户账号非因商报auto过错被他人盗用；商报auto不承担赔偿与补偿责任；</p>
			<p>5、商报auto受他人攻击，导致账户金额受损，且无法恢复的，商报auto不承担赔偿与补偿责任；</p>
			<p>6、其它非商报auto原因造成的硬件或技术故障，导致暂停服务或账户金额受损，且无法恢复的，商报auto不承担赔偿与补偿责任。</p>
			<h2>七、个人隐私</h2>
			<p>
				尊重用户个人隐私信息的私有性是商报auto的一贯原则，商报auto将通过技术手段、强化内部管理等办法充分保护用户的个人隐私信息，除法律或有法律赋予权限的政府部门要求或事先得到用户明确授权等原因外，商报auto保证不对外公开或向第三方透露用户个人隐私信息，或用户在使用服务时存储的非公开内容。同时，为了运营和改善商报auto的技术与服务，商报auto将可能会自行收集使用提供用户的非个人隐私信息，这将有助于商报auto向用户提供更好的用户体验和服务质量。
			</p>
			<h2>八、免责声明</h2>
			<p>
				1、本网站中公开或共享的报告文本、条款及其简介（以下简称公开文本）。无论该公开文本是免费或需要付费购买，无论是否经过商报auto平台编辑、修订或经“商报auto认证”，均在本免责声明范围之内。
			</p>
			<p>
				2、本网站中的公开文本一般系依据中华人民共和国（港澳台地区除外）法律法规而拟定，未必适用于其它国家地区。即使在中华人民共和国范围内，亦根据本文件享有相应免责权利。
			</p>
			<p>3、商报auto致力于提升公开文本的质量，但仍不能保证本网站中的公开文本不存在法律、信息真实性上的错误。</p>
			<p>
				4、在任何情况下，不得因使用或依赖本网站上的信息、报告、文本而导致的任何损失要求商报auto承担责任，无论用户系使用优惠券下载使用或付费购买，均适用本免责声明。
			</p>
			<p>
				5、商报auto声明：商报auto向用户提供的产品，是商报auto基于自身的专业分析及相关领域的行业研究而做出的独立商业判断，使用的数据和信息均来自市场公开信息，但商报auto对信息的真实性、准确性及（或）完整性不做任何保证，也不保证所包含的信息和建议不会发生任何变更，即：报告/文档等所载的意见、评估及预测等内容仅为本报告/文档等出具日的观点和判断，报告/文档中的信息、意见的用途和目的仅限于为用户提供参考，不构成任何投资、商用等实质性建议，商报auto对其使用效果不做承诺，对结果不承担任何责任。商报auto有权将来根据不同假设、研究方法、即时动态信息、市场表现和相关数据等，发表与本报告/文档等不一致的意见、观点和预测，但商报auto并无义务向用户或任何接收报告的第三方进行更新。
			</p>
			<p>6、商报auto承诺该报告/文档等的分析结论、意见等不受任何第三方的授意，商报auto不会也不曾收取任何形式的报酬。</p>
			<h2>九、服务条款修改</h2>
			<p>
				1、根据互联网的发展和有关法律、法规及规范性文件的变化，或者因业务发展需要，商报auto有权对本协议的条款作出修改或变更，一旦本协议的内容发生变动，商报auto将会直接在商报auto网站上公布修改之后的协议内容，该公布行为视为商报auto已经通知用户修改内容。商报auto也可采用电子邮件或私信的传送方式，提示用户协议条款的修改、服务变更、或其它重要事项。
			</p>
			<p>
				2、如果不同意商报auto对本协议相关条款所做的修改，用户有权并应当停止使用商报auto。如果用户继续使用商报auto，则视为用户接受商报auto对本协议相关条款所做的修改。
			</p>
			<h2>十、争议解决</h2>
			<p>
				如因本协议发生争议，双方应本着有利于案件进展，维护用户权益的原则友好协商解决。如协商不能达成一致，双方同意提交至商报auto注册所在地法院管辖，并适用中华人民共和国法律。如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，协议其他条款仍有效并具有约束力。
			</p>
			<p class="last">
				商报auto提醒您：在使用商报auto服务前，请您务必仔细阅读并透彻理解本协议。您可以选择不使用商报auto，但如果您使用商报auto，您的使用行为将被视为对本协议全部内容的认可。
			</p>
		</div>
	</el-dialog>
</template>

<script setup>
const props = defineProps({
	// 控制弹窗
	modelValue: {
		type: Boolean,
		required: true,
	},
});

const emit = defineEmits(['update:modelValue']);

const visible = ref(false);

watch(
	() => props.modelValue,
	(newVal) => {
		visible.value = newVal;
	}
);

const handleClose = () => {
	visible.value = false;
	emit('update:modelValue', false);
};
</script>

<style scoped lang="less">
.agreement {
	font-family: Arial, sans-serif;
	height: 600px;
	overflow: auto;
	h1 {
		font-size: 24px;
		margin-bottom: 20px;
	}

	h2 {
		font-size: 20px;
		margin-top: 20px;
	}

	p {
		font-size: 16px;
		line-height: 1.5;
		margin-bottom: 10px;
	}
	.last {
		font-weight: 600;
		text-decoration: underline;
	}
}
</style>
