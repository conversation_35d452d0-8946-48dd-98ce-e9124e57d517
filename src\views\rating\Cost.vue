<template>
	<div class="bigbody">
		<el-row :gutter="20" justify="center" v-if="costData[0]">
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					评估对象和地块>>
					<el-divider class="divider" />
				</el-text>

				<el-text style="width: 80%; padding-top: 10px" size="large" tag="p" class="mx-1">{{ costData[0].evaluate_objects }}</el-text>
			</el-col>
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					可比市场法>>
					<el-divider class="divider" />
				</el-text>

				<el-text style="width: 80%; padding-top: 10px" size="large" tag="p" class="mx-1"
					>以{{ costData[0].comparable_instance }}作为市场法的参照物。</el-text
				>
				<el-text style="width: 80%; padding-top: 10px" size="large" tag="p" class="mx-1"
					>对交易时间、交易情况、房地产状况、区位状况、商业繁华度、基础设施完善度、自然及人文环境、公共服务设施状况、楼层、临路状况、实体状况、商业类型、进深比、配套基础设施、内部装修、层高、楼龄及保养、权益状况土地剩余年限、租约限制、规划限制条件（容积率）等因素进行修正，各给与{{
						weight
					}}权重。</el-text
				>
				<el-text style="width: 80%; padding-top: 10px" size="large" tag="p" class="mx-1">
					最终{{ costData[0].building_name }}的可比市场法价格系数为{{ costData[0].comparable_price }}。</el-text
				>
			</el-col>
		</el-row>
		<el-row :gutter="20" v-if="costData[0]">
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					可比收益法>>
					<el-divider class="divider" />
				</el-text>

				<el-text style="width: 80%; padding-top: 10px" size="large" tag="p" class="mx-1"
					>本次估价取各实例毛报酬率的简单算术平均数作为估计对象的毛报酬率，即{{ costData[0].gross_profit_margin }}%。</el-text
				>
				<el-text style="width: 80%; padding-top: 10px" size="large" tag="p" class="mx-1"
					>本次估价采取最终毛报酬率为{{ costData[0].gross_profit_margin }}%。故本次估价最终的净报酬率为{{ costData[0].net_interest_rate }}%。
					按照评估基准日{{ costData[0].addedtime }}中国十年期国债收益率{{ costData[0].ten_years_treasury_bond_yield }}%为无风险报酬率。
					对应折现率为()。</el-text
				>
				<el-text style="width: 80%; padding-top: 10px" size="large" tag="p" class="mx-1">
					通过可比现金流量折现法计算得出，可比现金流量价格系数为:{{ costData[0].comparable_cash_flow }}</el-text
				>
			</el-col>
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					评估结果确定>>
					<el-divider class="divider" />
				</el-text>

				<el-table :data="tableData" border style="width: 80%; margin-top: 10px">
					<el-table-column prop="name" label="评估对象" width="120" />
					<el-table-column prop="price" label="可比市场价格系数（权重70%）" width="220" />
					<el-table-column prop="money" label="可比现金流量折现系数（权重30%）" />
					<el-table-column prop="priceRes" label="市场可比价值系数（评估结果/市场平均价）" />
				</el-table>
				<el-text tag="p" size="large" class="mx-1" style="width: 80%; padding-top: 10px"
					>于{{ costData[0].addedtime }}，选取可比实例为{{ costData[0].comparable_instance }}。{{ costData[0].building_name }}可比价值系数为{{
						costData[0].comparable_value
					}}。</el-text
				>
			</el-col>
		</el-row>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http';
import { useRoute } from 'vue-router';
import { useStore } from '@/store/index';
const store = useStore();
const route = useRoute();
const costData = ref([]);
const tableData = ref([]);
const weight = ref('');
onMounted(async () => {
	const res = await http.get('/api/a_maps.do?act=ratingPassDetailsAction', {
		params: {
			action: 'value',
			maps_id: route.params.id,
		},
	});
	if (res && res.list && res.list.length > 0) {
		costData.value = res.list;
		weight.value = res.list[0].weight;
		const table = res.list.map((item) => {
			return {
				name: item.building_name,
				price: item.comparable_price,
				money: item.comparable_cash_flow,
				priceRes: item.comparable_value,
			};
		});
		tableData.value = table;
	}
});
</script>

<style lang="less" scoped>
.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
}
.divider {
	flex-grow: 1;
	margin: 16px 0 16px 8px;
	border-color: #3483ce;
}

.el-table:deep(.cell) {
	color: #000;
}
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 36px;
}
</style>
