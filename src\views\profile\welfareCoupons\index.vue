<template>
	<div class="rights-details-container">
		<div class="container_top">
			<el-icon @click="goBack" class="container_top_elion"><ArrowLeftBold /></el-icon>
			<div @click="goBack" class="container_top_title">福利卡券详情</div>
			<div class="container_top_line"></div>
			<div class="container_top_prompt">好礼优惠券使用后将发放至验证手机号绑定的对应平台账号中，登录对应平台即可查看及使用</div>
		</div>
		<div style="height: calc(100% - 90px)">
			<div class="contaner_content_">
				<smCoucher :itemCoupons="objCrad"></smCoucher>
			</div>

			<div class="t-box">
				<div class="Rights-box" v-for="(item, index) in RightsData" :key="index">
					<div class="name">{{ item.name }}</div>
					<div class="value">{{ objCrad[item.typeName] }}</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { reactive, onMounted } from 'vue';
import smCoucher from '../../../component/smCoucher/index.vue';
import { useRoute, useRouter } from 'vue-router';
const emit = defineEmits(['handleComponentClose']);
const router = useRouter();
const route = useRoute();
const props = defineProps({
	// 当前卡卷对象
	params: {
		type: Object,
		default: {},
	},
});

let objCrad = ref({});

const RightsData = reactive([
	{
		name: '包含权益',
		typeName: 'name',
		value: '',
		id: 1,
	},
	{
		name: '卡券类型',
		typeName: 'couponTypeName',
		value: '',
		id: 1,
	},
	{
		name: '卡券品牌',
		typeName: 'brandName',
		value: '',
		id: 1,
	},
	// {
	// 	name: '有效期限',
	// 	typeName: 'expirationTime',
	// 	value: '',
	// 	id: 1,
	// },
	{
		name: '使用规则',
		typeName: 'periodName',
		value: '无门槛，更多使用规则请登录对应平台查看',
		id: 1,
	},
]);

onMounted(() => {
	if (!props.params) return;
	// 处理数据
	handleProcessing();
});

// 处理数据
function handleProcessing() {
	objCrad.value = props.params;
	objCrad.value.couponsType = '5';
	objCrad.value.periodName = '无门槛，更多使用规则请登录对应平台查看';
}

const goBack = () => {
	emit('handleComponentClose');
};
</script>

<style lang="less" scoped>
@import url('./style.less');
</style>
