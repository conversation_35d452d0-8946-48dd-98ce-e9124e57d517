<template>
	<div class="container_wrap">
		<!-- 概况 -->
		<gaikuang :id="id"></gaikuang>
		<!-- 市场调查 -->
		<location :id="id"></location>
		<!-- 社区人口 -->
		<population :id="id"></population>
		<!-- 街道人口 -->
		<populationtwo :id="id"></populationtwo>
		<!-- 客流量 -->
		<populationthree :id="id"></populationthree>
		<!-- 招商 -->
		<business :id="id"></business>
		<!-- 估值 -->
		<value :id="id"></value>
		<!-- 融资方案 -->
		<security :id="id"></security>
		<!-- 信用风险 -->
		<creditrisk :id="id"></creditrisk>
	</div>
</template>

<script setup>
import gaikuang from './components/gaikuang.vue';
import location from './components/location.vue';
import population from './components/population.vue';
import populationtwo from './components/populationtwo.vue';
import populationthree from './components/populationthree.vue';
import business from './components/business.vue';
import value from './components/value.vue';
import security from './components/security.vue';
import creditrisk from './components/creditrisk.vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const id = ref(null);
id.value = route.query.id;
</script>

<style scoped lang="less">
.container_wrap {
	padding: 20px 30px;
}
</style>
