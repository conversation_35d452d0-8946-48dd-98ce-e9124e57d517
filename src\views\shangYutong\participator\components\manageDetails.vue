<template>
	<div class="content_box">
		<div class="title">
			<div class="goback" @click="handleGoBack()">
				<span
					><el-icon> <ArrowLeft /> </el-icon
				></span>
				返回
			</div>
			<div class="card_body">
				<div class="avatar">
					<img src="@/assets/Default.png" alt="" v-if="!route || route.query.avatar == '' || route.query.avatar == '暂无'" />
					<img :src="`${proxyAddress}${route.query.avatar}`" alt="" v-else />
				</div>
				<div class="tips_box">
					<div class="nickName">{{ route ? route.query.companyName : '这是公司名' }}</div>
					<div class="company_name">{{ route ? route.query.borkerName : '这是顾问名' }}</div>
				</div>
			</div>
		</div>
		<div class="container_box">
			<div class="echars_box">
				<div class="tag_box">资产列表</div>
				<div class="echars_main">
					<div class="card_box" v-for="(item, index) in companyDataList" :key="index">
						<div class="left_box">
							<div class="img"><img :src="`${proxyAddress}${item.buildingMainImg}`" alt="" /></div>
							<div class="company_box">
								<div class="company_name">
									{{ item.buildingName }}<span>{{ item.buildingType }}</span>
								</div>
								<div class="address overflowEllpsis">{{ item.street }}</div>
								<div class="tips_box">
									<div class="tips1 overflowEllpsis">物管公司：{{ item.buildingManager }}</div>
									<div class="tips1 overflowEllpsis">产权公司：{{ item.propertyOwner }}</div>
								</div>
							</div>
						</div>
						<div class="center_box">
							<div class="top_box">
								<div class="box1">
									<div class="black1">{{ item.buildYear }}</div>
									<div class="title1">建成年份</div>
								</div>
								<div class="box1">
									<div class="black1">{{ item.maintenance }}</div>
									<div class="title1">维护情况</div>
								</div>
								<div class="box1">
									<div class="black1">{{ item.regionalPotential }}</div>
									<div class="title1">区域潜力</div>
								</div>
							</div>
							<div class="top_box">
								<div class="box1">
									<div class="black1">{{ item.businessDynamism }}</div>
									<div class="title1">商业活力</div>
								</div>
								<div class="box1">
									<div class="black1">{{ item.spendingPower }}</div>
									<div class="title1">人均消费(元)</div>
								</div>
								<div class="box1">
									<div class="black1">{{ item.evaluation }}</div>
									<div class="title1">估值</div>
								</div>
							</div>
						</div>
						<div class="right_box">
							<div class="tips2">
								<div class="label_">评级</div>
								<div class="value_">{{ item.buildingRate }}</div>
							</div>
							<div class="tips2">
								<div class="label_">估值排名</div>
								<div class="value_">{{ item.evaComparableValueRank }}/{{ item.evaComparableValueRankTotal }}</div>
							</div>
							<div class="tips2">
								<div class="label_">证券化排名</div>
								<div class="value_">{{ item.secComparableValueRank }}/{{ item.secComparableValueRankTotal }}</div>
							</div>
						</div>
					</div>
					<div class="page_box">
						<div class="total_box">
							共<span>{{ total }}</span
							>项
						</div>
						<el-pagination
							@current-change="handleCurrentChange"
							:current-page="currentPage"
							layout="prev, pager, next,total, jumper"
							:page-size="10"
							class="mt-4"
							:total="total"
						/>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import manage from './manage.vue';
import { ref, reactive, toRefs, onMounted } from 'vue';
import { companyList } from '@/api/participant.js';
import { getCompanyRelatedBuildingList } from '@/api/syt.js';
import { useRoute, useRouter } from 'vue-router';
import { Edit } from '@element-plus/icons-vue';
const proxyAddress = ref('https://static.biaobiaozhun.com/');
let route = useRoute();
let router = useRouter();
// 返回
function handleGoBack() {
	router.push({
		path: '/shangYutong/participator/components/participant',
		query: {
			name: '管理人',
		},
	});
}
const companyDataList = ref([]);
const total = ref(0);
const currentPage = ref(1);
const handleCurrentChange = (page) => {
	currentPage.value = page;
	getcompanyList();
};
const getcompanyList = async () => {
	const params = {
		companyName: route.query.companyName,
		companyType: route.query.companyType,
		currentPage: currentPage.value,
		pageSize: 10,
		type: route.query.type,
	};
	await getCompanyRelatedBuildingList(params).then((res) => {
		companyDataList.value = res.data.rows;
		total.value = res.data.total;
	});
};
getcompanyList();
</script>

<style lang="less" scoped>
.el-pagination {
	margin-top: -10px !important;
}
.content_box {
	width: 100%;
	// height: 100%;
	// min-height: 100vh;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;

		.goback {
			width: 63px;
			height: 44px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			cursor: pointer;

			span {
				width: 14px;
				height: 14px;
				display: flex;
				justify-content: center;
				align-items: center;
				color: rgba(24, 104, 241, 1);
				font-weight: bold;
				margin-right: 5px;
			}
		}

		.card_body {
			width: 24%;
			height: 44px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			border-radius: 6px;
			padding: 0 15px;
			box-sizing: border-box;
			position: relative;

			&::before {
				content: '';
				width: 1px;
				height: 15px;
				background-color: rgba(231, 231, 231, 1);
				position: absolute;
				left: 0;
			}

			.avatar {
				width: 40px;
				height: 40px;
				border-radius: 8px;
				overflow: hidden;
				margin-right: 10px;

				img {
					width: 100%;
					height: 100%;
				}
			}

			.tips_box {
				width: 200px;
				height: 44px;
				display: flex;
				justify-content: center;
				align-items: flex-start;
				flex-direction: column;

				.nickName {
					font-weight: bold;
				}

				.company_name {
					font-size: 12px;
				}
			}
		}
	}

	.container_box {
		width: 100%;
		// height: 100%;
		// min-height: 100vh;
		padding-top: 10px;
		box-sizing: border-box;
		position: relative;

		.echars_box {
			width: 100%;
			height: calc(100% - 143px);
			padding-top: 60px;
			box-sizing: border-box;
			background-color: rgba(255, 255, 255, 1);
			// margin-top: 10px;
			border-radius: 6px;
			position: relative;

			.tag_box {
				width: auto;
				height: 16px;
				position: absolute;
				left: 0;
				top: 20px;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
			}

			.echars_main {
				width: 100%;
				height: 100%;
				padding: 0 15px;
				box-sizing: border-box;

				.card_box {
					width: 100%;
					height: 136px;
					margin-bottom: 15px;
					background-color: rgba(245, 246, 247, 1);
					border-radius: 6px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					padding: 10px;
					box-sizing: border-box;

					.left_box {
						width: 412px;
						height: 100%;
						display: flex;
						justify-content: flex-start;
						align-items: center;

						.img {
							width: 120px;
							height: 120px;
							border-radius: 6px;
							overflow: hidden;
							display: flex;
							justify-content: center;
							align-items: center;

							img {
								width: auto;
								height: 100%;
							}
						}

						.company_box {
							width: 282px;
							margin-left: 10px;

							.company_name {
								width: 100%;
								font-family: '微软雅黑';
								margin-bottom: 10px;
								font-size: 20px;
								font-weight: 500;
							}

							.address {
								color: rgba(134, 144, 156, 1);
								font-size: 12px;
								margin-bottom: 10px;
							}

							.tips_box {
								color: rgba(134, 144, 156, 1);
								width: 100%;
								font-size: 12px;
							}
						}
					}

					.center_box {
						width: 300px;
						height: 120px;
						margin-left: 30px;
						border-radius: 6px;
						padding: 10px 0;
						border: 1px solid rgba(231, 231, 231, 1);
						box-sizing: border-box;
						display: flex;
						justify-content: space-between;
						align-items: center;
						flex-direction: column;

						.top_box {
							width: 100%;
							height: 48px;
							display: flex;
							justify-content: space-between;
							align-items: center;

							.box1 {
								width: 33%;
								text-align: center;

								.black1 {
									width: 100%;
									margin-bottom: 5px;
									font-weight: bold;
								}

								.title1 {
									width: 100%;
									font-size: 12px;
									color: rgba(134, 144, 156, 1);
								}
							}
						}
					}

					.right_box {
						width: 300px;
						height: 120px;
						margin-left: 30px;
						border-radius: 6px;
						overflow: hidden;
						display: flex;
						justify-content: space-between;
						align-items: center;
						flex-direction: column;

						.tips2 {
							width: 100%;
							height: 34px;
							display: flex;
							justify-content: center;
							align-items: center;
							background-color: rgba(255, 255, 255, 1);

							.label_ {
								width: 80px;
								height: 34px;
								display: flex;
								justify-content: flex-start;
								align-items: center;
								color: rgba(134, 144, 156, 1);
								font-size: 14px;
							}

							.value_ {
								width: 100px;
								height: 34px;
								margin-left: 10px;
								display: flex;
								justify-content: flex-start;
								align-items: center;
								font-weight: bold;
							}
						}
					}
				}

				.page_box {
					width: 100%;
					height: 56px;
					height: auto;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.total_box {
						width: auto;
						height: 56px;
						display: flex;
						justify-content: center;
						align-items: center;

						span {
							color: rgba(3, 93, 255, 1);
						}
					}
				}
			}
		}
	}
}
</style>
