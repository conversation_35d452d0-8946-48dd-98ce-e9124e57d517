<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Document</title>
	</head>
	<body>
		<script>
			const originalArray = [
				{ commercial: '零售', merchant_name: '盒马鲜生', floor: 'B2' },
				{ commercial: '配套', merchant_name: '盒马配送', floor: 'B2' },
				{ commercial: '餐饮', merchant_name: '龍七爷跷脚牛肉面 ebeecake小蜜蜂蛋糕', floor: 'B2' },
				// ...（数组的其余部分）
			];

			const resultArray = [];
			const seenFloors = new Set();

			for (const entry of originalArray) {
				const floor = entry['floor'];
				if (!seenFloors.has(floor)) {
					resultArray.push({ commercial: entry['commercial'], merchant_name: entry['merchant_name'], floor: floor });
					seenFloors.add(floor);
				} else {
					resultArray.push({ commercial: entry['commercial'], merchant_name: entry['merchant_name'] });
				}
			}

			// 显示结果
			for (const entry of resultArray) {
				console.log(entry);
			}
			let data = [
				{ a: '配套', b: '零售', c: '餐饮', d: '休闲', e: '亲子' },
				{ a: 3, b: 3, total: 13, c: 3, d: 3, e: 1 },
				{ a: '23.08%', b: '23.08%', total: '100.0%', c: '23.08%', d: '23.08%', e: '7.69%' },
			];

			let new_array = [];

			Object.keys(data[0]).forEach(function (key) {
				new_array.push({name:data[0][key]});
			});

			console.log(new_array);
		</script>
	</body>
</html>
