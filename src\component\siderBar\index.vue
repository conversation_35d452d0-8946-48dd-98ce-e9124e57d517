<template>
	<div class="sidebar_wrap" :class="{ is_syt: isSYT }">
		<div class="btn_wrap">
			<el-popover
				@show="sidebarActiveKey = item.name"
				:offset="20"
				placement="left"
				trigger="hover"
				v-for="item in sidebarBtns"
				:key="item.name"
				:width="item.width"
				:popper-style="{
					background: '#f7faffe5',
					borderRadius: '1.5rem',
					right: '72px !important',
					zIndex: 999,
				}"
				popper-class="popper_customer"
			>
				<template #reference>
					<div v-if="!(item.name == 'App' && isSYT)" class="btn" :class="{ btn_active: sidebarActiveKey == item.name }" style="cursor: pointer">
						<img v-show="sidebarActiveKey != item.name" :src="item.img" style="width: 24px; height: 24px" />
						<img v-show="sidebarActiveKey == item.name" :src="item.img_active" style="width: 24px; height: 24px" />
						{{ item.name }}
					</div>
				</template>
				<template #default>
					<div class="felx_Center" v-if="item.name == 'App'">
						<img
							style="width: 6.875rem; height: 6.875rem"
							src="http://oa.biaobiaozhun.com/sm/v1/qrCode/generateQRCode?category=BBZ_APP&source=BBZHUN_WEB"
						/>
					</div>
					<div class="felx_Center" v-if="item.name == '小程序'">
						<img style="width: 6.875rem; height: 6.875rem" :src="wxamp" />
					</div>
					<div v-if="item.name == '预约'">
						<div style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem; color: #000">咨询预约</div>
						<el-form ref="siderFormRef" class="form" label-position="top" label-width="auto" :model="formData" :rules="rules">
							<el-form-item label="您的姓名:" prop="name">
								<el-input v-model="formData.name" clearable />
							</el-form-item>
							<el-form-item label="您的电话:" prop="phone">
								<el-input v-model="formData.phone" clearable />
							</el-form-item>
						</el-form>
						<div class="btn" @click="submitForm">立即预约</div>
					</div>
					<div v-if="item.name == '电话'" class="felx_Center">
						<div class="call_left">
							<img src="../../assets/images/home/<USER>/call.png" />
						</div>
						<div class="call_right">
							<div style="font-size: 0.8125rem; font-weight: 500; color: #000; margin-bottom: 0.125rem">服务电话：************</div>
							<div style="font-size: 0.75rem; font-weight: 400; color: #868aa0">周一至周五北京时间 AM8:00-PM5:30</div>
						</div>
					</div>
					<div class="felx_Center" v-if="item.name == '客服'">
						<img style="width: 6.875rem; height: 6.875rem" src="../../assets/images/home/<USER>/wxconnect.png" />
					</div>
				</template>
			</el-popover>
		</div>
		<div class="back_top" @click="backTop" @mouseenter="topActive = true" @mouseleave="topActive = false">
			<img :src="topActive ? top_active : top" style="width: 24px; height: 24px; cursor: pointer" />
		</div>
	</div>
</template>
<script setup>
import top from '../../assets/images/home/<USER>/top.png';
import top_active from '../../assets/images/home/<USER>/top_active.png';
import xiaochengxu from '../../assets/images/home/<USER>';
import xiaochengxuSYT from '../../assets/images/home/<USER>';
import { ElMessage } from 'element-plus';
import { sidebarBtns } from './data';
import { Addcontact } from '../../api/home.js';
import { defineProps } from 'vue';

const props = defineProps({
  // 是否为商宇通
  isSYT: {
    type: Boolean,
    default: false,
  },
});

const sidebarActiveKey = ref('');
const topActive = ref(false);
const formData = ref({
	name: '',
	phone: '',
});
const siderFormRef = ref();
const rules = ref({
	name: [{ required: true, message: '请输入您的姓名', trigger: 'manual' }],
	phone: [
		{ required: true, message: '请输入您的联系电话', trigger: 'manual' },
		{ pattern: /^\d+$/, message: '电话只能输入数字', trigger: 'blur' },
		{ pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' },
	],
});

// 小程序图片
const wxamp = computed(() => {
  return props.isSYT ? xiaochengxuSYT : xiaochengxu;
})

async function submitForm() {
	if (!siderFormRef.value) return;
	await siderFormRef.value[0].validate(async (valid, fields) => {
		if (valid) {
			try {
				const res = await Addcontact(formData.value);
				if (res.code == 200) {
					ElMessage({
						message: '提交成功',
						type: 'success',
						offset: 300,
					});
					formData.value = { name: '', phone: '' };
				}
			} catch (error) {}
		} else {
			console.log('error submit!', fields);
		}
	});
}

function backTop() {
	window.scrollTo({
		top: 0,
		behavior: 'smooth',
	});
}
</script>
<style lang="less" scoped>
.felx_Center {
	display: flex;
	align-items: center;
	justify-content: center;
}

:deep(.popper_customer .el-popper__arrow::before) {
	background-color: #f7faffe5 !important;
	border-color: #f7faffe5 !important;
}
.popper_customer {
	.btn {
		width: 100%;
		height: 2rem;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #0a42f1;
		font-size: 0.875rem;
		font-weight: 400;
		border: 0.0625rem solid #0a42f1;
		cursor: pointer;
	}
	.form {
		:deep(.el-form-item__label) {
			color: #000;
			font-size: 0.875rem;
			font-weight: 400;
		}
	}
}
.sidebar_wrap {
	z-index: 999;
	width: 3rem;
	height: 22.3125rem;
	position: fixed;
	top: 24.125rem;
	right: 1rem;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	.btn_wrap {
		height: 18.8125rem;
		background: #f7faffe5;
		// backdrop-filter: blur(6.25rem);
		border-radius: 1.5rem;
		overflow: hidden;
		padding: 1rem 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		border: 0.0625rem solid #ffffff;

		.btn {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: 0.75rem;
			font-weight: 400;
			color: #5d617f;
		}
		.btn_active {
			color: #0a42f1;
		}
	}
	.back_top {
		height: 3rem;
		background: #f7faffe5;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 1.5rem;
		border: 0.0625rem solid #ffffff;
		cursor: pointer;
	}
}

.is_syt {
  height: 18.3125rem;
}
</style>
