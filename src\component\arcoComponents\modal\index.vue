<template>
	<arco-modal
		v-bind="$attrs"
		:class="[$attrs.class]"
		modal-class="custom-modal"
		:modal-style="{ borderRadius: '12px', overflow: 'hidden' }"
		:footer="false"
	>
		<!-- 动态传递所有命名插槽 -->
		<template v-for="(_, name) in slots" #[name]>
			<slot :name="name"></slot>
		</template>
		<!-- 默认插槽传递 -->
		<slot></slot>
	</arco-modal>
</template>

<script setup>
import { useAttrs, useSlots } from 'vue';

// 如果需要访问 attrs
const attrs = useAttrs();
// 获取插槽内容
const slots = useSlots();
</script>

<style lang="less">
.custom-modal {
	/* 自定义样式 */
	.arco-modal-header {
		background-color: #f7f8fa;
		border-radius: 12px 12px 0 0;
		height: 56px;
		padding: 0 24px;
	}
	.arco-modal-body {
		padding: 20px 24px;
	}
}
</style>
