<template>
	<div class="cangchu_wrap">
		<template v-if="leftData && leftData.length > 1">
			<div class="double_box_wrap" v-for="(element, childIndex) in leftData" :key="childIndex">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">基本信息</div>
					</div>
					<div class="content_wrap" v-if="element.id">
						<div class="content_flex">
							<div class="content_c" v-for="(item, index) in list" :key="index">
								<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
									<div class="content_left">{{ childitem.name }}</div>
									<div class="content_right">{{ element[childitem.key] }}{{ childitem.unit }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 200px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>

				<div class="single_wrap">
					<div class="title1">
						<div class="title">租金信息</div>
					</div>
					<div class="content_wrap">
						<div class="table_wrap" v-if="element.id">
							<arco-table
								:columns="columns"
								:data="element.apartmentAreaVoList"
								:pagination="false"
								:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
							>
							</arco-table>
						</div>
						<div class="empty_wrap" v-else style="min-height: 160px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>

				<div class="single_wrap">
					<div class="title1">
						<div class="title">配套设施</div>
					</div>
					<div class="content_wrap" v-if="element.id">
						<div class="content_s" v-for="(childitem, childIndex) in informationList" :key="childIndex">
							<div class="content_left">{{ childitem.name }}</div>
							<div class="content_right">{{ element[childitem.key] }}</div>
						</div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 144px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</template>
		<template v-else>
			<div class="box_wrap" v-for="(element, childIndex) in leftData" :key="childIndex">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">基本信息</div>
						</div>
						<div class="content_wrap" v-if="element.id">
							<div class="content_flex">
								<div class="content_c" v-for="(item, index) in list" :key="index">
									<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
										<div class="content_left">{{ childitem.name }}</div>
										<div class="content_right">{{ element[childitem.key] }}{{ childitem.unit }}</div>
									</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 200px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">租金信息</div>
						</div>
						<div class="content_wrap">
							<div class="table_wrap" v-if="element.id">
								<arco-table
									:columns="columns"
									:data="element.apartmentAreaVoList"
									:pagination="false"
									:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
								>
								</arco-table>
							</div>
							<div class="empty_wrap" v-else style="min-height: 160px">
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">配套设施</div>
						</div>
						<div class="content_wrap" v-if="element.id">
							<div class="content_s" v-for="(childitem, childIndex) in informationList" :key="childIndex">
								<div class="content_left">{{ childitem.name }}</div>
								<div class="content_right">{{ element[childitem.key] }}</div>
							</div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 144px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import { defineExpose } from 'vue';
import { getBuildApartmentInfo } from '@/api/syt.js';

const props = defineProps({
	buildData: {
		type: Array,
		default: () => [],
	},
});
watch(
	() => props.buildData,
	(newVal) => {
		if (newVal.length > 0) {
			console.log('props', newVal);
		}
	},
	{
		deep: true,
	}
);

const leftData = ref([]);

const columns = ref([
	{
		title: '面积(㎡)',
		dataIndex: 'area',
	},
	{
		title: '租金(元/月)',
		dataIndex: 'rent',
	},
]);

const informationList = ref([
	{
		name: '房间配套设施',
		key: 'roomFacilities',
		value: '卫生间、浴室、办公桌、洗衣机、热水器、油烟机、空调、冰箱、衣柜、1.5M床、 智能门锁、智能水表',
	},
	{
		name: '公共配套设施',
		key: 'publicFacilities',
		value: '电梯、便利店、停车场、健身房、活动场地、地铁、安全监控、书吧',
	},
]);

const list = ref([
	{
		arr: [
			{ name: '竣工时间', key: 'completionTime' },
			{ name: '产权年限', key: 'ownershipPeriod' },
		],
	},
	{
		arr: [
			{ name: '总户数', key: 'householdsNum' },
			{ name: '建筑类型', key: 'buildingType' },
		],
	},
	{
		arr: [
			{ name: '容积率', key: 'floorAreaRatio' },
			{ name: '绿化率', key: 'greeningRate', unit: '%' },
		],
	},
	{
		arr: [
			{ name: '统一供暖', key: 'centralizedHeating' },
			{ name: '供水供电', key: 'waterElectricitySupply' },
		],
	},
]);

const getData = async () => {
	let ids = props.buildData.map((item) => item.id).join(',');
	const res = await getBuildApartmentInfo({
		buildingIds: ids,
	});
	if (res.code === 200) {
		leftData.value = res.data;
	}
};

defineExpose({
	getData,
});
</script>
<style lang="less" scoped>
.cangchu_wrap {
	flex: 1;
	display: flex;
	gap: 16px;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
		.single_wrap {
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			//	.content_wrap {
			//	padding: 20px 16px;
			//	}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
	.double_box_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16px;
		.single_wrap {
			flex: 1;
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
}

.content_wrap {
	padding: 20px 16px;
	.table_wrap {
		width: 100%;
		::v-deep .arco-table .arco-table-th {
			background: #f7f8fa;
		}
	}
	.content_flex {
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		border: 1px solid #e5e6eb;
		border-bottom: 0px;
		.content_c {
			width: 100%;
			display: flex;
		}
	}
	.features {
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.features_Item {
			width: 139px;
			height: 96px;
			padding: 21px 16px;
			border-radius: 4px;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: center;
			background: #f7f8fa;
			.item_img {
				width: 28px;
				height: 28px;
				margin-bottom: 4px;
			}
			.item_content {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 22px;
				.item_content_l {
					width: 16px;
					height: 16px;
					margin: 3px 4px 3px 0;
					.item_content_img {
						width: 16px;
						height: 16px;
					}
				}
				.item_content_text {
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
					text-align: center;
					color: #1d2129;
				}
			}
		}
	}
	.geographic {
		background: #f7f8fa;
		width: calc(100% - 32px);
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
		padding: 9px 16px;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #4e5969;
	}
	.content_ {
		display: flex;
		width: 50%;
		.content_left {
			box-sizing: border-box;
			padding: 9px 16px;
			background: #f7f8fa;
			border-right: 1px solid #e5e6eb;
			border-bottom: 1px solid #e5e6eb;
			font-weight: 500;
			font-size: 14px;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 120px;
			color: #1d2129;
		}
		.content_right {
			width: calc(100% - 120px);
			background: #ffffff;
			border-right: 1px solid #e5e6eb;
			border-bottom: 1px solid #e5e6eb;
			box-sizing: border-box;
			padding: 9px 16px;
			font-weight: 400;
			font-size: 14px;
			line-height: 21px;
			color: #4e5969;
		}
	}
	.content_flex > :nth-child(n) > :nth-child(2) > :nth-child(2) {
		border-right: 0px !important;
	}

	.content_flex > :nth-last-child(1) > :nth-last-child(1) > :nth-child(2) {
		border-right: 0px !important;
	}
}

.content_s {
	display: flex;
	.content_left {
		box-sizing: border-box;
		padding: 9px 16px;
		background: #e8f3ff;
		border-top-left-radius: 4px;
		border-bottom-left-radius: 4px;
		font-weight: 500;
		font-size: 14px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 116px;
		margin: 0 2px 2px 0;
		color: #1868f1;
	}
	.content_right {
		width: calc(100% - 118px);
		background: #f7f8fa;
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
		box-sizing: border-box;
		margin: 0 0 2px 0;
		padding: 9px 16px;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #4e5969;
	}
}
</style>
