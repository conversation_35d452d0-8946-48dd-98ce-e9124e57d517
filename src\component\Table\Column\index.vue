<template>
	<el-table-column
		v-if="!column.slot"
		:type="column.type ?? null"
		:index="column.index"
		:show-overflow-tooltip="column.showOverflowTooltip"
		:label="column.label"
		:prop="column.dataName"
		:width="column.width"
		:min-width="column.minWidth"
		:fixed="column.fixed"
		:render-header="column.renderHeader"
		:sortable="column.sortable"
		:sort-method="column.sortMethod"
		:formatter="column.formatter"
		:align="column.align"
		:header-align="column.headerAlign || 'left'"
		:class-name="column.className"
		:selectable="column.selectable"
		:reserve-selection="column.reserveSelection"
	>
		<template v-if="column.child">
			<Column v-for="(item, index) in column.child" :key="index" :column="item" :formatter="item.formatter">
				<template v-if="item.slot" #[item.slot]="{ row }">
					<slot :name="item.slot" :row="row"></slot>
				</template>
			</Column>
		</template>
	</el-table-column>
	<el-table-column
		v-else
		:prop="column.dataName"
		:label="column.label"
		:width="column.width"
		:fixed="column.fixed"
		:align="column.align"
		:type="column.type ?? null"
		:index="column.index"
		:header-align="column.headerAlign || 'center'"
		:render-header="column.renderHeader"
	>
		<template v-if="column.slot" #default="{ row }">
			<slot :name="column.slot" :row="row"></slot>
		</template>
	</el-table-column>
</template>

<script lang="ts" setup>
import { ColumnProps } from '../types';
import Column from './index.vue';
interface Props {
	column: ColumnProps;
}
defineProps<Props>();
</script>
<style lang="less"></style>
