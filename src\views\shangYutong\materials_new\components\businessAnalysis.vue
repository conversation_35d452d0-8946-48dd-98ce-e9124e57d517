<template>
	<div class="comparison_box">
		<div class="container_box">
			<div class="common_wrap">
				<div class="left_empty_wrap" v-if="tableDatao.length == 0">
					<img :src="add" class="icon" />
					<arco-button type="primary" @click="dialogTableVisible = true">
						<template #icon> <icon-plus /> </template>选择商圈
					</arco-button>
				</div>
				<div v-if="tableDatao && tableDatao.length > 0" class="left_content_wrap">
					<div class="title_wrap">
						<div class="left">
							<arco-button type="primary" @click="dialogTableVisible = true">
								<template #icon> <icon-plus /> </template>选择商圈
							</arco-button>
						</div>
						<div class="right">
							<arco-button @click="clear('left')"> 清除 </arco-button>
						</div>
					</div>
					<div class="table_wrap">
						<arco-table
							row-key="id"
							:data="tableDatao"
							:pagination="false"
							:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
						>
							<template #columns>
								<arco-table-column title="商圈名称" data-index="businessDistrictName" ellipsis tooltip></arco-table-column>
								<arco-table-column title="商圈位置" data-index="district" ellipsis tooltip :width="90"></arco-table-column>
								<arco-table-column title="商圈面积" data-index="area" ellipsis tooltip :width="300">
									<template #cell="scope">
										{{ scope.record.area ? scope.record.area + '万平方米' : '' }}
									</template>
								</arco-table-column>
								<arco-table-column title="商圈定位" data-index="positioning" :width="100" ellipsis tooltip> </arco-table-column>
								<arco-table-column data-index="businessRankAndAll" title="商圈排名" :width="90" align="center"> </arco-table-column>
							</template>
						</arco-table>
					</div>
				</div>
				<div v-if="tableDatao && tableDatao.length > 0 && tableDatat.length == 0" class="right_empty_wrap">
					<img :src="add" class="icon" />
					<arco-button type="primary" @click="dialogTableVisible = true">
						<template #icon> <icon-plus /> </template>选择对比商圈
					</arco-button>
				</div>
				<div v-if="tableDatat && tableDatat.length > 0" class="right_content_wrap">
					<div class="title_wrap">
						<div class="left">
							<arco-button type="primary" @click="dialogTableVisible = true">
								<template #icon> <icon-plus /> </template>选择商圈
							</arco-button>
						</div>
						<div class="right">
							<arco-button @click="clear('right')"> 清除 </arco-button>
						</div>
					</div>
					<div class="table_wrap">
						<arco-table
							row-key="id"
							:data="tableDatat"
							:pagination="false"
							:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
						>
							<template #columns>
								<arco-table-column title="商圈名称" data-index="businessDistrictName" ellipsis tooltip></arco-table-column>
								<arco-table-column title="商圈位置" data-index="district" ellipsis tooltip :width="90"></arco-table-column>
								<arco-table-column title="商圈面积" data-index="area" ellipsis tooltip :width="300">
									<template #cell="scope">
										{{ scope.record.area ? scope.record.area + '万平方米' : '' }}
									</template>
								</arco-table-column>
								<arco-table-column title="商圈定位" data-index="positioning" :width="100" ellipsis tooltip> </arco-table-column>
								<arco-table-column data-index="businessRankAndAll" title="商圈排名" :width="90" align="center"> </arco-table-column>
							</template>
						</arco-table>
					</div>
				</div>
			</div>
			<!-- <div class="table_maints">
				<div class="table_">
					<div class="tag_box">对比商圈一</div>
					<div class="table_1" v-if="tableDatao.length > 0">
						<el-table border :data="tableDatao" height="80px" style="width: 100%">
							<el-table-column prop="businessDistrictName" label="商圈名称" show-overflow-tooltip width="" />
							<el-table-column prop="district" label="商圈位置" width=""> </el-table-column>
							<el-table-column prop="area" label="商圈面积" width="" show-overflow-tooltip>
								<template #default="scope">
									{{ scope.row.area ? scope.row.area + '万平方米' : '' }}
								</template>
							</el-table-column>
							<el-table-column prop="positioning" label="商圈定位" width="" />
							<el-table-column prop="businessRankAndAll" label="商圈排名" width="" />
						</el-table>
					</div>
					<div class="add active" @click="choose(1)" v-else>+ 选择对比商圈</div>
					<div class="clear active" v-if="tableDatao.length > 0" @click="clearDate(tableDatao[0], 1)">× 清空</div>
				</div>
				<div class="table_">
					<div class="top_box">
						<div class="tag_box">对比商圈二</div>
						<div class="table_1" v-if="tableDatat.length > 0">
							<el-table :data="tableDatat" border height="80px" style="width: 100%">
								<el-table-column prop="businessDistrictName" label="商圈名称" show-overflow-tooltip width="" />
								<el-table-column prop="district" label="商圈位置" width=""> </el-table-column>
								<el-table-column prop="area" label="商圈面积" width="" show-overflow-tooltip>
									<template #default="scope">
										{{ scope.row.area ? scope.row.area + '万平方米' : '' }}
									</template>
								</el-table-column>
								<el-table-column prop="positioning" label="商圈定位" width="" />
								<el-table-column prop="businessRankAndAll" label="商圈排名" width="" />
							</el-table>
						</div>
						<div class="add active" @click="choose(2)" v-else>+ 选择对比商圈</div>
						<div class="clear active" v-if="tableDatat.length > 0" @click="clearDate(tableDatat[0], 2)">× 清空</div>
					</div>
				</div>
			</div> -->

			<!-- 对比图 -->
			<div class="echars_box">
				<div class="echars_main">
					<div :class="sixRingDateList.length > 1 ? 'box_' : 'box_ss'">
						<div class="title1">商圈分析</div>
						<div
							:class="sixRingDateList.length > 1 ? 'introduction_content' : 'introduction_contents'"
							:style="{ marginBottom: sixRingDateList.length > 1 ? '0px' : '0px', marginTop: '0px' }"
						>
							<div class="position_content" v-show="tableDatao.length == 0 && tableDatat.length == 0">
								<div class="header_contents">
									<img src="@/assets/nonedata.png" alt="" />
									<div class="contentText">暂无数据</div>
								</div>
							</div>
							<div class="introduction_title">商圈介绍</div>

							<div class="introduction_contentFlex">
								<div
									v-for="(item, index) in sixRingDateList"
									:key="index"
									class="box_left"
									:style="`${sixRingDateList.length > 1 ? 'width: 50%;' : 'width: 100%;'}`"
								>
									<div class="img_box">
										<div class="img_left">
											<el-image
												style="width: 100%; border-radius: 4px; height: 248px"
												:src="item.districtsList.imgList?.[0]"
												:preview-src-list="item.districtsList.imgList"
												:initial-index="0"
												fit="cover"
											/>
										</div>

										<div class="img_left">
											<rat
												class="rat"
												v-if="tableDatao[0]?.id == item?.businessDistrict?.id || tableDatat[0]?.id == item?.businessDistrict?.id"
												:containerIds="'contianer' + item?.businessDistrict?.id + index"
												:polygonArr="item.districtsList.businessCoordinateList"
												style="width: 100%; height: 248px; border-radius: 4px"
											></rat>
										</div>
									</div>

									<div class="boxTitle">
										<div class="titlesq">商圈范围</div>
										<div class="textsq">
											{{ item.districtsList.range }}
										</div>
									</div>
								</div>
							</div>

							<template v-if="sixRingDateList.length > 1">
								<div class="btn_box" v-if="handlerBtnBoxs()?.length > 0" style="margin: 0px 16px 20px 16px">
									<div class="btn_box_container">
										<div class="btn_box_Img">
											<img src="../../../../assets/businessBjimg.png" alt="" />
										</div>
										<div class="btn_box_details">
											{{ handlerBtnBoxs() }}
										</div>
									</div>
									<div class="box_copy" @click="handlerCopy(handlerBtnBoxs())">复制</div>
								</div>
							</template>
						</div>

						<div :class="sixRingDateList.length > 1 ? 'introduction_content' : 'introduction_contents'">
							<div class="position_content" v-show="tableDatao.length == 0 && tableDatat.length == 0">
								<div class="header_contents">
									<img src="@/assets/nonedata.png" alt="" />
									<div class="contentText">暂无数据</div>
								</div>
							</div>
							<div class="introduction_title">商圈内建筑一览</div>
							<div class="table_main" style="height: auto">
								<div
									class="table_"
									v-for="(item, index) in sixRingDateList"
									:key="index"
									style="height: auto"
									:style="`${sixRingDateList.length > 1 ? '' : 'width: 100%;'}`"
								>
									<div class="table_1 table_Content">
										<div class="header_content">
											{{ item?.businessDistrict?.businessDistrictName }}
										</div>
										<arco-table :columns="columns" :bordered="{ cell: true }" :data="item.buildBusinessDistrictList" pagination="10">
											<template #columns>
												<arco-table-column
													v-for="(item, index) in columns"
													:key="index"
													:data-index="item.dataIndex"
													:title="item.title"
													:width="item.width"
												>
													<template #cell="scope">
														<div v-if="item.dataIndex == 'vacancyRate'">{{ scope.record[item.dataIndex] ? scope.record[item.dataIndex] + ' %' : '-' }}</div>
														<div v-else>{{ $utils.isEmpty(scope.record[item.dataIndex]) || scope.record[item.dataIndex] == 0 ? '-' : scope.record[item.dataIndex] }}</div>
													</template>
												</arco-table-column>
											</template>
										</arco-table>
									</div>
								</div>
							</div>
						</div>
						<div :class="sixRingDateList.length > 1 ? 'introduction_content' : 'introduction_contents'">
							<div class="position_content" v-show="tableDatao.length == 0 && tableDatat.length == 0">
								<div class="header_contents">
									<img src="@/assets/nonedata.png" alt="" />
									<div class="contentText">暂无数据</div>
								</div>
							</div>
							<div class="introduction_title">商圈均价统计</div>
							<div class="table_main" style="height: auto; padding: 20px 16px 16px 16px">
								<div
									class="table_"
									v-for="(item, index) in sixRingDateList"
									:key="index"
									style="height: auto"
									:style="`${sixRingDateList.length > 1 ? '' : 'width: 100%;'}`"
								>
									<div class="table_1 table_Content">
										<div class="header_content">
											{{ item?.businessDistrict?.businessDistrictName }}
										</div>
										<arco-table :columns="columnsSixRing" :pagination="false" :bordered="{ cell: true }" :data="item.buildBusinessDistrictAvgList">
											<template #columns>
												<arco-table-column
													v-for="(item, index) in columnsSixRing"
													:key="index"
													:data-index="item.dataIndex"
													:title="item.title"
													:width="item.width"
												>
													<template #cell="scope">
														<div v-if="item.dataIndex == 'vacancyRate'">{{ scope.record[item.dataIndex] ? scope.record[item.dataIndex] + ' %' : '-' }}</div>
														<div v-else>{{ $utils.isEmpty(scope.record[item.dataIndex]) || scope.record[item.dataIndex] == 0 ? '-' : scope.record[item.dataIndex] }}</div>
													</template>
												</arco-table-column>
											</template>
										</arco-table>
									</div>
								</div>
							</div>
							<template v-if="sixRingDateList.length > 1">
								<div
									class="btn_box"
									v-if="handlerPriceStatistics().length > 0"
									style="margin: 0px 16px 20px 16px"
									:style="`${sixRingDateList.length > 1 ? '' : 'width: 100%;'}`"
								>
									<div class="btn_box_container">
										<div class="btn_box_Img">
											<img src="../../../../assets/businessBjimg.png" alt="" />
										</div>
										<div class="btn_box_details">
											{{ handlerPriceStatistics() }}
										</div>
									</div>
									<div class="box_copy" @click="handlerCopy(handlerPriceStatistics())">复制</div>
								</div>
							</template>
						</div>

						<div :class="sixRingDateList.length > 1 ? 'introduction_content' : 'introduction_contents'">
							<div class="position_content" v-show="tableDatao.length == 0 && tableDatat.length == 0">
								<div class="header_contents">
									<img src="@/assets/nonedata.png" alt="" />
									<div class="contentText">暂无数据</div>
								</div>
							</div>
							<div class="introduction_title">商圈评估表</div>
							<div class="table_main" style="height: auto">
								<div
									:class="sixRingDateList.length > 1 ? 'table_ table_line' : 'table_s table_ table_line'"
									style="height: fit-content"
									v-for="(item, index) in sixRingDateList"
									:key="index"
								>
									<div class="textContent" v-for="(itemsColumn, indexsColumn) in labelInfo" :key="indexsColumn">
										<div class="left_text">{{ itemsColumn.label }}</div>
										<div class="right_text">
											{{ item?.businessEvaluateInfo?.[itemsColumn.value] }}
										</div>
									</div>

									<div class="line_bottom_border" v-for="(itemRows, indexRows) in labelInfoRow" :key="indexRows">
										<div
											class="table_ table_line_border"
											style="height: auto; border-radius: 0px"
											v-for="(itemRow, indexRow) in itemRows"
											:key="indexRow"
										>
											<div class="text_f">{{ itemRow.label }}</div>
											<div class="text_t">{{ item?.businessEvaluateInfo?.[itemRow.value] }}</div>
										</div>
									</div>
								</div>
							</div>

							<template v-if="sixRingDateList.length > 1">
								<div class="btn_box" v-if="handlerBtnBox()?.length > 0" style="margin: 0px 16px 20px 16px">
									<div class="btn_box_container">
										<div class="btn_box_Img">
											<img src="../../../../assets/businessBjimg.png" alt="" />
										</div>
										<div class="btn_box_details">
											{{ handlerBtnBox() }}
										</div>
									</div>
									<div class="box_copy" @click="handlerCopy(handlerBtnBox())">复制</div>
								</div>
							</template>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<buildSelect
		key="all"
		v-model="dialogTableVisible"
		:maxSelectNum="2"
		:selectedData="multipleSelection"
		title="选择商圈"
		dialogType="business"
		:searchTypes="['position', 'keyword']"
		:tableColumns="tableColumns"
		tagLabelKey="businessDistrictName"
		@confirm="handleBuildConfirm"
	></buildSelect>
</template>

<script setup>
import buildSelect from '@/component/buildSelect/index.vue';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index'; //处理数值
import rat from '@/RatMap.vue'; //地图
import { getAllBusinessDistrict, getBusinessAnalysis } from '@/api/syt.js';
import { useStore } from '../../../../store';
const store = useStore();
const columnsSixRing = [
	{
		title: '业态',
		dataIndex: 'buildingType',
	},
	{
		title: '租金均价(元/㎡/天)',
		dataIndex: 'rental',
		width: 160,
	},
	{
		title: '物业费均价(元/㎡/月)',
		dataIndex: 'propertyFee',
		width: 170,
	},
	{
		title: '单价(元)',
		dataIndex: 'absoluteValue',
	},
	{
		title: '空置率',
		dataIndex: 'vacancyRate',
	},
];
const columns = [
	{
		title: '建筑名称',
		dataIndex: 'buildingName',
	},
	{
		title: '业态',
		dataIndex: 'buildingType',
	},
	{
		title: '租金（元/㎡/天）',
		dataIndex: 'rental',
	},
	{
		title: '物业费（元/㎡/月）',
		dataIndex: 'propertyFee',
	},
	{
		title: '单价（元）',
		dataIndex: 'absoluteValue',
	},
	{
		title: '空置率',
		dataIndex: 'vacancyRate',
	},
];
const dialogTableVisible = ref(false); //对话框显示
const province = ref(''); //省
const city = ref(''); //市
const essential = ref(''); //关键词
const currentPage = ref(1); //当前页
const total = ref(0); //总条数
const multipleTableRef = ref(null); //多选
const tableData = ref([]); //表格数据
const multipleSelection = ref([]); //选中的数据
const selectedCity = ref([]); //选中的城市
const btnBoxPriceStatistics = ref(null); //价格统计dom

const isExpanded = ref(false);
const isExpandeds = ref(false);
const sixRingDateList = ref([
	//商圈数据
	{
		districtsList: {
			//商圈数据
			businessDistrictName: '',
			range: '',
			imgList: [],
			businessCoordinateList: [],
		},
		buildBusinessDistrictAvgList: [],
		buildBusinessDistrictList: [],
	},
	// {
	// 	districtsList: {
	// 		//商圈数据
	// 		businessDistrictName: '',
	// 		range: '',
	// 		imgList: [],
	// 		businessCoordinateList: [],
	// 	},
	// 	buildBusinessDistrictAvgList: [],
	// 	buildBusinessDistrictList: [],
	// },
]);
// 商圈评估表横向
const labelInfoRow = ref([
	[
		{
			label: '商业设施齐全程度',
			value: 'facilityLevel',
		},
		{
			label: '商业覆盖度',
			value: 'businessCover',
		},
	],
	[
		{
			label: '商业氛围',
			value: 'businessAtmo',
		},
		{
			label: '交通网络',
			value: 'trafficNetwork',
		},
	],
	[
		{
			label: '交通拥堵情况',
			value: 'trafficJam',
		},
		{
			label: '人流量',
			value: 'peopleTraffic',
		},
	],
	[
		{
			label: '消费人群特征',
			value: 'spendPeopleFeature',
		},
		{
			label: '配套设施',
			value: 'facilitySupport',
		},
	],
	[
		{
			label: '商圈管理',
			value: 'businessManage',
		},
		{
			label: '竟争品牌数量',
			value: 'brandCompeteNum',
		},
	],
	[
		{
			label: '竞争形式',
			value: 'businessCompete',
		},
		{
			label: '总分',
			value: 'totalScore',
		},
	],
]);
// 商圈评估表纵向
const labelInfo = ref([
	{
		label: '商圈亮点',
		value: 'businessHighlight',
	},
	{
		label: '商圈不足',
		value: 'businessDefect',
	},
	{
		label: '商圈评价',
		value: 'businessEvaluateDesc',
	},
]);
// 表格
const tableColumns = [
	{
		title: '商圈名称',
		dataIndex: 'businessDistrictName',
		width: '200',
	},
	{
		title: '所在城市',
		dataIndex: 'city',
		// width: '300',
	},
];
const tableDatao = ref([]); //商圈一
const tableDatat = ref([]); //商圈二
const reset = () => {
	selectedCity.value = []; //选中的城市
	province.value = ''; //省
	city.value = ''; //市
	essential.value = ''; //关键词
	currentPage.value = 1; //当前页
	Compared(); //查询
};

// 选中
const handleSelectionChange = (val) => {
	setTimeout(() => {
		let mergedSet = new Set([...multipleSelection.value, ...val]);
		let mergedArray = Array.from(mergedSet);
		let uniqueById = Array.from(new Set(mergedArray.map((item) => item.id))).map((id) => {
			return mergedArray.find((item) => item.id === id);
		});
		// 当前页 tableData.value
		// 当前页选中 val
		// 当前页选中和之前选中的重复的去掉的 uniqueById
		tableData.value.map((item) => {
			uniqueById.map((uniqueItem, index) => {
				if (item.id == uniqueItem.id) {
					const foundInVal = val.some((v) => v.id === uniqueItem.id);
					if (!foundInVal) {
						uniqueById.splice(index, 1);
					}
				}
			});
		});
		multipleSelection.value = uniqueById;
	}, 100);
};

function clear(type) {
	if (type == 'left') {
		tableDatao.value = [];
		multipleSelection.value.shift();
	} else {
		tableDatat.value = [];
		multipleSelection.value.pop();
	}
	save();
}

// 点击选择对比资产
const choose = (item) => {
	dialogTableVisible.value = true;
};

// 复制
function handlerCopy(name) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(name)
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = name;
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

// 均价统计
function handlerPriceStatistics() {
	if (!sixRingDateList.value[0]?.businessDistrict) {
		return '';
	}
	let leftName = sixRingDateList.value[0].businessDistrict.businessDistrictName;
	let rightName = sixRingDateList.value[1].businessDistrict.businessDistrictName;

	let name = '';
	sixRingDateList.value[0].buildBusinessDistrictAvgList.forEach((element) => {
		sixRingDateList.value[1].buildBusinessDistrictAvgList.forEach((item) => {
			if (element.buildingType == item.buildingType) {
				let rental = '';
				let propertyFee = '';
				let absoluteValue = '';
				//rental 租金 propertyFee 物业费 absoluteValue 单价
				if (element.rental && item.rental) {
					if (element.rental > item.rental) {
						// 倍数
						rental = element.rental / item.rental;
						name +=
							leftName +
							element.buildingType +
							'业态租金均价为' +
							element.rental +
							'元/㎡/天，是' +
							rightName +
							'的' +
							formattedMoney(handleNumber(rental)) +
							'倍；';
					} else {
						rental = item.rental / element.rental;
						name +=
							rightName +
							item.buildingType +
							'业态租金均价为' +
							item.rental +
							'元/㎡/天，是' +
							leftName +
							'的' +
							formattedMoney(handleNumber(rental)) +
							'倍；';
					}
				}

				if (element.propertyFee && item.propertyFee) {
					if (element.propertyFee > item.propertyFee) {
						// 倍数
						propertyFee = element.propertyFee / item.propertyFee;
						name +=
							leftName +
							element.buildingType +
							'业态物业费均价为' +
							element.propertyFee +
							'元/㎡/月，是' +
							rightName +
							'的' +
							formattedMoney(handleNumber(propertyFee)) +
							'倍；';
					} else {
						propertyFee = item.propertyFee / element.propertyFee;
						name +=
							rightName +
							item.buildingType +
							'业态物业费均价为' +
							item.propertyFee +
							'元/㎡/月，是' +
							leftName +
							'的' +
							formattedMoney(handleNumber(propertyFee)) +
							'倍；';
					}
				}

				if (element.absoluteValue && item.absoluteValue) {
					if (element.absoluteValue > item.absoluteValue) {
						// 倍数
						absoluteValue = element.absoluteValue / item.absoluteValue;
						name +=
							leftName +
							element.buildingType +
							'业态单价均价为' +
							element.absoluteValue +
							'元，是' +
							rightName +
							'的' +
							formattedMoney(handleNumber(absoluteValue)) +
							'倍；';
					} else {
						absoluteValue = item.absoluteValue / element.absoluteValue;
						name +=
							rightName +
							item.buildingType +
							'业态单价均价为' +
							item.absoluteValue +
							'元，是' +
							leftName +
							'的' +
							formattedMoney(handleNumber(absoluteValue)) +
							'倍；';
					}
				}
			}
		});
	});
	return name;
}

function handlerBtnBoxs() {
	if (sixRingDateList.value.length === 0 || sixRingDateList.value.length === 1) {
		return;
	}
	let obj = sixRingDateList.value[0];
	let obj1 = sixRingDateList.value[1];
	let name = '';
	let lastName = '';
	if (!obj.businessDistrict) {
		return '';
	}
	if (obj.businessDistrict?.area > obj1.businessDistrict?.area) {
		const ratio = obj.businessDistrict.area / obj1.businessDistrict.area; // 倍数
		name =
			obj.businessDistrict.businessDistrictName +
			'的面积为' +
			obj.businessDistrict.area +
			'万平方米，是' +
			obj1.businessDistrict.businessDistrictName +
			'的' +
			formattedMoney(handleNumber(ratio)) +
			'倍';
	} else if (obj1.businessDistrict?.area > obj.businessDistrict?.area) {
		const ratio = obj1.businessDistrict.area / obj.businessDistrict.area; // 倍数
		name =
			obj1.businessDistrict.businessDistrictName +
			'的面积为' +
			obj1.businessDistrict.area +
			'万平方米，是' +
			obj.businessDistrict.businessDistrictName +
			'的' +
			formattedMoney(handleNumber(ratio)) +
			'倍';
	}
	let str = tableDatao.value[0]?.businessRankAndAll;
	let str1 = tableDatat.value[0]?.businessRankAndAll;
	if (str == undefined || str1 == undefined) {
		lastName = '';
	} else {
		var substr = str.split('/')[0]; // 分隔字符串并获取第一个元素
		var substr1 = str1.split('/')[0]; // 分隔字符串并获取第一个元素

		if (Number(substr) > Number(substr1)) {
			let num = Number(substr) - Number(substr1);

			lastName =
				obj.businessDistrict.businessDistrictName +
				'的商圈排名为' +
				substr1 +
				'，比' +
				obj1.businessDistrict.businessDistrictName +
				'商圈高' +
				num +
				'位';
		} else if (Number(substr1) > Number(substr)) {
			let num = Number(substr1) - Number(substr);

			lastName =
				obj1.businessDistrict.businessDistrictName +
				'的商圈排名为' +
				substr +
				'，比' +
				obj.businessDistrict.businessDistrictName +
				'商圈高' +
				num +
				'位';
		}
	}

	return name + '；' + lastName;
}

// 比较
function handlerBtnBox() {
	if (sixRingDateList.value.length === 0 || sixRingDateList.value.length === 1) {
		return;
	}
	let obj = sixRingDateList.value[0];
	let obj1 = sixRingDateList.value[1];
	if ((obj && obj1 && obj.businessEvaluateInfo == undefined) || obj.businessEvaluateInfo == undefined) return '';
	if (obj.businessEvaluateInfo.totalScore > obj1.businessEvaluateInfo.totalScore) {
		let name = '';
		let lastName = '其中';

		const ratio = obj.businessEvaluateInfo.totalScore / obj1.businessEvaluateInfo.totalScore; // 倍数
		name =
			obj.businessDistrict.businessDistrictName +
			'的商圈评估表总分为' +
			obj.businessEvaluateInfo.totalScore +
			'比' +
			obj1.businessDistrict.businessDistrictName +
			'高' +
			formattedMoney(handleNumber(ratio)) +
			'倍，';

		if (obj.businessEvaluateInfo.facilityLevel > obj1.businessEvaluateInfo.facilityLevel) {
			lastName += '商业设施齐全程度，';
		}
		if (obj.businessEvaluateInfo.businessCover > obj1.businessEvaluateInfo.businessCover) {
			lastName += '商业覆盖度，';
		}
		if (obj.businessEvaluateInfo.businessAtmo > obj1.businessEvaluateInfo.businessAtmo) {
			lastName += '商业氛围，';
		}
		if (obj.businessEvaluateInfo.trafficNetwork > obj1.businessEvaluateInfo.trafficNetwork) {
			lastName += '交通网络，';
		}
		if (obj.businessEvaluateInfo.trafficJam > obj1.businessEvaluateInfo.trafficJam) {
			lastName += '交通拥堵情况，';
		}
		if (obj.businessEvaluateInfo.peopleTraffic > obj1.businessEvaluateInfo.peopleTraffic) {
			lastName += '人流量，';
		}
		if (obj.businessEvaluateInfo.spendPeopleFeature > obj1.businessEvaluateInfo.spendPeopleFeature) {
			lastName += '消费人群特征，';
		}
		if (obj.businessEvaluateInfo.facilitySupport > obj1.businessEvaluateInfo.facilitySupport) {
			lastName += '配套设施，';
		}
		if (obj.businessEvaluateInfo.businessManage > obj1.businessEvaluateInfo.businessManage) {
			lastName += '商圈管理，';
		}
		if (obj.businessEvaluateInfo.brandCompeteNum > obj1.businessEvaluateInfo.brandCompeteNum) {
			lastName += '竟争品牌数量，';
		}
		if (obj.businessEvaluateInfo.businessCompete > obj1.businessEvaluateInfo.businessCompete) {
			lastName += '竞争形式，';
		}
		if (lastName) {
			lastName += '得分高于' + obj1.businessDistrict.businessDistrictName;
			return name + lastName;
		}
	} else {
		let name = '';
		let lastName = '其中';

		const ratio = obj1.businessEvaluateInfo.totalScore / obj.businessEvaluateInfo.totalScore; // 倍数

		name =
			obj1.businessDistrict.businessDistrictName +
			'的商圈评估表总分为' +
			obj1.businessEvaluateInfo.totalScore +
			'比' +
			obj.businessDistrict.businessDistrictName +
			'高' +
			formattedMoney(handleNumber(ratio)) +
			'倍，';

		if (obj1.businessEvaluateInfo.facilityLevel > obj.businessEvaluateInfo.facilityLevel) {
			lastName += '商业设施齐全程度，';
		}
		if (obj1.businessEvaluateInfo.businessCover > obj.businessEvaluateInfo.businessCover) {
			lastName += '商业覆盖度，';
		}
		if (obj1.businessEvaluateInfo.businessAtmo > obj.businessEvaluateInfo.businessAtmo) {
			lastName += '商业氛围，';
		}
		if (obj1.businessEvaluateInfo.trafficNetwork > obj.businessEvaluateInfo.trafficNetwork) {
			lastName += '交通网络，';
		}
		if (obj1.businessEvaluateInfo.trafficJam > obj.businessEvaluateInfo.trafficJam) {
			lastName += '交通拥堵情况，';
		}
		if (obj1.businessEvaluateInfo.peopleTraffic > obj.businessEvaluateInfo.peopleTraffic) {
			lastName += '人流量，';
		}
		if (obj1.businessEvaluateInfo.spendPeopleFeature > obj.businessEvaluateInfo.spendPeopleFeature) {
			lastName += '消费人群特征，';
		}
		if (obj1.businessEvaluateInfo.facilitySupport > obj.businessEvaluateInfo.facilitySupport) {
			lastName += '配套设施，';
		}
		if (obj1.businessEvaluateInfo.businessManage > obj.businessEvaluateInfo.businessManage) {
			lastName += '商圈管理，';
		}
		if (obj1.businessEvaluateInfo.brandCompeteNum > obj.businessEvaluateInfo.brandCompeteNum) {
			lastName += '竟争品牌数量，';
		}
		if (obj1.businessEvaluateInfo.businessCompete > obj.businessEvaluateInfo.businessCompete) {
			lastName += '竞争形式，';
		}
		if (lastName) {
			lastName += '得分高于' + obj.businessDistrict.businessDistrictName;
			return name + lastName;
		}
	}
}

// 修改城市
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const queryParams = computed(() => {
	return {
		city: province.value,
		district: city.value,
		keyword: essential.value,
		current: currentPage.value,
		// year: 2024,
		size: 10,
	};
});
onMounted(() => {
	Compared();
});
//对比资产分页查询
const handleCurrentChange = (val) => {
	currentPage.value = val;
	Compared();
};
// 查询
const Compared = async () => {
	await getAllBusinessDistrict(queryParams.value)
		.then((res) => {
			tableData.value = [];
			tableData.value = res.data.rows;
			nextTick(() => {
				tableData.value.map((v) => {
					multipleSelection.value.map((i) => {
						if (v.id == i.id) {
							multipleTableRef.value.toggleRowSelection(v, true);
						}
					});
				});
			});
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
// 全选
const toggleSelection = (rows, isSelect) => {
	if (rows) {
		rows.forEach((row) => {
			multipleTableRef.value.toggleRowSelection(row, undefined, isSelect);
		});
	} else {
		multipleTableRef.value.clearSelection();
	}
};

// 清空
function handelClear(row) {
	multipleSelection.value.forEach((item, indexs) => {
		if (item.id == row.id) {
			multipleSelection.value.splice(indexs, 1);
		}
	});
}

//清空资产选项
const clearDate = (row, type, index) => {
	if (type === 1) {
		//资产一清空
		tableDatao.value = [];
		handelClear(row);
	} else if (type === 2) {
		//资产二清空
		tableDatat.value = [];
		handelClear(row);
	} else {
		// 弹窗内资产清空
		multipleSelection.value.splice(index, 1);
	}
	if (tableData.value?.length > 0) {
		// 删除table选中的数据后，清空table选中的数据
		tableData.value.forEach((item) => {
			if (item.id == row.id) {
				toggleSelection([row]);
			}
		});
	}
	if (sixRingDateList.value.length > 0) {
		sixRingDateList.value.map((item, key) => {
			if (item?.businessDistrict && item?.businessDistrict.businessDistrictName == row.businessDistrictName) {
				sixRingDateList.value.splice(key, 1);
			}
		});
	}
};

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	save();
}

// 确定
const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		ElMessage({
			message: multipleSelection.value.length == 0 ? '至少选一个商圈' : '最多选择两个商圈',
			type: 'error',
		});
	} else {
		let ids = multipleSelection.value.map((item) => item.id).join(',');
		getBusinessAnalysis({ businessDistrictId: ids }).then((res) => {
			sixRingDateList.value = [];
			tableDatao.value = [];
			tableDatat.value = [];
			if (res.code === 200) {
				nextTick(() => {
					// tableDatao.value.push(res.data[0].businessDistrict);
					// tableDatat.value.push(res.data[1].businessDistrict);
					sixRingDateList.value = res.data;
					sixRingDateList.value.forEach((element, index) => {
						multipleSelection.value.forEach((item) => {
							if (item.id === element.businessDistrict.id && index === 0) {
								tableDatao.value.push(item);
							}
							if (item.id === element.businessDistrict.id && index === 1) {
								tableDatat.value.push(item);
							}
						});
						let businessCoordinateLists = [];
						let imgUrlList = [];
						let imgUrl = element.businessDistrict.businessDistrictPics.split(',');
						imgUrl.forEach((item) => {
							imgUrlList.push(`${store.imagePathPrefix}${item}`);
						});
						element.businessCoordinateList.forEach((item) => {
							businessCoordinateLists.push([item.lng, item.lat]);
						});
						element.districtsList = {
							businessDistrictName: element.businessDistrict.businessDistrictName,
							range: element.range,
							imgList: imgUrlList,
							businessCoordinateList: businessCoordinateLists,
						};
					});

					setTimeout(() => {
						btnBoxPriceStatistics.value?.offsetHeight > 60 ? (isExpandeds.value = true) : (isExpandeds.value = false);
					}, 100);
				});
			}
			dialogTableVisible.value = false;
		});
	}
};
</script>
<style scoped lang="less">
::v-deep .arco-btn-size-medium {
	border-radius: 4px !important;
}
.comparison_box {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 16px;
		box-sizing: border-box;
		.table_maints {
			padding: 0 !important;
		}
		.table_main,
		.table_maints {
			display: flex;
			justify-content: space-between;
			padding: 20px 16px 16px 16px;
			gap: 16px;
			min-height: 335px;
			.table_s {
				width: 100% !important;
			}
			.table_ {
				width: calc(50% - 0px);
				height: 144px;
				border-radius: 6px;
				background-color: rgba(255, 255, 255, 1);
				position: relative;

				.tag_box {
					width: auto;
					height: 16px;
					position: absolute;
					left: 0;
					top: 20px;
					font-size: 14px;
					font-weight: bold;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&::before {
						content: '';
						width: 4px;
						height: 16px;
						background-color: rgba(24, 104, 241, 1);
						margin-right: 10px;
					}
				}

				.table_1 {
					width: 96%;
					position: absolute;
					bottom: 10px;
					left: 2%;
					&::v-deep .el-table--fit {
						border-radius: 8px;
					}

					&::v-deep .el-table th {
						background-color: rgba(245, 245, 245, 1);
					}
				}

				.add {
					width: 96%;
					height: 90px;
					position: absolute;
					bottom: 10px;
					left: 2%;
					border-radius: 6px;
					border: 1px solid rgba(231, 231, 231, 1);
					display: flex;
					justify-content: center;
					align-items: center;
					color: rgba(3, 93, 255, 1);
					font-size: 14px;
					font-weight: bold;
				}

				.clear {
					width: auto;
					height: 20px;
					position: absolute;
					top: 15px;
					right: 15px;
					font-size: 14px;
					color: rgba(24, 104, 241, 1);
				}
			}
		}
		.echars_box {
			width: calc(100% - 32px);
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			padding: 4px 16px 16px 16px;
			border-radius: 6px;
			.tag_box {
				width: auto;
				height: 24px;
				padding: 16px 0;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
				span {
					margin-left: 10px;
					font-size: 12px;
					display: flex;
					line-height: normal;
					// justify-content: flex-start;
					// align-items: center;
					&:first-child {
						&::after {
							content: '';
							width: 8px;
							height: 12px;
							margin-top: 2.5px;
							margin-left: 10px;
							background-color: rgba(4, 80, 218, 1);
						}
					}
					&:last-child {
						&::after {
							content: '';
							width: 8px;
							margin-top: 2.5px;
							height: 12px;
							margin-left: 10px;
							background-color: rgba(30, 170, 117, 1);
						}
					}
				}
			}
			.echars_main {
				width: 100%;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				height: 100%;
				.box_ss > :nth-child(2n) {
					margin-right: 16px;
				}
				.box_,
				.box_ss {
					width: calc(100% - 0px);
					border-radius: 6px;
					box-sizing: border-box;
					height: calc(100vh - 0);
					display: flex;
					flex-wrap: wrap;
					.introduction_contents {
						position: relative;
						border: 1px solid #e5e6eb;
						border-radius: 4px;
						width: calc(50% - 10px);
						margin-bottom: 16px !important;
						// margin-bottom: 36px;
					}
					.introduction_content {
						width: 100%;
						border: 1px solid #e5e6eb;
						border-radius: 4px;
						margin-top: 16px;
					}
					.introduction_title {
						width: calc(100% - 40px);
						height: 48px;
						border-bottom: 1px solid #e5e6eb;
						padding: 0 20px;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						background-color: #f7f8fa;
						font-weight: 500;
						font-size: 16px;
						line-height: 48px;
						color: #1d2129;
					}
					.introduction_contentFlex {
						display: flex;
						padding: 20px 16px 0px 16px;
						gap: 16px;
						min-height: 335px;
					}
					.title1 {
						width: 100%;
						height: 60px;
						font-weight: 500;
						font-size: 20px;
						line-height: 60px;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						color: #1d2129;
						&::before {
							content: '';
							width: 5px;
							height: 14px;
							background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
							border-radius: 10px;
							margin-right: 8px;
						}
					}

					.box_left {
						// gap: 16px;
						// height: 380px;
						.boxTitle {
							display: flex;
							height: 40px;
							border-radius: 4px;
							padding: 0px 20px;
							justify-content: flex-start;
							border-radius: 3px;
							border: 1px solid #e7e7e7;
							margin: 8px 0 16px 0px;
							background: #f7f8fa;

							.titlesq {
								width: 56px;
								margin-right: 12px;
								line-height: 40px;
								font-size: 12px;
								font-weight: 400;
								color: #1d2129;
							}
							.textsq {
								width: calc(100% - 68px);
								font-size: 14px;
								font-weight: 400;
								line-height: 40px;
								color: #4e5969;
							}
						}

						.img_box {
							height: 248px;
							display: flex;
							gap: 8px;
							.img_leftTitle {
								font-size: 12px;
								font-weight: 400;
								line-height: 24px;
								color: #1d2129;
							}
							.img_left {
								width: calc(50% - 0px);
								height: 248px;
								position: relative;

								.img_boxNum {
									position: absolute;
									bottom: 6px;
									left: 6px;
									font-size: 12px;
									padding: 2px 8px;
									font-weight: 500;
									line-height: 20px;
									color: #fff;
									background: #00000099;
								}
							}
						}
					}
				}
			}
		}
	}
}
.title_box {
	width: 100%;
	max-height: 100px;
	overflow: scroll;
	border-top: 1px solid rgba(231, 231, 231, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	box-sizing: border-box;
	display: flex;
	flex-wrap: wrap;
	.tab {
		width: 31%;
		height: 32px;
		margin: 8px 15px 8px 0;
		background-color: rgba(245, 246, 247, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
		position: relative;
		text-wrap: nowrap;
		.det {
			width: 10px;
			height: 10px;
			position: absolute;
			right: 10px;
			font-size: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: rgba(201, 205, 212, 1);
			cursor: pointer;
		}
	}
}
.search_box {
	width: 100%;
	height: auto;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
	.box_1 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		border: 1px solid rgba(231, 231, 231, 1);
		box-sizing: border-box;

		::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
			box-shadow: 0;
		}

		.label {
			width: 50%;
			height: 100%;
			font-size: 14px;
			color: rgba(134, 144, 156, 1);
			background-color: rgba(245, 246, 247, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.box_2 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		box-sizing: border-box;
	}
}
.table_2 {
	width: 100%;
	height: 308px;
	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}
.btn_box {
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-radius: 6px;
	background-image: url(@/assets/businessBj.png);
	background-size: 100% 100%;
	font-size: 14px;
	color: #1d2129;
	line-height: 20px;
	padding: 10px 18.5px 10px 19.63px;
	position: relative;
	.btn_box_container {
		display: flex;
		width: 100%;
	}
	.btn_box_Img {
		width: 40px;
		height: 40px;
		margin-right: 11.78px;
		img {
			width: 40px;
			height: 40px;
		}
	}
	.btn_box_details {
		width: calc(100% - 122px);
		display: flex;
		align-items: center;
	}
	.box_copy {
		width: 60px;
		height: 32px;
		text-align: center;
		background: #e8f3ff;
		border-radius: 4px;
		border: 1px solid #1868f1;
		font-size: 14px;
		line-height: 32px;
		font-weight: 400;
		cursor: pointer;
		color: #1868f1;
	}
}

.table_Content {
	width: 100% !important;
	position: unset !important;
	height: auto;
	.header_content {
		height: 40px;
		line-height: 40px;
		text-align: center;
		color: #1d2129;
		font-size: 14px;
		font-weight: 500;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		background: #f7f8fa;
		border: 1px solid #e5e6eb;
		border-bottom: 0px;
	}
	::v-deep .arco-table-container {
		border-top-left-radius: 0px !important;
		border-top-right-radius: 0px !important;
		border-bottom-left-radius: 4px !important;
		border-bottom-right-radius: 4px !important;
		.arco-table-th {
			background: #f7f8fa !important;
		}
		.arco-table-td-content {
			color: #4e5969 !important;
		}
		.arco-table-content {
			border-bottom-left-radius: 4px !important;
			border-bottom-right-radius: 4px !important;
			tbody > :nth-last-child(1) > :nth-last-child(1) {
				border-bottom-right-radius: 4px !important;
			}
			tbody > :nth-last-child(1) > :nth-child(1) {
				border-bottom-left-radius: 4px !important;
			}
		}
	}
	::v-deep .arco-pagination-item {
		min-width: 24px !important;
		height: 24px !important;
		font-size: 12px !important;
		line-height: 24px !important;
	}
}

.table_line {
	// width: 49.2% !important;
	border: 1px solid #e7e7e7;
	.textContent {
		display: flex;
		// height: 60px;
		border-top: 1px solid #e7e7e7;
		.left_text {
			display: flex;
			align-items: center;
			background: #f7f8fa;
			width: 165px;
			min-height: 90px;
			padding: 0 16px;
			color: #1d2129;
			font-weight: 500;
			font-size: 14px;
		}
		.right_text {
			display: flex;
			align-items: center;
			width: calc(100% - 227px);
			font-size: 14px;
			font-weight: 400;
			padding: 12px 16px;
			line-height: 20px;
			color: #4e5969;
			display: flex;
		}
	}
}

.table_line > :nth-child(1) {
	border-top: 0px solid #e7e7e7;
}

.table_line_border {
	display: flex;
	height: 40px !important;
	align-items: center;
	border-right: 1px solid #e7e7e7;
	& > :nth-child(n) {
		// width: 50%;
		padding: 0 16px;
	}

	.text_f {
		background: #f7f8fa;
		width: 164px;
		font-weight: 500;
		font-size: 14px;
		line-height: 40px;
		color: #1d2129;
	}
	.text_t {
		width: calc(100% - 227px);
		font-weight: 500;
		font-size: 14px;
		line-height: 40px;
		color: #1868f1;
	}
}

.line_bottom_border {
	display: flex;
	border-top: 1px solid #e7e7e7;

	> :nth-last-child(1) {
		border-right: 0px solid #e7e7e7;
	}
}

::v-deep .rat {
	.amap-logo {
		display: none !important;
	}
}

.btnBoxPriceStatisticsDom {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	overflow: hidden;
	text-overflow: ellipsis;
}

.box_copyContent {
	width: 100%;
	display: flex;
	justify-content: end;
	margin-bottom: -10px;
}

.box_copyContent > :nth-child(n) {
	cursor: pointer;
	color: #1868f1;
}

.position_content {
	position: absolute;
	top: 48px;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: calc(100% - 48px);
	background: #fff;
	z-index: 999;
	.header_contents {
		width: 100%;
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
		height: calc(100% - 48px);
		padding-bottom: 48px;

		img {
			width: 74px;
			height: 74px;
		}
		.contentText {
			font-weight: 400;
			font-size: 14px;
			line-height: 22px;
			color: #86909c;
		}
	}
}

.common_wrap {
	padding: 20px 16px;
	background-color: #fff;
	display: flex;
	gap: 16px;
	border-radius: 0px 4px 4px 4px;
	.left_empty_wrap,
	.left_content_wrap,
	.right_empty_wrap,
	.right_content_wrap {
		flex: 1;
	}
	.left_empty_wrap,
	.right_empty_wrap {
		border: 1px solid #e5e6eb;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 4px;
		padding: 14px 0;
		.icon {
			width: 64px;
			height: 64px;
		}
	}
	.title_wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12px;
	}
}
</style>
