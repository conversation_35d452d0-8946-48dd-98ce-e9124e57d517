import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import 'element-plus/theme-chalk/index.css';
import App from './App.vue';
import router from '@/router';
import { openPop } from '@/component/PopWindow/script.js';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import * as utils from './utils/index'; // 公用方法文件
import { dragTable } from '@/utils/common'; // 表格横向拖拽方法
import { formattedMoney } from 'UTILS'; // 千分符
import { createPinia } from 'pinia';
import { vuexStore, useStore } from './store';
import Progress from '@/component/Progress/index.vue';
import ArcoVue from '@arco-design/web-vue';
// 额外引入图标库
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import '@arco-design/web-vue/dist/arco.css';

//引入公用css
import './assets/css/public.less';
const app = createApp(App); // 生成 Vue 实例 app
const pinia = createPinia();
for (const [key, component] of Object.entries(ElementPlus)) {
	app.component(key, component);
}
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
	app.component(key, component);
}
app.component('Progress', Progress);
app.config.globalProperties.$dragTable = dragTable; // 表格横向拖拽方法注入
app.config.globalProperties.$utils = utils; // 公用方法文件
app.config.globalProperties.$formattedMoney = formattedMoney; // 千分符
app.config.globalProperties.$vuexStore = vuexStore; // vuexStore
app.config.globalProperties.$useStore = useStore; // useStore

app.provide('$openPop', openPop);
app.use(ArcoVue, {
	// 用于改变使用组件时的前缀名称
	componentPrefix: 'arco',
});
app.use(ArcoVueIcon);
app.use(pinia).use(router);
app.use(ElementPlus); // 引用 ElementPlus 组件库
app.mount('#app');
export const globals = app.config.globalProperties;
