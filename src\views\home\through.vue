<template>
	<el-dialog
		:title="`${$vuexStore.state.userInfo.userName || '用户名'}，您好！`"
		v-model="dialogVisible"
		width="640px"
		class="purchase_dialog"
		align-center
		style="border-radius: 16px; background: linear-gradient(180deg, #ecf3fb 0%, #f5f7f9 100%)"
		:show-close="true"
		:close-on-click-modal="false"
		@close="handleClose"
		custom-class="purchase-dialog"
	>
		<div
			class="purchase_dialog_content"
			:style="{ background: couponVisible ? '#0A42F10F' : 'linear-gradient(111.61deg, #30A8FF 35.81%, #083EF0 96.36%)' }"
		>
			<div class="purchase_dialog_content_left" v-if="!couponVisible">
				<div class="purchase_dialog_content_left_title">新用户免费试用7天</div>
				<div class="purchase_dialog_content_left_desc">下载试用版本即可体验</div>
			</div>

			<div class="purchase_dialog_content_lefts" v-if="couponVisible">
				<div class="purchase_dialog_content_left_title">您已免费试用过</div>
				<div class="purchase_dialog_content_left_desc">下载客户端开通体验</div>
			</div>

			<div class="purchase_dialog_content_right">
				<div class="platform-btn" @click="downloadWindows">
					<div>
						<img src="@/assets/images/home/<USER>" alt="" />
					</div>
					Windows
				</div>
				<div class="platform-btn" @click="downloadMac">
					<div>
						<img src="@/assets/images/home/<USER>" alt="" />
					</div>
					macOS
				</div>
			</div>
		</div>

		<!-- 商估通订阅免费领取功能暂时注释 -->
		<!-- <div class="vip-card" v-if="!equityCard">
			<div class="card-info">
				<div class="card-title">商估通权益卡</div>
				<div class="card-subtitle">免费试用1个月</div>
			</div>
			<el-button class="free-btn" type="primary" plain @click="handleFreeTrial"> 免费领取 </el-button>
		</div>

		<div class="success-notice" v-if="equityCard">
			<div class="notice-title">1个月体验券 领取成功！</div>
			<div class="notice-detail">
				可前往【<router-link to="/profile/browsingHistory"><span class="notice_soon">个人中心-权益中心-我的权益卡券</span></router-link
				>】查看或开通权益，或<span class="notice_soon" @click="handleUse">立即使用</span>。
			</div>
		</div> -->

		<div class="purchase-info" v-if="!scanCodePayment">
			<div class="purchaseFlex">
				<div class="purchase-title">
					<div>购买：</div>
					<div class="product-name">商估通权益卡-年卡</div>
				</div>
				<div class="price">
					<span class="symbol">¥</span>
					<span class="number">{{ purchaseObj.payableAmount }}</span>
					<!-- <div class="discount">已优惠 ¥ {{ purchaseObj.discountAmount }}</div> -->
					<div class="discount"></div>
				</div>
			</div>

			<div class="price-info">
				<div class="amount-control">
					<span class="label">购买数量</span>
					<el-input-number v-model="amount" :min="1" @change="handlePriceChangeAll" :max="99" controls-position="right" />
				</div>
				<div class="duration">
					<div class="tipd">购买时长：12个月/次</div>
					<div class="tip">使用后根据购买市场相应延长会员权益的有效期限</div>
				</div>
			</div>

			<div class="action-buttons">
				<!-- <el-button class="cart-btn" @click="addToCart">加入购物车</el-button> -->
				<el-button class="buy-btn" @click="purchase">立即购买</el-button>
			</div>
		</div>
		<div class="scan-payment" v-if="scanCodePayment">
			<!-- 头部返回 -->
			<div class="header">
				<el-icon class="back-icon" @click="handleBack">
					<ArrowLeft />
				</el-icon>
				<span>扫码支付</span>
			</div>

			<!-- 支付信息卡片 -->
			<div class="payment-card">
				<div class="agreement_section" v-show="paymentStatus == '' && checkbox">
					<el-skeleton-item variant="image" style="width: 100%; height: 100%; background: #fff" />
				</div>
				<div class="agreement_section" v-show="paymentStatus !== '' && checkbox">
					<el-skeleton-item variant="image" v-if="!paymentStatus" style="width: 100%; height: 100%; background: #fff" />
					<iframe
						:src="paymentURL"
						v-show="paymentStatus === 'ALI_PC'"
						frameborder="no"
						border="0"
						marginwidth="0"
						marginheight="0"
						scrolling="no"
						width="200"
						height="200"
						style="overflow: hidden; transform: scale(0.63); transform-origin: 27px 25px; /* 确保从左上角开始缩放 */"
					>
					</iframe>
					<canvas v-show="paymentStatus === 'WX_NATIVE'" ref="qrcodeCanvas" class="qrcode"></canvas>
				</div>
				<div class="agreement-section" v-show="!checkbox">
					<div class="agreement-box">
						<div class="agreement-title">同意即可订阅</div>
						<div class="agreement-link" @click="showShangqiuTong = true">《权益订阅服务协议》</div>
						<el-button type="primary" class="agree-btn" size="small" @click="handleAgree" round>同意</el-button>
					</div>
				</div>
				<div class="payment-info-section">
					<div class="payment-info">
						<div class="payment-label">共计支付：</div>
						<div class="payment-amount">
							<span class="currency">¥</span>
							<span class="amount">{{ purchaseObj.payableAmount }}</span>
							<!-- <span class="discount">已优惠 ¥ {{ purchaseObj.discountAmount }}</span> -->
						</div>
					</div>

					<div class="payment-method">
						<img src="@/assets/alipay-icon.png" alt="支付宝" class="payment-icon" />
						<img src="@/assets/wechat-icon.png" alt="微信" class="payment-icon" />
						<span class="payment-text">支付宝/微信扫码支付</span>
						<div class="content_box_bottom">
							<el-radio-group v-model="paymentStatus" @change="handlePayment">
								<el-radio value="ALI_PC" size="large">支付宝</el-radio>
								<el-radio value="WX_NATIVE" size="large">微信</el-radio>
							</el-radio-group>
						</div>
					</div>
				</div>
			</div>
		</div>
	</el-dialog>

	<SuccessToast ref="successToast" />

	<cardPopUp ref="cardPopUps" @handleConfirm="handleConfirm" />

	<buySuccess ref="buySuccessRef" @handleUseNow="handleUseNow" />

	<el-dialog v-model="showShangqiuTong" fullscreen>
		<shangYutong @handleReturn="handleReturn"></shangYutong>
	</el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { getUserExtension, sendSgtCoupon } from '@/api/layout.js';
import { ElMessage } from 'element-plus';
import QRCode from 'qrcode';
import { vuexStore } from '@/store';
import { getSgtCoupon, getOrderCoupon, activeCoupon } from '@/api/equityTerm.js';
import { orderOreate, orderStatus, addShoppingCart, getDiscount } from '@/api/rights';
import shangYutong from '../equityServices/shangqiuTong.vue';
import { getCouponDetail } from '@/api/equityTerm.js';
import SuccessToast from '../../component/SuccessToast/index.vue';
import cardPopUp from '../../component/cardPopUp/index.vue';
import buySuccess from '../../component/buySuccess/index.vue';
const showShangqiuTong = ref(false);
const timerId = ref(null); // 订单轮询定时器
const paymentStatus = ref(''); //支付方式 WX_NATIVE 微信 ALI_PC 支付宝
const qrcodeCanvas = ref(); // 微信二维码
const paymentURL = ref(''); //支付二维码
const orderId = ref(''); // 订单ID
const checkbox = ref(false); // 同意协议
// 创建订单后支付状态
const createSuccessType = ref(true);
// const couponDetail = ref({
// 	couponsType: '1',
// 	name: '146元打车券礼包',
// 	desc: '包含1张￥11券、1张￥12券、1张￥13券、2张￥10券、2张￥10券',
// 	useLimit: '无门槛',
// }); //优惠卷详情

const purchaseObj = ref({});

const orderCouponsId = ref('');

const dialogVisible = ref(false);

// 新人券弹窗
const couponVisible = ref(false);

//权益卡
const equityCard = ref(false);
//扫码支付
const scanCodePayment = ref(false);

//购买数量
const amount = ref(1);

//公用提示弹框
const successToast = ref(null);
//卡卷确定使用弹框
const cardPopUps = ref(null);
//购买成功弹出
const buySuccessRef = ref(null);

// 父组件传递过来的方法 显示
const show = () => {
	dialogVisible.value = true;
	handleUserExtension();
	handleSgtCoupon();
};

function handleReturn() {
	// 关闭对话框
	showShangqiuTong.value = false;
}

const downloadWindows = () => {
	const link = document.createElement('a');
	link.href = 'https://static.biaobiaozhun.com/sgt-software/%E5%95%86%E4%BC%B0%E9%80%9A-win.zip'; // replace with your app's URL
	link.download = '商估通-win';
	link.click();
};

const downloadMac = () => {
	const a = document.createElement('a');
	a.href = 'https://static.biaobiaozhun.com/sgt-software/%E5%95%86%E4%BC%B0%E9%80%9A-mac.zip';
	a.download = '商估通-mac';
	a.click();
};

// 商估通领取新人券
function handleSendSgtCoupon() {
	sendSgtCoupon().then((res) => {
		if (res.code == 200) {
			// 提示领取成功
			ElMessage({
				message: `领取成功`,
				type: 'success',
			});
			couponVisible.value = true;
		}
	});
}

//获取用户扩展信息
function handleUserExtension() {
	getUserExtension().then((res) => {
		if (res.code == 200) {
			if (!res.data.sendSgtNewUserCoupon) {
				couponVisible.value = false;
				// 两秒后自动领取新人劵
				// 提示领取中
				ElMessage({
					message: `领取中...`,
					type: 'warning',
				});
				setTimeout(() => {
					handleSendSgtCoupon();
				}, 2000);
			} else {
				couponVisible.value = res.data.sendSgtNewUserCoupon;
			}
		}
	});
}

// 商估通权益卡券
function handleSgtCoupon() {
	getSgtCoupon().then((res) => {
		if (res.code === 200) {
			purchaseObj.value = res.data?.[0];
			handlePriceChangeAll();
		}
	});
}

// 支付明细
function handlePriceChangeAll() {
	getDiscount({ totalAmount: purchaseObj.value.price * amount.value }).then((res) => {
		if (res.code === 200) {
			purchaseObj.value['payableAmount'] = Number(res.data.payableAmount.toFixed(2)); // 应付金额
			purchaseObj.value['discountAmount'] = Number(res.data.discountAmount.toFixed(2)); // 折扣金额
			purchaseObj.value['totalAmount'] = Number(res.data.totalAmount.toFixed(2)); // 总金额
		}
	});
}

function handlePayment() {
	//清楚定时器
	clearInterval(timerId.value);
	let arr = [
		{
			orderCount: '1', // 套餐数量
			businessType: 'SGT_COUPON_ORDER', // 订单类型
			couponId: purchaseObj.value.id, // 套餐id
			quantity: amount.value, // 年月数量
			team: '1', // 人数
			totalAmount: purchaseObj.value.totalAmount, // 总金额
		},
	];

	let params = {
		payType: paymentStatus.value, // 支付方式
		orderCount: '1', // 套餐数量
		totalAmount: purchaseObj.value.totalAmount, // 总金额
		discountAmount: purchaseObj.value.discountAmount, // 折扣
		payableAmount: purchaseObj.value.payableAmount, // 应付金额
		orderDetails: arr, // 套餐
	};
	handleOrderCreate(params); // 订单创建
}

//订单创建
function handleOrderCreate(params) {
	orderOreate(params).then((res) => {
		if (res.code == 200) {
			createSuccessType.value = false;
			let data = res.data;
			if (paymentStatus.value === 'ALI_PC') {
				// 支付宝
				paymentURL.value = data.url;
			} else {
				// 微信
				const qrCodeDiv = qrcodeCanvas.value;
				QRCode.toCanvas(qrCodeDiv, data.url, (error) => {
					if (error) console.error(error);
				});
			}

			if (data.outTradeNo) {
				orderId.value = data.outTradeNo;
				// 开始轮询
				timerId.value = setInterval(() => {
					pollOrderStatus(orderId.value);
				}, 1500);
			}
		}
	});
}

// 订单轮询获取支付状态
const pollOrderStatus = async (orderId) => {
	if (paymentStatus.value === '' || !checkbox.value) {
		return;
	}
	try {
		const response = await orderStatus(orderId);
		if (orderId === '') {
			return; // 提前返回，不执行后续代码
		}
		//PENDING("待支付"),
		// PAID("已支付"),
		// FAILED("支付失败"),
		// CANCELLED("已取消"),
		// REFUNDED("已退款");
		if (response.code == 200) {
			// 根据返回的状态更新状态提示信息
			switch (response.data) {
				case 'PAID':
					ElMessage({
						message: `支付成功`,
						type: 'success',
					});
					createSuccessType.value = true;
					clearInterval(timerId.value);
					//根据订单id获取优惠卷详情
					handleCouponDetail(orderId);
					//  获取订单所关联的券id
					handleOrderCoupon(orderId);
					break;
				case 'FAILED':
					ElMessage({
						message: `支付失败`,
						type: 'error',
					});
					clearInterval(timerId.value);
					break;
				case 'CANCELLED':
					ElMessage({
						message: `订单已取消`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				case 'REFUNDED':
					ElMessage({
						message: `已退款`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				default:
					break;
			}
		} else {
			clearInterval(timerId.value);
		}
	} catch (error) {
		clearInterval(timerId.value);
	}
};

//根据订单id获取优惠卷详情
const handleCouponDetail = (orderId) => {
	getCouponDetail({ outTradeNo: orderId }).then((res) => {
		if (res.code === 200 && res.data) {
			let couponDetail = res.data;
			couponDetail['couponsType'] = '1'; //优惠卷标识
			buySuccessRef.value.show({ payType: paymentStatus.value, ...purchaseObj.value, couponDetail: couponDetail });
		} else {
			buySuccessRef.value.show({ payType: paymentStatus.value, ...purchaseObj.value });
		}
	});
};

//立即使用
function handleUseNow() {
	cardPopUps.value.show();
}
//确认
const handleConfirm = () => {
	// 处理确认逻辑
	cardPopUps.value.hide(); //确认使用弹窗
	handleActiveCoupon();
};
//激活
function handleActiveCoupon() {
	activeCoupon({ id: orderCouponsId.value }).then((res) => {
		if (res.code == 200) {
			buySuccessRef.value.hide(); //购买成功弹窗
			successToast.value.show(); //成功提示
			dialogVisible.value = false;
		}
	});
}
//获取订单所关联的券id
function handleOrderCoupon(params) {
	getOrderCoupon({ orderId: params }).then((res) => {
		if (res.code == 200) {
			orderCouponsId.value = res.data[0];
		}
	});
}

const addToCart = () => {
	// 处理加入购物车逻辑
	let param = {
		orderCount: '1', // 订单数量
		businessType: 'SGT_COUPON_ORDER', // 订单类型
		couponId: purchaseObj.value.id, // 套餐id
		quantity: amount.value, // 套餐数量
		team: '1',
		totalAmount: purchaseObj.value.totalAmount,
	};
	addShoppingCart({ ...param }).then((res) => {
		if (res.code === 200) {
			vuexStore.dispatch('handleGetShoppingCart'); // 获取购物车
			ElMessage({
				message: `加入购物车成功`,
				type: 'success',
			});
		}
	});
};

const purchase = () => {
	// 处理立即购买逻辑
	scanCodePayment.value = true;
};
const handleBack = () => {
	scanCodePayment.value = false;
	checkbox.value = false;
	// 处理返回逻辑
	// 支付状态清楚
	paymentStatus.value = '';
	//清楚定时器
	clearInterval(timerId.value);
};

const handleAgree = () => {
	// 处理同意协议逻辑
	checkbox.value = true;
};

const handleClose = () => {
	if (!createSuccessType.value) {
		buySuccessRef.value.show({
			paymentState: true,
			name: '商估通权益卡-年卡',
			payType: paymentStatus.value,
			payableAmount: purchaseObj.value.totalAmount,
		});
		createSuccessType.value = true;
	}
	dialogVisible.value = false;
	scanCodePayment.value = false;
	checkbox.value = false;
	// 清空
	amount.value = 1;
	// 清空
	purchaseObj.value = {};
	// 支付状态清楚
	paymentStatus.value = '';
	//清楚定时器
	clearInterval(timerId.value);
};

//免费领取
const handleFreeTrial = () => {
	// 处理免费领取逻辑
	equityCard.value = true;
};

//立即使用
function handleUse() {
	cardPopUps.value.show();
}

// 暴露方法给父组件
defineExpose({ show });
</script>
<style lang="scss">
.purchase_dialog {
	.el-dialog__header {
		padding: 8px 24px 16px 24px;
		margin: 0;

		.el-dialog__title {
			font-size: 20px;
			font-weight: 500;
			color: #1d2129;
		}
		.el-dialog__headerbtn {
			height: 76px;
			right: 20px;
		}
	}

	.el-dialog__body {
		padding: 8px;
	}
}
</style>
<style scoped lang="scss">
.content_box_bottom {
	height: 22px;
	.el-radio-group {
		height: 22px;
		flex-wrap: nowrap;
		& > :nth-child(n) {
			height: 32px;
			margin-right: 10px;
		}

		.el-radio:last-child {
			margin-right: 0px;
		}
	}
}

.success-notice {
	background: #0a42f10f;
	border-radius: 24px;
	padding: 22px 0;
	margin-bottom: 16px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	.notice-title {
		font-size: 20px;
		font-weight: 500;
		line-height: 28px;
		color: #1d2129;
	}
	.notice-detail {
		font-size: 14px;
		font-weight: 400;
		line-height: 19.6px;
		color: #23366e;
		margin-top: 9px;
	}
	.notice_soon {
		cursor: pointer;
		font-size: 14px;
		font-weight: 700;
		color: #0a42f1;
	}
}

.vip-card {
	background: linear-gradient(111.61deg, #307eff 35.81%, #083ef0 96.36%);
	border-radius: 24px;
	padding: 24px 40px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;

	.card-info {
		.card-title {
			font-size: 18px;
			color: #fff;
			margin-bottom: 4px;
		}

		.card-subtitle {
			font-size: 14px;
			color: rgba(255, 255, 255, 0.8);
		}
	}

	.free-btn {
		height: 52px;
		font-size: 20px;
		padding: 0 41px;
		border-color: #fff;
		border-radius: 40px;
		color: #fff;
		background: linear-gradient(111.61deg, #307eff 35.81%, #083ef0 96.36%);
	}
}

.purchase-info {
	background: #fff;
	border-radius: 16px;
	padding: 20px 32px 20px 32px;
	.purchaseFlex {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 23px;
	}
	.price {
		text-align: right;
		height: 70px;

		.symbol {
			font-size: 20px;
			font-weight: 510;
			line-height: 23.87px;
			color: #ff2324;
		}

		.number {
			font-size: 40px;
			font-weight: 700;
			line-height: 47.73px;
			color: #ff2324;
		}

		.discount {
			font-size: 16px;
			font-weight: 400;
			line-height: 22.4px;
			color: #7281ab;
		}
	}
	.purchase-title {
		// margin-bottom: 20px;
		font-size: 18px;
		font-weight: 500;
		line-height: 25.2px;
		color: #1d2129;

		.product-name {
			font-size: 20px;
			font-weight: 500;
			line-height: 28px;
			color: #c59266;
		}
	}

	.price-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 40px;
		.amount-control {
			display: flex;
			align-items: center;
			gap: 17px;

			.label {
				font-size: 16px;
				font-weight: 400;
				line-height: 22.4px;
				color: #23366e;
			}
			.el-input-number {
				width: 102px;
			}
		}
	}

	.duration {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		.tipd {
			font-size: 14px;
			font-weight: 400;
			line-height: 19.6px;
			color: #23366e;
		}

		.tip {
			font-size: 12px;
			font-weight: 400;
			line-height: 16.8px;
			color: #7281ab;
		}
	}

	.action-buttons {
		display: flex;
		gap: 40px;

		.el-button {
			flex: 1;
			height: 40px;
			width: 236px;
			height: 52px;
			border-radius: 4px;
			font-size: 20px;
			font-weight: 400;
			line-height: 28px;
			text-align: center;
		}

		.cart-btn {
			border-color: #b88d67;
			color: #b88d67;
		}

		.buy-btn {
			background: #b88d67;
			border-color: #b88d67;
			color: #fff;

			&:hover {
				background: #c69c7b;
				border-color: #c69c7b;
			}
		}
	}
}

.scan-payment {
	background: #fff;
	border-radius: 16px;
	padding: 27.3px 32px 29px 32px;

	.header {
		display: flex;
		align-items: center;
		margin-bottom: 20px;

		.back-icon {
			font-size: 16px;
			margin-right: 4px;
			margin-top: -3px;
			cursor: pointer;
			color: #333;
		}
		span {
			font-size: 18px;
			font-weight: 500;
			line-height: 25.2px;
			color: #1d2129;
		}
	}

	.payment-card {
		background: #fff;
		border-radius: 8px;
		display: flex;
		.payment-info-section {
			width: calc(100% - 208px);
			padding: 16px 24px;
		}
		.agreement_section {
			width: 160px;
			height: 160px;
		}
		.agreement-section {
			width: 160px;
			height: 160px;
			background-image: url(../../assets/sectionImg.png);
			background-repeat: no-repeat;
			background-size: 100% 100%;
			background-size: cover;
			.agreement-box {
				padding: 32px 0px;
				display: flex;
				flex-direction: column;
				align-items: center;
				.agreement-title {
					font-size: 13px;
					font-weight: 400;
					line-height: 18.2px;
					color: #23366e;
				}

				.agreement-link {
					font-size: 13px;
					font-weight: 400;
					line-height: 18.2px;
					color: #0a42f1;
					cursor: pointer;
					margin: 3px 0 17px 0;
				}

				.agree-btn {
					background-color: #0a42f1;
					height: 36px;
					width: 60px;
					font-size: 14px;
					font-weight: 400;
					line-height: 19.6px;
					border: 1px solid #0a42f1;
				}
			}
		}

		.payment-info {
			.payment-label {
				font-size: 14px;
				color: #333;
			}

			.payment-amount {
				height: 48px;
				display: flex;
				align-items: baseline;

				.currency {
					font-size: 20px;
					font-weight: 510;
					line-height: 23.87px;
					color: #ff2324;
					font-size: 16px;
					margin-right: 5px;
				}

				.amount {
					font-size: 40px;
					font-weight: 700;
					line-height: 47.73px;
					color: #ff2324;
				}

				.discount {
					margin-left: 8px;
					font-size: 16px;
					font-weight: 400;
					line-height: 22.4px;
					color: #7281ab;
				}
			}
		}

		.payment-method {
			display: flex;
			align-items: center;
			gap: 5px;
			margin-top: 36px;

			.payment-icon {
				width: 16px;
				height: 16px;
			}

			.payment-text {
				margin-left: 3px;
				color: #666;
				font-size: 14px;
			}
		}
	}
}

.qrcode {
	height: 160px !important;
	margin: -8px 0px 0 -8px;
	width: 160px !important;
}

.purchase_dialog_content {
	margin-bottom: 16px;
	width: 592px;
	height: 100px;
	background: linear-gradient(111.61deg, #30a8ff 35.81%, #083ef0 96.36%);
	border-radius: 16px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32px;
	box-sizing: border-box;
	.purchase_dialog_content_left {
		.purchase_dialog_content_left_title {
			height: 28px;
			font-weight: 500;
			font-size: 20px;
			color: #fff;
			margin-bottom: 4px;
		}
		.purchase_dialog_content_left_desc {
			height: 22px;
			font-weight: 400;
			font-size: 16px;
			color: #fff;
		}
	}

	.purchase_dialog_content_lefts {
		.purchase_dialog_content_left_title {
			height: 28px;
			font-weight: 500;
			font-size: 20px;
			color: #1868f1;
			margin-bottom: 4px;
		}
		.purchase_dialog_content_left_desc {
			height: 22px;
			font-weight: 400;
			font-size: 16px;
			color: #1868f1;
		}
	}

	.purchase_dialog_content_right {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 12px;

		.platform-btn {
			box-shadow: 0px 4px 20px 0px #253c7726;
			display: flex;
			height: 48px;
			align-items: center;
			padding: 0px 22.5px;
			box-sizing: border-box;
			background: #fff;
			border-radius: 8px;
			cursor: pointer;
			transition: all 0.3s;

			img {
				width: 16px;
				height: 16px;
				margin: 2px 8px 0 0;
			}

			&:hover {
				background: #f5f7fa;
			}
		}
	}
}
</style>
