// 交易材料相关接口
import { fetch } from "./http";

export function getMapList(params) { //查询列表
	return fetch({
		url: "/api/amber/getMap",
		method: "get",
		params: params,
		loading: true,
	});
}

export function calculate(params) { //交易计算
	return fetch({
		url: "/api/amber/trade-calculate",
		method: "get",
		params: params,
		loading: true,
	});
}

export function getBusinessList(params) { //交易计算-获取商圈
	return fetch({
		url: "/api/amber/get-business-list",
		method: "get",
		params: params,
		loading: true,
	});
}

export function getBuildList(params) { //交易计算-获取建筑物
	return fetch({
		url: "/api/amber/get-map-list-by-business",
		method: "get",
		params: params,
		loading: true,
	});
}

export function getPrice(params) { //交易计算-获取成交价格
	return fetch({
		url: "/api/amber/get-price-list-by-business-map",
		method: "get",
		params: params,
		loading: true,
	});
}

export function valueList(params) { //价值对比
	return fetch({
		url: "/api/amber/getSelPkAssetsAndAngleArena",
		method: "get",
		params: params,
		loading: true,
	});
}

export function getSixRingList(params) { //六角擂台
    return fetch({
        url: "/api/amber/getSixAngleArena",
        method: "get",
        params: params,
        loading: true,
    })
}

export function getPopulationList(ids) { //人口对比
    return fetch({
        url: "/api/amber/populationContrast",
        method: "get",
        params: ids,
        loading: true,
    })
}

export function getLesseeList(ids) { // 租户对比
    return fetch({
        url: "/api/amber/tenantContrast",
        method: "get",
        params: ids,
        loading: true,
    })
}

export function getFloorList(params) { // 户型图
    return fetch({
        url: "/sm/v1/houseplanpic/list",
        method: "get",
        params: params,
        loading: true,
    })
}