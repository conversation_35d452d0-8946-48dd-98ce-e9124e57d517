<template>
	<div class="mian">
		<div class="header">
			<el-row :gutter="20" class="main">
				<el-col :span="1"></el-col>
				<el-col :span="16"
					><div class="grid-content" />
					<el-button
						v-for="item in buildingTypes"
						:class="{ 'selected-button': buildingTypesValue.includes(item) }"
						:key="item"
						@click="search(item)"
						>{{ item }}</el-button
					>
				</el-col>

				<el-col style="margin-left: 57px" :span="6">
					<div class="grid-content" />
					<el-button v-for="item in rate" :class="{ 'selected-button': rateValue.includes(item) }" :key="item" @click="handle(item)">{{
						item
					}}</el-button>
				</el-col>
			</el-row>
			<el-row :gutter="20" class="main">
				<el-col :span="1"></el-col>
				<el-col :span="3">
					<el-cascader
						:disabled="disabled"
						placeholder="请选择城市"
						@change="handleChange"
						:options="pcaOptions"
						:props="{ value: 'label' }"
						:key="refurbish"
					>
					</el-cascader>
				</el-col>
				<el-col :span="13">
					<el-input v-model="essential" placeholder="请输入关键字"></el-input>
				</el-col>
				<el-col :span="4" style="margin-left: 8px">
					<el-select
						@change="() => (disabled = true)"
						v-model="distance"
						class="m-2"
						:disabled="disabledScope"
						placeholder="请选择范围（m）"
						style="width: 100%"
					>
						<el-option v-for="item in range" :key="item" :label="item" :value="item" />
					</el-select>
				</el-col>
				<el-col :span="2">
					<el-button type="primary" @click="initData">查询</el-button>
					<el-button type="primary" @click="reset">重置</el-button>
				</el-col>
			</el-row>
		</div>
		<el-text class="mx-1" style="padding-left: 80px">共有{{ total }}个资产</el-text>
		<div class="content">
			<el-card shadow="hover" class="crad" v-for="item in data" @click="router.push('/main/property/' + item.id)">
				<el-row :gutter="20">
					<el-col :span="12"><el-image style="width: 80%; height: 130px; object-fit: cover" :src="`${proxyAddress}${item.file_url}`" /> </el-col>
					<el-col :span="12" class="item-container">
						<el-text class="mx-1" style="font-size: 18px" tag="h4">{{ item.name }}</el-text>
						<el-text class="mx-1" style="font-size: 14px" line-clamp="2" tag="p">{{ item.address }}</el-text>
						<el-button size="" icon="plus" style="margin-left: 120px" @click.stop=""></el-button>
					</el-col>
				</el-row>

				<div style="display: flex; align-items: center; justify-content: space-between; height: 50px; border-top: 1px solid #f1f1f1">
					<el-popover placement="right" :width="300" trigger="click">
						<template #reference>
							<el-button @click.stop="ganinData(item.id)"
								><span style="margin-right: 6px">{{ item.remark_total }}</span
								><el-icon><ChatRound /></el-icon
							></el-button>
						</template>
						<el-scrollbar v-if="item.remark_total > 0" height="200px">
							<el-card style="margin-top: 8px" v-for="comment in commentList" :key="comment.addedtime">
								<div slot="header" class="clearfix">
									<span>{{ comment.username }} 评论于 {{ comment.addedtime }}</span>
								</div>
								<p>{{ comment.remark }}</p>
							</el-card>
						</el-scrollbar>
						<!-- 如果没有评论，显示相应的消息 -->
						<div v-else>
							<div style="text-align: center; padding: 10px">暂无评论</div>
						</div>
					</el-popover>
					<div class="bottom">
						<el-text class="mx-1" style="font-size: 14px; padding-right: 20px" line-clamp="2" tag="span">{{ item.de_type }}</el-text>
						<el-text class="mx-1" style="font-size: 14px" line-clamp="2" tag="span">{{ item.degree }}</el-text>
					</div>
				</div>
			</el-card>
		</div>
		<el-pagination
			layout="prev, pager, next,jumper"
			:total="total"
			background
			v-model:current-page="currentPage"
			@current-change="initData()"
			v-model:page-size="size"
			style="justify-content: center"
		/>
	</div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref } from 'vue';
import { useRouter } from 'vue-router';
import http from '@/utils/http';
const commentList = ref([]);
const disabled = ref(false);
const disabledScope = ref(false);
const refurbish = ref(0);
const pcaOptions = [
	{
		value: 'province1',
		label: '青岛市',
		children: [
			{
				value: 'city1-1',
				label: '市南区',
			},
			{
				value: 'city1-2',
				label: '市北区',
			},
			{
				value: 'city1-1',
				label: '黄岛区',
			},
			{
				value: 'city1-2',
				label: '崂山区',
			},
			{
				value: 'city1-1',
				label: '李沧区',
			},
			{
				value: 'city1-2',
				label: '城阳区',
			},
			{
				value: 'city1-1',
				label: '即墨区',
			},
			{
				value: 'city1-2',
				label: '胶州市',
			},
			{
				value: 'city1-1',
				label: '平度市',
			},
			{
				value: 'city1-2',
				label: '莱西市',
			},
		],
	},
];

const ganinData = async (id) => {
	const res = await http.get('/api/a_maps.do?act=getUserRemark', {
		params: {
			map_id: id,
		},
	});
	if (res && res.list) {
		commentList.value = res.list;
	}
};
const reset = () => {
	disabled.value = false;
	disabledScope.value = false;
	refurbish.value++;
	currentPage.value = 1;
	province.value = '';
	city.value = '';
	county.value = '';
	buildingTypesValue.value = '';
	essential.value = '';
	rateValue.value = '';
	distance.value = '';
	initData();
};
const router = useRouter();
const total = ref();
const data = ref([]);
const range = ['300', '1000', '3000'];
const distance = ref();
const currentPage = ref(1);
const size = ref(12);
const buildingTypesValue = ref('');
const initData = async () => {
	const res = await http.get('/api/api.do?act=ratingPassList', {
		params: {
			currentPage: currentPage.value,
			province: province.value,
			city: city.value,
			de_type: buildingTypesValue.value,
			keywords: essential.value,
			degree: rateValue.value,
			county: county.value,
			distance: distance.value,
			loasts: loats.value,
		},
	});
	data.value = res.list;
	total.value = res.total;
};
const savedSearchConditions = JSON.parse(localStorage.getItem('searchConditions')) || {
	currentPage: '',
	province: '',
	city: '',
	buildingTypesValue: '',
	essential: '',
	rateValue: '',
	county: '',
};

onBeforeUnmount(() => {
	const currentSearchConditions = {
		currentPage: currentPage.value,
		province: province.value,
		city: city.value,
		buildingTypesValue: buildingTypesValue.value,
		essential: essential.value,
		rateValue: rateValue.value,
		county: county.value,
	};
	localStorage.setItem('searchConditions', JSON.stringify(currentSearchConditions));
});
const loats = ref('');
onMounted(async () => {
	currentPage.value = savedSearchConditions.currentPage;
	province.value = savedSearchConditions.province;
	city.value = savedSearchConditions.city;
	buildingTypesValue.value = savedSearchConditions.buildingTypesValue;
	essential.value = savedSearchConditions.essential;
	rateValue.value = savedSearchConditions.rateValue;
	county.value = savedSearchConditions.county;
	if (navigator.geolocation) {
		navigator.geolocation.getCurrentPosition(
			(position) => {
				const { latitude, longitude } = position.coords;
				loats.value = longitude + ',' + latitude;
			},
			(error) => {
				console.error('获取地理位置时发生错误:', error.message);
			}
		);
	} else {
		console.error('此浏览器不支持地理位置信息。');
	}
	initData();
});
const proxyAddress = process.env.NODE_ENV === 'development' ? 'http://**************:8081/' : 'http://**************:8081/';

const province = ref('');
const city = ref('');
const essential = ref('');
const rateValue = ref('');
const county = ref('');
const handleChange = (val) => {
	disabledScope.value = true;
	// province.value = val[0];
	city.value = val[0];
	county.value = val[1];
};
const search = (item) => {
	try {
		// 清空输入值，如果选择了 '全部'
		if (item === '不限') {
			buildingTypesValue.value = '';
		} else {
			if (buildingTypesValue.value.includes(item)) return;
			buildingTypesValue.value += item + ',';
		}
		initData();
	} catch (error) {
		console.error('发生错误：', error);
	}
};
const handle = (item) => {
	try {
		// 清空输入值，如果选择了 '全部'
		if (item === '全部') {
			rateValue.value = ''; // 或者 rateValue.value = null;
		} else {
			// 如果传入了有效的 item，设置输入值为该值
			if (rateValue.value.includes(item)) return;
			rateValue.value += item + ',';
		}

		// 初始化数据（假设是一个名为 initData 的函数）
		initData();
	} catch (error) {
		console.error('发生错误：', error);
	}
};
const rate = ['A+', 'A ', 'B+', 'B ', 'B-', 'C', '全部'];
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '保障房', '综合市场', '不限'];
</script>

<style lang="less" scoped>
.el-card :deep(.el-card__body) {
	padding: 20px 20px 0 20px;
}
.selected-button {
	background-color: #409eff;
	color: #fff;
}
.header {
	margin-bottom: 15px;
}
.main {
	margin-bottom: 20px;
}
.content {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	.crad {
		margin: 10px;
		border-radius: 15px;
		width: 22%;
	}
}
.bottom {
	display: flex;
	margin: 30px 0;
	justify-content: end;
}

.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 10px;
}
.item-container {
	// display: flex;
	// flex-direction: column;
	// text-align: center;
}

.item-container > * {
	margin-bottom: 10px;
}
</style>
