<template>
	<div class="mian">
		<div class="top">
			<img src="../../assets/images/population/home.png" alt="" />
			<div class="right">
				<p class="title">执行摘要</p>
				<p class="center">
					<span>{{ basic.buildingUniqueCode_dictText }},{{ basic.buildingDistrict }}, {{ basic.buildingCity }} </span>
					<span>术木智能</span>
				</p>
				<p class="bottom">时间：5,10,15分钟 半径</p>
			</div>
		</div>
		<div class="container">
			<p class="titmin">人数统计</p>
			<table>
				<thead>
					<tr>
						<th>人员结构</th>
						<th v-for="stat in population.slice(1)" :key="stat.statisticalTime">{{ stat.statisticalTime }} 分钟</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>1960 后</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.after1960 }}</td>
					</tr>
					<tr>
						<td>1970 后</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.after1970 }}</td>
					</tr>
					<tr>
						<td>1980 后</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.after1980 }}</td>
					</tr>
					<tr>
						<td>1990 后</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.after1990 }}</td>
					</tr>
					<tr>
						<td>2000 后</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.after2000 }}</td>
					</tr>
					<tr>
						<td>2010 后</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.after2010 }}</td>
					</tr>

					<tr>
						<td>个人占比</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.individualProportion }}</td>
					</tr>
					<tr>
						<td>情侣占比</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.coupleProportion }}</td>
					</tr>
					<tr>
						<td>家庭占比</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.familyProportion }}</td>
					</tr>
					<tr>
						<td>多人占比</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">
							{{ (parseFloat(item.individualProportion) + parseFloat(item.coupleProportion) + parseFloat(item.familyProportion)).toFixed(2) }}
						</td>
					</tr>

					<tr>
						<td>男性占比</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.maleProportion }}</td>
					</tr>
					<tr>
						<td>女性占比</td>
						<td v-for="(item, index) in population.slice(1)" :key="index">{{ item.femaleProportion }}</td>
					</tr>
				</tbody>
			</table>
			<!-- <p class="titmin">不同时段人数</p> -->
			<table style="margin-top: 30px">
				<thead>
					<tr>
						<th>不同时段人数</th>
						<th v-for="stat in timenot.slice(1)" :key="stat.statisticalTime">{{ stat.statisticalTime }} 分钟</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>工作日上午</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.weekdayMorningProportion }}</td>
					</tr>
					<tr>
						<td>工作日中午</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.weekdayNoonProportion }}</td>
					</tr>
					<tr>
						<td>工作日下午</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.weekdayAfternoonProportion }}</td>
					</tr>
					<tr>
						<td>工作日晚上</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.weekdayNightProportion }}</td>
					</tr>
					<tr>
						<td>节假日上午</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.holidaysMorningProportion }}</td>
					</tr>
					<tr>
						<td>节假日中午</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.holidaysNoonProportion }}</td>
					</tr>
					<tr>
						<td>节假日下午</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.holidaysAfternoonProportion }}</td>
					</tr>
					<tr>
						<td>节假日晚上</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.holidaysNightProportion }}</td>
					</tr>
					<tr>
						<td>一共</td>
						<td v-for="(item, index) in timenot.slice(1)" :key="index">
							{{
								(
									parseFloat(item.weekdayMorningProportion) +
									parseFloat(item.weekdayNoonProportion) +
									parseFloat(item.weekdayAfternoonProportion) +
									parseFloat(item.weekdayNightProportion) +
									parseFloat(item.holidaysMorningProportion) +
									parseFloat(item.holidaysNoonProportion) +
									parseFloat(item.holidaysAfternoonProportion) +
									parseFloat(item.holidaysNightProportion)
								).toFixed(2)
							}}
						</td>
					</tr>
				</tbody>
			</table>
			<!-- 外卖 -->
			<table style="margin-top: 30px">
				<thead>
					<tr>
						<th>外卖配送</th>
						<th v-for="stat in waimai.slice(1)" :key="stat.statisticalTime">{{ stat.statisticalTime }} 分钟</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>工作日进出人次</td>
						<td v-for="(item, index) in waimai.slice(1)" :key="index">{{ item.weekdayTakeoutNum }}</td>
					</tr>
					<tr>
						<td>节假日进出人次</td>
						<td v-for="(item, index) in waimai.slice(1)" :key="index">{{ item.holidaysTakeoutNum }}</td>
					</tr>
				</tbody>
			</table>
			<!-- 停车场 -->
			<table style="margin-top: 30px">
				<thead>
					<tr>
						<th>停车场进出车次</th>
						<th v-for="stat in shop.slice(1)" :key="stat.statisticalTime">{{ stat.statisticalTime }} 分钟</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td>工作日车次</td>
						<td v-for="(item, index) in shop.slice(1)" :key="index">{{ item.weekdayTraffic }}</td>
					</tr>
					<tr>
						<td>节假日车次</td>
						<td v-for="(item, index) in shop.slice(1)" :key="index">{{ item.holidaysTraffic }}</td>
					</tr>
					<!-- <tr>
						<td>车型均价</td>
						<td v-for="(item, index) in shop.slice(1)" :key="index">{{ item.holidaysTakeoutNum }}</td>
					</tr> -->
				</tbody>
			</table>
		</div>
		<div class="footer">
			<p style="float: right">报告时间：{{ basic.recordTime }}</p>
		</div>
	</div>
</template>

<script setup>
import { perponOwn } from '../../api/baogao';
import { useRoute } from 'vue-router';
const router = useRoute();
import { ref, onMounted, reactive, nextTick } from 'vue';
const basic = ref({});
const timenot = ref([]);
const waimai = ref([]);
const population = ref([]);
const shop = ref([]);
const getdata = async () => {
	const res = await perponOwn({ buildingId: router.query.id });
	if (res.code == 200) {
		basic.value = res.result;
		population.value = res.result.populationStatisticsList;
		timenot.value = res.result.differentTimeStatisticsList;
		waimai.value = res.result.takeawayDeliveryStatisticsList;
		shop.value = res.result.carInOutStatisticsList;
		population.value.unshift({
			after1960: '1960后',
			after1970: '1970后',
			after1980: '1980后',
			after1990: '1990后',
			after2000: '2000后',
			after2010: '2010后',
			coupleProportion: '情侣占比',
			familyProportion: '家庭占比',
			femaleProportion: '女占比',
			individualProportion: '个人占比',
			maleProportion: '男占比',
			statisticalTime: '时间',
		});
		timenot.value.unshift({
			statisticalTime: '不同时段人数',
		});
		waimai.value.unshift({
			statisticalTime: '外卖',
		});
		shop.value.unshift({
			statisticalTime: '停车场',
		});
		console.log(population.value, 888);
	}
};
onMounted(() => {
	getdata();
});
</script>
<style lang="less" scoped>
.mian {
	width: 1046px;
	// width: 1100px;
	height: 100%;
	background-color: #fff;
	padding: 20px;
	box-sizing: border-box;
	.top {
		display: flex;
		align-items: center;
		width: 100%;
		border-bottom: 3px solid #000;
		img {
			width: 80px;
			height: 80px;
			margin-right: 60px;
		}
		.right {
			width: 100%;
			.center {
				display: flex;
				justify-content: space-between;
			}
		}
		.title {
			background-color: #f49445;
			font-size: 20px;
			color: #fff;
		}
	}
	.container {
		margin-top: 30px;
		.titmin {
			font-size: 20px;
			font-weight: 700;
		}
		table {
			width: 100%;
			border-collapse: collapse; /* 合并边框 */
			th,
			td {
				border: 1px solid #ccc;
				padding: 8px;
				text-align: center;
			}
			th {
				background-color: #f5f5f5; /* 表头背景色 */
			}
			tbody tr:nth-child(odd) {
				background-color: #fff; /* 奇数行背景色 */
			}
			tbody tr:nth-child(even) {
				background-color: #f9f9f9; /* 偶数行背景色 */
			}
		}
	}
}
</style>
