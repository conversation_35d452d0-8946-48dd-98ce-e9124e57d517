<template>
	<div class="main" id="printMe">
		<div class="title toPdf">一、基本情况</div>
		<div class="content toPdf">建筑物介绍</div>
		<!-- <el-button class="outPdfBtn" type="primary" @click="putPdf">导出PDF</el-button> -->
		<!--		<el-button class="outPdfBtn" type="primary" v-print="printObj" >导出PDF</el-button>-->
		<div class="basic toPdf">
			{{ jieshao || '暂无数据' }}
		</div>
		<div class="content toPdf">位置和区域</div>
		<div class="basic toPdf">
			{{ basic.name }}位于{{ basic.address }}。
			<div>配套商圈为{{ basic.businessDistrictName }},周边的主要项目有：{{ basic.supportingProjects }}</div>
		</div>
		<div class="content toPdf">物业信息</div>
		<div class="wuye toPdf" style="margin: 20px 0; border: 1px solid #000; font-size: 14px">
			<div style="display: flex; align-items: center; border-bottom: 1px solid #000">
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">物业名称</div>
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">业态</div>
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">面积（m²）</div>
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">物管公司</div>
				<div style="flex: 1; text-align: center">产权人</div>
			</div>
			<div style="display: flex; align-items: center; height: 80px; border-bottom: 1px solid #000">
				<div style="height: 80px; text-align: center; flex: 1; line-height: 20px; align-content: center; border-right: 1px solid #000">
					{{ basic.name }}
				</div>
				<div style="height: 80px; text-align: center; flex: 1; border-right: 1px solid #000">
					<div style="line-height: 40px; border-bottom: 1px solid #000">{{ basic.deType || '暂无数据' }}</div>
					<div style="line-height: 40px">车位数量</div>
				</div>
				<div style="height: 80px; text-align: center; flex: 1; border-right: 1px solid #000">
					<div style="line-height: 40px; border-bottom: 1px solid #000">{{ wuye.coveredArea || '暂无数据' }}
					</div>
					<div style="line-height: 40px">{{ wuye.carportSum }}</div>
				</div>
				<div style="height: 80px; text-align: center; flex: 1; line-height: 20px; align-content: center; border-right: 1px solid #000">
					{{ wuye.propertyCompany || '暂无数据' }}</div>
				<div style="height: 80px; text-align: center; flex: 1; line-height: 20px;align-content: center;">
					{{ wuye.ownerCompany || '暂无数据' }}</div>
			</div>
			<div style="display: flex; align-items: center; border-bottom: 1px solid #000">
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">商圈</div>
				<div style="flex: 2; border-right: 1px solid #000; text-align: center">地址</div>
				<div style="flex: 2; text-align: center">交通线路</div>
			</div>
			<div style="display: flex; align-items: center; border-bottom: 1px solid #000">
				<div style="flex: 1; border-right: 1px solid #000;text-align: center">{{ basic.businessDistrictName ||
					'暂无数据' }}</div>
				<div style="flex: 2; border-right: 1px solid #000;text-align: center">{{ basic.address || '暂无数据' }}
				</div>
				<div style="flex: 2;text-align: center">
					<template v-if="jiaotong && jiaotong.trim()">
						{{ jiaotong }}
					</template>
					<template v-else> 暂无 </template>
				</div>
			</div>
			<div style="text-align: center; border-bottom: 1px solid #000">{{ basic.name }}</div>
			<div style="display: flex; align-items: center; border-bottom: 1px solid #000">
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">租金</div>
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">物业费</div>
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">车位费</div>
				<div style="flex: 1; border-right: 1px solid #000; text-align: center; min-width: 160px">总层数（标准层高）</div>
				<div style="flex: 1; border-right: 1px solid #000; text-align: center">得房率</div>
				<div style="flex: 1; text-align: center">资产评级</div>
			</div>
			<div style="display: flex; align-items: center">
				<div style="flex: 1; border-right: 1px solid #000;text-align: center">{{ wuye.rental ? (wuye.rental +
					'/天/平米') : '暂无数据' }}</div>
				<div style="flex: 1; border-right: 1px solid #000;text-align: center">{{ wuye.propertyFee ?
					(wuye.propertyFee + '元/月/平米') : '暂无数据' }}</div>
				<div style="flex: 1; border-right: 1px solid #000;text-align: center">{{ wuye.parkingFee || '暂无数据' }}
				</div>
				<div style="flex: 1; border-right: 1px solid #000;text-align: center">{{ wuye.totalFloors ?
					(wuye.totalFloors + '层') : '暂无数据' }}</div>
				<div style="flex: 1; border-right: 1px solid #000;text-align: center">{{ wuye.buyingRatio ?
					(wuye.buyingRatio + '%') : '暂无数据' }}</div>
				<div style="flex: 1; text-align: center">{{ basic.degree }}</div>
			</div>
		</div>

		<div class="title toPdf">
			二、写字楼租金和物业费：（商业环境和起租时间会影响租金，{{ wuye.rental ? ('按照租金' + wuye.rental + '元/天/平米，') : '' }}{{
				wuye.propertyFee ? ('物业费' + wuye.propertyFee + '元/月/平米计算，') : '' }}供参考）
		</div>
		<table class="toPdf" style="width: 100%; margin-top: 20px; color: black; border-collapse: collapse">
			<thead>
				<tr>
					<th style="width: 16%; border: 1px solid black">面积(m²)</th>
					<th style="width: 16%; border: 1px solid black">租金(元/天/m²)</th>
					<th style="width: 16%; border: 1px solid black">物业费(元/月/m²)</th>
					<th style="width: 16%; border: 1px solid black">总租金(元/月)</th>
					<th style="width: 16%; border: 1px solid black">总物业费(元/月)</th>
					<th style="width: 16%; border: 1px solid black">租金加物业费(元)</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(item, index) in rentData" :key="index">
					<td style="border: 1px solid black;text-align: center;">{{ item.area }}</td>
					<td style="border: 1px solid black;text-align: center;">{{ item.rent }}</td>
					<td style="border: 1px solid black;text-align: center;">{{ item.property_fee }}</td>
					<td style="border: 1px solid black;text-align: center;">{{ item.gross_rent }}</td>
					<td style="border: 1px solid black;text-align: center;">{{ item.total_property_charge }}</td>
					<td style="border: 1px solid black;text-align: center;">{{ item.rental_charge }}</td>
				</tr>
			</tbody>
		</table>
		<div class="title toPdf">周边配套（半径一公里）</div>
		<baogaomap class="toPdf" v-if="locadata.length > 0" style="margin-bottom: 20px" :locadata="locadata" :lat="lat">
		</baogaomap>
		<!-- 周边配套设置 -->
		<table class="toPdf" style="padding: 10px; border-collapse: collapse; width: 100%;">
			<tbody>
				<tr v-for="item in banjing" :key="item.id">
					<td style="width: 20%; border: 1px solid black">{{ item.type }}</td>
					<td style="border: 1px solid black">
						<span v-for="(poi, index) in item.pois" :key="index"> {{ poi.name }}
							<span v-if="index < item.pois.length - 1">, </span>
						</span>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { baogao, zuobiao } from '@/api/baogao';
import baogaomap from '../../Mapbaogao.vue';
// import {htmlToPDF} from '../../utils/toBePdf'
const route = useRoute();
const id = ref('');
const jieshao = ref('');
const basic = ref({});
const rentData = ref([]);
const banjing = ref([]);
const wuye = ref({});
const lat = ref('');
const jiaotong = ref('');
const locadata = ref([]);
const printObj = {
	url: window.location.origin + '/gaikuangPreview?id=' + route.query.id,
	preview: true,
	previewTitle: '导出预览',
};

const getzuobiao = async () => {
	const res = await zuobiao({ buildingId: id.value });
	if (res.code === 200) {
		banjing.value = res.result;
		res.result.forEach((item) => {
			if (item.type === '公交车站') {
				item.pois.forEach((poi) => {
					jiaotong.value += (jiaotong.value ? ', ' : '') + poi.name;
				});
			}
		});
	}
	console.log(jiaotong.value, '555');
};
const getdata = async () => {
	if (id.value) {
		try {
			const res = await baogao({ buildingId: id.value });
			if (res.code === 200) {
				jieshao.value = res.result.buildingIntroduce;
				basic.value = res.result;
				wuye.value = res.result.propertyInfoVo;
				res.result.mapsD3LocationShoppingMapVo.forEach((item) => {
					locadata.value.push([Number(item.lng), Number(item.lat)]);
				});
				lat.value = res.result.coordinate;
				console.log(lat.value, 9999);

				const areas = [80, 160, 260, 350, 480, 630, 820, 1000, 1220, 1500];
				const { rental, propertyFee } = wuye.value;

				rentData.value = areas.map((area) => {
					const gross_rent = Math.round(area * rental);
					const total_property_charge = Math.round(area * propertyFee);
					return {
						area,
						rent: rental,
						property_fee: propertyFee,
						gross_rent,
						total_property_charge,
						rental_charge: gross_rent + total_property_charge,
					};
				});

				console.log('Received data:', res);
			} else {
				console.error('Failed to fetch data: status', res.status);
			}
		} catch (error) {
			console.error('Error fetching data:', error);
		}
	}
};

const putPdf = () => {
	nextTick(() => {
		htmlToPDF('printMe', '个人报告');
	});
};

onMounted(() => {
	console.log('onMounted');
	id.value = route.query.id;
	const name = route.query.name;
	console.log('Received ID:', id.value);
	console.log('Received Name:', name);
	getzuobiao();
	getdata();
});
</script>

<style scoped lang="less">
.main {
	width: 100%;
	height: 100%;
	background-color: #fff;
	padding: 20px 30px;
	box-sizing: border-box;
	overflow: hidden;
	position: relative;

	.outPdfBtn {
		position: absolute;
		top: 20px;
		right: 30px;
	}

	.content {
		font-size: 18px;
		font-weight: bold;
		margin: 10px 0;
	}
}

.title {
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 20px;
}

.basic {
	font-size: 14px;
}
</style>
