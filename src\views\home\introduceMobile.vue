<template>
	<div style="background: #f4f7fc">
		<div class="container_header">
			<div class="header">
				<img src="@/assets/images/home/<USER>" alt="Logo" class="logo" />
				<div class="title">楼宇测评软件</div>
				<div class="desc">支持多业态资产的动态多参数运营分析，轻松管理资产组合</div>
			</div>
			<div class="footer">
				<div class="desc">请用电脑端浏览器搜索“标准”或打开下方链接体验完整功能</div>
				<div class="link-container">
					<div style="display: flex; align-items: center">
						<img src="../../assets/images/home/<USER>" style="margin-right: 4px" alt="" />
						<div @click="copyLink" class="link_container_text">https://bbzhun.com/#/introduce</div>
					</div>

					<div class="link_container_copy" @click="copyLink">复制</div>
				</div>
			</div>
		</div>
		<div class="content_container">
			<div class="content-title">
				<div>商估通可以帮您完成</div>
			</div>
			<el-collapse accordion :model-value="activeNames" @change="handleChange">
				<el-collapse-item v-for="(item, index) in propertyLIst" :key="index" :name="index">
					<template #title>
						<div class="content_title">
							<img :src="item.image" alt="content" class="content-image" />
							<div :class="{ active: activeNames[0] === index }">{{ item.title }}</div>
						</div>
					</template>
					<div class="content_content">
						<div class="content_content_box">
							<div class="content_content_title">{{ item.content }}</div>
							<img :src="item.img" alt="content" class="content-img" />
						</div>
					</div>
				</el-collapse-item>
			</el-collapse>
		</div>

		<div class="container_content">
			<div class="content_title">商估通对 Argus 的优势</div>
			<div class="content_box">
				假设您在管理一个大型商业地产项目，其中包含多个租户，每个租户的租赁协议都有不同的租金增长模式。
				例如，某些租户的租金可能在第五年以固定百分比增长，而其他租户可能基于CPI（消费者价格指数）的租金调整，或者根据租户的销售额比例进行浮动租金计算。
			</div>
			<div class="tab_box">
				<div
					class="tab"
					:class="activeIndex === index ? 'tabAct' : ''"
					v-for="(item, index) in customList"
					@click="handleTabClick(index)"
					:key="index"
				>
					{{ item.name }}
				</div>
			</div>
			<div class="excel-side" v-if="activeIndex === 0">
				<div class="content">
					<div :class="index == 1 ? 'firstItem' : ''" class="item" v-for="(item, index) in excelItems" :key="index">
						<div class="dot"></div>
						<div class="text">
							<div class="main_content">{{ item.title }}</div>
							<div class="sub">{{ item.desc }}</div>
						</div>
					</div>
				</div>
			</div>
			<div class="sgt-side" v-if="activeIndex === 1">
				<div class="side-image">
					<img src="@/assets/images/home/<USER>" alt="" />
				</div>
				<div class="content">
					<div class="item" v-for="(item, index) in sgtItems" :key="index">
						<div class="dot"></div>
						<div class="text">
							<div class="main_content">{{ item.title }}</div>
							<div class="sub">{{ item.desc }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="container_content_footer">
			<div class="title">让资产评估工作变得简单</div>
			<div class="card" v-for="(item, index) in cardList" :key="index">
				<img :src="item.img" class="card_img" alt="" />
				<div class="card_title">{{ item.title }}</div>
				<div class="card_desc">{{ item.description }}</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import property from '../../assets/images/home/<USER>';
import customize from '../../assets/images/home/<USER>';
import dynamics from '../../assets/images/home/<USER>';
import propertyImg from '../../assets/images/home/<USER>';
import customizeImg from '../../assets/images/home/<USER>';
import dynamicsImg from '../../assets/images/home/<USER>';

import operation1 from '../../assets/images/home/<USER>';
import operation2 from '../../assets/images/home/<USER>';
import operation3 from '../../assets/images/home/<USER>';

const propertyLIst = ref([
	{
		title: '资产管理组合',
		image: property,
		img: propertyImg,
		content: '支持创建不同的资产组合，帮助您根据不同需求分析复杂的资产运营数据',
	},
	{
		title: '自定义创建资产',
		image: customize,
		img: customizeImg,
		content: '支持为资产组合自定义添加楼宇资产，帮助您维护个人专属资产数据',
	},
	{
		title: '动态分析运营数据',
		image: dynamics,
		img: dynamicsImg,
		content: '支持动态多参数运营资产数据，帮助您快速评估资产数据，做出更佳决策',
	},
	{
		title: '生成报告',
    image: property,
    img: propertyImg,
		content: '实时生成现金流预测报告、融资策略报告',
	},
]);
const cardList = ref([
	{
		title: '专业数据动态分析',
		img: operation1,
		description: '基于平台强大的分析能力，帮助您高效管理丰富的资产数据和资产组合，让您能更快选、灵活地进行决策，做出更加合理的选择。',
	},
	{
		title: '功能强大逻辑连贯',
		img: operation2,
		description: '为您清晰划分逻辑处理模块及分析顺序，按序按需提供数据后，即可自动测算收益、估值等专业结论。',
	},
	{
		title: '交互友好操作清晰',
		img: operation3,
		description: '采用PC客户端的设计模式，配合客户明确用户操作引导，帮助您快速理解系统功能，清晰决策获取所需信息。',
	},
]);

const excelItems = [
	{
		title: '模拟所有租户的租金增长模式：',
		desc: '这些模式可能包括固定的百分比增长、基于市场租金的增长、或者基于某些经济指标如CPI的增长。',
	},
	{
		title: '自动化租金收入预测：',
		desc: '系统需要自动生成未来每一年的租金收入，并根据每个租户的不同租金条款进行调整。',
	},
	{
		title: '整合不同租赁条款并进行敏感性分析：',
		desc: '你希望能够轻松调整假设参数（如市场租金增长率或CPI增长率），并立即查看对整个地产项目现金流的影响。',
	},
];

const sgtItems = [
	{
		title: '复杂租赁条款的建模:',
		desc: '商估通有专门的功能来处理复杂的租赁条款和租金调整机制。它可以自动应用这些规则并生成精确的现金流预测，而这些在Excel中会需要大量的自定义公式和手动工作，且极易出错。',
	},
	{
		title: '自动化与效率：',
		desc: '商估通可以快速处理大量的租户数据和复杂的租赁条款，生成标准化的报告和预测。Excel虽然也可以实现这些，但会非常耗时，尤其当涉及到大量的租户和复杂的租金结构时，管理和维护这些模型将变得极其困难。',
	},
	{
		title: '敏感性分析：',
		desc: '商估通能够快速进行敏感性分析，允许你调整不同的假设条件（如CPI增长、市场租金变化）并即时查看对整体项目财务表现的影响。这在Excel中可能需要重复调整多个工作表和公式，效率低下且容易出错。',
	},
];

const customList = ref([
	{
		name: 'Argus',
	},
	{
		name: '商估通',
	},
]);

const activeIndex = ref(0);
const activeNames = ref([0]);

const copyLink = () => {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText('https://bbzhun.com/#/introduce')
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = 'https://bbzhun.com/#/introduce';
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
};
const handleChange = (val) => {
	console.log(val);
	activeNames.value = [val];
	console.log(activeNames.value);
};

const handleTabClick = (index) => {
	activeIndex.value = index;
};
</script>

<style scoped lang="scss">
.container_header {
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #e1eefd;
	.header {
		background-image: url('../../assets/images/home/<USER>');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		text-align: center;
		width: 100%;
		height: 208px;
		margin-top: 20px;
		.title {
			font-weight: 600;
			font-size: 26px;
			line-height: 39px;
			color: #1d2129;
			margin: 8px auto 4px auto;
		}
		.desc {
			font-size: 12px;
			font-weight: 400;
			line-height: 18px;
			color: #4e5969;
		}
	}

	.logo {
		width: 48px;
		margin-top: 12px;
	}

	.footer {
		width: calc(100% - 40px);
		background-color: #fff;
		border-radius: 8px;
		padding: 12px;
		margin: 0 20px;
		.desc {
			font-weight: 400;
			font-size: 10px;
			line-height: 15px;
			color: #4e5969;
			margin-bottom: 6px;
		}
		.link-container {
			width: calc(100% - 16px);
			padding: 0 8px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 36px;
			background: #f6f7f8;
		}

		.link_container_copy {
			cursor: pointer;
			width: 48px;
			height: 24px;
			border-radius: 4px;
			background: #2c6be9;
			font-size: 12px;
			font-weight: 500;
			line-height: 24px;
			text-align: center;
			color: #ffffff;
		}
		.link_container_text {
			cursor: pointer;
			font-weight: 400;
			font-size: 15px;
			line-height: 18px;
			color: #1868f1;
		}
	}
}

.content_container {
	background-color: #f4f7fc;
	padding-top: 48px;
	.content-title {
		height: 33px;
		font-weight: 500;
		font-size: 22px;
		line-height: 33px;
		text-align: center;
		color: #000000;
		margin-bottom: 20px;
	}
	.content_title {
		padding: 0 16px;
		display: flex;
		align-items: center;
		.active {
			color: #1868f1;
		}
	}
	.content-image {
		width: 28px;
		height: 28px;
		margin-right: 4px;
	}
	.content_content {
		display: flex;
		padding: 12px;
		background: #f4f7fc;
		width: calc(100% - 24px);
		.content_content_box {
			width: inherit;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 8px;
			background: #fff;
			padding: 24px 12px 0px 12px;
		}
		.content_content_title {
			margin-bottom: 12px;
			font-weight: 400;
			font-size: 12px;
			line-height: 18px;
			text-align: center;
			color: #4e5969;
		}
		.content-img {
			height: 172px;
			object-fit: cover;
		}
	}
}

.container_content {
	max-width: 600px;
	margin: 0 auto;
	padding: 48px 12px 0px 12px;
	border-radius: 8px;
	.content_box {
		line-height: 22.5px;
		font-weight: 400;
		font-size: 12px;
		color: #4e5969;
		margin: 12px 0 20px 0;
	}
	.content_title {
		font-weight: 500;
		font-size: 22px;
		line-height: 33px;
		text-align: center;
		color: #000000;
	}

	.excel-side {
		border: 1px solid #d9dee4;
		background: linear-gradient(180deg, #f2f4f7 0%, #ffffff 100%);

		.main_content {
			color: #7281ac;
		}
		.sub {
			color: #7281ac;
		}
	}
	.sgt-side {
		border: 1px solid #2a75fd;
		position: relative;
		background: linear-gradient(180deg, #e9f3fe 0%, #ffffff 100%);

		.dot {
			background: #0a42f1 !important;
		}
		.side-image {
			position: absolute;
			top: 0;
			left: 50%;
			transform: translateX(-50%);
		}
		.main_content {
			color: #2043ae;
		}
		.sub {
			color: #2043ae;
		}
		.item {
			border-top: 1px solid #2a75fd;
		}
	}
	.excel-side,
	.sgt-side {
		border-radius: 8px;
		padding: 0px 12px 12px 12px;
		height: max-content;
		.content {
			padding-top: 16px;
			.firstItem {
				height: 79px;
			}
			.item {
				display: flex;
				margin-bottom: 16px;
				border-top: 1px solid #d9dee4;
				padding-top: 16px;
				.dot {
					width: 6px;
					height: 6px;
					border-radius: 50%;
					background: #6c7c9d;
					margin-top: 8px;
					margin-right: 8px;
					flex-shrink: 0;
				}

				.text {
					.main_content {
						font-weight: 500;
						font-size: 14px;
						line-height: 21px;
						text-align: left;
						margin-bottom: 8px;
					}

					.sub {
						font-size: 12px;
						font-weight: 400;
						line-height: 19.6px;
					}
				}
			}
		}
		.content > :nth-child(1) {
			border-top: none !important;
		}
	}
}

.container_content_footer {
	padding: 0 12px 186px 12px;
	.title {
		font-weight: 500;
		font-size: 22px;
		line-height: 33px;
		text-align: center;
		color: #000000;
		margin: 48px 0 20px 0;
	}

	.card {
		background-color: #fff;
		padding: 20px 12px;
		height: 86px;
		margin-bottom: 12px;
		border-radius: 8px;
		box-shadow: 0px 4px 20px 0px #001c550a;
		position: relative;
	}
	.card_img {
		position: absolute;
		right: 0px;
		bottom: 0px;
		width: 120px;
		height: 120px;
	}

	.card_title {
		margin-bottom: 4px;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: #1d2129;
	}

	.card_desc {
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;
		color: #4e5969;
	}
}

.tab_box {
	width: 100%;
	height: 43px;
	box-sizing: border-box;
	font-size: 14px;
	font-weight: 600;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
	padding: 0 50px;
	.tab {
		width: 60px;
		height: 43px;
		padding: 0 14px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		cursor: pointer;

		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		color: #4e5969;
	}

	.tabAct {
		width: 60px;
		height: 43px;
		font-size: 14px;
		font-weight: 700;
		line-height: 22px;
		color: #1868f1;

		&::after {
			content: '';
			width: 32px;
			height: 2px;
			position: absolute;
			bottom: 6px;
			background-color: rgba(3, 93, 255, 1);
		}
	}
}
</style>
