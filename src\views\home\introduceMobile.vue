<template>
	<div style="background: #f4f7fc">
		<div class="container_header">
			<div class="header">
				<img src="@/assets/images/home/<USER>" alt="Logo" class="logo" />
				<div class="title">楼宇测评软件</div>
				<div class="desc">支持多业态资产的动态多参数运营分析，轻松管理资产组合</div>
			</div>
			<div class="footer">
				<div class="desc">请用电脑端浏览器搜索“标准”或打开下方链接体验完整功能</div>
				<div class="link-container">
					<div style="display: flex; align-items: center">
						<img src="../../assets/images/home/<USER>" style="margin-right: 4px" alt="" />
						<div @click="copyLink" class="link_container_text">https://bbzhun.com/#/introduce</div>
					</div>

					<div class="link_container_copy" @click="copyLink">复制</div>
				</div>
			</div>
		</div>
		<div class="content_container">
			<div class="content-title">
				<div>商估通可以帮您完成</div>
			</div>
			<el-collapse accordion :model-value="activeNames" @change="handleChange">
				<el-collapse-item v-for="(item, index) in propertyLIst" :key="index" :name="index">
					<template #title>
						<div class="content_title">
							<img :src="item.image" alt="content" class="content-image" />
							<div :class="{ active: activeNames[0] === index }">{{ item.title }}</div>
						</div>
					</template>
					<div class="content_content">
						<div class="content_content_box">
							<div class="content_content_title">{{ item.content }}</div>
							<img :src="item.img" alt="content" class="content-img" />
						</div>
					</div>
				</el-collapse-item>
			</el-collapse>
		</div>

		<div class="container_content">
			<div class="content_title">商估通对Argus的优势</div>
			<div class="content_box">
				假设您在管理一个大型商业地产项目，其中包含多个租户，每个租户的租赁协议都有不同的租金增长模式。
				例如，某些租户的租金可能在第五年以固定百分比增长，而其他租户可能基于CPI（消费者价格指数）的租金调整，或者根据租户的销售额比例进行浮动租金计算。
			</div>
			<div class="tab_box">
				<div
					class="tab"
					:class="activeIndex === index ? 'tabAct' : ''"
					v-for="(item, index) in customList"
					@click="handleTabClick(index)"
					:key="index"
				>
					{{ item.name }}
				</div>
			</div>
			<div class="compare-box">
				<div class="box_bg">
					<div class="box1"></div>
					<div class="box2" v-if="activeIndex == 0"></div>
					<div class="box3" v-if="activeIndex == 1"></div>
				</div>
				<div class="header_wrap">
					<div class="header1">对比维度</div>
					<div class="header2" v-if="activeIndex == 0">Argus</div>
					<div class="header3" v-if="activeIndex == 1">商估通</div>
				</div>
				<div class="content_wrap">
					<div class="content_item" v-for="(item, index) in compareData" :key="index">
						<div class="content1">{{ item.title }}</div>
						<div class="content2" v-if="activeIndex == 0">{{ item.argus }}</div>
						<div class="content3" v-if="activeIndex == 1">{{ item.syt }}</div>
					</div>
				</div>

				<div style="height: 10px"></div>
			</div>
		</div>

		<div class="container_content_footer">
			<div class="title">让资产评估工作变得简单</div>
			<div class="card" v-for="(item, index) in cardList" :key="index">
				<img :src="item.img" class="card_img" alt="" />
				<div class="card_title">{{ item.title }}</div>
				<div class="card_desc">{{ item.description }}</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import property from '../../assets/images/home/<USER>';
import customize from '../../assets/images/home/<USER>';
import dynamics from '../../assets/images/home/<USER>';
import propertyImg from '../../assets/images/home/<USER>';
import customizeImg from '../../assets/images/home/<USER>';
import dynamicsImg from '../../assets/images/home/<USER>';
import reportImg from '../../assets/images/home/<USER>';

import operation1 from '../../assets/images/home/<USER>';
import operation2 from '../../assets/images/home/<USER>';
import operation3 from '../../assets/images/home/<USER>';

const propertyLIst = ref([
	{
		title: '资产管理组合',
		image: property,
		img: propertyImg,
		content: '支持创建不同的资产组合，帮助您根据不同需求分析复杂的资产运营数据',
	},
	{
		title: '自定义创建资产',
		image: customize,
		img: customizeImg,
		content: '支持为资产组合自定义添加楼宇资产，帮助您维护个人专属资产数据',
	},
	{
		title: '动态分析运营数据',
		image: dynamics,
		img: dynamicsImg,
		content: '支持动态多参数运营资产数据，帮助您快速评估资产数据，做出更佳决策',
	},
	{
		title: '生成报告',
		image: property,
		img: reportImg,
		content: '实时生成现金流预测报告、实时估值报告、标准化产品融资报告',
	},
]);
const cardList = ref([
	{
		title: '专业数据动态分析',
		img: operation1,
		description: '基于平台强大的分析能力，帮助您高效管理丰富的资产数据和资产组合，让您能更快选、灵活地进行决策，做出更加合理的选择。',
	},
	{
		title: '功能强大逻辑连贯',
		img: operation2,
		description: '为您清晰划分逻辑处理模块及分析顺序，按序按需提供数据后，即可自动测算收益、估值等专业结论。',
	},
	{
		title: '交互友好操作清晰',
		img: operation3,
		description: '采用PC客户端的设计模式，配合客户明确用户操作引导，帮助您快速理解系统功能，清晰决策获取所需信息。',
	},
]);

const excelItems = [
	{
		title: '模拟所有租户的租金增长模式：',
		desc: '这些模式可能包括固定的百分比增长、基于市场租金的增长、或者基于某些经济指标如CPI的增长。',
	},
	{
		title: '自动化租金收入预测：',
		desc: '系统需要自动生成未来每一年的租金收入，并根据每个租户的不同租金条款进行调整。',
	},
	{
		title: '整合不同租赁条款并进行敏感性分析：',
		desc: '你希望能够轻松调整假设参数（如市场租金增长率或CPI增长率），并立即查看对整个地产项目现金流的影响。',
	},
];

const sgtItems = [
	{
		title: '复杂租赁条款的建模:',
		desc: '商估通有专门的功能来处理复杂的租赁条款和租金调整机制。它可以自动应用这些规则并生成精确的现金流预测，而这些在Excel中会需要大量的自定义公式和手动工作，且极易出错。',
	},
	{
		title: '自动化与效率：',
		desc: '商估通可以快速处理大量的租户数据和复杂的租赁条款，生成标准化的报告和预测。Excel虽然也可以实现这些，但会非常耗时，尤其当涉及到大量的租户和复杂的租金结构时，管理和维护这些模型将变得极其困难。',
	},
	{
		title: '敏感性分析：',
		desc: '商估通能够快速进行敏感性分析，允许你调整不同的假设条件（如CPI增长、市场租金变化）并即时查看对整体项目财务表现的影响。这在Excel中可能需要重复调整多个工作表和公式，效率低下且容易出错。',
	},
];

const customList = ref([
	{
		name: 'Argus',
	},
	{
		name: '商估通',
	},
]);

const compareData = ref([
	{
		title: '核心定位',
		argus: '国际专业级资产估值与投资分析工具',
		syt: '企业级楼宇测评工具',
		// syt: '企业级全周期资产运营管理平台（Argus国内优化版）',
	},
	{
		title: '技术架构',
		argus: '客户端-服务器架构，支持云端协作（需额外配置）',
		syt: '微服务架构，支持混合云部署，动态负载均衡，API开放生态',
	},
	{
		title: '核心功能',
		argus: '复杂现金流建模、敏感性分析、投资组合优化、自动化估值报告',
		syt: '动态现金流分析、估值与证券化分析、租赁与财务集成、楼宇效用分析',
		// syt: '动态多参数分析、楼宇资产全生命周期管理、租赁与财务集成、移动端操作',
	},
	{
		title: '算法引擎',
		argus: '专业级估值算法（DCF、IRR等），支持蒙特卡洛模拟、敏感性分析',
		syt: '机器学习驱动的动态预测模型（结合市场数据、政策变量）',
	},
	{
		title: '数据处理能力',
		argus: '支持大规模数据建模，内置专业算法和财务函数',
		syt: '支持海量数据动态分析，提供本土化财务模型与参数库',
	},
	{
		title: '协作与权限',
		argus: '支持多用户角色权限分配，审计追踪功能',
		syt: '企业级权限分级管理，支持实时协作与审批流程，移动端同步',
	},
	{
		title: '扩展性与集成',
		argus: '提供API接口，支持与财务、CRM等系统对接',
		syt: '开放API接口，深度集成国内主流企业系统（如OA、财务平台），支持定制化开发',
	},
	{
		title: '成本模型',
		argus: '软件许可费¥50万+/年 + 维护费15%，培训成本¥10万+/团队',
		syt: '订阅制4.8万元/年，含数据和技术服务包',
		// syt: '订阅制¥2.8万/年起，包含本土服务包',
	},
	{
		title: '成本与资源',
		argus: '高昂的许可费用+年度维护成本，需专业培训团队',
		syt: '订阅制模式，成本低于Argus，提供本地化培训与技术支持',
	},
	{
		title: '典型场景',
		argus: '大型商业地产估值、跨国投资组合分析',
		syt: '中大型企业楼宇资产运营、国内商业地产全周期管理、多项目并行决策',
	},
	{
		title: '技术优势',
		argus: '专业级估值引擎，支持蒙特卡洛模拟等高级分析',
		syt: '结合机器学习优化预测模型，支持AI辅助决策',
	},
	{
		title: '用户体验',
		argus: '功能复杂，需长期培训，专业术语门槛高',
		syt: '国内用户习惯优化，支持中文界面与本土化操作逻辑，移动端适配性强',
	},
	{
		title: '劣势',
		argus: '高昂成本、本地化支持有限、学习曲线陡峭，难度较高',
		syt: '生态成熟度较Argus低、部分高级功能需定制开发',
	},
	{
		title: '独特价值',
		argus: '国际认可的专业估值标准，适合跨国投资与复杂财务分析',
		syt: '国产替代首选方案，全生命周期管理+移动端支持，性价比优于Argus',
	},
]);

const activeIndex = ref(0);
const activeNames = ref([0]);

const copyLink = () => {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText('https://bbzhun.com/#/introduce')
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = 'https://bbzhun.com/#/introduce';
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
};
const handleChange = (val) => {
	console.log(val);
	activeNames.value = [val];
	console.log(activeNames.value);
};

const handleTabClick = (index) => {
	activeIndex.value = index;
};
</script>

<style scoped lang="scss">
.container_header {
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #e1eefd;
	.header {
		background-image: url('../../assets/images/home/<USER>');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		text-align: center;
		width: 100%;
		height: 208px;
		margin-top: 20px;
		.title {
			font-weight: 600;
			font-size: 26px;
			line-height: 39px;
			color: #1d2129;
			margin: 8px auto 4px auto;
		}
		.desc {
			font-size: 12px;
			font-weight: 400;
			line-height: 18px;
			color: #4e5969;
		}
	}

	.logo {
		width: 48px;
		margin-top: 12px;
	}

	.footer {
		width: calc(100% - 40px);
		background-color: #fff;
		border-radius: 8px;
		padding: 12px;
		margin: 0 20px;
		.desc {
			font-weight: 400;
			font-size: 10px;
			line-height: 15px;
			color: #4e5969;
			margin-bottom: 6px;
		}
		.link-container {
			width: calc(100% - 16px);
			padding: 0 8px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 36px;
			background: #f6f7f8;
		}

		.link_container_copy {
			cursor: pointer;
			width: 48px;
			height: 24px;
			border-radius: 4px;
			background: #2c6be9;
			font-size: 12px;
			font-weight: 500;
			line-height: 24px;
			text-align: center;
			color: #ffffff;
		}
		.link_container_text {
			cursor: pointer;
			font-weight: 400;
			font-size: 15px;
			line-height: 18px;
			color: #1868f1;
		}
	}
}

.content_container {
	background-color: #f4f7fc;
	padding-top: 48px;
	.content-title {
		height: 33px;
		font-weight: 500;
		font-size: 22px;
		line-height: 33px;
		text-align: center;
		color: #000000;
		margin-bottom: 20px;
	}
	.content_title {
		padding: 0 16px;
		display: flex;
		align-items: center;
		.active {
			color: #1868f1;
		}
	}
	.content-image {
		width: 28px;
		height: 28px;
		margin-right: 4px;
	}
	.content_content {
		display: flex;
		padding: 12px;
		background: #f4f7fc;
		width: calc(100% - 24px);
		.content_content_box {
			width: inherit;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 8px;
			background: #fff;
			padding: 24px 12px 0px 12px;
		}
		.content_content_title {
			margin-bottom: 12px;
			font-weight: 400;
			font-size: 12px;
			line-height: 18px;
			text-align: center;
			color: #4e5969;
		}
		.content-img {
			height: 172px;
			object-fit: cover;
		}
	}
}

.container_content {
	max-width: 600px;
	margin: 0 auto;
	padding: 48px 12px 0px 12px;
	border-radius: 8px;
	.content_box {
		line-height: 22.5px;
		font-weight: 400;
		font-size: 12px;
		color: #4e5969;
		margin: 12px 0 20px 0;
	}
	.content_title {
		font-weight: 500;
		font-size: 22px;
		line-height: 33px;
		text-align: center;
		color: #000000;
	}
}

.container_content_footer {
	padding: 0 12px 186px 12px;
	.title {
		font-weight: 500;
		font-size: 22px;
		line-height: 33px;
		text-align: center;
		color: #000000;
		margin: 48px 0 20px 0;
	}

	.card {
		background-color: #fff;
		padding: 20px 12px;
		height: 86px;
		margin-bottom: 12px;
		border-radius: 8px;
		box-shadow: 0px 4px 20px 0px #001c550a;
		position: relative;
	}
	.card_img {
		position: absolute;
		right: 0px;
		bottom: 0px;
		width: 120px;
		height: 120px;
	}

	.card_title {
		margin-bottom: 4px;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: #1d2129;
	}

	.card_desc {
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;
		color: #4e5969;
	}
}

.tab_box {
	width: 100%;
	height: 32px;
	box-sizing: border-box;
	font-size: 14px;
	font-weight: 400;
	display: flex;
	align-items: center;
	margin-bottom: 12px;
	gap: 8px;
	.tab {
		height: 100%;
		padding: 0 20px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		cursor: pointer;
		font-size: 14px;
		font-weight: 400;
		line-height: 26px;
		color: #86909c;
		border: 1px solid #e5e6eb;
		border-radius: 4px;
		box-sizing: border-box;
	}

	.tabAct {
		font-weight: 500;
		color: #1868f1;
		background-color: #edf3fe;
		border-color: #1868f1;
		border-radius: 4px;
	}
}
.compare-box {
	position: relative;
	.box_bg {
		z-index: 1;
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		gap: 8px;

		.box1 {
			border-radius: 8px;
			width: 96px;
			background: linear-gradient(180deg, #f2f4f7 0%, #ffffff 49.52%, #ffffff 100%);
		}
		.box2 {
			border-radius: 8px;
			flex: 1;
			background: linear-gradient(180deg, #e9f3fe 0%, #ffffff 49.52%, #ffffff 100%);
		}
		.box3 {
			border-radius: 8px;
			flex: 1;
			background: linear-gradient(180deg, #e9ebfe 0%, #ffffff 50%, #ffffff 100%);
		}
	}

	.header_wrap {
		position: relative;
		z-index: 9;
		height: 48px;
		display: flex;
		justify-content: center;
		gap: 8px;
		margin-bottom: 10px;
		.header1 {
			width: 96px;
			background: linear-gradient(284.14deg, #dfe4ea 4.47%, #ecf1f7 88.62%);
			border: 1px solid #dee5ec;
			color: #0f2860;
		}
		.header2 {
			flex: 1;
			background: linear-gradient(95.82deg, #eff5fe 5.7%, #cfe5fc 73.5%);
			border: 1px solid #b7daff;
			color: #1868f1;
		}
		.header3 {
			flex: 1;
			background: linear-gradient(95.82deg, #eae7fb 5.7%, #cacdfb 73.5%);
			border: 1px solid #cdcffa;
			color: #3141c5;
		}
		.header1,
		.header2,
		.header3 {
			display: flex;
			align-items: center;
			justify-content: center;
			box-sizing: border-box;
			border-radius: 8px;
			font-size: 14px;
			font-weight: 500;
		}
	}
	.content_wrap {
		position: relative;
		z-index: 9;
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.content_item {
			position: relative;
			z-index: 9;
			display: flex;
			justify-content: center;
			gap: 8px;
			box-sizing: border-box;
			cursor: pointer;
			width: 100%;
			.content1 {
				width: 96px;
				justify-content: center;
				color: #0f2860;
			}
			.content2 {
				flex: 1;
				text-align: left;
				padding: 6px 12px;
				color: #5e6a8e;
			}
			.content3 {
				flex: 1;
				text-align: left;
				padding: 6px 12px;
				color: #0f2860;
			}
			.content1,
			.content2,
			.content3 {
				display: flex;
				align-items: center;
				box-sizing: border-box;
				font-size: 12px;
				font-weight: 400;
				line-height: 18px;
			}
		}
	}
}
</style>
