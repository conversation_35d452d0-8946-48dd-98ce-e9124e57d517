<template>
	<div v-if="basic.lists2">
		<div class="page">
			<div class="row p1">
				<businessTemp>
					<template #left>
						<div class="cc">
							<div class="buildname">
								<div>{{ basic.buildingName }}</div>
								<div class="static_label">写字楼</div>
							</div>
							<div>租户调查</div>
							<div class="time">{{ getDate() }}</div>
						</div>
					</template>
					<template #right>
						<el-image :src="address + imgs[2]" fit="cover" />
					</template>
				</businessTemp>
			</div>
		</div>
		<div class="page">
			<div class="row p2">
				<div class="page_header">
					<div class="header_left">标标准</div>
					<div class="header_right">{{ getDate() }}{{ basic.buildingName }}写字楼租户调查</div>
				</div>
				<businessTemp>
					<template #left>
						<h1>前言</h1>
						<div>
							2020年至今，新冠疫情使写字楼办公企业的商业活动收到不同程度的影响。{{
								basic.city
							}}作为商业活动中心，展示出坚强的韧性，在需求端有较好表现。
						</div>
						<div>
							{{ basic.buildingName }}写字楼作为单一产权优质甲级写字楼，商业环境优越。术木智能对{{
								basic.buildingName
							}}写字楼进行了全面普查，对比2023年写字楼需求结构，并进一步对重点租户从租户普查、需求势能、租赁策略三个角度，综合得出租赁用户画像，对比{{
								basic.city
							}}与全国租赁需求差异，探寻{{ basic.buildingName }}写字楼租赁的未来趋势。
						</div>
					</template>
					<template #right>
						<el-image :src="address + imgs[1]" fit="cover" />
					</template>
				</businessTemp>
				<div class="page_footer">
					<div class="footer_bottom">
						<span class="year">{{ getYear() }}</span
						>术木智能
					</div>
				</div>
			</div>
		</div>
		<div class="page">
			<div class="row p3">
				<el-image class="cover_img" :src="address + imgs[0]" fit="cover" />
				<div class="cover">
					<h1>目录</h1>
					<h2>{{ basic.buildingName }}写字楼租户调查</h2>
					<div>01 租户普查</div>
					<h2>{{ basic.buildingName }}写字楼重点租户调查</h2>
					<div>02 需求势能</div>
					<div>03 租赁策略</div>
				</div>
			</div>
		</div>
		<div class="page">
			<div class="row p4">
				<div class="page_header">
					<div class="header_left">标标准</div>
					<div class="header_right">{{ getDate() }}{{ basic.buildingName }}写字楼租户调查</div>
				</div>
				<businessTemp>
					<template #left>
						<h2 class="subtitle">01 租户普查</h2>
						<h3>{{ basic?.lists2?.[3]?.tenantCensusText.text0 }}</h3>
						<div>
							{{ basic?.lists2?.[3]?.tenantCensusText.text1 }}
						</div>
						<div>{{ basic?.lists2?.[3]?.tenantCensusText.text2 }}</div>
						<div>{{ basic?.lists2?.[3]?.tenantCensusText.text3 }}</div>
						<div>{{ basic?.lists2?.[3]?.tenantCensusText.text4 }}</div>
					</template>
					<template #right>
						<div class="charts">
							<div class="name">信息技术 <br/>{{ basic?.lists2?.[0]?.IT?.total }}%</div>
							<div class="char" id="chart1"></div>
						</div>
						<div class="charts">
							<div class="name">金融 <br/>{{ basic?.lists2?.[0]?.finance?.total }}%</div>
							<div class="char" id="chart2"></div>
						</div>
						<div class="charts">
							<div class="name">租赁和商务服务 <br/>{{ basic?.lists2?.[0]?.businessServices?.total }}%</div>
							<div class="char" id="chart3"></div>
						</div>
						<div class="charts">
							<div class="name">批发和零售 <br/>{{ basic?.lists2?.[0]?.wholesaleRetail?.total }}%</div>
							<div class="char" id="chart4"></div>
						</div>
						<div class="charts">
							<div class="name">其他 <br/>{{ basic?.lists2?.[0]?.other?.total }}%</div>
							<div class="char" id="chart5"></div>
						</div>
						<div class="chart-desc">图:{{ new Date().getFullYear() }}年{{ basic.buildingName }}写字楼租户构成（按租户数量）</div>
					</template>
				</businessTemp>
				<div class="page_footer">
					<div class="footer_top">数据来源：术木智能，{{ getDate() }}</div>
					<div class="footer_bottom">
						<span class="year">{{ getYear() }}</span
						>术木智能
					</div>
				</div>
			</div>
		</div>
		<div class="page">
			<div class="row p5">
				<div class="page_header">
					<div class="header_left">标标准</div>
					<div class="header_right">{{ getDate() }}{{ basic.buildingName }}写字楼租户调查</div>
				</div>
				<businessTemp>
					<template #left>
						<h2 class="subtitle">01 租户普查</h2>
						<h3>{{basic.leaseTop?.[0]}}与{{basic.leaseTop?.[1]}}引领全年租赁需求</h3>
						<div>
							从2024年前半年看来， {{basic.leaseTop?.[0]}}与{{basic.leaseTop?.[1]}}仍是{{
								basic.city
							}}甲级写字楼新增租赁的主力行业来源。两大行业在国内经济发展大背景下，驱动租赁需求释放。房地产行业稳定发挥，除开发商升级搬迁外，第三方办公运营表现较好。
						</div>
						<div>
							尽管疫情在线下对{{basic.leaseTop?.[0]}}造成负面影响，以{{basic.leaseSecondaryTop?.[0] }}{{basic.leaseSecondaryTop?.[1]?'、'+ basic.leaseSecondaryTop?.[1]:''}}{{basic.leaseSecondaryTop?.[2]? '、'+ basic.leaseSecondaryTop?.[2]:'' }}为代表的{{basic.leaseTop?.[0]}}企业仍在不确定因素下展现出强劲韧性，在上半年新租需求中位居前列。
						</div>
					</template>
					<template #right>
						<div id="chart6"></div>
						<div class="chart-desc">图：{{ basic.city }}甲级写字楼新增租赁行业占比</div>
					</template>
				</businessTemp>
				<div class="page_footer">
					<div class="footer_top">数据来源：术木智能，{{ getDate() }}</div>
					<div class="footer_bottom">
						<span class="year">{{ getYear() }}</span
						>术木智能
					</div>
				</div>
			</div>
		</div>
		<div class="page">
			<div class="row p6">
				<div class="page_header">
					<div class="header_left">标标准</div>
					<div class="header_right">{{ getDate() }}{{ basic.buildingName }}写字楼租户调查</div>
				</div>
				<businessTemp>
					<template #left>
						<h2 class="subtitle">02 需求势能</h2>
						<h3>超过两成企业计划增加办公室面积</h3>
						<div>
							30%的{{
								basic.city
							}}受访企业预计将在2024年下半年增加办公面积，与全国调研数据几乎持平。而59%的受访企业表示维持现有办公面积，明显高于全国比例。
						</div>
						<div>有8%的受访企业计划缩减面积，明显低于全国平均水平。而3%的受访企业表示不确定。</div>
					</template>
					<template #right>
						<div id="chart7"></div>
						<div class="chart-desc">图：{{ basic.city }}与全国办公室面积变化预测</div>
					</template>
				</businessTemp>
				<div class="page_footer">
					<div class="footer_top">数据来源：术木智能，{{ getDate() }}</div>
					<div class="footer_bottom">
						<span class="year">{{ getYear() }}</span
						>术木智能
					</div>
				</div>
			</div>
		</div>
		<div class="page">
			<div class="row p7">
				<div class="page_header">
					<div class="header_left">标标准</div>
					<div class="header_right">{{ getDate() }}{{ basic.buildingName }}写字楼租户调查</div>
				</div>
				<businessTemp>
					<template #left>
						<h2 class="subtitle">02 需求势能</h2>
						<h3>{{basic.expandTop}}扩张意愿明显</h3>
						<div>
							{{ basic.district }}受访企业中，{{basic.expandTopPercentage}}的{{basic.expandTop}}计划增加办公面积，扩张意愿最为明显。从2024年上半年{{
								basic.district
							}}甲级写字楼租赁需求看，{{basic.expandTop}}连续两个季度占比第一。
						</div>
						<div>而其中的本地企业，一直是新增租赁市场主要资金来源。</div>
					</template>
					<template #right>
						<!-- <div>图：2024年下半年预期办公室面积增加的内外资占比</div> -->
						<!-- <div class="percent">
							<div>11</div>
							<div>11</div>
							<div>11</div>
						</div> -->
						<div class="echars">
							<div id="chart8"></div>
							<!-- <div class="rrr">123</div> -->
						</div>
						<div class="chart-desc">图：2024年下半年{{ basic.district }}写字楼新增租赁本来源占比</div>
					</template>
				</businessTemp>
				<div class="page_footer">
					<div class="footer_top">数据来源：术木智能，{{ getDate() }}</div>
					<div class="footer_bottom">
						<span class="year">{{ getYear() }}</span
						>术木智能
					</div>
				</div>
			</div>
		</div>
		<div class="page">
			<div class="row p8">
				<div class="page_header">
					<div class="header_left">标标准</div>
					<div class="header_right">{{ getDate() }}{{ basic.buildingName }}写字楼租户调查</div>
				</div>
				<businessTemp>
					<template #left>
						<h2 class="subtitle">03 租赁策略</h2>
						<h3>租赁写字楼仍是企业办公的主要选择</h3>
						<div>
							58%的{{
								basic.district
							}}受访企业表示，在扩张办公室面积时，租赁写字楼是他们最倾向的物业类型。在各种办公物业类型中，租赁写字楼占据了绝对的首选位置。
						</div>
						<div>
							同时21%受访企业也表示，由于疫情带来的居家的办公形态的转变和对未来经济的不可预见性，开始愿意考虑使用灵活办公空间，这超过了其他物业类型位居第二。
						</div>
					</template>
					<template #right>
						<div id="chart9"></div>
						<div class="chart-desc">图：企业办公物业类型偏好</div>
						<div class="bg"></div>
					</template>
				</businessTemp>
				<div class="page_footer">
					<div class="footer_top">数据来源：术木智能，{{ getDate() }}</div>
					<div class="footer_bottom">
						<span class="year">{{ getYear() }}</span
						>术木智能
					</div>
				</div>
			</div>
		</div>
		<div class="page">
			<div class="row p9">
				<div class="page_header">
					<div class="header_left">标标准</div>
					<div class="header_right">{{ getDate() }}{{ basic.buildingName }}写字楼租户调查</div>
				</div>
				<businessTemp>
					<template #left>
						<h2>03 租赁策略</h2>
						<h3>各行业不动产策略出现分化</h3>
						<div>
							行业特征决定了不动产的选择策略。例如消费和工业和商业服务将利用租金下降的窗口期对办公地点进行升级和扩租，而房地产行业由于线下销售部分前几年受到疫情和宏观经济环境预期的影响而被迫收缩。
						</div>
						<div>科技行业倾向于总部办公，而消费和原材料和通讯服务业 ，更多地选择分散办公。</div>
					</template>
					<template #right>
						<div class="strategy">
							<div class="item" v-for="(item, index) in arr">
								<div class="icon">
									<el-icon>
										<!-- <Edit /> -->
										<component :is="item.icon" />
									</el-icon>
								</div>
								<div class="tr">
									<span class="tt">{{ item.title }}:</span>{{ item.des }}
								</div>
							</div>
						</div>
						<div class="chart-desc">图：不同行业企业不动产策略选择差异</div>
					</template>
				</businessTemp>
				<div class="page_footer">
					<div class="footer_top">数据来源：术木智能，{{ getDate() }}</div>
					<div class="footer_bottom">
						<span class="year">{{ getYear() }}</span
						>术木智能
					</div>
				</div>
			</div>
		</div>
		<div class="page" style="margin-bottom: 0px;">
			<div class="row p10">
				<div class="page_header">
					<div class="header_left">标标准</div>
					<div class="header_right">{{ getDate() }}{{ basic.buildingName }}写字楼租户调查</div>
				</div>
				<businessTemp>
					<template #left>
						<h2>03 租赁策略</h2>
						<h3>交通便利是关键因素</h3>
						<div>交通便利性仍是企业最关注因素，另外楼宇及商圈配套也在租赁决策中占重要位置。</div>
					</template>
					<template #right>
						<div class="flex">
							<div class="item" v-for="(item, index) in arr2">
								<div class="percent" :style="`color:${item.color}`">{{ item.percent }}</div>
								<div class="title">{{ item.title }}</div>
							</div>
						</div>
						<div class="chart-desc">图：企业租赁决策的配套、服务偏好</div>
					</template>
				</businessTemp>
				<div class="page_footer">
					<div class="footer_top">数据来源：术木智能，{{ getDate() }}</div>
					<div class="footer_bottom">
						<span class="year">{{ getYear() }}</span
						>术木智能
					</div>
				</div>
			</div>
		</div>
	</div>
	<div v-else="lingshow">
		<div class="lingshow">
			<div class="title">一、品牌分布</div>
			<table class="custom-table">
				<thead>
					<tr>
						<th style="width: 50px">楼层</th>
						<th style="width: 80px">业态</th>
						<th>商户名称</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="(item, index) in lingshow.commerceRetailShoppingMallVoList" :key="index">
						<!-- 解决 merchants[index - 1] undefined 错误 -->
						<td v-if="index === 0 || item.floor !== lingshow.commerceRetailShoppingMallVoList[index - 1].floor">
							{{ item.floor }}
						</td>
						<td v-else></td>
						<td>{{ item.commercial }}</td>
						<td>{{ item.merchantName }}</td>
					</tr>
				</tbody>
			</table>
			<div style="display: flex">
				<p style="padding: 8px; border: 1px solid #ddd; text-align: left; margin: 0; width: 50px">出租率</p>
				<p style="padding: 8px; border: 1px solid #ddd; text-align: left; margin: 0; width: 92%" v-if="lingshow.rent_rate">
					{{ lingshow.rent_rate }}%
				</p>
				<p style="padding: 8px; border: 1px solid #ddd; text-align: left; margin: 0; width: 92%" v-else>暂无数据</p>
			</div>
			<div class="title">二、品牌分析</div>
			<div class="mintit">业态组合</div>
			<div class="ttb">
				<div v-for="(item, index) in lingshow.commerceRetailAnalyzeVoList" :key="index" style="border: 1px solid #ddd; width: 100%">
					<div style="border-bottom: 1px solid #ddd; padding: 8px">{{ item.commercial }}</div>
					<div style="border-bottom: 1px solid #ddd; padding: 8px">{{ item.num }}</div>
					<div style="padding: 8px">{{ item.percentage }}%</div>
				</div>
			</div>
			<div class="mintit">业态配比</div>
			<div class="yetai">
				<div class="minleft">品牌定位</div>
				<div class="minright">{{ lingshow?.commerceRetailPositionVo?.brandPositioning }}</div>
			</div>
			<div class="yetai">
				<div class="minleft">客群定位</div>
				<div class="minright">{{ lingshow?.commerceRetailPositionVo?.customerName }}</div>
			</div>
			<div class="yetai">
				<div class="minleft">业态组合</div>
				<div class="minright">{{ lingshow?.commerceRetailPositionVo?.businessMix }}</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import { business, businesstwo } from '@/api/baogao';
import { useRoute } from 'vue-router';
import { getDate, getYear } from '../../utils/time';
import * as echarts from 'echarts';
import businessTemp from './businessTemp.vue';
import { formatter } from 'element-plus';
const address = ref('https://static.biaobiaozhun.com/');
const route = useRoute();
const id = ref('');
const lingshow = ref({});
const getling = async () => {
	const res = await businesstwo({ buildingId: id.value });
	if (res.code === 200) {
		lingshow.value = res.result;
		lingshow.value.commerceRetailAnalyzeVoList.unshift({
			commercial: '业态',
			num: '数量',
			percentage: '比例',
		});
	}
};
const imgs = ref([]);
const basic = ref({});
const qiye = ref({});
const shu = ref({});

const getxie = async () => {
	const res = await business({ buildingId: id.value });
	if (res.code === 200) {
		basic.value = res.result;
		imgs.value = res.result.buildPicList;
		qiye.value = res.result?.lists2?.[5];
		shu.value = res.result?.lists2?.[4];
		console.log(res.result?.lists2?.[4], 999);
		arr2.value = [
			{
				title: '公共/城际交通',
				percent: shu.value?.publicIntercityTraffic + '%',
				// color: '#33FF57', // 随机颜色
				color: '#82BCAE', // 随机颜色
			},
			{
				title: '楼宇及商圈配套',
				percent: shu.value?.buildingDistrictAncillary + '%',
				// color: '#3357FF', // 随机颜色
				color: '#94A7A9', // 随机颜色
			},
			{
				title: '楼内租户结构',
				percent: shu.value?.tenantStructure + '%',
				color: '#18F798', // 随机颜色
			},

			{
				title: '业主声誉',
				percent: shu.value?.ownersReputation + '%',
				// color: '#FFD133', // 随机颜色
				color: '#E6E4A2', // 随机颜色
			},
			{
				title: '物业管理团队',
				percent: shu.value?.propertyManage + '%',
				// color: '#33FFD1', // 随机颜色
				color: '#D58165', // 随机颜色
			},
			{
				title: '单一业权',
				percent: shu.value?.singleTitle + '%',
				// color: '#A133FF', // 随机颜色
				color: '#8F5479', // 随机颜色
			},
			{
				title: '精装修服务',
				percent: shu.value?.flexibleOfficeConfig + '%',
				// color: '#FF5733', // 随机颜色
				color: '#AB8EC8', // 随机颜色
			},
			{
				title: '灵活办公空间配置',
				percent: shu.value?.flexibleOfficeConfig + '%',
				// color: '#FF8333', // 随机颜色
				color: '#203A6A', // 随机颜色
			},
			{
				title: '楼内共享办公室',
				percent: shu.value?.sharedMeetingRoom + '%',
				// color: '#33A1FF', // 随机颜色
				color: '#3E7CA6', // 随机颜色
			},
			{
				title: '智能楼宇管理系统',
				percent: shu.value?.intelligentBuildingManage + '%',
				// color: '#57FF33', // 随机颜色
				color: '#627176', // 随机颜色
			},
		];
	}
};

onMounted(async () => {
	id.value = route.query.id;
	await getxie();
	await getling();
	initEchart();
});
const arr2 = ref([
	{
		title: '公共/城际交通',
		percent: shu.value?.publicIntercityTraffic,
		color: '#33FF57', // 随机颜色
	},
	{
		title: '楼宇及商圈配套',
		percent: '69%',
		color: '#3357FF', // 随机颜色
	},
	{
		title: '楼内租户结构',
		percent: '69%',
		color: '#FF33A1', // 随机颜色
	},
	{
		title: '业主声誉',
		percent: '69%',
		color: '#FFD133', // 随机颜色
	},
	{
		title: '物业管理团队',
		percent: '69%',
		color: '#33FFD1', // 随机颜色
	},
	{
		title: '单一业权',
		percent: '69%',
		color: '#A133FF', // 随机颜色
	},
	{
		title: '灵活办公空间配置',
		percent: '69%',
		color: '#FF8333', // 随机颜色
	},
	{
		title: '楼内共享办公室',
		percent: '69%',
		color: '#33A1FF', // 随机颜色
	},
	{
		title: '智能楼宇管理系统',
		percent: '69%',
		color: '#57FF33', // 随机颜色
	},
]);
const arr = ref([
	{
		icon: 'UploadFilled',
		title: '升级扩租',
		des: '疫情过后，消费和工商业服务行业呈现复苏态势，商业活力增强，对于办公地点的需求也呈现上涨的需求态势。',
	},
	{
		icon: 'location',
		title: '区位搬迁',
		des: '专业服务、科技互联网、房地产建筑表现出去中心化特征;金融业则与之相反，表现出再中心化的特征;零售贸易、房地产为节约成本，意向搬迁至租金更低楼宇。',
	},
	{
		icon: 'Connection',
		title: '整合到更少的办公地点',
		des: '科技互联网行业出于优化办公效率和节约成本的考量，计划整合办公地点。',
	},
	{
		icon: 'OfficeBuilding',
		title: '分散到更多的办公地点',
		des: '消费服务行业出于临近客户的考量，更多选择分散办公地点;而房地产建筑行业更倾向项目团队办公而选择分散办公地点。',
	},
	{
		icon: 'coin',
		title: '将部分职能转移到较低成本城市',
		des: '房地产建筑倾向降低成本，选择将部分职能转移到成本更低的城市。',
	},
]);
const initEchart = () => {
	let myChart1 = echarts.init(document.querySelector('#chart1'));
	let myChart2 = echarts.init(document.querySelector('#chart2'));
	let myChart3 = echarts.init(document.querySelector('#chart3'));
	let myChart4 = echarts.init(document.querySelector('#chart4'));
	let myChart5 = echarts.init(document.querySelector('#chart5'));
	let myChart6 = echarts.init(document.querySelector('#chart6'));
	let myChart7 = echarts.init(document.querySelector('#chart7'));
	let myChart8 = echarts.init(document.querySelector('#chart8'));
	let myChart9 = echarts.init(document.querySelector('#chart9'));

	const opt7 = () => {
		const option = {
			legend: { show: true },
			grid: {
				show: false,
			},
			dataset: {
				source: [
					['增加', 30, 32],
					['保持不变', 59, 38],
					['减少', 8, 24],
					['不确定', 3, 6],
				],
			},
			xAxis: {
				type: 'category',
			},
			yAxis: {
				label: {
					show: false,
				},
			},

			series: [
				// These series are in the first grid.
				{
					name: basic.value.city,
					type: 'bar',
					itemStyle: {
						color: '#80baac',
					},
					label: {
						show: true,
						position: 'top',
						formatter: (params) => {
							return params.value[1] + '%';
						},
					},
				},
				{
					name: '全国',
					type: 'bar',
					itemStyle: {
						color: '#455259',
					},
					label: {
						show: true,
						position: 'top',
						formatter: (params) => {
							return params.value[2] + '%';
						},
					},
				},
				// These series are in the second grid.
			],
		};
		return option;
	};
	const opt8 = {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
			},
		},
		legend: {
			show: true
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			data: qiye.value.categories, // 对应的横轴标签
		},
		yAxis: {
			type: 'value',
		},
		series: [
			{
				label: {
					show: true,
					formatter: (params) => params.value + '%',
					position: 'top'
				},
				name: '内资企业',
				type: 'bar',
				data: qiye.value.series[0].data,
			},
			{
				label: {
					show: true,
					formatter: (params) => params.value + '%',
					position: 'top'
				},
				name: '外资企业',
				type: 'bar',
				data: qiye.value.series[1].data,
			},
			{
				label: {
					show: true,
					formatter: (params) => params.value + '%',
					position: 'top'
				},
				name: '本地企业',
				type: 'bar',
				data: qiye.value.series[2].data,
			},
		],
	};
	const opt9 = {
		xAxis: {
			type: 'category',
			data: ['租赁写字楼', '使用灵活办公空间', '租赁商务园区办公楼', '自建企业总部', '购置写字楼', '购置商务园区办公楼'],
			axisLabel: {
				fontSize: 8, // 设置字体大小
				// rotate: 30, // 旋转标签，以避免重叠
				margin: 10, // 调整标签与轴线的距离
				interval: 0,
			},
		},
		yAxis: {
			type: 'value',
			max: 100
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true,
		},
		series: [
			{
				data: [58, 21, 16, 9, 7, 3],
				type: 'bar',
				label: {
					show: true,
					formatter: (params) => params.value + '%',
					position: 'top'
				},
			},
		],
	};
	const opt = (arrY, data, color) => {
		let option = {
			// legend: {show:false},
			xAxis: {
				splitLine: {
					show: false,
				},
				// max: 'dataMax',
				max: 100,
				axisLabel: {
					show: false,
					rotate: 50,
					fontSize: 12,
				},
			},
			grid: {
				top: '3%',
				left: '20%',
				bottom: '10%',
			},
			yAxis: {
				type: 'category',
				data: arrY,

				axisLabel: {
					// rotate:50,
					fontSize: 9,
				},
			},
			series: [
				{
					// realtimeSort: true,
					type: 'bar',
					data: data,
					barWidth: '50%',
					itemStyle: {
						color: color,
					},
					label: {
						show: true,
						position: 'right',
						formatter: function (params) {
							return params.value + '%'
						},
					},
				},
			],

			animationDuration: 0,
			animationDurationUpdate: 3000,
			animationEasing: 'linear',
			animationEasingUpdate: 'linear',
		};
		return option;
	};
	const arrY1 = ['电视广播', '互联网', '软件和信息技术'];
	const data1 = [
		basic.value?.lists2?.[0]?.IT?.telecomBroadcast,
		basic.value?.lists2?.[0]?.IT?.internet,
		basic.value?.lists2?.[0]?.IT?.softInfoTech,
	];
	const arrY2 = ['货币金融', '资本市场', '保险业', '其他金融'];
	const data2 = [
		basic.value?.lists2?.[0]?.finance?.monetaryFinance,
		basic.value?.lists2?.[0]?.finance?.capitalMarket,
		basic.value?.lists2?.[0]?.finance?.insurance,
		basic.value?.lists2?.[0]?.finance?.otherFinance,
	];
	const arrY3 = ['租赁', '商务服务'];
	const data3 = [
		basic.value?.lists2?.[0]?.businessServices?.lease,
		basic.value?.lists2?.[0]?.businessServices?.businessServices,
	];
	const arrY4 = ['批发', '零售'];
	const data4 = [
		basic.value?.lists2?.[0]?.wholesaleRetail?.wholesale,
		basic.value?.lists2?.[0]?.wholesaleRetail?.retail,
	];
	const arrY5 = ['房地产', '制造', '交通运输', '科学研究', '其他'];
	const data5 = [
		basic.value?.lists2?.[0]?.other?.realEstate,
		basic.value?.lists2?.[0]?.other?.fabricate,
		basic.value?.lists2?.[0]?.other?.transportation,
		basic.value?.lists2?.[0]?.other?.scienceResearch,
		basic.value?.lists2?.[0]?.other?.other,
	];
	const arrY6 = ['制造', '金融', '信息技术', '租赁和商务服务', '其他', '房地产', '科学研究', '交通运输','批发零售'];
	const data6 = [
		basic.value?.lists2?.[1]?.fabricate,
		basic.value?.lists2?.[1]?.finance,
		basic.value?.lists2?.[1]?.infoTech,
		basic.value?.lists2?.[1]?.leaseBusiness,
		basic.value?.lists2?.[1]?.other,
		basic.value?.lists2?.[1]?.realEstate,
		basic.value?.lists2?.[1]?.scienceResearch,
		basic.value?.lists2?.[1]?.transportation,
		basic.value?.lists2?.[1]?.wholesaleRetail,
	];
	myChart1.setOption(opt(arrY1.reverse(), data1.reverse(), '#80baac'));
	myChart2.setOption(opt(arrY2.reverse(), data2.reverse(), '#435253'));
	myChart3.setOption(opt(arrY3.reverse(), data3.reverse(), '#17e88f'));
	myChart4.setOption(opt(arrY4.reverse(), data4.reverse(), '#3d7ba6'));
	myChart5.setOption(opt(arrY5.reverse(), data5.reverse(), '#a287be'));
	myChart6.setOption(opt(arrY6.reverse(), data6.reverse(), '#e6b9b8'));
	myChart7.setOption(opt7());
	myChart8.setOption(opt8);
	myChart9.setOption(opt9);
};
</script>
<style lang="less" scoped>
@width40: 40%;
@width50: 50%;

.lingshow {
	margin: 20px auto;
	width: 1046px;
	.yetai {
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid #ddd;
		.minleft {
			text-align: center;
			width: 30%;
			border-right: 1px solid #ddd;
		}
		.minright {
			width: 70%;
			padding: 8px;
		}
	}

	.ttb {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.mintit {
		font-size: 20px;
		font-weight: bold;
		padding: 20px 0;
	}

	.title {
		font-size: 24px;
		font-weight: bold;
		padding: 20px 0 20px 0;
	}
	.custom-table {
		width: 100%;
		border-collapse: collapse;
		/* 去掉重复边框 */
	}

	.custom-table th,
	.custom-table td {
		padding: 8px;
		border: 1px solid #ddd;
		text-align: left;
	}

	.custom-table td {
		border-top: none;
		/* 去掉重复边框 */
	}

	.custom-table th {
		background-color: #f2f2f2;
	}
}

.page {
	height: 717px;
	width: 1046px;
	margin-bottom: 100px;
}

.f-c(@direction) {
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: @direction;
}

.row {
	.f-c(row);
	.page_header {
		border-bottom: 1px #c9d1d2 solid;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.header_left,
		.header_right {
			padding: 7px;
			font-size: 12px;
			font-weight: 600;
		}
		.header_left {
			font-weight: 400;
			color: #435253;
		}
	}
	.page_footer {
		width: 100%;
		.footer_top {
			font-size: 12px;
			padding: 7px;
		}
		.footer_bottom {
			font-size: 12px;
			padding: 7px;
			border-top: 1px #c9d1d2 solid;
			text-align: right;
			.year {
				color: #f5a631;
			}
		}
	}
}

.col {
	.f-c(column);
}

.p1 {
	.cc {
		div {
			margin: 35px 0;
			font-size: 40px;
			font-weight: 700;
			text-align: right;
			line-height: 40px;
		}
		.buildname {
			line-height: 60px;
			.static_label {
				margin: 0;
			}
		}
		.time {
			font-size: 20px;
		}
	}

	.l {
		width: 100%;
		.f-c(column);
		align-items: flex-end;
		padding-right: 40px;
		height: 100%;
		font-weight: 700;

		div {
			margin: 35px 0;
			font-size: 40px;
		}

		.time {
			font-size: 20px;
		}
	}

	.bg {
		height: 100%;
		width: 100%;
		background-image: url('../../assets/images/bussiness/home.jpg');
		background-size: cover;
	}
}

.p2 {
	display: flex;
	flex-direction: column;
	.right {
		.bg {
			background-image: url('../../assets/images/bussiness/img1.png');
			width: 100%;
			height: 400px;
		}
	}
}

.p3 {
	position: relative;
	.cover_img {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		z-index: -10;
	}
	.cover {
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		padding-left: 300px;
		color: #fff;
		justify-content: center;
		display: flex;
		flex-direction: column;
		z-index: 10;
		div {
			margin: 10px 0;
		}
	}
}

.p4 {
	display: flex;
	flex-direction: column;
	.right {
		.charts {
			width: 100%;
			height: 120px;
			display: flex;
			align-items: center;

			.name {
				width: 120px;
				text-align: left;
			}

			.char {
				flex: 1;
				width: 100%;
				height: 100%;
			}
		}
	}
}

.p5 {
	display: flex;
	flex-direction: column;
	.right {
		#chart6 {
			width: 100%;
			height: 600px;
		}
	}
}

.p6 {
	display: flex;
	flex-direction: column;
	.right {
		#chart7 {
			width: 100%;
			height: 600px;
		}
	}
}

.p7 {
	display: flex;
	flex-direction: column;
	.right {
		.percent {
			display: flex;
			align-items: center;
			margin: 20px 0;

			div {
				width: 33%;
				height: 100px;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}

		.echars {
			display: flex;

			#chart8 {
				width: 500px;
				height: 400px;
			}

			.right {
			}
		}
	}
}

.p8 {
	display: flex;
	flex-direction: column;
	#chart9 {
		height: 300px;
		width: 100%;
	}

	.bg {
		height: 200px;
		margin-top: 5px;
		background-image: url('../../assets/images/bussiness/img3.jpg');
		background-size: cover;
	}
}

.p9 {
	display: flex;
	flex-direction: column;
	.strategy {
		display: flex;
		flex-direction: column;

		.item {
			margin: 20px 0;
			font-size: 14px;
			display: flex;
			.icon {
				font-size: 40px;
				color: #8bc1b4;
				margin-right: 14px;
				display: flex;
				align-items: center;
			}
			.tr {
				padding: 10px 7px;
				background-color: #f1f1f1;
			}
			.tt {
				margin-right: 10px;
				color: #81bcae;
			}
		}
	}
}

.p10 {
	display: flex;
	flex-direction: column;
	.flex {
		display: flex;
		flex-wrap: wrap;

		.item {
			flex-basis: 33%;
			font-size: 13px;
			display: flex;
			align-items: flex-end;
			border-bottom: 1px solid #dddddd;
			padding: 30px 0;
			text-align: left;
			.percent {
				font-size: 40px;
				// width: 85px;
				text-align: left;
			}
			.title {
				padding-bottom: 5px;
				font-weight: 600;
			}
		}
	}
}
.chart-desc {
	margin-top: 5px;
	font-size: 12px;
	text-align: center;
}
</style>
