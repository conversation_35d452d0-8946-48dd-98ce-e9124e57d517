<template>
	<div class="mian">
		<el-row :gutter="20" v-show="type === '1'">
			<el-col :span="12" v-if="general.IT"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					租户普查>>
					<el-divider class="divider" />
				</el-text>
				<el-text class="mx-1" tag="p" style="margin-bottom: 10px"
					>{{ Other.rankingName[0] }}、{{ Other.rankingName[1] }}、{{ Other.rankingName[2] }}行业租户占比前三位。
				</el-text>
				<el-text class="mx-1" tag="p" style="margin-bottom: 10px"
					>{{ Other.mapsInfo.name }}{{ Other.mapsInfo.de_type }}目前有{{ Other.mapsInfo.currentBuildingCount }}户，其中，{{
						Other.rankingName[0]
					}}、{{ Other.rankingName[1] }}、{{ Other.rankingName[2] }}行业是租户前三大主力、分别有{{ Other.rankingCount[0] }}户、{{
						Other.rankingCount[1]
					}}户、{{ Other.rankingCount[2] }}户，合计占比{{
						Other.percentageCount
					}}%。从二级行业看，金融的保险、工业与商业服务的其他商业服务、工业与商业服务的咨询服务占比分别为{{ scaling[0]?.retailAndTradeCount }}%，{{
						scaling[1]?.businessServicesCount
					}}%，{{ scaling[2]?.realEstateCount }}%。</el-text
				>
				<el-text class="mx-1" tag="p" style="margin-bottom: 10px"
					>根据资金来源，外资企业有{{ Other.enterpriseAll.foreignCapitalAllCount }}户，占比{{
						Other.enterpriseAll.foreignCapitalAllPercentageCount
					}}%；根据注册地址，{{ Other.enterpriseAll.localCityName }}企业有{{ Other.enterpriseAll.localRegEnterpriseCount }}户，占比{{
						Other.enterpriseAll.localRegEnterprisePercentageCount
					}}； 租户所属行业均属于第三产业，主要依靠人力成本和资本性支出，产业附加值高。符合{{ Other.enterpriseAll.localCityName
					}}{{ Other.rankingName[0] }}、{{ Other.rankingName[1] }}、{{ Other.rankingName[2] }}的定位。</el-text
				>
				<el-text tag="p" style="padding: 16px 0 0 0" class="Title">
					租户构成>>
					<el-divider class="divider" />
				</el-text>
				<div class="list">
					<el-text class="mx-1 ali" tag="p">信息技术</el-text>
					<div class="rightList">
						<div style="display: flex; padding-top: 8px" v-for="(item, index) in general.IT" :key="item" v-show="index !== 'total'">
							<el-text class="mx-1 title" tag="span" v-if="index === 'ITECommerceCount'">互联网电商</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'technologyInternetCount'">科技互联网</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'hardwareDevelopmentCount'">硬件研发</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'c3ProductCount'">3C电子产品</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'unknownITCount'">未知（其他）</el-text>
							<el-progress color="#f56c6c" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
						</div>
					</div>
					<el-text class="mx-1 ali" tag="p"> {{ general.IT.total }}%</el-text>
				</div>
				<div class="list">
					<el-text class="mx-1 ali" tag="p">金融</el-text>
					<div class="rightList">
						<div style="display: flex; padding-top: 8px" v-for="(item, index) in general.finance" :key="item" v-show="index !== 'total'">
							<el-text class="mx-1 title" tag="span" v-if="index === 'financeInsuranceCount'">保险经济</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'securityFundCount'">证券/期货</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'bankCount'">银行</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'nonTraditionalCount'">非传统金融</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherCount'">其他金融</el-text>

							<el-progress color="#67C23A" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
						</div>
					</div>
					<el-text class="mx-1 ali" tag="p"> {{ general.finance.total }}%</el-text>
				</div>
				<div class="list">
					<el-text class="mx-1 ali" tag="p">工业与商业服务</el-text>
					<div class="rightList">
						<div style="display: flex; padding-top: 8px" v-for="(item, index) in general.businessServices" :key="item" v-show="index !== 'total'">
							<el-text class="mx-1 title" tag="span" v-if="index === 'businessServicesCount'">商服务</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'consultingServiceCount'">咨询服务</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherConsultationsCount'">其他咨询</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'designServicesCount'">设计服务</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'integratedManagementCount'">综合管理</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'communicationServicesCount'">通讯服务</el-text>

							<el-progress color="#E6A23C" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
						</div>
					</div>
					<el-text class="mx-1 ali" tag="p"> {{ general.businessServices.total }}%</el-text>
				</div>
				<div class="list">
					<el-text class="mx-1 ali" tag="p">消费</el-text>
					<div class="rightList">
						<div style="display: flex; padding-top: 8px" v-for="(item, index) in general.consumption" :key="item" v-show="index !== 'total'">
							<el-text class="mx-1 title" tag="span" v-if="index === 'consumerGoodsManufactureCount'">消费品制造</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherConsumerServicesCount'">其他消费服务</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'educationServicesCount'">教育服务</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'retailAndTradeCount'">零售与贸易</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'nursingCount'">医美个人护理</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'hotelsCateringCount'">酒店与餐饮</el-text>

							<el-progress color="#E74C3C" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
						</div>
					</div>
					<el-text class="mx-1 ali" tag="p"> {{ general.consumption.total }}%</el-text>
				</div>
				<div class="list">
					<el-text class="mx-1 ali" tag="p">其他</el-text>
					<div class="rightList">
						<div style="display: flex; padding-top: 8px" v-for="(item, index) in general.other" :key="item" v-show="index !== 'total'">
							<el-text class="mx-1 title" tag="span" v-if="index === 'medicineCount'">医药生物</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'rawMaterialCount'">原材料</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'energyCount'">能源</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'realEstateCount'">房地产</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'publicUtilityCount'">公用事业</el-text>
							<el-text class="mx-1 title" tag="span" v-else-if="index === 'unknownCount'">其它</el-text>
							<el-progress color="#5B2C6F" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
						</div>
					</div>
					<el-text class="mx-1 ali" tag="p"> {{ general.other.total }}%</el-text>
				</div>
			</el-col>

			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					需求势能>>
					<el-divider class="divider" />
				</el-text>
				<el-text class="tit" type="danger" tag="p">{{ requirementTextV }} </el-text>
				<el-text tag="p" class="TitleTwo"> 租赁占比 </el-text>
				<el-text tag="p" class="mx-1" style="margin-top: 12px"> 2023年该地区对办公区域需求的行业分布 </el-text>
				<div class="list" v-for="(item, index) in right" :key="item" v-show="index !== 'total'">
					<el-text class="mx-1 title" tag="span" v-if="index === 'financeAddCount'">金融</el-text>
					<el-text class="mx-1 title" tag="span" v-else-if="index === 'ITAddCount'">信息技术</el-text>
					<el-text class="mx-1 title" tag="span" v-else-if="index === 'realEstateAddCount'">房地产</el-text>
					<el-text class="mx-1 title" tag="span" v-else-if="index === 'consumptionAddCount'">消费</el-text>
					<el-text class="mx-1 title" tag="span" v-else-if="index === 'serviceAddCount'">工业与商业服务</el-text>
					<el-text class="mx-1 title" tag="span" v-else-if="index === 'energyPublicAddCount'">能源及公共事业</el-text>
					<el-text class="mx-1 title" tag="span" v-else-if="index === 'medicineAddCount'">医药</el-text>
					<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherAddCount'">原材料及通讯服务（其他）</el-text>
					<el-progress color="#2ECC71" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
				</div>
				<el-text tag="p" class="TitleTwo" style="margin-top: 20px"> 扩张意愿 </el-text>
				<div v-show="show">
					<el-text class="mx-1" tag="p" style="padding: 15px 0; font-size: 14px">2023年下半年预期办公室面积增加内外资占比</el-text>
					<div style="height: 200px; width: 100%; display: flex; padding: 10px 0">
						<el-card class="box-card" v-for="(value, key) in Other.anticipatePercentage">
							<el-text class="title mx-1" tag="p" style="font-size: 45px; width: 100%; padding: 10px 0"> {{ value }}%</el-text>
							<el-text class="title mx-1" tag="span" v-if="key === 'domesticPercentageAnticipate'">内资企业</el-text>
							<el-text class="title mx-1" tag="span" v-else-if="key === 'foreignCapitalPercentageAnticipate'">外资企业</el-text>
							<el-text class="title mx-1" tag="span" v-else-if="key === 'localPercentageAnticipate'">本地企业</el-text>
						</el-card>
					</div>
				</div>
				<el-text class="mx-1" tag="p" style="padding: 15px 0; font-size: 14px" v-if="Other.enterpriseAll"
					>2023年下半年{{ Other.enterpriseAll.localCityName }}的{{ Other.mapsInfo.de_type }}本来源占比</el-text
				>
				<div id="myChart" style="height: 400px; width: 100%"></div>
				<el-text class="mx-1" tag="p" style="padding: 15px 0; font-size: 14px" v-if="Other.enterpriseAll">企业租赁决策的配置、服务偏好</el-text>
				<div class="contoin">
					<div v-for="(value, key) in preference" style="width: 30%; padding: 10px; border-bottom: 1px solid #f1f1f1">
						<el-text class="mx-1" type="warning" style="font-size: 45px; width: 100%; padding: 10px 0">{{ value }}%</el-text>
						<el-text class="mx-1" style="padding-left: 8px" v-if="key === 'public_intercity_traffic'">公共/城际交通</el-text>
						<el-text class="mx-1" style="padding-left: 8px" v-else-if="key === 'building_district_ancillary'">楼宇商圈配套设施</el-text>
						<el-text class="mx-1" style="padding-left: 8px" v-else-if="key === 'tenant_structure'">楼内租户结构</el-text>
						<el-text class="mx-1" style="padding-left: 8px" v-else-if="key === 'owners_reputation'">业主声誉</el-text>
						<el-text class="mx-1" style="padding-left: 8px" v-else-if="key === 'property_manage'">物业管理团队</el-text>
						<el-text class="mx-1" style="padding-left: 8px" v-else-if="key === 'single_title'">单一业权</el-text>
						<el-text class="mx-1" style="padding-left: 8px" v-else-if="key === 'Flexible_office_config'">灵活办公空间配置</el-text>
						<el-text class="mx-1" style="padding-left: 8px" v-else-if="key === 'shared_meeting_room'">楼内共享办公室</el-text>
						<el-text class="mx-1" tag="span" style="padding-left: 8px" v-else-if="key === 'Intelligent_building_manage'">智能楼宇管理系统</el-text>
					</div>
				</div>
			</el-col>
		</el-row>
		<el-row :gutter="20" v-show="type === '2'">
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title" style="margin-top: 20px">
					品牌分布>>
					<el-divider class="divider" />
				</el-text>
				<el-table border :data="resultArray" stripe style="width: 100%; margin-top: 20px">
					<el-table-column prop="floor" label="楼层" width="115" />
					<el-table-column prop="commercial" label="业态" width="280" />
					<el-table-column prop="merchant_name" label="商户名称" />
				</el-table>
				<el-row style="border: 1px solid #ebeef5; box-sizing: border-box; color: #606266">
					<el-col :span="3" style="border-right: 1px solid #ebeef5; padding: 0 10px; box-sizing: border-box">出租率</el-col>
					<el-col :span="21" style="text-aline: center">{{ occupancy }}</el-col>
				</el-row>
			</el-col>
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title" style="margin-top: 20px">
					品牌分析>>
					<el-divider class="divider" />
				</el-text>
				<el-table :data="analyse" stripe style="width: 100%">
					<el-table-column v-for="column in cloums" :key="column" :prop="column" :label="title[column]" />
				</el-table>
				<el-text class="mx-1 titleTwo" size="large" tag="p" style="font-weight: 700; padding-top: 20px">经营分析</el-text>
				<el-row style="border: 1px dashed #606266; min-height: 60px; margin-bottom: 0 !important; margin-top: 15px">
					<el-col
						:span="4"
						style="border-right: 1px dashed #606266; box-sizing: border-box; text-align: center; display: flex; justify-content: center"
					>
						<el-text style="line-height: 100%">品牌定位</el-text>
					</el-col>
					<el-col :span="20" style="display: flex; justify-content: center">
						<el-text style="padding-left: 15px !important; display: block">{{ businessAnalysis.brand_positioning }}</el-text>
					</el-col>
				</el-row>
				<el-row style="border: 1px dashed #606266; min-height: 60px; margin-bottom: 0 !important">
					<el-col
						:span="4"
						style="border-right: 1px dashed #606266; box-sizing: border-box; text-align: center; display: flex; justify-content: center"
					>
						<el-text style="line-height: 100%">客群定位</el-text>
					</el-col>
					<el-col :span="20" style="display: flex; justify-content: center">
						<el-text style="padding-left: 15px !important; display: block">{{ businessAnalysis.customer_name }}</el-text>
					</el-col>
				</el-row>
				<el-row style="border: 1px dashed #606266; min-height: 60px; margin-bottom: 0 !important">
					<el-col
						:span="4"
						style="border-right: 1px dashed #606266; box-sizing: border-box; text-align: center; display: flex; justify-content: center"
					>
						<el-text style="line-height: 100%">业态组合</el-text>
					</el-col>
					<el-col :span="20" style="display: flex; justify-content: center">
						<el-text style="padding-left: 15px !important; display: block">{{ businessAnalysis.business_mix }}</el-text>
					</el-col>
				</el-row>
				<el-row style="margin-top: 10px">
					<el-col :span="12"></el-col>
					<el-col :span="12">
						<el-button type="primary" v-for="item in buttonList" :key="item" @click="Brandanalysis(item)">{{ item }}</el-button>
					</el-col>
				</el-row>
				<div style="display: flex">
					<div id="ChartBuyLeft" style="height: 400px; width: 50%"></div>

					<div id="ChartBuyRight" style="height: 400px; width: 50%"></div>
				</div>
			</el-col>
		</el-row>

		<el-row :gutter="20" v-show="type === '3'">
			<el-col :span="12">
				<div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					顾客满意度>>
					<el-divider class="divider" />
				</el-text>
				<el-rate size="large" v-model="rate" allow-half score-template="{value}" show-score />
				<div id="myChartHotel" style="height: 400px; width: 100%"></div>
				<el-text tag="p" class="Title">
					优惠政策>>
					<el-divider class="divider" />
				</el-text>
				<el-text tag="p" class="mx-1 linhei">
					对于建筑物的物理情况和服务情况，我们建立了全新和科学的打分体系，通过二十项打分，交叉分析，合理赋权。形成准确的物业管理情况评价体系。经评测，{{
						hotelData.hotel_name
					}}的消费活跃度为{{ hotelData.population_activity }}。
				</el-text>
				<!-- <el-text tag="p" class="mx-1 linhei"> 优惠政策 </el-text> -->
				<el-text tag="p" class="mx-1 linhei"> 原价:{{ hotelData.original_price }} </el-text>
				<el-text tag="p" class="mx-1 linhei"> 促销:{{ hotelData.promotion_method }}（具体价格以门店为准） </el-text>
				<el-text tag="p" class="mx-1 linhei"> highrate:{{ hotelData.high_rate }} </el-text>
			</el-col>
			<el-col :span="12">
				<div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					内部设施>>
					<el-divider class="divider" />
				</el-text>
				<div style="display: flex; margin-bottom: 20px">
					<el-text class="mx-1 lin" style="padding: 0 30px 0 0px" tag="p">位置</el-text>
					<el-text class="mx-1 lin" style="padding: 0 30px 0 0px" tag="p">{{ hotelData.location_mark }}</el-text>
					<el-progress :percentage="(hotelData.location_mark / 5) * 100" :stroke-width="10" style="width: 35%" :show-text="false" />

					<el-text class="mx-1 lin" style="padding: 0 30px 0 20px" tag="p">设施</el-text>
					<el-text class="mx-1 lin" style="padding: 0 30px 0 0px" tag="p">{{ hotelData.facility_mark }}</el-text>
					<el-progress :percentage="(hotelData.facility_mark / 5) * 100" :stroke-width="10" style="width: 35%" :show-text="false" />
				</div>
				<div style="display: flex; margin-bottom: 20px">
					<el-text class="mx-1 lin" style="padding: 0 30px 0 0px" tag="p">卫生</el-text>
					<el-text class="mx-1 lin" style="padding: 0 30px 0 0px" tag="p">{{ hotelData.facility_mark }}</el-text>
					<el-progress :percentage="(hotelData.facility_mark / 5) * 100" :stroke-width="10" style="width: 35%" :show-text="false" />

					<el-text class="mx-1 lin" style="padding: 0 30px 0 20px" tag="p">服务</el-text>
					<el-text class="mx-1 lin" style="padding: 0 30px 0 0px" tag="p">{{ hotelData.service_mark }}</el-text>
					<el-progress :percentage="(hotelData.service_mark / 5) * 100" :stroke-width="10" style="width: 35%" :show-text="false" />
				</div>

				<!-- <el-text class="mx-1 lin" style="padding: 0 30px 0 20px">设施</el-text>
				<el-progress type="circle" :percentage="((hotelData.facility_mark / 5) * 100).toFixed(2)" />
				<el-text class="mx-1 lin" style="padding: 0 30px 0 20px">卫生</el-text>
				<el-progress type="circle" :percentage="((hotelData.hygiene_mark / 5) * 100).toFixed(2)" />
				<el-text class="mx-1 lin" style="padding: 0 30px 0 20px">服务</el-text>
				<el-progress type="circle" :percentage="((hotelData.service_mark / 5) * 100).toFixed(2)" /> -->

				<el-text tag="p" class="Title">
					配套设施>>
					<el-divider class="divider" />
				</el-text>
				<el-descriptions column="1">
					<el-descriptions-item label="前台服务">{{ hotelData.front_desk_service }}</el-descriptions-item>
					<el-descriptions-item label="餐饮服务">{{ hotelData.catering_service }}</el-descriptions-item>
					<el-descriptions-item label="休闲娱乐">{{ hotelData.recreation }}</el-descriptions-item>
					<el-descriptions-item label="通用设施">{{ hotelData.universal_facility }}</el-descriptions-item>
					<el-descriptions-item label="支持酒店设施">{{ hotelData.hotel_facilities }}</el-descriptions-item>
				</el-descriptions>
			</el-col>
		</el-row>
		<div v-show="!type" style="display: flex; justify-content: center; align-items: center; height: 500px">
			<el-text style="font-size: 20px" tag="p">暂无数据......</el-text>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue';
import http from '@/utils/http';
import { useRoute } from 'vue-router';
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
const route = useRoute();
const general = ref({});
const right = ref({});
const quarterOne = ref([]);
const quarterTwo = ref([]);
const quarterThree = ref([]);
const quarterFour = ref([]);
const requirementTextV = ref('');
const date = ref([]);
const Other = ref({});
const preference = ref({});
const type = ref('');
const brand = ref([]);
const businessAnalysis = ref({});
const occupancy = ref('');
const analyse = ref([]);
const title = ref({});
const scaling = ref([]);
const rate = ref(3.8);
const hotelData = ref({});
let myChart;
let myChartHotel;
let ChartBuyLeft;
let ChartBuyRight;
const buttonList = ref([]);
const floorData = ref([]);
const Brandanalysis = (val) => {
	const index = buttonList.value.indexOf(val); // 获取按钮在 buttonList 中的索引
	if (index > -1) {
		// 确保索引有效
		ChartBuyRight.setOption({
			title: {
				text: val + '层',
			},
			series: [
				{
					data: floorData.value[index], // 使用对应索引的楼层数据
				},
			],
		});
	} else {
		console.error('按钮值无效');
	}
};

const cloums = computed(() => {
	return ['work', ...Object.keys(title.value).filter((column) => column !== 'work')];
});
const resultArray = ref([]);
const totalData = ref([]);
const show = ref('');
onMounted(async () => {
	try {
		const res = await http.get('/api/a_maps.do?act=ratingPassDetailsAction', {
			params: { maps_id: route.params.id, action: 'commerce' },
		});
		type.value = res.de_type_code;
		if (res.de_type_code === '1') {
			if (res.list && res.list.length > 0 && res) {
				Other.value = res.list[3];
				show.value = res.list.anticipatePercentage?.domesticPercentageAnticipate;
				requirementTextV.value = res.list[3].requirementText;
				general.value = res.list[0];
				right.value = res.list[1].demand;
				date.value = res.list[2][3];
				preference.value = res.list[4];
				scaling.value = res.list[3].tenantFinanceMaxCount;

				res.list[2].forEach((item) => {
					quarterOne.value.push(item['2023-Q1']);
					quarterTwo.value.push(item['2023-Q2']);
					quarterThree.value.push(item['2023-Q3']);
					quarterFour.value.push(item['2023-Q4']);
				});
			} else {
				console.error('kkkkk');
			}
		} else if (res.de_type_code === '2') {
			if (res.list && res.list.length > 0) {
				brand.value = res.list[0];
				businessAnalysis.value = res.list[2];
				occupancy.value = res.list[1].letting_rate;
				analyse.value = res.list[3];
				analyse.value[2].work = '比例';
				analyse.value[1].work = '数量';
				Object.keys(res.list[3][0]).forEach((item, index) => {
					totalData.value.push({
						name: res.list[3][0][item],
						value: res.list[3][1][item],
					});
				});
				console.log(totalData.value, 'totalData');
				const groupedData = {};
				res.list[0].forEach((item) => {
					const floor = item.floor;

					// 如果当前 floor 不存在于 groupedData 中，则创建一个新的数组
					if (!groupedData[floor]) {
						groupedData[floor] = [];
					}

					// 将商户信息添加到对应 floor 的数组中
					groupedData[floor].push({
						value: item.merchant_total,
						name: item.commercial,
					});
				});

				for (const floor in groupedData) {
					if (groupedData.hasOwnProperty(floor)) {
						console.log(`Floor: ${floor}`);
						buttonList.value.push(floor);
					}
				}
				buttonList.value.forEach((item) => {
					floorData.value.push(groupedData[item]);
				});
				console.log(floorData.value, buttonList.value, 'floorData');
				title.value = res.list[3][0];
				title.value.work = '业态';
				title.value.total = '总数';
				const seenFloors = new Set();
				for (const entry of brand.value) {
					const floor = entry['floor'];
					if (!seenFloors.has(floor)) {
						resultArray.value.push({ commercial: entry['commercial'], merchant_name: entry['merchant_name'], floor: floor });
						seenFloors.add(floor);
					} else {
						resultArray.value.push({ commercial: entry['commercial'], merchant_name: entry['merchant_name'] });
					}
				}

				analyse.value.shift();
			}
		} else if (res.de_type_code === '3') {
			rate.value = res.list[0].satisfaction;
			hotelData.value = res.list[0];
		} else {
			ElMessage.warning('数据待上传！！！');
		}
	} catch (error) {
		console.error('Failed to fetch data:', error);
	}
	await nextTick();
	if (type.value === '1') {
		myChart = echarts.init(document.getElementById('myChart'));
		// 设置图表选项
		myChart.setOption({
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'shadow',
				},
			},
			legend: {},
			grid: {
				left: '3%',
				right: '4%',
				bottom: '3%',
				containLabel: true,
			},
			xAxis: [
				{
					type: 'category',
					data: ['内资企业', '外资企业', '本地企业'],
				},
			],
			yAxis: [
				{
					type: 'value',
				},
			],
			series: [
				{
					name: date.value[0].year + '年' + date.value[0].quarter + '季度',
					type: 'bar',
					emphasis: {
						focus: 'series',
					},
					data: quarterOne.value,
				},
				{
					name: date.value[1].year + '年' + date.value[1].quarter + '季度',
					type: 'bar',
					emphasis: {
						focus: 'series',
					},
					data: quarterTwo.value,
				},
				{
					name: date.value[2].year + '年' + date.value[2].quarter + '季度',
					type: 'bar',
					emphasis: {
						focus: 'series',
					},
					data: quarterThree.value,
				},
				{
					name: date.value[3].year + '年' + date.value[3].quarter + '季度',
					type: 'bar',
					emphasis: {
						focus: 'series',
					},
					data: quarterFour.value,
				},
			],
		});
	}
	if (type.value === '2') {
		ChartBuyLeft = echarts.init(document.getElementById('ChartBuyLeft'));
		console.log(ChartBuyLeft, '4444444');
		ChartBuyRight = echarts.init(document.getElementById('ChartBuyRight')); // 这里修改了变量名
		ChartBuyLeft.setOption({
			title: {
				text: '品牌分析总览',
				subtext: '所有楼层品牌分析',
				left: 'center',
			},
			tooltip: {
				trigger: 'item',
			},
			legend: {
				orient: 'vertical',
				left: 'left',
			},
			series: [
				{
					name: 'Access From',
					type: 'pie',
					radius: '50%',
					data: totalData.value,
					emphasis: {
						itemStyle: {
							shadowBlur: 10,
							shadowOffsetX: 0,
							shadowColor: 'rgba(0, 0, 0, 0.5)',
						},
					},
				},
			],
		});
		console.log(buttonList.value[0], '555555555555');

		ChartBuyRight.setOption({
			title: {
				text: buttonList.value[0] + '层',
				subtext: '对应每一层的品牌分析',
				left: 'center',
			},
			tooltip: {
				trigger: 'item',
			},
			legend: {
				orient: 'vertical',
				left: 'left',
			},
			series: [
				{
					name: '品牌分析',
					type: 'pie',
					radius: '50%',
					data: floorData.value[0],
					emphasis: {
						itemStyle: {
							shadowBlur: 10,
							shadowOffsetX: 0,
							shadowColor: 'rgba(0, 0, 0, 0.5)',
						},
					},
				},
			],
		});
	}
	if (type.value === '3') {
		myChartHotel = echarts.init(document.getElementById('myChartHotel'));
		// 设置图表选项
		myChartHotel.setOption({
			title: {
				text: hotelData.value.bottom_evaluation,
				left: 'center',
				bottom: '10%',
			},
			tooltip: {
				trigger: 'item',
			},
			legend: {
				orient: 'vertical',
				left: 'left',
			},
			series: [
				{
					name: '酒店优势',
					type: 'pie',
					radius: '50%',
					data: hotelData.value.evaluation,
					emphasis: {
						itemStyle: {
							shadowBlur: 10,
							shadowOffsetX: 0,
							shadowColor: 'rgba(0, 0, 0, 0.5)',
						},
					},
				},
			],
		});
	}
	window.addEventListener('resize', () => {
		myChart.resize();
		myChartHotel.resize();
	});
});

// 在组件销毁前销毁 ECharts 实例
onUnmounted(() => {
	if (myChart) {
		myChart.dispose();
	}
	if (myChartHotel) {
		myChartHotel.dispose();
	}
});
</script>

<style lang="less" scoped>
.linhei {
	line-height: 1.5;
}
.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
}
.TitleTwo {
	font-size: 14px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
}
.divider {
	flex-grow: 1;
	margin: 16px 0 16px 8px;
	border-color: #3483ce;
}
.titleTwo {
	color: #000;
	border-bottom: 1px solid #f1f1f1;
	padding-bottom: 10px;
}
.mian {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	padding-left: 20px;
	.contoin {
		display: flex;
		flex-wrap: wrap;
	}
	.box-card {
		width: 30%;
		margin-left: 10px;
	}
	.tit {
		line-height: 1.5;
		padding-top: 8px;
	}
	.ali {
		width: 20%;
	}
	.el-row {
		margin-bottom: 20px;
		&:last-child {
			margin-bottom: 0;
		}
	}

	.el-col {
		border-radius: 4px;

		.grid-content {
			border-radius: 4px;
			min-height: 26px;
		}
	}

	.list {
		display: flex;
		justify-content: space-between;
		padding-top: 15px;
		.rightList {
			width: 50%;
		}
	}

	.title {
		width: 20%;
	}
}
</style>
