<template>
	<div class="comparison_box">
		<el-button class="chose_box" type="primary" @click="choose()">选择对比资产</el-button>
		<div class="tableBox">
			<el-table class="table_box" :data="tableDatao" style="width: 550px">
				<el-table-column prop="selPkAssetsVo.name" label="资产名称" width="180" />
				<el-table-column prop="selPkAssetsVo.deType" label="资产类型" width="180" />
				<el-table-column prop="selPkAssetsVo.address" label="地址" width="180" />
			</el-table>
			<el-table class="table_box" :data="tableDatat" style="width: 550px">
				<el-table-column prop="selPkAssetsVo.name" label="资产名称" width="180" />
				<el-table-column prop="selPkAssetsVo.deType" label="资产类型" width="180" />
				<el-table-column prop="selPkAssetsVo.address" label="地址" width="180" />
			</el-table>
		</div>

		<div class="comparison">
			<Valuecomparison :valueDateList="valueDateList"></Valuecomparison>
			<echartBox :sixRingDateList="sixRingDateList" ref="echartBox_ref"></echartBox>
		</div>
	</div>
	<!-- 对话框 -->
	<el-dialog v-model="dialogTableVisible" title="对比资产">
		<el-row :gutter="20">
			<el-col :span="4">
				<el-cascader size="large" placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }">
				</el-cascader>
			</el-col>
			<el-col :span="4">
				<el-select style="margin-left: 20px" v-model="rateValue" placeholder="全部资产评级" size="large">
					<el-option v-for="(item, value) in rate" :key="value" :label="item" :value="item" />
				</el-select>
			</el-col>
			<el-col :span="4">
				<el-select style="margin-left: 20px" v-model="buildingTypesValue" placeholder="全部资产" size="large">
					<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" /> </el-select
			></el-col>
			<el-col :span="4"> <el-input v-model="essential" placeholder="请输入关键字" size="large"></el-input></el-col>
			<el-col :span="6">
				<el-button type="primary" @click="Compared()">查询</el-button>
				<el-button type="primary">重置</el-button></el-col
			>
		</el-row>
		<el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange" stripe>
			<el-table-column type="selection" width="55" ref="multipleTableRef" />
			<el-table-column
				v-for="(column, index) in tableColumns"
				:key="index"
				:label="column.label"
				:prop="column.prop"
				:width="column.width"
				:show-overflow-tooltip="column.showOverflowTooltip"
			/>
		</el-table>
		<el-pagination
			@current-change="handleCurrentChange"
			:current-page="currentPage"
			small
			background
			layout="prev, pager, next"
			class="mt-4"
			:total="total"
		/>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogTableVisible = false">取消</el-button>
				<el-button type="primary" @click="save()"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { valueList } from '@/api/materials';
import Valuecomparison from './Valuecomparison.vue';
// import sixRing from './sixRing.vue'
import echartBox from './echarts_box.vue';
const dialogTableVisible = ref(false); //对话框显示
const rateValue = ref('');
const buildingTypesValue = ref('');
const rate = ['S', 'A+', 'A', 'B+', 'B', 'C'];
const buildingTypes = ['写字楼', '零售', '产业园区', '仓储物流', '酒店', '长租公寓', '医疗', '综合市场'];
const province = ref('');
const city = ref('');
const essential = ref('');
const currentPage = ref(1);
const total = ref(0);
const multipleTableRef = ref(null);
const tableData = ref([]);
const multipleSelection = ref([]);
const echartBox_ref = ref(null);
const tableColumns = [
	{
		label: '资产名称',
		prop: 'selPkAssetsVo.name',
		width: '300',
	},
	{
		label: '资产类型',
		prop: 'selPkAssetsVo.deType',
		showOverflowTooltip: true,
	},
	{
		label: '地址',
		prop: 'selPkAssetsVo.address',
		width: '300',
	},
];
const tableData2 = ref([]);
const tableDatao = ref([]);
const tableDatat = ref([]);

const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};
// 点击选择对比资产
const choose = () => {
	// multipleTableRef.value.clearSelection();
	dialogTableVisible.value = true;
	// 清空上一次选的
	multipleSelection.value = [];
};
// 修改城市
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const queryParams = computed(() => {
	return {
		city: province.value,
		degree: rateValue.value,
		county: city.value,
		deType: buildingTypesValue.value,
		search: essential.value,
		currentPage: currentPage.value,
		year: 2024,
	};
});
//对比资产分页查询
const handleCurrentChange = (val) => {
	currentPage.value = val;
	Compared();
};
// 查询
const Compared = async () => {
	console.log(buildingTypesValue.value, 'buildingTypesValue1');
	console.log(queryParams.value.deType, 'queryParams.value1');
	if (buildingTypesValue.value == '零售') {
		queryParams.value.deType = '购物中心,百货大楼';
		console.log(1);
	}
	if (buildingTypesValue.value == '医疗') {
		queryParams.value.deType = '医疗康养';
	}
	if (buildingTypesValue.value == '长租公寓') {
		queryParams.value.deType = '公寓';
	}
	await valueList(queryParams.value)
		.then((res) => {
			console.log(res);
			tableData.value = res.data.records;
			console.log(tableData.value, 'tableData');
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
// 确定
const valueDateList = ref([]);
const sixRingDateList = ref([]);

const save = () => {
	console.log(multipleSelection.value, 'multipleSelection');
	if (multipleSelection.value.length != 2) {
		ElMessage({
			message: '请选择两个资产',
			type: 'error',
		});
	} else {
		valueDateList.value = multipleSelection.value.map((item) => item.selPkAssetsVo);
		sixRingDateList.value = multipleSelection.value.map((item) => item.sixAngleVo);
		echartBox_ref.value.echartsData(sixRingDateList.value);
		console.log(valueDateList.value, 'valueDateList');
		console.log(sixRingDateList.value, 'sixRingDateList');
		tableData2.value = multipleSelection.value;
		// 两个数组 一个数组里面放一个
		tableDatao.value.push(tableData2.value[0]);
		tableDatat.value.push(tableData2.value[1]);

		console.log(tableDatao.value, 36522);

		dialogTableVisible.value = false;
	}
};
</script>
<style scoped lang="less">
.comparison_box {
	width: 100%;
	padding-top: 20px;

	.comparison {
		display: flex;
		width: 100%;
		justify-content: space-between;
		align-items: center;
		margin-top: 20px;

		div {
			width: 50%;
		}
	}

	.chose_box {
		margin-top: 20px;
		margin-left: 44px;
	}
	.tableBox {
		display: flex;
		// justify-content: ;
	}

	.table_box {
		margin-top: 20px;
		margin-left: 44px;
		margin-right: 150px;
	}
}
</style>
