<template>
	<div class="lingshou_wrap">
		<template v-if="allData && allData.length > 1">
			<div class="double_box_wrap" v-for="(item, index) in allData">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">品牌分布</div>
					</div>
					<div class="content_wrap">
						<div class="floor_item" v-for="val in item.floorDistribution">
							<div class="floor_wrap">{{ val.floor }}</div>
							<div class="detail_wrap">
								<div class="item_wrap" v-for="(item1, index1) in val.floorDetailsList">
									<div class="type">{{ item1.commercialForm }}</div>
									<div class="detail">{{ item1.merchantName }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">品牌分析</div>
					</div>
					<div class="content_wrap_two">
						<div class="table_wrap">
							<arco-table
								row-key="id"
								:columns="columns1"
								:data="item.floorAnalysis.retailFloorBrandAnalysisTotal"
								:pagination="false"
								:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
							>
							</arco-table>
						</div>
						<div class="pie_wrap">
							<div class="title">品牌分析数据总览</div>
							<div class="floor_tabs">
								<div
									v-if="index == 0"
									@click="handleFloorTabChange(index, floor)"
									class="tab"
									:class="{ tab_active: floorLeftTabActiveKey == floor.floor }"
									v-for="floor in item.floorAnalysis.retailFloorBrandAnalysis"
									:key="floor.floor"
								>
									{{ floor.floor }}
								</div>
								<div
									v-if="index == 1"
									@click="handleFloorTabChange(index, floor)"
									class="tab"
									:class="{ tab_active: floorRightTabActiveKey == floor.floor }"
									v-for="floor in item.floorAnalysis.retailFloorBrandAnalysis"
									:key="floor.floor"
								>
									{{ floor.floor }}
								</div>
							</div>
							<div class="pie_box">
								<echartPie v-if="index == 0" ref="leftPieRef_ls" :pieData="left_pie_data" :otherOpts="pieOpts"></echartPie>
								<echartPie v-if="index == 1" ref="rightPieRef_ls" :pieData="right_pie_data" :otherOpts="pieOpts"></echartPie>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">经营分析</div>
					</div>
					<div class="content_wrap_two">
						<div class="table_wrap">
							<arco-table
								row-key="id"
								:columns="columns2"
								:data="index == 0 ? jyfxLeftTableData : jyfxrightTableData"
								:pagination="false"
								:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
							>
							</arco-table>
						</div>
						<div class="desc_wrap">
							<div class="desc_item">
								<div class="icon">
									<img :src="lingshou_icon1" />
								</div>
								<div class="desc">
									<div class="title">品牌定位</div>
									<div class="content">
										{{ item.businessAnalysis.brandPositioning }}
									</div>
								</div>
							</div>
							<div class="desc_item">
								<div class="icon">
									<img :src="lingshou_icon2" />
								</div>
								<div class="desc">
									<div class="title">客群定位</div>
									<div class="content">
										{{ item.businessAnalysis.customerPositioning }}
									</div>
								</div>
							</div>
							<div class="desc_item">
								<div class="icon">
									<img :src="lingshou_icon3" />
								</div>
								<div class="desc">
									<div class="title">业态组合</div>
									<div class="content">
										{{ item.businessAnalysis.businessMix }}
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</template>
		<template v-else>
			<div class="box_wrap" v-if="!$utils.isEmpty(leftData)">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">品牌分布</div>
						</div>
						<div class="content_wrap">
							<div class="floor_item" v-for="(item, index) in leftData.floorDistribution">
								<div class="floor_wrap">{{ item.floor }}</div>
								<div class="detail_wrap">
									<div class="item_wrap" v-for="(item1, index1) in item.floorDetailsList">
										<div class="type">{{ item1.commercialForm }}</div>
										<div class="detail">{{ item1.merchantName }}</div>
									</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">品牌分析</div>
						</div>
						<div class="content_wrap_two">
							<div class="table_wrap">
								<arco-table
									row-key="id"
									:columns="columns1"
									:data="leftData.floorAnalysis.retailFloorBrandAnalysisTotal"
									:pagination="false"
									:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
								>
								</arco-table>
							</div>
							<div class="pie_wrap">
								<div class="title">品牌分析数据总览</div>
								<div class="floor_tabs">
									<div
										@click="handleFloorTabChange(0, item)"
										class="tab"
										:class="{ tab_active: floorLeftTabActiveKey == item.floor }"
										v-for="item in leftData.floorAnalysis.retailFloorBrandAnalysis"
										:key="item.floor"
									>
										{{ item.floor }}
									</div>
								</div>
								<div class="pie_box">
									<echartPie ref="leftPieRef_ls" :pieData="left_pie_data" :otherOpts="pieOpts"></echartPie>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">经营分析</div>
						</div>
						<div class="content_wrap_two">
							<div class="table_wrap">
								<arco-table
									row-key="id"
									:columns="columns2"
									:data="jyfxLeftTableData"
									:pagination="false"
									:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
								>
								</arco-table>
							</div>
							<div class="desc_wrap">
								<div class="desc_item">
									<div class="icon">
										<img :src="lingshou_icon1" />
									</div>
									<div class="desc">
										<div class="title">品牌定位</div>
										<div class="content">
											{{ leftData.businessAnalysis.brandPositioning }}
										</div>
									</div>
								</div>
								<div class="desc_item">
									<div class="icon">
										<img :src="lingshou_icon2" />
									</div>
									<div class="desc">
										<div class="title">客群定位</div>
										<div class="content">
											{{ leftData.businessAnalysis.customerPositioning }}
										</div>
									</div>
								</div>
								<div class="desc_item">
									<div class="icon">
										<img :src="lingshou_icon3" />
									</div>
									<div class="desc">
										<div class="title">业态组合</div>
										<div class="content">
											{{ leftData.businessAnalysis.businessMix }}
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import lingshou_icon1 from '@/assets/images/shangYutong/buildInfo/lingshou_icon1.png';
import lingshou_icon2 from '@/assets/images/shangYutong/buildInfo/lingshou_icon2.png';
import lingshou_icon3 from '@/assets/images/shangYutong/buildInfo/lingshou_icon3.png';
import echartPie from '@/views/shangYutong/materials_new/components/echart/pie.vue';
import { getBuildLingshouInfo } from '@/api/syt.js';
import { nextTick, onMounted, getCurrentInstance, defineExpose } from 'vue';
const { proxy } = getCurrentInstance();
const props = defineProps({
	buildData: {
		type: Array,
		default: () => [],
	},
});
watch(
	() => props.buildData,
	(newVal) => {
		if (newVal.length > 0) {
			// getData();
		}
	},
	{
		deep: true,
	}
);
let pieOpts = ref({
	grid: {
		left: '0%', // 饼图靠左
		top: '10%', // 饼图顶部距离
		bottom: '10%', // 饼图底部距离
	},
	legend: {
		show: true, // 显示 legend
		orient: 'horizontal', // 图例排列方向，horizontal 为水平，vertical 为垂直
		bottom: '0%', // 图例靠右
		icon: 'circle', // 将图例小图标设置为圆形
		itemWidth: 8, // 更小的圆点
		itemHeight: 8, // 更小的圆点
		textStyle: {
			color: '#4E5969', // 文字颜色
			fontSize: 14, // 文字大小
		},
	},
});
const leftPieRef_ls = ref();
const rightPieRef_ls = ref();
const left_pie_data = ref(null);
const right_pie_data = ref(null);
const leftData = ref({});
const rightData = ref({});
const allData = ref({});
const floorLeftTabActiveKey = ref();
const floorRightTabActiveKey = ref();

const floorTabs = ref([
	{
		name: '全部',
		key: 'all',
	},
	{
		name: 'B2',
		key: 'B2',
	},
	{
		name: 'B1',
		key: 'B1',
	},
	{
		name: 'F1',
		key: 'F1',
	},
	{
		name: 'F2',
		key: 'F2',
	},
	{
		name: 'F3',
		key: 'F3',
	},
	{
		name: 'F4',
		key: 'F4',
	},
]);

const columns1 = [
	{
		title: '类型',
		dataIndex: 'commercialForm',
	},
	{
		title: '数量(个)',
		dataIndex: 'merchantNum',
	},
	{
		title: '比例(%)',
		dataIndex: 'merchantPercentage',
	},
];
const jyfxLeftTableData = ref([]);
const jyfxRightTableData = ref([]);
const columns2 = [
	{
		title: '租金',
		dataIndex: 'rental',
	},
	{
		title: '物业费',
		dataIndex: 'propertyFee',
	},
	{
		title: '出租率',
		dataIndex: 'occupancyRate',
	},
];

onMounted(() => {});
async function getData() {
	const res = await getBuildLingshouInfo({
		buildingIds: props.buildData.map((item) => item.id).join(),
	});
	allData.value = res.data;
	if (res.data && res.data.length == 1) {
		leftData.value = res.data[0];
		if (
			proxy.$utils.isEmpty(leftData.value.rental) &&
			proxy.$utils.isEmpty(leftData.value.propertyFee) &&
			proxy.$utils.isEmpty(leftData.value.occupancyRate)
		) {
			jyfxLeftTableData.value = [];
		} else {
			jyfxLeftTableData.value = [
				{
					rental: leftData.value.rental,
					propertyFee: leftData.value.propertyFee,
					occupancyRate: leftData.value.occupancyRate,
				},
			];
		}
		floorLeftTabActiveKey.value = res.data[0].floorAnalysis.retailFloorBrandAnalysis[0].floor;
		left_pie_data.value = {
			name: props.buildData[0].buildingName,
			data: res.data[0].floorAnalysis.retailFloorBrandAnalysis[0].floorDetailsList.map((val) => {
				return {
					name: val.commercialForm,
					value: val.merchantNum,
				};
			}),
		};
		nextTick(() => {
			leftPieRef_ls.value.init();
		});
	} else if (res.data && res.data.length == 2) {
		if (
			proxy.$utils.isEmpty(res.data[0].rental) &&
			proxy.$utils.isEmpty(res.data[0].propertyFee) &&
			proxy.$utils.isEmpty(res.data[0].occupancyRate)
		) {
			jyfxLeftTableData.value = [];
		} else {
			jyfxLeftTableData.value = [
				{
					rental: res.data[0].rental,
					propertyFee: res.data[0].propertyFee,
					occupancyRate: res.data[0].occupancyRate,
				},
			];
		}
		if (
			proxy.$utils.isEmpty(res.data[1].rental) &&
			proxy.$utils.isEmpty(res.data[1].propertyFee) &&
			proxy.$utils.isEmpty(res.data[1].occupancyRate)
		) {
			jyfxRightTableData.value = [];
		} else {
			jyfxRightTableData.value = [
				{
					rental: res.data[1].rental,
					propertyFee: res.data[1].propertyFee,
					occupancyRate: res.data[1].occupancyRate,
				},
			];
		}
		floorLeftTabActiveKey.value = res.data[0].floorAnalysis.retailFloorBrandAnalysis[0].floor || '全部';
		floorRightTabActiveKey.value = res.data[1].floorAnalysis.retailFloorBrandAnalysis[0].floor || '全部';
		left_pie_data.value = {
			name: props.buildData[0].buildingName,
			data: res.data[0].floorAnalysis.retailFloorBrandAnalysis[0].floorDetailsList.map((val) => {
				return {
					name: val.commercialForm,
					value: val.merchantNum,
				};
			}),
		};
		right_pie_data.value = {
			name: props.buildData[1].buildingName,
			data: res.data[1].floorAnalysis.retailFloorBrandAnalysis[1].floorDetailsList.map((val) => {
				return {
					name: val.commercialForm,
					value: val.merchantNum,
				};
			}),
		};
		nextTick(() => {
			if (leftPieRef_ls.value) {
				leftPieRef_ls.value.forEach((item) => {
					item.init();
				});
			}
			if (rightPieRef_ls.value) {
				rightPieRef_ls.value.forEach((item) => {
					item.init();
				});
			}
		});
	}
}
function handleFloorTabChange(type, item) {
	if (type == 0) {
		floorLeftTabActiveKey.value = item.floor;
		left_pie_data.value = {
			name: props.buildData[0].buildingName,
			data: item.floorDetailsList.map((val) => {
				return {
					name: val.commercialForm,
					value: val.merchantNum,
				};
			}),
		};
		nextTick(() => {
			if (allData.value.length == 2) {
				if (leftPieRef_ls.value) {
					leftPieRef_ls.value.forEach((item) => {
						item.init();
					});
				}
			} else {
				leftPieRef_ls.value.init();
			}
		});
	} else {
		floorRightTabActiveKey.value = item.floor;
		right_pie_data.value = {
			name: props.buildData[0].buildingName,
			data: item.floorDetailsList.map((val) => {
				return {
					name: val.commercialForm,
					value: val.merchantNum,
				};
			}),
		};
		nextTick(() => {
			if (allData.value.length == 2) {
				if (rightPieRef_ls.value) {
					rightPieRef_ls.value.forEach((item) => {
						item.init();
					});
				}
			} else {
				rightPieRef_ls.value.init();
			}
		});
	}
}
defineExpose({
	getData,
});
</script>
<style lang="less" scoped>
.lingshou_wrap {
	flex: 1;
	display: flex;
	gap: 16px;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
		.single_wrap {
			box-sizing: border-box;
			min-height: 300px;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.content_wrap,
			.content_wrap_two {
				padding: 20px 16px;
				display: flex;
				flex-direction: column;
				gap: 8px;
				.floor_item {
					display: flex;
					gap: 2px;
					.floor_wrap {
						width: 64px;
						display: flex;
						align-items: center;
						justify-content: center;
						background: #e8f3ff;
						color: #1868f1;
						font-size: 14px;
						font-weight: 500;
						line-height: 22px;
						border-radius: 4px 0 0 4px;
					}
					.detail_wrap {
						flex: 1;
						display: flex;
						flex-direction: column;
						gap: 2px;
						.item_wrap {
							display: flex;
							gap: 12px;
							font-size: 14px;
							line-height: 22px;
							padding: 9px 16px;
							background: #f7f8fa;
							border-radius: 0 4px 4px 0;
							.type {
								font-weight: 600;
								color: #1d2129;
							}
							.detail {
								flex: 1;
								color: #4e5969;
								font-weight: 400;
							}
						}
					}
				}

				.table_wrap {
					width: 100%;
				}
			}
			.content_wrap_two {
				flex-direction: column;
				.pie_wrap {
					margin-top: 40px;
					.title {
						font-size: 16px;
						font-weight: 600;
						line-height: 24px;
						color: #1d2129;
						margin-bottom: 20px;
						text-align: center;
					}
					.floor_tabs {
						display: flex;
						justify-content: center;
						flex-wrap: wrap;
						gap: 8px;
						max-width: 100%;
						.tab {
							width: 64px;
							height: 32px;
							display: flex;
							align-items: center;
							justify-content: center;
							border-radius: 100px;
							background: #f2f3f5;
							color: #86909c;
							font-size: 14px;
							font-weight: 400;
							cursor: pointer;
						}
						.tab_active {
							background: #e8f3ff;
							font-weight: 600;
							color: #1868f1;
						}
					}
				}
				.desc_wrap {
					margin-top: 16px;
					display: flex;
					flex-direction: column;
					gap: 8px;
					.desc_item {
						display: flex;
						background: #f7f8fa;
						padding: 20px;
						border-radius: 4px;
						gap: 12px;
						.icon {
							width: 32px;
							height: 32px;
							img {
								width: 100%;
								height: 100%;
							}
						}
						.desc {
							flex: 1;
							.title {
								font-size: 16px;
								font-weight: 600;
								line-height: 24px;
								color: #1d2129;
								margin-bottom: 8px;
							}
							.content {
								color: #4e5969;
								font-size: 14px;
								font-weight: 400;
								line-height: 22px;
							}
						}
					}
				}
			}

			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
	.double_box_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16px;
		.single_wrap {
			// flex: 1;
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.content_wrap,
			.content_wrap_two {
				padding: 20px 16px;
				display: flex;
				flex-direction: column;
				gap: 8px;
				.floor_item {
					display: flex;
					gap: 2px;
					.floor_wrap {
						width: 64px;
						display: flex;
						align-items: center;
						justify-content: center;
						background: #e8f3ff;
						color: #1868f1;
						font-size: 14px;
						font-weight: 500;
						line-height: 22px;
						border-radius: 4px 0 0 4px;
					}
					.detail_wrap {
						flex: 1;
						display: flex;
						flex-direction: column;
						gap: 2px;
						.item_wrap {
							display: flex;
							gap: 12px;
							font-size: 14px;
							line-height: 22px;
							padding: 9px 16px;
							background: #f7f8fa;
							border-radius: 0 4px 4px 0;
							.type {
								font-weight: 600;
								color: #1d2129;
							}
							.detail {
								flex: 1;
								color: #4e5969;
								font-weight: 400;
							}
						}
					}
				}

				.table_wrap {
					width: 100%;
				}
			}
			.content_wrap_two {
				flex-direction: column;
				.pie_wrap {
					margin-top: 40px;
					.title {
						font-size: 16px;
						font-weight: 600;
						line-height: 24px;
						color: #1d2129;
						margin-bottom: 20px;
						text-align: center;
					}
					.floor_tabs {
						display: flex;
						justify-content: center;
						flex-wrap: wrap;
						gap: 8px;
						max-width: 100%;
						.tab {
							width: 64px;
							height: 32px;
							display: flex;
							align-items: center;
							justify-content: center;
							border-radius: 100px;
							background: #f2f3f5;
							color: #86909c;
							font-size: 14px;
							font-weight: 400;
							cursor: pointer;
						}
						.tab_active {
							background: #e8f3ff;
							font-weight: 600;
							color: #1868f1;
						}
					}
				}
				.desc_wrap {
					margin-top: 16px;
					display: flex;
					flex-direction: column;
					gap: 8px;
					.desc_item {
						display: flex;
						background: #f7f8fa;
						padding: 20px;
						border-radius: 4px;
						gap: 12px;
						.icon {
							width: 32px;
							height: 32px;
							img {
								width: 100%;
								height: 100%;
							}
						}
						.desc {
							flex: 1;
							.title {
								font-size: 16px;
								font-weight: 600;
								line-height: 24px;
								color: #1d2129;
								margin-bottom: 8px;
							}
							.content {
								color: #4e5969;
								font-size: 14px;
								font-weight: 400;
								line-height: 22px;
							}
						}
					}
				}
			}

			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
}
</style>
