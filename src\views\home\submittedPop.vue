<template>
	<el-dialog
		v-model="dialogVisible"
		width="620px"
		class="purchase_dialog_submitted"
		style="border-radius: 16px; background: #fff"
		:show-close="true"
		:close-on-click-modal="false"
		align-center
		@close="handleClose"
	>
		<div class="content_box">
			<div class="result_box">
				<img src="@/assets/resultIcon.png" alt="" />
			</div>
			<div class="result_text">已提交</div>
			<div class="result_desc">我们已收到您的咨询信息，并会尽快与您取得联系，请留意电话和邮箱~</div>
			<div class="result_btn">
				<el-button type="primary" style="width: 88px" @click="handleClose" color="#1868F1">我知道了</el-button>
			</div>
		</div>
	</el-dialog>
</template>

<script setup>
import { ref } from 'vue';
const dialogVisible = ref(false);

// 父组件传递过来的方法 显示
const show = () => {
	dialogVisible.value = true;
};

const handleClose = () => {
	dialogVisible.value = false;
};

const onSubmit = async () => {
	handleClose();
};

// 暴露方法给父组件
defineExpose({ show });
</script>
<style lang="scss">
.purchase_dialog_submitted {
	.el-dialog__header {
		display: none;
	}

	.el-dialog__body {
		padding: 8px !important;
	}
}
</style>
<style scoped lang="scss">
::v-deep .el-form-item {
	margin-bottom: 16px !important;
}

.content_box {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	.result_box {
		width: 48px;
		height: 48px;
		img {
			width: 100%;
			height: 100%;
		}
		margin-bottom: 16px;
	}
	.result_text {
		font-weight: 500;
		font-size: 14px;
		line-height: 22px;
		color: #1d2129;
	}
	.result_desc {
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		text-align: center;
		color: #86909c;
	}
	.result_btn {
		margin-top: 16px;
		width: 100%;
		display: flex;
		justify-content: center;
	}
}
</style>
