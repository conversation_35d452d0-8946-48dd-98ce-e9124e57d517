<template>
	<div class="content_box">
		<!-- 折线图 -->
		<div class="echars_box">
			<div class="container_box">
				<div class="search_box">
					<div class="box_1">
						<div class="label">城市</div>
						<arco-cascader
							placeholder="请选择城市"
							v-model="cascaderCity"
							:arrow-icon="ArrowDown"
							@change="handleChange"
							path-mode
							:fieldNames="{ label: 'label', value: 'label', children: 'children' }"
							ref="cascaderDom"
							:options="$vuexStore.state.cityArray"
							:style="{ width: '220px' }"
						/>
					</div>
					<div class="box_1" style="margin-right: 16px">
						<div class="label">资产类型</div>
						<arco-select v-model="buildingTypesValue" style="width: 220px" placeholder="请选择资产类型">
							<arco-option
								:style="{ color: item.value === buildingTypesValue ? '#1868F1' : '' }"
								v-for="item in buildingTypes"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</arco-select>
					</div>
					<div class="box_2">
						<el-button type="primary" @click="onSubmit()" color="#1868F1">查询</el-button>
						<el-button @click="onReset()" style="color: #4e5969; margin-left: 8px" color="#F2F3F5">重置</el-button>
					</div>
				</div>
				<arco-table :columns="tableColumns" empty="暂无数据" :pagination="false" :bordered="{ cell: true }" :data="real">
					<template #empty>
						<div class="table_empty">暂无数据</div>
					</template>
					<template #columns>
						<arco-table-column
							v-for="(item, index) in tableColumns"
							:key="index"
							:data-index="item.dataIndex"
							:title="item.title"
							:width="item.width"
							:minWidth="item.minWidth"
						>
							<template #cell="scope">
								<Progress
									v-if="item.dataIndex == 'vacancyRate'"
									:progress="{ percentage: scope.record[item.dataIndex], color: '#3DD598', strokeWidth: 12 }"
								></Progress>

								<Progress
									v-else-if="item.dataIndex == 'rentGrowthRate' || item.dataIndex == 'saleGrowthRate'"
									:progress="{ percentage: scope.record[item.dataIndex], color: '#249EFF', strokeWidth: 12 }"
								></Progress>
								<div v-else-if="item.dataIndex == 'salePastTwelveMonth'">
									{{ scope.record[item.dataIndex] === 0 ? '-' : scope.record[item.dataIndex] }}
								</div>
								<div v-else>{{ scope.record[item.dataIndex] }}</div>
							</template>
						</arco-table-column>
					</template>
				</arco-table>
			</div>

			<!-- 折线图 -->
			<div class="echars_main">
				<div class="mainTitle1">城市数据</div>
				<div class="box_">
					<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
						<icon-download class="downIcon" />
					</div>
					<div style="width: 100%; height: 458px">
						<div class="title1">
							{{ city }}{{ province }}{{ buildingTypes.filter((item) => item.value == buildingTypesValue)?.[0]?.text }}每平米市场租金/租金要价
						</div>
						<div id="myChart1" v-show="newarr.length > 0" class="charts"></div>
						<div class="empty_wrap" v-show="newarr.length == 0">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="box_">
					<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
						<icon-download />
					</div>
					<div style="width: 100%; height: 458px">
						<div class="title1">
							{{ city }}{{ province }}{{ buildingTypes.filter((item) => item.value == buildingTypesValue)?.[0]?.text }}市场租金增长
						</div>
						<div id="myChart2" v-show="data2.length > 0" class="charts"></div>
						<div class="empty_wrap" v-show="data2.length == 0">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="box_">
					<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
						<icon-download />
					</div>
					<div style="width: 100%; height: 458px">
						<div class="title1">
							{{ city }}{{ province }}{{ buildingTypes.filter((item) => item.value == buildingTypesValue)?.[0]?.text }}每平米市场售价/买入要价
						</div>
						<div id="myChart3" v-show="data3.length > 0" class="charts"></div>
						<div class="empty_wrap" v-show="data3.length == 0">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="box_">
					<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
						<icon-download />
					</div>
					<div style="width: 100%; height: 458px">
						<div class="title1">
							{{ city }}{{ province }}{{ buildingTypes.filter((item) => item.value == buildingTypesValue)?.[0]?.text }}市场售价增长
						</div>
						<div id="myChart4" v-show="data4.length > 0" class="charts"></div>
						<div class="empty_wrap" v-show="data4.length == 0">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
// 引入echarts
import * as echarts from 'echarts';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import { getFinanceReportList, getCityData, getDictList } from '@/api/syt.js';

const real = ref([]);
const cascaderCity = ref([]);
const tableColumns = [
	{
		dataIndex: 'buildingType',
		title: '资产类型',
		minWidth: 140,
	},
	{
		dataIndex: 'vacancyRate',
		title: '空置率(%)',
		width: 300,
	},
	{
		dataIndex: 'perSquareRent',
		title: '每平米租金(元/天)',
		minWidth: 160,
	},
	{
		dataIndex: 'rentGrowthRate',
		title: '租金增长率(%)',
		width: 300,
	},
	{
		dataIndex: 'perSquareSale',
		title: '每平米售价(万元)',
		minWidth: 160,
	},
	{
		dataIndex: 'saleGrowthRate',
		title: '售价增长率(%)',
		width: 300,
	},
	{
		dataIndex: 'salePastTwelveMonth',
		title: '近12月销售额(亿)',
		minWidth: 160,
	},
	// {
	// 	prop: 'saleRatePastTwelveMonth',
	// 	label: '近12月去化率(%)',
	// },
];

const buildingTypes = ref([]);
const province = ref('');
const city = ref('');
const buildingTypesValue = ref();

const data2 = ref([]);
const data3 = ref([]);
const data4 = ref([]);
const newarr = ref([]);
let echarts1Max = ref(null);
let echarts2Max = ref(null);
let echarts3Max = ref(null);
let echarts4Max = ref(null);
let echarts1Min = ref(null);
let echarts2Min = ref(null);
let echarts3Min = ref(null);
let echarts4Min = ref(null);

let echartsTwelve1 = reactive([]);
let echartsTwelve2 = ref(null);
let echartsTwelve3 = ref(null);
let echartsTwelve4 = ref(null);
let myChart1;
let myChart2;
let myChart3;
let myChart4;

onMounted(() => {
	getDict();
});

// 选择地区
const handleChange = (val) => {
	city.value = val[0];
	province.value = val[1];
};

// 重置
function onReset() {
	cascaderCity.value = [];
	city.value = '';
	province.value = '';
	buildingTypesValue.value = null;
	real.value = [];
	newarr.value = [];
	data2.value = [];
	data3.value = [];
	data4.value = [];
}

// 搜索
const onSubmit = () => {
	const queryParams = {
		city: city.value,
		district: province.value,
		buildingType: buildingTypesValue.value,
		reportType: 0,
		year: new Date().getFullYear(),
	};

	if ((city.value && province.value && !buildingTypesValue.value) || (city.value && province.value && buildingTypesValue.value)) {
		handleSearch(queryParams);
	}
};

//绑定大楼
const handleSearch = async (param) => {
	let queryParams = {
		city: city.value,
		district: province.value,
		buildingType: buildingTypesValue.value,
	};
	await getCityData({ ...queryParams }).then((res) => {
		if (res.code == 200) {
			real.value = res.data;
			if (buildingTypesValue.value) {
				for (let index = 0; index < 4; index++) {
					getData(param, index);
				}
			} else {
				newarr.value = [];
				data2.value = [];
				data3.value = [];
				data4.value = [];
			}
		} else {
			newarr.value = [];
			data2.value = [];
			data3.value = [];
			data4.value = [];
			real.value = [];
		}
	});
};

const getData = async (queryParams, reportType) => {
	try {
		// 发送请求
		const response = await getFinanceReportList({ ...queryParams, reportType: reportType });
		if (reportType == 0) {
			// 处理数据
			if (response.data.length > 0) {
				newarr.value = response.data.map((item) => item.financeValue);
				echartsTwelve1 = response.data.map((item) => item.twelveMonth);
				echarts1Max.value = newarr.value.reduce((a, b) => Math.max(a, b));
				echarts1Min.value = newarr.value.reduce((a, b) => Math.min(a, b));
			} else {
				newarr.value = [];
			}
		} else if (reportType == 1) {
			if (response.data.length > 0) {
				data2.value = response.data.map((item) => (item.financeValue * 100).toFixed(2));
				echartsTwelve2 = response.data.map((item) => item.twelveMonth);

				echarts2Max.value = data2.value.reduce((a, b) => Math.max(a, b));
				echarts2Min.value = data2.value.reduce((a, b) => Math.min(a, b));
			} else {
				data2.value = [];
			}
		} else if (reportType == 2) {
			if (response.data.length > 0) {
				data3.value = response.data.map((item) => item.financeValue);
				echartsTwelve3 = response.data.map((item) => item.twelveMonth);
				echarts3Max.value = data3.value.reduce((a, b) => Math.max(a, b));
				echarts3Min.value = data3.value.reduce((a, b) => Math.min(a, b));
			} else {
				data3.value = [];
			}
			console.log(data3.value, '[]');
		} else if (reportType == 3) {
			if (response.data.length > 0) {
				data4.value = response.data.map((item) => (item.financeValue * 100).toFixed(2));
				echartsTwelve4 = response.data.map((item) => item.twelveMonth);
				echarts4Max.value = data4.value.reduce((a, b) => Math.max(a, b));
				echarts4Min.value = data4.value.reduce((a, b) => Math.min(a, b));
				console.log(data4.value, '[]');
			} else {
				data4.value = [];
			}
		}

		if (response.code !== 200) {
			return;
		}
		myChart1 = echarts.init(document.getElementById('myChart1'));
		myChart2 = echarts.init(document.getElementById('myChart2'));
		myChart3 = echarts.init(document.getElementById('myChart3'));
		myChart4 = echarts.init(document.getElementById('myChart4'));
		myChart1.resize({
			width: 795,
			height: 458,
		});
		myChart1.setOption({
			color: '#249EFF',
			tooltip: {
				trigger: 'axis',
				backgroundColor: 'rgba(244, 247, 252, .8)',
				borderColor: 'transparent',
				borderRadius: 4,
				formatter: function (params) {
					return `
        <div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
          <div style="width:8px;height:8px;background-color:#249EFF;margin-right: 8px;border-radius: 4px;">
          </div>
          <div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
           ${params[0].name}
          </div>
          <div style="font-size: 14px; color: #1D2129; font-weight: 500">
          ${params[0].value}元/㎡/天
          </div>
        </div>
        `;
				},
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: echartsTwelve1,
				axisLabel: {
					interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
				},
			},
			yAxis: {
				type: 'value',
				name: '元/㎡/天',
				splitLine: {
					show: true, // 虚拟线
					lineStyle: {
						color: '#E5E6EB',
						type: 'dashed',
					},
				},
				nameTextStyle: {
					padding: [0, 20, 8, 0],
				},
				axisLabel: {
					formatter: function (value, index) {
						return value.toFixed(2);
					},
				},
			},

			series: [
				{
					emphasis: {
						focus: 'series',
						itemStyle: { showSymbol: true, color: '#249EFF', symbolSize: 16, borderColor: '#D3ECFF', borderWidth: 2 },
					},
					symbol: 'circle',
					data: newarr.value,
					type: 'line',
					showSymbol: false,
					smooth: true,
					symbolSize: 6,
				},
			],
		});

		// 2

		myChart2.resize({
			width: 795,
			height: 458,
		});
		myChart2.setOption({
			color: '#37E2E2',
			tooltip: {
				trigger: 'axis',
				backgroundColor: 'rgba(244, 247, 252, .8)',
				borderColor: 'transparent',
				borderRadius: 4,
				formatter: function (params) {
					return `
        <div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
          <div style="width:8px;height:8px;background-color:#37E2E2;margin-right: 8px;border-radius: 4px;">
          </div>
          <div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
           ${params[0].name}
          </div>
          <div style="font-size: 14px; color: #1D2129; font-weight: 500">
          ${params[0].value}%
          </div>
        </div>
        `;
				},
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: echartsTwelve2,
				axisLabel: {
					interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
				},
			},
			yAxis: {
				type: 'value',
				name: '%',
				nameTextStyle: {
					padding: [0, 50, 8, 0],
				},
				splitLine: {
					show: true, // 虚拟线
					lineStyle: {
						color: '#E5E6EB',
						type: 'dashed',
					},
				},
				axisLabel: {
					formatter: function (value, index) {
						return value.toFixed(2);
					},
				},
			},
			series: [
				{
					emphasis: {
						focus: 'series',
						itemStyle: { showSymbol: true, color: '#37E2E2', symbolSize: 16, borderColor: '#D3ECFF', borderWidth: 2 },
					},
					symbol: 'circle',
					data: data2.value,
					type: 'line',
					showSymbol: false,
					smooth: true,
					symbolSize: 6,
				},
			],
		});
		myChart3.resize({
			width: 795,
			height: 458,
		});
		// 3
		myChart3.setOption({
			color: '#FF7D00',
			tooltip: {
				trigger: 'axis',
				backgroundColor: 'rgba(244, 247, 252, .8)',
				borderColor: 'transparent',
				borderRadius: 4,
				formatter: function (params) {
					return `
        <div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
          <div style="width:8px;height:8px;background-color:#FF7D00;margin-right: 8px;border-radius: 4px;">
          </div>
          <div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
           ${params[0].name}
          </div>
          <div style="font-size: 14px; color: #1D2129; font-weight: 500">
          ${params[0].value}万/㎡
          </div>
        </div>
        `;
				},
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: echartsTwelve3,
				axisLabel: {
					interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
				},
			},
			yAxis: {
				name: '万/㎡',
				nameTextStyle: {
					padding: [0, 35, 8, 0],
				},
				type: 'value',
				splitLine: {
					show: true, // 虚拟线
					lineStyle: {
						color: '#E5E6EB',
						type: 'dashed',
					},
				},
				axisLabel: {
					formatter: function (value, index) {
						return value.toFixed(2);
					},
				},
			},
			series: [
				{
					emphasis: {
						focus: 'series',
						itemStyle: { showSymbol: true, color: '#FF7D00', symbolSize: 16, borderColor: '#D3ECFF', borderWidth: 2 },
					},
					symbol: 'circle',
					smooth: true,
					data: data3.value,
					type: 'line',
					showSymbol: false,
					symbolSize: 6,
					// areaStyle: {},
				},
			],
		});
		// 4
		myChart4.resize({
			width: 795,
			height: 458,
		});
		myChart4.setOption({
			color: '#7482F1',
			tooltip: {
				trigger: 'axis',
				backgroundColor: 'rgba(244, 247, 252, .8)',
				borderColor: 'transparent',
				borderRadius: 4,
				formatter: function (params) {
					return `
        <div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
          <div style="width:8px;height:8px;background-color:#7482F1;margin-right: 8px;border-radius: 4px;">
          </div>
          <div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
           ${params[0].name}
          </div>
          <div style="font-size: 14px; color: #1D2129; font-weight: 500">
          ${params[0].value}%
          </div>
        </div>
        `;
				},
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: echartsTwelve4,
				axisLabel: {
					interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
				},
			},
			yAxis: {
				type: 'value',
				name: '%',
				nameTextStyle: {
					padding: [0, 50, 8, 0],
				},
				splitLine: {
					show: true, // 虚拟线
					lineStyle: {
						color: '#E5E6EB',
						type: 'dashed',
					},
				},
				axisLabel: {
					formatter: function (value, index) {
						return value.toFixed(2);
					},
				},
			},
			series: [
				{
					emphasis: {
						focus: 'series',
						itemStyle: { showSymbol: true, color: '#7482F1', symbolSize: 16, borderColor: '#D3ECFF', borderWidth: 2 },
					},
					symbol: 'circle',
					data: data4.value,
					smooth: true,
					type: 'line',
					showSymbol: false,
					symbolSize: 6,
					// areaStyle: {},
				},
			],
		});
	} catch (error) {
		console.error('请求失败', error);
	}
};

function handleScope(params) {
	console.log(params, 'paramsparamsparams');
}
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = [
				{
					color: null,
					jsonObject: null,
					label: '不限',
					text: '不限',
					title: '不限',
					value: '',
				},
				...res.data,
			];
		})
		.catch((err) => {
			console.log(err);
		});
};
</script>

<style scoped lang="less">
.content_box {
	width: calc(100% - 16px);
	height: 100%;
	min-height: 100vh;
	padding: 16px 16px 16px 0;
	background-color: rgba(245, 245, 245, 1);

	.echars_box {
		width: 100%;
		// min-height: 522px;
		box-sizing: border-box;
		border-radius: 6px;
		position: relative;
		.container_box {
			width: 100%;
			height: 100%;
			padding: 0 16px 16px 16px;
			margin-bottom: 16px;
			border-radius: 4px;
			box-sizing: border-box;
			background-color: rgba(255, 255, 255, 1);
			.table_empty {
				width: 100%;
				display: flex;
				justify-content: center;
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: #86909c;
			}
			::v-deep .arco-table-container {
				border-radius: 4px !important;
				.arco-table-th {
					background: #f7f8fa !important;
				}
				.arco-table-td-content {
					color: #4e5969 !important;
				}
				.arco-table-content {
					border-radius: 4px !important;
					tbody > :nth-last-child(1) > :nth-last-child(1) {
						border-bottom-right-radius: 4px !important;
					}
					tbody > :nth-last-child(1) > :nth-child(1) {
						border-bottom-left-radius: 4px !important;
					}
				}
			}
		}
		.search_box {
			width: 100%;
			overflow: hidden;
			padding: 20px 0px;
			box-sizing: border-box;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;

			.box_1 {
				height: 32px;
				margin-right: 40px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				border-radius: 4px;
				box-sizing: border-box;
				::v-deep .arco-select-view-focus {
					border: 1px solid #409eff !important;
				}
				::v-deep .arco-select-view-single {
					border: 1px solid #e5e6eb;
					background-color: #fff;
					height: 32px;
					border-radius: 4px;
				}
				::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
					box-shadow: 0;
				}

				.label {
					color: #4e5969;
					margin-right: 16px;
					font-weight: 400;
					font-size: 14px;
					color: rgba(134, 144, 156, 1);
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
			.box_2 {
				width: 230px;
				height: 32px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				border-radius: 4px;
				box-sizing: border-box;
			}
		}
		.echars_main {
			background-color: rgba(255, 255, 255, 1);
			width: 100%;
			height: 100%;
			padding: 4px 16px 0px 16px;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;
			.mainTitle1 {
				width: 100%;
				height: 60px;
				font-weight: 500;
				font-size: 20px;
				line-height: 60px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				color: #1d2129;
				&::before {
					content: '';
					width: 5px;
					height: 14px;
					background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
					border-radius: 10px;
					margin-right: 8px;
				}
			}

			.box_ {
				width: 49.5%;
				height: 506px;
				margin-bottom: 15px;
				border: 1px solid rgba(231, 231, 231, 1);
				border-radius: 6px;
				box-sizing: border-box;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-direction: column;
				position: relative;
				.download {
					position: absolute;
					top: 12px;
					right: 20px;
					cursor: pointer;
					color: #333333;
					font-size: 20px;
					font-weight: 600;
					&:hover {
						color: #1868f1;
					}
					::v-deep .arco-icon {
						font-size: 18px;
						stroke-width: 3px;
						color: #1868f1;
					}
				}
				.title1 {
					padding-left: 20px;
					width: calc(100% - 20px);
					height: 47px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					font-weight: 500;
					font-size: 16px;
					color: #1d2129;
					background-color: #f7f8fa;
					border-bottom: 1px solid #e5e6eb;
				}
			}
		}
	}
}

.charts {
	height: 458px;
	> :nth-child(1) {
		width: 100% !important;
		> :nth-child(1) {
			width: 100% !important;
		}
	}
}

.empty_wrap {
	flex: 1;
	height: 440px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 400;
	color: #86909c;
	img {
		width: 80px;
		height: 80px;
	}
}
</style>
