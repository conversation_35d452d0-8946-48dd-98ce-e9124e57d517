<template>
	<div class="recruit_main">
		<div class="goback_box">
			<div class="btn" @click="router.go(-1)">
					<span><el-icon><ArrowLeft /></el-icon></span>
				返回
			</div>
		</div>
	<div class="container_box">
		<div class="title">数据分析助理</div>
		<div class="tips">
			<span>青岛</span>
			<span>双休</span>
			<span>五一双金</span>
			<span>带薪年假</span>
			<span>工龄工资</span>
		</div>
		<div class="title1">职位描述</div>
		<div class="tips1">数据分析、经济学、金融学等相关专业，以及其他专业优秀毕业生；熟悉使用office软件，会使用SPSS、CAD等软件优先，认真细致、注重团队协作。</div>
		<div class="title1">职位要求</div>
		<div class="tips1">数据分析、经济学、金融学等相关专业，以及其他专业优秀毕业生；熟悉使用office软件，会使用SPSS、CAD等软件优先，认真细致、注重团队协作。</div>
		<div class="title1">薪资待遇</div>
		<div class="tips1">五险一金，双休法休，带薪年假，工龄工资，系统的培训，有前景的晋升空间。</div>
		<div class="title1">投递方式</div>
		<div class="tips1">请发送简历至 <EMAIL>，邮件中请写明应聘岗位</div>
	</div>
		
	</div>

</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue';
	import {
		useRouter
	} from 'vue-router';
	import http from '@/utils/http';
	const router = useRouter();
	const recruitData = ref([])
	const items = ref(1)
	onMounted(() => {
		console.log('招聘');
		// getRecruitData()
	})
	const getRecruitData = async () => {
		const res = await http.get('gis/a_threeename.do?act=getRecruitInfo')
		// recruitData.value = res.data
		console.log(1, res);
		recruitData.value = res.body
	}
	const onitem = (id) => {
		console.log(id);
		items.value = id
	}
	// 点击下一个
	const next = () => {
		console.log('下一个');
		if (items.value < 4) {
			items.value++
		}
	}
	// 点击上一个
	const last = () => {
		console.log('上一个');
		if (items.value > 1) {
			items.value--
		}
	}
</script>

<style lang="less" scoped>
	.recruit_main{
		width: 100%;
		height: 100%;
		min-height: 100vh;
		background-color: rgba(245, 245, 245, 1);
		.goback_box{
			width: 100%;
			height: 48px;
			background-color: rgba(255, 255, 255, 1);
			box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
			.btn{
				width: 1200px;
				height: 48px;
				margin: 0 auto;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				cursor: pointer;
				span{
					margin-top: 3px;
				}
			}
		}
		.container_box{
			width: 100%;
			height: 100%;
			max-width: 1200px;
			margin: 0 auto;
			margin-top: 50px;
			.title{
				font-size: 48px;
				font-weight: bold;
			}
			.tips{
				font-size: 16px;
				color: rgba(134, 144, 156, 1);
				margin-top: 30px;
				span{
					margin-right: 15px;
				}
			}
			.title1{
				font-size: 20px;
				font-weight: bold;
				border-left: 5px solid rgba(24, 104, 241, 1);
				padding-left: 15px;
				box-sizing: border-box;
				margin-top: 30px;
			}
			.tips1{
				font-size: 20px;
				color: rgba(29, 33, 41, 1);
				margin-top: 30px;
			}
			
		}
	}
	@media screen and (max-width:1024px) {
		.recruit_main{
			.container_box{
				width: 100%;
				height: 100%;
				max-width: 375px;
				padding: 15px;
				box-sizing: border-box;
			}
		}
	}
</style>