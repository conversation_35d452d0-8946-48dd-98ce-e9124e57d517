import { fetch } from './http';
export function getFloorPlan(params) {
	// 交易材料-户型图数据查询
	return fetch({
		url: '/api/syt/tradingMaterials/floorPlan',
		method: 'get',
		params: params,
	});
}
// 根据字典code查询列表
export function getDictList(params) {
	return fetch({
		url: '/api/dict/dictList',
		method: 'get',
		params: params,
	});
}
export function getComparativeValue(params) {
	// 交易材料-价值对比数据查询
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeValues',
		method: 'get',
		params: params,
	});
}
export function getComparativeTenant(params) {
	// 交易材料-租户对比数据查询
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeTenant',
		method: 'get',
		params: params,
	});
}
export function getComparativePopulation(params) {
	// 交易材料-人口对比数据查询
	return fetch({
		url: '/api/syt/tradingMaterials/comparativePopulation',
		method: 'get',
		params: params,
	});
}

export function getCreditRisk(params) {
	// 楼宇信息-信用风险
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeCreditRisk',
		method: 'get',
		params: params,
	});
}

export function getBuildingSoldPrice(params) {
	// 交易材料-交易计算楼宇成交价数据查询
	return fetch({
		url: '/api/syt/tradingMaterials/buildingSoldPrice',
		method: 'get',
		params: params,
	});
}

// 商宇通-地产金融接口控制器
export function getFinanceReitsList(params) {
	// REITS管理-分页列表查询
	return fetch({
		url: '/api/syt/realEstateFinance/reitsList',
		method: 'POST',
		data: params,
	});
}
export function getFinanceReportList(params) {
	// 金融市场-金融报表数据查询
	return fetch({
		url: '/api/syt/realEstateFinance/financeReportList',
		method: 'get',
		params: params,
	});
}
export function getFinanceIndex(params) {
	// 金融市场-金融指数数据查询
	return fetch({
		url: '/api/common/financial-data',
		method: 'get',
		params: params,
	});
}
export function getFinanceAbsList(params) {
	// ABS管理-分页列表查询
	return fetch({
		url: '/api/syt/realEstateFinance/absList',
		method: 'POST',
		data: params,
	});
}
export function getAbsCompanySelectList(params) {
	// ABS管理-公司列表下拉分页查询
	return fetch({
		url: '/api/syt/realEstateFinance/absCompanySelectList',
		method: 'get',
		params: params,
	});
}
// 商宇通-参与者接口控制器
export function getOwnerCompanyList(params) {
	// 顾问-产权人列表查询
	return fetch({
		url: '/api/syt/participants/ownerCompanyList',
		method: 'get',
		params: params,
	});
}
export function getManagerCompanyList(params) {
	// 顾问-管理人列表查询
	return fetch({
		url: '/api/syt/participants/managerCompanyList',
		method: 'get',
		params: params,
	});
}
export function getConsultantList(params) {
	// 顾问-顾问列表数据查询
	return fetch({
		url: '/api/syt/participants/consultantList',
		method: 'get',
		params: params,
	});
}
export function getCompanyRelatedBuildingList(params) {
	// 顾问-公司关联楼宇查询
	return fetch({
		url: '/api/syt/participants/companyRelatedBuildingList',
		method: 'get',
		params: params,
	});
}
export function getBuildingBookList(params) {
	// 顾问-楼书信息列表查询
	return fetch({
		url: '/api/syt/participants/buildingBookList',
		method: 'get',
		params: params,
	});
}
export function getBrokerBuildingList(params) {
	// 顾问-顾问绑定楼宇列表查询
	return fetch({
		url: '/api/syt/participants/brokerBuildingList',
		method: 'get',
		params: params,
	});
}
// 商宇通-市场统计接口控制器
export function getTransactionStatistics(params) {
	// 市场统计-交易统计数据查询
	return fetch({
		url: '/api/syt/marketStatistics/transactionStatistics',
		method: 'POST',
		data: params,
	});
}
export function getMarketStatistics(params) {
	// 市场统计-市场统计数据查询
	return fetch({
		url: '/api/syt/marketStatistics/marketStatistics',
		method: 'POST',
		data: params,
	});
}
export function getBuildingCardList(params) {
	// 市场统计-楼宇卡片列表查询
	return fetch({
		url: '/api/syt/marketStatistics/buildingCardList',
		method: 'POST',
		data: params,
	});
}
// 楼宇信息管理
export function getBuildingListByMultiCondition(params) {
	// 多条件获取楼宇列表
	return fetch({
		url: '/api/building/buildingListByMultiCondition',
		method: 'POST',
		data: params,
	});
}
// 楼宇信息根据Id查询
export function getBuildingListById(params) {
	return fetch({
		url: '/api/building/buildingInfoById',
		method: 'GET',
		params: params,
	});
}
export function getBuildingBusinessDistrictList(params) {
	// 根据商圈ID查询商圈内的楼宇列表
	return fetch({
		url: '/api/building/businessDistrictList',
		method: 'get',
		params: params,
	});
}
// 商圈信息管理
export function getBusinessDistrictList(params) {
	// 根据城市地区名称查询商圈名称列表
	return fetch({
		url: '/api/businessDistrict/businessDistrictList',
		method: 'get',
		params: params,
	});
}

export function getAllBusinessDistrict(params) {
	// 查询商圈
	return fetch({
		url: '/api/syt/tradingMaterials/allBusinessDistrict',
		method: 'get',
		params: params,
	});
}

export function getBusinessAnalysis(params) {
	// 商圈分析对比数据
	return fetch({
		url: '/api/syt/tradingMaterials/businessAnalysis',
		method: 'get',
		params: params,
	});
}

export function getBuildingLocation(params) {
	// 资产对比-位置
	return fetch({
		url: '/api/syt/tradingMaterials/buildingLocation',
		method: 'get',
		params: params,
	});
}

export function getBuildingAppraisedValue(params) {
	// 估值与证券化
	return fetch({
		url: '/api/syt/tradingMaterials/buildingAppraisedValue',
		method: 'get',
		params: params,
	});
}

export function getBuildingOverview(params) {
	// 楼宇信息
	return fetch({
		url: '/api/building/getBuildingOverview',
		method: 'get',
		params: params,
	});
}

export function getFloorPlanCount(params) {
	// 户型图统计
	return fetch({
		url: '/api/syt/tradingMaterials/floorPlanCount',
		method: 'get',
		params: params,
	});
}

export function getCityData(params) {
	// 城市数据
	return fetch({
		url: '/api/syt/marketStatistics/cityData',
		method: 'get',
		params: params,
	});
}
export function getFloorNew(params) {
	// 城市数据
	return fetch({
		url: '/api/syt/tradingMaterials/floorPlan',
		method: 'get',
		params: params,
	});
}
export function getBuildLingshouInfo(params) {
	// 楼宇信息-零售
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeRetailTenant',
		method: 'get',
		params: params,
	});
}

export function getMedicalInfo(params) {
	// 租户-医疗
	return fetch({
		url: '/api/syt/tenant/merchantHealth',
		method: 'get',
		params: params,
	});
}

export function getBuildJiuDianInfo(params) {
	// 楼宇信息-酒店
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeHotel',
		method: 'get',
		params: params,
	});
}

export function getBuildMarketInfo(params) {
	// 综合市场
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeMarket',
		method: 'get',
		params: params,
	});
}

export function getBuildApartmentInfo(params) {
	// 楼宇信息-长租公寓
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeApartment',
		method: 'get',
		params: params,
	});
}

export function getComparativeOverviews(params) {
	// 交易材料-楼宇信息-楼宇概况
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeOverviews',
		method: 'get',
		params: params,
	});
}

export function getBuildCangchuInfo(params) {
	// 楼宇信息-仓储物流
	return fetch({
		url: '/api/syt/tenant/merchantWarehouse',
		method: 'get',
		params: params,
	});
}

export function getPopulationTrend(params) {
	// 人口趋势
	return fetch({
		url: '/api/syt/population/trend',
		method: 'get',
		params: params,
	});
}
export function getTransactionHistory(params) {
	// 租户-交易历史
	return fetch({
		url: '/api/syt/tradingMaterials/comparativeTradeHistory',
		method: 'get',
		params: params,
	});
}

export function getbrandList(params) {
	// 品牌列表
	return fetch({
		url: '/api/syt/brand/brandList',
		method: 'get',
		params: params,
	});
}

export function getbrandDetail(params) {
	// 品牌详情
	return fetch({
		url: '/api/syt/brand/brandDetail',
		method: 'get',
		params: params,
	});
}

export function getbrandCollect(params) {
	// 品牌关注
	return fetch({
		url: '/api/syt/brand/brandCollect',
		method: 'POST',
		data: params,
	});
}

export function getcontactorList(params) {
	// 对接品牌方列表
	return fetch({
		url: '/api/syt/brand/contactorList',
		method: 'get',
		params: params,
	});
}
