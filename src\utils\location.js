// location.js

export const getCurrentCity = async () => {
	return new Promise((resolve, reject) => {
		if (navigator.geolocation) {
			navigator.geolocation.getCurrentPosition(
				(position) => {
					console.log(position, 'pos');

					const latitude = position.coords.latitude.toFixed(7);
					const longitude = position.coords.longitude.toFixed(7);
					console.log(latitude, longitude, '2');
					const apiUrl = `https://restapi.amap.com/v3/geocode/regeo?key=28b9195959eab7fd9bb3ec23662fc026&location=${longitude},${latitude}&poitype=&radius=&extensions=base&roadlevel=`;
					// const apiUrl = `https://restapi.amap.com/v3/geocode/regeo?key=28b9195959eab7fd9bb3ec23662fc026&location=${latitude},${longitude}`;
					
					resolve(latitude, longitude);
					
				},
				(error) => {
					reject(error);
				}
			);
		} 
	});
};
