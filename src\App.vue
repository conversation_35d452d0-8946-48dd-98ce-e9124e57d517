<template>
	<el-config-provider :locale="zhCn">
		<router-view />
	</el-config-provider>
	<smPermissionPop ref="smPermissionPop" class="smPermissionPop"></smPermissionPop>
</template>

<script setup>
import smPermissionPop from './component/smPermissionPop/index.vue';
import { ElMessage } from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const errorNotification = ref({
	pending: false,
	timeout: 1000,
});
const router = useRouter();
router.beforeEach((to, from, next) => {
	router.onError((error, to) => {
		// 错误处理逻辑
		if (error.message.includes('Failed to fetch dynamically imported module')) {
			if (errorNotification.value.pending) return;
			errorNotification.value.pending = true;
			ElMessage({
				type: 'success',
				message: '发现系统版本更新，页面即将刷新~',
			});

			setTimeout(() => {
				errorNotification.value.pending = false;
				window.location = to.fullPath;
			}, errorNotification.value.timeout);
		} else {
			window.location = to.fullPath;
		}
	});

	document.title = to.meta.title || '术木智能';
	next();
});
</script>

<script>
var _hmt = _hmt || [];
(function () {
	var hm = document.createElement('script');
	hm.src = 'https://hm.baidu.com/hm.js?dc2a733677508064cb9bd9c77a0984d8';
	var s = document.getElementsByTagName('script')[0];
	s.parentNode.insertBefore(hm, s);
	//   console.log('百度统计',hm);
})();
</script>

<style>
#app {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	/* overflow: hidden; */
	font-family: 'PingFang SC', sans-serif;
}
.smPermissionPop {
	padding: 0 !important;
}

.smPermissionPop .el-dialog__header {
	padding: 0 !important;
}
</style>
<style lang="scss">
body {
	padding: 0;
	margin: 0;
	font-size: 16px;
	box-sizing: border-box;
	font-family: '微软雅黑';
	.smPermissionPopdialog {
		.el-dialog__header {
			padding: 0 !important;
		}
	}
}
.bigbody {
	margin-left: 20px;
}
.mx-1 {
	color: #000;
	font-size: 14px;
}
.el-menu {
	border-right: none !important;
}
.el-submenu {
	border-top: 1px solid hsla(0, 0%, 100%, 0.05);
	border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}
.el-submenu:first-child {
	border-top: none;
}
.el-submenu [class^='el-icon-'] {
	vertical-align: -1px !important;
}
a {
	color: #409eff;
	text-decoration: none;
}
.el-pagination {
	text-align: center;
	margin-top: 20px;
}

::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
	height: 1px;
}
::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 5px;
	background: #cfcbcb;
}
::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	border-radius: 6px;
	background: #ededed;
}

::-webkit-scrollbar {
	width: 6 !important;
	height: 0;
}

.loadingComparison,
.loadingPopulation {
	.el-loading-spinner .path {
		stroke: #1868f1;
	}
	.el-loading-text {
		color: #1868f1;
	}
}

.trial_benefits_dialog {
	padding: 0px;
	border-radius: 16px;
	.el-dialog__header {
		padding: 0 !important;
	}
}
</style>
