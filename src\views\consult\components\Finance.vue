<template>
    <div class="title_box">
        <!-- <el-button  round @click="back(router.push('consult'))" style="margin: 15px;">返回</el-button> -->
        <a style="margin: 15px;">地产金融</a>
    </div>
    <div class="finance_table">
        <el-tabs type="border-card">
        <el-tab-pane label="金融市场">
            <finance-bazaar></finance-bazaar>
        </el-tab-pane>
        <el-tab-pane label="标准化产品">
            <finance-security></finance-security>
        </el-tab-pane>
        <el-tab-pane label="抵押贷款">
            <finance-loans></finance-loans>
        </el-tab-pane>
        <el-tab-pane label="信用风险">
            <finance-risk></finance-risk>
        </el-tab-pane>
      </el-tabs>
    </div>
    </template>
    
    <script setup>
    import FinanceBazaar from './finance-bazaar.vue'
    import FinanceSecurity from './finance-security.vue'
    import FinanceRisk from './finance-risk.vue'
    import FinanceLoans from './finance-loans.vue'
    import {ref} from 'vue'
    import {useRouter} from 'vue-router'
    const router = useRouter()
    </script>
    
    <style lang="less" scoped>
    .title_box {
        display: flex;
        margin-top: 2px;
        margin-left: 19px;
        a 
        {
            text-decoration: none;
            color: #000;
            font-size: 24px;
            font-weight: bold;
        }
    }
    </style>