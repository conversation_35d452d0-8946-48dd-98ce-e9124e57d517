<template>
	<div class="body_box">
		<div class="container_top">
			<div class="top_content">
				<div class="container_left">
					<div><img src="@/assets/newLogo.png" alt="" /></div>
					<div class="con_title" @click="router.push('/')">
						<div>术木智能</div>
						<div>·商业地产价值分析平台</div>
					</div>
				</div>
				<div class="container_right" @click="router.push('/relation')">
					<div><img src="@/assets/customerService.png" alt="" /></div>
					<div>客户服务</div>
				</div>
			</div>
		</div>

		<div class="protocolReturn" v-if="agreeStatus">
			<div @click="handleSuerAgree(0)">
				<el-icon><ArrowLeft /></el-icon>
				<div>返回</div>
			</div>
		</div>

		<div class="containerBanner" v-if="!agreeStatus">
			<div><img src="@/assets/platform.png" alt="" /></div>
			<div class="c_right">
				<div class="c_content">
					<!-- 关闭按钮 -->
					<div class="close_button" @click="router.push('/')">
						<span class="close_icon">×</span>
					</div>
					<div class="c_contentpad">
						<div class="title_content" :class="indexActive === 3 || indexActive === 4 ? 'title_contents' : ''">
							<div class="con_title">
								<div>
									{{
										indexActive === 1 || indexActive === 2 ? '您好，欢迎来到' : indexActive === 3 ? '找回密码' : indexActive === 4 ? '欢迎注册' : ''
									}}
								</div>
								<div v-if="indexActive === 1 || indexActive === 2">术木智能·商业地产价值分析平台</div>
							</div>

							<!-- 标签切换 -->
							<div class="tab_switch" v-if="indexActive === 1 || indexActive === 2">
								<div class="tab_item" :class="{ active: indexActive === 1 }" @click="handleBtnClick({}, 1)">
									<span>账号登录</span>
									<div class="tab_underline" v-if="indexActive === 1"></div>
								</div>
								<div class="tab_item" :class="{ active: indexActive === 2 }" @click="handleBtnClick({}, 2)">
									<span>手机号登录</span>
									<div class="tab_underline" v-if="indexActive === 2"></div>
								</div>
							</div>
						</div>

						<div class="accountlogin">
							<el-form :model="ruleForm" size="large" ref="ruleForms">
								<el-form-item v-if="indexActive === 1 || indexActive === 4" prop="userName">
									<el-input v-model="ruleForm.userName" maxlength="14" placeholder="请输入用户名" />
								</el-form-item>
								<el-form-item v-if="indexActive !== 1" prop="phone">
									<el-input v-model="ruleForm.phone" maxlength="11" placeholder="请输入手机号" />
								</el-form-item>
								<el-form-item v-if="indexActive === 2 || indexActive === 3 || indexActive === 4" prop="code">
									<el-input v-model="ruleForm.code" maxlength="4" placeholder="请输入验证码">
										<template #suffix>
											<div class="verificationCode" @click="onGetCode()" v-if="isSend">获取验证码</div>
											<div class="getCode" v-else style="margin-right: 8px">重新发送{{ time }}s</div>
										</template>
									</el-input>
								</el-form-item>

								<el-form-item v-if="indexActive === 1 || indexActive === 3 || indexActive === 4" prop="password" class="miyaoWrap">
									<el-input
										v-model="ruleForm.password"
										type="text"
										:class="password ? 'no-autofill-pwd' : 'no-auto'"
										maxlength="20"
										:placeholder="indexActive === 1 || indexActive === 4 ? '请输入登录密码' : '设置新密码'"
									>
										<template #suffix>
											<div style="display: flex; cursor: pointer">
												<img v-if="password" src="@/assets/hideIcon.png" alt="" @click="showPwd" />
												<img v-else src="@/assets/showIcon.png" alt="" @click="showPwd" />
											</div>
										</template>
									</el-input>
								</el-form-item>

								<el-form-item v-if="indexActive === 3 || indexActive === 4" prop="passwords" class="miyaoWrap">
									<el-input
										v-model="ruleForm.passwords"
										type="text"
										:class="passwords ? 'no-autofill-pwd' : 'no-auto'"
										maxlength="20"
										placeholder="请再次确认新密码"
									>
										<template #suffix>
											<div style="display: flex; cursor: pointer">
												<img v-if="passwords" src="@/assets/hideIcon.png" alt="" @click="showPwds" />
												<img v-else src="@/assets/showIcon.png" alt="" @click="showPwds" />
											</div>
										</template>
									</el-input>
								</el-form-item>

								<el-form-item>
									<el-button type="primary" @click="handleLogin(indexActive)" color="#1868F1">{{
										indexActive === 3 || indexActive === 4 ? '完成' : '登录'
									}}</el-button>
								</el-form-item>

								<el-form-item prop="agree" v-if="indexActive !== 3">
									<el-checkbox size="large" v-model="formLabelAlign.agreeAgreement" class="checkAgree">
										我已阅读并同意<span @click="handleSuerAgree(1)" class="agree">用户协议、隐私政策</span> 和
										<span class="agree" @click="handleSuerAgree(2)">产品使用条款</span>
									</el-checkbox>
								</el-form-item>
							</el-form>
						</div>

						<div class="c_option" v-if="indexActive === 1 || indexActive === 2">
							<div @click="handleBtnClick({}, 3)">忘记密码</div>
							<div class="separator">｜</div>
							<div @click="handleBtnClick({}, 4)">注册账号</div>
						</div>

						<div class="c_option" v-if="indexActive === 3">
							<div @click="handleBtnClick({}, 1)">返回登录</div>
							<div class="separator">｜</div>
							<div @click="handleBtnClick({}, 4)">注册账号</div>
						</div>

						<div class="c_option" v-if="indexActive === 4">
							<div @click="handleBtnClick({}, 1)">返回登录</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div v-else class="agreements">
			<div class="agreement_bnanner">
				<privacy v-if="agreeStatus === 1"></privacy>
				<agreement v-if="agreeStatus === 2" :returnShow="false"></agreement>
			</div>
		</div>
	</div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import privacy from './privacy.vue';
import agreement from './agreement.vue';
import { useStore, vuexStore } from '../../store';
import { getCodes, getRegister, getLogin, getForget } from 'REQUEST_API';
import { ElMessage } from 'element-plus';
const store = useStore();
const route = useRoute();
const ruleForm = reactive({
	// 用户名
	userName: null,
	// 手机号码
	phone: null,
	// 验证码
	code: null,
	// 密码
	password: null,
	// 确认密码
	passwords: null,
});

const formLabelAlign = reactive({
	// 是否同意协议
	agreeAgreement: false,
	listOption: [
		{
			name: '密码登录',
			index: 1,
		},
		{
			name: '验证码登录',
			index: 2,
		},
		{
			name: '找回密码',
			index: 3,
		},
		{
			name: '注册账户',
			index: 4,
		},
	],
});

// dom
const ruleForms = ref(null);
// 验证码秒数
const time = ref(60);
// 获取验证码
const isSend = ref(true);
// 控制展示内容
const indexActive = ref(1);

// 控制协议展示状态
const agreeStatus = ref(0);

const router = useRouter();

let password = ref(true);

let passwords = ref(true);

// 页面加载
onMounted(() => {
	vuexStore.dispatch('handleDistrict'); // 获取区域
});

const showPwd = () => {
	password.value = !password.value;
};

const showPwds = () => {
	passwords.value = !passwords.value;
};

/**
 * @function handleBtnClick 切换登录页展示内容
 */
function handleBtnClick(item, idx) {
	formLabelAlign.agreeAgreement = false;
	passwords.value = true;
	password.value = true;
	ruleForms.value.resetFields();
	if (idx) {
		indexActive.value = idx;
	} else {
		indexActive.value = item.index;
	}
}

/**
 * @function handleLogin
 * @index 1 密码登录 2 验证码登录 3 找回密码 4 注册账户
 */
function handleLogin(index) {
	if (index === 1 && handleAgree()) {
		// 用户名密码验证
		if (!ruleForm.userName) {
			ElMessage.warning('请输入用户名');
			return;
		}

		// 用户名密码验证
		if (!ruleForm.password) {
			ElMessage.warning('请输入密码');
			return;
		}

		handleLogins({ type: 'PASSWORD' });
		return;
	}
	if (index === 2 && handleAgree()) {
		// 手机号码验证
		if (!ruleForm.phone) {
			ElMessage.warning('请输入手机号');
			return;
		}
		//验证码验证
		if (!ruleForm.code) {
			ElMessage.warning('请输入验证码');
			return;
		}
		handleLogins({ type: 'PHONE' });
		return;
	}
	if (index === 3 && handleAgree(3)) {
		// 找回密码
		if (!ruleForm.phone) {
			ElMessage.warning('请输入手机号');
			return;
		}
		// 密码验证6-20个字符，需包含字母、数字，不能包含空格
		if (!ruleForm.password) {
			ElMessage.warning('请输入密码');
			return;
		}
		if (ruleForm.password.length < 6 || ruleForm.password.length > 20) {
			ElMessage.warning('密码长度6-20个字符');
			return;
		}
		if (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$/.test(ruleForm.password)) {
			ElMessage.warning('密码需包含字母、数字，不能包含空格');
			return;
		}

		handlegetForget();
		return;
	}

	if (index === 4 && handleAgree()) {
		// 用户注册
		if (!ruleForm.userName) {
			ElMessage.warning('请输入用户名');
			return;
		}
		// 验证密码
		if (!ruleForm.password) {
			ElMessage.warning('请输入密码');
			return;
		}
		// 比较两次密码
		if (ruleForm.password !== ruleForm.passwords) {
			ElMessage.warning('两次密码不一致');
			return;
		}
		handlegetRegister();
	}
}
// 用户名登录 / 密码登录
function handleLogins(param) {
	getLogin({ ...ruleForm, ...param }).then((res) => {
		handleUpdate(res);
	});
}
// 忘记密码
function handlegetForget() {
	getForget({ ...ruleForm }).then((res) => {
		// handleUpdate(res);
		if (res.code == 200) {
			ElMessage({
				message: '密码重置成功',
				type: 'success',
			});
			handleBtnClick({}, 1);
		}
	});
}

// 用户注册
function handlegetRegister() {
	getRegister({ ...ruleForm }).then((res) => {
		handleUpdate(res);
	});
}

// 更新数据
function handleUpdate(res) {
	if (!res) return;
	if (res && res.code == 200) {
		window.localStorage.setItem('token', res.data.accessToken); // 设置token
		store.getUserInfo(); // 获取用户信息
		vuexStore.dispatch('handleGetShoppingCart'); // 获取购物车
		vuexStore.dispatch('handleDistrict'); // 获取区域
		vuexStore.commit('handleNewUserCoupon', res.data.sendSytNewUserCoupon); // 新用户专享优惠券
		ElMessage({
			message: '登录成功',
			type: 'success',
		});
		if (window.history.length === 1) {
			router.push('/');
			// 当前页面是历史记录中的最后一页
		} else {
			if (window.history.state.back !== '/login') {
				if (route.query['0'] === '1') {
					router.push('/');
				} else {
					router.push(window.history.state.back);
				}
			} else {
				router.push('/');
			}
		}
	} else if (res.code == 500 || res.code == 2005) {
		// ElMessage.error(res.message);
	} else if (res.code == 1002) {
		ElMessage.error('请您先点击获取验证码再做操作');
	}
}

// 效验用户协议 type 3 找回密码
function handleAgree(type) {
	if (type === 3) {
		if (ruleForm.password !== ruleForm.passwords) {
			ElMessage.warning('请输入相同的密码');
			return false;
		}
	} else {
		if (!formLabelAlign.agreeAgreement) {
			ElMessage.warning('请先同意用户协议');
			return false;
		}
	}
	return true;
}

// 获取验证码
const onGetCode = async () => {
	const phone = ruleForm.phone;
	if (!phone) {
		return ElMessage({
			message: '请输入手机号',
			type: 'warning',
		});
	}
	// 校验手机号
	const reg = /^1[3-9]\d{9}$/;
	if (!reg.test(phone)) {
		return ElMessage({
			message: '请输入正确的手机号',
			type: 'warning',
		});
	}
	// console.log('获取验证码');
	isSend.value = false;
	const timer = setInterval(() => {
		time.value--;
		if (time.value == 0) {
			clearInterval(timer);
			isSend.value = true;
			time.value = 60;
		}
	}, 1000);

	// 发送验证码
	await getCodes({ phone: ruleForm.phone })
		.then((res) => {
			console.log(res);
			// 发送成功提示
			ElMessage({
				message: '验证码发送成功,请注意查收~',
				type: 'success',
			});
		})
		.catch((err) => {
			console.log('err', err);
			// 显示错误信息给用户
		});
};
// 产品使用条款
const handlePrivacyAgree = () => {
	// router.push('/privacy');
};
// 用户协议  index 1 用户协议 2 产品使用条款
const handleSuerAgree = (index) => {
	agreeStatus.value = index;
	// router.push('/agreement');
};
</script>

<style lang="less" scoped>
h1 {
	font-family: 'Sofia';
	font-size: 40px;
}

.body_box {
	height: 100vh;
	width: 100%;
	overflow: hidden;
	background-color: #DDE6F4;
	.container_top {
		height: 78px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-bottom: 1px solid #e7e7e7;
		.top_content {
			width: 76%;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		img {
			height: 28px;
			width: 28px;
		}
		.container_left > :nth-child(1) {
			display: flex;
			align-items: center;
			margin: -1px 4px 0 0;
		}
		.container_left {
			display: flex;

			.con_title {
				display: flex;
				width: 100%;
				cursor: pointer;
				text-wrap-mode: nowrap;
			}
			.con_title > :nth-child(1) {
				line-height: 41px;
				font-size: 23px;
				font-weight: 500;
				color: #1d2129;
			}
			.con_title > :nth-child(2) {
				font-size: 16px;
				font-weight: 500;
				line-height: 32px;
				display: flex;
				align-items: end;
			}
		}
		.container_right {
			cursor: pointer;
			display: flex;
			font-size: 16px;
			font-weight: 400;
			text-align: center;
			text-wrap-mode: nowrap;

			img {
				cursor: pointer;
				width: 16px;
				height: 16px;
				margin-right: 8px;
			}
		}
	}

	.protocolReturn {
		width: 100%;
		height: 48px;
		display: flex;
		justify-content: center;
		box-shadow: 0px 4px 10px 0px #0000000f;
	}
	.protocolReturn > :nth-child(1) {
		width: 60%;
		display: flex;
		align-items: center;
		font-size: 16px;
		font-weight: 700;
		color: #86909c;
		cursor: pointer;
		div {
			margin-left: 5px;
			height: 100%;
			line-height: 50px;
		}
	}

	.containerBanner {
		width: 100%;
		height: calc(100% - 78px);
		display: flex;
		overflow: hidden;
		.c_right {
			width: calc(100% - 31%);
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			.c_content {
				width: 478px;
				height: 594px;
				box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.05);
				background: #ffffff;
				border-radius: 20px;
				position: relative;

				.close_button {
					position: absolute;
					top: 24px;
					right: 24px;
					width: 28px;
					height: 28px;
					cursor: pointer;
					display: flex;
					align-items: center;
					justify-content: center;
					z-index: 100;
					background: rgba(255, 255, 255, 0.8);
					border-radius: 50%;
					transition: background 0.3s ease;
				}

				.close_button:hover {
					background: rgba(255, 255, 255, 1);
				}

				.c_contentpad {
					width: 382px;
					padding: 48px 48px 56px 48px;
					height: calc(100% - 104px);
					display: flex;
					flex-direction: column;

					.title_contents {
						height: initial !important;
						// align-items: flex-start !important;
						margin-bottom: 40px !important;
						// padding-top: 0px !important;
					}
					.title_content {
						display: flex;
						flex-direction: column;
						gap: 40px;
						.con_title {
							display: flex;
							flex-direction: column;
							text-wrap-mode: nowrap;
						}

						.con_title > :nth-child(1) {
							font-family: 'PingFang SC', sans-serif;
							font-size: 24px;
							font-weight: 500;
							line-height: 1.5em;
							color: #1d2129;
							margin-bottom: 0;
						}
						.con_title > :nth-child(2) {
							font-family: 'PingFang SC', sans-serif;
							font-size: 24px;
							font-weight: 500;
							line-height: 1.5em;
							color: #1d2129;
						}

						.tab_switch {
							display: flex;
							align-items: center;
							gap: 36px;

							.tab_item {
								display: flex;
								flex-direction: column;
								gap: 4px;
								cursor: pointer;

								span {
									font-family: 'PingFang SC', sans-serif;
									font-size: 16px;
									font-weight: 500;
									line-height: 1.5em;
									color: #4E5969;
									transition: color 0.3s ease;
								}

								&.active span {
									color: #1868F1;
								}

								.tab_underline {
									width: 100%;
									height: 2px;
									background-color: #1868F1;
								}
							}
						}
					}
					.c_options {
						margin-top: -5px !important;
					}

					.c_optionss {
						margin-top: 75px !important;
					}
					.c_option {
						margin-top: 80px;
						display: flex;
						justify-content: center;
						align-items: center;
						gap: 18px;
						font-family: 'PingFang SC', sans-serif;
						font-size: 14px;
						font-weight: 400;
						line-height: 1.5714285714285714em;
						color: #4E5969;
					}
					.c_option > :nth-child(n) {
						cursor: pointer;
						transition: color 0.3s ease;
					}
					.c_option > :nth-child(n):hover {
						color: #1868F1;
					}

					.separator {
						color: #C9CDD4 !important;
						cursor: default !important;
					}

					.separator:hover {
						color: #C9CDD4 !important;
					}
				}
			}
		}
	}
	.containerBanner > :nth-child(1) {
		width: 31%;
		height: 100%;
		// overflow: hidden; /* 隐藏溢出的部分 */
		// height: 100vh;
		img {
			width: -webkit-fill-available;
			height: inherit;
			display: block;
			object-fit: cover;
		}
	}
	.accountlogin {
		width: 100%;
		margin-top: 32px;

		.verificationCode {
			cursor: pointer;
			font-family: 'PingFang SC', sans-serif;
			font-size: 14px;
			font-weight: 400;
			line-height: 1.5714285714285714em;
			color: #1868f1;
			text-align: right;
		}

		::v-deep .el-form-item {
			margin-bottom: 20px !important;
		}

		::v-deep .el-form-item:last-of-type {
			margin-bottom: 0 !important;
		}

		::v-deep .el-input__wrapper {
			border: 1px solid #E5E6EB;
			border-radius: 4px;
			padding: 10px 12px;
			height: auto;
			min-height: 40px;
		}

		::v-deep input {
			font-family: 'PingFang SC', sans-serif;
			font-size: 14px;
			font-weight: 400;
			line-height: 1.5714285714285714em;
			color: #1D2129;
			height: auto !important;
		}

		::v-deep input::placeholder {
			color: #86909C;
		}

		::v-deep button {
			width: 100%;
			height: 40px !important;
			border-radius: 4px;
			font-family: 'PingFang SC', sans-serif;
			font-size: 14px;
			font-weight: 500;
			line-height: 1.5714285714285714em;
			margin-top: 16px;
		}
	}
	.agreements {
		height: calc(100% - 146px);
		overflow-y: scroll;
		display: flex;
		justify-content: center;
		.agreement_bnanner {
			width: 60%;
			height: 100%;
		}
	}
}

.login_back {
	width: 220px;
	height: 470px;
	margin-right: 20px;
	// background-image: url('@/assets/homebj.png');
	background-size: 30% 100%;
	background-repeat: no-repeat;
	background-position: center;
}

.login_box {
	position: relative;
	width: 415px;
	height: 470px;
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
	flex-direction: column;
}

.el-form-item__label {
	width: 20px !important;
}

.form_box {
	margin-top: 10px;

	// margin-bottom: 100px;
	.btn_wangji {
		width: 100%;
		padding: 0 15px;
		font-size: 14px;
		box-sizing: border-box;
		color: rgba(0, 0, 0, 0.6);
	}
}

.register {
	width: 100%;
	height: 50px;
	margin: 0 auto;
	text-align: center;
	margin-top: 10px;
	box-sizing: border-box;
	border: 3.5px solid rgb(0, 0, 0);
	border-radius: 50px;

	background: rgb(255, 255, 255);
}

.login_input {
	width: 100%;
	height: auto;
	display: flex;

	::v-deep .el-input__wrapper {
		width: 300px;
		height: 50px;
		background-color: rgb(240, 240, 240);
		border-radius: 30px;
		box-shadow: none;
	}
}

.getCode {
	cursor: pointer;
}

.agree {
	font-size: 14px;
	font-weight: 700;
	text-align: center;
	color: #1868f1;
}
.checkAgree {
	margin-top: 16px;
	margin-bottom: 0;

	::v-deep .el-checkbox__label {
		font-family: 'PingFang SC', sans-serif;
		font-size: 12px;
		font-weight: 400;
		line-height: 1.5em;
		color: #4E5969;
	}

	::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
		background-color: #1868F1;
		border-color: #1868F1;
	}

	::v-deep .el-checkbox__inner {
		width: 14px;
		height: 14px;
		border-radius: 2px;
	}
}
@media only screen and (max-width: 600px) {
	.container_top {
		.top_content {
			width: 90% !important;
		}
	}
	.c_right {
		width: 100% !important;
		background: url(../../assets/platform.png);
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
	}
	.containerBanner > :nth-child(1) {
		display: none;
	}
	.c_content {
		width: 90% !important;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		// transform: scale(0.9);
		.c_contentpad {
			width: 90% !important;

			::v-deep .el-checkbox {
				white-space: normal;
			}
		}
	}
	.protocolReturn > :nth-child(1) {
		width: 100% !important;
	}
	.agreement_bnanner {
		width: 100% !important;
	}
}

.miyaoWrap {
	.no-autofill-pwd {
		-webkit-text-security: disc !important;
	}
	.no-auto {
		-webkit-text-security: none !important;
	}
}
</style>
