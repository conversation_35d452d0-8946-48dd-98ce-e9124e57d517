<template>
	<div class="bigbody">
		<el-row :gutter="20" style="max-height: 500px" v-if="tableData">
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					REITs产品投资/估值逻辑>>
					<el-divider class="divider" />
				</el-text>

				<el-text tag="p" class="mx-1 con">
					2023年3月之后，消费基础设施纳入公募REITs的试点范围。未来，纳入公募REITs范围内的不动产种类将越来越多。截至2023
					年年底，我国商业不动产存量市值约35~40万亿元。
				</el-text>
				<el-text tag="p" class="mx-1 con">
					随着政策的进一步放开，未来，权益性的商业不动产证券化率将达到15%以上，市场潜在规模在4~6万亿之间。
					目前大部分投资人将其视为固定收益类产品，投资更加关注主体评级和增信措施。专业机构给出资产估值，确定融资价值后，投资人按照固定的价格认购，并收取每期回报，到期时大部分以本金赎回形式退出。后续产品的发展将以权益性为主，即依托对资产的良好的运营，摆脱主体和增信。
				</el-text>
				<el-text tag="p" class="mx-1 con">
					我国目前的10年期国债回报率在2.6-2.8%之间、REITs分红收益一般在10年期国债150bps左右、且基建REITs低于各类别REITs
					150bps，基建REITs回报率4%较符合国际规律。对于投资者也具有一定吸引力。
				</el-text>
			</el-col>
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					投资风险补偿水平>>
					<el-divider class="divider" />
				</el-text>
				<el-text class="mx-1 con" tag="p">
					从投资角度出发，不动产类的REITs的风险识别关键在于：（1）法律权属明确，不属于负面清单；可以产生独立、可预测、特定化的财产权利；（2）基础资产不附带其他抵质押担保负担或权利限制，转让无瑕疵；（3）现金流的预测合理。
				</el-text>
				<div>
					<el-image fit="scale-down" style="height: 250px; padding-top: 20px; width: 50%" :src="leftImgUrl" />
					<el-image fit="scale-down" style="height: 250px; padding-top: 20px; width: 50%" :src="rightImgUrl" />
				</div>

				<el-text class="mx-1 con" tag="p"> 从左图可以看出，3年期债项AAA级ABS产品的发行利率略低于同期发行的非公开公司债券。 </el-text>
				<el-text class="mx-1 con" tag="p">
					从右图来看，3年期主体AAA级PPN发行利率高于非公开公司债约20-30BP；二者发行利率均高于公开公司债，主要因为当前企业ABS仅能非公开发行，故发行利率含有流动性溢价。
				</el-text>
			</el-col>
		</el-row>
		<el-row :gutter="20">
			<el-col :span="12">
				<el-text tag="p" class="Title">
					产品定价>>
					<el-divider class="divider" />
				</el-text>
				<div style="padding-top: 15px">
					<el-text tag="span" class="mx-1 con">
						根据《{{ name }}单价评估报告》，{{ name }}的可比价值系数为{{ price }}。 根据术木智能EBITDA系数模型，结合{{ name }}的面积、运维情况，{{
							name
						}}EBITDA系数为{{ EBIDT }}。 根据术木智能金融项目风险评估模型，{{ name }}权益型金融产品参数如下。</el-text
					>
					<el-table :data="tableData" border :span-method="spanMethod" style="width: 100%; margin: 10px 0">
						<!-- 证券评级列 -->
						<el-table-column prop="grade" label="证券评级"></el-table-column>

						<!-- 可比价值系数列 -->
						<el-table-column prop="prices" label="可比价值系数"></el-table-column>

						<!-- 最高规模系数列 -->
						<el-table-column prop="scale" label="最高规模系数"></el-table-column>

						<!-- 杠杆率列 -->
						<el-table-column prop="leverage" label="杠杆率"></el-table-column>
					</el-table>

					<el-text tag="span" class="mx-1 con">
						发行成本参照同时期市场发行标准化产品的利息或分配率。规模系数表明，在权益产品上，相比于{{ city }}场上其他购物中心发行的标准化金融产品，{{
							name
						}}的定价在{{ smallTotal }}-{{ bigTotal }}倍之间。</el-text
					>
				</div>
			</el-col>
			<el-col :span="12"></el-col>
		</el-row>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http';
import { useRoute } from 'vue-router';
import leftImg from '@/assets/a1.png';
import rightImg from '@/assets/a2.png';

const leftImgUrl = ref(leftImg);
const rightImgUrl = ref(rightImg);
const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
	if (columnIndex === 1) {
		if (rowIndex % 3 === 0) {
			return {
				rowspan: 3,
				colspan: 1,
			};
		} else {
			return {
				rowspan: 0,
				colspan: 0,
			};
		}
	}
};
const route = useRoute();
const tableData = ref([]);
const name = ref('');
const price = ref('');
const EBIDT = ref('');
const smallTotal = ref(0);
const bigTotal = ref(0);
const city = ref('');
onMounted(async () => {
	const res = await http.get('/api/a_maps.do?act=ratingPassDetailsAction', {
		params: {
			action: 'finance',
			maps_id: route.params.id,
		},
	});
	const table = res.list.map((item) => {
		return [
			{
				grade: 'AAA',
				prices: item.comparable_price,
				scale: item.max_scale_AAA,
				leverage: item.lever_AAA,
			},
			{
				grade: 'AA+',
				prices: '',
				scale: item['max_scale_AA+'],
				leverage: item['lever_AA+'],
			},
			{
				grade: 'AA',
				prices: '',
				scale: item.max_scale_AA,
				leverage: item.lever_AA,
			},
		];
	});
	city.value = res.list[0].city;
	name.value = res.list[0].building_name;
	price.value = res.list[0].comparable_price;
	tableData.value = [].concat(...table);
	smallTotal.value = Math.round(res.list[0].max_scale_AAA * 100) / 100;
	bigTotal.value = Math.round(res.list[0].max_scale_AA * 100) / 100;

	EBIDT.value = res.list[0].EBIDTA;
});
</script>

<style lang="less" scoped>
.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
}
.divider {
	flex-grow: 1;
	margin: 16px 0 16px 8px;
	border-color: #3483ce;
}

.con {
	padding-top: 20px;
	line-height: 1.5;
	font-size: 14px;
}
.el-table:deep(.cell) {
	color: #000;
}
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 36px;
}
img {
	margin-top: 15px;
}
</style>
