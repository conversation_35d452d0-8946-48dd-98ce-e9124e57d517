<template>
	<div class="comparison_box">
		<div class="common_wrap">
			<div class="left_empty_wrap" v-if="tableDataLeft.length == 0">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择资产
				</arco-button>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0" class="left_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('left')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="buildingUniqueCode"
						:columns="tableColumns"
						:data="tableDataLeft"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="110" ellipsis tooltip>
								<template #cell="{ record }">
									<span
										v-if="
											$utils.isEmpty(sixRingDateList[0].updateDataValueList) ||
											(sixRingDateList &&
												sixRingDateList.length > 0 &&
												sixRingDateList[0].updateDataValueList.filter((item) => item.fieldName == 'absoluteValue').length == 0)
										"
										style="color: #1868f1; cursor: pointer"
										>{{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }}</span
									>
									<el-tooltip v-else effect="customized" :show-arrow="false">
										<span style="color: #1868f1; cursor: pointer">{{
											record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : ''
										}}</span>
										<template #content>
											<div class="tooltip_content">
												<div class="label">单价</div>
												<div class="update_item">
													<span class="val"
														>{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').oldValue }}元</span
													>
													<icon-sync style="color: #86909c" size="16" />
													<span class="date">{{
														sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').oldValueCreateTime
													}}</span>
												</div>
												<div class="update_item">
													<span class="val"
														>{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').newValue }}元</span
													>
													<icon-sync style="color: #86909c" size="16" />
													<span class="date">{{
														sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').newValueCreateTime
													}}</span>
												</div>
											</div>
										</template>
									</el-tooltip>
								</template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
				<div class="desc_wrap" v-if="sixRingDateList && sixRingDateList.length > 0">
					<img :src="descBg" class="bg" />
					<div class="desc">
						{{ sixRingDateList[0].buildingName }}的单价为
						<span
							v-if="
								$utils.isEmpty(sixRingDateList[0].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 0 &&
									sixRingDateList[0].updateDataValueList.filter((item) => item.fieldName == 'absoluteValue').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[0].absoluteValue)) }}元/㎡</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[0].absoluteValue)) }}元/㎡</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">单价</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').oldValue }}元</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').newValue }}元</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template>
						</el-tooltip>
						，周边配套为<span
							v-if="
								$utils.isEmpty(sixRingDateList[0].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 0 &&
									sixRingDateList[0].updateDataValueList.filter((item) => item.fieldName == 'assorted').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[0].assorted)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[0].assorted)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">周边配套</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'assorted').oldValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'assorted').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'assorted').newValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'assorted').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>，维护情况为<span
							v-if="
								$utils.isEmpty(sixRingDateList[0].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 0 &&
									sixRingDateList[0].updateDataValueList.filter((item) => item.fieldName == 'maintenance').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[0].maintenance)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[0].maintenance)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">维护情况</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'maintenance').oldValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'maintenance').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'maintenance').newValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'maintenance').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>，商业活力为<span
							v-if="
								$utils.isEmpty(sixRingDateList[0].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 0 &&
									sixRingDateList[0].updateDataValueList.filter((item) => item.fieldName == 'businessDynamism').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[0].businessDynamism)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[0].businessDynamism)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">商业活力</div>
									<div class="update_item">
										<span class="val">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'businessDynamism').oldValue
										}}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'businessDynamism').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'businessDynamism').newValue
										}}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'businessDynamism').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>，人均消费能力为<span
							v-if="
								$utils.isEmpty(sixRingDateList[0].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 0 &&
									sixRingDateList[0].updateDataValueList.filter((item) => item.fieldName == 'spendingPower').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[0].spendingPower)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[0].spendingPower)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">人均消费能力</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'spendingPower').oldValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'spendingPower').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'spendingPower').newValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'spendingPower').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>，区域潜力为<span
							v-if="
								$utils.isEmpty(sixRingDateList[0].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 0 &&
									sixRingDateList[0].updateDataValueList.filter((item) => item.fieldName == 'regionalPotential').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[0].regionalPotential)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[0].regionalPotential)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">区域潜力</div>
									<div class="update_item">
										<span class="val">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'regionalPotential').oldValue
										}}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'regionalPotential').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'regionalPotential').newValue
										}}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[0].updateDataValueList.find((item) => item.fieldName == 'regionalPotential').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>。
					</div>
					<div class="copy" @click="handlerCopy(1)">复制</div>
				</div>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0 && tableDataRight.length == 0" class="right_empty_wrap">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择对比资产
				</arco-button>
			</div>
			<div v-if="tableDataRight && tableDataRight.length > 0" class="right_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('right')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="buildingUniqueCode"
						:columns="tableColumns"
						:data="tableDataRight"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="110" ellipsis tooltip>
								<template #cell="{ record }">
									<span
										v-if="
											$utils.isEmpty(sixRingDateList[1].updateDataValueList) ||
											(sixRingDateList &&
												sixRingDateList.length > 1 &&
												sixRingDateList[1].updateDataValueList.filter((item) => item.fieldName == 'absoluteValue').length == 0)
										"
										style="color: #1868f1; cursor: pointer"
										>{{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }}</span
									>
									<el-tooltip v-else effect="customized" :show-arrow="false">
										<span style="color: #1868f1; cursor: pointer">{{
											record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : ''
										}}</span>
										<template #content>
											<div class="tooltip_content">
												<div class="label">单价</div>
												<div class="update_item">
													<span class="val"
														>{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').oldValue }}元</span
													>
													<icon-sync style="color: #86909c" size="16" />
													<span class="date">{{
														sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').oldValueCreateTime
													}}</span>
												</div>
												<div class="update_item">
													<span class="val"
														>{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').newValue }}元</span
													>
													<icon-sync style="color: #86909c" size="16" />
													<span class="date">{{
														sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').newValueCreateTime
													}}</span>
												</div>
											</div>
										</template>
									</el-tooltip>
								</template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
				<div class="desc_wrap" v-if="sixRingDateList && sixRingDateList.length > 1">
					<img :src="descBg" class="bg" />
					<div class="desc">
						{{ sixRingDateList[1].buildingName }}的单价为
						<span
							v-if="
								$utils.isEmpty(sixRingDateList[1].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 1 &&
									sixRingDateList[1].updateDataValueList.filter((item) => item.fieldName == 'absoluteValue').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[1].absoluteValue)) }}元/㎡</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[1].absoluteValue)) }}元/㎡</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">单价</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').oldValue }}元</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').newValue }}元</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'absoluteValue').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template>
						</el-tooltip>
						，周边配套为<span
							v-if="
								$utils.isEmpty(sixRingDateList[1].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 1 &&
									sixRingDateList[1].updateDataValueList.filter((item) => item.fieldName == 'assorted').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[1].assorted)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[1].assorted)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">周边配套</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'assorted').oldValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'assorted').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'assorted').newValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'assorted').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>，维护情况为<span
							v-if="
								$utils.isEmpty(sixRingDateList[1].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 1 &&
									sixRingDateList[1].updateDataValueList.filter((item) => item.fieldName == 'maintenance').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[1].maintenance)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[1].maintenance)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">维护情况</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'maintenance').oldValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'maintenance').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'maintenance').newValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'maintenance').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>，商业活力为<span
							v-if="
								$utils.isEmpty(sixRingDateList[1].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 1 &&
									sixRingDateList[1].updateDataValueList.filter((item) => item.fieldName == 'businessDynamism').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[1].businessDynamism)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[1].businessDynamism)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">商业活力</div>
									<div class="update_item">
										<span class="val">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'businessDynamism').oldValue
										}}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'businessDynamism').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'businessDynamism').newValue
										}}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'businessDynamism').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>，人均消费能力为<span
							v-if="
								$utils.isEmpty(sixRingDateList[1].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 1 &&
									sixRingDateList[1].updateDataValueList.filter((item) => item.fieldName == 'spendingPower').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[1].spendingPower)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[1].spendingPower)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">人均消费能力</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'spendingPower').oldValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'spendingPower').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{ sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'spendingPower').newValue }}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'spendingPower').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>，区域潜力为<span
							v-if="
								$utils.isEmpty(sixRingDateList[1].updateDataValueList) ||
								(sixRingDateList &&
									sixRingDateList.length > 1 &&
									sixRingDateList[1].updateDataValueList.filter((item) => item.fieldName == 'regionalPotential').length == 0)
							"
							class="number"
							>{{ formattedMoney(handleNumber(sixRingDateList[1].regionalPotential)) }}</span
						>
						<el-tooltip v-else effect="customized" :show-arrow="false">
							<span class="number">{{ formattedMoney(handleNumber(sixRingDateList[1].regionalPotential)) }}</span>
							<template #content>
								<div class="tooltip_content">
									<div class="label">区域潜力</div>
									<div class="update_item">
										<span class="val">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'regionalPotential').oldValue
										}}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'regionalPotential').oldValueCreateTime
										}}</span>
									</div>
									<div class="update_item">
										<span class="val">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'regionalPotential').newValue
										}}</span>
										<icon-sync style="color: #86909c" size="16" />
										<span class="date">{{
											sixRingDateList[1].updateDataValueList.find((item) => item.fieldName == 'regionalPotential').newValueCreateTime
										}}</span>
									</div>
								</div>
							</template> </el-tooltip
						>。
					</div>
					<div class="copy" @click="handlerCopy(2)">复制</div>
				</div>
			</div>
		</div>

		<div class="chart_wrap">
			<div class="header_wrap" style="margin-bottom: 16px">
				<span class="line"></span>
				<span class="title">楼宇概况</span>
			</div>
			<buildingView ref="buildingViewRef" :buildData="multipleSelection"></buildingView>
		</div>

		<div class="chart_wrap">
			<div class="header_wrap">
				<span class="line"></span>
				<span class="title">价值分析</span>
				<div v-if="tableDataLeft.length > 0" style="display: flex; align-items: center; margin-right: 20px">
					<div class="circle_left"></div>
					{{ tableDataLeft.length > 0 ? tableDataLeft[0]?.buildingName : '' }}
				</div>
				<div v-if="tableDataRight.length > 0" style="display: flex; align-items: center">
					<div class="circle_right"></div>
					{{ tableDataRight.length > 0 ? tableDataRight[0]?.buildingName : '' }}
				</div>
			</div>
			<div class="echars_main">
				<div class="box_">
					<div style="width: 100%; height: 100%">
						<div class="title1">
							<div class="title">价格趋势(红色背景月份为预测数据)</div>
							<div v-if="valueDateList.length == 0" class="download">
								<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
							</div>
							<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[0])">
								<icon-download class="download_icon" size="16" />
							</div>
						</div>
						<div v-if="valueDateList && valueDateList.length > 0" ref="chartRef" class="chart" style="width: 100%; height: 395px"></div>
						<!-- <Valuecomparison ref="valueChartRef" v-if="valueDateList && valueDateList.length > 0" :valueDateList="valueDateList"></Valuecomparison> -->
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="box_">
					<div style="width: 100%; height: 100%">
						<div class="title1">
							<div class="title">维度分析</div>
							<div v-if="sixRingDateList.length == 0" class="download">
								<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
							</div>
							<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[0])">
								<icon-download class="download_icon" size="16" />
							</div>
						</div>
						<echartBox v-if="sixRingDateList && sixRingDateList.length > 0" :sixRingDateList="sixRingDateList" ref="echartBox_ref"></echartBox>
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
			<div class="summary_wrap" v-if="handlerBtnBox().length > 0">
				<img :src="summary_bg" class="bg" />
				<img :src="summary_icon" class="icon" />
				<div class="summary">
					{{ handlerBtnBox() }}
				</div>
				<div class="copy" @click="handlerCopy(3)">复制</div>
			</div>
		</div>
	</div>
	<buildSelect key="all" v-model="dialogTableVisible" :selectedData="multipleSelection" :maxSelectNum="2" @confirm="handleBuildConfirm"></buildSelect>
	<!-- <buildSelect key="single_left" v-model="dialogSingleLeftVisible" :maxSelectNum="1" @confirm="handleBuildLeftConfirm"></buildSelect>
	<buildSelect key="single_right" v-model="dialogSingleRightVisible" :maxSelectNum="1" @confirm="handleBuildRightConfirm"></buildSelect> -->
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>

<script setup>
import buildingView from './buildingView.vue';
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import { getBuildingListByMultiCondition, getComparativeValue, getDictList } from '@/api/syt.js';
import Valuecomparison from './Valuecomparison.vue';
import getReport from '../../../../component/getReport/index.vue';
import echartBox from './echarts_box.vue';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import descBg from '@/assets/images/shangYutong/buildInfo/desc_bg.png';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import summary_bg from '@/assets/images/shangYutong/buildInfo/summary_bg.png';
import summary_icon from '@/assets/images/shangYutong/buildInfo/summary_icon.png';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import buildSelect from '@/component/buildSelect/index.vue';
import * as echarts from 'echarts';
import { IconSync } from '@arco-design/web-vue/es/icon';
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const echartBox_ref = ref();
const buildingViewRef = ref();
const valueChartRef = ref();
const getReportRef = ref();
const loading = ref();
const dialogTableVisible = ref(false); //对话框显示
const dialogSingleLeftVisible = ref(false); //对话框显示
const dialogSingleRightVisible = ref(false); //对话框显示
const rate = ref([]);
const buildingTypes = ref([]);
const box_copyObj = ref('');
const box_copyObjTwo = ref('');
const multipleSelection = ref([]);
// const multipleSelection = ref([
// 	{
// 		id: '1833701603456532482',
// 	},
// 	{ id: '1833706846806294530' },
// ]);
const tableData2 = ref([]);
const tableDataLeft = ref([]);
const tableDataRight = ref([]);
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const tableColumns = [
	{
		title: '资产名称',
		dataIndex: 'buildingName',
		width: '150',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '资产类型',
		dataIndex: 'buildingType',
		width: '100',
	},
	{
		title: '地址',
		dataIndex: 'street',
		width: '300',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '建筑面积',
		dataIndex: 'buildingSize',
		width: '100',
	},
	{
		title: '维护情况',
		dataIndex: 'maintenance',
		width: '100',
	},
	{
		title: '单价',
		dataIndex: 'absoluteValue',
		width: '100',
	},
];

const chartRef = ref();
let chartInstance = null;

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
});
onUnmounted(() => {
	if (chartInstance) chartInstance.dispose();
});
// 更新图表配置
const updateChart = () => {
	let newarr = [];
	let dealPrice1 = [];
	let dealPrice2 = [];
	let forecastList = [];
	if (valueDateList.value[0]) {
		valueDateList.value[0].dealPrice.map((item, index) => {
			dealPrice1.push((item.dealPrice / 10000).toFixed(2));
			newarr.push(item.month);
			if (item.isPredictive) {
				forecastList.push([
					{
						xAxis: valueDateList.value[0].dealPrice[index - 1].month,
					},
					{
						xAxis: item.month,
					},
				]);
			}
		});
		if (valueDateList.value.length > 1) {
			if (valueDateList.value[1]) {
				valueDateList.value[1].dealPrice.map((item) => {
					dealPrice2.push((item.dealPrice / 10000).toFixed(2));
				});
			} else {
				dealPrice2 = [];
			}
		} else {
			dealPrice2 = [];
		}
	} else {
		dealPrice1 = [];
	}
	const options = {
		grid: {
			left: '8%',
			top: '14%',
			right: '5%',
			bottom: '10%',
		},
		color: ['#249eff', '#37e2e2'],
		xAxis: {
			boundaryGap: false,
			type: 'category',
			data: newarr,
			axisTick: { show: true }, // 隐藏刻度线
			axisLabel: {
				interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
			},
		},
		yAxis: { type: 'value', name: '单价(万元/㎡)' },
		series: [
			{
				name: valueDateList.value[0]?.buildingName,
				data: dealPrice1,
				markArea: {
					itemStyle: {
						color: 'rgba(255, 173, 177, 0.4)',
					},
					data: forecastList,
					// [
					// [
					//   {

					//     xAxis: '07:30'
					//   },
					//   {
					//     xAxis: '10:00'
					//   }
					// ],
					// [
					//   {
					//     name: 'Evening Peak',
					//     xAxis: '17:30'
					//   },
					//   {
					//     xAxis: '21:15'
					//   }
					// ]
					// ]
				},
				smooth: true,
				type: 'line',
				symbol: 'circle', // 数据点样式
				symbolSize: 10,
				showSymbol: false,
				emphasis: {
					focus: 'series',
					itemStyle: { showSymbol: true, color: '#249eff', symbolSize: 16, borderColor: '#D3ECFF', borderWidth: 2 },
				},
				lineStyle: {
					color: '#249eff', // 设置第一条折线的颜色
				},
			},
			{
				name: valueDateList.value[1]?.buildingName || '',
				data: dealPrice2,
				smooth: true,
				type: 'line',
				symbol: 'circle', // 数据点样式
				symbolSize: 10,
				showSymbol: false,
				emphasis: {
					focus: 'series',
					itemStyle: { showSymbol: true, color: '#37e2e2', symbolSize: 16, borderColor: '#E8FFFB', borderWidth: 2 },
				},
				lineStyle: {
					color: '#37e2e2', // 设置第一条折线的颜色
				},
			},
		],
		tooltip: {
			// 悬浮提示框
			trigger: 'axis',
			backgroundColor: 'rgba(244, 247, 252, .8)',
			borderColor: 'transparent',
			borderRadius: 4,
			formatter: (params) => {
				let str = '';
				params.forEach((item) => {
					str += `<div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
				<div style="width:8px;height:8px;background-color:${item.color};margin-right: 8px;border-radius: 50%;">
				</div>
				<div style="width:100%;display:flex;justify-content: space-between;align-items: center;">
					<div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
					${item.seriesName}
				</div>
				<div style="font-size: 14px; color: #1D2129; font-weight: 600">
					${item.data}万元/㎡
				</div>
				</div>
				</div>`;
				});
				return `
			<div style="font-size: 14px; color: #1D2129; font-weight: 600;margin-bottom: 4px">${params[0].name}</div>
			<div style="display: flex;
			flex-direction: column;
			gap: 4px;">
			${str}
			</div>
			`;
			},
		},
	};
	chartInstance.setOption(options);
};
//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}
//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}

	getComparativeValue({ buildingIds: obj.ids }).then((res) => {
		valueDateList.value = [];
		sixRingDateList.value = [];
		tableDataLeft.value = [];
		tableDataRight.value = [];
		if (res.code === 200) {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			valueDateList.value = res.data;
			sixRingDateList.value = res.data;
			if (res.data && res.data.length > 0) {
				nextTick(() => {
					// valueChartRef.value.init();
					chartInstance = echarts.init(chartRef.value);
					updateChart();
					echartBox_ref.value.echartsData(sixRingDateList.value);
				});
			}
			if (res.data && res.data.length == 1) {
				tableDataLeft.value.push(obj.arr[0]);
			} else if (res.data && res.data.length == 2) {
				tableDataLeft.value.push(obj.arr[0]);
				tableDataRight.value.push(obj.arr?.[1]);
			}
			multipleSelection.value = obj.arr;
			nextTick(() => {
				if (buildingViewRef.value) {
					buildingViewRef.value.getData();
				}
			});
			loading.value.close();
		} else {
			multipleSelection.value = [];
		}
	});
}

// 复制
function handlerCopy(type) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(type === 1 ? handlerLineBox(1) : type === 2 ? handlerLineBox(2) : handlerBtnBox())
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = type === 1 ? handlerLineBox(1) : type === 2 ? handlerLineBox(2) : handlerBtnBox();
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

function handlerBtnBox() {
	const [obj1, obj] = sixRingDateList.value;

	if (!obj || !obj1) return '';

	let ratio;
	let stronger, weaker;
	let prefix = '';
	let suffix = '强于';

	// 判断哪个资产单价更高
	if (obj.absoluteValue > obj1.absoluteValue) {
		ratio = obj.absoluteValue / obj1.absoluteValue;
		stronger = obj;
		weaker = obj1;
		prefix = `${stronger.buildingName}的单价是${weaker.buildingName}的`;
	} else {
		ratio = obj1.absoluteValue / obj.absoluteValue;
		stronger = obj1;
		weaker = obj;
		prefix = `${stronger.buildingName}的单价是${weaker.buildingName}的`;
		suffix = '强于';
	}

	const ratioText = `${prefix}${formattedMoney(handleNumber(ratio))}倍，`;

	const dimensions = [
		{ key: 'assorted', label: '周边配套' },
		{ key: 'maintenance', label: '维护情况' },
		{ key: 'evaluation', label: '评估结果' },
		{ key: 'businessDynamism', label: '商业活力' },
		{ key: 'spendingPower', label: '人均消费能力' },
		{ key: 'regionalPotential', label: '区域潜力' },
	];

	const matchedLabels = [];

	dimensions.forEach(({ key, label }) => {
		if (stronger[key] > weaker[key]) {
			matchedLabels.push(label);
		}
	});

	if (matchedLabels.length === 0) return '';

	const details = `${stronger.buildingName}的${matchedLabels.join('、')}${suffix}${weaker.buildingName}。`;

	return ratioText + details;
}

// 比较
function handlerLineBox(type) {
	if (type === 1) {
		let obj = null;
		if (tableDataLeft.value.length > 0) {
			if (tableDataLeft.value?.[0]?.buildingName === sixRingDateList.value[0].buildingName) {
				obj = sixRingDateList.value[0];
			} else {
				obj = sixRingDateList?.value?.[1];
			}
		}

		if (!obj) return '';

		let name =
			obj.buildingName +
			'的单价为' +
			formattedMoney(handleNumber(obj.absoluteValue)) +
			'元/㎡' +
			'，周边配套为' +
			formattedMoney(handleNumber(obj.assorted)) +
			'，维护情况为' +
			formattedMoney(handleNumber(obj.maintenance)) +
			'，商业活力为' +
			formattedMoney(handleNumber(obj.businessDynamism)) +
			'，人均消费能力为' +
			formattedMoney(handleNumber(obj.spendingPower)) +
			'，区域潜力为' +
			formattedMoney(handleNumber(obj.regionalPotential)) +
			'。';
		if (name) {
			box_copyObj.value = name;
		}
		return name;
	} else {
		let obj1 = null;
		if (tableDataRight.value.length > 0) {
			if (tableDataRight.value?.[0]?.buildingName === sixRingDateList.value?.[0]?.buildingName) {
				obj1 = sixRingDateList.value[0];
			} else {
				obj1 = sixRingDateList?.value?.[1] ? sixRingDateList.value[1] : undefined;
			}
		}
		if (!obj1) return '';
		let name =
			obj1.buildingName +
			'的单价为' +
			formattedMoney(handleNumber(obj1.absoluteValue)) +
			'元/㎡' +
			'，周边配套为' +
			formattedMoney(handleNumber(obj1.assorted)) +
			'，维护情况为' +
			formattedMoney(handleNumber(obj1.maintenance)) +
			'，评估结果为' +
			formattedMoney(handleNumber(obj1.evaluation)) +
			'，商业活力为' +
			formattedMoney(handleNumber(obj1.businessDynamism)) +
			'，人均消费能力为' +
			formattedMoney(handleNumber(obj1.spendingPower)) +
			'，区域潜力为' +
			formattedMoney(handleNumber(obj1.regionalPotential)) +
			'。';

		if (name) {
			box_copyObjTwo.value = name;
		}
		return name;
	}
}

// 确定
const valueDateList = ref([]);
const sixRingDateList = ref([]);

const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		tableDataLeft.value = [];
		tableDataRight.value = [];
		valueDateList.value = [];
		sixRingDateList.value = [];
		emit('handleBuildingId', { ids: null, arr: [] });
		if (buildingViewRef.value) {
			buildingViewRef.value.getData(1);
		}
		return;
	} else {
		let ids = multipleSelection.value.map((item) => item.id).join(',');
		getComparativeValue({ buildingIds: ids }).then((res) => {
			valueDateList.value = [];
			sixRingDateList.value = [];
			tableDataLeft.value = [];
			tableDataRight.value = [];
			if (res.code === 200) {
				emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
				valueDateList.value = res.data;
				sixRingDateList.value = res.data;
				tableData2.value = multipleSelection.value;
				// 两个数组 一个数组里面放一个
				res.data.forEach((item, index) => {
					tableData2.value.forEach((element) => {
						if (item.buildingUniqueCode === element.uniqueCode && index === 0) {
							tableDataLeft.value.push(element);
						}
						if (item.buildingUniqueCode === element.uniqueCode && index === 1) {
							tableDataRight.value.push(element);
						}
					});
				});

				if (buildingViewRef.value) {
					buildingViewRef.value.getData();
				}
				if (valueDateList.value && valueDateList.value.length > 0) {
					nextTick(() => {
						// valueChartRef.value.init();
						chartInstance = echarts.init(chartRef.value);
						updateChart();
					});
				}
				if (sixRingDateList.value && sixRingDateList.value.length > 0) {
					nextTick(() => {
						echartBox_ref.value.echartsData(sixRingDateList.value);
					});
				}
				dialogTableVisible.value = false;
			} else {
				multipleSelection.value = [];
			}
			dialogTableVisible.value = false;
		});
	}
};

// 获取字典
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	save();
}
function handleBuildLeftConfirm(data) {
	multipleSelection.value[0] = data[0];
	save();
}
function handleBuildRightConfirm(data) {
	multipleSelection.value[1] = data[0];
	save();
}
function clear(type) {
	// if (type == 'left') {
	// 	tableDataLeft.value = [];
	// 	valueDateList.value[0] = undefined;
	// 	sixRingDateList.value[0] = undefined;
	// } else {
	// 	tableDataRight.value = [];
	// 	valueDateList.value[1] = undefined;
	// 	sixRingDateList.value[1] = undefined;
	// }
	// nextTick(() => {
	// 	valueChartRef.value.init();
	// 	echartBox_ref.value.echartsData(sixRingDateList.value);
	// });
	if (type == 'left') {
		tableDataLeft.value = [];
		multipleSelection.value.shift();
		// valueDateList.value.shift();
		// sixRingDateList.value.shift();
	} else {
		tableDataRight.value = [];
		multipleSelection.value.pop();
		// valueDateList.value.pop();
		// sixRingDateList.value.pop();
	}
	save();
	// nextTick(() => {
	// 	valueChartRef.value.init();
	// 	echartBox_ref.value.echartsData(sixRingDateList.value);
	// });

	// if (sixRingDateList.value.length > 0) {
	// 	sixRingDateList.value.map((item, inde) => {
	// 		if (item.buildingName == row.buildingName) {
	// 			sixRingDateList.value.splice(inde, 1);
	// 			echartBox_ref.value.echartsData(sixRingDateList.value);
	// 		}
	// 	});
	// }
}
</script>
<style lang="less">
.el-popper.is-customized {
	padding: 12px 8px;
	background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%);
	/* backdrop-filter: blur(10px); */
	box-shadow: 0px 4px 16px 0px #1d21290f;
	.tooltip_content {
		display: flex;
		flex-direction: column;
		gap: 4px;
		color: #1d2129;
		.label {
			font-size: 14px;
			font-weight: 600;
			line-height: 22px;
		}
		.update_item {
			background-color: #fff;
			border-radius: 4px;
			padding: 9px 12px;
			font-size: 14px;
			font-weight: 400;
			line-height: 22px;
			display: flex;
			align-items: center;
			.val {
				margin-right: 20px;
				font-weight: 500;
			}
			.date {
				margin-left: 4px;
				color: #86909c;
			}
		}
	}
}
</style>
<style lang="less" scoped>
.comparison_box {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.common_wrap {
		padding: 20px 16px;
		background-color: #fff;
		display: flex;
		gap: 16px;
		border-radius: 0px 4px 4px 4px;
		.left_empty_wrap,
		.left_content_wrap,
		.right_empty_wrap,
		.right_content_wrap {
			flex: 1;
		}
		.left_empty_wrap,
		.right_empty_wrap {
			border: 1px solid #e5e6eb;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 4px;
			padding: 54px 0;
			.icon {
				width: 64px;
				height: 64px;
			}
		}
		.title_wrap {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;
		}
		.table_wrap {
		}
		.desc_wrap {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20px;
			border-radius: 4px;
			overflow: hidden;
			margin-top: 16px;
			.desc {
				max-width: 660px;
				z-index: 9;
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
				color: #1d2129;
				.number {
					color: #1868f1;
					cursor: pointer;
				}
			}
			.copy {
				z-index: 9;
				padding: 5px 16px;
				border: 1px solid #1868f1;
				color: #1868f1;
				background: #e8f3ff;
				border-radius: 4px;
				cursor: pointer;
			}
			.bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}
	}
	.chart_wrap {
		flex: 1;
		border-radius: 4px;
		margin-top: 16px;
		padding: 20px 16px 16px 16px;
		background-color: #fff;
		.header_wrap {
			display: flex;
			align-items: center;
			.line {
				width: 4px;
				height: 14px;
				background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
				border-radius: 4px;
			}
			.title {
				color: #1d2129;
				font-size: 20px;
				font-weight: 600;
				margin-left: 8px;
				margin-right: 20px;
				line-height: 28px;
			}
			.circle_left,
			.circle_right {
				width: 8px;
				height: 8px;
				border-radius: 50%;
				margin-right: 8px;
			}
			.circle_left {
				background-color: #249eff;
			}
			.circle_right {
				background-color: #37e2e2;
			}
		}
		.echars_main {
			margin-top: 16px;
			width: 100%;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;
			gap: 16px;
			.box_ {
				flex: 1;
				height: 442px;
				border: 1px solid #e5e6eb;
				border-radius: 4px;
				box-sizing: border-box;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-direction: column;
				.title1 {
					box-sizing: border-box;
					padding: 0 20px;
					width: 100%;
					height: 48px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #f7f8fa;
					border-bottom: 1px solid #e5e6eb;
					.title {
						font-size: 16px;
						font-weight: 600;
						color: #1d2129;
					}
					.download {
						color: #1868f1;
						.download_icon {
							font-size: 16px;
							cursor: pointer;
						}
					}
				}
				.empty_wrap {
					height: 100%;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: 400;
					color: #86909c;
					img {
						width: 80px;
						height: 80px;
					}
				}
			}
		}
		.summary_wrap {
			margin-top: 16px;
			position: relative;
			display: flex;
			align-items: center;
			height: 62px;
			padding: 0 20px;
			.bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
			.icon {
				width: 40px;
				height: 40px;
				z-index: 99;
				margin-right: 12px;
			}
			.summary {
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
				color: #1d2129;
				z-index: 99;
			}
			.copy {
				z-index: 99;
				padding: 5px 16px;
				border: 1px solid #1868f1;
				color: #1868f1;
				background: #e8f3ff;
				border-radius: 4px;
				cursor: pointer;
				position: absolute;
				right: 20px;
			}
		}
	}

	.arco-btn-size-medium {
		border-radius: 4px;
	}
}
</style>
