Index: src/App.vue
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><template>\r\n\t<el-config-provider :locale=\"zhCn\">\r\n\t\t<router-view />\r\n\t</el-config-provider>\r\n\t<smPermissionPop ref=\"smPermissionPop\" class=\"smPermissionPop\"></smPermissionPop>\r\n</template>\r\n\r\n<script setup>\r\nimport smPermissionPop from './component/smPermissionPop/index.vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport zhCn from 'element-plus/es/locale/lang/zh-cn';\r\nimport { ref } from 'vue';\r\nimport { useRouter } from 'vue-router';\r\nconst errorNotification = ref({\r\n\tpending: false,\r\n\ttimeout: 1000,\r\n});\r\nconst router = useRouter();\r\nrouter.beforeEach((to, from, next) => {\r\n\trouter.onError((error, to) => {\r\n\t\t// 错误处理逻辑\r\n\t\tif (error.message.includes('Failed to fetch dynamically imported module')) {\r\n\t\t\tif (errorNotification.value.pending) return;\r\n\t\t\terrorNotification.value.pending = true;\r\n\t\t\tElMessage({\r\n\t\t\t\ttype: 'success',\r\n\t\t\t\tmessage: '发现系统版本更新，页面即将刷新~',\r\n\t\t\t});\r\n\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\terrorNotification.value.pending = false;\r\n\t\t\t\twindow.location = to.fullPath;\r\n\t\t\t}, errorNotification.value.timeout);\r\n\t\t} else {\r\n\t\t\twindow.location = to.fullPath;\r\n\t\t}\r\n\t});\r\n\r\n\tdocument.title = to.meta.title || '标标准';\r\n\tnext();\r\n});\r\n</script>\r\n\r\n<script>\r\nvar _hmt = _hmt || [];\r\n(function () {\r\n\tvar hm = document.createElement('script');\r\n\thm.src = 'https://hm.baidu.com/hm.js?dc2a733677508064cb9bd9c77a0984d8';\r\n\tvar s = document.getElementsByTagName('script')[0];\r\n\ts.parentNode.insertBefore(hm, s);\r\n\t//   console.log('百度统计',hm);\r\n})();\r\n</script>\r\n\r\n<style>\r\n#app {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbox-sizing: border-box;\r\n\t/* overflow: hidden; */\r\n\tfont-family: Inter, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\r\n}\r\n.smPermissionPop {\r\n\tpadding: 0 !important;\r\n}\r\n\r\n.smPermissionPop .el-dialog__header {\r\n\tpadding: 0 !important;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\nbody {\r\n\tpadding: 0;\r\n\tmargin: 0;\r\n\tfont-size: 16px;\r\n\tbox-sizing: border-box;\r\n\tfont-family: '微软雅黑';\r\n\t.smPermissionPopdialog {\r\n\t\t.el-dialog__header {\r\n\t\t\tpadding: 0 !important;\r\n\t\t}\r\n\t}\r\n}\r\n.bigbody {\r\n\tmargin-left: 20px;\r\n}\r\n.mx-1 {\r\n\tcolor: #000;\r\n\tfont-size: 14px;\r\n}\r\n.el-menu {\r\n\tborder-right: none !important;\r\n}\r\n.el-submenu {\r\n\tborder-top: 1px solid hsla(0, 0%, 100%, 0.05);\r\n\tborder-bottom: 1px solid rgba(0, 0, 0, 0.2);\r\n}\r\n.el-submenu:first-child {\r\n\tborder-top: none;\r\n}\r\n.el-submenu [class^='el-icon-'] {\r\n\tvertical-align: -1px !important;\r\n}\r\na {\r\n\tcolor: #409eff;\r\n\ttext-decoration: none;\r\n}\r\n.el-pagination {\r\n\ttext-align: center;\r\n\tmargin-top: 20px;\r\n}\r\n\r\n::-webkit-scrollbar {\r\n\t/*滚动条整体样式*/\r\n\twidth: 6px; /*高宽分别对应横竖滚动条的尺寸*/\r\n\theight: 1px;\r\n}\r\n::-webkit-scrollbar-thumb {\r\n\t/*滚动条里面小方块*/\r\n\tborder-radius: 5px;\r\n\tbackground: #cfcbcb;\r\n}\r\n::-webkit-scrollbar-track {\r\n\t/*滚动条里面轨道*/\r\n\tborder-radius: 6px;\r\n\tbackground: #ededed;\r\n}\r\n\r\n::-webkit-scrollbar {\r\n\twidth: 6 !important;\r\n\theight: 0;\r\n}\r\n\r\n.loadingComparison,\r\n.loadingPopulation {\r\n\t.el-loading-spinner .path {\r\n\t\tstroke: #1868f1;\r\n\t}\r\n\t.el-loading-text {\r\n\t\tcolor: #1868f1;\r\n\t}\r\n}\r\n\r\n.trial_benefits_dialog {\r\n\tpadding: 0px;\r\n\tborder-radius: 16px;\r\n\t.el-dialog__header {\r\n\t\tpadding: 0 !important;\r\n\t}\r\n}\r\n</style>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/App.vue b/src/App.vue
--- a/src/App.vue	(revision 0404a579e64a5006ba787faac536c997d501b035)
+++ b/src/App.vue	(date 1752472621599)
@@ -36,7 +36,7 @@
 		}
 	});
 
-	document.title = to.meta.title || '标标准';
+	document.title = to.meta.title || '术木智能';
 	next();
 });
 </script>
Index: src/views/login/login.vue
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><template>\r\n\t<div class=\"body_box\">\r\n\t\t<div class=\"container_top\">\r\n\t\t\t<div class=\"top_content\">\r\n\t\t\t\t<div class=\"container_left\">\r\n\t\t\t\t\t<div><img src=\"@/assets/newLogo.png\" alt=\"\" /></div>\r\n\t\t\t\t\t<div class=\"con_title\" @click=\"router.push('/')\">\r\n\t\t\t\t\t\t<div>标标准</div>\r\n\t\t\t\t\t\t<div>·商业地产信息分析平台</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"container_right\" @click=\"router.push('/relation')\">\r\n\t\t\t\t\t<div><img src=\"@/assets/customerService.png\" alt=\"\" /></div>\r\n\t\t\t\t\t<div>客户服务</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div class=\"protocolReturn\" v-if=\"agreeStatus\">\r\n\t\t\t<div @click=\"handleSuerAgree(0)\">\r\n\t\t\t\t<el-icon><ArrowLeft /></el-icon>\r\n\t\t\t\t<div>返回</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div class=\"containerBanner\" v-if=\"!agreeStatus\">\r\n\t\t\t<div><img src=\"@/assets/platform.png\" alt=\"\" /></div>\r\n\t\t\t<div class=\"c_right\">\r\n\t\t\t\t<div class=\"c_content\">\r\n\t\t\t\t\t<div class=\"c_contentpad\">\r\n\t\t\t\t\t\t<div class=\"title_content\" :class=\"indexActive === 3 || indexActive === 4 ? 'title_contents' : ''\">\r\n\t\t\t\t\t\t\t<div class=\"con_title\">\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t{{\r\n\t\t\t\t\t\t\t\t\t\tindexActive === 1 || indexActive === 2 ? '欢迎来到标标准' : indexActive === 3 ? '找回密码' : indexActive === 4 ? '欢迎注册' : ''\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div v-if=\"indexActive === 1 || indexActive === 2\">·商业地产信息分析平台</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"accountlogin\">\r\n\t\t\t\t\t\t\t<el-form :model=\"ruleForm\" size=\"large\" ref=\"ruleForms\">\r\n\t\t\t\t\t\t\t\t<el-form-item v-if=\"indexActive === 1 || indexActive === 4\" prop=\"userName\">\r\n\t\t\t\t\t\t\t\t\t<el-input v-model=\"ruleForm.userName\" maxlength=\"14\" placeholder=\"用户名\" />\r\n\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t<el-form-item v-if=\"indexActive !== 1\" prop=\"phone\">\r\n\t\t\t\t\t\t\t\t\t<el-input v-model=\"ruleForm.phone\" maxlength=\"11\" placeholder=\"请输入手机号\" />\r\n\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t\t<el-form-item v-if=\"indexActive === 2 || indexActive === 3 || indexActive === 4\" prop=\"code\">\r\n\t\t\t\t\t\t\t\t\t<el-input v-model=\"ruleForm.code\" maxlength=\"4\" placeholder=\"请输入验证码\">\r\n\t\t\t\t\t\t\t\t\t\t<template #suffix>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"verificationCode\" @click=\"onGetCode()\" v-if=\"isSend\">重新发送</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"getCode\" v-else style=\"margin-right: 8px\">重新发送{{ time }}s</div>\r\n\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t\t\t\t\t<el-form-item v-if=\"indexActive === 1 || indexActive === 3 || indexActive === 4\" prop=\"password\" class=\"miyaoWrap\">\r\n\t\t\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\t\t\tv-model=\"ruleForm.password\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t:class=\"password ? 'no-autofill-pwd' : 'no-auto'\"\r\n\t\t\t\t\t\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t\t\t\t\t\t\t:placeholder=\"indexActive === 1 || indexActive === 4 ? '请输入密码' : '设置新密码'\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<template #suffix>\r\n\t\t\t\t\t\t\t\t\t\t\t<div style=\"display: flex; cursor: pointer\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<img v-if=\"password\" src=\"@/assets/hideIcon.png\" alt=\"\" @click=\"showPwd\" />\r\n\t\t\t\t\t\t\t\t\t\t\t\t<img v-else src=\"@/assets/showIcon.png\" alt=\"\" @click=\"showPwd\" />\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t\t\t\t\t<el-form-item v-if=\"indexActive === 3 || indexActive === 4\" prop=\"passwords\" class=\"miyaoWrap\">\r\n\t\t\t\t\t\t\t\t\t<el-input\r\n\t\t\t\t\t\t\t\t\t\tv-model=\"ruleForm.passwords\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t:class=\"passwords ? 'no-autofill-pwd' : 'no-auto'\"\r\n\t\t\t\t\t\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"请再次确认新密码\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<template #suffix>\r\n\t\t\t\t\t\t\t\t\t\t\t<div style=\"display: flex; cursor: pointer\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<img v-if=\"passwords\" src=\"@/assets/hideIcon.png\" alt=\"\" @click=\"showPwds\" />\r\n\t\t\t\t\t\t\t\t\t\t\t\t<img v-else src=\"@/assets/showIcon.png\" alt=\"\" @click=\"showPwds\" />\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t\t</el-input>\r\n\t\t\t\t\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t\t\t\t\t<el-form-item>\r\n\t\t\t\t\t\t\t\t\t<el-button type=\"primary\" @click=\"handleLogin(indexActive)\" color=\"#1868F1\">{{\r\n\t\t\t\t\t\t\t\t\t\tindexActive === 3 || indexActive === 4 ? '完成' : '登录'\r\n\t\t\t\t\t\t\t\t\t}}</el-button>\r\n\t\t\t\t\t\t\t\t</el-form-item>\r\n\r\n\t\t\t\t\t\t\t\t<el-form-item prop=\"agree\" v-if=\"indexActive !== 3\">\r\n\t\t\t\t\t\t\t\t\t<el-checkbox size=\"large\" v-model=\"formLabelAlign.agreeAgreement\" class=\"checkAgree\">\r\n\t\t\t\t\t\t\t\t\t\t我已阅读并同意<span @click=\"handleSuerAgree(1)\" class=\"agree\">用户协议、隐私政策</span> 和\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"agree\" @click=\"handleSuerAgree(2)\">产品使用条款</span>\r\n\t\t\t\t\t\t\t\t\t</el-checkbox>\r\n\t\t\t\t\t\t\t\t</el-form-item>\r\n\t\t\t\t\t\t\t</el-form>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"c_option\" :class=\"indexActive === 3 || indexActive === 4 ? 'c_options' : ''\" v-if=\"indexActive !== 3\">\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in formLabelAlign.listOption\"\r\n\t\t\t\t\t\t\t\t@click=\"handleBtnClick(item)\"\r\n\t\t\t\t\t\t\t\tv-show=\"item.index !== indexActive && indexActive !== 4\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{{ item.name }}\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t<div v-if=\"indexActive === 4\" @click=\"handleBtnClick({}, 1)\">返回登录</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t<div class=\"c_option\" :class=\"indexActive === 3 ? 'c_optionss' : ''\" v-if=\"indexActive === 3\">\r\n\t\t\t\t\t\t\t<div @click=\"handleBtnClick({}, 1)\">返回登录</div>\r\n\t\t\t\t\t\t\t<div @click=\"handleBtnClick({}, 4)\">登录注册</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\r\n\t\t<div v-else class=\"agreements\">\r\n\t\t\t<div class=\"agreement_bnanner\">\r\n\t\t\t\t<privacy v-if=\"agreeStatus === 1\"></privacy>\r\n\t\t\t\t<agreement v-if=\"agreeStatus === 2\" :returnShow=\"false\"></agreement>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script setup>\r\nimport { useRoute, useRouter } from 'vue-router';\r\nimport privacy from './privacy.vue';\r\nimport agreement from './agreement.vue';\r\nimport { useStore, vuexStore } from '../../store';\r\nimport { getCodes, getRegister, getLogin, getForget } from 'REQUEST_API';\r\nimport { ElMessage } from 'element-plus';\r\nconst store = useStore();\r\nconst route = useRoute();\r\nconst ruleForm = reactive({\r\n\t// 用户名\r\n\tuserName: null,\r\n\t// 手机号码\r\n\tphone: null,\r\n\t// 验证码\r\n\tcode: null,\r\n\t// 密码\r\n\tpassword: null,\r\n\t// 确认密码\r\n\tpasswords: null,\r\n});\r\n\r\nconst formLabelAlign = reactive({\r\n\t// 是否同意协议\r\n\tagreeAgreement: false,\r\n\tlistOption: [\r\n\t\t{\r\n\t\t\tname: '密码登录',\r\n\t\t\tindex: 1,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: '验证码登录',\r\n\t\t\tindex: 2,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: '找回密码',\r\n\t\t\tindex: 3,\r\n\t\t},\r\n\t\t{\r\n\t\t\tname: '注册账户',\r\n\t\t\tindex: 4,\r\n\t\t},\r\n\t],\r\n});\r\n\r\n// dom\r\nconst ruleForms = ref(null);\r\n// 验证码秒数\r\nconst time = ref(60);\r\n// 获取验证码\r\nconst isSend = ref(true);\r\n// 控制展示内容\r\nconst indexActive = ref(1);\r\n\r\n// 控制协议展示状态\r\nconst agreeStatus = ref(0);\r\n\r\nconst router = useRouter();\r\n\r\nlet password = ref(true);\r\n\r\nlet passwords = ref(true);\r\n\r\n// 页面加载\r\nonMounted(() => {\r\n\tvuexStore.dispatch('handleDistrict'); // 获取区域\r\n});\r\n\r\nconst showPwd = () => {\r\n\tpassword.value = !password.value;\r\n};\r\n\r\nconst showPwds = () => {\r\n\tpasswords.value = !passwords.value;\r\n};\r\n\r\n/**\r\n * @function handleBtnClick 切换登录页展示内容\r\n */\r\nfunction handleBtnClick(item, idx) {\r\n\tformLabelAlign.agreeAgreement = false;\r\n\tpasswords.value = true;\r\n\tpassword.value = true;\r\n\truleForms.value.resetFields();\r\n\tif (idx) {\r\n\t\tindexActive.value = idx;\r\n\t} else {\r\n\t\tindexActive.value = item.index;\r\n\t}\r\n}\r\n\r\n/**\r\n * @function handleLogin\r\n * @index 1 密码登录 2 验证码登录 3 找回密码 4 注册账户\r\n */\r\nfunction handleLogin(index) {\r\n\tif (index === 1 && handleAgree()) {\r\n\t\t// 用户名密码验证\r\n\t\tif (!ruleForm.userName) {\r\n\t\t\tElMessage.warning('请输入用户名');\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// 用户名密码验证\r\n\t\tif (!ruleForm.password) {\r\n\t\t\tElMessage.warning('请输入密码');\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\thandleLogins({ type: 'PASSWORD' });\r\n\t\treturn;\r\n\t}\r\n\tif (index === 2 && handleAgree()) {\r\n\t\t// 手机号码验证\r\n\t\tif (!ruleForm.phone) {\r\n\t\t\tElMessage.warning('请输入手机号');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t//验证码验证\r\n\t\tif (!ruleForm.code) {\r\n\t\t\tElMessage.warning('请输入验证码');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\thandleLogins({ type: 'PHONE' });\r\n\t\treturn;\r\n\t}\r\n\tif (index === 3 && handleAgree(3)) {\r\n\t\t// 找回密码\r\n\t\tif (!ruleForm.phone) {\r\n\t\t\tElMessage.warning('请输入手机号');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t// 密码验证6-20个字符，需包含字母、数字，不能包含空格\r\n\t\tif (!ruleForm.password) {\r\n\t\t\tElMessage.warning('请输入密码');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (ruleForm.password.length < 6 || ruleForm.password.length > 20) {\r\n\t\t\tElMessage.warning('密码长度6-20个字符');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$/.test(ruleForm.password)) {\r\n\t\t\tElMessage.warning('密码需包含字母、数字，不能包含空格');\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\thandlegetForget();\r\n\t\treturn;\r\n\t}\r\n\r\n\tif (index === 4 && handleAgree()) {\r\n\t\t// 用户注册\r\n\t\tif (!ruleForm.userName) {\r\n\t\t\tElMessage.warning('请输入用户名');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t// 验证密码\r\n\t\tif (!ruleForm.password) {\r\n\t\t\tElMessage.warning('请输入密码');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t// 比较两次密码\r\n\t\tif (ruleForm.password !== ruleForm.passwords) {\r\n\t\t\tElMessage.warning('两次密码不一致');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\thandlegetRegister();\r\n\t}\r\n}\r\n// 用户名登录 / 密码登录\r\nfunction handleLogins(param) {\r\n\tgetLogin({ ...ruleForm, ...param }).then((res) => {\r\n\t\thandleUpdate(res);\r\n\t});\r\n}\r\n// 忘记密码\r\nfunction handlegetForget() {\r\n\tgetForget({ ...ruleForm }).then((res) => {\r\n\t\t// handleUpdate(res);\r\n\t\tif (res.code == 200) {\r\n\t\t\tElMessage({\r\n\t\t\t\tmessage: '密码重置成功',\r\n\t\t\t\ttype: 'success',\r\n\t\t\t});\r\n\t\t\thandleBtnClick({}, 1);\r\n\t\t}\r\n\t});\r\n}\r\n\r\n// 用户注册\r\nfunction handlegetRegister() {\r\n\tgetRegister({ ...ruleForm }).then((res) => {\r\n\t\thandleUpdate(res);\r\n\t});\r\n}\r\n\r\n// 更新数据\r\nfunction handleUpdate(res) {\r\n\tif (!res) return;\r\n\tif (res && res.code == 200) {\r\n\t\twindow.localStorage.setItem('token', res.data.accessToken); // 设置token\r\n\t\tstore.getUserInfo(); // 获取用户信息\r\n\t\tvuexStore.dispatch('handleGetShoppingCart'); // 获取购物车\r\n\t\tvuexStore.dispatch('handleDistrict'); // 获取区域\r\n\t\tvuexStore.commit('handleNewUserCoupon', res.data.sendSytNewUserCoupon); // 新用户专享优惠券\r\n\t\tElMessage({\r\n\t\t\tmessage: '登录成功',\r\n\t\t\ttype: 'success',\r\n\t\t});\r\n\t\tif (window.history.length === 1) {\r\n\t\t\trouter.push('/');\r\n\t\t\t// 当前页面是历史记录中的最后一页\r\n\t\t} else {\r\n\t\t\tif (window.history.state.back !== '/login') {\r\n\t\t\t\tif (route.query['0'] === '1') {\r\n\t\t\t\t\trouter.push('/');\r\n\t\t\t\t} else {\r\n\t\t\t\t\trouter.push(window.history.state.back);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\trouter.push('/');\r\n\t\t\t}\r\n\t\t}\r\n\t} else if (res.code == 500 || res.code == 2005) {\r\n\t\t// ElMessage.error(res.message);\r\n\t} else if (res.code == 1002) {\r\n\t\tElMessage.error('请您先点击获取验证码再做操作');\r\n\t}\r\n}\r\n\r\n// 效验用户协议 type 3 找回密码\r\nfunction handleAgree(type) {\r\n\tif (type === 3) {\r\n\t\tif (ruleForm.password !== ruleForm.passwords) {\r\n\t\t\tElMessage.warning('请输入相同的密码');\r\n\t\t\treturn false;\r\n\t\t}\r\n\t} else {\r\n\t\tif (!formLabelAlign.agreeAgreement) {\r\n\t\t\tElMessage.warning('请先同意用户协议');\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n// 获取验证码\r\nconst onGetCode = async () => {\r\n\tconst phone = ruleForm.phone;\r\n\tif (!phone) {\r\n\t\treturn ElMessage({\r\n\t\t\tmessage: '请输入手机号',\r\n\t\t\ttype: 'warning',\r\n\t\t});\r\n\t}\r\n\t// 校验手机号\r\n\tconst reg = /^1[3-9]\\d{9}$/;\r\n\tif (!reg.test(phone)) {\r\n\t\treturn ElMessage({\r\n\t\t\tmessage: '请输入正确的手机号',\r\n\t\t\ttype: 'warning',\r\n\t\t});\r\n\t}\r\n\t// console.log('获取验证码');\r\n\tisSend.value = false;\r\n\tconst timer = setInterval(() => {\r\n\t\ttime.value--;\r\n\t\tif (time.value == 0) {\r\n\t\t\tclearInterval(timer);\r\n\t\t\tisSend.value = true;\r\n\t\t\ttime.value = 60;\r\n\t\t}\r\n\t}, 1000);\r\n\r\n\t// 发送验证码\r\n\tawait getCodes({ phone: ruleForm.phone })\r\n\t\t.then((res) => {\r\n\t\t\tconsole.log(res);\r\n\t\t\t// 发送成功提示\r\n\t\t\tElMessage({\r\n\t\t\t\tmessage: '验证码发送成功,请注意查收~',\r\n\t\t\t\ttype: 'success',\r\n\t\t\t});\r\n\t\t})\r\n\t\t.catch((err) => {\r\n\t\t\tconsole.log('err', err);\r\n\t\t\t// 显示错误信息给用户\r\n\t\t});\r\n};\r\n// 产品使用条款\r\nconst handlePrivacyAgree = () => {\r\n\t// router.push('/privacy');\r\n};\r\n// 用户协议  index 1 用户协议 2 产品使用条款\r\nconst handleSuerAgree = (index) => {\r\n\tagreeStatus.value = index;\r\n\t// router.push('/agreement');\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\nh1 {\r\n\tfont-family: 'Sofia';\r\n\tfont-size: 40px;\r\n}\r\n\r\n.body_box {\r\n\theight: 100vh;\r\n\twidth: 100%;\r\n\toverflow: hidden;\r\n\t.container_top {\r\n\t\theight: 78px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-bottom: 1px solid #e7e7e7;\r\n\t\t.top_content {\r\n\t\t\twidth: 76%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t\timg {\r\n\t\t\theight: 28px;\r\n\t\t\twidth: 28px;\r\n\t\t}\r\n\t\t.container_left > :nth-child(1) {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin: -1px 4px 0 0;\r\n\t\t}\r\n\t\t.container_left {\r\n\t\t\tdisplay: flex;\r\n\r\n\t\t\t.con_title {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\ttext-wrap-mode: nowrap;\r\n\t\t\t}\r\n\t\t\t.con_title > :nth-child(1) {\r\n\t\t\t\tline-height: 41px;\r\n\t\t\t\tfont-size: 23px;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #1d2129;\r\n\t\t\t}\r\n\t\t\t.con_title > :nth-child(2) {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tline-height: 32px;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: end;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.container_right {\r\n\t\t\tcursor: pointer;\r\n\t\t\tdisplay: flex;\r\n\t\t\tfont-size: 16px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\ttext-align: center;\r\n\t\t\ttext-wrap-mode: nowrap;\r\n\r\n\t\t\timg {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\twidth: 16px;\r\n\t\t\t\theight: 16px;\r\n\t\t\t\tmargin-right: 8px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.protocolReturn {\r\n\t\twidth: 100%;\r\n\t\theight: 48px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tbox-shadow: 0px 4px 10px 0px #0000000f;\r\n\t}\r\n\t.protocolReturn > :nth-child(1) {\r\n\t\twidth: 60%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 700;\r\n\t\tcolor: #86909c;\r\n\t\tcursor: pointer;\r\n\t\tdiv {\r\n\t\t\tmargin-left: 5px;\r\n\t\t\theight: 100%;\r\n\t\t\tline-height: 50px;\r\n\t\t}\r\n\t}\r\n\r\n\t.containerBanner {\r\n\t\twidth: 100%;\r\n\t\theight: calc(100% - 78px);\r\n\t\tdisplay: flex;\r\n\t\toverflow: hidden;\r\n\t\t.c_right {\r\n\t\t\twidth: calc(100% - 31%);\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\t.c_content {\r\n\t\t\t\twidth: 480px;\r\n\t\t\t\tmin-height: 550px;\r\n\t\t\t\tbox-shadow: 0px 6px 32px 0px #2626261a;\r\n\t\t\t\tbackground: #ffffff;\r\n\t\t\t\tborder-radius: 6px 0px 0px 0px;\r\n\t\t\t\t.c_contentpad {\r\n\t\t\t\t\twidth: 400px;\r\n\t\t\t\t\tpadding: 40px;\r\n\t\t\t\t\theight: calc(100% - 80px);\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t\t.title_contents {\r\n\t\t\t\t\t\theight: initial !important;\r\n\t\t\t\t\t\t// align-items: flex-start !important;\r\n\t\t\t\t\t\tmargin-bottom: 40px !important;\r\n\t\t\t\t\t\t// padding-top: 0px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.title_content {\r\n\t\t\t\t\t\theight: 113px;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t.con_title {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\theight: 33px;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\ttext-wrap-mode: nowrap;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.con_title > :nth-child(1) {\r\n\t\t\t\t\t\t\tline-height: 41px;\r\n\t\t\t\t\t\t\tfont-size: 23px;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\tcolor: #1d2129;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.con_title > :nth-child(2) {\r\n\t\t\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\t\t\theight: 33px;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\tline-height: 39px;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.c_options {\r\n\t\t\t\t\t\tmargin-top: -5px !important;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.c_optionss {\r\n\t\t\t\t\t\tmargin-top: 75px !important;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.c_option {\r\n\t\t\t\t\t\tmargin-top: 113px;\r\n\t\t\t\t\t\theight: 22px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tline-height: 22px;\r\n\t\t\t\t\t\twidth: calc(100% - 34px);\r\n\t\t\t\t\t\tpadding: 0 16px;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\tcolor: #86909c;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.c_option > :nth-child(n) {\r\n\t\t\t\t\t\twidth: 122px;\r\n\t\t\t\t\t\theight: 16px;\r\n\t\t\t\t\t\tline-height: 16px;\r\n\t\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\t\tborder-right: 1px solid #e7e7e7;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.c_option > :last-child {\r\n\t\t\t\t\t\tborder-right: 0px solid #e7e7e7;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.containerBanner > :nth-child(1) {\r\n\t\twidth: 31%;\r\n\t\theight: 100%;\r\n\t\t// overflow: hidden; /* 隐藏溢出的部分 */\r\n\t\t// height: 100vh;\r\n\t\timg {\r\n\t\t\twidth: -webkit-fill-available;\r\n\t\t\theight: inherit;\r\n\t\t\tdisplay: block;\r\n\t\t\tobject-fit: cover;\r\n\t\t}\r\n\t}\r\n\t.accountlogin {\r\n\t\twidth: 100%;\r\n\t\t// height: 222px;\r\n\t\t.verificationCode {\r\n\t\t\tcursor: pointer;\r\n\t\t\tfont-size: 14px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tline-height: 22px;\r\n\t\t\tcolor: #1868f1;\r\n\t\t}\r\n\t\t::v-deep .el-form-item {\r\n\t\t\tmargin-bottom: 10px !important;\r\n\t\t}\r\n\t\t::v-deep input {\r\n\t\t\theight: 52px !important;\r\n\t\t}\r\n\t\t::v-deep button {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 52px !important;\r\n\t\t}\r\n\t}\r\n\t.agreements {\r\n\t\theight: calc(100% - 146px);\r\n\t\toverflow-y: scroll;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\t.agreement_bnanner {\r\n\t\t\twidth: 60%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.login_back {\r\n\twidth: 220px;\r\n\theight: 470px;\r\n\tmargin-right: 20px;\r\n\t// background-image: url('@/assets/homebj.png');\r\n\tbackground-size: 30% 100%;\r\n\tbackground-repeat: no-repeat;\r\n\tbackground-position: center;\r\n}\r\n\r\n.login_box {\r\n\tposition: relative;\r\n\twidth: 415px;\r\n\theight: 470px;\r\n\tdisplay: flex;\r\n\tjustify-content: flex-start;\r\n\talign-items: flex-start;\r\n\tflex-direction: column;\r\n}\r\n\r\n.el-form-item__label {\r\n\twidth: 20px !important;\r\n}\r\n\r\n.form_box {\r\n\tmargin-top: 10px;\r\n\r\n\t// margin-bottom: 100px;\r\n\t.btn_wangji {\r\n\t\twidth: 100%;\r\n\t\tpadding: 0 15px;\r\n\t\tfont-size: 14px;\r\n\t\tbox-sizing: border-box;\r\n\t\tcolor: rgba(0, 0, 0, 0.6);\r\n\t}\r\n}\r\n\r\n.register {\r\n\twidth: 100%;\r\n\theight: 50px;\r\n\tmargin: 0 auto;\r\n\ttext-align: center;\r\n\tmargin-top: 10px;\r\n\tbox-sizing: border-box;\r\n\tborder: 3.5px solid rgb(0, 0, 0);\r\n\tborder-radius: 50px;\r\n\r\n\tbackground: rgb(255, 255, 255);\r\n}\r\n\r\n.login_input {\r\n\twidth: 100%;\r\n\theight: auto;\r\n\tdisplay: flex;\r\n\r\n\t::v-deep .el-input__wrapper {\r\n\t\twidth: 300px;\r\n\t\theight: 50px;\r\n\t\tbackground-color: rgb(240, 240, 240);\r\n\t\tborder-radius: 30px;\r\n\t\tbox-shadow: none;\r\n\t}\r\n}\r\n\r\n.getCode {\r\n\tcursor: pointer;\r\n}\r\n\r\n.agree {\r\n\tfont-size: 14px;\r\n\tfont-weight: 700;\r\n\ttext-align: center;\r\n\tcolor: #1868f1;\r\n}\r\n.checkAgree {\r\n\t::v-deep .el-checkbox__label {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: 700;\r\n\t\tline-height: 22px;\r\n\t\tcolor: #1d2129;\r\n\t}\r\n}\r\n@media only screen and (max-width: 600px) {\r\n\t.container_top {\r\n\t\t.top_content {\r\n\t\t\twidth: 90% !important;\r\n\t\t}\r\n\t}\r\n\t.c_right {\r\n\t\twidth: 100% !important;\r\n\t\tbackground: url(../../assets/platform.png);\r\n\t\tbackground-size: cover;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-position: center;\r\n\t}\r\n\t.containerBanner > :nth-child(1) {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.c_content {\r\n\t\twidth: 90% !important;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tflex-direction: column;\r\n\t\t// transform: scale(0.9);\r\n\t\t.c_contentpad {\r\n\t\t\twidth: 90% !important;\r\n\r\n\t\t\t::v-deep .el-checkbox {\r\n\t\t\t\twhite-space: normal;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.protocolReturn > :nth-child(1) {\r\n\t\twidth: 100% !important;\r\n\t}\r\n\t.agreement_bnanner {\r\n\t\twidth: 100% !important;\r\n\t}\r\n}\r\n\r\n.miyaoWrap {\r\n\t.no-autofill-pwd {\r\n\t\t-webkit-text-security: disc !important;\r\n\t}\r\n\t.no-auto {\r\n\t\t-webkit-text-security: none !important;\r\n\t}\r\n}\r\n</style>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/views/login/login.vue b/src/views/login/login.vue
--- a/src/views/login/login.vue	(revision 0404a579e64a5006ba787faac536c997d501b035)
+++ b/src/views/login/login.vue	(date 1752472857089)
@@ -5,8 +5,8 @@
 				<div class="container_left">
 					<div><img src="@/assets/newLogo.png" alt="" /></div>
 					<div class="con_title" @click="router.push('/')">
-						<div>标标准</div>
-						<div>·商业地产信息分析平台</div>
+						<div>术木智能</div>
+						<div>·商业地产价值分析平台</div>
 					</div>
 				</div>
 				<div class="container_right" @click="router.push('/relation')">
@@ -24,25 +24,41 @@
 		</div>
 
 		<div class="containerBanner" v-if="!agreeStatus">
-			<div><img src="@/assets/platform.png" alt="" /></div>
+			<div><img src="@/assets/login_background.png" alt="" /></div>
 			<div class="c_right">
 				<div class="c_content">
+					<!-- 关闭按钮 -->
+					<div class="close_button" @click="router.push('/')">
+						×
+					</div>
 					<div class="c_contentpad">
 						<div class="title_content" :class="indexActive === 3 || indexActive === 4 ? 'title_contents' : ''">
 							<div class="con_title">
-								<div>
-									{{
-										indexActive === 1 || indexActive === 2 ? '欢迎来到标标准' : indexActive === 3 ? '找回密码' : indexActive === 4 ? '欢迎注册' : ''
-									}}
+								<div v-if="indexActive === 1 || indexActive === 2" class="welcome_title">
+									<div>您好，欢迎来到</div>
+									<div>术木智能·商业地产价值分析平台</div>
+								</div>
+								<div v-else-if="indexActive === 3" class="single_title">找回密码</div>
+								<div v-else-if="indexActive === 4" class="single_title">欢迎注册</div>
+							</div>
+
+							<!-- 标签切换 -->
+							<div class="tab_switch" v-if="indexActive === 1 || indexActive === 2">
+								<div class="tab_item" :class="{ active: indexActive === 1 }" @click="handleBtnClick({}, 1)">
+									<span>账号登录</span>
+									<div class="tab_underline" v-if="indexActive === 1"></div>
+								</div>
+								<div class="tab_item" :class="{ active: indexActive === 2 }" @click="handleBtnClick({}, 2)">
+									<span>手机号登录</span>
+									<div class="tab_underline" v-if="indexActive === 2"></div>
 								</div>
-								<div v-if="indexActive === 1 || indexActive === 2">·商业地产信息分析平台</div>
 							</div>
 						</div>
 
 						<div class="accountlogin">
 							<el-form :model="ruleForm" size="large" ref="ruleForms">
 								<el-form-item v-if="indexActive === 1 || indexActive === 4" prop="userName">
-									<el-input v-model="ruleForm.userName" maxlength="14" placeholder="用户名" />
+									<el-input v-model="ruleForm.userName" maxlength="14" placeholder="请输入用户名" />
 								</el-form-item>
 								<el-form-item v-if="indexActive !== 1" prop="phone">
 									<el-input v-model="ruleForm.phone" maxlength="11" placeholder="请输入手机号" />
@@ -50,7 +66,7 @@
 								<el-form-item v-if="indexActive === 2 || indexActive === 3 || indexActive === 4" prop="code">
 									<el-input v-model="ruleForm.code" maxlength="4" placeholder="请输入验证码">
 										<template #suffix>
-											<div class="verificationCode" @click="onGetCode()" v-if="isSend">重新发送</div>
+											<div class="verificationCode" @click="onGetCode()" v-if="isSend">获取验证码</div>
 											<div class="getCode" v-else style="margin-right: 8px">重新发送{{ time }}s</div>
 										</template>
 									</el-input>
@@ -62,7 +78,7 @@
 										type="text"
 										:class="password ? 'no-autofill-pwd' : 'no-auto'"
 										maxlength="20"
-										:placeholder="indexActive === 1 || indexActive === 4 ? '请输入密码' : '设置新密码'"
+										:placeholder="indexActive === 1 || indexActive === 4 ? '请输入登录密码' : '设置新密码'"
 									>
 										<template #suffix>
 											<div style="display: flex; cursor: pointer">
@@ -105,22 +121,20 @@
 							</el-form>
 						</div>
 
-						<div class="c_option" :class="indexActive === 3 || indexActive === 4 ? 'c_options' : ''" v-if="indexActive !== 3">
-							<div
-								v-for="(item, index) in formLabelAlign.listOption"
-								@click="handleBtnClick(item)"
-								v-show="item.index !== indexActive && indexActive !== 4"
-								:key="index"
-							>
-								{{ item.name }}
-							</div>
+						<div class="c_option" v-if="indexActive === 1 || indexActive === 2">
+							<div @click="handleBtnClick({}, 3)">忘记密码</div>
+							<div class="separator">｜</div>
+							<div @click="handleBtnClick({}, 4)">注册账号</div>
+						</div>
 
-							<div v-if="indexActive === 4" @click="handleBtnClick({}, 1)">返回登录</div>
+						<div class="c_option" v-if="indexActive === 3">
+							<div @click="handleBtnClick({}, 1)">返回登录</div>
+							<div class="separator">｜</div>
+							<div @click="handleBtnClick({}, 4)">注册账号</div>
 						</div>
 
-						<div class="c_option" :class="indexActive === 3 ? 'c_optionss' : ''" v-if="indexActive === 3">
+						<div class="c_option" v-if="indexActive === 4">
 							<div @click="handleBtnClick({}, 1)">返回登录</div>
-							<div @click="handleBtnClick({}, 4)">登录注册</div>
 						</div>
 					</div>
 				</div>
@@ -446,6 +460,7 @@
 	height: 100vh;
 	width: 100%;
 	overflow: hidden;
+	background-color: #DDE6F4;
 	.container_top {
 		height: 78px;
 		display: flex;
@@ -541,15 +556,42 @@
 			align-items: center;
 			justify-content: center;
 			.c_content {
-				width: 480px;
-				min-height: 550px;
-				box-shadow: 0px 6px 32px 0px #2626261a;
+				width: 478px;
+				height: 594px;
+				box-shadow: 0px 5px 15px 0px rgba(0, 0, 0, 0.05);
 				background: #ffffff;
-				border-radius: 6px 0px 0px 0px;
+				border-radius: 20px;
+				position: relative;
+
+				.close_button {
+					position: absolute;
+					top: 24px;
+					right: 24px;
+					width: 28px;
+					height: 28px;
+					cursor: pointer;
+					display: flex;
+					align-items: center;
+					justify-content: center;
+					z-index: 100;
+					background: rgba(255, 255, 255, 0.8);
+					border-radius: 50%;
+					transition: all 0.3s ease;
+					font-size: 18px;
+					color: #333333;
+					font-weight: 300;
+					line-height: 1;
+				}
+
+				.close_button:hover {
+					background: rgba(255, 255, 255, 1);
+					transform: scale(1.1);
+				}
+
 				.c_contentpad {
-					width: 400px;
-					padding: 40px;
-					height: calc(100% - 80px);
+					width: 382px;
+					padding: 48px 48px 56px 48px;
+					height: calc(100% - 104px);
 					display: flex;
 					flex-direction: column;
 
@@ -560,27 +602,68 @@
 						// padding-top: 0px !important;
 					}
 					.title_content {
-						height: 113px;
 						display: flex;
-						align-items: center;
+						flex-direction: column;
+						gap: 40px;
 						.con_title {
 							display: flex;
-							height: 33px;
-							align-items: center;
+							flex-direction: column;
 							text-wrap-mode: nowrap;
 						}
 
-						.con_title > :nth-child(1) {
-							line-height: 41px;
-							font-size: 23px;
+						.welcome_title {
+							font-family: 'PingFang SC', sans-serif;
+							font-size: 24px;
+							font-weight: 500;
+							line-height: 1.5em;
+							color: #1d2129;
+						}
+
+						.single_title {
+							font-family: 'PingFang SC', sans-serif;
+							font-size: 24px;
 							font-weight: 500;
+							line-height: 1.5em;
 							color: #1d2129;
 						}
-						.con_title > :nth-child(2) {
-							font-size: 16px;
-							height: 33px;
-							font-weight: 500;
-							line-height: 39px;
+
+						.tab_switch {
+							display: flex;
+							align-items: center;
+							gap: 36px;
+
+							.tab_item {
+								display: flex;
+								flex-direction: column;
+								gap: 4px;
+								cursor: pointer;
+								position: relative;
+
+								span {
+									font-family: 'PingFang SC', sans-serif;
+									font-size: 16px;
+									font-weight: 500;
+									line-height: 1.5em;
+									color: #4E5969;
+									transition: color 0.3s ease;
+									white-space: nowrap;
+								}
+
+								&.active span {
+									color: #1868F1;
+								}
+
+								&:hover:not(.active) span {
+									color: #1868F1;
+								}
+
+								.tab_underline {
+									width: 100%;
+									height: 2px;
+									background-color: #1868F1;
+									border-radius: 1px;
+								}
+							}
 						}
 					}
 					.c_options {
@@ -591,28 +674,32 @@
 						margin-top: 75px !important;
 					}
 					.c_option {
-						margin-top: 113px;
-						height: 22px;
-						font-size: 14px;
-						font-weight: 600;
-						line-height: 22px;
-						width: calc(100% - 34px);
-						padding: 0 16px;
+						margin-top: 80px;
 						display: flex;
 						justify-content: center;
-						color: #86909c;
-						text-align: center;
 						align-items: center;
+						gap: 18px;
+						font-family: 'PingFang SC', sans-serif;
+						font-size: 14px;
+						font-weight: 400;
+						line-height: 1.5714285714285714em;
+						color: #4E5969;
 					}
 					.c_option > :nth-child(n) {
-						width: 122px;
-						height: 16px;
-						line-height: 16px;
 						cursor: pointer;
-						border-right: 1px solid #e7e7e7;
+						transition: color 0.3s ease;
+					}
+					.c_option > :nth-child(n):hover {
+						color: #1868F1;
 					}
-					.c_option > :last-child {
-						border-right: 0px solid #e7e7e7;
+
+					.separator {
+						color: #C9CDD4 !important;
+						cursor: default !important;
+					}
+
+					.separator:hover {
+						color: #C9CDD4 !important;
 					}
 				}
 			}
@@ -632,23 +719,80 @@
 	}
 	.accountlogin {
 		width: 100%;
-		// height: 222px;
+		margin-top: 32px;
+
 		.verificationCode {
 			cursor: pointer;
+			font-family: 'PingFang SC', sans-serif;
 			font-size: 14px;
 			font-weight: 400;
-			line-height: 22px;
+			line-height: 1.5714285714285714em;
 			color: #1868f1;
+			text-align: right;
 		}
+
 		::v-deep .el-form-item {
-			margin-bottom: 10px !important;
+			margin-bottom: 20px !important;
+		}
+
+		::v-deep .el-form-item:last-of-type {
+			margin-bottom: 0 !important;
+		}
+
+		::v-deep .el-input__wrapper {
+			border: 1px solid #E5E6EB;
+			border-radius: 4px;
+			padding: 10px 12px;
+			height: auto;
+			min-height: 40px;
+			box-shadow: none;
+			transition: border-color 0.3s ease;
+		}
+
+		::v-deep .el-input__wrapper:hover {
+			border-color: #1868F1;
 		}
+
+		::v-deep .el-input__wrapper.is-focus {
+			border-color: #1868F1;
+			box-shadow: 0 0 0 2px rgba(24, 104, 241, 0.1);
+		}
+
 		::v-deep input {
-			height: 52px !important;
+			font-family: 'PingFang SC', sans-serif;
+			font-size: 14px;
+			font-weight: 400;
+			line-height: 1.5714285714285714em;
+			color: #1D2129;
+			height: auto !important;
 		}
+
+		::v-deep input::placeholder {
+			color: #86909C;
+			font-family: 'PingFang SC', sans-serif;
+		}
+
 		::v-deep button {
 			width: 100%;
-			height: 52px !important;
+			height: 40px !important;
+			border-radius: 4px;
+			font-family: 'PingFang SC', sans-serif;
+			font-size: 14px;
+			font-weight: 500;
+			line-height: 1.5714285714285714em;
+			margin-top: 16px;
+			border: none;
+			transition: all 0.3s ease;
+		}
+
+		::v-deep button:hover {
+			background-color: #0056d6 !important;
+			transform: translateY(-1px);
+			box-shadow: 0 4px 12px rgba(24, 104, 241, 0.3);
+		}
+
+		::v-deep button:active {
+			transform: translateY(0);
 		}
 	}
 	.agreements {
@@ -738,11 +882,38 @@
 	color: #1868f1;
 }
 .checkAgree {
+	margin-top: 16px;
+	margin-bottom: 0;
+
 	::v-deep .el-checkbox__label {
-		font-size: 14px;
-		font-weight: 700;
-		line-height: 22px;
-		color: #1d2129;
+		font-family: 'PingFang SC', sans-serif;
+		font-size: 12px;
+		font-weight: 400;
+		line-height: 1.5em;
+		color: #4E5969;
+		padding-left: 8px;
+	}
+
+	::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
+		background-color: #1868F1;
+		border-color: #1868F1;
+	}
+
+	::v-deep .el-checkbox__input.is-checked .el-checkbox__inner::after {
+		border-color: #FFFFFF;
+		border-width: 2px;
+	}
+
+	::v-deep .el-checkbox__inner {
+		width: 14px;
+		height: 14px;
+		border-radius: 2px;
+		border: 1px solid #E5E6EB;
+		transition: all 0.3s ease;
+	}
+
+	::v-deep .el-checkbox__inner:hover {
+		border-color: #1868F1;
 	}
 }
 @media only screen and (max-width: 600px) {
