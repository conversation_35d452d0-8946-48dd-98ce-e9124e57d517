<template>
	<div class="propertyHome_box">
		<h1>{{ route.query.companyName }}</h1>
		<div class="building_box">
			<div class="building_item" v-for="item in companyDataList" :key="item">
				<img :src="`${proxyAddress}${item.fileUrl}`" alt="" />
				<div class="text_box">
					<div class="head">
						<div class="left">
							<div class="name">{{ item.name }}</div>
							<div class="type">{{ item.buildingType }}</div>
						</div>

						<div class="rate">{{ item.degree }}</div>
					</div>
					<div class="address">{{ item.address }}</div>
					<div class="footer">
						<div class="left">
							<div class="company">
								物管公司:<span>{{ item.buildingManager }}</span>
							</div>
							<div class="company">
								产权公司:<span>{{ item.propertyOwner }}</span>
							</div>
							<div class="grade">
								<div class="tips_body">
									<div class="tips">
										<div class="label1">维护情况</div>
										<div class="value1">{{ item.maintenance }}</div>
									</div>
									<div class="tips">
										<div class="label1">周边配套</div>
										<div class="value1">{{ item.assorted }}</div>
									</div>
									<div class="tips">
										<div class="label1">区域潜力</div>
										<div class="value1">{{ item.regionalPotential }}</div>
									</div>
									<div class="tips">
										<div class="label1">商业活力</div>
										<div class="value1">{{ item.businessDynamism }}</div>
									</div>
									<div class="tips">
										<div class="label1">人均消费能力</div>
										<div class="value1">{{ item.spendingPower }}元</div>
									</div>
									<div class="tips">
										<div class="label1">建成年份</div>
										<div class="value1">{{ item.buildYear }}年</div>
									</div>
								</div>
							</div>
						</div>
						<div class="ranking_box">
							<div class="ranking">
								<div>估值排名</div>
								<div>{{ item.comparableValueRank }}/{{ item.comparableValueRankTotal }}</div>
							</div>
							<div class="ranking">
								<div>证券化排名</div>
								<div>
									<span>{{ percent }}%</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { companyList } from '@/api/participant.js';
import { useRoute } from 'vue-router';
const proxyAddress = ref('https://static.biaobiaozhun.com/');
let route = useRoute();

console.log(1241234, route.query);
const group = ref('张三集团');
const property = ref('物管公司');
const interest = ref('产权公司');
const companyDataList = ref([]);
const percent = ref(0);
const getcompanyList = async () => {
	const params = {
		companyName: route.query.companyName,
		pageNo: 1,
		pageSize: 10,
		type: route.query.type,
	};
	await companyList(params).then((res) => {
		console.log(res);
		companyDataList.value = res.result.records;
		const adas = (res.result.records[0].scaleSum3Result / res.result.records[0].scaleSum3ResultTotal) * 100;
		console.log(adas, 123123);
		percent.value = (res.result.records[0].scaleSum3Result / res.result.records[0].scaleSum3ResultTotal) * 100;
		percent.value = Math.ceil(percent.value);
		console.log(percent.value, 23534, adas);
	});
};
getcompanyList();
</script>

<style scoped lang="less">
.propertyHome_box {
	padding: 30px 52px;
	h1 {
		color: rgb(0, 0, 0);
		font-family: 微软雅黑;
		font-size: 24px;
		font-weight: 700;
		line-height: 35px;
		letter-spacing: 0px;
		text-align: left;
	}
	.building_box {
		.building_item {
			width: 1176px;
			height: 350px;
			display: flex;
			box-sizing: border-box;
			border-bottom: 1px solid rgb(205, 205, 205);
			border-top: 1px solid rgb(205, 205, 205);
			background: rgb(250, 250, 250);
			margin-bottom: 20px;
			padding: 30px;
			img {
				width: 355px;
				height: 289px;
			}
			.text_box {
				margin-left: 46px;
				width: 720px;
				margin-top: 16px;
				.head {
					display: flex;
					justify-content: space-between;
					.left {
						display: flex;
						.name {
							color: rgb(0, 0, 0);
							font-family: 微软雅黑;
							font-size: 24px;
							font-weight: 700;
							line-height: 32px;
							letter-spacing: 0px;
							text-align: left;
						}
						.type {
							width: 72px;
							height: 31px;
							box-sizing: border-box;
							border: 1px solid rgb(64, 158, 255);
							border-radius: 2px;
							background: rgb(241, 248, 255);
							color: rgb(64, 158, 255);
							font-family: 微软雅黑;
							font-size: 16px;
							font-weight: 400;
							line-height: 31px;
							letter-spacing: 0px;
							text-align: center;
							margin-left: 15px;
						}
					}

					.rate {
						color: rgb(0, 0, 0);
						font-family: 微软雅黑;
						font-size: 32px;
						font-weight: 700;
						line-height: 42px;
						letter-spacing: 0px;
						text-align: left;
					}
				}
				.address {
					width: 610px;
					color: rgb(0, 0, 0);
					font-family: 微软雅黑;
					font-size: 16px;
					font-weight: 400;
					line-height: 21px;
					letter-spacing: 0px;
					text-align: left;
				}
				.footer {
					display: flex;
					justify-content: space-between;
					.left {
						.company {
							color: rgb(108, 108, 108);
							font-family: 微软雅黑;
							font-size: 16px;
							font-weight: 400;
							line-height: 21px;
							letter-spacing: 0px;
							text-align: left;
							margin-top: 10px;
							span {
								color: rgb(0, 0, 0);
								font-family: 微软雅黑;
								font-size: 16px;
								font-weight: 400;
								line-height: 21px;
								letter-spacing: 0px;
								text-align: left;
							}
						}
					}
					.grade {
						// width: 260px;
						display: flex;
						justify-content: space-between;
						margin-bottom: 15px;
						.tips_body {
							height: 100px;
							font-size: 16px;
							display: flex;
							justify-content: space-between;
							align-items: center;
							flex-wrap: wrap;
							.tips {
								width: 30%;
								height: 40px;
								line-height: 14px;
								display: flex;
								justify-content: center;
								align-items: center;
								flex-direction: column;
								.label1 {
									width: 100%;
									text-align: center;
									margin: 5px 0;
								}
							}
						}
					}
					.ranking_box {
						margin-top: 30px;
						.ranking {
							width: 146px;
							height: 60px;
							display: flex;
							flex-direction: column;
							justify-content: space-around;
							align-items: center;

							box-sizing: border-box;
							border: 1px solid rgb(64, 158, 255);
							border-radius: 3px;

							background: rgb(241, 248, 255);
							margin-bottom: 15px;
							.div {
								line-height: 50px;
							}
						}
					}
				}
			}
		}
	}
}
</style>
