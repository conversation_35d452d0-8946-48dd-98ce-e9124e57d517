<template>
	<div class="box">
		<div class="main_box">
			<!-- 顶部菜单 -->
			<div class="menu_box">
				<div class="title">
					<div class="left_box">
						<img src="@/assets/newLogo.png" @click="router.push('/')" class="logo" alt="" />
						<div class="menu1_box">
							<div class="menu1 active" @click="router.push('/')">关于标标准</div>
						</div>
					</div>
					<div class="login_btn">
						<!-- <button @click="onLogin" v-if="!islogin" class="logoutBtn">登录</button> -->
						<el-popover placement="bottom" :width="120" trigger="click" v-show="!islogin">
							<template #reference>
								<div class="head" v-show="islogin">
									<img v-if="!imageUrl" src="@/assets/rectangles.png" alt="" />
									<img v-if="imageUrl" :src="imageUrl" alt="" />
								</div>
							</template>
							<div class="popover_box">
								<div class="userName">{{ $vuexStore.state.userInfo.userName || '用户名' }}</div>
								<div class="exitlogon" v-if="islogin" @click="onQuit">退出登录</div>
							</div>
						</el-popover>
					</div>
				</div>
			</div>
			<div class="menuList" v-if="router.currentRoute.value.name !== 'homepage'">
				<div class="menu_box1">
					<div class="logo_box" @click="router.push('/')" v-if="route.meta.title !== '商宇通'">
						<img src="@/assets/newLogo.png" class="logo" alt="" />
						<div class="d-title">术木智能商业地产价值分析平台</div>
					</div>
					<div class="shangYuTongBox" v-else>
						<div class="shangYuTongBox_item">
							<img src="@/assets/images/shangYutong/menu/shangYutlogo.png" class="shangYuTongBox_item_img" alt="" />
							<img src="@/assets/images/shangYutong/menu/shangYutlogos.png" class="shangYuTongBox_item_imgs" alt="" />
						</div>
						<div class="shangYuTongBox_item_box" @click="router.push('/')">
							<div class="shangYuTongBox_item_box_img">
								<img src="@/assets/images/shangYutong/menu/shangYutlogoimg.png" class="logo" alt="" />
							</div>
							<div class="shangYuTongBox_item_box_text">首页</div>
						</div>
					</div>
					<!-- 登录按钮 -->
					<div class="login_btn">
						<div class="phone">
							<img src="@/assets/landlinePhone.png" alt="" />
							<div>************</div>
						</div>

						<el-badge :value="$vuexStore.state.shoppingCart">
							<el-icon size="22" style="cursor: pointer" @click="handlePushCart"><ShoppingCart /></el-icon>
						</el-badge>

						<button @click="onLogin" v-if="!islogin" class="logoutBtn">登录</button>

						<el-popover placement="bottom" :width="130" trigger="hover" v-show="!islogin" ref="popoverRef">
							<template #reference>
								<div class="head" v-show="islogin">
									<img v-if="!imageUrl" class="headImg" src="@/assets/rectangles.png" alt="" />
									<img v-if="imageUrl" class="headImg" :src="imageUrl" alt="" />
								</div>
							</template>
							<div class="popover_box">
								<div class="userName">{{ $vuexStore.state.userInfo.userName || '用户名' }}</div>
								<div class="personal" @click="onMy">个人中心</div>
								<div class="exitlogon" v-if="islogin" @click="onQuit">退出登录</div>
							</div>
						</el-popover>
					</div>
				</div>
			</div>

			<!-- 中间主体部分 -->
			<div class="content_box">
				<router-view />
			</div>
			<!-- 底部菜单 -->
			<Footer v-if="handleFooter" />
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter();
const route = useRoute();
import { reactive } from 'vue';
import Footer from './footer.vue';
import { storeToRefs } from 'pinia';
import { useStore, vuexStore } from '../../store';
const store = useStore();
const popoverRef = ref(null);
const isfooter = ref(true);
const drawer = ref(false);
const imageUrl = ref();
const dialogVisible = ref(false); //确认框
const islogin = ref(true);

onMounted(() => {
	handleLogin();
	if (vuexStore.state.userInfo.headImage) {
		let randomInt = Math.floor(Math.random() * 100) + 1;
		imageUrl.value = store.imagePathPrefix + vuexStore.state.userInfo.headImage + '?v=' + randomInt;
	} else {
		imageUrl.value = null;
	}
});

// 监听
watch(
	() => vuexStore.state.userInfo,
	(newVal, oldVal) => {
		if (vuexStore.state.userInfo.headImage) {
			let randomInt = Math.floor(Math.random() * 100) + 1;
			imageUrl.value = store.imagePathPrefix + vuexStore.state.userInfo.headImage + '?v=' + randomInt;
		} else {
			imageUrl.value = null;
		}
	}
);

// 登录
function handleLogin() {
	let token = window.localStorage.getItem('token');
	if (token == null) {
		islogin.value = false;
	}
}
// 底部菜单
const handleFooter = computed(() => {
	if (
		router.currentRoute.value.name !== 'homepage' &&
		router.currentRoute.value.name !== 'biaobiaozhun' &&
		router.currentRoute.value.name !== 'IntroductionPage' &&
		router.currentRoute.value.name !== 'introduce' &&
		router.currentRoute.value.name !== 'rights' &&
		router.currentRoute.value.name !== 'relation' &&
		router.currentRoute.value.name !== 'mobileEndSyt' &&
		router.currentRoute.value.name !== 'mobileShangAuto' &&
		router.currentRoute.value.name !== 'introduceMobile'
	) {
		return false;
	} else {
		return true;
	}
});

// 购物车跳转
function handlePushCart(params) {
	router.push({
		path: '/shoppingCart',
	});
}

// 登录
const onLogin = () => {
	router.push({
		path: '/login',
	});
};

// 退出登录
const onQuit = () => {
	onExit();
	router.push({
		path: '/login',
		query: '1',
		replace: true,
	});
};
// 退出登录
const onExit = () => {
	localStorage.removeItem('token');
	// 清空vuex中用户数据
	vuexStore.dispatch('clearData');
	vuexStore.commit('handleNewUserCoupon', false); // 新用户专享优惠券
	islogin.value = false;
};
// 个人中心
const onMy = () => {
	router.push({
		path: '/profile/mymessage',
	});
};
</script>

<style lang="less" scoped>
.menuList {
	position: sticky !important;
	top: 0px;
	z-index: 999;
	overflow-x: auto;
	-ms-overflow-style: none;
	scrollbar-width: none;
	width: 100%;
	height: 56px;
	background: #FFFFFF;
	color: #4E5969;
	box-shadow: 0px 4px 14px 0px #0000000F;
	display: flex;
	// justify-content: center;
	padding: 0 24px;
	align-items: center;
	box-sizing: border-box;

	::-webkit-scrollbar {
		display: none;
	}

	.menu_box1 {
		height: 37px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		position: relative;
		color: #1D2129;
		.logo_box {
			width: 283px;
			height: 37px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			font-weight: bold;
			font-size: 18px;
			margin-right: 50px;
			cursor: pointer;
			.logo {
				width: 28px;
				height: 28px;
				margin-right: 10px;
			}
			.d-title {
				font-size: 16px;
				font-weight: 600;
	     }
	     	.t-title{
				font-size: 14px;
				}
		}

    .shangYuTongBox{
      display: flex;
      align-items: center;
      height: 36px;
      .shangYuTongBox_item{
        display: flex;
        align-items: center;
        margin-right: 16px;
        .shangYuTongBox_item_img{
          width: 23px;
          height: 26px;
          margin: 3px 5px 3px 4px;
        }
        .shangYuTongBox_item_imgs{
          width: 56px;
          height: 20px;

        }
      }

      .shangYuTongBox_item_box{
        width: 66px;
        height: 24px;
        gap: 4px;
        border-radius: 4px;
        padding:0 12px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        background-color: #E8F3FF;
        cursor: pointer;
        .shangYuTongBox_item_box_img{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 12px;
          height: 12px;
          img{
            width: 100%;
            height: 100%;
          }
        }
        .shangYuTongBox_item_box_text{
            font-weight: 400;
            font-size: 13px;
            line-height: 23px;
            color: #1868F1;
        }
      }
    }

		.menu2 {
			width: auto;
			// width: 10%;
			height: 40px;
			padding: 0 15px;
			margin-right: 15px;
			font-size: 16px;
			border-radius: 6px;
			box-sizing: border-box;
			display: flex;
			justify-content: center;
			align-items: center;

			span {
				width: 16px;
				height: 16px;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: 5px;
				transition: .3s all;
			}

			// margin-right: 20px;
			&:hover {
				border-radius: 6px;
				color: rgba(23, 62, 136, 1);
				background-color: rgba(242, 243, 245, 1);
			}
		}

		.login_btn {
			font-size: 12px;
			width: auto;
			height: 60px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			position: absolute;
			right: 0;
        ::v-deep .el-badge{
         margin-right: 24px;
           .el-badge__content--danger{
               background-color:rgb(24, 104, 241) !important;
           }
         }
         .phone{
           margin-right: 30px;
           display: flex;
           align-items: center;
           line-height: 22px;
           font-size: 14px;
           font-weight: 500;

           img{
             width: 18px;
             height: 18px;
             margin-right:8px ;
           }
         }


			.head {
				width: 33px !important;
				height: 33px;
				border-radius: 50%;
         margin-left: 20px;

				img {
           cursor: pointer;
					width: 33px;
					height: 33px;
					border-radius: 50%;
				}
			}
		}


	}

}

.box {
	width: 100%;
	// height: 100%;
	// min-height: 100vh;
	display: flex;

	background: #f6f6f6;

	.main_box {
		width: 100%;
		// max-width: 1600px;
		margin: 0 auto;
		height: 100%;
		// min-height: 100vh;
		box-sizing: border-box;
		margin: 0 auto;
     position: relative;
		.childMenu_box {
			width: 100%;
			height: 500px;
			position: fixed;
			left: 0;
			top: 76px;
			background-color: rgba(255, 255, 255, 1);

			z-index: 202;
			display: flex;
			justify-content: center;
			align-items: flex-start;

			.child_menus_main {
				width: 1440px;
				height: 500px;
				border-radius: 0 0 8px 8px;
				box-sizing: border-box;
				display: flex;
				justify-content: center;
				align-items: center;

				.left_box {
					width: 210px;
					height: 480px;
					padding: 20px;
					margin: 5px;
					box-shadow: 5px 5px 10px 0 rgba(0, 0, 0, 0.1);
					box-sizing: border-box;
					.title {
						font-size: 20px;
						margin-top: 10px;
					}
					.tips{
						font-size: 12px;
						color: #4E5969;
						line-height: 24px;
					}
				}
			}
		}

		.menu_box {
			width: 100%;
			height: 55px;
			background-color: white;
			display: none;

			.title {
				width: 100%;
				height: 60px;
				padding: 0 20px;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.left_box {
					height: 60px;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.menu_icon {
						cursor: pointer;
						width: 25px;
						min-width: 25px;
						height: 25px;
						background-image: url('@/assets/layout/homepage-menu.png');
						background-size: cover
					}

					.logo {
						width: 30px;


						margin-left: 20px;
					}

					.menu1_box {
						width: auto;
						height: 60px;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						font-size: 1rem;

						.menu1 {
							padding: 0 5px;
							box-sizing: border-box;
							font-size: 1rem;
							font-weight: 500;
						}
					}
				}

				.login_btn {
					font-size: 12px;
					width: auto;
					height: 60px;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.head {
						width: 33px !important;
						height: 33px;
						border-radius: 50%;
						margin-right: 20px;

						img {
							width: 33px;
							height: 33px;
							border-radius: 50%;
						}
					}
				}
			}
		}

		.content_box {
			position: relative;
			width: 100%;
			margin: 0 auto;
			box-sizing: border-box;
		}
	}
}

@media screen and (max-width:1024px) {
	.box {
		width: 100%;
		display: flex;
		background: rgb(255, 255, 255);
		.menuList{
			display: none;
		}
		.main_box {
			width: 100%;
			margin: 0 auto;
			height: 100%;
			// min-height: 100vh;
			box-sizing: border-box;

			.menu_box {
				width: 100%;
				height: 52px;
				display: block;

				.title {
					width: 100%;
					height: 52px;
					padding: 0 5px;
					box-sizing: border-box;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.left_box {
						// width: 150px;
						height: 60px;
						display: flex;
						justify-content: space-between;

						align-items: center;

						.menu_icon {
							width: 20px;

							background-image: url('@/assets/layout/homepage-menu.png');
							background-size: cover // margin-right: 10px;
						}

						.logo {
							width: 34px;
							// margin-left: 20px;
						}

						.menu1_box {
							height: 60px;
							display: flex;
							// justify-content: flex-start;
							align-items: center;
							font-size: 8px;
							-webkit-text-size-adjust: none;

							.menu1 {
								padding: 0 5px;
								// box-sizing: border-box;
								font-size: 16px;
								-webkit-text-size-adjust: none;
								// font-weight: 500;
							}
						}
					}

					.login_btn {
						font-size: 16px;
						width: auto;
						height: 60px;
						display: flex;
						justify-content: flex-start;
						align-items: center;

						.head {
							width: 23px !important;
							height: 23px;
							border-radius: 50%;
							margin-right: 20px;

							img {
								width: 23px;
								height: 23px;
								border-radius: 50%;
							}
						}
					}
				}

				.menuList {
					width: 100%;
					height: 50px;
					background-color: #38609a;
					margin: 0 auto;
					// padding: 0 30px;
					box-sizing: border-box;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					z-index: 99;

					.menu2 {
						// width: 50%;
						// min-width: 25%;
						height: 50px;
						font-size: 16px;
						min-width: 120px;
						box-sizing: border-box;
						color: rgba(255, 255, 255, 1);
						display: flex;
						justify-content: center;
						align-items: center;
						transition: 0.3s all;

						// margin-right: 20px;
						&:hover {
							border-radius: 5px;
							background-color: rgba(0, 0, 0, 0.3);
						}
					}
				}
			}

		}
	}
}
</style>

<style scoped lang="less">
.d-header {
	display: flex;
	align-items: center;
}

.d-content {
	padding-top: 20px;

	.d-menu {
		display: flex;
		align-items: center;
		border-radius: 6px;
		padding: 10px 0px 10px 20px;
		border-top: 1px solid #dddddd;
	}

	.d-menu:hover {
		background-color: #f2f3f4;
		cursor: pointer;
	}
}

.d-content > :nth-last-child(1) {
	border-radius: 6px;
	// border-bottom: 1px solid #dddddd;
}

.logoutBtn {
	display: flex;
	align-items: center;
	font-size: 1rem;
	margin: 0 auto;
	// width: 99px;
	height: 36px;
	padding: 10px 20px;
	border-radius: 5px;
	color: #1868f1;
	border: none;
	background-color: #f5f5f5;
	cursor: pointer;
}

.popover_box {
	display: flex;
	flex-direction: column;
	// align-items: center;
	// justify-content: center;
	// height: 143px;
	border-radius: 15px;
	.userName {
		font-size: 16px;
		font-weight: 500;
		line-height: 24px;
		color: #1d2129;
		padding: 8px 16px;
		border-bottom: 1px solid #e7e7e7;
	}
	.personal {
		cursor: pointer;
		margin-top: 8px;
		height: 20px;
		padding: 8px 16px;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: left;
		color: #4e5969;
		&:hover {
			background: #f5f6f7;
			border-radius: 4px;
			color: #1d2129;
		}
	}

	.exitlogon {
		cursor: pointer;
		margin-top: 8px;
		height: 20px;
		padding: 8px 16px;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: left;
		color: #86909c;
		&:hover {
			background: #f5f6f7;
			border-radius: 4px;
			color: #ec655f;
		}
	}
	// padding:0 30px;
	.headImg {
		cursor: pointer;
		width: 49px;
		height: 49px;
		border-radius: 50%;
		margin: 0 auto;
		cursor: pointer;
	}

	p {
		width: 99px;
		overflow: hidden;

		text-overflow: ellipsis;

		white-space: nowrap;
		text-align: center;
	}
}
</style>
