<template>
	<div class="advertising_box">
		<div class="bzkjBox"><img src="@/assets/layout/logo.png" class="logo" alt="" /><span>标准空间</span></div>
		<div class="advertising">
			<div class="title_box">
				<p class="title">{{ titleList.title }}</p>
				<p class="area">{{ titleList.feature }}</p>
			</div>
			<div class="address_box">{{ titleList.city }}/{{ titleList.district }}/{{ titleList.street }}</div>
			<div class="img_boxone boxs">
				<div class="tab_box">
					<div class="tab_item" :class="selectIndex == 0 ? 'activeed' : ''" @click="onSelect(0)">视频</div>
					<div class="tab_item" :class="selectIndex == 1 ? 'activeed' : ''" @click="onSelect(1)">图片</div>
				</div>
				<!-- 轮播图 -->
				<el-carousel
					type="card"
					indicator-position="none"
					interval="5000"
					class="swiper_box"
					autoplay="false"
					v-if="selectIndex == 1"
					object-fit="fill"
				>
					<el-carousel-item v-for="(item, index) in carouselArr" :key="index">
						<img :src="`${proxyAddress}${item}`" alt="" />
					</el-carousel-item>
				</el-carousel>
				<div class="redio_box" v-if="selectIndex == 0">
					<video :src="`${proxyAddress_video}${titleList.displayVideo}`" controls style="width: 100%; height: 100%; object-fit: fill" autoplay />
				</div>
			</div>
			<div class="describe_box">
				<div class="zsld">
					<h5>招商亮点</h5>
					<div v-html="titleList.investmentHighlight" class="content"></div>
				</div>
				<div class="card_box">
					<div class="card">
						<!-- 经纪人 -->
						<div class="card_jjr">
							<img class="jjrtx" :src="`${proxyAddress}${titleList.linkPic}`" alt="" />
							<div class="jjrBox">
								<span class="linkName">{{ titleList.linkName }}</span>
								<div class="jjrzw"><img class="jjrimg" src="@/assets/layout/logo.png" alt="" /> {{ titleList.linkPosition }}</div>
							</div>
						</div>
						<!-- 公司 -->
						<div class="company">
							<p>{{ titleList.company }}</p>
						</div>
						<div class="phone">
							<img class="phone_img" src="@/assets/images/home/<USER>" alt="" />
							<span>{{ telephone }}</span>
						</div>
					</div>
				</div>
			</div>
			<div class="tenantable">
				<h5>可用空间</h5>
				<el-table :data="tableData" style="width: 100%">
					<el-table-column prop="floorNum" label="空间" />
					<el-table-column prop="rentArea" label="出租面积" />
					<el-table-column prop="minRentPeriod" label="最短租期" />
					<el-table-column prop="paymentMethod" label="支付方式" />
					<el-table-column prop="rentPrice" label="租金" />
					<el-table-column prop="propertyFee" label="物业费" />
					<el-table-column prop="waterFee" label="水费" />
					<el-table-column prop="electricityFee" label="电费" />
					<el-table-column prop="feeDescription" label="空调收费标准" :width="220" />
					<el-table-column prop="feeDescription" label="" type="expand">
						<template #default="props">
							<!-- {{props}} -->
							<div style="display: flex">
								<div v-for="item in props.row.standardSpaceFloorPicVOList" :key="item">
									<img style="width: 100%" alt="" :src="`${proxyAddress}${item.floorPic}`" />
								</div>
							</div>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<div class="map_box">
				<h4>配套服务</h4>
				<div v-html="titleList.supportServices" class="content"></div>
				<div class="gaode">
					<rat :coordinate="titleList.addressCoordinate"></rat>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { AdvertDetail } from '../../../src/api/home.js';
import { useRoute } from 'vue-router';
import rat from '@/RatMap.vue';
import http from '@/utils/http';
const proxyAddress = 'https://static.biaobiaozhun.com/';
const proxyAddress_video = ref('https://static.biaobiaozhun.com/'); //视频前缀
//获取新闻详情信息
const id = ref(0);
const zb = ref('115.72,40.02');

const route = useRoute();
console.log(route.query, 7812);

onMounted(async () => {
	let id = route.query.id;
	await getNewsDetail(id);

	// console.log(id,123121,route.query);
});
const titleList = ref([]);
const spacePic = ref('');
const spacePicArr = ref([]);
const carousel = ref('');
const carouselArr = ref([]);
const tableData = ref([]);
const coordinateValue = ref('');
const telephone = ref('');

const getNewsDetail = async (id) => {
	console.log(id, 12312);

	await AdvertDetail({ spaceId: id })
		.then((res) => {
			// console.log(spaceId,12312);
			console.log('新闻1231', res);
			titleList.value = res.data;
			spacePic.value = res.data.carousel;
			spacePicArr.value = spacePic.value.split(',');
			console.log(spacePicArr.value, 12312);
			carousel.value = res.data.carousel;
			carouselArr.value = carousel.value.split(',');
			console.log(carouselArr.value, 123121);
			tableData.value = res.data.standardSpaceFloorVOList;
			coordinateValue.value = res.data.addressCoordinate;
			telephone.value = res.data.linkTel;
			// 在第三个和第八个后面加-
			telephone.value = telephone.value.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
			console.log(telephone.value, 8876);
		})
		.catch((err) => {
			console.log('请求失败');
		});
};

const selectIndex = ref(0);
const onSelect = (tab) => {
	console.log(tab, 73487);
	selectIndex.value = tab;
};
</script>
<style scoped lang="less">
.advertising_box {
	width: 100%;
	height: 100%;
	min-height: 100vh;
	background: #fff;
	// padding-top: 20px;
	.bzkjBox {
		width: 150px;
		height: 40px;
		margin: 0 auto;
		display: flex;
		align-items: center;
		line-height: 40px;

		.logo {
			width: 30px;
			height: 30px;
			margin-left: 20px;
		}
	}
}
.advertising {
	width: 1200px;
	margin: 0 auto;
	.title_box {
		width: 1200px;
		height: 80px;
		box-shadow: 0px 4px 10px 2px rgba(0, 0, 0, 0.5);
		background: rgba(255, 255, 255, 0.38);
		// opacity: 0.38;
		// margin-top: 39px;
		text-align: center;

		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 24px;
			font-weight: 400;
			// line-height: 32px;
			letter-spacing: 0px;
			text-align: center;
			// margin-top: 8px;
		}
		.area {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			// line-height: 21px;
			letter-spacing: 0px;
			text-align: center;
		}
	}
	.address_box {
		margin: 16px 16px;
	}
	.boxs {
		width: 1200px;
		height: 376px;
		margin-top: 20px;
		display: flex;
		justify-content: space-between;
		position: relative;
		.swiper_box,
		.redio_box {
			width: 1200px;
			height: 376px;
			img {
				width: 100%;
				height: 100%;
			}
			.video {
				background-size: cover;
				width: 1200px;
				height: 376px;
			}
		}
	}
	.tab_box {
		width: 127px;
		height: 29px;
		display: flex;
		justify-content: space-around;
		border-radius: 15px;
		background: rgba(0, 0, 0, 0.45);
		line-height: 29px;
		color: #fff;
		// text-align: center;
		position: absolute;
		top: 15px;
		left: 50%;
		z-index: 99;
		.tab_item {
			padding: 0 10px;
			text-align: center;
		}
	}
	.activeed {
		border-radius: 15px;
		width: 40%;
		background: rgba(0, 0, 0, 0.5);
	}
	.describe_box {
		display: flex;
		justify-content: space-between;
		h5 {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 20px;
			font-weight: 400;
			line-height: 26px;
			letter-spacing: 0px;
			text-align: left;
		}
		.content {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 21px;
			letter-spacing: 0px;
			// text-align: center;
		}
		.card_box {
			width: 224px;
			height: 263px;
			box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.5);
			background: rgb(255, 255, 255);
			// opacity: 0.38;

			.card {
				.card_jjr {
					display: flex;
					margin-top: 20px;
					margin-left: 15px;
					.jjrtx {
						width: 68px;
						height: 68px;
					}
					.jjrBox {
						margin-left: 14px;
						margin-top: 10px;
						.linkName {
							font-size: 16px;
							font-weight: 700;
						}
						.jjrzw {
							display: flex;
							align-items: center;
							margin-top: 10px;
							.jjrimg {
								width: 20px;
								height: 20px;
							}
						}
					}
				}
				.company {
					font-size: 18px;
					text-align: center;
					color: #38609a;
					font-weight: 700;
					margin-top: 30px;
					margin-bottom: 30px;
				}
				// 手机号
				.phone {
					display: flex;
					margin-top: 20px;
					width: 200px;
					color: rgb(0, 0, 0);
					font-family: 微软雅黑;
					font-size: 16px;
					font-weight: 700;
					line-height: 21px;
					letter-spacing: 0px;
					text-align: center;
					display: flex;
					justify-content: center;
					align-items: center;

					.phone_img {
						width: 24px;
						height: 24px;
					}
				}
			}
		}
	}
	.tenantable {
		h5 {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 20px;
			font-weight: 400;
			line-height: 26px;
			letter-spacing: 0px;
			text-align: left;
		}
	}
}
.map_box {
	width: 1200px;
	height: 400px;
	margin: 0 auto;
	margin-top: 20px;
	margin-bottom: 20px;
	// background: rgb(246, 246, 246);
	// position: relative;
	h4 {
		color: rgb(0, 0, 0);
		font-family: 微软雅黑;
		font-size: 20px;
		font-weight: 400;
		line-height: 26px;
		letter-spacing: 0px;
		text-align: left;
	}
	.gaode {
		width: 100%;
		height: 400px;
		display: flex;
	}
}
.facility_box {
	width: 1200px;
	height: 400px;
	margin: 0 auto;
	margin-top: 50px;
	margin-bottom: 20px;
	// background: rgb(246, 246, 246);
	.facility_item {
		display: flex;
		h4 {
			width: 64px;
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 21px;
			letter-spacing: 0px;
			text-align: left;
		}
		p {
			width: 977px;
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 21px;
			letter-spacing: 0px;
			text-align: left;
			margin-left: 80px;
		}
	}
}
</style>
