<template>
	<div class="footer-container">
		<div class="footer-links">
			<div v-for="(item, index) in footerList" :key="index" :class="item.href || item.path || item.dialogStatus ? 'footer-link' : ''">
				<div @click="handletoUrl(item)">{{ item.name }}</div>
			</div>
		</div>
		<div class="copyright">
      Copyright © 术木智能信息技术（青岛）有限公司, All Rights Reserved.
      <a href="https://beian.miit.gov.cn/" target="_blank">鲁ICP备2025141259号-2</a>
    </div>
		<el-dialog v-model="standardPrivacyDialog" fullscreen>
			<standardPrivacy @handleReturn="handleReturn"></standardPrivacy>
		</el-dialog>

		<el-dialog v-model="standardTermsUseDialog" fullscreen>
			<standardTermsUse @handleReturn="handleReturn"></standardTermsUse>
		</el-dialog>
	</div>
</template>

<script setup>
import standardPrivacy from '@/views/equityServices/standardPrivacy.vue';
import standardTermsUse from '@/views/equityServices/standardTermsUse.vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter();

const standardPrivacyDialog = ref(false);

const standardTermsUseDialog = ref(false);

const footerList = ref([
	{
		name: '联系我们',
		path: '/relation',
	},
	{
		name: '隐私声明',
		dialogStatus: 1,
	},
	{
		name: '使用条款',
		dialogStatus: 2,
	},
	{
		name: '术木智能官方网站',
		href: 'https://smuai.cn/#/',
	},
	{
		name: '盛佳联行官方网站',
		href: 'https://sjlh.com.cn/',
	},
	{
		name: '咨询热线400-677-8895',
	},
]);

function handleReturn() {
	// 关闭对话框
	standardPrivacyDialog.value = false;
	standardTermsUseDialog.value = false;
}

const handletoUrl = (item) => {
	if (item.dialogStatus == 1) {
		standardPrivacyDialog.value = true;
		return;
	}
	if (item.dialogStatus == 2) {
		standardTermsUseDialog.value = true;
	}

	if (item.path) {
		router.push(item.path);
	} else {
		if (item.href) {
			window.open(item.href, '_blank');
		}
	}
};
</script>

<style lang="less" scoped>
.footer-container {
	position: absolute;
	bottom: 0px;
	width: 100%;
	height: 52px;
	padding: 18px 0;
	background: rgba(0, 0, 0, 0.5);
	color: #fff;
	text-align: center;
	.footer-links {
		display: flex;
		justify-content: center;
		gap: 60px;
		flex-wrap: wrap;
		margin-bottom: 10px;
		font-weight: 400;
		font-size: 14px;
		color: rgba(255, 255, 255, 0.85);
		line-height: 20px;
		.footer-link {
			cursor: pointer;
		}
	}
	.copyright {
		font-weight: 400;
		font-size: 14px;
		color: rgba(255, 255, 255, 0.85);
		padding: 0 10px;
		line-height: 20px;
	}
}
@media (max-width: 768px) {
	.footer-container {
		height: auto !important;
		.footer-links {
			padding: 0 10px;
			gap: 20px !important;
		}
	}
}
</style>
