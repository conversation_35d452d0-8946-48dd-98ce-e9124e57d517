<template>
	<div class="map_box">
		<div class="search">
			<div class="search_box">
				<!-- <div style="display: flex; justify-content: flex-start; align-items: center"> -->
				<div class="box_1 city_box">
					<div class="city_box_img">
						<img src="../../assets/positionGroup.png" alt="" />
					</div>
					<arco-cascader
						v-model="cascaderModel"
						@change="handleChange"
						:arrow-icon="ArrowDown"
						path-mode
						:fieldNames="{ label: 'label', value: 'label', children: 'children' }"
						ref="cascaderDom"
						:options="$vuexStore.state.cityArray"
						:style="{ width: '218px' }"
					/>
				</div>

				<div class="box_1" style="width: 190px">
					<arco-select v-model="businessDistrict" placeholder="选择商圈" allow-clear @change="handleDistrictSearch">
						<arco-option
							v-for="(item, value) in businessDistrictList"
							:style="{ color: item.businessDistrictName === businessDistrict ? '#1868F1' : '' }"
							:key="value"
							:label="item.businessDistrictName"
							:value="item.businessDistrictName"
						></arco-option>
					</arco-select>
				</div>
				<!-- </div> -->

				<!-- <div style="display: flex; justify-content: flex-end; align-items: center; margin-right: 8px"> -->
				<div class="box_1" style="width: 190px; margin-left: 48px">
					<arco-select v-model="buildingTypesValue" placeholder="请选择资产类型" allow-clear>
						<arco-option
							v-for="(item, value) in buildingTypes"
							:style="{ color: item.value === buildingTypesValue ? '#1868F1' : '' }"
							:key="value"
							:label="item.label"
							:value="item.value"
						></arco-option>
					</arco-select>
				</div>
				<div class="box_1" style="width: 190px">
					<arco-select v-model="rateValue" placeholder="请选择资产评级" allow-clear>
						<arco-option
							v-for="(item, value) in rate"
							:style="{ color: item.value === rateValue ? '#1868F1' : '' }"
							:key="value"
							:label="item.label"
							:value="item.value"
						></arco-option>
					</arco-select>
				</div>
				<div class="box_1" style="width: 190px">
					<arco-select v-model="securitization" placeholder="是否证券化" allow-clear>
						<arco-option label="证券化不限" :style="{ color: securitization === '' ? '#1868F1' : '' }" :value="''"></arco-option>
						<arco-option label="是" :style="{ color: securitization === true ? '#1868F1' : '' }" :value="true"></arco-option>
						<arco-option label="否" :style="{ color: securitization === false ? '#1868F1' : '' }" :value="false"></arco-option>
					</arco-select>
				</div>

				<div class="box_1" style="width: 190px">
					<arco-select v-model="deal" placeholder="交易历史" allow-clear>
						<arco-option label="交易历史不限" :style="{ color: deal === '' ? '#1868F1' : '' }" :value="''"></arco-option>
						<arco-option label="是" :style="{ color: deal === true ? '#1868F1' : '' }" :value="true"></arco-option>
						<arco-option label="否" :style="{ color: deal === false ? '#1868F1' : '' }" :value="false"></arco-option>
					</arco-select>
				</div>

				<div class="box_1" style="width: 190px">
					<arco-select
						v-model="inputValue"
						class="allow_search"
						allow-search
						@search="handleArcoSelect"
						value-key="buildingName"
						@change="handleChangeOption"
						placeholder="请输入资产/地点关键词"
						allow-clear
					>
						<arco-option v-for="(item, value) in copyOptions" :key="value" :label="item.buildingName" :value="item">
							<div class="allowSearch">
								<div class="searchName">
									<el-icon><Location /></el-icon>
									<div class="search_Name" v-html="item.buildingTitle"></div>
								</div>
								<div class="searchDetails">
									{{ item.street }}
								</div>
							</div>
						</arco-option>
					</arco-select>
				</div>

				<el-button type="primary" style="width: 76px" size="large" color="#1868F1" @click="searchBtn()">搜索</el-button>
				<el-button style="width: 76px" size="large" @click="handleClear('1')">重置</el-button>
				<!-- </div> -->
			</div>

			<div class="gaode">
				<div class="details" v-if="radioDraw && selectedRadius !== '' && selectedRadiusStr">
					<div class="titleContent">
						<!-- <img src="../../assets/location.png" alt="" /> -->
						<!-- <div v-if="radioDraw === 'circle'" class="titleLocation">请选择半径后在地图上选择圆心圈选范围</div> -->
						<el-dropdown v-if="radioDraw === 'circle'" :teleported="false" trigger="click" :hide-on-click="true">
							<!-- <span class="el-dropdown-link detailsDropdown">
								{{ selectedRadius ? selectedRadius + 'm' : '300m' }}<el-icon style="margin: -1.5px 0 0 2px"><CaretBottom /></el-icon>
							</span> -->
							<div class="radius_box">
								<div class="radius_box_item">选择半径</div>
								<div class="radius_box_item_2">
									{{ selectedRadius ? selectedRadius + 'M' : '300M' }}<el-icon style="margin: -1.5px 0 0 2px"><CaretBottom /></el-icon>
								</div>
							</div>

							<template #dropdown>
								<el-dropdown-menu class="detailsDropdownMenu">
									<el-dropdown-item @click="handleButtonClick(300)" :style="{ color: selectedRadius === 300 ? '#1868F1!important' : '' }"
										>300M<el-icon :style="{ display: selectedRadius === 300 ? 'block' : 'none' }"><CircleCheckFilled /></el-icon
									></el-dropdown-item>
									<el-dropdown-item @click="handleButtonClick(500)" :style="{ color: selectedRadius === 500 ? '#1868F1!important' : '' }"
										>500M<el-icon :style="{ display: selectedRadius === 500 ? 'block' : 'none' }"><CircleCheckFilled /></el-icon
									></el-dropdown-item>
									<el-dropdown-item @click="handleButtonClick(1000)" :style="{ color: selectedRadius === 1000 ? '#1868F1!important' : '' }"
										>1000M<el-icon :style="{ display: selectedRadius === 1000 ? 'block' : 'none' }"><CircleCheckFilled /></el-icon
									></el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<div v-if="radioDraw === 'polygon'" class="titleLocation">请在地图上点击选点圈画范围</div>
					</div>
					<div class="quit" @click="handlequit">退出</div>
				</div>

				<div class="tab_box">
					<div class="tab_box_left">
						<div class="tab_box_left_item">
							可视区域内为您找到 <span class="tab_box_left_item_num">{{ cardList.length }}</span> 个楼宇
						</div>
						<div class="tab_box_right_item" @click="listShow = listShow ? false : true">
							<el-icon v-if="listShow"><ArrowUp /></el-icon>
							<el-icon v-else><ArrowDown /></el-icon>
						</div>
					</div>
					<div
						class="list_box"
						v-if="cardList.length > 0"
						ref="scrollbarRef"
						infinite-scroll-distance="10"
						infinite-scroll-immediate="false"
						:style="{ height: listShow ? '100%' : '0px', paddingBottom: listShow ? '0px' : '0px', maxHeight: 'calc(100vh - 400px)' }"
					>
						<div
							class="list"
							:class="{ 'list-item-curr': item.buildingId === currentBuildingId }"
							v-for="(item, index) in cardList"
							:key="index"
							@click="gomap(item)"
							:ref="handleGetRef(item.buildingId)"
						>
							<div class="list-item">
								<div class="img"><img :src="http_oa + item.mainImgUrl" alt="" /></div>
								<div class="tips_box">
									<div class="title" :title="item.buildingName">
										{{ item.buildingName }}
									</div>
									<div class="address_content">
										<div class="address_icon">
											<img src="../../assets/zcbuilding.png" alt="" />
											<div class="address_text">{{ item.buildYear }}年建成</div>
										</div>
										<div class="address_icon_box">
											<img src="../../assets/zcposition.png" alt="" />
											<div class="address_texts" :title="item.street">{{ item.street }}</div>
										</div>
									</div>

									<div class="tag_list">
										<div class="tag1">维护{{ item.maintenance }}</div>
										<div class="tag1 tag2">区域潜力-{{ item.regionalPotential }}</div>
										<div class="tag1 tag3">商业活力-{{ item.businessDynamism }}</div>
									</div>
									<div class="money">
										<div class="money_item">
											<span class="money_item_span">{{ item.evaluation }}</span>
											<span class="money_item_span_2">元/平</span>
										</div>
										<div class="money_item_span_2">
											人均可支配收入<span class="money_item_span_3">{{ item.spendingPower }}</span
											>元
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="tabBox">
					<div class="reduce_content">
						<div class="reduce_content_item" @click="handleReduce('add')">
							<img src="../../assets/reduceAdd.png" alt="" />
						</div>
						<div class="reduce_content_item" @click="handleReduce('minus')">
							<img src="../../assets/reduceMinus.png" alt="" />
						</div>
					</div>

					<div class="ability">
						<div class="contentDetals contentDetals_c" @click="handleDraw('circle')">
							<div class="imgDetails" v-if="radioDraw !== 'circle'">
								<img src="../../assets/circular.png" style="width: 20px; height: 20px" alt="" />
							</div>
							<div class="detailsbj" v-if="radioDraw === 'circle'">退出半径找楼</div>
							<div class="detailsbj_c" v-else>半径找楼</div>
						</div>
						<div class="contentDetals" @click="handleDraw('polygon')">
							<div class="imgDetails" v-if="radioDraw !== 'polygon'">
								<img src="../../assets/polygon.png" style="width: 20px; height: 20px" alt="" />
							</div>
							<div class="detailsbj" v-if="radioDraw === 'polygon'">退出点选多边形</div>
							<div class="detailsbj_c" v-else>点选多边形</div>
						</div>
					</div>
					<div class="btn active" @click="handleTableShow()">
						{{ tableShow ? '收起' : '展开'
						}}<span :style="{ transform: tableShow ? 'rotate(-90deg)' : 'rotate(90deg)' }"
							><el-icon><ArrowRight /></el-icon
						></span>
					</div>
					<el-tabs v-model="activeName" class="demo-tabs">
						<!-- <el-tab-pane label="市场统计" name="first"> -->
						<div class="table">
							<!-- <p>大楼信息</p> -->
							<el-table
								:data="real"
								style="width: 100%"
								:height="tableShow ? '300px' : '128px'"
								border
								header-row-class-name="tableHeader"
								:stripe="false"
								:lazy="true"
								size="small"
							>
								<el-table-column
									v-for="column in tableColumns"
									:key="column.prop"
									:prop="column.prop"
									:label="column.label"
									:width="column.width"
									style="height: 100px"
								>
									<template #default="scope">
										<Progress v-if="column.label.includes('%')" :progress="{ percentage: scope.row[column.prop], color: '#00C261' }"></Progress>
										<span v-else-if="column.label === '序号'">{{ scope.$index + 1 }}</span>
										<span v-else>{{ scope.row[column.prop] }}</span>
									</template>
								</el-table-column>
							</el-table>
						</div>
					</el-tabs>
				</div>
				<!-- <div
					v-if="markerMenuShow"
					style="width: 100px; height: 200px; background-color: red; position: absolute; z-index: 9"
					:style="{ left: `${markerMenuPos.x+30}px`, top: `${markerMenuPos.y}px` }"
				>
					菜单111111
				</div> -->
				<gaode
					@updateRadius="handleUpdateRadius"
					@handlePolygon="handlePolygon"
					@handleupDateCity="handleupDateCity"
					@clickChild="clickEven"
					@handleParentScroll="handleParentScroll"
					@clickMarker="gomap"
					@mouseoverMarker="mouseoverMarker"
					@mouseoutMarker="mouseoutMarker"
					:radius="selectedRadius"
					:radioDraw="radioDraw"
					:markers="cardList"
					@searchMap="getCardListData"
					ref="aMap"
				></gaode>
			</div>
		</div>
	</div>
</template>

<script setup>
import arcoSelect from '@/component/arcoComponents/select/index.vue';
import { ref, onMounted, computed, reactive, nextTick } from 'vue';
import gaode from '@/MapContainer.vue';
import { getMarketStatistics, getDictList, getTransactionStatistics, getBuildingCardList, getAllBusinessDistrict } from '@/api/syt.js';
import { toRaw } from 'vue';
import { useStore } from '../../store';
import { storeToRefs } from 'pinia';
import { color } from 'echarts';
const activeName = ref('first');
const listShow = ref(true);
const selectedRadiusStr = ref(false);
const tableShow = ref(false);
const radioDraw = ref(); // 画圆还是画多边形
const store = useStore();
const { http_oa } = storeToRefs(store);
const aMap = ref(null);
const tableColumns = [
	{
		prop: '',
		label: '序号',
		width: 60,
	},
	{
		prop: 'buildingType',
		label: '资产类型',
	},
	{
		prop: 'buildingNum',
		label: '数量',
	},
	{
		prop: 'coveredArea',
		label: '覆盖面积(万m²)',
	},
];
const lng = ref(null);
const lat = ref(null);
const real = ref([]);
// 动态添加ref
const nodeRefs = {};
const scrollbarRef = ref();
const buildOffsetTop = ref();
const quStats = ref(false);
let lnglatObj = reactive({
	maxLatitude: '',
	minLatitude: '',
	maxLongitude: '',
	minLongitude: '',
});
const city = ref('');
const county = ref('');
// 选中城市
const cascaderDom = ref(null);
const cascaderModel = ref([]);
const checkedObj = ref([]);
const selectedRadius = ref(); // 默认半径值
const rateValue = ref('');
const buildingTypesValue = ref('');
const securitization = ref('');
const deal = ref('');
const inputValue = ref('');
const cardList = ref([]);
const copyOptions = ref([]);
const real1 = ref([]);
const rate = ref([]);
const buildingTypes = ref([]);
const businessDistrict = ref(''); //商圈
const businessDistrictList = ref([]); //商圈列表
const currentBuildingId = ref(''); //选中楼宇Id
const markerMenuPos = ref({});
const markerMenuShow = ref(false);

const propsCascader = ref({
	value: 'label',
	children: 'children',
});

// 初始化
onMounted(() => {
	// handleSearch();
	getDict();
});

function mouseoverMarker(e) {
	console.log('🚀 ~ mouseoverMarker ~ e:', e);
	markerMenuPos.value = e;
	markerMenuShow.value = true;
}
function mouseoutMarker() {
	console.log('🚀 ~ mouseoutMarker ~ mouseoutMarker:');
	markerMenuShow.value = false;
}
function handleArcoSelect(val) {
	if (val) {
		getBuildingCardList({
			currentPage: 1,
			pageSize: 10000,
			city: city.value,
			district: county.value,
			buildingRate: rateValue.value, // 资产评级
			businessDistrict: businessDistrict.value || '', //商圈
			distribution: securitization.value, //证券化
			deal: deal.value, // 交易历史
			buildingType: buildingTypesValue.value, // 资产类型
			keyword: val,
		}).then((res) => {
			if (res.code === 200) {
				if (res.data?.rows?.length > 0) {
					copyOptions.value = setHightLight(res.data?.rows, val);
				} else {
					copyOptions.value = [];
				}
			}
		});
	} else {
		copyOptions.value = [];
	}
}

function handleChangeOption(val) {
	if (val.buildingName) {
		cardList.value = [val];
		copyOptions.value = [val];
		nextTick(() => {
			aMap.value.addMarker();
			aMap.value.handleSetCenter([cardList.value[0].lng, cardList.value[0].lat], 0, 1);
		});
	} else {
		inputValue.value = '';
		copyOptions.value = [];
		// searchBtn();
		handleClear('1', '1');
		// cardList.value = []
	}
}

function setHightLight(arr, keyword) {
	if (arr) {
		let newArr = JSON.parse(JSON.stringify(arr));
		if (newArr && newArr.length > 0 && keyword) {
			newArr = newArr.filter((item) => {
				item.buildingTitle = item.buildingName;
				let reg = new RegExp(keyword, 'g');
				let replaceString = `<span style='color: #1868f1;font-weight: 500;'>${keyword.trim()}</span>`;
				if (item.buildingTitle.match(reg)) {
					item.buildingTitle = item.buildingTitle.replace(reg, replaceString);
					return item;
				}
			});
			return newArr;
		}
		// 空返回原数组
		if (!keyword) {
			return newArr;
		}
	}
}

// 收起展开
const handleTableShow = () => {
	tableShow.value = !tableShow.value;
	listShow.value = !tableShow.value;
};

// 获取商圈
function funcDistrict() {
	getAllBusinessDistrict({ city: city.value, district: county.value, current: 1, size: 100 }).then((res) => {
		if (res.code === 200) {
			businessDistrictList.value = res.data.rows;
		}
	});
}

//绑定大楼
const handleSearch = async () => {
	real.value = [];
	await getMarketStatistics(queryParams.value)
		.then((res) => {
			if (res.data?.length > 0) {
				let newList = toRaw(res.data);
				real.value = JSON.parse(JSON.stringify(newList));
			}
		})
		.catch((err) => {
			console.log('err', err);
			// 显示错误信息给用户
		});
};

const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = [
				{
					color: null,
					jsonObject: null,
					label: '资产类型不限',
					text: '资产类型不限',
					title: '资产类型不限',
					value: '',
				},
				...res.data,
			];
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = [
				{
					color: null,
					jsonObject: null,
					label: '资产评级不限',
					text: '资产评级不限',
					title: '资产评级不限',
					value: '',
				},
				...res.data,
			];
		})
		.catch((err) => {
			console.log(err);
		});
};

// 绘制多边形
function handleDraw(val) {
	if (val === radioDraw.value) {
		handlequit();
		radioDraw.value = '';
		selectedRadius.value = '';
		return;
	}
	radioDraw.value = val;
	cardList.value = [];
	if (val === 'polygon') {
		selectedRadius.value = '';
		aMap.value.clearMarker();
		aMap.value.handleCircle();
	}
}

function handleReduce(type) {
	if (aMap.value) {
		aMap.value.setZoom(type);
	}
}

// 清除搜索条件
function handleClear(type, num) {
	// county.value = ''; // 市区
	if (!num) {
		selectedRadius.value = ''; // 半径
		rateValue.value = ''; // 评级
		buildingTypesValue.value = ''; // 类型
		securitization.value = ''; // 证券化
		deal.value = ''; // 交易历史
		inputValue.value = ''; // 关键字
		copyOptions.value = [];
	}
	handlequit(type);
}

// 搜索
const searchBtn = () => {
	radioDraw.value = '';
	cardList.value = [];
	selectedRadius.value = '';
	if (aMap.value) {
		aMap.value.handleCircle();
		if (checkedObj.value.length > 0) {
			// 城市更改后且第一次点击搜索
			aMap.value.handleSetCenter(quStats.value ? checkedObj.value : null, !quStats.value ? 1 : 0);
			quStats.value = false;
		} else {
			getCardListData(1);
		}
	}
};

// 获取定位城市
function handleupDateCity(address) {
	// cascaderModel.value = [address.city, address.district];
	cascaderModel.value = [address.city, address.district];

	city.value = address.city;
	county.value = address.district;
	funcDistrict();
}
// 更新半径
const handleUpdateRadius = (radius) => {
	selectedRadius.value = radius;
	setTimeout(() => {
		selectedRadiusStr.value = true;
	}, 500);
};

// 获取地图四个角经纬度
const clickEven = (lnglat) => {
	// selectedRadiusStr.value = false;
	if (selectedRadius.value) {
		lng.value = lnglat?.clickPosition?.lng;
		lat.value = lnglat?.clickPosition?.lat;
	} else {
		lng.value = '';
		lat.value = '';
	}
	lnglatObj.maxLatitude = lnglat?.bounds?.northEast?.lat; // 右上角纬度
	lnglatObj.minLatitude = lnglat?.bounds?.southWest?.lat; // 左下角经度
	lnglatObj.maxLongitude = lnglat?.bounds?.northEast?.lng; // 右上角经度
	lnglatObj.minLongitude = lnglat?.bounds?.southWest?.lng; // 左下角维度
	nextTick(() => {
		getCardListData(lnglat.type);
	});
};

// 切换城市
const handleChange = (val, selectedOptions) => {
	// 字符串转数组截取
	businessDistrict.value = '';
	let obj = {};
	cascaderDom.value.filteredLeafOptions.forEach((element) => {
		if (element.key === cascaderDom.value.activeKey) {
			obj = element;
		}
	});
	checkedObj.value = [obj?.raw?.lng, obj?.raw?.lat];

	// checkedObj.value = [cascaderDom.value.getCheckedNodes()?.[0]?.data?.lng, cascaderDom.value.getCheckedNodes()?.[0]?.data?.lat];
	if (val) {
		quStats.value = true;
		city.value = val[0];
		county.value = val[1];
		cascaderModel.value = [val[0], val[1]];
		funcDistrict();
		// 搜索
		searchBtn();
	} else {
		city.value = '';
		county.value = '';
		cascaderModel.value = [];
	}
};

function handleDistrictSearch(val) {
	if (val) {
		// searchBtn(val);
		radioDraw.value = '';
		cardList.value = [];
		selectedRadius.value = '';
		getCardListData(1, 1);
	} else {
		handleClear();
	}
}

//半径圆实现
const queryParams = computed(() => {
	return {
		currentPage: 1,
		pageSize: lnglatObj.minLongitude ? 10000 : 100,
		city: city.value,
		district: county.value,
		buildingRate: rateValue.value,
		businessDistrict: businessDistrict.value || '', //商圈
		distribution: securitization.value, //证券化
		deal: deal.value, // 交易历史
		buildingType: buildingTypesValue.value,
		keyword: inputValue.value.buildingName,
		lng: lng.value,
		lat: lat.value,
		...lnglatObj,
		distance: selectedRadius.value, // 半径
	};
});
// 多边形
function handlePolygon(params) {
	cardList.value = params;
}

// 卡片信息
const getCardListData = (value, type) => {
	cardList.value = [];
	if (selectedRadius.value || (selectedRadius.value == '' && !value) || inputValue.value.buildingName || businessDistrict.value) {
		queryParams.value.maxLatitude = '';
		queryParams.value.minLatitude = '';
		queryParams.value.maxLongitude = '';
		queryParams.value.minLongitude = '';
	}

	if (!selectedRadius.value) {
		queryParams.value.lng = '';
		queryParams.value.lat = '';
	}

	// 市场统计
	handleSearch();
	getBuildingCardList(queryParams.value)
		.then((res) => {
			if (res.code === 200) {
				if (res.data?.rows?.length > 0) {
					cardList.value = res.data?.rows;
					nextTick(() => {
						aMap.value.addMarker();
						if (type) {
							aMap.value.handleSetCenter([cardList.value[0].lng, cardList.value[0].lat], 0, 1);
						}
					});
				}
			} else {
				cardList.value = [];
				aMap.value.clearMarker();
			}
		})
		.catch((err) => {
			cardList.value = [];
			if (aMap.value) {
				aMap.value.clearMarker();
			}
			console.log('err', err);
			// 显示错误信息给用户
		});
};

// 动态添加ref
const handleGetRef = (buildingId) => {
	nodeRefs[buildingId] = ref(null);
	return (el) => {
		nodeRefs[buildingId].value = el;
	};
};

function handleParentScroll(buildingId) {
	currentBuildingId.value = buildingId;
	nodeRefs[buildingId].value.scrollIntoView({ behavior: 'smooth' });
}

//点击跳转地图
const gomap = (item) => {
	currentBuildingId.value = item.buildingId;
	let params = {
		target: {
			data: item,
		},
	};
	if (aMap.value) {
		aMap.value.clickMarker(params, '1');
	}
};

// 退出
function handlequit(type) {
	if (aMap.value) {
		radioDraw.value = '';
		selectedRadius.value = '';
		aMap.value.handleCircle();
		if (type == '1' && businessDistrict.value) {
			getCardListData(1, 1);
		} else {
			aMap.value.handleSetCenter(null, 1);
		}
	}
}

// 点击半径
const handleButtonClick = (radius) => {
	selectedRadius.value = radius;
	handleUpdateRadius(radius);
	if (aMap.value) {
		aMap.value.creatCircle(null, radius);
	}
	getCardListData('');
};

// 文本展示
const truncateAddress = (address, maxLength = 15) => {
	if (address.length <= maxLength) {
		return address;
	} else {
		return address.slice(0, maxLength) + '...';
	}
};
</script>
<style lang="less">
.arco-cascader-option-active {
	color: #1868f1 !important;
}
</style>
<style lang="less" scoped>
::v-deep .city_box_cascader > :nth-child(1) {
	--el-input-border-color: #fff;
	--el-input-hover-border-color: #fff;
	--el-input-focus-border-color: #fff;
}
.title_box {
	p {
		font-size: 20px;
		margin-top: 8px;
		font-weight: 600;
		margin-bottom: 10px;
	}
}

.table {
	width: 100%;
	height: 100%;
	margin-bottom: 10px;
	text-align: center;

	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}

.search {
	position: relative;
	width: 100%;
	.search_box {
		width: 100%;
		position: absolute;
		top: 16px;
		z-index: 9;
		right: 0;
		flex-wrap: wrap;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		height: 40px;
		margin-left: -6px;
		gap: 12px 0px;
	}
	button {
		margin-left: 10px;
	}

	.clearMap {
		width: 80px;
		height: 27px;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #409eff;
		border: 1px solid #409eff;
		box-sizing: border-box;
		border-radius: 20px;
		font-size: 12px;
		margin-left: 5px;
	}
	.city_box {
		width: 218px !important;
		height: 40px;
		position: relative;
		::v-deep .arco-select-view-single {
			padding-left: 48px;
		}
	}
	.box_1 {
		width: 220px;
		margin-left: 12px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 4px;
		// border: 1px solid rgba(231, 231, 231, 1);
		box-sizing: border-box;
		background-color: #fff;
		::v-deep .arco-select-view-single {
			background-color: #fff;
			height: 40px;
			border-radius: 4px;
		}

		::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
			box-shadow: 0;
		}
		.city_box_img {
			position: absolute;
			z-index: 9999;
			top: 6px;
			left: 12px;
			width: 28px;
			height: 28px;
			img {
				width: 100%;
				height: 100%;
			}
		}
		// .label {
		// 	width: 50%;
		// 	height: 100%;
		// 	font-size: 14px;
		// 	color: rgba(134, 144, 156, 1);
		// 	background-color: rgba(245, 246, 247, 1);
		// 	display: flex;
		// 	justify-content: center;
		// 	align-items: center;
		// }
	}
}

.tag {
	height: 38px;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 10px;
	font-size: 12px;
	color: #515151;
}

.el-card__body,
.el-card {
	padding: 0 !important;
}

.scrollbar-demo-item {
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
	border-radius: 4px;
	background: var(--el-color-primary-light-9);
}

.details {
	z-index: 9;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(10px, -37px);
	width: 132px;
	height: 90px;
	// display: flex;
	// justify-content: space-between;
	// align-items: center;
	box-sizing: border-box;
	background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%);
	border-radius: 6px;
	font-size: 14px;
	.titleContent {
		// display: flex;
		// align-items: center;

		.radius_box {
			height: 66px;
			padding: 12px 8px;
			border-radius: 4px;
			background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%);
			.radius_box_item {
				width: 100%;
				height: 22px;
				font-weight: 500;
				font-size: 14px;
				line-height: 22px;
				color: #1d2129;
				margin-bottom: 4px;
			}
			.radius_box_item_2 {
				width: 92px;
				height: 22px;
				padding: 9px 12px;
				border-radius: 4px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #ffffff;
				box-shadow: 6px 0px 20px 0px #2257bc1a;
				font-weight: 500;
				font-size: 14px;
				color: #4e5969;
			}
		}
		img {
			width: 16px;
			height: 16px;
		}
		.titleLocation {
			margin: 0 10px;
			font-size: 14px;
			font-weight: 500;
			line-height: 22px;
			color: #fff;
		}

		.detailsDropdown {
			font-size: 14px;
			font-weight: 400;
			line-height: 22px;
			color: #fff;
			display: flex;
			align-items: center;
		}
	}
	.quit {
		font-size: 12px;
		font-weight: 500;
		line-height: 20px;
		cursor: pointer;
		color: #fff;
	}
}

.gaode {
	width: 100%;
	height: calc(100vh - 66px);
	position: relative;

	.tab_box {
		width: 400px;
		height: auto;
		background-color: rgba(255, 255, 255, 1);
		border-radius: 6px;
		position: absolute;
		z-index: 2;
		right: 8px;
		top: 68px;

		.tab_list {
			width: 100%;
			height: 40px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			border-bottom: 1px solid rgba(231, 231, 231, 1);
			box-sizing: border-box;

			.tab {
				width: 88px;
				height: 40px;
				display: flex;
				justify-content: center;
				align-items: center;
				position: relative;
			}

			.tabAct {
				width: 88px;

				&::after {
					content: '';
					width: 15px;
					height: 3px;
					position: absolute;
					bottom: 0;
					background-color: rgba(3, 93, 255, 1);
				}
			}
		}

		.list_box {
			width: 100%;
			height: 276px;
			box-sizing: border-box;
			overflow-y: auto;
			overflow-x: hidden;

			.list {
				cursor: pointer;
				width: 100%;
				height: 126px;
				padding: 16px;
				background-color: #fff;
				border-radius: 4px;
				overflow: hidden;
				box-sizing: border-box;
				border-bottom: 1px solid #e5e6eb;
				.list-item {
					display: flex;
					height: 100%;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					box-sizing: border-box;
					border: 4px solid transparent;
				}

				.img {
					width: 124px;
					height: 100%;
					border-radius: 4px;
					margin-right: 12px;
					display: flex;
					justify-content: center;
					align-items: center;
					overflow: hidden;

					img {
						width: auto;
						height: 100%;
					}
				}

				.tips_box {
					width: calc(100% - 136px);
					height: 96px;
					box-sizing: border-box;

					.title {
						width: 100%;
						height: 24px;
						font-size: 16px;
						font-weight: 500;
						line-height: 24px;
						align-items: center;
						color: #1d2129;
						white-space: nowrap; /* 防止文本换行 */
						overflow: hidden; /* 隐藏超出容器的内容 */
						text-overflow: ellipsis; /* 超出容器的内容用省略号表示 */
					}
					.address_content {
						width: 100%;
						height: 18px;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						.address_icon {
							width: 77px;
							display: flex;
							justify-content: flex-start;
							align-items: center;
							margin-right: 16px;
							img {
								margin-right: 4px;
								width: 10px;
								height: 11px;
								margin-top: -3px;
							}
							.address_text {
								font-weight: 400;
								font-size: 12px;
								line-height: 18px;
								color: #4e5969;
								white-space: nowrap; /* 防止文本换行 */
								overflow: hidden; /* 隐藏超出容器的内容 */
								text-overflow: ellipsis; /* 超出容器的内容用省略号表示 */
							}
						}

						.address_icon_box {
							display: flex;
							justify-content: flex-start;
							align-items: center;
							img {
								margin-right: 4px;
								width: 10px;
								height: 11px;
								margin-top: -3px;
							}
							.address_texts {
								width: 128px;
								font-weight: 400;
								font-size: 12px;
								line-height: 18px;
								color: #4e5969;
								white-space: nowrap; /* 防止文本换行 */
								overflow: hidden; /* 隐藏超出容器的内容 */
								text-overflow: ellipsis; /* 超出容器的内容用省略号表示 */
							}
						}
					}
					.address {
						max-width: calc(100% - 20px);
						// width: 100%;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						font-size: 12px;
						color: rgba(134, 144, 156, 1);
						margin-top: 5px;
						white-space: nowrap; /* 防止文本换行 */
						overflow: hidden; /* 隐藏超出容器的内容 */
						text-overflow: ellipsis; /* 超出容器的内容用省略号表示 */
					}

					.tag_list {
						width: 100%;
						height: 20px;
						margin-top: 4px;
						display: flex;
						justify-content: center;
						align-items: center;

						.tag1 {
							white-space: nowrap;
							padding: 1px 8px;
							box-sizing: border-box;
							font-weight: 500;
							font-size: 10px;
							line-height: 22px;
							border-radius: 2px;
							margin-right: 4px;
							background-color: #e8f3ff;
							color: #1868f1;
						}
						.tag2 {
							background-color: #e7f8f8 !important;
							color: #12b8b4 !important;
						}
						.tag3 {
							background-color: #fff3e8 !important;
							color: #f77234 !important;
						}
					}

					.money {
						width: 100%;
						height: 19px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						font-size: 12px;
						margin-top: 11px;
						.money_item {
							display: flex;
							justify-content: flex-start;
							align-items: center;
							.money_item_span {
								font-weight: 600;
								font-size: 18px;
								color: #1868f1;
								margin-right: 4px;
							}
						}
						.money_item_span_2 {
							font-weight: 400;
							font-size: 10px;
							color: #535b66;
						}
						.money_item_span_3 {
							font-weight: 600 !important;
						}
					}
				}
			}
			.list-item-curr {
				border: 2px solid #4080ff !important;
				border-radius: 4px;
			}
		}
	}

	.tab_box_left {
		height: 48px;
		width: 366px;
		padding: 0px 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #e5e6eb;
		.tab_box_left_item {
			font-weight: 500;
			font-size: 14px;
			line-height: 22px;
			color: #1d2129;
		}
		.tab_box_left_item_num {
			font-weight: 600 !important;
			color: #1868f1;
		}
	}

	.tabBox {
		width: calc(100% - 8px);
		height: auto;
		max-height: 420px;
		background-color: rgba(255, 255, 255, 1);
		position: absolute;
		border-radius: 8px;
		left: 0px;
		bottom: 0px;
		z-index: 9;
		.reduce_content {
			position: absolute;
			top: -198px;
			left: 0px;
			border-radius: 6px;
			width: 32px;
			height: 64px;
			border-radius: 4px;
			z-index: 10;
			background: #fff;
			padding: 4px;
			.reduce_content_item {
				width: 32px;
				height: 32px;
				img {
					width: 32px;
					height: 32px;
				}
			}
		}
		.ability {
			position: absolute;
			top: -110px;
			left: 0px;
			border-radius: 6px;
			z-index: 10;
			background: #fff;
			.contentDetals_c {
				border-bottom: 1px solid #e5e6eb;
			}
			.contentDetals {
				margin: 0 16px;
				width: 102px;
				height: 48px;
				display: flex;
				align-items: center;
				line-height: 38px;
				font-size: 14px;
				font-weight: 500;
				color: #1d2129;
				.imgDetails {
					width: 24px;
					height: 24px;
					line-height: 34px;
					text-align: center;
					margin-right: 8px;
				}
				.detailsbj {
					font-weight: 500;
					font-size: 14px;
					color: #1868f1;
				}
				.detailsbj_c {
					font-weight: 500;
					font-size: 14px;
					color: #1d2129;
				}
			}
		}
		.btn {
			position: absolute;
			top: 12px;
			right: 10px;
			z-index: 10;
			font-weight: 400;
			font-size: 14px;
			color: #4e5969;
			span {
				display: inline-block;
				transform: rotate(-90deg);
				margin-left: 5px;
			}
		}

		&::v-deep .el-table--fit {
			border-radius: 8px;
		}

		&::v-deep .el-table th {
			background-color: rgba(245, 245, 245, 1);
		}
	}

	.real {
		width: 420px;
		max-height: 700px;
		overflow-y: auto;
		overflow-x: hidden;
		position: absolute;
		z-index: 2;
		right: 0;
		box-sizing: border-box;
		list-style: none;

		.card_items {
			// margin-bottom: 10px;
			// margin-top: 10px;
			width: 100%;
			height: 140px;
			background-color: #fff;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			box-shadow: 0 5px 5px rgba(0, 0, 0, 0.3);
			border-bottom: #000 1px solid;
			padding: 10px;

			.img {
				width: 135px;
				height: 140px;
				display: flex;
				justify-content: center;
				align-content: center;
				overflow: hidden;

				img {
					width: auto;
					height: 100%;
				}
			}

			.tips_box {
				width: calc(100% - 145px);
				margin-left: 10px;

				.company_name {
					color: #38609a;
					font-size: 16px;
					font-weight: bold;
				}

				.company_text {
					color: #38609a;
					font-size: 14px;
				}

				.tips_body {
					font-size: 12px;
					display: flex;
					justify-content: space-between;
					align-items: center;
					flex-wrap: wrap;

					.tips {
						width: 30%;
						line-height: 14px;
						display: flex;
						justify-content: center;
						align-items: center;
						flex-direction: column;

						.label1 {
							width: 100%;
							text-align: center;
							margin: 5px 0;
						}
					}
				}

				.level {
					font-weight: bold;
				}
			}
		}
	}
}

.market_num {
	width: 100%;
	height: 48px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	padding-left: 15px;
	box-sizing: border-box;
}

.demo-tabs > .el-tabs__content {
	padding: 20px;
	color: #6b778c;
	font-size: 32px;
	font-weight: 600;
}

.demo-tabs {
	padding: 31px 20px 0 20px;
}

.active-button {
	background-color: #409eff; // 蓝色背景
	color: white; // 文字颜色
}

.detailsDropdownMenu {
	width: 100px;

	> :nth-child(n) {
		margin: 4px 8px;
		border-radius: 4px;
		padding: 7px 8px !important;
		font-size: 12px;
		font-weight: 500;
		display: flex;
		justify-content: space-between;
		color: #1d2129;
		.el-icon {
			display: none;
			fill: #1868f1;
		}
		&:hover {
			background: #f5f6f7 !important;
			color: #1d2129;
		}
	}
}

.el-pagination {
	margin-top: 6px;
}
.tipsContent {
	font-size: 14px;
	font-weight: 400;
	height: 38px;
	line-height: 22px;
	color: #86909c;
}

.allowSearch {
	height: 40px;
	margin: 8px 0px;
	.searchName {
		height: 22px;
		display: flex;
		align-items: center;
		.search_Name {
			overflow: hidden; // 隐藏超出部分
			text-overflow: ellipsis; // 超出部分显示省略号
			white-space: nowrap; // 防止文本换行
			width: 174px;
			margin-left: 4px;
			font-weight: 500;
			font-size: 14px;
			line-height: 22px;
			color: #000000;
		}
	}
	.searchDetails {
		margin-left: 18px;
		height: 18px;
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;
		color: #86909c;
	}
}

::v-deep .allow_search .arco-select-view-icon {
	display: none;
}
</style>
