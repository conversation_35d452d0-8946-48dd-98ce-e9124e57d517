<template>
	<el-dialog title="在线聊天" v-model="dialogVisible" width="600px" style="border-radius:15px;">
		<el-scrollbar class="chat-container" height="400px">
			<div class="chat-message-list" ref="messageList" style="display: flex;flex-direction: column;">
				<div
					v-for="(message, index) in messages"
					:key="index"
					class="chat-message"
					:class="{ 'current-user': message.isCurrentUser, 'other-user': !message.isCurrentUser }"
				>
					<div class="chat-message-sender">{{ message.sendUser }}</div>
					<div class="chat-message-content">{{ message.message }}</div>
				</div>
			</div>
			<div class="chat-input">
				<el-input v-model="inputMessage" placeholder="请输入消息"></el-input>
				<el-button style="margin-left: 8px" @click="sendMessage" type="primary">发送</el-button>
			</div>
		</el-scrollbar>
	</el-dialog>
</template>
<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useWebSocket } from '@vueuse/core';
const dialogVisible = ref(true); // 控制弹框显示与隐藏
const messageList = ref(null);
const messages = ref([]);
const inputMessage = ref('');
const sendUser = ref('');

const socket = useWebSocket(`ws://*************:8080/api/chat/1`);
onMounted(() => {
	console.log(socket);
	nextTick(() => {
		console.log('MessageList element:', messageList.value);
	});
});

const sendMessage = () => {
	if (inputMessage.value) {
		const message = {
			sendUser: '1',
			toUser: '2',
			message: inputMessage.value,
			isCurrentUser: true, // 标识消息是否为当前用户发送
		};
		messages.value.push(message);
		socket.send(JSON.stringify(message));
		inputMessage.value = '';
		nextTick(() => {
			messageList.value.scrollTop = messageList.value.scrollHeight;
		});
	}
};
watch(socket, (newSocket) => {
	if (newSocket) {
		newSocket.onmessage = (event) => {
			const receivedMessage = JSON.parse(event.data);
			receivedMessage.isCurrentUser = false; // 标识消息为对方发送
			messages.value.push(receivedMessage);
			nextTick(() => {
				messageList.value.scrollTop = messageList.value.scrollHeight;
			});
		};
	}
});
onUnmounted(() => {
	socket.close();
});
</script>

<style scoped>
.chat-container {
	display: flex;
	flex-direction: column;
	height: 400px;
	padding: 20px;
	border: 1px solid #ddd;
	background-color: #f5f5f5;
	position: relative;
}
.current-user {
	align-self: flex-end;
	background-color: #b2dfdb;
	margin-left: auto;
}

.current-user .chat-message-sender {
	text-align: right;
	color: #4caf50;
}
.other-user {
	align-self: flex-start;
	background-color: #ffc107;
}

.other-user .chat-message-sender {
	text-align: left;
	color: #ff5722;
}
.chat-message-list {
	flex: 1;
	padding-bottom: 25px;
}

.chat-message {
	margin-bottom: 10px;
	padding: 10px;
	border-radius: 10px;
	background-color: #e0f7fa; /* 淡蓝色背景 */
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-message-sender {
	font-weight: bold;
	color: #009688; /* 深绿色 */
}

.chat-input {
	position: absolute;
	bottom: 10px;
	left: 20px;
	right: 20px;
	display: flex;
	align-items: center;
}

.chat-input el-input {
	flex: 1;
	margin-right: 10px;
	border-radius: 20px;
	background-color: #fff;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-input el-button {
	width: 80px;
	border-radius: 20px;
}
</style>
