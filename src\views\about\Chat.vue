<template>
  <div class="chat-container">
    <div class="chat-message-list">
      <div v-for="(message, index) in messages" :key="index" class="chat-message">
        <div class="chat-message-sender">{{ message.sender }}</div>
        <div class="chat-message-content">{{ message.content }}</div>
      </div>
    </div>
    <div class="chat-input">
      <el-input v-model="inputMessage" placeholder="请输入消息"></el-input>
      <el-button @click="sendMessage">发送</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
const messages = ref([])
const inputMessage = ref('')

const sendMessage = () => {
  if (inputMessage.value) {
    messages.value.push({
      sender: '用户',
      content: inputMessage.value
    })
    // 发送消息逻辑
    // ...  
    // 清空输入框
    inputMessage.value = ''
  }
}

</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.chat-message-list {
  flex: 1;
  overflow-y: auto;
}

.chat-message {
  margin-bottom: 10px;
}

.chat-message-sender {
  font-weight: bold;
}

.chat-input {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
</style>