<template>
	<div class="content">
		<el-dialog v-model="dialogTableVisible" title="对比资产">
			<el-row :gutter="20">
				<el-col :span="4">
					<el-cascader size="large" placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }">
					</el-cascader>
				</el-col>
				<el-col :span="4">
					<el-select style="margin-left: 20px" v-model="rateValue" placeholder="全部资产评级" size="large">
						<el-option v-for="(item, value) in rate" :key="item" :label="item" :value="item" />
					</el-select>
				</el-col>
				<el-col :span="4">
					<el-select style="margin-left: 20px" v-model="buildingTypesValue" placeholder="全部资产" size="large">
						<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" /> </el-select
				></el-col>
				<el-col :span="4"> <el-input v-model="essential" placeholder="请输入关键字" size="large"></el-input></el-col>
				<el-col :span="6">
					<el-button type="primary" @click="Compared()">查询</el-button>
					<el-button type="primary">重置</el-button></el-col
				>
			</el-row>
			<el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange" stripe>
				<el-table-column type="selection" width="55" ref="multipleTableRef" />
				<el-table-column
					v-for="(column, index) in tableColumns"
					:key="index"
					:label="column.label"
					:prop="column.prop"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table>
			<el-pagination
				@current-change="handleCurrentChange"
				:current-page="currentPage"
				small
				background
				layout="prev, pager, next"
				class="mt-4"
				:total="tableData.length"
			/>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogTableVisible = false">取消</el-button>
					<el-button type="primary" @click="save()"> 确定 </el-button>
				</span>
			</template>
		</el-dialog>

		<el-button type="primary" @click="choose()">选择对比资产</el-button>
		<div ref="echartsContainer" style="height: 700px"></div>
	</div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
// import { provinceAndCityData } from 'element-china-area-data';
import http from '@/utils/http';
import * as echarts from 'echarts';
const echartsContainer = ref(null);
const myChart = ref(null);
const xAxis = ref([]);
const yAxis = ref([]);
const title = ref([]);
const multipleTableRef = ref(null);

onMounted(() => {
	// 初始化 ECharts 实例
	myChart.value = echarts.init(echartsContainer.value);
	window.addEventListener('resize', () => {
		myChart.value.resize(); // 窗口发生改变就更新echarts
	});
	// 设置图表选项
	myChart.value.setOption({
		title: {
			text: '资产对比表',
			left: 'center',
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				label: {
					backgroundColor: '#6a7985',
				},
			},
		},
		legend: {
			data: title.value,
			top: '20px',
		},
		toolbox: {
			feature: {
				saveAsImage: {},
			},
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true,
		},
		xAxis: [
			{
				type: 'category',
				boundaryGap: false,
				data: ['年份', '日期'],
			},
		],
		yAxis: [
			{
				type: 'value',
			},
		],
		series: [
			{
				name: 'AA',
				type: 'line',
				areaStyle: {},
				emphasis: {
					focus: 'series',
				},
				data: ['76', '32', '23', '46', '13', '35', '23', '41', '45', '32', '16', '37', '51'],
			},
		],
	});
});

onUnmounted(() => {
	// 在组件销毁时销毁 ECharts 实例
	if (myChart.value) {
		// 先销毁旧的实例
		myChart.value.dispose();
		// 置空变量
		myChart.value = null;
	}
	window.removeEventListener('resize', myChart.value);
});

const save = () => {
	console.log(multipleSelection.value, 'val');
	myChart.value.clear();
	dialogTableVisible.value = false;
	const line1Values = multipleSelection.value[0].line1.split(',');
	const line2Arrays = multipleSelection.value.map((item) => item.line2.split(','));
	const titlevalue = multipleSelection.value.map((item) => item.name);
	// 将拆分后的数组依次添加到xAxis.value中
	xAxis.value.push(...line1Values);
	yAxis.value.push(...line2Arrays);
	title.value = titlevalue;
	let newSeries = multipleSelection.value.map((item, index) => {
		return {
			name: item.name,
			type: 'line',
			areaStyle: {},
			emphasis: {
				focus: 'series',
			},
			data: yAxis.value[index],
		};
	});

	updateChart(newSeries);
	xAxis.value = [];
	yAxis.value = [];
	newSeries = {};
};
const choose = () => {
	// multipleTableRef.value.clearSelection();
	dialogTableVisible.value = true;
};
const updateChart = (newSeries) => {
	// 更新图表
	myChart.value.setOption({
		title: {
			text: '资产对比表',
			left: 'center',
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				label: {
					backgroundColor: '#6a7985',
				},
			},
		},
		legend: {
			data: title.value,
			top: '20px',
		},
		toolbox: {
			feature: {
				saveAsImage: {},
			},
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true,
		},
		xAxis: [
			{
				type: 'category',
				boundaryGap: false,
				data: xAxis.value,
			},
		],
		yAxis: [
			{
				type: 'value',
			},
		],
		series: newSeries,
	});
};
const multipleSelection = ref([]);
const tableData = ref([]);
const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};

const tableColumns = [
	{ label: '资产名称', prop: 'name', width: '300' },
	{ label: '地址', prop: 'address', width: '300' },
	{ label: '资产类型', prop: 'type', showOverflowTooltip: true },
];
const currentPage = ref(1);

const handleCurrentChange = (val) => {
	currentPage.value = val;
};
const queryParams = computed(() => {
	return {
		city: city.value,
		degree: rateValue.value,
		province: province.value,
		de_type: buildingTypesValue.value,
		search: essential.value,
		currentPage: currentPage.value,
	};
});
const Compared = async () => {
	try {
		// 发送请求
		const response = await http.get('/api/a_new_cal_jiazhi.do?act=getSelPkAssetsList', {
			params: queryParams.value, // 注意这里
		});

		tableData.value = response;
	} catch (error) {
		console.error('请求失败', error);
	}
};

const dialogTableVisible = ref(false);
const selectedOptions = ref([]);
const province = ref('');
const city = ref('');
const essential = ref('');
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const rateValue = ref('');
const rate = ['A+', 'A', 'B+', 'B', 'B-', 'C'];
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '数据中心', '保障房', '农贸市场'];
const buildingTypesValue = ref('');
</script>

<style lang="less" scoped>
.content {
	margin: 20px 0 0 20px;
	padding: 20px 0 0 20px;
}
</style>
