<template>
	<div>
		<el-sub-menu v-if="props.item.children" :index="props.item">
			<template #title>
				<!-- <el-icon v-if="props.item.icon"><location /></el-icon> -->
				<!-- <div v-else style="width: 24px"></div> -->
				<span class="item_name">{{ props.item.name }}</span>
			</template>
			<menu-Item v-for="child in props.item.children" :key="child.path" :index="child" :item="child" :active-menu="props.activeMenu" />
		</el-sub-menu>
		<el-menu-item v-else :index="props.item">
			<!-- <el-icon v-if="props.item.icon"><location /></el-icon> -->
			<!-- <div v-else style="width: 24px"></div> -->
			<span class="item_name">{{ props.item.name }}</span>
		</el-menu-item>
	</div>
</template>

<script setup>
const props = defineProps({
	item: {
		type: Object,
		required: true,
	},
	activeMenu: {
		type: String,
		required: true,
	},
});
</script>

<style scoped lang="scss"></style>
