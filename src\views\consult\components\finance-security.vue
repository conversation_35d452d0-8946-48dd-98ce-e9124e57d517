<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="ABS" name="first">
        <finance-abs></finance-abs>
    </el-tab-pane>
    <el-tab-pane label="REITS" name="second">
      <finance-reits></finance-reits>

</el-tab-pane>
</el-tabs>
</template>

<script setup>
import { ref } from 'vue'
import FinanceAbs from './finance-abs.vue'
import FinanceReits from './finance-reits.vue'
const activeName = ref('first')
const handleClick = () => {
    console.log(tab)
}
</script>

<style lang="less" scoped>

</style>