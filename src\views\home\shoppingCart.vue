<template>
	<div class="shoppingCart">
		<div class="table_box">
			<div class="tableShopping_box">
				<div class="tableShopping">
					<div class="top_box">
						<div>购物车（{{ tableData.length }}）</div>
					</div>
					<div class="t_ContentTop">
						<div class="flex_box">
							<el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"> 全选 </el-checkbox>
							<div class="delete" @click="handleDeleteChecked" v-if="isIndeterminate || checkAll">批量删除</div>
						</div>
						<div class="flex_box_right">
							<div v-for="(item, index) in dataShopTitle" :key="index">{{ item }}</div>
						</div>
					</div>

					<div class="t_Content" v-if="key_value">
						<div class="flex_boxContent" v-for="(item, index) in tableData" :key="index">
							<div>
								<el-checkbox v-model="item.checked" @change="handleCheckedCitiesChange($event, index)"></el-checkbox>
								<div class="box_name">{{ item.description }}</div>
							</div>
							<div>
								<div style="display: flex; align-items: center; width: calc(100% - 60px); flex-shrink: 0">
									<div class="box_city">
										城市：{{ item.cityName }}
										<div style="margin-top: 4px">团队人数：{{ item.team }}人</div>
									</div>
									<div class="box_UnitPrice">
										<div><span style="font-size: 16px">￥</span>{{ item.price }}</div>
										<div class="box_month">/ {{ item.period === 'MONTH' ? '月' : '年' }}</div>
									</div>
									<div class="box_number">
										<el-input-number v-model="item.orderCount" :min="1" :max="10" @change="handleChange($event, item)" />
										<div class="box_time">购买时长：{{ item.quantity }}个{{ item.period === 'MONTH' ? '月' : '年' }}</div>
									</div>
								</div>

								<div class="box_delete">
									<div class="deleteName" @click="handleDelete(item.id)">删除</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="right_box">
					<div class="right_box_content">
						<div class="right_box_content_title">支付明细</div>
						<div class="right_box_content_box">
							<div class="right_box_content_box_left_title">商品总价</div>
							<div class="right_box_content_box_left_price"><span>¥</span>{{ priceTotal.toFixed(2) }}</div>
						</div>
						<!-- <div class="right_box_content_box_center"> -->
						<!-- <div class="box_left">折扣优惠</div>
							<div class="box_right">
								<div class="box_right_content">-￥{{ discounts }}</div>
								<div class="box_bottom_content">{{ discountName }}</div>
							</div> -->
						<!-- </div> -->
						<div class="right_box_content_box_line"></div>

						<div class="bottom_box">
							<div class="box_left">商品总价</div>
							<div class="box_right">
								<div class="box_right_content"><span>￥</span>{{ priceAll || priceTotal.toFixed(2) }}</div>
								<!-- <div class="box_bottom_content">共减{{ discounts }}</div> -->
							</div>
						</div>
						<div class="footer_btn_box">
							<el-button type="primary" color="#1868F1" @click="handleOnSubmit">提交订单并支付</el-button>
						</div>
					</div>
				</div>
			</div>
		</div>

		<el-dialog
			v-model="dialogVisible"
			:close-on-click-modal="false"
			:destroy-on-close="true"
			:show-close="false"
			align-center="center"
			:before-close="handleClose"
			style="width: 800px"
		>
			<template #header>
				<div class="dialogHeader">
					<div class="dialogHeaderLeft">
						<div>购物车结算</div>
					</div>

					<div class="dialogHeaderRight">
						<el-icon><CloseBold @click="handleClose" /></el-icon>
					</div>
				</div>
			</template>

			<el-dialog v-model="showShangYutong" fullscreen>
				<shangYutong @handleReturn="handleReturn"></shangYutong>
			</el-dialog>
			<div v-if="successType" class="form_content">
				<div class="content_box_left">
					<el-skeleton-item variant="image" v-if="!paymentStatus" style="width: 100%; height: 100%; background: #fff" />
					<iframe
						:src="paymentURL"
						v-show="paymentStatus === 'ALI_PC' && checkbox"
						frameborder="no"
						border="0"
						marginwidth="0"
						marginheight="0"
						scrolling="no"
						width="200"
						height="200"
						style="overflow: hidden; transform: scale(0.6); margin: -20px 0 0 -40px; transform-origin: 100px 50px; /* 确保从左上角开始缩放 */"
					>
					</iframe>
					<canvas v-show="paymentStatus === 'WX_NATIVE' && checkbox > 0" ref="qrcodeCanvas" class="qrcode"></canvas>
				</div>
				<div class="content_box_right">
					<el-checkbox value="Agree" v-model="checkbox" name="type" class="check_boxAgreement"
						>我已阅读并同意<span class="blueSpan" @click="showShangYutong = true">《商宇通权益订阅服务协议 》</span>
					</el-checkbox>

					<div class="content_box_price">
						<div>扫码支付</div>
						<div>¥</div>
						<div>{{ priceAll || priceTotal.toFixed(2) }}</div>
					</div>

					<div class="content_box_bottom">
						<el-radio-group v-model="paymentStatus" @change="handlePayment">
							<el-radio value="ALI_PC" size="large">支付宝</el-radio>
							<el-radio value="WX_NATIVE" size="large">微信</el-radio>
						</el-radio-group>
					</div>
				</div>
			</div>

			<div v-if="!successType" class="prosperity">
				<div class="prosperity-box">
					<img src="@/assets/prosperity.png" alt="" />
					<div class="title">购买成功</div>
					<div class="prompt-content">
						<div>您可以在“个人中心-权益中心-我的权益卡券”查看及使用</div>
					</div>
					<el-button type="primary" @click="RightsCentre">去权益中心使用</el-button>
				</div>
				<div class="prosperityCoucher" v-if="couponDetail.couponsType">
					<div class="titleDetails">购买卡券得优惠好礼（已发放至个人中心-权益中心-福利卡券）</div>
					<sm-coucher :itemCoupons="couponDetail" @handleWelfareAddCrad="handleWelfareAddCrad"></sm-coucher>
				</div>
			</div>
		</el-dialog>
	</div>
	<buySuccess ref="buySuccessRef" />
</template>
<script setup>
import buySuccess from '../../component/buySuccess/index.vue';
import shangYutong from '../equityServices/shangYutong.vue';
import smCoucher from '../../component/smCoucher/index.vue';
import { getCouponDetail } from '@/api/equityTerm.js';
import { reactive, onMounted, ref, nextTick } from 'vue';
import { orderOreate, orderStatus, getShoppingCart, editCount, deleteShoppingCart, getDiscount } from '@/api/rights';
import QRCode from 'qrcode';
import { ElMessage } from 'element-plus';
import { vuexStore } from '@/store';
import { useRouter } from 'vue-router';
// 创建订单后支付状态
const createSuccessType = ref(true);
//购买成功弹出
const buySuccessRef = ref(null);
const showShangYutong = ref(false); // 订阅服务
const router = useRouter(); // 路由
const qrcodeCanvas = ref(); // 微信二维码
const element = ref(); // 底部元素
const dataShopTitle = reactive(['商品属性', '单价', '数量']); // 列表标题
const tableData = ref([]); // 列表数据
const checkAll = ref(false); // 全选状态
const isIndeterminate = ref(false); // 半全选状态
const priceAll = ref(0); //折扣后的商品总价
const priceTotal = ref(0); // 商品总价
const discounts = ref(0); // 折扣
const discountName = ref(''); // 折扣名称
const dialogVisible = ref(false); // 弹窗
const checkbox = ref(false); // 同意协议
const paymentStatus = ref(''); // 支付方式
const paymentURL = ref(''); // 支付链接
const orderId = ref(''); // 订单ID
const successType = ref(true); // 成功类型
const key_value = ref(true); // key更新页面
const couponDetail = ref({
	// couponsType: '1',
	// name: '146元打车券礼包',
	// desc: '包含1张￥11券、1张￥12券、1张￥13券、2张￥10券、2张￥10券',
	// useLimit: '无门槛',
}); //优惠卷详情
let timerId = ref(null); // 订单轮询定时器
// 初始化
onMounted(() => {
	handlegetShoppingCart();
	handleFooterBox(); //隐藏底部
});

function handleReturn() {
	// 关闭对话框
	showShangYutong.value = false;
}

// 获取购物车
function handlegetShoppingCart() {
	getShoppingCart().then((res) => {
		if (res.code === 200) {
			key_value.value = false;
			nextTick(() => {
				key_value.value = true;
				tableData.value = res.data;
				checkAll.value = false;
				isIndeterminate.value = false;
				vuexStore.commit('handleShoppingCart', res.data.length);
			});
		}
	});
}
// 跳转福利卡劵
function handleWelfareAddCrad() {
	router.push({
		path: '/profile/browsingHistory',
		query: { type: 'third' },
	});
}

//根据订单id获取优惠卷详情
const handleCouponDetail = (orderId) => {
	getCouponDetail({ outTradeNo: orderId }).then((res) => {
		if (res.code === 200 && res.data) {
			couponDetail.value = res.data;
			couponDetail.value.couponsType = '1';
		}
	});
};

// 订单轮询获取支付状态
const pollOrderStatus = async (orderId) => {
	if (paymentStatus.value === '' || !checkbox.value) {
		return;
	}
	try {
		const response = await orderStatus(orderId);
		if (orderId === '') {
			return; // 提前返回，不执行后续代码
		}
		//PENDING("待支付"),        // 待支付
		// PAID("已支付"),          // 已支付
		// FAILED("支付失败"),      // 支付失败
		// CANCELLED("已取消"),     // 已取消
		// REFUNDED("已退款");      // 已退款
		if (response.code == 200) {
			// 根据返回的状态更新状态提示信息
			switch (response.data) {
				case 'PAID':
					ElMessage({
						message: `支付成功`,
						type: 'success',
					});
					clearInterval(timerId.value);
					//根据订单id获取优惠卷详情
					handleCouponDetail(orderId);
					handlegetShoppingCart(); // 获取购物车
					createSuccessType.value = true;
					successType.value = false;
					break;
				case 'FAILED':
					ElMessage({
						message: `支付失败`,
						type: 'error',
					});
					clearInterval(timerId.value);
					break;
				case 'CANCELLED':
					ElMessage({
						message: `订单已取消`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				case 'REFUNDED':
					ElMessage({
						message: `已退款`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				default:
					break;
			}
		} else {
			clearInterval(timerId.value);
		}
	} catch (error) {
		clearInterval(timerId.value);
	}
};

//订单创建
function handleOrderCreate(params) {
	orderOreate(params).then((res) => {
		if (res.code == 200) {
			createSuccessType.value = false;
			let data = res.data;
			if (paymentStatus.value === 'ALI_PC') {
				// 支付宝
				paymentURL.value = data.url;
			} else {
				// 微信
				const qrCodeDiv = qrcodeCanvas.value;
				QRCode.toCanvas(qrCodeDiv, data.url, (error) => {
					if (error) console.error(error);
				});
			}

			if (data.outTradeNo) {
				orderId.value = data.outTradeNo;
				// 开始轮询
				timerId.value = setInterval(() => {
					pollOrderStatus(orderId.value);
				}, 1500);
			}
		}
	});
}

// 支付
function handlePayment() {
	//清楚定时器
	clearInterval(timerId.value);
	let orderCounts = 0; // 套餐数量
	let arr = []; // 选中的套餐
	tableData.value.forEach((item) => {
		if (item.checked) {
			orderCounts += item.orderCount;
			arr.push({
				orderCount: item.orderCount, // 套餐数量
				shoppingCardId: item.id, // 购物车id
				businessType: 'COUPON_ORDER', // 订单类型
				couponId: item.couponId, // 套餐id
				quantity: item.quantity, // 年月数量
				team: item.team, // 人数
				totalAmount: item.price * item.orderCount, // 总金额
			});
		}
	});
	let params = {
		payType: paymentStatus.value, // 支付方式
		orderCount: orderCounts, // 套餐数量
		totalAmount: Number(priceTotal.value.toFixed(2)), // 总金额
		discountAmount: Number(discounts.value), // 折扣
		payableAmount: Number(priceAll.value), // 应付金额
		orderDetails: arr, // 套餐
	};
	handleOrderCreate(params); // 订单创建
}
// 确定
function handleOnSubmit() {
	let cities = tableData.value.filter((item) => item.checked);
	if (!cities.length) {
		ElMessage({
			message: '请选择套餐~',
			type: 'warning',
		});
		return;
	}
	dialogVisible.value = true;
}

// 支付明细
function handlePriceChangeAll() {
	priceTotal.value = 0;
	tableData.value.forEach((item) => {
		if (item.checked) {
			priceTotal.value += item.price * item.orderCount;
		}
	});
	getDiscount({ totalAmount: Number(priceTotal.value.toFixed(2)) }).then((res) => {
		if (res.code === 200) {
			discounts.value = Number(res.data.discountAmount.toFixed(2)); // 折扣
			priceAll.value = Number(res.data.payableAmount.toFixed(2)); // 应付金额
			discountName.value = res.data.discountText; // 折扣展示name
		}
	});
}

// 全选
function handleCheckAllChange(value) {
	tableData.value.forEach((item) => {
		item.checked = value;
	});
	handleCheckAll(); // 回显全选
}

//  单选
function handleCheckedCitiesChange() {
	handleCheckAll(); // 回显全选
}

// 回显全选
function handleCheckAll() {
	const checkedCount = tableData.value.length;
	if (checkedCount === 0) {
		checkAll.value = false;
		isIndeterminate.value = false;
		priceAll.value = 0; // 应付金额
		priceTotal.value = 0; // 总金额
		discounts.value = 0; // 折扣
		discountName.value = ''; // 折扣展示name
		return;
	}
	let cities = tableData.value.filter((item) => item.checked);
	checkAll.value = checkedCount === cities.length;
	isIndeterminate.value = cities.length > 0 && cities.length < checkedCount;
	handlePriceChangeAll(); // 支付明细
}

// 根据勾选状态删除购物车 批量删除
function handleDeleteChecked() {
	let cities = tableData.value.filter((item) => item.checked);
	let ids = cities.map((item) => item.id);
	handleDelete(ids.join(','));
}

// 单删除
function handleDelete(id) {
	deleteShoppingCart({ ids: id }).then((res) => {
		if (res.code === 200) {
			ElMessage({
				message: '删除成功',
				type: 'success',
			});
			const arr = id.split(',');
			console.log(arr.length, 'arr.length');
			tableData.value = tableData.value.filter((item) => !arr.includes(item.id));
			key_value.value = false;
			nextTick(() => {
				key_value.value = true;
				handleCheckAll(); // 回显全选
				vuexStore.commit('handleShoppingCart', vuexStore.state.shoppingCart - arr.length);
			});
		}
	});
}

// 修改商品数量
function handleChange(num, item) {
	item.orderCount = num;
	editCount({ id: item.id, count: num }).then((res) => {
		if (res.code === 200) {
			if (item.checked) {
				handlePriceChangeAll(); // 支付明细
			}
		}
	});
}
//隐藏底部
function handleFooterBox() {
	setTimeout(() => {
		element.value = document.querySelector('.footer_box');
		if (element.value?.style) {
			element.value.style.display = 'none';
		}
	}, 100);
}

// 权益中心
const RightsCentre = () => {
	clearInterval(timerId.value);
	router.push({
		path: '/profile/browsingHistory',
	});
};

// 关闭对话框
const handleClose = () => {
	if (!createSuccessType.value) {
		buySuccessRef.value.show({ paymentState: true, payType: paymentStatus.value, payableAmount: Number(priceTotal.value.toFixed(2)) });
		createSuccessType.value = true;
	}
	paymentStatus.value = ''; // 支付状态清楚
	// 清楚checkBox
	checkbox.value = false;
	dialogVisible.value = false;
	//清楚定时器
	clearInterval(timerId.value);
	handlegetShoppingCart(); // 获取购物车
	priceAll.value = 0; // 应付金额
	priceTotal.value = 0; // 总金额
	discounts.value = 0; // 折扣
	discountName.value = ''; // 折扣展示name
};

onBeforeUnmount(() => {
	if (element.value?.style) {
		element.value.style.display = '';
	}
});
</script>
<style lang="scss" scoped>
::v-deep .footer_box {
	display: none;
}
.shoppingCart {
	height: calc(100vh - 125px);
	overflow: hidden;
	display: flex;
	justify-content: center;
	background: url('../../assets/shoppingCart.png') no-repeat;
	background-size: inherit;
	padding: 16px 16px 32px 16px;
	border-radius: 6px;
	.table_box {
		margin-top: 24px;
		// width: 80%;
		.top_box {
			height: 82px;
			div {
				font-size: 34px;
				font-weight: 500;
				line-height: 42px;
				color: #1d2129;
			}
		}

		.tableShopping_box {
			display: flex;

			.tableShopping {
				position: relative;
				width: 800px;
				height: 100%;
				margin-right: 80px;
				.t_ContentTop {
					width: 800px;
					height: 40px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 0 24px;
					box-sizing: border-box;
					border-radius: 6px;
					background: #fff;
					.flex_box {
						width: 38%;
						display: flex;
						align-items: center;
						.el-checkbox {
							&::v-deep .el-checkbox__label {
								margin-left: 8px;
								font-size: 14px;
								font-weight: 500;
								line-height: 40px;
								color: #86909c;
							}
						}
						.delete {
							margin-left: 16px;
							cursor: pointer;
							font-size: 14px;
							font-weight: 500;
							line-height: 22px;
							color: #c9cdd4;
						}
					}
					.flex_box_right {
						width: 62%;
						display: flex;
						align-items: center;
					}
					.flex_box_right > :nth-child(n) {
						margin-right: 20px;
						width: 135px;
						height: 40px;
						line-height: 40px;
						font-size: 14px;
						font-weight: 500;
						color: #86909c;
						white-space: nowrap;
					}

					.flex_box_right > :nth-last-child(1) {
						margin-right: 20px;
					}
				}

				.t_Content {
					width: 800px;
					height: calc(100vh - 300px);
					overflow: scroll;
					margin-top: 8px;
					.flex_boxContent > :nth-child(1) {
						width: 38%;
						flex-grow: 1;
						display: flex;
						align-items: center;
					}

					.flex_boxContent > :nth-child(2) {
						width: calc(100% - 38%);
						display: flex;
						justify-content: space-between;
						align-items: center;
						white-space: nowrap;
					}
					.flex_boxContent {
						padding: 0 24px;
						width: calc(100% - 48px);
						height: 80px;
						border-radius: 6px;
						background: #ffffff;
						margin-bottom: 8px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						.box_name {
							width: calc(100% - 52px);
							margin: 0 36px 0 16px;
							color: #1d2129;
							font-size: 14px;
							font-weight: 700;
							white-space: nowrap;
							flex-shrink: 1;
						}
						.box_city {
							white-space: nowrap;
							margin-right: 20px;
							width: 135px;
							// min-width: 120px;
							font-size: 14px;
							font-weight: 700;
							color: #1d2129;
							flex-shrink: 0;
						}
						.box_UnitPrice {
							white-space: nowrap;
							margin-right: 20px;
							width: 135px;
							// min-width: 120px;
							font-size: 20px;
							font-weight: 700;
							color: #1d2129;
							display: flex;
							flex-shrink: 0;
							.box_month {
								margin-top: 4px;
								margin-left: 4px;
								font-size: 14px;
								font-weight: 700;
								color: #1d2129;
							}
						}

						.box_number {
							flex-shrink: 0;
							margin-right: 20px;
							width: 135px;
							// min-width: 120px;
							::v-deep .el-input-number {
								width: 110px !important;
								flex-shrink: 0;
								.el-icon {
									font-size: large;
									width: 20px;
									color: #000;
								}
								.el-input-number__decrease,
								.el-input-number__increase {
									width: 24px;
									height: 24px;
									border-radius: 4px;
									background: #ffffff;
									border: 1px solid #e7e7e7;
									&:hover {
										background: #f5f5f5;
									}
								}
								.el-input__wrapper {
									height: 24px;
									line-height: 24px;
								}
								.el-input {
									--el-input-border-color: none;
									--el-input-hover-border-color: none;
									--el-input-clear-hover-color: none;
									--el-input-focus-border-color: none;

									.el-input__inner {
										color: #1d2129;
										font-size: 20px;
										font-weight: 500;
									}
								}
							}

							.el-input-number input {
								border: none;
							}
							.box_time {
								margin-top: 4px;
								font-size: 12px;
								height: 20px;
								line-height: 20px;
								font-weight: 400;
								color: #1868f1;
							}
						}

						.box_delete {
							flex-shrink: 0;
							width: 60px;
							height: 100%;
							display: flex;
							align-items: center;
							justify-content: end;
							white-space: nowrap;
							font-size: 12px;
							font-weight: 400;
							.deleteName {
								font-size: 12px;
								cursor: pointer;
								color: #86909c;
								&:hover {
									color: #ff4d4f;
								}
							}
						}
					}
				}

				.t_Content > :nth-last-child(1) {
					margin-bottom: 0px !important;
				}
			}

			.right_box {
				margin-top: 82px;
				width: 316px;
				// height: 352px;
				height: max-content;
				border-radius: 8px;
				background: #fff;
				.right_box_content {
					padding: 16px 24px;
					width: 276px;
					flex-shrink: 0;
					.right_box_content_title {
						width: 100%;
						height: 32px;
						font-size: 24px;
						font-weight: 500;
						line-height: 32px;
						color: #4e5969;
					}

					.right_box_content_box {
						margin: 8px 0;
						width: 100%;
						height: 44px;
						line-height: 44px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						.right_box_content_box_left_title {
							font-size: 14px;
							font-weight: 500;
							color: #4e5969;
						}
						.right_box_content_box_left_price {
							font-size: 20px;
							font-weight: 600;
							color: #1d2129;
							span {
								font-size: 16px;
								font-weight: 600;
							}
						}
					}

					.right_box_content_box_center {
						width: 100%;
						height: 70px;
						border-radius: 6px;
						color: #f5f8fd;
						display: flex;
						justify-content: space-between;
						background: #f5f8fd;
						.box_left {
							font-size: 14px;
							font-weight: 500;
							line-height: 22px;
							color: #4e5969;
							margin: 8px 0 0 8px;
						}
						.box_right {
							display: flex;
							flex-direction: column;
							align-items: end;
							margin: 8px 8px 0 0px;
							// height: 54px;
							.box_right_content {
								font-size: 20px;
								font-weight: 500;
								line-height: 28px;
								margin-bottom: 4px;
								color: #ec655f;
							}

							.box_bottom_content {
								font-size: 14px;
								font-weight: 500;
								line-height: 22px;
								color: #86909c;
							}
						}
					}

					.right_box_content_box_line {
						height: 1px;
						width: 100%;
						background: #e7e7e7;
						margin: 20px 0 8px 0;
					}

					.bottom_box {
						//height: 74px;
						display: flex;
						justify-content: space-between;
						margin-bottom: 8px;
						.box_left {
							font-size: 14px;
							font-weight: 500;
							line-height: 38px;
							color: #4e5969;
						}

						.box_right {
							display: flex;
							flex-direction: column;
							align-items: end;
							margin: 8px 8px 0 0px;
							// height: 54px;
							.box_right_content {
								font-size: 24px;
								font-weight: 500;
								line-height: 29px;
								margin-bottom: 4px;
								color: #1868f1;
								span {
									font-size: 16px;
									font-weight: 600;
								}
							}

							.box_bottom_content {
								font-size: 14px;
								font-weight: 500;
								line-height: 22px;
								color: #ec655f;
							}
						}
					}

					.footer_btn_box {
						width: 100%;
						height: 48px;
						border-radius: 6px;
						.el-button {
							width: 100%;
							height: 48px;
							border-radius: 6px;
						}
					}
				}
			}
		}
	}
	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}

.custom_table_height {
	margin-top: -7px;
	border: none;
	::v-deep .el-table__body {
		//-webkit-border-horizontal-spacing: 13px;  // 水平间距
		-webkit-border-vertical-spacing: 8px; // 垂直间距
	}

	--el-table-row-hover-bg-color: rgba(245, 248, 253, 1) !important;
	--el-table-border-color:none

 // 上面的线
 ::v-deep th.el-table__cell.is-leaf {
		border: none;
	}
}

::v-deep .el-dialog {
	padding: 0;
	border-radius: 6px;
}

.form_content {
	margin: -2px 32px 16px 32px;
	width: calc(100% - 64px);
	height: 152px;
	display: flex;
	justify-content: space-between;
	background: #f5f6f7;
	border-radius: 4px;
	.content_box_left {
		width: 120px;
		margin: 16px;
	}
	.content_box_right {
		margin: 16px 0;
		width: calc(100% - 152px);
		.check_boxAgreement {
			height: 22px;
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			margin-bottom: 34px;
		}
		.blueSpan {
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			color: rgba(24, 104, 241, 1);
		}

		.content_box_price {
			display: flex;
			height: 24px;
			margin-bottom: 10px;
			& > :nth-child(1) {
				font-size: 16px;
				font-weight: 700;
				line-height: 27px;
				color: #1d2129;
			}
			& > :nth-child(2) {
				font-size: 16px;
				font-weight: 700;
				line-height: 29px;
				color: #1868f1;
				margin: 0 8px;
			}
			& > :nth-child(3) {
				font-size: 28px;
				font-weight: 500;
				line-height: 24px;
				color: #1868f1;
				margin-bottom: -2px;
			}
		}

		.content_box_bottom {
			height: 22px;
			.el-radio-group {
				height: 24px;
				& > :nth-child(n) {
					height: 32px;
					margin-right: 10px;
				}
			}
		}
	}
}

.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	border-bottom: 1px solid #e7e7e7;
}
.dialogHeaders {
	border-bottom: none !important;
	font-weight: 700 !important;
}
.dialogHeaderLeft {
	display: flex;
	align-items: center;
	color: #1d2129;
	font-size: 16px;
	font-weight: 700;
}
.dialogHeaderRight > :nth-child(1) {
	cursor: pointer;
	margin: 19px 0px 0 0;
}

.prosperity {
	width: 100%;

	.prosperity-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40px 40px 14px 40px;

		img {
			width: 50px;
			height: 50px;
		}

		.title {
			font-weight: 700;
			font-size: 20px;
			margin-top: 8px;
			margin-bottom: 16px;
			line-height: 32px;
			height: 32px;
			color: #1d2129;
		}

		.prompt-content {
			height: 36px;
			display: flex;
			flex-direction: column;
			align-items: center;
			line-height: 22px;

			div {
				// margin: 5px 0;
				color: #4e5969;
				font-size: 14px;
			}
		}

		.el-button {
			margin-top: 18px;
			padding: 0 40px;
			width: 306px;
			height: 48px;
			background: #1868f1;
		}
	}
}

.qrcode {
	height: 136px !important;
	margin: -8px 0px 0 -8px;
	width: 136px !important;
}

::v-deep .el-checkbox {
	--el-checkbox-checked-bg-color: #1868f1;
	.el-checkbox__input.is-checked .el-checkbox__inner {
		border-color: #1868f1;
	}
	.el-checkbox__input.is-indeterminate .el-checkbox__inner {
		border-color: #1868f1;
	}
}

.prosperityCoucher {
	width: 100%;
	height: 134px;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-top: 1px solid #e7e7e7;
	margin-bottom: 16px;
	.titleDetails {
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: center;
		color: #86909c;
		margin: 12px 0 10px 0;
	}
}
</style>
