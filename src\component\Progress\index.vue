<template>
	<el-progress
		v-if="props.progress.percentage > 0"
		:percentage="props.progress.percentage"
		:color="props.progress.color"
		class="progresst"
		:stroke-width="props.progress.strokeWidth || 8"
		:text-inside="props.progress.textInside"
		:show-text="props.progress.showText"
	/>
	<el-progress
		:percentage="Math.abs(props.progress.percentage)"
		v-else
		color="#C9CDD4"
		class="progressf"
		direction="right"
		:stroke-width="props.progress.strokeWidth || 8"
		:show-text="props.progress.showText"
	>
		<template #default> -{{ Math.abs(props.progress.percentage) }}% </template>
	</el-progress>
</template>

<script setup>
const props = defineProps({
	progress: {
		type: Object,
		default: () => {
			// percentage: 0//百分比
			// color: '#409eff'//颜色
			// strokeWidth: 12//宽度
			// textInside: true//是否内置文字
			// showText: true//是否显示文字
		},
	},
});
</script>
<style lang="scss" scoped>
.progressf {
	height: 20px;
	margin-top: -3px;
	::v-deep .el-progress-bar {
		rotate: 180deg;
	}
	::v-deep .el-progress__text {
		font-weight: 400;
		font-size: 14px !important;
		color: #4e5969 !important;
		margin-left: 10px !important;
	}
}

.progresst {
	height: 20px;
	margin-top: -3px;
	::v-deep .el-progress__text {
		font-weight: 400;
		font-size: 14px !important;
		color: #4e5969 !important;
		margin-left: 10px !important;
	}
}
</style>
