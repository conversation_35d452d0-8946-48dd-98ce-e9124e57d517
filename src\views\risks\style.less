.credit_risks {
  width: calc(100% - 16px);
  background: #f7f8fa;
  border-radius: 4px;
  box-sizing: border-box;
  margin: 0px 16px 0px 0;
  .mainTitle1 {
    width: 100%;
    height: 28px;
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #1d2129;
    margin: 20px 16px 16px 16px;
    &::before {
      content: '';
      width: 5px;
      height: 14px;
      background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
      border-radius: 10px;
      margin-right: 8px;
    }
  }
  .card-details-box {
    box-sizing: border-box;
    background-color: #ffffff;

  }

  .container_box {
    border-radius: 6px;
    // width: 100%;
    // height: 100%;
    box-sizing: border-box;
    background-color: #ffffff;

    .card_box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
      background-color: rgba(255, 255, 255, 1);
      border-radius: 6px;
      // padding: 50px 35px 35px 35px;
      height: 64px;
      // margin-bottom: 15px;
      box-sizing: border-box;
      position: relative;

    }


  }

  .details-top-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    background: #fff;
    gap: 16px;
    .left_box {
      width: calc(50% - 0px);


      .building-details {
        width: calc(100% - 16px);
        height: 288px;
        margin-left: 16px;
        border: 1px solid #E5E6EB;
        border-radius: 4px;
        .head-left-details {
          display: flex;
          align-items: center;
          img {
            width:298px;
            height: 200px;
            border-radius: 8px;
            object-fit: fill;
          }

          .head-right_details {
            width: 371px;
            height: 182px;
            margin-left: 4px;
            .right_details{
              padding: 0 20px;
              margin-bottom: 24px;
            }
            .top_tent {
              display: flex;
              height: 28px;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;
              .top_text {
                font-weight: 500;
                font-size: 18px;
                line-height: 28px;
                color: #1d2129;
              }
              .topt_text {
                background: linear-gradient(90deg, #77a9ff 0%, #1868f1 100%);
                height: 20px;
                border-radius: 2px;
                padding: 1px 8px;
                color: #ffffff;
                font-weight: 500;
                font-size: 12px;
                line-height: 20px;
                box-sizing: border-box;
              }
              .topth_text {
                box-sizing: border-box;
                background: #e8f3ff;
                height: 20px;
                border-radius: 2px;
                padding: 1px 8px;
                color: #1868f1;
                font-weight: 500;
                font-size: 12px;
                line-height: 20px;
              }
            }

            .center_tent {
              display: flex;
              height: 22px;
              align-items: center;
              gap: 24px;
              margin-bottom: 24px;
              .center_tent_left {
                display: flex;
                align-items: center;
              }
              .center_tent_left_text {
                font-weight: 400;
                font-size: 14px;
                // line-height: 22px;
                margin-left: 4px;
                color: #4e5969;
                // white-space: nowrap; /* 不换行 */
                // overflow: hidden; /* 内容超出部分隐藏 */
                // text-overflow: ellipsis; /* 显示省略号 */
              }
            }

            
            .items_box_border {
              display: flex;
              flex-wrap: wrap;
              align-items: center;
              gap: 16px 41px;
              // justify-content: center;
              // border-radius: 5px;
              // padding: 24px 0;

              .index_left_item {
                width: 96px;
                height: 44px;
                position: relative;
                .lingIndex{
                  width: 1px;
                  height: 20px;
                  background: #E5E6EB;
                  position: absolute;
                  right: -20.5px;
                  bottom: 12px;
                }
                .value {
                  font-weight: 600;
                  font-size: 18px;
                  line-height: 18px;
                  text-align: center;
                  color: #1D2129;
                }

                .title {
                  margin-top: 4px;
                  font-weight: 400;
                  font-size: 14px;
                  line-height: 22px;
                  text-align: center;
                  color: #4E5969;
                }
              }
            }
            // .name {
            //   display: flex;
            //   align-items: center;
            //   justify-content: space-between;
            //   margin:0 0 0 10px;

            //   span {
            //     font-size: 14px;
            //     font-weight: 600;
            //   }

            //   .type {
            //     font-size: 12px;
            //     // width: 44px;
            //     padding: 0 3px;
            //     height: 22px;
            //     font-weight: 400;
            //     background: rgba(24, 104, 241, 1);
            //     text-align: center;
            //     border-radius: 3px;
            //     line-height: 22px;
            //     // margin-right: 5px;
            //     color: rgba(255, 255, 255, 1);
            //   }
            // }

            // .address {
            //   font-size: 12px;
            //   margin: 4px 0 26px 10px;
            //   color: rgba(134, 144, 156, 1);
            //   // line-height: 35px;
            //   min-height: 30px;
            // }

            // .estate {
            //   font-size: 24px;
            //   font-weight: 700;
            //   color: rgba(24, 104, 241, 1);
            //   margin-left:10px;
            //   span {
            //     font-size: 20px;
            //   }
            // }

          }
        }



        .boxss {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .items_box {
            width: 300px;
            height: 200px;

            margin-left: 5px;
            color: rgb(0, 0, 0);
            font-family: 微软雅黑;
            text-align: center;
            margin: 0 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            .items_box_border>:nth-child(1){
              margin-bottom: 24px;
            }
            .items_box_border>:nth-child(2){
              margin-bottom: 24px;
            }
            .items_box_border>:nth-child(3){
              margin-bottom: 24px;
            }



          }

          .rate {
            color: rgb(0, 0, 0);
            font-family: 微软雅黑;
            font-size: 48px;
            font-weight: 700;
            line-height: 63px;
            letter-spacing: 0px;
            text-align: center;
          }

          // }
        }

        // }

      }

    }

    .right_box {
      width: calc(50% - 0px);

    
      .container_box {
        .card_boxs {
          width: calc(100% - 16px);
          margin-right: 16px;
          height: 484px;
          border-radius: 4px;
          border: 1px solid  #E5E6EB;
          .tag_box_details{
            height: 23px;
            line-height: 23px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            color: #1D2129;
            background: #F7F8FA;
            border-bottom: 1px solid  #E5E6EB;
          }
          .left_box {
            width: 100%;
            height: 436px;
            box-sizing: border-box;
            // background: rgb(255, 255, 255);
            padding: 30px 14px;
            display: flex;

            .left_item {
              width: 300px;
              background: #F9FBFE;
              border: 1px solid #E8F3FF;
              border-radius: 4px;
              padding: 40px 32px 40px 20px;
              box-sizing: border-box;
              display: flex;
              flex-direction: column;
              gap: 12px;
              .item_qy{
                display: flex;
                align-items: center;
                height: 32px;
                width: 100%;
              }
              .item_qy_title{
                width: 70px;
                font-weight: 400;
                font-size: 14px;
                line-height: 22px;
                color: #4E5969;
                text-align: right;
                margin-right: 16px;

              }


              .item_qy_value{
                font-weight: 500;
                font-size: 14px;
                line-height: 22px;
                color: #1D2129;
              }
            }

            .left_items {
              width: calc(70% - 10px);
            }
          }
        }
      }
    }
  }
  .details_bottom_container{
    margin:16px 16px 0 16px;
    width: calc(100% - 32px);
    box-sizing: border-box;
    background: #fff;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
  }
  .details-bottom-container {
    width: calc(100% - 0px);
    // height: 264px;
    padding: 20px 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    
    box-sizing: border-box;
    // margin: 0 16px;
    // margin-bottom: 16px;
    .card-details-boxs{
      width: 515px;
      height: 104px;
      display: flex;
      align-items: center;
      // flex-direction: column;
      justify-content: space-between;
      padding: 20px 24px 20px 32px;
      box-sizing: border-box;
      border-radius: 4px; 
      background: #F7F8FA;
      .card_details{
        display: flex;
        align-items: center;
        .details_box{
          margin-right: 48px;
          .value {
            font-weight: 600;
            font-size: 32px;
            line-height: 32px;
            color: #0F2860;
            margin-bottom: 4px;
          }
    
          .name {
            white-space:nowrap;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: #0F2860;
          }
        }
        .line{
          margin-right: 39px;
          width: 1px;
          height: 60px;
          background: #D8EAFF;
        }
      }
      .details_box_fee{
        display: flex;
        width: calc(100% - 200px);
        align-items: center;
        justify-content: space-between;
        .box_fee{
          height: 64px;
          // width: 152px;
          .box_fee_circle{
            display: flex;
            height: 26px;
            margin-bottom: 12px;
            align-items: end;
            .box_fee_circle_img{
              width: 16px;
              height: 16px;
              margin-bottom: 2px;
              img{
                width: 100%;
                height: 100%;
              }
            }
            .box_fee_circle_si{
              margin-left: 4px;
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;
              height: 22px;
              color: #0F2860;
            }
            .tax_feetax{
              margin-left: 12px;
              font-weight: 600;
              font-size: 26px;
              line-height: 26px;
              color: #0F2860;

            }
          }
          .box_fee_circleFx{
            display: flex;
            align-items: flex-end;
            height: 26px;
            .box_fee_title{
              width: 72px;
              height: 22px;
              line-height: 22px;
              text-align: right;
              margin-right: 12px;
              font-weight: 400;
              font-size: 14px;
              color: #0F2860;
            }
            .box_fee_value{
              font-weight: 600;
              font-size: 26px;
              line-height: 26px;
              color: #0F2860;
            }
          }
        }
        .details_boxImg{
          width: 48px;
          height: 48px;
          img{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .card-details-box {
      // width: calc((100% - (5* 16px)) / 6);
      width: 249px;
      // height: calc((100% - (2* 16px)) / 3);
      // width: 249px;
      height: 104px;
      display: flex;
      align-items: center;
      // flex-direction: column;
      justify-content: space-between;
      padding: 23px 24px 23px 32px;
      box-sizing: border-box;
      border-radius: 4px; 
      background: #F7F8FA;
      .details_box{
        .value {
          font-weight: 600;
          font-size: 32px;
          line-height: 32px;
          color: #0F2860;
          margin-bottom: 4px;
        }
  
        .name {
          white-space:nowrap;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #0F2860;
        }
      }
      .details_boxImg{
        width: 48px;
        height: 48px;
        img{
          width: 100%;
          height: 100%;
        }
      }

    }

    .tax-v2 {
      display: flex;
      flex-direction: row;

      .tax_fee-left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 40%;

        .num {
          height: 50%;
          font-size: 24px;
          font-weight: 700;
          color: rgba(24, 104, 241, 1);
          display: flex;
          align-items: flex-end;
        }

        .test {
          margin-top: 10px;
          margin-left: 7px;
          width: 112px;
          font-size: 14px;
          color: rgba(78, 89, 105, 1);
        }
      }

      .tax_fee {
        width: 60%;
        height: 72px;
        border-left: 1px solid rgba(231, 231, 231, 1);
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        font-size: 14px;
        color: rgba(78, 89, 105, 1);

        .tax,
        .fee {
          font-size: 20px;
          font-weight: 600;
          color: rgba(24, 104, 241, 1);
        }
      }
    }

    .proportion {
      display: flex;
      flex-direction: column;
      justify-content: center;

    }
  }

  .table-counter-container {
    margin-top: 16px;
    width: 100%;
    ::v-deep .arco-input-focus {
      border: 1px solid #409eff !important;
    }
    ::v-deep .arco-input-number {
      border: 1px solid #e5e6eb;
      background-color: #fff;
      height: 32px;
      border-radius: 4px;
      padding-right: 10px;
    }

    
    .card_box_tag{

    }
    .form-box {
      display: flex;
      height: 72px;
      padding: 20px 0px;
      box-sizing: border-box;
      align-items: center;
      position: relative;
      .download{
        position: absolute;
        right: 0px;
        bottom: -30px;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: #1868F1;
        display: flex;
        align-items: center;
        z-index: 9;
        cursor: pointer;
        img{
          margin-left: 4px;
          color: #1868F1;
        }
      }

      .financing-limit {
        height: 32px;
        // width: 130px;
        margin-right: 24px;
        display: flex;
        align-items: center;
        .financing_limitTitle{
          height: 22px;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #4E5969;
          margin-right: 16px;
        }
        .financing_limitValue{
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          color: #1D2129;
        }
      }

      .financing-interest {
        height: 32px;
        width: 252px;
        display: flex;
        margin-right: 24px;
        .interest_name{
          font-weight: 400;
          font-size: 14px;
          line-height: 32px;
          color: #4E5969;
          margin-right: 16px;
        }
      }

      .start-count {
        height: 100%;
        display: flex;
        align-content: flex-end;
        flex-wrap: wrap;

      }
    }

    .securitzation {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      .securitzation_content{
        border-radius: 4px;
        background-color: #EFF5FE;
        padding: 20px 16px;
        margin-top: 4px;
      }
      .classnames{
        margin-bottom: 16px;
      }
      .left {
        width: calc(50% - 8px);
        .title {
          border: 1px solid #C7DFFF;
          text-align: center;
          width: 100%;
          height: 56px;
          background: linear-gradient(90.27deg, #EFF5FE 20.27%, #D0E4FC 98.11%);
          line-height: 56px;
          border-radius: 4px;
          color: rgba(255, 255, 255, 1);
          font-weight: 500;
          font-size: 20px;
          color: #0E42D2;
        }

        .table-container {
          // margin: 10px 10px 10px 10px;
          border: 1px solid #E5E6EB;
          border-radius: 4px;
          background: #fff;

          .table-title {
            border-bottom: 1px solid #E5E6EB;
            height: 39px;
            background: #F7F8FA;
            line-height: 39px;
            text-align: center;
            color: #1D2129;
            font-size: 14px;
            font-weight: 500;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
          }

          .details-box {
            text-align: center;
            height: 39px;
            line-height: 39px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            border-bottom: 1px solid rgba(231, 231, 231, 1);
            padding: 0 15px;
            .name {
              // width: 50%;
              height: 100%;
              line-height: 39px;
              color: rgba(78, 89, 105, 1);
              text-align: left;
            }

            .value {
              // width: 50%;
              height: 100%;
              line-height: 39px;
              color: rgba(78, 89, 105, 1);
              text-align: right;
            }
          }

          .details-box:last-child {
            border: none;
          }
        }

        .active-0 {
          width: calc((100% - 2px) / 3);
          // margin: 5px 0px 5px 5px;
          border-right: 1px solid #E5E6EB;
        }

        .active-1 {
          border-right: 1px solid #E5E6EB;
          // margin: 5px 5px 5px 5px;
          width: calc((100% - 0px) / 3)
        }

        .active-2 {
          width: calc((100% - 0px) / 3);
          // margin: 5px 5px 5px 0px;
        }

        .el-table {
          width: 100%;
          border: 1px solid #333;
          border-radius: 4px;

          tr {
            border: rgba(56, 158, 253, 1) 0px solid !important;
          }
        }
      }

      .right {
        width: calc(50% - 8px);

        .title {
          text-align: center;
          border: 1px solid #C0F0FF;
          height: 56px;
          width: 100%;
          background: linear-gradient(90.27deg, #F2FCFF 20.27%, #DAF6FF 98.11%);
          line-height: 56px;
          border-radius: 4px;
          color: #0BABA7;
          font-size: 20px;
          font-weight: 500;
        }
        .securitzation_contents{
          border-radius: 4px;
          background-color: #F2FCFF;
          padding: 20px 16px;
          margin-top: 4px;
        }
        .table-container {
          // margin: 10px 10px 10px 10px;
          // border: 1.5px solid rgba(78, 203, 115, 1);
          border: 1px solid #E5E6EB;
          border-radius: 4px;
          background: #fff;

          .table-title {
            border-bottom: 1px solid #E5E6EB;
            height: 39px;
            line-height: 39px;
            text-align: center;
            color: #1D2129;
            font-size: 14px;
            font-weight: 500;
            background: #F7F8FA;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
          }

          .details-box {
            text-align: center;
            height: 39px;
            line-height: 39px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            border-bottom: 1px solid rgba(231, 231, 231, 1);
            padding: 0 15px;

            .name {
              // width: 50%;
              height: 100%;
              line-height: 39px;
              color: rgba(78, 89, 105, 1);
              text-align: left;
            }

            .value {
              // width: 50%;
              height: 100%;
              line-height: 39px;
              color: rgba(78, 89, 105, 1);
              text-align: right;
            }
          }

          .details-box:last-child {
            border: none;
          }
        }

        .active-0 {
          border-right: 1px solid #E5E6EB;
          width: calc((100% - 2px) / 3);
        }

        .active-1 {
          border-right: 1px solid #E5E6EB;
          width: calc((100% - 0px) / 3);
        }

        .active-2 {
          width: calc((100% - 20px) / 3);
        }
      }


    }
  }

}

.addressDetails{
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 内容超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.headContent{
  height: 57px;
  padding: 4px 0px 20px 0;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  // margin: -15px 0 10px 0;
  .financing-interest {
    height: 32px;
    width: 294px;
    display: flex;
    margin-right: 24px;
    .interest_name{
      width: 114px;
      font-weight: 400;
      font-size: 14px;
      line-height: 32px;
      color: #4E5969;
      margin-right: 16px;
    }
  }
        .Btn {
          display: flex;
          align-items: center;
          >:nth-child(1) {
            width: 80px;
          }
        }
}

::v-deep .boxItemTooltip{
  .el-input-group__append{
    padding: 0 10px;
  }
}

::v-deep .arco-select-view-focus {
  border: 1px solid #409eff !important;
}

::v-deep .arco-select-view-single:hover{
  background: #fff;
  border: 1px solid #e5e6eb;
}
::v-deep .arco-select-view-single {
  border: 1px solid #e5e6eb;
  background-color: #fff;
  height: 32px;
  border-radius: 4px;
  padding-right: 10px;
}

.tableDeleteIcon{
  width: 16px;
  height: 16px;
  cursor: pointer;
  img{
    width: 16px;
    height: 16px;
  }
}
.deleteIconCon{
  display: flex;
  align-items: center;
}
.tableDelete{
  margin-left: 4px;
  cursor: pointer;
font-weight: 400;
font-size: 14px;
line-height: 22px;
color: #F53F3F;
}

::v-deep .arco-table-tr-empty .arco-table-cell{
  height: 240px !important;
}
::v-deep .arco-table-tr .arco-table-cell{
  padding: 8.5px 16px;
}
::v-deep .arco-table-tr .arco-table-th{
  background: #F7F8FA;

}

::v-deep .arco-table-container {
  border-radius: 4px !important;
  .arco-table-body {
    border-radius: 4px !important;
  }
  .arco-table-content {
    border-radius: 4px !important;
    tbody > :nth-last-child(1) > :nth-last-child(1) {
      border-bottom-right-radius: 4px !important;
    }
    tbody > :nth-last-child(1) > :nth-child(1) {
      border-bottom-left-radius: 4px !important;
    }
  }
}


.empty_wrap {
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 400;
	color: #86909c;
	img {
		width: 80px;
		height: 80px;
	}
}

::v-deep .arco-carousel-arrow > div{
  background: #e8f3ff;
}

::v-deep .arco-carousel-arrow > div > svg {
  color: #1868F1;
}

::v-deep .arco-carousel-arrow > div:hover {
  background: #e8f3ff;
}

.slideshow{
  height: 240px;
  padding: 20px 71px 20px 56px;
  box-sizing: border-box;
}

.arrowtopimg{
  margin: 0 0 -3px -5px;
}

::v-deep .arco-table-td-content {
  color: #4e5969 !important;
}

.financeImg{
  display: flex;
  align-items: center;
  img{
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

::v-deep .arco-tabs-tab{
  padding: 6.5px 0!important;
}

::v-deep .arco-tabs-nav-type-line .arco-tabs-tab:hover .arco-tabs-tab-title::before {
  background: #fff!important;
}


.tag_box_details{
  height: 23px;
  line-height: 23px;
  padding: 12px 20px;
  font-weight: 500;
  font-size: 16px;
  color: #1D2129;
  background: #F7F8FA;
  border-bottom: 1px solid  #E5E6EB;
}

.right-top-container{
  width: calc(100% - 16px);
 height: 180px;
 margin: 16px 0 0 16px;
 border: 1px solid #E5E6EB;
 border-radius: 4px;
}
.card_boxss {
  height: 132px;
  width: calc(100% - 11px);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 20px 16px;
  box-sizing: border-box;
  gap: 16px;
  .card_details_box0{
    background: linear-gradient(180deg, #DCECFF 0%, #F2F9FF 100%);
  }
  .card_details_box1{
    background: linear-gradient(180deg, #DAF6FF 0%, #F2FCFF 100%);
  }
  .card_details_box2{
    background: linear-gradient(180deg, #E2E5FF 0%, #F5F7FF 100%);
  }
  .card_details_box3{
    background: linear-gradient(180deg, #DCECFF 0%, #F2F9FF 100%);
  }
  .card-details-box {
    position: relative;
    width:calc((100% - (3* 16px)) / 4);
    // width: 180px;
    height: 92px;
    box-sizing: border-box;
    padding: 17px 0 17px 24px;
    border-radius: 6px;
    .details_boxImg{
      width: 64px;
      height: 64px;
      position: absolute;
      right: 0px;
      bottom: 0px;
    }
    .details_box{
      height: 32px;
      display: flex;
      align-items: flex-end;
      gap: 4px;
      margin-bottom: 4px;
    }
    .value {
      font-weight: 600;
      font-size: 32px;
      line-height: 32px;
      color: #0F2860;
    }
    .topRight_bf{
      color: #0F2860;
      font-weight: 600;
      font-size: 16px;
      line-height: 23px;
    }

    .name {
      font-weight: 400;
      font-size: 14px;
      margin-top: 4px;
      line-height: 22px;
      color: #0F2860;
    }

  }
}


.right_top{
  height: 22px;
  margin: 23px 16px 19px 0;
  background: #FFFFFF;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .title_btn{
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #1868F1;
    display: flex;
    align-items: center;
    z-index: 9;
    cursor: pointer;
    img{
      margin-left: 4px;
    }
  }
}


.add-building {
  width: 100%;
  // height: 446px;
  margin-bottom: 16px;

  .table {
    width: calc(100% - 32px);
    border-radius: 4px;
    border: 1px solid  #E5E6EB;
    height: 412px;
    margin:0 16px 10px 16px;
    box-sizing: border-box;
    .tag_box_details{
      height: 24px;
      line-height: 24px;
      padding: 12px 20px;
      font-weight: 500;
      font-size: 16px;
      color: #1D2129;
      background: #F7F8FA;
      // border: 1px solid  #E5E6EB;
      // border-bottom: none;
    }
    .tag_box_Table{
      height: 364px;
      width: 100%;
      padding: 8px 16px 20px 16px;
      box-sizing: border-box;
      ::v-deep .arco-table-td .arco-table-cell{
        height: 40px;
        box-sizing: border-box;
      }
     ::v-deep .proportionColumn{
      height: 28px;
        // padding: 0 16px!important;
      }
    }
    .operate {
      display: flex;
      justify-content: space-between;
      height: 56px;
      box-sizing: border-box;
      padding: 12px 0;

      // .add {
      //   width: 110px;
      //   height: 32px;
      // }

      // .btn_box {
      //   height: 32px;
      // }
    }
  }

  .container_box {
    .card_box {
      width: 100%;

    }
  }
}