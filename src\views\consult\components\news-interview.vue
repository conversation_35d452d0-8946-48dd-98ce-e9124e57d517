<template>
  <!-- <span>访谈</span> -->
  <el-input v-model="input" placeholder="关键字搜索" class="input" @change="search()"/>

   <el-row>
     <el-col
       v-for="item in interviewList"
       :key="item.id"
     >
       <el-card :body-style="{ padding: '0px' }">
         <img
           :src="`${proxyAddress}${'/upfile_jpg/'}${item.id}${'.jpg?i=1'}`"
           class="image"
         />
         <div style="padding: 14px" class="text">
           <span>{{ item.title }}</span>
         </div>
       </el-card>
     </el-col>
   </el-row>
 </template>
 
 <script setup>
 import http from "@/utils/http.js";
 import { ref } from 'vue'
 const proxyAddress = 'http://*************:8080/api/';

 const input = ref('')
 // const currentDate = ref(new Date())
 const interviewList = ref([])
 const keyword = ref(input.value)
 // 获取咨询数据
 const getinterviewData = async() => { 
   const res = await http.get('/api/api.do?act=listNewsNew3&k=false',
   {params: keyword.value})
   console.log('咨询',res.list);
   interviewList.value = res.list
 }
 getinterviewData()
   // 关键字搜索
   const search = () => {
    keyword.value = input.value
    getinterviewData()
  }
 </script>
 
 <style scoped lang="less">
 .el-row {
   width: 100%;
   display: block;
 }
 .el-col {
   width: calc(25% - 60px);
   float: left;
   margin: 20px auto;
   padding: 20px;
   background-color: #fff;
 }
 .el-col-8 {
   flex: 0;
 }
 .text {
   color: #333;
   flex-grow: 1;
 }
 .image {
   width: 100%;
   display: block;
 }
 .input {
   width: 300px;
   border-radius: 55px;
   background-color: #fff;
 }
 /* .el-tabs__content{
   position: relative;
 } */
 .el-input__wrapper {
   border-radius: 55px;
   /* position: absolute; */
   /* right: 0; */
 }
 </style>
 