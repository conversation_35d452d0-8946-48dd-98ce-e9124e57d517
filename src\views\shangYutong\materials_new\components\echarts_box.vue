<template>
	<div class="tab_box">
		<div class="tubiao_box" ref="echartsContaine"></div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { onMounted, ref } from 'vue';
const echartsContaine = ref(null);
let myChart = null;
const newArr = ref([]);
const props = defineProps({
	sixRingDateList: {
		type: Array,
		default: [],
	},
});
const legend1 = ref('');
const legend2 = ref('');
let chartOption = {
	color: ['#249eff', '#37e2e2'],
	tooltip: {
		show: true,
		trigger: 'item',
	},
	legend: {
		data: ['数据1', '数据2'],
	},
	radar: {
		indicator: [
			{
				name: '维护情况',
			},
			{
				name: '周边配套',
			},
			{
				name: '区域潜力',
			},
			{
				name: '人均消费能力(元)',
			},
			{
				name: '商业活力',
			},
			{
				name: '评估结果(元/㎡)',
			},
		],
	},
	series: [
		{
			type: 'radar',
			data: [],
			// name: '数据1',
			areaStyle: {
				color: '#249EFF66',
			},
		},
		{
			type: 'radar',
			data: [],
			// name: '数据2',
			areaStyle: {
				color: '#37E2E266',
			},
		},
	],
};
onMounted(() => {
	myChart = echarts.init(echartsContaine.value);
	myChart.setOption(chartOption);
});
const echartsData = (sixRingDateList) => {
	newArr.value = sixRingDateList.map((item) => {
		return [item.maintenance, item.assorted, item.regionalPotential, item.spendingPower, item.businessDynamism, item.evaluation];
	});
	newArr.value[0] = newArr.value[0].map((item) => Number(item.toFixed(2)));
	if (newArr.value && newArr.value.length > 1) {
		newArr.value[1] = newArr.value[1].map((item) => Number(item.toFixed(2)));
	}
	legend1.value = sixRingDateList[0]?.buildingName || '';
	legend2.value = sixRingDateList[1]?.buildingName || '';
	updateEcharts(sixRingDateList);
};
const updateEcharts = (sixRingDateList) => {
	if (sixRingDateList.length > 0) {
		chartOption.series = [
			{
				highlight: {
					itemStyle: {
						borderWidth: 4,
						borderColor: '#1E90FF',
					},
				},

				type: 'radar',
				data: [
					{
						value: newArr.value[0],
						name: legend1.value,
					},
					{
						value: newArr.value[1],
						name: legend2.value,
					},
				],
			},
		];
		myChart.setOption(chartOption);
	} else {
		chartOption.series = [
			{
				type: 'radar',
				data: [],
			},
		];
		myChart.setOption(chartOption);
	}
};
defineExpose({
	echartsData,
});
</script>
<style lang="less" scoped>
.tubiao_box {
	width: 360px;
	height: 380px;
	box-sizing: border-box;
}

.tab_box {
	position: relative;
	display: flex;
	justify-content: center;
	.download {
		position: absolute;
		top: 0px;
		right: -97px;
		cursor: pointer;
		color: #333333;
		font-size: 20px;
		font-weight: 600;
		z-index: 9;
		&:hover {
			color: #1868f1;
		}
	}
}
</style>
