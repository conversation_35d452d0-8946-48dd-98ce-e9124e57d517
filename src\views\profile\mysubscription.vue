<template>
    <div class="mysubscription">
        <div class="subscription_item" v-for="item in mysubscription" :key="item">
            <img :src="picurl" alt="">
            <div class="subscription_text">
                <h3>{{ item.name }}</h3>
                <span>{{ item.addedtime }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import http from '@/utils/http';
const mysubscription = ref([])
const picurl = ref('')
// 获取我的订阅
// http://************:8080/api//upfile_jpg/13333333333_37687b4324dd4b43850842d8aa17e321_1.jpg?i=1
const getMySubscription = async () => {
    const res = await http.get('/api/api.do?act=list'); // 替换为实际的API端点
    console.log('订阅',res);
    mysubscription.value = res.list;
    picurl.value = 'http://************:8080/api//upfile_jpg'+res.fid+'.jpg?i=1'
}
// getMySubscription();
</script>

<style scoped>
.subscription_item {
    display: flex;
    padding: 15px;
    margin: 15px;
    /* background-color: #fff; */
    /* border-radius: 15px; */
    border-bottom: 1px solid #ccc;
}
img {
    width: 80px;
    height: 80px;
    margin-right: 20px;
}
</style>