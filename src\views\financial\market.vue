<template>
	<div class="data_box">
		<div class="data_item">
			<div class="top items">
				<span class="datas">上证指数</span>
				<span class="ratio">{{ finDataList.shanghaiCompositeIndex }}</span>
			</div>
			<!-- <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div> -->
		</div>
		<div class="data_item">
			<div class="top items">
				<span class="datas">纳斯达克</span>
				<span class="ratio">{{ finDataList.nasdaq }}</span>
			</div>
			<!-- <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div> -->
		</div>
		<div class="data_item">
			<div class="top items">
				<span class="datas">美元指数</span>
				<span class="ratio">{{ finDataList.usDollarIndex }}</span>
			</div>
			<!-- <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div> -->
		</div>
		<div class="data_item">
			<div class="top items">
				<span class="datas">恒生指数</span>
				<span class="ratio">{{ finDataList.hangSengIndex }}</span>
			</div>
			<!-- <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div> -->
		</div>
		<div class="data_item">
			<div class="top items">
				<span class="datas">在岸人民币</span>
				<span class="ratio">{{ finDataList.onshoreRmb }}</span>
			</div>
			<!-- <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div> -->
		</div>
		<div class="data_item">
			<div class="top items">
				<span class="datas">离岸人民币</span>
				<span class="ratio">{{ finDataList.offshoreRmb }}</span>
			</div>
			<!-- <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div> -->
		</div>
	</div>
	<div class="search">
		<div class="area_box">
			<div class="title">城市</div>
			<div class="city">
				<span v-for="(city, index) in $vuexStore.state.cityArray" :key="index" @click="onChangeCity(city, index)" class="active">{{
					city.label
				}}</span>
			</div>
			<div class="county">
				<span v-for="(county, index) in provinceList" :key="index" @click="onChangeCounty(county, index)" :class="{ active: activeed == index }">{{
					county.label
				}}</span>
			</div>
		</div>
		<div class="property_box">
			<div class="title">资产</div>
			<div class="property">
				<span
					v-for="(property, index) in buildingTypes"
					:key="index"
					@click="onChangeProperty(property, index)"
					:class="{ active: activeIndex == property }"
					>{{ property }}</span
				>
			</div>
		</div>
		<button @click="onSubmit">确认</button>
	</div>
	<div class="echarts_box">
		<div id="myChart1" class="charts"></div>
		<div id="myChart2" class="charts"></div>
		<div id="myChart3" class="charts"></div>
		<div id="myChart4" class="charts"></div>
	</div>
</template>

<script setup>
// 引入echarts
import * as echarts from 'echarts';
import { onBeforeMount, onMounted, ref, toRaw } from 'vue';
import { financialList, financeDataList } from '@/api/finance.js';
const rate = ['S', 'A+', 'A', 'B+', 'B', 'C'];
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const buildingTypes = ['写字楼', '零售', '产业园区', '仓储物流', '酒店', '长租公寓', '医疗', '综合市场'];
const data = ref([]);
const province = ref('');
const provinceList = ref([]);
const city = ref('');
const activeed = ref(0);
const counselor = ref('');
const buildingTypesValue = ref('');
const onChangeCity = (val, index) => {
	debugger;
	provinceList.value = val.children;
	city.value = val.label;
	console.log(province.value, 'province.value', city.value, 'city.value');
	console.log(val, 'val23567', index);
	activeed.value = index;
};

const onChangeCounty = (val, index) => {
	console.log(val, 'val23567', index);
	activeed.value = index;
	province.value = val.label;
};

const activeIndex = ref('');
const onChangeProperty = (val, index) => {
	console.log(val, 'val23567', index);
	activeIndex.value = val;
	buildingTypesValue.value = val;
	console.log(buildingTypesValue.value, 'buildingTypesValue1');
};
onChangeProperty(buildingTypes[0], 0);
const onSubmit = () => {
	console.log(1243);
	const queryParams = {
		city: city.value,
		distance: province.value,
		deType: buildingTypesValue.value,
		reportType: 0,
	};
	console.log(queryParams, 'queryParams1243');
	getData(queryParams);
};
const params = ref({
	reportType: 0,
});
// getData( );
import http from '@/utils/http';
//   onBeforeMount(()=>{
//     getData()
// })

// onMounted(() => { // 需要获取到element,所以是onMounted的Hook
//    getData()
// });
// 获取数据
const echartsList = ref([]);
const data1 = ref([]);
const data2 = ref([]);
const data3 = ref([]);
const data4 = ref([]);
const newarr = ref([]);

const getData = async (queryParams) => {
	// console.log(queryParams, 'queryParams',buildingTypesValue.value);
	if (buildingTypesValue.value == '零售') {
		queryParams.deType = '购物中心,百货大楼';
		console.log(1);
	}
	if (buildingTypesValue.value == '医疗') {
		queryParams.deType = '医疗康养';
	}
	if (buildingTypesValue.value == '长租公寓') {
		queryParams.deType = '公寓';
	}
	try {
		// 发送请求
		const response = await financialList(queryParams);

		console.log('金融市场', response);
		// 把每一项的financeValue拿出来
		newarr.value = response.result.map((item) => item.financeValue);
		console.log(newarr.value, 'arr123');

		let myChart1 = echarts.init(document.getElementById('myChart1'));
		let myChart2 = echarts.init(document.getElementById('myChart2'));
		let myChart3 = echarts.init(document.getElementById('myChart3'));
		let myChart4 = echarts.init(document.getElementById('myChart4'));

		myChart1.setOption({
			title: {
				text: '每平米市场租金/租金要价(可选择资产类型和地区)',
				left: 'center', // 文字说明的位置，默认为居中
				bottom: 10, // 距离底部的距离，单位为像素
				// backgroundColor: '#999',
				width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度

				textStyle: {
					// 文字样式
					color: '#000',
					fontSize: 18,
				},
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
			},
			yAxis: {
				type: 'value',
				min: 0,
				max: 20,
			},

			series: [
				{
					data: newarr.value,
					type: 'line',
					// areaStyle: {},
				},
			],
		});
		// 2
		myChart2.setOption({
			title: {
				text: '市场租金增长(可选择资产类型和地区)',
				left: 'center', // 文字说明的位置，默认为居中
				bottom: 10, // 距离底部的距离，单位为像素
				// backgroundColor: '#999',
				width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度

				textStyle: {
					// 文字样式
					color: '#000',
					fontSize: 18,
				},
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
			},
			yAxis: {
				type: 'value',
				min: 0,
				max: 20,
			},
			series: [
				{
					data: echartsList.value.rentIncrease,
					type: 'line',
					// areaStyle: {},
				},
			],
		});

		// 3
		myChart3.setOption({
			title: {
				text: '每平米市场售价/买入要价(可选择资产类型和地区)',
				left: 'center', // 文字说明的位置，默认为居中
				bottom: 10, // 距离底部的距离，单位为像素
				// backgroundColor: '#999',
				width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度

				textStyle: {
					// 文字样式
					color: '#000',
					fontSize: 18,
				},
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
			},
			yAxis: {
				type: 'value',
				min: 0,
				max: 20,
			},
			series: [
				{
					data: echartsList.value.sale,
					type: 'line',
					// areaStyle: {},
				},
			],
		});
		// 4
		myChart4.setOption({
			title: {
				text: '市场售价增长(可选择资产类型和地区)',
				left: 'center', // 文字说明的位置，默认为居中
				bottom: 10, // 距离底部的距离，单位为像素
				// backgroundColor: '#999',
				width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度
				textStyle: {
					// 文字样式
					color: '#000',
					fontSize: 18,
				},
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
			},
			yAxis: {
				type: 'value',
				min: 0,
				max: 20,
			},
			series: [
				{
					data: echartsList.value.saleIncrease,
					type: 'line',
					// areaStyle: {},
				},
			],
		});
	} catch (error) {
		console.error('请求失败', error);
	}
};
// onMounted(() => {
//     getData()
// }
// )
const finDataList = ref([]);
const getfinData = async () => {
	await financeDataList()
		.then((res) => {
			finDataList.value = res.result;
			console.log(res, 'shuju111');
		})
		.catch((err) => {
			console.log(err);
		});
};
getfinData();
</script>

<style scoped lang="less">
.echarts_box {
	width: 100%;
	display: grid;
	grid-template-columns: repeat(2, 1fr); // 定义三列的布局。
	grid-gap: 50px; // 根据实际需求调整。
}
.charts {
	width: 70%;
	height: 400px;
	padding: 5px;
	margin-left: 15px;
	box-shadow: 1px 1px 1px 1px #ccc;
	margin-top: 25px;
}
.data_box {
	width: 100%;
	display: flex;
	justify-content: space-around;
}
.data_item {
	width: 13%;
	height: 60px;
	box-sizing: border-box;
	border: 1px solid rgb(205, 205, 205);
	background: rgb(250, 250, 250);
	display: flex;
	justify-content: center;
	align-items: center;
}
.items {
	// flex: 1;
	// width: 15%;
	display: flex;
	vertical-align: middle;
	text-align: center;
}
.ratio {
	color: green;
	margin-left: 15px;
}
.datas {
	margin-bottom: 15px;
	font-size: 18px;
	font-weight: 600;
}
.search {
	width: 100%;
	height: 285px;
	background: rgb(250, 250, 250);
	padding: 30px 0;
	position: relative;
	button {
		width: 85px;
		height: 36px;
		border-radius: 2px;
		background: rgb(56, 96, 154);
		color: rgb(255, 255, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 21px;
		letter-spacing: 0px;
		// text-align: center;
		position: absolute;
		right: 20px;
		border: none;
	}
	.active {
		box-sizing: border-box;
		border: 1px solid rgb(64, 158, 255);
		border-radius: 2px;

		background: rgb(241, 248, 255);
		color: rgb(64, 158, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 23px;
		letter-spacing: 0px;
		text-align: left;
	}
	.area_box {
		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}
		.city {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin: 15px 0;
			margin-bottom: 15px;
			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
		.county {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin: 15px 0;
			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}
	.property_box {
		margin-top: 15px;
		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}
		.property {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}
	.type_box {
		margin-top: 15px;
		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}
		.type {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}
}
</style>
