<template>
	<el-dialog
		v-model="props.resendDialogVisible"
		:close-on-click-modal="false"
		:show-close="false"
		align-center="center"
		:before-close="handleClose"
		style="width: 700px"
	>
		<template #header>
			<div class="dialogHeader">
				<div class="dialogHeaderLeft">
					<el-icon><ArrowLeftBold @click="handleClose" /></el-icon>
					<div>重新发送发票</div>
				</div>

				<div class="dialogHeaderRight">
					<el-icon><CloseBold @click="handleClose" /></el-icon>
				</div>
			</div>
		</template>
		<div class="form_content">
			<div class="content_bottom">
				<div class="content_name">接收邮箱</div>
				<el-input v-model="email" size="large" class="container_Input_email" style="width: 100%; margin-bottom: 8px" placeholder="邮箱" />
				<div class="container_text_email">电子发票需要一定时间才能发送到您的邮箱，请耐心等候</div>
			</div>
		</div>
		<template #footer>
			<div class="dialog_footer">
				<div>
					<el-button @click="handleResend" color="#1868F1"> 重新发送 </el-button>
				</div>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { reSendInvoice } from '@/api/equityTerm.js';

const emit = defineEmits();
const props = defineProps({
	resendDialogVisible: {
		type: Boolean,
		default: false,
	},
	objActive: {
		type: Object,
		default: () => {},
	},
});
const email = ref(null);

watch(
	() => props.resendDialogVisible,
	() => {
		if (props.resendDialogVisible) {
			email.value = props.objActive.receiveEmail;
		}
	}
);
// 关闭对话框
function handleClose() {
	emit('handleEmailDialogClose');
}
// 重新发送
function handleResend() {
	reSendInvoice({ id: props.objActive.id, email: email.value }).then((res) => {
		if (res.code === 200) {
			//成功提示
			ElMessage({
				message: '重新发送成功',
				type: 'success',
			});
			emit('handleResendClose');
		}
	});
}
</script>

<style lang="scss" scoped>
.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	color: rgba(29, 33, 41, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	.el-icon {
		cursor: pointer;
	}

	.dialogHeaderLeft {
		display: flex;
		align-items: center;
	}
}

.form_content {
	padding: 0 16px;
	.content_bottom {
		height: 316px;
		padding: 80px 120px 0 120px;
		.content_name {
			font-weight: 500;
			line-height: 22px;
			font-size: 14px;
			color: #4e5969;
			margin-bottom: 10px;
		}
	}
}

.dialog_footer {
	height: 74px;
	width: 100%;
	border-top: 1px solid rgba(231, 231, 231, 1);
	display: flex;
	align-items: center;
	justify-content: center;
	button {
		height: 34px;
		width: 216px;
	}
}

.container_Input_email {
	::v-deep .el-input__inner {
		height: 54px;
		font-size: 14px;
		color: #1d2129;
	}
}

.container_text_email {
	font-size: 10px;
	font-weight: 400;
	line-height: 18px;
	color: #86909c;
}
</style>
