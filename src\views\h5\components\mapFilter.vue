<template>
	<div class="filter_wrap">
		<div class="input_wrap">
			<el-input v-model="keyword" placeholder="请输入关键词搜索" :prefix-icon="Search" @change="confirm" clearable> </el-input>
			<el-icon @click="confirm"><Search /></el-icon>
		</div>
		<div class="select_wrap">
			<div class="select_label" @click="handleFilter('location')">
				<div class="label" :class="{ active: locationActive || locationLabel !== '位置' }">{{ locationLabel }}</div>
				<IconUp v-if="locationActive" size="8" style="color: #1868f1" />
				<IconDown v-else size="8" :style="{ color: locationLabel !== '位置' ? '#1868f1' : '#1d2129' }" />
			</div>
			<div class="select_label" @click="handleFilter('type')">
				<div class="label" :class="{ active: typeActive || typeSelected }">资产类型</div>
				<IconUp v-if="typeActive" size="8" style="color: #1868f1" />
				<IconDown v-else size="8" :style="{ color: typeSelected ? '#1868f1' : '#1d2129' }" />
			</div>
			<div class="select_label" @click="handleFilter('more')">
				<div class="label" :class="{ active: moreActive || moreSelected }">更多</div>
				<IconUp v-if="moreActive" size="8" style="color: #1868f1" />
				<IconDown v-else size="8" :style="{ color: moreSelected ? '#1868f1' : '#1d2129' }" />
			</div>
		</div>
		<div class="select_content_wrap" v-if="filterContentShow">
			<template v-if="currentFilterKey == 'location'">
				<div class="cascade-menu">
					<div class="menu-column1">
						<div
							@click="handleLevel1Select(item)"
							v-for="(item, index) in cityOptions"
							:key="index"
							:class="['menu-item', city === item.text ? 'active' : '']"
						>
							{{ item.text }}
						</div>
					</div>

					<div class="menu-column2" v-if="districtOptions.length">
						<div
							@click="handleLevel2Select(item)"
							v-for="(item, index) in districtOptions"
							:key="index"
							:class="['menu-item', district === item.text ? 'active' : '']"
						>
							{{ item.text }}
						</div>
					</div>

					<div class="menu-column3" v-show="businessNameOptions.length">
						<div
							@click="handleLevel3Select(item)"
							v-for="(item, index) in businessNameOptions"
							:key="index"
							:class="['menu-item', businessName === item.text ? 'active' : '']"
						>
							{{ item.text }}
						</div>
					</div>
				</div>
			</template>
			<template v-if="currentFilterKey == 'type'">
				<div class="tag_wrap">
					<div
						@click="handleChecked('type', index)"
						class="tag"
						:class="{ tag_active: item.select }"
						v-for="(item, index) in typeList"
						:key="item.value"
					>
						{{ item.label }}
					</div>
				</div>
			</template>
			<template v-if="currentFilterKey == 'more'">
				<div class="content">
					<div
						class="block"
						:class="{ last: index == menuList.length - 1 }"
						v-for="(item, index) in menuList"
						:key="item.searchKey"
						:ref="item.searchKey"
						:id="item.searchKey"
					>
						<div class="title">{{ item.name }}</div>
						<div class="detail">
							<div
								:class="[`itm ${val.select ? 'chose' : ''}`]"
								v-for="(val, index1) in item.itemList"
								:key="index1"
								@click="choseItm(item, val, index1)"
							>
								{{ val.name }}
							</div>
						</div>
					</div>
				</div>
			</template>
			<view class="footer_wrap">
				<view class="reset" @click="reset">重置</view>
				<view class="submit" @click="confirm">确定</view>
			</view>
		</div>
	</div>
</template>
<script setup>
import { Search } from '@element-plus/icons-vue';
import { IconDown, IconUp } from '@arco-design/web-vue/es/icon';
import { getAreaCategory, getBusinessList, getBuildingType } from '@/api/h5.js';
import { computed, onMounted, ref } from 'vue';

const emits = defineEmits(['confirm', 'reset', 'close', 'filterShow']);
const props = defineProps({
	locationValue: {
		type: Object,
		default: () => ({
			city: '',
			district: '',
			businessName: '',
		}),
	},
	// 是否有定位
	isPositioning: {
		type: Boolean,
		default: false,
	},
});

const keyword = ref('');
const filterContentShow = ref(false);
const currentFilterKey = ref(null);
const cityOptions = ref([]);
const districtOptions = ref([]);
const businessNameOptions = ref([]);
const latitude = ref('');
const longitude = ref('');
const city = ref('');
const district = ref('');
const businessName = ref('');

const locationLabel = computed(() => {
	if (businessName.value == '' && district.value == '' && city.value == '') {
		return '位置';
	} else if (businessName.value !== '') {
		if (businessName.value == '不限') {
			return district.value;
		} else {
			return businessName.value;
		}
	} else if (district.value !== '') {
		if (district.value == '不限') {
			return city.value;
		} else {
			return district.value;
		}
	} else {
		return city.value;
	}
});
const locationActive = computed(() => {
	return currentFilterKey.value === 'location' && filterContentShow.value;
});
const typeActive = computed(() => {
	return currentFilterKey.value === 'type' && filterContentShow.value;
});
const typeSelected = computed(() => {
	return typeList.value.some((item) => item.select);
});
const moreActive = computed(() => {
	return currentFilterKey.value === 'more' && filterContentShow.value;
});
const moreSelected = computed(() => {
	return menuList.value.some((item) => {
		return item.itemList.some((val) => val.select);
	});
});

onMounted(() => {
	getDict();
	handleGetDict();
});

function handleFilter(type) {
	console.log('🚀 ~ handleOpen ~ type:', type);
	if (currentFilterKey.value == type && filterContentShow.value) {
		filterContentShow.value = false;
	} else {
		filterContentShow.value = true;
	}
	currentFilterKey.value = type;
	emits('filterShow', filterContentShow.value);
}

// 获取区域字典
async function getDict() {
	const res = await getAreaCategory();
	if (res.code === 200) {
		cityOptions.value = res.result;
		cityOptions.value.unshift({
			text: '不限',
			value: 0,
		});
		cityOptions.value.forEach((item) => {
			if (item.children) {
				item.children.unshift({
					text: '不限',
					value: 0,
				});
			}
		});
		console.log('🚀 ~ cityOptions.value.forEach ~ cityOptions.value:', cityOptions.value);
	}
}

const typeList = ref([]);
const menuList = ref([
	{
		searchKey: 'buildingRate',
		name: '资产评级',
		itemList: [
			{
				name: '不限',
				key: '',
				select: false,
			},
			{
				name: 'S级',
				key: '1',
				select: false,
			},
			{
				name: 'A+级',
				key: '2',
				select: false,
			},
			{
				name: 'A级',
				key: '3',
				select: false,
			},
			{
				name: 'B+级',
				key: '4',
				select: false,
			},
			{
				name: 'B级',
				key: '5',
				select: false,
			},
			{
				name: 'C级',
				key: '6',
				select: false,
			},
		],
	},
	{
		searchKey: 'distribution',
		name: '证券化',
		itemList: [
			{
				name: '不限',
				key: '',
				select: false,
			},
			{
				name: '是',
				key: true,
				select: false,
			},
			{
				name: '否',
				key: false,
				select: false,
			},
		],
	},
	{
		searchKey: 'deal',
		name: '交易历史',
		itemList: [
			{
				name: '不限',
				key: '',
				select: false,
			},
			{
				name: '有',
				key: true,
				select: false,
			},
			{
				name: '无',
				key: false,
				select: false,
			},
		],
	},
]);
// 获取筛选字典项
async function handleGetDict() {
	const res = await getBuildingType();
	// 类型字典
	typeList.value = res.result.map((item) => {
		return {
			...item,
			select: false,
		};
	});
	typeList.value.unshift({
		label: '不限',
		value: '',
		select: false,
	});
}
// 处理一级菜单选择
async function handleLevel1Select(item) {
	latitude.value = item.latitude;
	longitude.value = item.longitude;
	city.value = item.text;
	districtOptions.value = item.children || [];
	businessNameOptions.value = [];
	district.value = '';
	businessName.value = '';
}
// 处理二级菜单选择
async function handleLevel2Select(item) {
	district.value = item.text;
	businessName.value = '';
	if (item.value !== 0) {
		latitude.value = item.latitude;
		longitude.value = item.longitude;
		// 获取三级菜单数据
		const res = await getBusinessList({
			city: city.value,
			district: item.text,
		});
		if (res.code === 200) {
			businessNameOptions.value = res.result;
			businessNameOptions.value.unshift({
				text: '不限',
				value: 0,
			});
		}
	} else {
		businessNameOptions.value = [];
	}
}
// 处理三级菜单选择
function handleLevel3Select(item) {
	latitude.value = item.latitude;
	longitude.value = item.longitude;
	businessName.value = item.text;
}
function handleChecked(type, index) {
	if (type == 'type') {
		if (index == 0) {
			typeList.value.forEach((element) => {
				element.select = false;
			});
		} else {
			typeList.value[0].select = false;
		}
		typeList.value[index].select = !typeList.value[index].select;
	}
}
const moreParams = ref({});
function choseItm(item, e, index) {
	// 单选
	if (item.itemList[index].select) {
		item.itemList[index].select = false;
	} else {
		item.itemList.forEach((item, i) => (item.select = i === index));
	}
	// 多选
	// item.itemList[index].select = !item.itemList[index].select;

	moreParams.value[item.searchKey] = item.itemList
		.filter((item) => item.select)
		.map((item) => item.key)
		.join();
}
function confirm() {
	let data = {
		keyword: keyword.value,
		city: city.value == '不限' ? '' : city.value,
		district: district.value == '不限' ? '' : district.value,
		businessName: businessName.value == '不限' ? '' : businessName.value,
		buildingType: '',
		latitude: latitude.value,
		longitude: longitude.value,
	};
	if (typeSelected.value) {
		data.buildingType = typeList.value
			.filter((item) => item.select)
			.map((item) => item.value)
			.join();
	} else {
		data.buildingType = '';
	}
	data = {
		...data,
		...moreParams.value,
	};
	emits('confirm', data);
	filterContentShow.value = false;
}
function reset() {
	keyword.value = '';
	city.value = '';
	district.value = '';
	businessName.value = '';
	latitude.value = '';
	longitude.value = '';
	typeList.value.forEach((item) => (item.select = false));
	menuList.value.forEach((item) => {
		item.itemList.forEach((val) => (val.select = false));
	});
	emits('reset');
	filterContentShow.value = false;
}
</script>
<style lang="less" scoped>
:deep(.el-input__wrapper) {
	background-color: #f2f3f5;
	box-shadow: none;
	border-radius: 4px;
}
.filter_wrap {
	position: relative;
	display: flex;
	flex-direction: column;
	height: 100%;
	.input_wrap {
		display: flex;
		align-items: center;
		gap: 8px;
		box-sizing: border-box;
		height: 52px;
		width: 100%;
		padding: 12px 12px 4px 12px;
	}
	.select_wrap {
		box-sizing: border-box;
		height: 38px;
		width: 100%;
		padding: 9px 12px;
		color: #1d2129;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.select_label {
			padding: 0 8px;
			display: flex;
			align-items: center;
			.label {
				margin-right: 4px;
			}
			.active {
				font-weight: 500;
				color: #1868f1;
			}
		}
	}
	.select_content_wrap {
		background-color: #fff;
		z-index: 99;
		border-radius: 0 0 12px 12px;
		.cascade-menu {
			display: flex;
			height: 280px;
			background: #fff;
			.menu-column1 {
				overflow: auto;
				width: 72px;
				background: #f2f3f5;
				.active {
					background: #f7f8fa;
					color: #1868f1;
				}
			}
			.menu-column2 {
				overflow: auto;
				width: 120px;
				background: #f7f8fa;
				.active {
					background: #fff;
					color: #1868f1;
				}
			}
			.menu-column3 {
				overflow: auto;
				flex: 1;
				background-color: #fff;
				.active {
					background: #fff;
					color: #1868f1;
				}
			}
			.menu-item {
				padding: 9px 4px 9px 16px;
				font-size: 14px;
				color: #1d2129;
			}
		}
		.tag_wrap {
			padding: 12px;
			display: grid;
			grid-template-columns: repeat(3, 1fr); /* 三列等宽 */
			gap: 8px; /* 设置间距 */
			.tag {
				height: 32px;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #f7f8fa;
				color: #1d2129;
				font-size: 12px;
				font-weight: 400;
				line-height: 18px;
				border-radius: 4px;
			}
			.tag_active {
				background: #edf3fe;
				color: #1868f1;
				font-weight: 500;
			}
		}
		.label {
			color: #1d2129;
			font-size: 16px;
			font-weight: 500;
			line-height: 22px;
			margin-top: 4px;
			text-align: center;
		}
		.value {
			font-size: 14px;
			font-weight: 400;
			line-height: 20px;
			color: #1d2129;
			text-align: center;
			margin-top: 8px;
			margin-bottom: 4px;
		}
		.slider_wrap {
			width: 100%;
			padding: 0 12px;
		}
		.content {
			width: 100%;
			padding: 8px 12px;
			box-sizing: border-box;
			.block {
				margin-bottom: 16px;
				.title {
					font-weight: 500;
					margin-bottom: 4px;
					color: #000000;
					font-size: 14px;
					line-height: 22px;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
				.value {
					margin-top: 4px;
				}
				.detail {
					display: grid;
					grid-template-columns: repeat(3, 1fr); /* 三列等宽 */
					gap: 8px; /* 设置间距 */
					.itm {
						height: 32px;
						display: flex;
						align-items: center;
						justify-content: center;
						background: #f7f8fa;
						color: #1d2129;
						font-size: 12px;
						font-weight: 400;
						line-height: 18px;
						border-radius: 4px;
					}
					.chose {
						background: #edf3fe;
						color: #1868f1;
						font-weight: 500;
					}
				}
			}
			.last {
				height: 100%;
			}
		}
		.sort_wrap {
			padding: 4px 12px;
			.sort_item {
				width: 100%;
				height: 40px;
				padding-left: 12px;
				display: flex;
				align-items: center;
				font-size: 14px;
				font-weight: 400;
				line-height: 38px;
				color: #1d2129;
				background-color: #fff;
				border-radius: 4px;
			}
			.sort_item_active {
				color: #1868f1;
				font-weight: 500;
				background: #edf3fe;
			}
		}
		.footer_wrap {
			box-sizing: border-box;
			height: 72px;
			padding: 0 12px;
			width: 100%;
			display: flex;
			align-items: center;
			gap: 8px;
			box-shadow: 0px -2px 8px 0px #1d212914;
			background: #ffffff;
			border-radius: 0 0 12px 12px;
			.reset,
			.submit {
				flex: 1;
				height: 36px;
				border-radius: 6px;
				background: #e5e6ea;
				font-size: 14px;
				font-weight: 500;
				line-height: 20px;
				color: #4e5969;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.submit {
				background: #1868f1;
				color: #fff;
			}
		}
	}
}
</style>
