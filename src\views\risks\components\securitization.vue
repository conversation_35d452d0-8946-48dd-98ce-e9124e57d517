<template>
	<div style="display: flex; justify-content: center">
		<el-table :data="dataTable" border :span-method="objectSpanMethod" style="width: 100%" :lazy="true" :class="classname" empty-text="暂无数据">
			<el-table-column
				border
				resizable
				min-width="30"
				label-class-name="fixed_header"
				v-for="column in tableColumns"
				:key="column.prop"
				:prop="column.prop"
				:label="column.label"
				:align="column.align"
				:width="column.width"
				style="height: 100px"
				:formatter="formatterAmount"
			>
				<!-- <template #default="scope">
					<div>{{ column.prop === 'interestRate' ? scope.row[column.prop] * 100 + '%' : scope.row[column.prop] }}</div>
				</template> -->
			</el-table-column>
		</el-table>
	</div>
</template>
<script setup>
import { defineProps, onMounted, watch } from 'vue';
const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
	classname: {
		type: String,
		default: [],
	},
});

const dataTable = ref([]);

watch(
	() => props.data,
	() => {
		if (!props.data.length) return;
		handleDateUpdata();
	}
);
onMounted(() => {
	handleDateUpdata();
});

function handleDateUpdata() {
	if (!props.data.length) return;
	props.data.forEach((item, index) => {
		if (item.stratification === '优先级规模' || item.stratification === '劣后级') {
			item.level = item.scale;
			item.scale = '';
			if (item.stratification === '优先级规模') {
				item.scale = '总规模';
				item.interestRate = props.data[index + 2].scale;
			}
		}
	});
	props.data.splice(-1, 1);
	dataTable.value = props.data;
}
const formatterAmount = (row, column, cellValue) => {
	if (column.label == '规模(亿)') {
		if (typeof cellValue == 'number') {
			return cellValue.toFixed(2);
		}
	} else if (parseFloat(cellValue) && column.label.includes('率') && row.stratification != '优先级规模') {
		let cellv = cellValue * 100;
		return cellv.toFixed(2) + '%';
	} else if (parseFloat(cellValue) && row.scale == '总规模' && row.stratification == '优先级规模') {
		return cellValue.toFixed(2) + '亿';
	} else if (parseFloat(cellValue) && row.stratification == '劣后级') {
		return cellValue.toFixed(2) + '亿';
	} else if (parseFloat(cellValue)) {
		return cellValue.toFixed(2);
	}
	return cellValue;
};
const tableColumns = [
	// { prop: 'index', label: '序号' },
	{ prop: 'stratification', label: '分层', width: 100 },
	{ prop: 'level', label: '评级', align: 'center' },
	{ prop: 'scale', label: '规模(亿)', align: 'center' },
	{ prop: 'interestRate', label: '利率', align: 'center' },
	{ prop: 'coverageRatio', label: '覆盖倍数', align: 'center' },
	{ prop: 'loanToValueRatio', label: '抵押率', align: 'center' },
];
const secured = [];

/**
 * @param {Object} row - The row object.
 * @param {Object} column - The column object.
 * @param {number} rowIndex - The row index.
 * @param {number} columnIndex - The column index.
 * @return {Object} An object containing rowspan and colspan properties.
 */
function objectSpanMethod({ row, column, rowIndex, columnIndex }) {
	if ((rowIndex === 3 || rowIndex === 4) && columnIndex === 1) {
		//优先级规模 //劣后级合并
		return [1, 2];
	} else if (rowIndex === 3 && columnIndex === 2) {
		//总规模
		return [2, 1];
	} else if (rowIndex === 3 && columnIndex === 3) {
		//规模亿
		return [2, 2];
	} else {
		return [1, 1];
	}
}
</script>
<style scoped lang="less">
::v-deep .fixed_header .cell {
	font-weight: 500;
	font-size: 14px;
	line-height: 22px;
	color: #1d2129;
}

::v-deep .fixed_header {
	height: 40px;
	background: #f7f8fa !important;
}

.el-table {
	--el-table-border-color: #e5e6eb;
	border-radius: 4px;
	.el-table__row {
		background: #fff !important;
	}
}
</style>
