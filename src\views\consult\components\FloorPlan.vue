<template>
	<div class="body_box">
		<el-button plain @click="search()" class="getbtn"> 获取平面图 </el-button>
		<div style="display: flex; flex-wrap: wrap; justify-content: center; align-items: center">
			<div class="box" v-for="item in imgList">
				<el-image style="width: 900px; height: 500px" :src="`${proxyAddress}${item.url}`" fit="scale-down" />
			</div>
		</div>
	</div>
	<!-- 对话框 -->
	<el-dialog v-model="dialogVisible" title="户型图" width="800" :before-close="handleClose">
		<el-row :gutter="20">
			<el-col :span="8">
				<el-cascader placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }"> </el-cascader>
			</el-col>
			<el-col :span="8">
				<el-select style="margin-left: 20px; width: 200px" v-model="rateValue" placeholder="全部资产评级">
					<el-option v-for="(item, value) in buildingTypes" :key="item" :label="item" :value="item" />
				</el-select>
			</el-col>
			<el-col :span="6">
				<el-input v-model="essential" placeholder="请输入关键字"></el-input>
			</el-col>
			<el-col :span="2">
				<el-button @click="search">查询</el-button>
			</el-col>
		</el-row>
		<el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange" stripe>
			<el-table-column type="selection" width="55" ref="multipleTableRef" />
			<el-table-column v-for="(column, index) in tableColumns" :key="index" :label="column.label" :prop="column.prop" :width="column.width" />
		</el-table>
		<!-- 分页 -->
		<el-pagination
			@current-change="pageChange"
			:current-page="currentPage"
			small
			background
			layout="prev, pager, next"
			class="mt-4"
			:total="tableData.length"
		/>

		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="onConfirm()">确认</el-button>
				<el-button @click="reset"> 取消 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import http from '@/utils/http';
// import { pcaTextArr } from 'element-china-area-data';
import { ElMessage } from 'element-plus';
const province = ref('');
const city = ref('');
const county = ref('');
const imgList = ref([]);
const proxyAddress = process.env.NODE_ENV === 'development' ? 'http://*************:8080/' : 'http://**************:8081/';
const search = async () => {
	const res = await http.get('/api/a_maps.do?act=getMap', {
		params: {
			province: province.value,
			city: city.value,
			county: county.value,
			de_type: rateValue.value,
			keywords: essential.value,
			currentPage: currentPage.value,
		},
	});
	tableData.value = res.list;
	dialogVisible.value = true;
	console.log(res, 'ss');
};
const handleChange = (val) => {
	console.log(val, 'ss');
	province.value = val[0];
	city.value = val[1];
	county.value = val[2];
};
const rateValue = ref('');
const essential = ref('');

const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '数据中心', '保障房'];
const dialogVisible = ref(false);
// const familyList = ref([])
const currentPage = ref(1);
// const total = ref(0)
const tableData = ref([]);
// 选中的值
const selectValue = ref([]);
const idss = ref([]); //id存放
const multipleSelection = ref([]);
const tableColumns = [
	{ label: '地图名字', prop: 'name', width: '300' },
	{ label: '类型', prop: 'de_type', width: '300' },
];
//
const pageChange = (val) => {
	console.log(11, val);
	currentPage.value = val;
	search();
};
const reset = () => {
	province.value = '';
	city.value = '';
	county.value = '';
	rateValue.value = '';
	essential.value = '';
	currentPage.value = 1;
	dialogVisible.value = false;
};
const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};

// 点击获取
// 获取户型图url
const getFloorPlanList = async () => {
	console.log('ids1', idss.value);
	const res = await http.get('/api/a_maps.do?act=getFloorPlan', {
		params: {
			ids: idss.value.join(','),
		},
	});

	imgList.value = res.list;
};
// 点击确定
const onConfirm = () => {
	// 检查是否选择了多个项目
	if (multipleSelection.value.length !== 1) {
		ElMessage.warning('只能选择一个噢');
		return;
	}

	// 如果只选择了一个项目，则继续后续操作
	console.log('选中的值', multipleSelection.value);
	selectValue.value = multipleSelection.value;
	idss.value = selectValue.value.map((item) => item.id);
	getFloorPlanList();
	dialogVisible.value = false;
	reset();
};
// 点击图片进行缩放
const imgClick = (event) => {
	console.log(event);
};
</script>
<style lang="less" scoped>
.body_box {
	text-align: center;
	width: 100%;
}

.box {
	width: 48%; /* 使每个 box 占据容器宽度的一半，留出一些间隔 */
	margin-bottom: 10px;
}

.getbtn {
	width: 500px;
	height: 40px;
	margin: 30px auto;
}
</style>
