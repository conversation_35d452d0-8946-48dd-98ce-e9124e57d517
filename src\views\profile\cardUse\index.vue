<template>
	<div class="carduse-container">
		<div class="container_top">
			<el-icon @click="goBack" class="container_top_elion"><ArrowLeftBold /></el-icon>
			<div @click="goBack" class="container_top_title">使用卡卷</div>
			<div class="container_top_line"></div>
			<div class="container_top_prompt">确认使用后权益卡券即生效，开始消耗权益时长</div>
		</div>

		<div style="overflow: hidden scroll; height: calc(100% - 150px)">
			<div class="prompt" v-if="data.objCrad.alert">
				<img src="@/assets/prompt.png" alt="" />【现金流】【证券化】【Pre-Reits基金】权益在【信用风险】权益生效的前提下才能正常使用
			</div>
			<div class="contaner_content_">
				<div
					:class="`${
						data.objCrad.cardStatus === '3'
							? 'type_2'
							: data.objCrad.cardStatus == '2'
							? 'type_1'
							: data.objCrad.cardStatus == 'newUser'
							? 'newUser'
							: 'status_3'
					}`"
					class="details-box"
				>
					<div class="operate">
						<div class="status">
							<div class="type occupied" v-if="data.objCrad.cardStatus == '2'">未使用</div>
							<div class="type xse" v-if="data.objCrad.cardStatus == '3'">未使用</div>
							<div class="type newUser" v-if="data.objCrad.cardStatus == 'newUser'">未使用</div>
						</div>
						<div class="button-data">
							<div class="button_top_content">
								<div>{{ data.objCrad.description }}</div>
								<div class="occupied">{{ data.objCrad.cityName?.replace('市', '') }}</div>
							</div>
							<div class="button_content_two">{{ data.objCrad.periodName }}</div>
							<div class="button_contents">{{ data.objCrad.buyTime + '购买' }}</div>
						</div>
					</div>
					<div class="center_line"></div>
					<div class="right-content">
						<div class="status_name">{{ data.objCrad.statusName }}</div>
					</div>
				</div>
			</div>
			<div class="contain-rights">
				<div class="name">包含权益</div>
				<div class="value">
					<div v-for="(item, index) in data.objCrad.includeDescArray" :key="index">{{ item }}</div>
				</div>
			</div>
			<div class="title-box">
				<div class="name">使用人</div>
				<div class="value">最多可添加{{ data.objCrad.team }}名使用人</div>
				<div class="accountUser" @click="handleAddAccount">
					<img src="@/assets/account.png" alt="" />
					<div>添加本账号为使用人</div>
				</div>
			</div>
			<div class="user-selection">
				<smUserCard :width="'255px'" :team="data.objCrad.team" @handleRemove="handleRemove" ref="smUserCards" @handleAddCrad="add"></smUserCard>
			</div>
		</div>
		<div style="display: flex; justify-content: center; margin-bottom: -16px; align-items: center; height: 74px; border-top: 1px solid #e7e7e7">
			<el-button type="primary" @click="handleConfirmUse" color="#1868F1">确认使用</el-button>
		</div>
		<el-dialog v-model="dialogVisibleRomove" :show-close="false" top="30vh" width="448" :before-close="handleCloseRomove">
			<template #header>
				<div class="dialogHeader dialogHeaders">
					<div class="dialogHeaderLeft">
						<div>提示</div>
					</div>
				</div>
			</template>
			<div class="dialogRomove">
				<div>确认移除该使用人吗？</div>
				<div>移除后您可添加其他使用人或重新添加该使用人</div>
			</div>
			<template #footer>
				<div class="dialog_footer dialog_footers">
					<div>
						<el-button @click="dialogVisibleRomove = false"> 取消 </el-button>
						<el-button type="danger" @click="handleConfirm"> 移除 </el-button>
					</div>
				</div>
			</template>
		</el-dialog>

		<dialogAddUser :dialogVisible="dialogVisible" @handleDialogClose="handleDialogClose"></dialogAddUser>
	</div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import dialogAddUser from './dialogAddUser.vue';
import smUserCard from '../../../component/sm-userCard/index.vue';
import { getUserCoupon, getSetUser } from '@/api/equityTerm.js';
import { useStore, vuexStore } from '../../../store/index.js';
const emit = defineEmits(['handleComponentClose']);
const route = useRoute();
const router = useRouter();

const props = defineProps({
	// 当前卡卷对象
	params: {
		type: Object,
		default: {},
	},
});

const dialogVisible = ref(false);
// 移除弹窗
const dialogVisibleRomove = ref(false);
let cardIndex = ref(null);
const smUserCards = ref(null);
let data = reactive({
	objCrad: {},
});

onMounted(() => {
	if (!props.params) return;
	// 处理数据
	handleProcessing();
	// 使用人
	handleUserPage();
});
// 关闭提示
function handleCloseRomove() {
	dialogVisibleRomove.value = false;
}

// 确定移除
function handleConfirm() {
	smUserCards.value.handleSureRemoves(cardIndex.value);
	dialogVisibleRomove.value = false;
}

/**
 * @function handleRemove 移除使用人
 * @param index 移除的索引
 */
function handleRemove(index) {
	cardIndex.value = index;
	dialogVisibleRomove.value = true;
	// 移除使用人
	// smUserCards.value.list.dataListCard.splice(index, 1);
}

const add = () => {
	dialogVisible.value = true;
};

const goBack = (param) => {
	emit('handleComponentClose', param);
};

// 确定使用
function handleConfirmUse() {
	// 如果是套餐卡券类型 使用人数不能超过团队人数
	if (data.objCrad.type === 'PACKAGE' && smUserCards.value.list.dataListCard?.length < data.objCrad.team) {
		ElMessage({
			message: `最多添加${data.objCrad.team}名使用人`,
			type: 'warning',
		});
		return;
	}
	// if (smUserCards.value.list.dataListCard?.length > 1) {
	// 	ElMessage({
	// 		message: `最多添加1名使用人`,
	// 		type: 'warning',
	// 	});
	// 	return;
	// }
	if (!smUserCards.value.list.dataListCard || smUserCards.value.list.dataListCard?.length === 0) {
		ElMessage({
			message: `请至少添加1名使用人`,
			type: 'warning',
		});
		return;
	}
	handleSetUser();
}

/**
 * @function handleSetUser 添加使用人
 */
function handleSetUser() {
	let arr = [];
	smUserCards.value.list.dataListCard.forEach((item) => {
		arr.push(item.id);
	});
	let param = {
		userIds: arr,
		userCouponId: data.objCrad.id,
		ignoreWarn: false,
	};
	getSetUser(param).then((res) => {
		if (res.code === 200) {
			ElMessage({
				message: '使用成功',
				type: 'success',
			});
			goBack(1);
			// smUserCards.value.handleListCard(res.data.rows);
		}
	});
}

// 处理数据
function handleProcessing() {
	data.objCrad = props.params;
	data.objCrad['includeDescArray'] = data.objCrad.includeDesc.split('；');
}

/**
 * @function handleUserPage 使用人
 */
function handleUserPage() {
	getUserCoupon({ id: data.objCrad.id }).then((res) => {
		if (res.code === 200) {
			smUserCards.value.handleListCard(res.data);
		}
	});
}

// 去重
function handleUnique(arr) {
	const res = new Map();
	return arr.filter((arr) => !res.has(arr.phone) && res.set(arr.phone, 1));
}

/**
 * @function handleDialogClose 关闭添加使用人弹窗
 * @param list 选中的使用人数组
 */
function handleDialogClose(list) {
	if (list && list.value) {
		let arr = [];
		// tableDataTerm.dataList = list.value;
		arr = [...smUserCards.value.list.dataListCard, ...list.value];
		let arr1 = handleUnique(arr);
		smUserCards.value.handleListCard(arr1);
	}
	dialogVisible.value = false;
}

// 添加本账号
function handleAddAccount() {
	handleDialogClose({ value: [vuexStore.state.userInfo] });
}
</script>

<style lang="less" scoped>
@import url('./style.less');
</style>
