<template>
	<div class="comparison_box">
		<div class="container_box">
			<div class="table_main">
				<div class="table_">
					<div class="top_boxFirst">
						<div class="tag_boxTitle">对比资产一</div>
						<div class="tag_boxCenter">
							<div class="tag_boxRight" v-if="tableDatao[0]" @click="clearDate(tableDatao[0], 1)">× 清空</div>
						</div>
					</div>

					<div class="table_1" v-if="tableDatao[0]">
						<el-table border :data="tableDatao" height="100px" style="width: 100%">
							<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
							<el-table-column prop="buildingType" label="资产类型" width="" />
							<el-table-column prop="street" label="地址" show-overflow-tooltip>
								<template #default="scope">
									{{
										scope.row?.city && scope.row?.district
											? scope.row?.city + scope.row?.district + scope.row?.street
											: scope.row?.buildingCity + scope.row?.buildingDistrict + scope.row?.buildingStreet
									}}
								</template>
							</el-table-column>

							<el-table-column prop="buildingSize" label="建筑面积" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
									</div>
								</template>
							</el-table-column>
							<el-table-column prop="maintenance" label="维护情况" width="" />
							<el-table-column prop="absoluteValue" label="单价" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="add active" @click="choose(1)" v-else>+ 选择对比资产</div>
				</div>
				<div class="table_">
					<div class="top_box">
						<div class="top_boxFirst">
							<div class="tag_boxTitle">对比资产二</div>
							<div class="tag_boxCenter">
								<div class="tag_boxRight" v-if="tableDatat[0]" @click="clearDate(tableDatat[0], 2)">× 清空</div>
							</div>
						</div>
						<div class="table_1" v-if="tableDatat[0]">
							<el-table :data="tableDatat" border height="100px" style="width: 100%">
								<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
								<el-table-column prop="buildingType" label="资产类型" width="" />
								<el-table-column prop="street" label="地址" show-overflow-tooltip>
									<template #default="scope">
										{{
											scope.row?.city && scope.row?.district
												? scope.row?.city + scope.row?.district + scope.row?.street
												: scope.row?.buildingCity + scope.row?.buildingDistrict + scope.row?.buildingStreet
										}}
									</template>
								</el-table-column>
								<el-table-column prop="buildingSize" label="建筑面积" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="maintenance" label="维护情况" width="" />
								<el-table-column prop="absoluteValue" label="单价" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="add active" @click="choose(2)" v-else>+ 选择对比资产</div>
					</div>
				</div>
			</div>

			<div class="flex_box" v-if="sixRingDateList.length > 0">
				<div class="line_box">
					<div v-if="handlerLineBox(1).length > 0">
						{{ handlerLineBox(1) }}
					</div>
					<div v-if="handlerLineBox(1).length > 0" class="box_copy" @click="handlerCopy(1)">复制</div>
				</div>
				<div class="line_box">
					<div v-if="handlerLineBox(2).length > 0">
						{{ handlerLineBox(2) }}
					</div>
					<div v-if="handlerLineBox(2).length > 0" class="box_copy" @click="handlerCopy(2)">复制</div>
				</div>
			</div>
			<!-- 对比图 -->
			<div class="echars_box" :style="handlerBtnBox().length > 0 ? 'height: 515px' : 'height: 660px'">
				<div class="tag_box">
					价值对比 <span v-if="tableDatao.length > 0">{{ tableDatao.length > 0 ? tableDatao[0]?.buildingName : '' }}</span
					><span v-if="tableDatat.length > 0">{{ tableDatat.length > 0 ? tableDatat[0]?.buildingName : '' }}</span>
				</div>
				<div class="echars_main">
					<div class="box_">
						<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
							<el-icon><Download /></el-icon>
						</div>
						<div style="width: 100%">
							<div class="title1">{{ valueDateList.length > 1 ? '绝对价值' : '绝对价值' }}</div>
							<Valuecomparison :valueDateList="valueDateList"></Valuecomparison>
						</div>
					</div>

					<div class="box_">
						<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
							<el-icon><Download /></el-icon>
						</div>
						<div style="width: 100%">
							<div class="title1">维度分析</div>
							<echartBox :sixRingDateList="sixRingDateList" ref="echartBox_ref"></echartBox>
						</div>
					</div>
				</div>
			</div>

			<div class="btn_box" v-if="handlerBtnBox().length > 0">
				{{ handlerBtnBox() }}
				<div class="box_copy" @click="handlerCopy(3)">复制</div>
			</div>
		</div>
	</div>
	<!-- 对话框 -->
	<el-dialog v-model="dialogTableVisible" width="800" title="选择对比资产" :close-on-click-modal="false">
		<div class="title_box">
			<div class="tab" v-for="(item, index) in multipleSelection" :key="index">
				<div style="text-wrap: nowrap">对比资产</div>
				{{ $utils.chineseNumber(index) }}：
				<div :title="item.buildingName" style="text-wrap: nowrap; width: 116px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
					{{ item.buildingName }}
				</div>
				<div class="det" @click="clearDate(item, 0, index)">×</div>
			</div>
		</div>
		<div class="search_box">
			<div class="box_1">
				<div class="label">城市</div>
				<el-cascader
					placeholder="请选择城市"
					:options="$vuexStore.state.cityArray"
					v-model="selectedCity"
					@change="handleChange"
					:props="{ value: 'label' }"
				>
				</el-cascader>
			</div>
			<div class="box_1">
				<div class="label">资产评级</div>
				<el-select v-model="rateValue" placeholder="全部资产评级">
					<el-option v-for="(item, value) in rate" :key="value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">资产类型</div>
				<el-select v-model="buildingTypesValue" placeholder="资产类型">
					<el-option v-for="item in buildingTypes" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">关键词</div>
				<el-input v-model="essential" placeholder="请输入关键字"></el-input>
			</div>
			<div class="box_2">
				<el-button type="primary" @click="Compareds()">查询</el-button>
				<el-button type="primary" @click="reset()">重置</el-button>
			</div>
		</div>
		<div class="table_2">
			<el-table
				:data="tableData"
				style="width: 100%"
				height="308px"
				border
				ref="multipleTableRef"
				@selection-change="(selection) => handleSelectionChange(selection)"
				stripe
			>
				<el-table-column type="selection" width="55" />
				<el-table-column
					v-for="(column, index) in tableColumns"
					:key="index"
					:label="column.label"
					:prop="column.prop"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table>
		</div>

		<el-pagination
			@current-change="handleCurrentChange"
			:current-page="currentPage"
			small
			background
			layout="prev, pager, next"
			class="mt-4"
			:total="total"
		/>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogTableVisible = false">取消</el-button>
				<el-button type="primary" @click="save()"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>

	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import { getBuildingListByMultiCondition, getComparativeValue, getDictList } from '@/api/syt.js';
import Valuecomparison from './Valuecomparison.vue';
import getReport from '../../../../component/getReport/index.vue';
// import sixRing from './sixRing.vue'
import echartBox from './echarts_box.vue';
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const getReportRef = ref();
const loading = ref();
const dialogTableVisible = ref(false); //对话框显示
const rateValue = ref('');
const buildingTypesValue = ref('');
const rate = ref([]);
const buildingTypes = ref([]);
const province = ref('');
const city = ref('');
const essential = ref('');
const currentPage = ref(1);
const total = ref(0);

const box_copyObj = ref('');

const box_copyObjTwo = ref('');

const multipleTableRef = ref(null);
const tableData = ref([]);
const multipleSelection = ref([]);
const echartBox_ref = ref(null);
const selectedCity = ref([]);
const tableColumns = [
	{
		label: '资产名称',
		prop: 'buildingName',
		width: '300',
	},
	{
		label: '资产类型',
		prop: 'buildingType',
		showOverflowTooltip: true,
	},
	{
		label: '地址',
		prop: 'street',
		width: '300',
	},
];
const tableData2 = ref([]);
const tableDatao = ref([]);
const tableDatat = ref([]);
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id

const reset = () => {
	selectedCity.value = [];
	province.value = '';
	rateValue.value = '';
	city.value = '';
	buildingTypesValue.value = '';
	essential.value = '';
	currentPage.value = 1;
	Compared();
};

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
	Compared();
});

//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}
//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getComparativeValue({ buildingIds: obj.ids }).then((res) => {
		valueDateList.value = [];
		sixRingDateList.value = [];
		tableDatao.value = [];
		tableDatat.value = [];
		if (res.code === 200) {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			valueDateList.value = res.data;
			sixRingDateList.value = res.data;
			echartBox_ref.value.echartsData(sixRingDateList.value);
			multipleSelection.value = obj.arr;
			tableDatao.value.push(obj.arr[0]);
			tableDatat.value.push(obj.arr?.[1]);
			loading.value.close();
		}
	});
}

const handleSelectionChange = (val) => {
	setTimeout(() => {
		let mergedSet = new Set([...multipleSelection.value, ...val]);
		let mergedArray = Array.from(mergedSet);
		let uniqueById = Array.from(new Set(mergedArray.map((item) => item.id))).map((id) => {
			return mergedArray.find((item) => item.id === id);
		});
		// 当前页 tableData.value
		// 当前页选中 val
		// 当前页选中和之前选中的重复的去掉的 uniqueById
		tableData.value.map((item) => {
			uniqueById.map((uniqueItem, index) => {
				if (item.id == uniqueItem.id) {
					const foundInVal = val.some((v) => v.id === uniqueItem.id);
					if (!foundInVal) {
						uniqueById.splice(index, 1);
					}
				}
			});
		});
		multipleSelection.value = uniqueById;
		console.log(multipleSelection.value, 'multipleSelection.value');
	}, 100);
};

// 点击选择对比资产
const choose = (item) => {
	dialogTableVisible.value = true;
};

// 复制
function handlerCopy(type) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(type === 1 ? box_copyObj.value : type === 2 ? box_copyObjTwo.value : handlerBtnBox())
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = type === 1 ? box_copyObj.value : type === 2 ? box_copyObjTwo.value : handlerBtnBox();
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

function handlerBtnBox() {
	let obj = sixRingDateList.value[1];
	let obj1 = sixRingDateList.value[0];
	if (!obj || !obj1) return '';
	if (obj.absoluteValue > obj1.absoluteValue) {
		let name = null;
		let lastName = null;

		const ratio = obj.absoluteValue / obj1.absoluteValue; // 倍数
		name = obj.buildingName + '的单价是' + obj1.buildingName + '的' + formattedMoney(handleNumber(ratio)) + '倍，';

		if (obj.assorted > obj1.assorted) {
			lastName = obj.buildingName + '的周边配套、';
		}
		if (obj.maintenance > obj1.maintenance) {
			if (!lastName) {
				lastName = obj.buildingName + '的周边配套、';
			}
			lastName += '维护情况、';
		}
		if (obj.evaluation > obj1.evaluation) {
			if (!lastName) {
				lastName = obj.buildingName + '的周边配套、';
			}
			lastName += '评估结果、';
		}
		if (obj.businessDynamism > obj1.businessDynamism) {
			if (!lastName) {
				lastName = obj.buildingName + '的周边配套、';
			}
			lastName += '商业活力、';
		}
		if (obj.spendingPower > obj1.spendingPower) {
			if (!lastName) {
				lastName = obj.buildingName + '的周边配套、';
			}
			lastName += '人均消费能力、';
		}
		if (obj.regionalPotential > obj1.regionalPotential) {
			if (!lastName) {
				lastName = obj.buildingName + '的周边配套、';
			}
			lastName += '区域潜力';
		}
		if (lastName) {
			lastName += '强于' + obj1.buildingName + '。';
			return name + lastName;
		}
	} else {
		let name = null;
		let lastName = null;

		const ratio = obj1.absoluteValue / obj.absoluteValue; // 倍数
		name = obj1.buildingName + '的单价是' + obj.buildingName + '的' + +formattedMoney(handleNumber(ratio)) + '倍，';

		if (obj1.assorted > obj.assorted) {
			lastName = obj1.buildingName + '的周边配套';
		}
		if (obj1.maintenance > obj.maintenance) {
			if (!lastName) {
				lastName = obj1.buildingName + '的周边配套';
			}
			lastName += '维护情况、';
		}
		if (obj1.evaluation > obj.evaluation) {
			if (!lastName) {
				lastName = obj1.buildingName + '的周边配套';
			}
			lastName += '评估结果、';
		}
		if (obj1.businessDynamism > obj.businessDynamism) {
			if (!lastName) {
				lastName = obj1.buildingName + '的周边配套';
			}
			lastName += '商业活力、';
		}
		if (obj1.spendingPower > obj.spendingPower) {
			if (!lastName) {
				lastName = obj1.buildingName + '的周边配套';
			}
			lastName += '人均消费能力、';
		}
		if (obj1.regionalPotential > obj.regionalPotential) {
			if (!lastName) {
				lastName = obj1.buildingName + '的周边配套';
			}
			lastName += '区域潜力';
		}
		if (lastName) {
			lastName += '强于' + obj.buildingName + '。';
			return name + lastName;
		}
	}
}

// 比较
function handlerLineBox(type) {
	if (type === 1) {
		let obj = null;
		if (tableDatao.value.length > 0) {
			if (tableDatao.value?.[0]?.buildingName === sixRingDateList.value[0].buildingName) {
				obj = sixRingDateList.value[0];
			} else {
				obj = sixRingDateList?.value?.[1];
			}
		}

		if (!obj) return '';

		let name =
			obj.buildingName +
			'的单价为' +
			formattedMoney(handleNumber(obj.absoluteValue)) +
			'元/㎡' +
			'，周边配套为' +
			formattedMoney(handleNumber(obj.assorted)) +
			'，维护情况为' +
			formattedMoney(handleNumber(obj.maintenance)) +
			'，评估结果为' +
			formattedMoney(handleNumber(obj.evaluation)) +
			'，商业活力为' +
			formattedMoney(handleNumber(obj.businessDynamism)) +
			'，人均消费能力为' +
			formattedMoney(handleNumber(obj.spendingPower)) +
			'，区域潜力为' +
			formattedMoney(handleNumber(obj.regionalPotential)) +
			'。';
		if (name) {
			box_copyObj.value = name;
		}
		return name;
	} else {
		let obj1 = null;
		if (tableDatat.value.length > 0) {
			if (tableDatat.value?.[0]?.buildingName === sixRingDateList.value?.[0]?.buildingName) {
				obj1 = sixRingDateList.value[0];
			} else {
				obj1 = sixRingDateList?.value?.[1] ? sixRingDateList.value[1] : undefined;
			}
		}
		if (!obj1) return '';
		let name =
			obj1.buildingName +
			'的单价为' +
			formattedMoney(handleNumber(obj1.absoluteValue)) +
			'元/㎡' +
			'，周边配套为' +
			formattedMoney(handleNumber(obj1.assorted)) +
			'，维护情况为' +
			formattedMoney(handleNumber(obj1.maintenance)) +
			'，评估结果为' +
			formattedMoney(handleNumber(obj1.evaluation)) +
			'，商业活力为' +
			formattedMoney(handleNumber(obj1.businessDynamism)) +
			'，人均消费能力为' +
			formattedMoney(handleNumber(obj1.spendingPower)) +
			'，区域潜力为' +
			formattedMoney(handleNumber(obj1.regionalPotential)) +
			'。';

		if (name) {
			box_copyObjTwo.value = name;
		}
		return name;
	}
}
// 修改城市
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const queryParams = computed(() => {
	return {
		city: province.value,
		buildingRate: rateValue.value,
		district: city.value,
		buildingType: buildingTypesValue.value,
		keyword: essential.value,
		currentPage: currentPage.value,
		// year: 2024,
		pageSize: 10,
	};
});

//对比资产分页查询
const handleCurrentChange = (val) => {
	currentPage.value = val;
	Compared();
};

function Compareds() {
	currentPage.value = 1;
	Compared();
}

// 查询
const Compared = async () => {
	await getBuildingListByMultiCondition(queryParams.value)
		.then((res) => {
			tableData.value = [];
			tableData.value = res.data.rows;
			nextTick(() => {
				tableData.value.map((v) => {
					multipleSelection.value.map((i) => {
						if (v.id == i.id) {
							multipleTableRef.value.toggleRowSelection(v, true);
						}
					});
				});
			});
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const toggleSelection = (rows, isSelect) => {
	if (rows) {
		rows.forEach((row) => {
			multipleTableRef.value.toggleRowSelection(row, undefined, isSelect);
		});
	} else {
		multipleTableRef.value.clearSelection();
	}
};

// 清空
function handelClear(row) {
	multipleSelection.value.forEach((item, indexs) => {
		if (item.id == row.id) {
			multipleSelection.value.splice(indexs, 1);
		}
	});

	save();
}

//清空资产选项
const clearDate = (row, type, index) => {
	if (type === 1) {
		//资产一清空
		tableDatao.value = [];
		handelClear(row);
	} else if (type === 2) {
		//资产二清空
		tableDatat.value = [];
		handelClear(row);
	} else {
		// 弹窗内资产清空
		multipleSelection.value.splice(index, 1);
	}
	if (tableData.value?.length > 0) {
		// 删除table选中的数据后，清空table选中的数据
		tableData.value.forEach((item) => {
			if (item.id == row.id) {
				toggleSelection([row]);
			}
		});
	}
	if (sixRingDateList.value.length > 0) {
		sixRingDateList.value.map((item, inde) => {
			if (item.buildingName == row.buildingName) {
				sixRingDateList.value.splice(inde, 1);
				echartBox_ref.value.echartsData(sixRingDateList.value);
			}
		});
	}
};
// 确定
const valueDateList = ref([]);
const sixRingDateList = ref([]);

const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		ElMessage({
			message: multipleSelection.value.length == 0 ? '至少选一个商圈' : '最多选择两个商圈',
			type: 'error',
		});
	} else {
		let ids = multipleSelection.value.map((item) => item.id).join(',');
		getComparativeValue({ buildingIds: ids }).then((res) => {
			valueDateList.value = [];
			sixRingDateList.value = [];
			tableDatao.value = [];
			tableDatat.value = [];
			if (res.code === 200) {
				emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
				valueDateList.value = res.data;
				console.log(res.data, 'res.data');
				sixRingDateList.value = res.data;
				echartBox_ref.value.echartsData(sixRingDateList.value);

				tableData2.value = multipleSelection.value;
				// 两个数组 一个数组里面放一个
				res.data.forEach((item, index) => {
					tableData2.value.forEach((element) => {
						if (item.buildingName === element.buildingName && index === 0) {
							tableDatao.value.push(element);
						}
						if (item.buildingName === element.buildingName && index === 1) {
							tableDatat.value.push(element);
						}
					});
				});
				dialogTableVisible.value = false;
			}
			dialogTableVisible.value = false;
		});
	}
};

// 获取字典
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();
</script>
<style scoped lang="less">
.comparison_box {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;

		.table_main {
			width: 100%;
			height: 162px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.table_ {
				width: calc(50% - 8px);
				height: 162px;
				border-radius: 6px;
				background-color: rgba(255, 255, 255, 1);
				position: relative;

				.tag_box {
					width: auto;
					height: 16px;
					position: absolute;
					left: 0;
					top: 20px;
					font-size: 14px;
					font-weight: bold;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&::before {
						content: '';
						width: 4px;
						height: 16px;
						background-color: rgba(24, 104, 241, 1);
						margin-right: 10px;
					}
				}

				.table_1 {
					width: 96%;
					position: absolute;
					bottom: 10px;
					left: 2%;
					&::v-deep .el-table--fit {
						border-radius: 8px;
					}

					&::v-deep .el-table th {
						background-color: rgba(245, 245, 245, 1);
					}
				}

				.add {
					width: 96%;
					height: 90px;
					position: absolute;
					bottom: 10px;
					left: 2%;
					border-radius: 6px;
					border: 1px solid rgba(231, 231, 231, 1);
					display: flex;
					justify-content: center;
					align-items: center;
					color: rgba(3, 93, 255, 1);
					font-size: 14px;
					font-weight: bold;
				}

				.clear {
					width: auto;
					height: 20px;
					position: absolute;
					top: 15px;
					right: 15px;
					font-size: 14px;
					color: rgba(24, 104, 241, 1);
				}
			}
		}
		.echars_box {
			width: 100%;
			height: 565px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			border-radius: 6px;
			position: relative;
			.tag_box {
				width: auto;
				height: 16px;
				position: absolute;
				left: 0;
				top: 20px;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
				span {
					margin-left: 10px;
					font-size: 12px;
					display: flex;
					line-height: normal;
					// justify-content: flex-start;
					// align-items: center;
					&:first-child {
						&::after {
							content: '';
							width: 8px;
							height: 12px;
							margin-top: 2.5px;
							margin-left: 10px;
							background-color: rgba(4, 80, 218, 1);
						}
					}
					&:nth-child(2) {
						&::after {
							content: '';
							width: 8px;
							margin-top: 2.5px;
							height: 12px;
							margin-left: 10px;
							background-color: rgba(30, 170, 117, 1);
						}
					}
				}
			}
			.echars_main {
				width: 98%;
				position: absolute;
				top: 60px;
				left: 1%;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.box_ {
					width: calc(50% - 8px);
					height: 442px;
					border: 1px solid rgba(231, 231, 231, 1);
					border-radius: 6px;
					box-sizing: border-box;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-direction: column;
					position: relative;
					.download {
						position: absolute;
						top: 10px;
						right: 15px;
						cursor: pointer;
						color: #333333;
						font-size: 20px;
						font-weight: 600;
						&:hover {
							color: #1868f1;
						}
					}
					.title1 {
						width: 100%;
						height: 44px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 12px;
						background-color: rgba(245, 245, 245, 1);
						border-bottom: 1px solid rgba(231, 231, 231, 1);
						margin-bottom: 20px;
					}
				}
			}
		}
	}
}
.title_box {
	width: 100%;
	max-height: 100px;
	overflow: scroll;
	border-top: 1px solid rgba(231, 231, 231, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	box-sizing: border-box;
	display: flex;
	flex-wrap: wrap;
	.tab {
		width: 31%;
		height: 32px;
		margin: 8px 15px 8px 0;
		background-color: rgba(245, 246, 247, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
		position: relative;
		text-wrap: nowrap;
		.det {
			width: 10px;
			height: 10px;
			position: absolute;
			right: 10px;
			font-size: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: rgba(201, 205, 212, 1);
			cursor: pointer;
		}
	}
}
.search_box {
	width: 100%;
	height: auto;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
	.box_1 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		border: 1px solid rgba(231, 231, 231, 1);
		box-sizing: border-box;

		::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
			box-shadow: 0;
		}

		.label {
			width: 50%;
			height: 100%;
			font-size: 14px;
			color: rgba(134, 144, 156, 1);
			background-color: rgba(245, 246, 247, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.box_2 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		box-sizing: border-box;
	}
}
.table_2 {
	width: 100%;
	height: 308px;
	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}
.btn_box {
	width: calc(100% - 20px);
	display: flex;
	justify-content: flex-start;
	align-items: center;
	border-radius: 6px;
	background-color: #ffffff;
	margin-top: 10px;
	font-size: 14px;
	color: #555555;
	line-height: 20px;
	position: relative;
	padding: 10px 10px 15px 10px;
	.box_copy {
		position: absolute;
		right: 12px;
		bottom: 7px;
		cursor: pointer;
		color: #1868f1;
	}
}
.flex_box {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: space-between;
	margin-top: 10px;
	.line_box {
		font-size: 14px;
		line-height: 20px;
		padding: 10px;
		width: calc(50% - 28px);
		height: 100%;
		border-radius: 6px;
		background-color: #ffffff;
		position: relative;
		color: #555555;
		.box_copy {
			position: absolute;
			right: 12px;
			bottom: 4px;
			cursor: pointer;
			color: #1868f1;
		}
	}
}

.top_boxFirst {
	display: flex;
	justify-content: space-between;
	height: 56px;
	align-items: center;
	.tag_boxTitle {
		width: auto;
		height: 16px;
		font-size: 14px;
		font-weight: bold;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		&::before {
			content: '';
			width: 4px;
			height: 16px;
			background-color: #1868f1;
			margin-right: 10px;
		}
	}

	.tag_boxCenter {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		font-size: 14px;
		color: #1868f1;
		padding-right: 16px;
		> :nth-child(n) {
			cursor: pointer;
		}
		.tag_boxLeft {
			display: flex;
			align-items: center;
			margin: 0 16px 0 24px;
		}
	}
}
</style>
