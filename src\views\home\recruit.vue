<template>
	<div class="recruit_main">
		<div class="banner_box">
			<div class="title">找到合适的岗位，加入标标准</div>
			<div class="tips">我们提供多种多样的岗位，适合每一位应聘者</div>
		</div>
		<div class="job_list">
			<div class="job_box" v-for="(item,index) in 4" :key="index" @click="router.push('/recruitDetail')">
				<div class="title1">数据分析助理</div>
				<div class="time">
					<span>青岛</span>
					<span>双休</span>
					<span>五一双金</span>
					<span>带薪年假</span>
					<span>工龄工资</span>
				</div>
				<div class="tips">数据分析、经济学、金融学等相关专业，以及其他专业优秀毕业生；熟悉使用office软件，会使用SPSS、CAD等软件优先，认真细致、注重团队协作。</div>
			</div>
		</div>
		
	</div>

</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue';
	import {
		useRouter
	} from 'vue-router';
	import http from '@/utils/http';
	const router = useRouter();
	const recruitData = ref([])
	const items = ref(1)
	onMounted(() => {
		console.log('招聘');
		// getRecruitData()
	})
	const getRecruitData = async () => {
		const res = await http.get('gis/a_threeename.do?act=getRecruitInfo')
		// recruitData.value = res.data
		console.log(1, res);
		recruitData.value = res.body
	}
	const onitem = (id) => {
		console.log(id);
		items.value = id
	}
	// 点击下一个
	const next = () => {
		console.log('下一个');
		if (items.value < 4) {
			items.value++
		}
	}
	// 点击上一个
	const last = () => {
		console.log('上一个');
		if (items.value > 1) {
			items.value--
		}
	}
</script>

<style lang="less" scoped>
	.recruit_main{
		width: 100%;
		height: 100%;
		background-color: rgba(245, 245, 245, 1);
		.banner_box{
			width: 100%;
			height: 300px;
			background-image: url('../../assets/images/home/<USER>');
			background-size: 100% 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
			.title{
				font-size: 48px;
				font-weight: bold;
				color: rgba(29, 33, 41, 1);
				margin-bottom: 50px;
			}
			.tips{
				font-size: 24px;
				font-weight: bold;
				color: rgba(29, 33, 41, 1);
			}
		}
		.job_list{
			width: 60%;
			height: 100%;
			max-width: 1440px;
			margin: 0 auto;
			.job_box{
				width: 100%;
				height: 188px;
				padding: 30px 50px;
				border-radius: 2px;
				cursor: pointer;
				box-sizing: border-box;
				&:hover{
					background-color: rgba(255, 255, 255, 1);
				}
				.title1{
					font-size: 30px;
					margin-bottom: 15px;
				}
				.time{
					font-size: 20px;
					margin-bottom: 15px;
					span{
						margin-right: 20px;
					}
				}
				.tips{
					font-size: 20px;
					color: rgba(134, 144, 156, 1);
				}
			}
		}
	
	}
	@media screen and (max-width:1024px) {
		.recruit_main{
			.banner_box{
				width: 100%;
				height: 100%;
				padding: 15px;
				box-sizing: border-box;
			}
			.job_list{
				width: 100%;
				height: 100%;
				max-width: 375px;
				padding: 15px;
				box-sizing: border-box;
				.job_box{
					width: 100%;
					height: auto;
					padding: 15px;
					box-sizing: border-box;
				}
			}
		}
	}
</style>