import { fetch } from './http';

export function getEquityTerm(params) {
	//权益期限
	return fetch({
		url: '/api/user-specs/list',
		method: 'get',
		params: params,
	});
}

export function getSgtEquityTerm(params) {
	//商估通权益期限
	return fetch({
		url: '/api/sgt-user-specs/list',
		method: 'get',
		params: params,
	});
}

export function getSgtCouponPage(params) {
	//我的商估通权益卡券
	return fetch({
		url: '/api/sgt-user-coupon/page',
		method: 'get',
		params: params,
	});
}

export function getUserPage(params) {
	//用户列表
	return fetch({
		url: '/api/user/page',
		method: 'get',
		params: params,
	});
}

export function getCommonCity(params) {
	//权益城市
	return fetch({
		url: '/api/common/city',
		method: 'get',
		params: params,
	});
}

export function getUsersCouponPage(params) {
	//卡片权益
	return fetch({
		url: '/api/user-coupon/page',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function getSetUser(data) {
	//添加使用人
	return fetch({
		url: '/api/user-coupon-record/set-user',
		method: 'post',
		data: data,
		loading: true,
	});
}

export function getUserCoupon(params) {
	//已添加使用人
	return fetch({
		url: '/api/user-coupon-record/active/' + params.id,
		method: 'get',
	});
}

export function getUserCouponCctive(params) {
	//使用人记录
	return fetch({
		url: '/api/user-coupon-record/page/' + params.id,
		method: 'get',
		params: params,
	});
}

export function getOrderPage(params) {
	//所有订单
	return fetch({
		url: '/api/order/page',
		method: 'get',
		params: params,
	});
}

export function getOrderDetail(params) {
	//所有订单
	return fetch({
		url: '/api/order/order-detail/' + params.outTradeNo,
		method: 'get',
	});
}

export function getCommonCouponPage(params) {
	// 福利卡卷page
	return fetch({
		url: '/api/common-coupon/page',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function useCommonCoupon(data) {
	//  使用福利卡卷
	return fetch({
		url: '/api/common-coupon/use',
		method: 'POST',
		data: data,
		loading: true,
	});
}

export function getCouponDetail(params) {
	// 根据订单获取优惠券信息
	return fetch({
		url: '/api/common-coupon/agg/' + params.outTradeNo,
		method: 'get',
	});
}

export function getReport(params) {
	// 获取报告价格
	return fetch({
		url: '/api/report/one',
		method: 'get',
		params: params,
	});
}

export function getInvoicePage(params) {
	// 发票抬头-分页列表查询
	return fetch({
		url: '/sm/v1/invoiceheader/list',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function addInvoice(data) {
	//  发票抬头-增加
	return fetch({
		url: '/sm/v1/invoiceheader/add',
		method: 'POST',
		params: data,
		loading: true,
	});
}

export function editInvoice(data) {
	//  发票抬头-编辑
	return fetch({
		url: '/sm/v1/invoiceheader/edit',
		method: 'POST',
		params: data,
		loading: true,
	});
}

export function getInvoiceById(params) {
	// 发票抬头-通过id查询
	return fetch({
		url: '/sm/v1/invoiceheader/queryById',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function deleteInvoice(data) {
	//发票抬头-删除
	return fetch({
		url: '/sm/v1/invoiceheader/delete',
		method: 'DELETE',
		params: data,
		loading: true,
	});
}

export function addInvoiceRecord(data) {
	//  开票记录-添加
	return fetch({
		url: '/sm/v1/sytinvoicerecord/add',
		method: 'POST',
		params: data,
		loading: true,
	});
}

export function getInvoiceRecord(params) {
	// 开票记录-通过订单id查询开票记录
	return fetch({
		url: '/sm/v1/sytinvoicerecord/queryByOrderId',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function reSendInvoice(data) {
	// 开票记录-发票重新发送
	return fetch({
		url: '/sm/v1/sytinvoicerecord/reSendInvoice',
		method: 'post',
		params: data,
		loading: true,
	});
}

export function getSgtCoupon(params) {
	//商估通权益卡券
	return fetch({
		url: '/api/sgt-coupon/list',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function getOrderCoupon(params) {
	//获取订单所关联的券id
	return fetch({
		url: '/api/order/coupons/' + params.orderId,
		method: 'get',
	});
}

export function activeCoupon(params) {
	// 立即使用激活权益券
	return fetch({
		url: '/api/sgt-user-coupon/active/' + params.id,
		method: 'post',
	});
}

export function continuePay(data) {
	//旧订单继续支付
	return fetch({
		url: '/api/order/pay/' + data.outTradeNo,
		method: 'PUT',
		data: data,
	});
}
