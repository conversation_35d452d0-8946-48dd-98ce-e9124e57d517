<template>
	<div v-if="tableData" style="padding: 20px">
		<el-row :gutter="20">
			<el-col :span="6">
				<el-cascader placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }"> </el-cascader>
			</el-col>
			<el-col :span="6">
				<el-select style="margin-left: 20px; width: 150px" v-model="buildingTypesValue" placeholder="全部资产">
					<el-option v-for="(item, index) in buildingTypes" :key="item" :label="item" :value="item" />
				</el-select>
			</el-col>
			<el-col :span="6">
				<el-input v-model="inputValue" placeholder="关键字搜索" style="height: 38px; width: 160px; margin-left: 20px" />
			</el-col>
		</el-row>
		<div class="mian">
			<h4>租赁</h4>
			<el-table :data="Array(tableData[0])" stripe style="width: 100%">
				<el-table-column v-for="item in cloumszu" :key="item.prop" :prop="item.prop" :label="item.label" />
				<el-table-column label="操作">
					<template v-slot="{ row }">
						<el-button size="small" @click="handleButtonClick(row)" type="primary">聊天</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				layout="prev, pager, next"
				v-model:page-size="size"
				:total="50"
				v-model:current-page="currentPage1"
				@current-change="initData()"
				style="justify-content: center"
			/>
		</div>
		<div class="mian">
			<h4>买卖</h4>
			<el-table :data="Array(tableData[1])" stripe style="width: 100%">
				<el-table-column v-for="item in cloums" :key="item.prop" :prop="item.prop" :label="item.label" />
				<el-table-column label="操作">
					<template v-slot="{ row }">
						<el-button size="small" @click="handleButtonClick(row)" type="primary">聊天</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				layout="prev, pager, next"
				v-model:page-size="size"
				:total="50"
				v-model:current-page="currentPage2"
				@current-change="initData()"
				style="justify-content: center"
			/>
		</div>
		<div class="mian">
			<h4>证券</h4>
			<el-table :data="Array(tableData[2])" stripe style="width: 100%">
				<el-table-column v-for="item in cloums" :key="item.prop" :prop="item.prop" :label="item.label" />
				<el-table-column label="操作">
					<template v-slot="{ row }">
						<el-button size="small" @click="handleButtonClick(row)" type="primary">聊天</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				layout="prev, pager, next"
				v-model:page-size="size"
				:total="50"
				v-model:current-page="currentPage3"
				@current-change="initData()"
				style="justify-content: center"
			/>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http';
import { useRouter } from 'vue-router';
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '数据中心', '保障房', '农贸市场'];
const buildingTypesValue = ref('');

const handleButtonClick = (id) => {
	router.push('/main/chatrat/becd573c7c45403db451b1b303d309b0');
};
const router = useRouter();
const tableData = ref([]);
const currentPage1 = ref(1);
const currentPage2 = ref(1);
const currentPage3 = ref(1);
const size = ref(10);
const city = ref('');
const county = ref('');
const inputValue = ref('');
const handleChange = (val) => {
	city.value = val[0];
	county.value = val[1];
};
const cloums = [
	{ prop: 'buildingName', label: '建筑物名称' },
	{ prop: 'serialNumber', label: '编号' },
	{ prop: 'area', label: '地区' },
	{ prop: 'deType', label: '资产类型' },
	{ prop: 'rentalUnitType', label: '出租户型' },
	{ prop: 'rentalUnitPrice', label: '出租价格' },
	{ prop: 'tenantry', label: '承租人' },
	{ prop: 'leaser', label: '出租人' },
	{ prop: 'intermediaryAgent', label: '中介' },
	{ prop: 'instructions', label: '备注' },
];
const cloumszu = [
	{ prop: 'buildingName', label: '建筑物名称' },
	{ prop: 'deType', label: '资产类型' },
	{ prop: 'rentalUnitType', label: '出租户型' },
	{ prop: 'rentalUnitPrice', label: '出租价格' },
	{ prop: 'tenantry', label: '承租人' },
	{ prop: 'leaser', label: '出租人' },
	{ prop: 'intermediaryAgent', label: '经纪人' },
	{ prop: 'instructions', label: '备注' },
];
const initData = async () => {
	// debugger
	const res = await http.get('/api/a_new_jiaoyi.do?act=getTradingHistoryList', {
		params: {
			currentPage1: currentPage1.value,
			currentPage2: currentPage2.value,
			currentPage3: currentPage3.value,
		},
	});
	tableData.value = res.list;
};
onMounted(() => {
	initData();
});
</script>

<style lang="less" scoped>
.mian {
	text-align: center;
}
</style>
