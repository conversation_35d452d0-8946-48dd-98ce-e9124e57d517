<template>
	<div>
		<h1><strong>七、信用风险分析</strong></h1>
		<h2><strong>7.1信用风险</strong></h2>
		<table class="MsoTableGrid" style="width: 100%">
			<tbody>
				<tr>
					<td valign="center" colspan="8">资产风险分析量化表</td>
				</tr>
				<tr>
					<td valign="center" colspan="8" class="flexCenter">资产概况</td>
				</tr>
				<tr>
					<td valign="top" class>资产名称</td>
					<td valign="top" class>资产类型</td>
					<td valign="top" class>地址</td>
					<td valign="top" class>建筑面积（㎡）</td>
					<td valign="center" class>建成年份</td>
					<td valign="center" class>区域潜力</td>
					<td valign="center" class>商业活力</td>
					<td valign="center" class>人均消费</td>
				</tr>
				<tr>
					<td valign="top" class>{{ dataList?.creditRiskAssetVoList?.[0]?.name }}</td>
					<td valign="top" class>{{ dataList?.creditRiskAssetVoList?.[0]?.assetType }}</td>
					<td valign="top" class>{{ dataList?.creditRiskAssetVoList?.[0]?.address }}</td>
					<td valign="top" class>{{ dataList?.creditRiskAssetVoList?.[0]?.buildingSize }}</td>
					<td valign="center" class>{{ dataList?.creditRiskAssetVoList?.[0]?.buildYear }}</td>
					<td valign="center" class>{{ dataList?.creditRiskAssetVoList?.[0]?.regionalPotential }}</td>
					<td valign="center" class>{{ dataList?.creditRiskAssetVoList?.[0]?.businessDynamism }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(dataList?.creditRiskAssetVoList?.[0]?.spendingPower) }}</td>
				</tr>
				<tr>
					<td valign="top" colspan="8" class="flexCenter">权益指数</td>
				</tr>
				<tr>
					<td valign="top" class>市场法价格 （万元）</td>
					<td valign="top" class>市场法价格系数</td>
					<td valign="top" class>收益法价格 (万元)</td>
					<td valign="top" class>收益法价格系数</td>
					<td valign="top" class>评估值 （亿元）</td>
					<td valign="top" class>价格 （万元/㎡）</td>
					<td valign="top" class>折旧 （万元）</td>
					<td valign="top" class>EBITDA（万元）</td>
				</tr>
				<tr>
					<td valign="top" class>{{ dataList?.marketPriceIndex }}</td>
					<td valign="top" class>70%</td>
					<td valign="top" class>{{ dataList?.incomePriceIndex }}</td>
					<td valign="top" class>30%</td>
					<td valign="top" class>{{ dataList?.presentValueOfAsset }}</td>
					<td valign="top" class>{{ dataList?.salePrice }}</td>
					<td valign="top" class>{{ $utils.formattedMoney(dataList?.depreciation) }}</td>
					<td valign="top" class>{{ $utils.formattedMoney(dataList?.ebitda) }}</td>
				</tr>
				<tr>
					<td valign="top" colspan="8" class="flexCenter">资产测算</td>
				</tr>
				<tr>
					<td valign="top" class>资本化率</td>
					<td valign="top" class>抵押率（LTV）</td>
					<td valign="top" class>违约率（租户）</td>
					<td valign="top" class>利息覆盖倍数ICR</td>
					<td valign="top" class>收入/租金增长率</td>
					<td valign="top" class>租售价格比</td>
					<td valign="top" class>运营风险</td>
					<td valign="top" class>资产现值（亿元）</td>
				</tr>
				<tr>
					<td valign="top" class>{{ dataList?.capitalizationRate }}%</td>
					<td valign="top" class>{{ dataList?.ltv }}%</td>
					<td valign="center" class>{{ dataList?.defaultRate }}%</td>
					<td valign="top" class>{{ dataList?.icr }}</td>
					<td valign="top" class>{{ dataList?.incomeGrowthRate }}%</td>
					<td valign="top" class>{{ dataList?.rentSaleRatio }}%</td>
					<td valign="top" class>{{ handleoperations(dataList?.operationalRisk) }}</td>
					<td valign="top" class>{{ dataList?.presentValueOfAsset }}</td>
				</tr>
				<tr>
					<td valign="top" class>交易成本（万元）</td>
					<td valign="top" class>税（包含契税及印花税） （万元）</td>
					<td valign="top" class>费 （万元）</td>
					<td valign="top" colspan="5" class="flexCenter">总投资额 （亿元）</td>
				</tr>
				<tr>
					<td valign="top" class>{{ $utils.formattedMoney(dataList?.transactionCost) }}</td>
					<td valign="top" class>{{ $utils.formattedMoney(dataList?.tax) }}</td>
					<td valign="top" class>{{ $utils.formattedMoney(dataList?.fee) }}</td>
					<td valign="top" colspan="5" class="flexCenter">{{ dataList?.investmentAmount }}</td>
				</tr>
			</tbody>
		</table>

		<h2><strong>7.2现金流测算表</strong></h2>
		<table class="MsoTableGrid" style>
			<tbody>
				<tr>
					<td valign="top" colspan="6" class>现金流测算表</td>
				</tr>
				<tr>
					<td valign="top" class>总投资额（亿元）</td>
					<td valign="top" class>融资比例</td>
					<td valign="top" class>融资额度 （亿元）</td>
					<td valign="top" class>融资利率</td>
					<td valign="top" class>假设租金增长率</td>
					<td valign="top" class>假设支出变化</td>
				</tr>
				<tr>
					<td valign="top" class>{{ dataList?.investmentAmount }}</td>
					<td valign="top" class>100%</td>
					<td valign="top" class>{{ dataList?.investmentAmount }}</td>
					<td valign="top" class>0%</td>
					<td valign="top" class>0%</td>
					<td valign="top" class>0%</td>
				</tr>
				<tr>
					<td valign="top" class>年份</td>
					<td valign="top" class>收入合计(万)</td>
					<td valign="top" class>物业及运营管理支出(万)</td>
					<td valign="top" class>税金合计 (万)</td>
					<td valign="top" class>借款成本 (万)</td>
					<td valign="top" class>EBITDA</td>
				</tr>

				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.cashFlows" :key="index">
					<td valign="center" class>{{ item.year }}</td>
					<td valign="center" class>{{ $utils.formattedMoney((item.totalRevenue * 10000).toFixed(2)) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney((item.mangeExpense * 10000).toFixed(2)) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney((item.totalTax * 10000).toFixed(2)) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney((item.borrowCost * 10000).toFixed(2)) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney((item.ebitda * 10000).toFixed(2)) }}</td>
				</tr>
			</tbody>
		</table>

		<h2><strong>7.3证券化</strong></h2>
		<p><strong>1.有担保</strong></p>
		<table class="MsoTableGrid" style>
			<tbody>
				<tr>
					<td valign="center" colspan="6" class>AAA主体只为优先A3提供担保</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>EBITDA</td>
					<td valign="center" colspan="2">{{ dataList?.creditRiskInvestVo?.securitization?.secured?.assume?.ebitda.toFixed(2) }}亿</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>评估值</td>
					<td valign="center" colspan="2">{{ dataList?.creditRiskInvestVo?.securitization?.secured?.assume?.evaluateValue.toFixed(2) }}亿</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>评级公司预估，若A3担保主体为AAA评级，则利率约为</td>
					<td valign="center" colspan="2">{{ (dataList?.creditRiskInvestVo?.securitization?.secured?.assume?.securedRate * 100).toFixed(2) }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>担保公司表示，担保费率约为发行规模的</td>
					<td valign="center" colspan="2">{{ (dataList?.creditRiskInvestVo?.securitization?.secured?.assume?.securedRatio * 100).toFixed(2) }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>托管费率</td>
					<td valign="center" colspan="2">
						{{ (dataList?.creditRiskInvestVo?.securitization?.secured?.assume?.custodianFeeRate * 100).toFixed(2) }}%
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>基金管理费率</td>
					<td valign="center" colspan="2">{{ (dataList?.creditRiskInvestVo?.securitization?.secured?.assume?.fundFeeRate * 100).toFixed(2) }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>承销费</td>
					<td valign="center" colspan="2">
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.secured?.assume?.underwritingFee.toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>计划管理费</td>
					<td valign="center" colspan="2">
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.secured?.assume?.planFee.toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>劣后级为过桥资金，需要从净现金流中扣除</td>
				</tr>
				<tr>
					<td valign="center" class>分层</td>
					<td valign="center" class>评级</td>
					<td valign="center" class>规模（亿）</td>
					<td valign="center" class>利率</td>
					<td valign="center" class>覆盖倍数</td>
					<td valign="center" class>抵押率</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.securitization?.secured?.types" :key="index">
					<td valign="center" class>{{ item.stratification }}</td>
					<td valign="center" class>{{ item.level }}</td>
					<td valign="center" class>{{ item.scale.toFixed(2) }}</td>
					<td valign="center" class>{{ (item.interestRate * 100).toFixed(2) }}%</td>
					<td valign="center" class>{{ item.coverageRatio ? item.coverageRatio.toFixed(2) : '' }}</td>
					<td valign="center" class>{{ (item.loanToValueRatio * 100).toFixed(2) }}%</td>
				</tr>

				<tr>
					<td
						valign="center"
						colspan="2"
						class="flexCenter"
						v-for="(item, index) in dataList?.creditRiskInvestVo?.securitization?.secured?.netCashFlows"
						:key="index"
					>
						{{ item.title }}
					</td>
				</tr>
				<tr v-for="(item, index) in netCashFlowsList" :key="index">
					<td valign="center" class>{{ item.name }}</td>
					<td valign="center" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.secured?.netCashFlows?.[0]?.[item.key].toFixed(2)) }}万元
					</td>
					<td valign="center" class>{{ item.name }}</td>
					<td valign="center" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.secured?.netCashFlows?.[1]?.[item.key].toFixed(2)) }}万元
					</td>
					<td valign="center" class>{{ item.name }}</td>
					<td valign="center" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.secured?.netCashFlows?.[2]?.[item.key].toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="10" class="flexCenter">综合成本率</td>
				</tr>
			</tbody>
		</table>

		<table class="MsoTableGrid" style="border-top: 0px">
			<tbody>
				<tr>
					<td valign="center" class>分层</td>
					<td valign="center" class>评级</td>
					<td valign="center" class>规模（亿）</td>
					<td valign="center" class>利率（%）</td>
					<td valign="center" class>利息（万元）</td>
					<td valign="center" class>担保费（万元）</td>
					<td valign="center" class>合计（万元）</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.securitization?.secured?.types.slice(0, 3)" :key="index">
					<td valign="center" class>{{ item.stratification }}</td>
					<td valign="center" class>{{ item.level }}</td>
					<td valign="center" class>{{ item.scale.toFixed(2) }}</td>
					<td valign="center" class>{{ (item.interestRate * 100).toFixed(2) }}</td>
					<td valign="center" class>{{ item.interest ? $utils.formattedMoney(item.interest.toFixed(2)) : '' }}</td>
					<td valign="center" class>{{ item.guaranteeFee ? $utils.formattedMoney(item.guaranteeFee.toFixed(2)) : '' }}</td>
					<td valign="center" class>{{ item.total ? $utils.formattedMoney(item.total.toFixed(2)) : '' }}</td>
				</tr>
				<tr>
					<td valign="top" colspan="10" class="flexCenter">综合成本率测算</td>
				</tr>
				<tr>
					<td valign="center" colspan="5" class>综合成本</td>
					<td valign="center" colspan="5" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.secured?.totalCost.toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="5" class>发行规模</td>
					<td valign="center" colspan="5" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.secured?.issuanceSize.toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="5" class>综合成本率</td>
					<td valign="center" colspan="5" class>{{ (dataList?.creditRiskInvestVo?.securitization?.secured?.totalCostRate * 100).toFixed(2) }}%</td>
				</tr>
			</tbody>
		</table>
		<p><strong>2.无担保</strong></p>
		<table class="MsoTableGrid" style>
			<tbody>
				<tr>
					<td valign="top" colspan="7" class>EBITDA</td>
					<td valign="center" colspan="3" class>{{ dataList?.creditRiskInvestVo?.securitization?.unsecured?.assume?.ebitda.toFixed(2) }}亿</td>
				</tr>
				<tr>
					<td valign="center" colspan="7" class>评估值</td>
					<td valign="center" colspan="3" class>{{ dataList?.creditRiskInvestVo?.securitization?.unsecured?.assume?.evaluateValue.toFixed(2) }}亿</td>
				</tr>

				<tr>
					<td valign="center" colspan="7" class>评级公司预估，若A3担保主体为AAA评级，则利率约为</td>
					<td valign="center" colspan="3">{{ (dataList?.creditRiskInvestVo?.securitization?.unsecured?.assume?.securedRate * 100).toFixed(2) }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="7" class>担保公司表示，担保费率约为发行规模的</td>
					<td valign="center" colspan="3">{{ (dataList?.creditRiskInvestVo?.securitization?.unsecured?.assume?.securedRatio * 100).toFixed(2) }}%</td>
				</tr>

				<tr>
					<td valign="center" colspan="7" class>托管费率</td>
					<td valign="center" colspan="3" class>
						{{ (dataList?.creditRiskInvestVo?.securitization?.unsecured?.assume?.securedRate * 100).toFixed(2) }}%
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="7" class>基金管理费率</td>
					<td valign="center" colspan="3" class>
						{{ (dataList?.creditRiskInvestVo?.securitization?.unsecured?.assume?.fundFeeRate * 100).toFixed(2) }}%
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="7" class>承销费</td>
					<td valign="center" colspan="3" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.unsecured?.assume?.underwritingFee.toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="7" class>计划管理费</td>
					<td valign="center" colspan="3" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.unsecured?.assume?.planFee.toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="10" class>劣后级为过桥资金，需要从净现金流中扣除</td>
				</tr>
				<tr>
					<td valign="center" class>分层</td>
					<td valign="center" colspan="2" class>评级</td>
					<td valign="center" colspan="2" class>规模（亿）</td>
					<td valign="center" colspan="2" class>利率</td>
					<td valign="center" colspan="2" class>覆盖倍数</td>
					<td valign="center" class>抵押率</td>
				</tr>

				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.securitization?.unsecured?.types" :key="index">
					<td valign="center" class>{{ item.stratification }}</td>
					<td valign="center" colspan="2" class>{{ item.level }}</td>
					<td valign="center" colspan="2" class>{{ item.scale.toFixed(2) }}</td>
					<td valign="center" colspan="2" class>{{ (item.interestRate * 100).toFixed(2) }}%</td>
					<td valign="center" colspan="2" class>{{ item.coverageRatio ? item.coverageRatio.toFixed(2) : '' }}</td>
					<td valign="center" class>{{ (item.loanToValueRatio * 100).toFixed(2) }}%</td>
				</tr>

				<tr>
					<td
						valign="center"
						:colspan="index === 1 ? '4' : '3'"
						class="flexCenter"
						v-for="(item, index) in dataList?.creditRiskInvestVo?.securitization?.unsecured?.netCashFlows"
						:key="index"
					>
						{{ item.title }}
					</td>
				</tr>
				<tr v-for="(item, index) in netCashFlowsList" :key="index">
					<td valign="center" class>{{ item.name }}</td>
					<td valign="center" colspan="2" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.unsecured?.netCashFlows?.[0]?.[item.key].toFixed(2)) }}万元
					</td>
					<td valign="center" colspan="2" class>{{ item.name }}</td>
					<td valign="center" colspan="2" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.unsecured?.netCashFlows?.[1]?.[item.key].toFixed(2)) }}万元
					</td>
					<td valign="center" colspan="2" class>{{ item.name }}</td>
					<td valign="center" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.unsecured?.netCashFlows?.[2]?.[item.key].toFixed(2)) }}万元
					</td>
				</tr>

				<tr>
					<td valign="center" colspan="10" class="flexCenter">综合成本率</td>
				</tr>
			</tbody>
		</table>
		<table class="MsoTableGrid" style="border-top: 0px">
			<tbody>
				<tr>
					<td valign="center" class>分层</td>
					<td valign="center" colspan="2" class>评级</td>
					<td valign="center" class>规模（亿）</td>
					<td valign="center" class>利率</td>
					<td valign="center" class>利息（万元）</td>
					<td valign="center" class>担保费（万元）</td>
					<td valign="center" class>合计（万元）</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.securitization?.unsecured?.types.slice(0, 3)" :key="index">
					<td valign="center" class>{{ item.stratification }}</td>
					<td valign="center" colspan="2" class>{{ item.level }}</td>
					<td valign="center" class>{{ item.scale.toFixed(2) }}</td>
					<td valign="center" class>{{ (item.interestRate * 100).toFixed(2) }}</td>
					<td valign="center" class>{{ item.interest ? $utils.formattedMoney(item.interest.toFixed(2)) : '' }}</td>
					<td valign="center" class>{{ item.guaranteeFee ? $utils.formattedMoney(item.guaranteeFee.toFixed(2)) : '' }}</td>
					<td valign="center" class>{{ item.total ? $utils.formattedMoney(item.total.toFixed(2)) : '' }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="10" class="flexCenter">综合成本率测算</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>综合成本</td>
					<td valign="center" colspan="4" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.unsecured?.totalCost.toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>发行规模</td>
					<td valign="center" colspan="4" class>
						{{ $utils.formattedMoney(dataList?.creditRiskInvestVo?.securitization?.unsecured?.issuanceSize.toFixed(2)) }}万元
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>综合成本率</td>
					<td valign="center" colspan="4" class>{{ (dataList?.creditRiskInvestVo?.securitization?.unsecured?.totalCostRate * 100).toFixed(2) }}%</td>
				</tr>
			</tbody>
		</table>

		<h2><strong>7.4PRE-REITS基金</strong></h2>
		<p><strong>1.有担保</strong></p>
		<table class="MsoTableGrid" style>
			<tbody>
				<tr>
					<td valign="top" colspan="3" class>一、期初募集支出</td>
				</tr>
				<tr>
					<td valign="center" class>科目</td>
					<td valign="center" class>计算（元）</td>
					<td valign="center" class>假设（%）</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.basicAssumption?.secured.slice(0, 9)" :key="index">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.calc) }}</td>
					<td valign="center" class>{{ item.assumption ? (item.assumption * 100).toFixed(2) : 0 }}</td>
				</tr>

				<tr>
					<td valign="center" colspan="3" class>二、存续期内支出</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.basicAssumption?.secured.slice(9, 25)" :key="index">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.calc) }}</td>
					<td valign="center" class>{{ item.assumption ? (item.assumption * 100).toFixed(2) : 0 }}</td>
				</tr>

				<tr>
					<td valign="center" colspan="3" class>三、退出期收支</td>
				</tr>

				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.basicAssumption?.secured.slice(25, 30)" :key="index" v-show="index !== 1">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.calc) }}</td>
					<td valign="center" class>{{ item.assumption ? item.assumption.toFixed(2) : 0 }}</td>
				</tr>

				<tr>
					<td valign="center" colspan="3" class>四、其他</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.basicAssumption?.secured.slice(30, 32)" :key="index">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.calc) }}</td>
					<td valign="center" class>{{ item.assumption ? item.assumption.toFixed(2) : 0 }}</td>
				</tr>
			</tbody>
		</table>
		<table class="MsoTableGrid" style="border-top: 0px">
			<tbody>
				<tr>
					<td valign="center" colspan="11" class>现金流测算</td>
				</tr>
				<tr>
					<td valign="center" class>科目</td>
					<td valign="center" class>（元）</td>
					<td valign="center" colspan="2" class>Y0（元）</td>
					<td valign="center" class>Y1（元）</td>
					<td valign="center" class>Y2（元）</td>
					<td valign="center" colspan="2" class>Y3（元）</td>
					<td valign="center" class>Y4（元）</td>
					<td valign="center" class>Y5（元）</td>
					<td valign="center" class>Y6（元）</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.cashFlowEstimation?.secured" :key="index">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.sum) }}</td>
					<td valign="center" colspan="2" class>{{ $utils.formattedMoney(item.y0) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y1) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y2) }}</td>
					<td valign="center" colspan="2" class>{{ $utils.formattedMoney(item.y3) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y4) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y5) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y6) }}</td>
				</tr>
			</tbody>
		</table>
		<p><strong>2.无担保</strong></p>
		<table class="MsoTableGrid" style>
			<tbody>
				<tr>
					<td valign="top" colspan="3" class>一、期初募集支出</td>
				</tr>
				<tr>
					<td valign="center" class>科目</td>
					<td valign="center" class>计算（元）</td>
					<td valign="center" class>假设（%）</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(0, 9)" :key="index">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.calc) }}</td>
					<td valign="center" class>{{ item.assumption ? (item.assumption * 100).toFixed(2) : 0 }}</td>
				</tr>

				<tr>
					<td valign="center" colspan="3" class>二、存续期内支出</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(9, 25)" :key="index">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.calc) }}</td>
					<td valign="center" class>{{ item.assumption ? (item.assumption * 100).toFixed(2) : 0 }}</td>
				</tr>

				<tr>
					<td valign="center" colspan="3" class>三、退出期收支</td>
				</tr>

				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(25, 30)" :key="index" v-show="index !== 1">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.calc) }}</td>
					<td valign="center" class>{{ item.assumption ? item.assumption.toFixed(2) : 0 }}</td>
				</tr>

				<tr>
					<td valign="center" colspan="3" class>四、其他</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(30, 32)" :key="index">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.calc) }}</td>
					<td valign="center" class>{{ item.assumption ? item.assumption.toFixed(2) : 0 }}</td>
				</tr>
			</tbody>
		</table>
		<table class="MsoTableGrid" style="border-top: 0px">
			<tbody>
				<tr>
					<td valign="center" colspan="11" class>现金流测算</td>
				</tr>
				<tr>
					<td valign="center" class>科目</td>
					<td valign="center" class>（元）</td>
					<td valign="center" colspan="2" class>Y0（元）</td>
					<td valign="center" class>Y1（元）</td>
					<td valign="center" class>Y2（元）</td>
					<td valign="center" colspan="2" class>Y3（元）</td>
					<td valign="center" class>Y4（元）</td>
					<td valign="center" class>Y5（元）</td>
					<td valign="center" class>Y6（元）</td>
				</tr>
				<tr v-for="(item, index) in dataList?.creditRiskInvestVo?.cashFlowEstimation?.unsecured" :key="index">
					<td valign="center" class>{{ item.title }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.sum) }}</td>
					<td valign="center" colspan="2" class>{{ $utils.formattedMoney(item.y0) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y1) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y2) }}</td>
					<td valign="center" colspan="2" class>{{ $utils.formattedMoney(item.y3) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y4) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y5) }}</td>
					<td valign="center" class>{{ $utils.formattedMoney(item.y6) }}</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
import { creditRisk } from '@/api/baogao';
import { onMounted } from 'vue';
const dataList = ref(null);

const netCashFlowsList = ref([
	{
		name: '发行总规模',
		key: 'totalIssuance',
	},
	{
		name: '贷款',
		key: 'loan',
	},
	{
		name: '保证金',
		key: 'margin',
	},
	{
		name: '担保费',
		key: 'guaranteeFee',
	},
	{
		name: '托管费',
		key: 'custodianFee',
	},
	{
		name: '基金管理费',
		key: 'fundFee',
	},
	{
		name: '承销费',
		key: 'underwritingFee',
	},
	{
		name: '计划管理费',
		key: 'planFee',
	},
	{
		name: '净现金流',
		key: 'netCashFlow',
	},
]);

const props = defineProps({
	id: {
		type: String,
		default: null,
		required: true,
	},
});

onMounted(async () => {
	const { data } = await creditRisk({
		ids: props.id,
		ratios: '1',
		loanRatio: '1',
		marketRatio: '0.7',
		incomeRatio: '0.3',
		interestRate: '0',
		secured: {},
		unsecured: {},
	});
	dataList.value = data;
});

function handleoperations(val) {
	if (0 < val && val <= 3) {
		return '低↓';
	} else if (3 < val && val <= 4.5) {
		return '低↑';
	} else if (4.5 < val && val <= 6) {
		return '中↓';
	} else if (6 < val && val <= 8) {
		return '中↑';
	} else if (8 < val && val <= 9) {
		return '高↓';
	} else if (9 < val && val <= 10) {
		return '高↑';
	}
}
</script>

<style scoped lang="less">
.MsoTableGrid {
	border-top: 1px solid #333;
	border-left: 1px solid #333;
	border-spacing: 0;
	background-color: #fff;
	width: 100%;
	.flexCenter {
		text-align: center;
	}
}
.MsoTableGrid td {
	border-bottom: 1px solid #333;
	border-right: 1px solid #333;
	font-size: 13px;
	padding: 5px;
}
</style>
