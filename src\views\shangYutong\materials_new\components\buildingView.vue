<template>
	<div class="cangchu_wrap">
		<template v-if="leftData && leftData.length > 1">
			<div class="double_box_wrap" v-for="(element, childIndex) in leftData" :key="childIndex">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">基本信息</div>
					</div>
					<div class="content_wrap">
						<div class="map_dialogDetail">
							<div class="header">
								<div class="leftImg">
									<img src="@/assets/mobileEndYut.png" v-if="!element.buildingMainImg" class="property-img" />
									<el-image
										:src="`${http_oa}${element.buildingMainImg}`"
										:initial-index="0"
										class="property-img"
										v-else
										fit="cover"
										:preview-src-list="[`${http_oa}${element.buildingMainImg}`]"
									/>
								</div>
								<div class="rightContent">
									<div class="rightFlex">
										<div class="title">{{ element.buildingName }}</div>
										<div class="level">{{ element.buildingRate }}级</div>
										<div class="certified" v-if="element.securitization">已证券化</div>
										<div class="uncertified" v-if="!element.securitization">未证券化</div>
									</div>
									<div class="address">
										<el-icon><Location /></el-icon>
										{{ element.address }}
									</div>
									<div class="address_send">
										<img src="@/assets/send.png" alt="" />
										{{ element.businessDistrict }}
									</div>
								</div>
							</div>

							<div class="business-area">
								<div class="title_box">建筑物介绍</div>
								<div class="address">
									{{ element.buildingSummary }}
								</div>
							</div>

							<!-- 商圈信息 -->
							<div class="business-area">
								<div class="info-grid">
									<div class="info-item" v-show="element[item.value]" v-for="(item, index) in activeMarkerBasic" :key="index">
										<div class="value">{{ element[item.value] }}{{ handleMontage(item.label) }}</div>
										<div class="label">{{ item.label }}</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="single_wrap" style="max-height: 348px">
					<div class="title1">
						<div class="title">现场调查</div>
					</div>
					<div class="content_wrap" style="min-height: 260px">
						<div class="business-area" style="margin: 0 0 16px 0">
							<div class="investigation">
								对于建筑物的物理信息和服务情况，我们建立了全新和科学的打分体系，通过八项打分，交叉分析，合理赋权。形成准确的物业管理情况评价体系。经评测，{{
									element.buildingName
								}}的物管情况为<span style="color: #1868f1">{{ element.buildingSceneSurvey.average || 0 }}</span
								>分。
							</div>
						</div>

						<div class="content_flex">
							<div class="content_c" v-for="(item, index) in list" :key="index">
								<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
									<div class="content_left">{{ childitem.name }}</div>
									<div class="content_right">{{ element.buildingSceneSurvey[childitem.key] }}{{ childitem.unit }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="single_wrap" style="max-height: 248px">
					<div class="title1">
						<div class="title">户型图</div>
					</div>
					<div class="content_wrap">
						<div style="display: flex; height: 100%; align-items: center" v-if="element.imgList?.length > 0">
							<div @click="scrollLeft('scrollContainer')" style="width: 24px; margin-right: 16px">
								<img src="@/assets/preImg.png" alt="" style="width: 24px; cursor: pointer" />
							</div>
							<div
								ref="scrollContainer"
								style="height: 160px; min-width: 690px; display: flex; gap: 16px; justify-content: space-between; overflow-x: auto"
							>
								<arco-image v-for="(item, index) in element.imgList" :key="index" :src="`${http_oa}${item}`" />
							</div>
							<div @click="scrollRight('scrollContainer')" style="width: 24px; margin-left: 16px">
								<img src="@/assets/nextImg.png" style="width: 24px; cursor: pointer" alt="" />
							</div>
						</div>

						<div class="empty_wrap" v-else style="min-height: 160px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
		</template>
		<template v-else-if="leftData.length == 1">
			<div class="box_wrap" v-for="(element, childIndex) in leftData" :key="childIndex">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">基本信息</div>
						</div>
						<div class="content_wrap">
							<div class="map_dialogDetail">
								<div class="header">
									<div class="leftImg">
										<img src="@/assets/mobileEndYut.png" v-if="!element.buildingMainImg" class="property-img" />
										<el-image
											:src="`${http_oa}${element.buildingMainImg}`"
											:initial-index="0"
											class="property-img"
											v-else
											fit="cover"
											:preview-src-list="[`${http_oa}${element.buildingMainImg}`]"
										/>
									</div>
									<div class="rightContent">
										<div class="rightFlex">
											<div class="title">{{ element.buildingName }}</div>
											<div class="level">{{ element.buildingRate }}级</div>
											<div class="certified" v-if="element.securitization">已证券化</div>
											<div class="uncertified" v-if="!element.securitization">未证券化</div>
										</div>
										<div class="address">
											<el-icon><Location /></el-icon>
											{{ element.address }}
										</div>
										<div class="address_send">
											<img src="@/assets/send.png" alt="" />
											{{ element.businessDistrict }}
										</div>
									</div>
								</div>

								<div class="business-area">
									<div class="title_box">建筑物介绍</div>
									<div class="address">
										{{ element.buildingSummary }}
									</div>
								</div>

								<!-- 商圈信息 -->
								<div class="business-area">
									<div class="info-grid">
										<div class="info-item" v-show="element[item.value]" v-for="(item, index) in activeMarkerBasic" :key="index">
											<div class="value">{{ element[item.value] }}{{ handleMontage(item.label) }}</div>
											<div class="label">{{ item.label }}</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="single_wrap" style="max-height: 348px">
						<div class="title1">
							<div class="title">现场调查</div>
						</div>
						<div class="content_wrap" style="min-height: 260px">
							<div class="business-area" style="margin: 0 0 16px 0">
								<div class="investigation">
									对于建筑物的物理信息和服务情况，我们建立了全新和科学的打分体系，通过八项打分，交叉分析，合理赋权。形成准确的物业管理情况评价体系。经评测，{{
										element.buildingName
									}}的物管情况为<span style="color: #1868f1">{{ element.buildingSceneSurvey.average || 0 }}</span
									>分。
								</div>
							</div>

							<div class="content_flex">
								<div class="content_c" v-for="(item, index) in list" :key="index">
									<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
										<div class="content_left">{{ childitem.name }}</div>
										<div class="content_right">{{ element.buildingSceneSurvey[childitem.key] }}{{ childitem.unit }}</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="single_wrap" style="max-height: 248px">
						<div class="title1">
							<div class="title">户型图</div>
						</div>
						<div class="content_wrap">
							<div style="display: flex; height: 100%; align-items: center" v-if="element.imgList?.length > 0">
								<div @click="scrollLeft('scrollContainer')" style="width: 24px; margin-right: 16px">
									<img src="@/assets/preImg.png" alt="" style="width: 24px; cursor: pointer" />
								</div>
								<div
									ref="scrollContainer"
									style="height: 160px; min-width: 690px; display: flex; gap: 16px; justify-content: space-between; overflow-x: auto"
								>
									<arco-image v-for="(item, index) in element.imgList" :key="index" :src="`${http_oa}${item}`" />
								</div>
								<div @click="scrollRight('scrollContainer')" style="width: 24px; margin-left: 16px">
									<img src="@/assets/nextImg.png" style="width: 24px; cursor: pointer" alt="" />
								</div>
							</div>

							<div class="empty_wrap" v-else style="min-height: 160px">
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>

		<template v-else>
			<div class="box_wrap">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">基本信息</div>
						</div>
						<div class="empty_wrap" style="min-height: 564px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">现场调查</div>
						</div>
						<div class="empty_wrap" style="min-height: 298px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">户型图</div>
						</div>
						<div class="empty_wrap" style="min-height: 200px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import { defineExpose } from 'vue';
import { getComparativeOverviews } from '@/api/syt.js';
import { storeToRefs } from 'pinia';
import { useStore } from '../../../../store';
const store = useStore();
const { http_oa } = storeToRefs(store);
const props = defineProps({
	buildData: {
		type: Array,
		default: () => [],
	},
});
watch(
	() => props.buildData,
	(newVal) => {
		if (newVal.length > 0) {
			console.log('props', newVal);
		}
	},
	{
		deep: true,
	}
);

const images = [
	'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/cd7a1aaea8e1c5e3d26fe2591e561798.png~tplv-uwbnlip3yd-webp.webp',
	'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/6480dbc69be1b5de95010289787d64f1.png~tplv-uwbnlip3yd-webp.webp',
	'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/0265a04fddbd77a19602a15d9d55d797.png~tplv-uwbnlip3yd-webp.webp',
	// 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/24e0dd27418d2291b65db1b21aa62254.png~tplv-uwbnlip3yd-webp.webp',
];

const scrollContainer = ref();

const activeMarkerBasic = ref([
	{
		label: '建筑面积',
		value: 'buildingArea',
	},
	{
		label: '资产类型',
		value: 'buildingType',
	},
	{
		label: '停车场',
		value: 'carPark',
	},
	{
		label: '得房率',
		value: 'roomRate',
	},
	{
		label: '总层数',
		value: 'totalFloors',
	},
	{
		label: '车位费',
		value: 'parkingFee',
	},
	{
		label: '物业费',
		value: 'propertyFee',
	},
	{
		label: '租金',
		value: 'rental',
	},
]);

const leftData = ref([]);

const columns = ref([
	{
		title: '面积(㎡)',
		dataIndex: 'area',
	},
	{
		title: '租金(元/月)',
		dataIndex: 'rent',
	},
]);

const list = ref([
	{
		arr: [
			{ name: '物管人员密度', key: 'manageStaffDensity' },
			{ name: '新旧程度', key: 'internalBuildingCondition' },
		],
	},
	{
		arr: [
			{ name: '周边繁荣程度', key: 'prosperity' },
			{ name: '设施配套', key: 'equipmentSupport' },
		],
	},
	{
		arr: [
			{ name: '购物便捷速度', key: 'shoppingConvenient' },
			{ name: '卫生设施', key: 'restroomImpression' },
		],
	},
	{
		arr: [
			{ name: '停车场', key: 'carPark' },
			{ name: '交通方便程度', key: 'trafficConvenient' },
		],
	},
]);

function handleMontage(label) {
	if (label === '建筑面积') {
		return '㎡';
	}
	if (label === '得房率') {
		return '%';
	}
	if (label === '物业费') {
		return '元/㎡/月';
	}
	if (label === '租金') {
		return '元/㎡/天';
	}

	return '';
}

function scrollLeft() {
	scrollContainer.value[0].scrollBy({
		left: -236, // 每次点击滚动的距离
		behavior: 'smooth',
	});
}
function scrollRight() {
	scrollContainer.value[0].scrollBy({
		left: 236, // 每次点击滚动的距离
		behavior: 'smooth',
	});
}

function handleLevelColor(level) {
	if (level == 'S') {
		return {
			background: `linear-gradient(90deg, #9D71DA 0%, #722ED1 100%)`,
		};
	}

	if (level == 'A+') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}
	if (level == 'A') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}

	if (level == 'B+') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}
	if (level == 'B') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}

	if (level == 'C') {
		return {
			background: `linear-gradient(90deg, #FFA44D 0%, #FF7D00 100%)`,
		};
	}
}

const getData = async (type) => {
	console.log(type, 'type');
	if (type == 1) {
		leftData.value = [];
		return;
	}
	let ids = props.buildData.map((item) => item.id).join(',');
	const res = await getComparativeOverviews({
		buildingIds: ids,
	});
	if (res.code === 200) {
		console.log('res,resresresresres', res);
		leftData.value = res.data;
		leftData.value.forEach((element) => {
			if (element.floorPlanUrls) {
				element.imgList = element.floorPlanUrls.split(',');
			} else {
				element.imgList = [];
			}
		});
		console.log(leftData.value, 'leftData.value[0]');
	}
};

defineExpose({
	getData,
});
</script>
<style lang="scss" scoped>
::v-deep .arco-carousel-indicator-position-outer {
	margin-bottom: 0 !important;
}

::v-deep .arco-image {
	width: 215px;
	height: 158px;
	cursor: pointer;
	border: 1px solid var(--Line-Line, #e5e6eb);
	border-radius: 7.96px;
}
::v-deep .arco-image-img {
	width: 215px;
	height: 158px;
	object-fit: cover;
}

::v-deep .arco-carousel-card > :nth-child(n) {
	width: 219px !important;
	height: 160px !important;
	gap: 16px;
	border-radius: 7.96px;
	border: 1px solid var(--Line-Line, #e5e6eb);
}
.cangchu_wrap {
	flex: 1;
	display: flex;
	gap: 16px;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
		.single_wrap {
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			::-webkit-scrollbar {
				/* 隐藏滚动条 */
				display: none;
			}
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			//	.content_wrap {
			//	padding: 20px 16px;
			//	}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
	.double_box_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16px;
		.single_wrap {
			flex: 1;
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
}

.content_wrap {
	padding: 20px 16px;
	.table_wrap {
		width: 100%;
		::v-deep .arco-table .arco-table-th {
			background: #f7f8fa;
		}
	}
	.content_flex {
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		border: 1px solid #e5e6eb;
		border-bottom: 0px;
		.content_c {
			width: 100%;
			display: flex;
		}
	}
	.features {
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.features_Item {
			width: 139px;
			height: 96px;
			padding: 21px 16px;
			border-radius: 4px;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: center;
			background: #f7f8fa;
			.item_img {
				width: 28px;
				height: 28px;
				margin-bottom: 4px;
			}
			.item_content {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 22px;
				.item_content_l {
					width: 16px;
					height: 16px;
					margin: 3px 4px 3px 0;
					.item_content_img {
						width: 16px;
						height: 16px;
					}
				}
				.item_content_text {
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
					text-align: center;
					color: #1d2129;
				}
			}
		}
	}
	.geographic {
		background: #f7f8fa;
		width: calc(100% - 32px);
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
		padding: 9px 16px;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #4e5969;
	}
	.content_ {
		display: flex;
		width: 50%;
		.content_left {
			box-sizing: border-box;
			padding: 9px 16px;
			background: #f7f8fa;
			border-right: 1px solid #e5e6eb;
			border-bottom: 1px solid #e5e6eb;
			font-weight: 500;
			font-size: 14px;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: calc(100% - 50%);
			color: #1d2129;
		}
		.content_right {
			width: calc(100% - 50%);
			background: #ffffff;
			border-right: 1px solid #e5e6eb;
			border-bottom: 1px solid #e5e6eb;
			box-sizing: border-box;
			padding: 9px 16px;
			font-weight: 500;
			font-size: 14px;
			line-height: 21px;
			color: #1868f1;
		}
	}
	.content_flex > :nth-child(n) > :nth-child(2) > :nth-child(2) {
		border-right: 0px !important;
	}

	.content_flex > :nth-last-child(1) > :nth-last-child(1) > :nth-child(2) {
		border-right: 0px !important;
	}
}

.content_s {
	display: flex;
	.content_left {
		box-sizing: border-box;
		padding: 9px 16px;
		background: #e8f3ff;
		border-top-left-radius: 4px;
		border-bottom-left-radius: 4px;
		font-weight: 500;
		font-size: 14px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 116px;
		margin: 0 2px 2px 0;
		color: #1868f1;
	}
	.content_right {
		width: calc(100% - 118px);
		background: #f7f8fa;
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
		box-sizing: border-box;
		margin: 0 0 2px 0;
		padding: 9px 16px;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #4e5969;
	}
}

.map_dialogDetail {
	.header {
		display: flex;
		align-items: center;
		gap: 16px;
		background: #f7f8fa;
		border-radius: 4px;
		padding: 20px;
		.leftImg {
			height: 136px;
		}
		.property-img {
			width: 200px;
			height: 136px;
			border-radius: 4px;
		}
		.title {
			font-weight: 500;
			font-size: 24px;
			line-height: 32px;
			color: #1d2129;
		}
		.address {
			font-weight: 400;
			font-size: 14px;
			line-height: 22px;
			color: #4e5969;
			display: flex;
			margin-bottom: 8px;
			align-items: center;
			.el-icon {
				margin-right: 4px;
			}
		}
	}

	.rightContent {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	.rightFlex {
		display: flex;
		align-items: center;
		margin-bottom: 12px;
		.title {
			font-weight: 500;
			font-size: 24px;
			line-height: 32px;
			color: #1d2129;
		}
		.level {
			background: linear-gradient(90deg, #77a9ff 0%, #1868f1 100%);
			font-weight: 500;
			font-size: 14px;
			height: 24px;
			border-radius: 4px;
			color: #fff;
			text-align: center;
			line-height: 24px;
			padding: 0px 9px;
			margin-left: 8px;
		}
		.certified {
			color: #1868f1;
			height: 22px;
			font-size: 14px;
			padding: 0 9px;
			border: 1px solid #1868f1;
			background: #e8f3ff;
			border-radius: 4px;
			line-height: 22px;
			margin-left: 4px;
		}
		.uncertified {
			font-weight: 500;
			font-size: 14px;
			margin-left: 4px;
			color: #1d2129;
			height: 22px;
			padding: 0 9px;
			background: #f2f3f5;
			border: 1px solid #7f7f7f;
			border-radius: 4px;
			line-height: 22px;
		}
	}
	.address_send {
		font-weight: 400;
		line-height: 22px;
		color: #4e5969;
		font-size: 14px;
		margin-bottom: 8px;
		display: flex;
		align-items: center;
		img {
			width: 16px;
			height: 16px;
			margin-right: 4px;
		}
	}

	.info-grid {
		border-radius: 4px;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20px;
		padding-right: 68px;
		.info-item {
			text-align: center;
			.value {
				font-weight: 600;
				font-size: 14px;
				line-height: 22px;
				color: #1d2129;
			}

			.label {
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: #4e5969;
			}
		}
	}
}

.business-area {
	border-radius: 4px;
	padding: 20px;
	background: var(--fill-1, #f7f8fa);
	position: relative;
	margin-top: 16px;
	.title_box {
		font-weight: 600;
		font-size: 20px;
		line-height: 28px;
		color: #1d2129;
		margin-bottom: 12px;
	}
	.address {
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #4e5969;
	}

	.investigation {
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #4e5969;
	}
}

.diagramContent {
	height: 160px;
	display: flex;
	gap: 16px;
	margin: 0 36px;
	img {
		width: 219.44px;
		height: 160px;
		border-width: 1px;
		border-radius: 7.96px;
		border: 1px solid var(--Line-Line, #e5e6eb);
	}
}
</style>
