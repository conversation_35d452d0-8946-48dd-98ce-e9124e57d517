<template>
	<div class="profile_main">
		<div class="left_menus">
			<div class="info-container">
				<img v-if="imageUrl" :src="imageUrl" alt="" />
				<img src="@/assets/rectangles.png" alt="" v-else />

				<div class="nickname">{{ $vuexStore.state.userInfo.userName || '用户名' }}</div>
			</div>
			<div class="menu-container">
				<div class="menu_item" v-for="(item, index) in menus" :key="index">
					<div class="title" :class="{ selected1: isMenuActive(item) }" @click="onitemss(item, index)">
						{{ item.name }}
					</div>
				</div>
			</div>
		</div>
		<div class="contionar_box">
			<router-view />
		</div>
	</div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useStore, vuexStore } from '../../store/index.js';
import { reactive, onMounted, ref, onBeforeUnmount, watchEffect, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
const store = useStore();
const { http_oa, userInfo } = storeToRefs(store);
const menus = reactive([
	{
		name: '基本信息',
		path: '/profile/mymessage',
		childMenus: [],
	},
	{
		name: '权益中心',
		path: '/profile/browsingHistory',
		childMenus: [],
	},
	{
		name: '订单中心',
		path: '/profile/orderCentre',
		childMenus: [],
	},
	// {
	// 	name: '通知公告',
	// 	path: '/profile/myinformation',
	// 	childMenus: [],
	// },
]);
const form = ref({
	nickname: '', //昵称
	phone: '', //手机号
	avatar: '', //头像
});
const imageUrl = ref('');
const element = ref();
const bodyElement = ref();

const imgUrl = ref('https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png');
const isMenuActive = (menu) => {
	// 检查当前菜单项
	if (menu.path == route.path) {
		return true;
	}
	// 如果有子菜单，递归检查子菜单项
	if (menu.childMenus) {
		for (const childMenu of menu.childMenus) {
			if (isMenuActive(childMenu)) {
				return true;
			}
		}
	}
	return false;
};

// 监听
watch(
	() => vuexStore.state.userInfo,
	(newVal, oldVal) => {
		if (vuexStore.state.userInfo.headImage) {
			let randomInt = Math.floor(Math.random() * 100) + 1;
			imageUrl.value = store.imagePathPrefix + vuexStore.state.userInfo.headImage + '?v=' + randomInt;
		} else {
			imageUrl.value = null;
		}
	}
);

watchEffect(() => {
	handleFooterBox();
});

onBeforeUnmount(() => {
	if (element.value?.style) {
		element.value.style.display = '';
	}
});

onMounted(() => {
	window.scrollTo(0, 0);
	if (vuexStore.state.userInfo.headImage) {
		imageUrl.value = store.imagePathPrefix + vuexStore.state.userInfo.headImage;
	}
});
const updateAvatarUrl = (e) => {
	console.log(e);
};
const onitemss = async (item, index) => {
	router.push({
		path: item.path,
	});
};

function handleFooterBox() {
	setTimeout(() => {
		// bodyElement.value = document.querySelector('#app');
		element.value = document.querySelector('.footer_box');
		if (element.value?.style) {
			element.value.style.display = 'none';
		}
		// bodyElement.value.style.overflow = 'hidden';
	}, 100);
}
</script>

<style lang="less" scoped>
::v-deep .footer_box {
	display: none;
}
.profile_main {
	width: 100%;
	height: calc(100vh - 76px);
	max-width: 1440px;
	margin: 0 auto;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	padding-top: 20px;

	.left_menus {
		height: 378px;
		border-radius: 6px;
		width: 220px;
		transition: all 0.3s;
		background-color: rgba(255, 255, 255, 1);
		position: absolute;
		.info-container {
			padding: 24px 16px;
			border-bottom: 1px solid rgba(231, 231, 231, 1);
			img {
				width: 60px;
				height: 60px;
				border-radius: 10px;
			}
			.nickname {
				height: 28px;
				margin: 7px 0 -2px 0;
				font-size: 20px;
				font-weight: 500;
				line-height: 28px;
				color: #1d2129;
			}
		}
		.menu-container {
			padding-top: 8px;
			.menu_item {
				width: 100%;
				min-height: 48px;
				margin-bottom: 8px;
				.title {
					padding-left: 16px;

					width: 100%;
					height: 48px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					font-weight: bold;
					cursor: pointer;

					&:hover {
						font-weight: bold;
						color: rgba(24, 104, 241, 1);
					}
				}

				.selected1 {
					font-weight: bold;

					color: rgba(24, 104, 241, 1);
					border-right: 3px solid rgba(24, 104, 241, 1);
					box-sizing: border-box;
				}
			}
		}
	}

	.contionar_box {
		width: calc(100% - 230px);
		height: calc(100vh - 120px);
		// min-height: calc(100vh - 40px - 76px);
		border-radius: 6px;
		box-sizing: border-box;
		margin-left: 230px;
		background: #f6f6f6;
		// padding: 20px 16px;
	}
}
</style>
