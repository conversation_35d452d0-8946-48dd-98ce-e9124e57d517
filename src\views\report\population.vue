<template>
	<div v-for="(item, index) in arr" :key="index">
		<div :key="index" class="container" :style="{marginBottom: (index==2? '0px':'')}">
			<div class="flex">
				<div class="left">
					<div>
						<div class="top">
							<div class="title">{{ item.res.buildingName }}辐射半径人口概况</div>
							<div class="address">{{ item.res.buildingName }} , {{ item.res.city }} , {{ item.res.districts }}</div>
							<div class="time">开车时间: {{ item.res.arrivalTime }} 分钟半径</div>
						</div>
						<div class="gaode">
							<div :id="item.containerId" style="height: 100%; width: 100%" ref="map_box"></div>
							<!-- <div style="display: flex">
						<el-button style="height: 27px;" round @click="handleButtonClick(300)"
							:class="{ 'active-button': selectedRadius === 300 }">300m</el-button>
						<el-button style="height: 27px;" round @click="handleButtonClick(500)"
							:class="{ 'active-button': selectedRadius === 500 }">500m</el-button>
						<el-button style="height: 27px;" round @click="handleButtonClick(1000)"
							:class="{ 'active-button': selectedRadius === 1000 }">1000m</el-button>
						<div  class="clearMap active" @click="cleanMap1()">清空地图</div>
					</div> -->
							<!-- <gaode @updateRadius="handleUpdateRadius" 
                @clickChild="clickEven" @clickMarker="gomap" :radius="selectedRadius" :markers="cardList"
                @searchMap="getCardListData" ref="aMap"></gaode> -->
						</div>
						<div class="bottom">
							<div class="text">此信息图包含术木智能提供的数据。GIS服务在此图中包含数据的年份是2021年。</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="r-left">
						<div class="r-l-top">
							<div class="item-title">教育</div>
							<div class="edu">
								<div v-for="(i, j) in item.degree" class="degrees">
									<div class="dd" :key="j" :style="`background-color:${i.bgcolor};width:100%;height:150px`">
										<el-icon class="avatar"><Avatar /></el-icon>
										<i class="el-icon-s-custom"></i>
										<span class="perc numberFont">{{ i.percent }}</span>
										<span style="text-align: center; font-size: 12px">{{ i.name }}</span>
									</div>
								</div>
							</div>
						</div>
						<div class="r-l-content">
							<div class="item-title">就业</div>
							<div style="display: inline-flex; width: 100%">
								<div class="obj">
									<div v-for="(i, j) in item.jobs" class="jobs" :style="`background-color:${i.bgcolor}`">
										<div :key="j" :style="`width:100%;height:50px;line-height:50px;`">
											<span style="padding-left: 20px">{{ i.name }}</span>
											<span style="padding-right: 10px; float: right;" class="numberFont"> {{ i.percent }}</span>
										</div>
									</div>
								</div>
								<div class="losejob">
									<div class="circle numberFont">{{ item.unemployedRate }}</div>
									<span>失业率</span>
								</div>
							</div>
						</div>
						<div class="r-l-bottom">
							<div class="item-title center">关键事实</div>
							<div class="trueths">
								<div v-for="(i, j) in item.trueths" class="trueth">
									<div :key="index" class="true1 numberFont">
										{{  $formattedMoney(i.count) }}
									</div>
									{{ i.name }}
								</div>
							</div>
						</div>
					</div>
					<div class="r-right">
						<div class="r-r-top">
							<div class="item-title">收入</div>
							<div>
								<div v-for="(i, j) in item.income">
									<div :key="j" class="moneies">
										<div class="image">
											<el-icon style="font-size: 60px; color: #fefddf"><Coin /></el-icon>
										</div>
										<div class="money numberFont">
											<span class="out"><span class="in">¥</span>{{ $formattedMoney(i.count) }}</span>
											{{ i.name }}
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="r-r-bottom" :id="`${item.chartsId}`" style="width: 300px; height: 330px"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<style lang="less" scoped>
@Width: 400px;
@Height: 400px;
@PaddingTop: 25px;
.text {
	width: 250px;
}
.dd {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.avatar {
	margin-top: 30px;
	font-size: 40px;
}
.bottom {
	font-size: 11px;
	display: flex;
	align-items: flex-end;
	justify-content: end;
}
.r-l-bottom {
	padding: @PaddingTop 10px;
}
.item-title {
	color: #8de98d;
	font-size: 14px;
	padding-bottom: 10px;
}
.center {
	text-align: center;
}
.true1 {
	font-size: 30px;
	text-align: center;
	border-bottom: 8px solid #598c44;
	margin-bottom: 4px;
	background-color: #333a2c;
	height: 55px;
	width: 150px;
	color: #fefddf;
	line-height: 50px;
}
.trueth {
	flex-basis: 50%;
	font-size: 12px;
	color: #8ab78a;
	display: flex;
	justify-content: center;
	flex-direction: column;
	align-items: center;
	margin-bottom: 10px;
}
.trueths {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-wrap: wrap;
}
.moneies {
	width: 95%;
	justify-content: center;
	background-color: #333a2c;
	margin-bottom: 10px;
	color: #8ebc8e;
	font-size: 12px;
	height: 60px;
	display: flex;
	justify-content: start;
}
.r-right {
	width: 310px;
}
.jobs {
}
.obj {
	width: 258px;
}
.job {
	height: 40px;
	width: 100px;
	background-color: #5f9747;
}
.losejob {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 0 7px;
	background: #333a2c;
}
.circle {
	width: 70px;
	height: 70px;
	font-size: 20px;
	border-radius: 50%;
	background-color: #538440;
	line-height: 70px;
	text-align: center;
	font-weight: 700;
}
.losejob span {
	margin-top: 20px;
	font-size: 12px;
}
.degrees {
	display: flex;
	flex-basis: 25%;
}
.edu {
	display: flex;
	flex-wrap: wrap;
	flex-direction: row;
}
.flex {
	display: flex;
}
.left {
	width: @Width;
	background-color: #5b9246;
}
.right {
	width: calc(1046px - @Width);
	background-color: #232126;
	display: flex;
}
.r-left {
	width: 53%;
}
.money {
	display: flex;
	padding-left: 20px;
	flex-direction: column;
}
.money .out {
	font-size: 35px;
}
.in {
	color: #fefddf;
}
.r-l-top {
	height: @Height - 270;
	padding: @PaddingTop 10px;
}
.r-l-content {
	height: @Height - 250;
	padding: @PaddingTop 10px;
}
.r-r-top {
	height: 240px;
	padding: @PaddingTop 10px;
}
.container {
	padding: 3px;
	font-family: serif;
	color: #ffffff;
	margin-bottom: 200px;
}
.gaode {
	width: @Width - 5;
	height: @Height;
}
.image {
	width: 60px;
	text-align: center;
	background-color: #5f9949;
}
.top {
	padding-left: 10px;
}
.address {
	margin: 10px 0px;
	font-size: 12px;
}
.perc {
	margin: 5px 0px;
}
.title {
	font-size: 30px;
	color: #ffffff;
	margin: 15px 0px;
}
.time {
	font-size: 11px;
	margin: 10px 0px;
}
.bottom {
	height: 100px;
}
.r-r-bottom{
	background-color: #333a2c;
}
.numberFont{
	font-family: 'Times New Roman', Times, serif;
}
</style>
<script setup>
import * as echarts from 'echarts';
import AMapLoader from '@amap/amap-jsapi-loader';
import { getPopulationData } from '../../api/baogao';
import { useRoute } from 'vue-router';
const router = useRoute();
import { ref, onMounted, getCurrentInstance, onBeforeMount, reactive } from 'vue';
import { storeToRefs } from 'pinia';
import { useStore } from '../../store';
const store = useStore();
const { map_key } = storeToRefs(store);
let map = null;
const containerId1 = 'container1';
const containerId2 = 'container2';
const containerId3 = 'container3';

const income = ref();
const arr = reactive([
	{
		containerId: `containerId1`,
		chartsId: `echarts1`,
		res: {},
	},
	{
		containerId: `containerId2`,
		chartsId: `echarts2`,
		res: {},
	},
	{
		containerId: `containerId3`,
		chartsId: `echarts3`,
		res: {},
	},
]);
const trueths = ref();

const initMap = (item, index) => {
	const a = item.res.coordinate.split(',')[0];
	const b = item.res.coordinate.split(',')[1];

	AMapLoader.load({
		key: map_key.value,
		version: '2.0',
		plugins: ['AMap.CircleEditor', 'AMap.Geolocation'],
	})
		.then((AMap) => {
			map = new AMap.Map(item.containerId, {
				viewMode: '2D',
				zoom: index == 0 ? 14 : index == 1 ? 13 : 12,
				center: [a, b],
			});
			//异步加载控件
			AMap.plugin('AMap.Scale', function () {
				var scale = new AMap.Scale(); //比例尺
				map.addControl(scale);
			});

			// 初始化圆编辑器
			const c = new AMap.LngLat(a, b);
			const circle = new AMap.Circle({
				center: c, // 圆心位置为点击位置
				radius: index == 0 ? 1000 : index == 1 ? 2500 : 5000, // 半径（单位：米）
				strokeColor: '#67C23A',
				strokeOpacity: 1,
				strokeWeight: 3,
				fillColor: '#409EFF',
				fillOpacity: 0.35,
			});
			map.add(circle);
		})
		.catch((e) => {
			console.log(e);
		});
};

onMounted(() => {
	if (router.query.id) {
		arr.forEach(async (item, index) => {
			let param = {
				buildingId: router.query.id,
				cityType: 1,
				radius: index == 0 ? 1000 : index == 1 ? 2500 : 5000,
			};
			let res = await getPopulationData(param);
			let temDegree = [
				{
					name: '无高中文凭',
					percent: res.result['educationNoHigh'] + '%',
					bgcolor: '#333a2c',
				},
				{
					name: '高中毕业',
					percent: res.result['educationHigh'] + '%',
					bgcolor: '#2d4923',
				},
				{
					name: '大专学历',
					percent: res.result['educationJuniorCollege'] + '%',
					bgcolor: '#467034',
				},
				{
					name: '学士/硕士生/博士生',
					percent: res.result['educationBachelorMasterDoctor'] + '%',
					bgcolor: '#66a54c',
				},
			];
			let tempJobs = [
				{ name: '白领', percent: res.result['whiteCollar'] + '%', bgcolor: '#5f9747' },
				{ name: '蓝领', percent: res.result['blueCollar'] + '%', bgcolor: '#538440' },
				{ name: '公共事业', percent: res.result['publicUtilities'] + '%', bgcolor: '#5f9747' },
			];
			let tempIncome = [
				{
					name: '平均家庭收入',
					count: res.result['householdIncomeAvg'],
				},
				{
					name: '人均收入',
					count: res.result['perCapitaIncome'],
				},
				{
					name: '人均资产净值中位数',
					count: res.result['perCapitaAssetMid'],
				},
			];
			let trueths = [
				{
					name: '流动人口',
					count: res.result['floatingPopulation'],
				},
				{
					name: '年龄中位数',
					count: res.result['ageMid'],
				},
				{
					name: '家庭数',
					count: res.result['householdsNum'],
				},
				{
					name: '人均可支配收入中位数',
					count: res.result['disposableIncomeMid'],
				},
			];
			item.degree = temDegree;
			item.jobs = tempJobs;
			item.res = res.result;
			item.trueths = trueths;
			item.unemployedRate = res.result.unemployedRate + '%';
			item.income = tempIncome;
			initMap(item, index);
			initEchart(item, index);
		});
	}
});
const initEchart = (item) => {
	let option;
	let myChart;
	var chartDom = document.querySelector('#' + item.chartsId);
	myChart = echarts.init(chartDom);
	const data = [
		item.res.householdIncome.lowIncome,
		item.res.householdIncome.lowerMiddleIncome,
		item.res.householdIncome.middleIncome,
		item.res.householdIncome.upperMiddleIncome,
		item.res.householdIncome.moderateIncome,
		item.res.householdIncome.highIncome,
		item.res.householdIncome.higherIncome,
		item.res.householdIncome.affluentIncome,
		item.res.householdIncome.wealthyIncome,
	];

	option = {
		title: {
			left: 'center',
			text: '家庭收入（￥）',
			textStyle: {
				color: '#45a768',
				fontSize: 13,
			},
		},
		legend: { show: false },
		xAxis: {
			axisLabel: {
				rotate: 50,
				fontSize: 12,
			},
		},
		grid: {
			left: 50,
		},
		yAxis: {
			type: 'category',
			data: ['0-4999', '5000-14999', '15000-24999', '25000-34999', '35000-49999', '50000-74999', '75000-100000', '100000-200000', '200000+'],
			axisLabel: {
				rotate: 50,
				fontSize: 9,
			},
		},
		series: [
			{
				// realtimeSort: true,
				type: 'bar',
				data: data,
				barWidth: '50%',
				label: {
					show: true,
					position: 'insideRight'
				},
				itemStyle: {
					color: '#358b21',
				},
			},
		],
		legend: {
			show: true,
		},
	};

	option && myChart.setOption(option);
};
</script>
