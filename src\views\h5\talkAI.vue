<template>
	<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
	<div>
		<div class="AItalk" ref="scrollContainer">
			<div class="avatar">
				<div class="avatext">
					<div class="firstText">hi!中午好！</div>
					<div class="secondText">今天有遇到什么困惑吗？跟我说说吧，我来给你提供灵感！</div>
				</div>
			</div>
			<div class="example">
				<div class="cc">
					<p>您可以试着问我</p>
					<button class="exampQuestion" :disabled="loading" v-for="item in arr" :key="item.question" @click="sendQuestion(item.question)">
						{{ item.question }}
					</button>
				</div>
			</div>
			<div class="result">
				<div v-for="(item, index) in dataList" :key="index">
					<div class="question">
						<div class="rightNode">{{ item.question }}</div>
						<img src="@/assets/newLogo.png" alt="" />
					</div>
					<div class="answer">
						<div class="AIavatar"></div>
						<div class="leftNode" :class="item.loading ? 'leftNodeLoading' : ''" :ref="handleGetRef(index)">
							<div :class="item.loadingTitle ? 'loadingEl' : ''"></div>
							<div v-html="md.render(item.content)"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="block"></div>
		</div>
		<div class="bg">
			<el-input
				class="input"
				resize="none"
				type="textarea"
				:autosize="autosize"
				:adjust-position="false"
				:disabled="loading"
				v-model="question"
				@keydown.enter.native="enterEvent"
				@keyup.enter="sendQuestion()"
				placeholder="请在此输入您的提问~"
			>
			</el-input>
			<el-button class="btn" :disabled="loading" plain type="primary" @click="sendQuestion()">发送</el-button>
		</div>
		<div class="tixing">内容由AI生成，无法确保真实准确，仅供参考</div>
	</div>
</template>

<script setup>
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { ref, h, onMounted, onUpdated, reactive, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getBearer } from '../../api/home';
import MarkdownIt from 'markdown-it';
const md = new MarkdownIt();
const autosize = {
	maxRows: 2,
};
let controller;
let messageElement;
// 存放第一次的conversation_id
const filstConversation_id = ref('');

const dataList = reactive([]);
// 动态添加ref
const nodeRefs = {};
const bearer = ref('');
let question = ref('');
const loading = ref(false);
let arr1 = reactive({
	//注意初始值要为空字符串,后面才能拼接
	content: '',
});
const scrollContainer = ref(null);

const arr = ref([
	{
		question: '北京零售地产市场情况',
	},
	{
		question: '什么是估值？',
	},
	{
		question: '青岛上季度经济增长是多少？',
	},
]);

// input 键盘事件
const enterEvent = (e) => {
	if (!e.ctrlKey) {
		e.preventDefault();
	}
};
// 问题提出
const sendQuestion = (q) => {
	if (!q && !question.value) {
		ElMessage({ message: '请输入问题', type: 'warning' });
		return;
	}
	if (q) {
		question.value = q;
	}
	loading.value = true;
	filstConversation_id.value = ''; // 清空id
	sendMessage(q || question.value);
};

// 动态添加ref
const handleGetRef = (index) => {
	nodeRefs['leftNode' + index] = ref(null);
	return (el) => {
		nodeRefs['leftNode' + index].value = el;
		// console.log(nodeRefs, 'nodeRefs');
	};
};

let sendMessage = async (name) => {
	dataList.push({
		question: name,
		loading: true,
		loadingTitle: true,
		content: arr1.content,
	});
	// 请求数据，流式输出
	await fetchEventSource('https://smai.biaobiaozhun.com/v1/chat-messages', {
		method: 'POST',
		headers: {
			Authorization: bearer.value,
			'Content-Type': 'application/json;charset=utf-8',
		},
		body: JSON.stringify({
			inputs: {},
			query: question.value || '你好',
			response_mode: 'streaming',
			conversation_id: '',
			user: 'smzn',
		}),

		async onmessage(ev) {
			if (ev.data) {
				let obj = JSON.parse(ev.data);
				dataList[dataList.length - 1].loadingTitle = false;
				// 拿到具体的内容
				let content = obj.answer;
				// 拼接：这里是数据打印机式输出的关键
				dataList[dataList.length - 1].content += content ? content : '';
			}
		},
		//会话发送完毕时触发
		onclose() {
			dataList[dataList.length - 1].loading = false;
			loading.value = false;
			question.value = '';
			// handleScrollToBottom();
		},
	});
};
// 建立 FETCH-SSE 连接
// const connectFetch = (e) => {
// 	if (e) {
// 		e.preventDefault();
// 	}
// 	// http://8.210.21.175/v1/chat-messages
// 	// https://smai.biaobiaozhun.com/smai/v1/chat-messages
// 	controller = new AbortController();
// 	fetchEventSource('https://smai.biaobiaozhun.com/v1/chat-messages', {
// 		method: 'POST',
// 		mode: 'cors',
// 		headers: {
// 			Authorization: bearer.value,
// 			'Content-Type': 'application/json',
// 		},
// 		body: JSON.stringify({
// 			inputs: {},
// 			query: question.value || '你好',
// 			response_mode: 'streaming',
// 			conversation_id: '',
// 			user: 'smzn',
// 		}),
// 		signal: controller.signal,
// 		onopen: () => {
// 			// setTimeout(() => {
// 			// anwerNode.scrollTop = anwerNode.scrollHeight;
// 			// });
// 			console.log('连接成功');
// 		},
// 		onclose: () => {
// 			question.value = '';
// 			console.log('连接关闭');
// 		},
// 		onmessage: (event) => {
// 			setTimeout(() => {
// 				if (event.data && JSON.parse(event.data)?.answer) {
// 					// console.log(JSON.parse(event.data).answer, 'JSON.parse(event.data).answer');
// 				} else {
// 					console.log('结束!!!!');
// 					dataList[dataList.length - 1].loading = false;
// 					question.value = '';

// 					loading.value = false;
// 					// handleScrollToBottom();
// 				}
// 			});
// 		},
// 		onerror: (e) => {
// 			console.log(e);
// 		},
// 	}).catch((e) => {
// 		console.log('SSEerror:', e);
// 	});

// let signal = controller.signal;
// // controller.abort() 方法用于发出取消信号。这时会触发abort事件，这个事件可以监听，也可以通过
// signal.addEventListener('abort', () => {
// 	console.log('abort!');
// });
// };

function handleScrollToBottom() {
	nextTick(() => {
		scrollContainer.value.scrollTop = scrollContainer.value.scrollHeight;
	});
}

onMounted(async () => {
	const res = await getBearer();
	bearer.value = res.message;
});

onUnmounted(() => {});

onUpdated(() => {
	scroll2B(); //滚动条置底
});

const scroll2B = () => {
	handleScrollToBottom();
};

// 断开 FETCH-SSE 连接
const closeSSE = () => {
	if (controller) {
		// 断开连接
		controller.abort();
		controller = undefined;
		messageElement.innerHTML += `FETCH 连接关闭<br />`;
	}
};
</script>
<style lang="less" scoped>
.container {
	padding: 20px;
}
.example {
	// width: 98%;
	border-radius: 16px;
	background: #edf4ff;
	display: flex;
	// justify-content: f;
	flex-direction: column;
	align-items: flex-start;
	width: 100%;
	margin: 20px 0;
	.cc {
		padding: 10px 20px;
		box-sizing: border-box;
		width: 100%;
		.exampQuestion {
			border: 0;
			width: 100%;
			// width: 100%;
			margin: 6px 0;
			border-radius: 16px;
			background: white;
			padding: 10px 0px;
			padding-left: 10px;
			display: flex;
			position: relative;
			&::after {
				content: '';
				background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTUuNTQxNjkgMy41TDkuMDQxNjkgN0w1LjU0MTY5IDEwLjUiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS1vcGFjaXR5PSIwLjg1IiBzdHJva2Utd2lkdGg9IjEuMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=');
				display: block;
				width: 20px;
				height: 20px;
				position: absolute;
				right: 10px;
				background-repeat: no-repeat;
				background-size: 100% 100%;
			}
		}
	}
}
.AItalk {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: calc(100vh - 30px);
	overflow: scroll;
	background: #f5f8fd;
	.avatar {
		background: linear-gradient(90deg, rgba(225, 236, 255, 1), rgba(169, 199, 255, 1));
		width: 100%;
		padding: 20px;
		box-sizing: border-box;
		height: 110px;
		border-radius: 16px;
		background-size: cover;
		position: relative;
		.avatext {
			// max-width: 200px;
			.firstText {
				font-size: 20px;
				font-weight: bold;
				color: rgba(23, 62, 136, 1);
			}
			.secondText {
				margin-top: 20px;
			}
		}
		&::after {
			content: '';
			background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNzAiIHZpZXdCb3g9IjAgMCA4MCA3MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zNy4wMzQ5IDcyLjM2NDZDNTQuOTA5MyA3NC4wMDIyIDcwLjcyNjkgNjAuODM5NiA3Mi4zNjQ0IDQyLjk2NTJDNzQuMDAyIDI1LjA5MDggNjAuODM5NSA5LjI3MzI0IDQyLjk2NTEgNy42MzU2NkMyNS4wOTA3IDUuOTk4MDggOS4yNzMxMSAxOS4xNjA2IDcuNjM1NTIgMzcuMDM1QzUuOTk3OTQgNTQuOTA5NCAxOS4xNjA1IDcwLjcyNyAzNy4wMzQ5IDcyLjM2NDZaTTM2LjM1MDYgNzkuODMzM0M1OC4zNDk5IDgxLjg0ODggNzcuODE3NyA2NS42NDg3IDc5LjgzMzIgNDMuNjQ5NUM4MS44NDg2IDIxLjY1MDIgNjUuNjQ4NiAyLjE4MjQyIDQzLjY0OTMgMC4xNjY5MzdDMjEuNjUwMSAtMS44NDg1NSAyLjE4MjI5IDE0LjM1MTUgMC4xNjY4MDMgMzYuMzUwOEMtMS44NDg2OCA1OC4zNSAxNC4zNTE0IDc3LjgxNzggMzYuMzUwNiA3OS44MzMzWk0xOS4xMDc5IDMwLjU1NDdDMTkuMzU5OSAyNy44MDQ4IDIxLjc5MzMgMjUuNzc5NyAyNC41NDMyIDI2LjAzMTdDMjcuMjkzMiAyNi4yODM2IDI5LjMxODIgMjguNzE3MSAyOS4wNjYyIDMxLjQ2N0wyOC4xNTM5IDQxLjQyNTNDMjcuOTAyIDQ0LjE3NTIgMjUuNDY4NSA0Ni4yMDAyIDIyLjcxODYgNDUuOTQ4M0MxOS45Njg3IDQ1LjY5NjMgMTcuOTQzNyA0My4yNjI5IDE4LjE5NTYgNDAuNTEzTDE5LjEwNzkgMzAuNTU0N1pNNDQuNDU5OCAyNy44NTY0QzQxLjcwOTkgMjcuNjA0NCAzOS4yNzY1IDI5LjYyOTQgMzkuMDI0NSAzMi4zNzkzTDM4LjExMjIgNDIuMzM3NkMzNy44NjAyIDQ1LjA4NzUgMzkuODg1MiA0Ny41MjEgNDIuNjM1MiA0Ny43NzI5QzQ1LjM4NTEgNDguMDI0OSA0Ny44MTg1IDQ1Ljk5OTkgNDguMDcwNSA0My4yNUw0OC45ODI4IDMzLjI5MTdDNDkuMjM0NyAzMC41NDE4IDQ3LjIwOTcgMjguMTA4MyA0NC40NTk4IDI3Ljg1NjRaIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfODQ5XzU2OTUpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfODQ5XzU2OTUiIHgxPSI0My42NDkzIiB5MT0iMC4xNjY5MzciIHgyPSI1Ny43MzQ3IiB5Mj0iNTQuNjc5MyIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSJ3aGl0ZSIgc3RvcC1vcGFjaXR5PSIwLjQiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSJ3aGl0ZSIgc3RvcC1vcGFjaXR5PSIwLjA4Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+Cg==');
			height: 76px;
			width: 100px;
			display: block;
			position: absolute;
			right: 0px;
			bottom: 30px;
			background-repeat: no-repeat;
		}
	}
	.result {
		width: 100%;
	}
}
.block {
	height: 10px;
}
.bg {
	max-height: 140px;
	width: calc(100% - 32px);
	padding: 0px 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: absolute;
	bottom: 22px;
	background-color: #fff;
	height: 62px;
	.input {
		width: calc(100% - 75px);
		max-height: 140px;
		overflow: auto;
	}
	::v-deep .el-textarea {
		--el-input-border-color: #fff;
		--el-input-hover-border: #fff;
		--el-input-hover-border-color: #fff;
		--el-input-focus-border-color: #fff;
	}
	.btn {
		width: 60px;
		// background: #fff;
	}
}
.tixing {
	width: calc(100% - 32px);
	padding: 0px 26px;
	height: 18px;
	font-size: 12px;
	color: #888888;
}
@media screen and (max-width: 768px) {
	.bg {
		.input {
			width: calc((100% - 80px));
			margin-right: 20px;
		}
		.btn {
			width: 50px;
		}
	}
	.AItalk {
		.avatar {
			.avatext {
				max-width: 200px;
				.firstText {
					font-size: 20px;
					font-weight: bold;
					color: rgba(23, 62, 136, 1);
				}
				.secondText {
					margin-top: 10px;
				}
			}
		}
	}
}

.loadingEl {
	width: 5px;
	aspect-ratio: 1;
	border-radius: 50%;
	animation: l5 1s infinite linear alternate;
}
@keyframes l5 {
	0% {
		box-shadow: 7px 0 #333333, -7px 0 #0002;
		background: #333333;
	}
	33% {
		box-shadow: 7px 0 #333333, -7px 0 #0002;
		background: #0002;
	}
	66% {
		box-shadow: 7px 0 #0002, -7px 0 #333333;
		background: #0002;
	}
	100% {
		box-shadow: 7px 0 #0002, -7px 0 #333333;
		background: #333333;
	}
}

.leftNodeLoading {
	border: none !important;
}
</style>
