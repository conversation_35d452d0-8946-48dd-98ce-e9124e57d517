<template>
	<div class="container_box">
		<div class="banner_box">
			<div class="banner_tips">
				<div class="left_box">
					<div class="logo active">标标准APP</div>
					<div class="title"><span>商业地产楼宇</span>分析和交易平台</div>
					<div class="tips">术木智能科技（深圳）有限公司<br />粤ICP备2023095873号-1</div>
					<div class="btn_box">
						<img src="../../assets/images/buildDeal/ewm.png" alt="">
						<div class="title2">
							<span>扫码下载</span>
							<span>术木智能科技（深圳）有限公司 粤ICP备2023095873号-1</span>
						</div>
						<!-- <div class="btn btn1 active">ios下载</div>
						<div class="btn btn2 active">安卓下载</div> -->
					</div>
				</div>
				<div class="right_box"><img src="../../assets/images/buildDeal/banner_img.png" alt=""></div>
			</div>
		</div>
		<div class="card_box">
			<div class="title1">使用标标准</div>
			<div class="card_list">
				<div class="card" v-for="(item,index) in data.cardList" :key="index">
						<div class="icon"><img :src="item.icon" alt=""></div>
						<div class="h1">{{item.title}}</div>
						<div class="tips">{{item.tips}}</div>
				</div>
			</div>
		</div>
		<div class="biaozhun_body">
			<div class="biapzhun">
				<div class="text_box">
					<div class="tag"><span><img src="../../assets/images/buildDeal/icon9.png" alt=""></span>全方位解析</div>
					<div class="text">概况/位置和商圈/人口概况/招商分析/估值和证券化，全方位解析，标标准助您掌握建筑全面信息，做出更佳决策</div>
				</div>
				<div class="zhanshi_img"><img src="../../assets/images/buildDeal/img_zhanshi.png" alt=""></div>
			</div>
			<div class="biapzhun">
				<div class="zhanshi_img"><img src="../../assets/images/buildDeal/img_zhanshi.png" alt=""></div>
				<div class="text_box">
					<div class="tag"><span><img src="../../assets/images/buildDeal/icon10.png" alt=""></span>琥珀咨询</div>
					<div class="text">标标准聚焦于商业地产行业的时讯要闻与分析，助您洞察趋势</div>
				</div>
			</div>
			<div class="biapzhun">
				<div class="text_box">
					<div class="tag"><span><img src="../../assets/images/buildDeal/icon11.png" alt=""></span>标准空间</div>
					<div class="text">标标准严选空间与建筑，全面展示空间信息，助您招商引资</div>
				</div>
				<div class="zhanshi_img"><img src="../../assets/images/buildDeal/img_zhanshi.png" alt=""></div>
			</div>
		</div>
		
		
	</div>

</template>

<script setup>
import { reactive } from 'vue';
import icon1 from '../../assets/images/buildDeal/icon1.png'
import icon2 from '../../assets/images/buildDeal/icon2.png'
import icon3 from '../../assets/images/buildDeal/icon3.png'
import icon4 from '../../assets/images/buildDeal/icon4.png'
import icon5 from '../../assets/images/buildDeal/icon5.png'
import icon6 from '../../assets/images/buildDeal/icon6.png'
import icon7 from '../../assets/images/buildDeal/icon7.png'
import icon8 from '../../assets/images/buildDeal/icon8.png'
	const data = reactive({
		cardList:[{
			icon:icon1,
			title:'全域资产',
			tips:'简单概述简单概述别太长'
		},{
			icon:icon2,
			title:'行业咨询',
			tips:'简单概述简单概述别太长'
		},{
			icon:icon3,
			title:'招商引资',
			tips:'简单概述简单概述别太长'
		},{
			icon:icon4,
			title:'大宗推广',
			tips:'简单概述简单概述别太长'
		},{
			icon:icon5,
			title:'楼宇分析',
			tips:'简单概述简单概述别太长'
		},{
			icon:icon6,
			title:'交易需求',
			tips:'简单概述简单概述别太长'
		},{
			icon:icon7,
			title:'专项报告',
			tips:'简单概述简单概述别太长'
		},{
			icon:icon8,
			title:'个性化服务',
			tips:'简单概述简单概述别太长'
		}]
	})
</script>
<style scoped lang="less">
	.container_box {
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 1);

		.banner_box {
			width: 100%;
			height: 910px;
			background-image: url('../../assets/images/buildDeal/banner_bg.png');
			background-size: 100% auto;
			background-repeat: no-repeat;
			display: flex;
			justify-content: center;
			align-items: center;

			.banner_tips {
				width: 100%;
				max-width: 1440px;
				height: 900px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				box-sizing: border-box;

				.left_box {
					width: 507px;
					height: 488px;

					.logo {
						width: 151px;
						height: 48px;
						margin-bottom: 30px;
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 200px;
						background-color: rgba(230, 239, 255, 1);
						color: rgba(24, 104, 241, 1);
						font-weight: bold;
					}

					.title {
						width: 100%;
						margin-bottom: 30px;
						line-height: 80px;
						font-size: 48px;
						font-weight: bold;

						span {
							color: rgba(24, 104, 241, 1);
						}
					}

					.tips {
						font-size: 16px;
						margin-bottom: 60px;
					}

					.btn_box {
						width: 405px;
						height: 64px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						img{
							width: 100px;
							height: 100px;
							margin-right: 15px;
						}
						.title2{
							
							span{
								display: block;
								margin: 5px 0;
								&:first-child{
									font-size: 24px;
									font-weight: bold;
								}
								&:nth-child(2){
									font-size: 14px;
								}
							}
						}
						// .btn {
						// 	width: 177px;
						// 	height: 64px;
						// 	background-color: rgba(255, 255, 255, 1);
						// 	display: flex;
						// 	justify-content: center;
						// 	align-items: center;
						// 	border-radius: 16px;
						// 	font-weight: bold;
						// }

						// .btn1 {
						// 	&::before {
						// 		content: '';
						// 		width: 24px;
						// 		height: 24px;
						// 		margin-right: 10px;
						// 		margin-bottom: 5px;
						// 		background-image: url('../../assets/images/buildDeal/ios_icon.png');
						// 		background-size: 100% 100%;
						// 	}
						// }

						// .btn2 {
						// 	&::before {
						// 		content: '';
						// 		width: 24px;
						// 		height: 24px;
						// 		margin-right: 10px;
						// 		background-image: url('../../assets/images/buildDeal/andriod_icon.png');
						// 		background-size: 100% 100%;
						// 	}
						// }
					}

				}
				.right_box {
					width: 910px;
					height: 850px;
					img{
						width: 100%;
						height: 100%;
					}
				}
			}
		}
		.card_box{
			width: 100%;
			max-width: 1440px;
			margin: 0 auto;
			height: 548px;
			.title1{
				width: 100%;
				text-align: center;
				font-size: 40px;
				margin: 20px 0;
			}
			.card_list{
				width: 100%;
				height: 472px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex-wrap: wrap;
				.card{
					width: 100%;
					max-width: 336px;
					height: 220px;
					margin-bottom: 20px;
					display: flex;
					justify-content: center;
					align-items: flex-start;
					flex-direction: column;
					padding: 0 40px;
					box-sizing: border-box;
					border-radius: 8px;
					background-color: rgba(245, 248, 253, 1);
					.icon{
						width: 40px;
						height: 40px;
						img{
							width: 100%;
							height: 100%;
						}
					}
					.h1{
						font-size: 32px;
						font-weight: bold;
						margin: 20px 0;
					}
					.tips{
						font-size: 18px;
						color: rgba(134, 144, 156, 1);
					}
					
				}
			}
			
		}
		.biaozhun_body{
			width: 100%;
			height: 100%;
			background-color: rgba(245, 246, 247, 1);
			margin-top: 200px;
			.biapzhun{
				width: 100%;
				max-width: 1440px;
				margin: 0 auto;
				box-sizing: border-box;
				height: 700px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.text_box{
					width: 40%;
					transform: translateY(-120px);
					.tag{
						width: 181px;
						height: 48px;
						margin-bottom: 30px;
						display: flex;
						justify-content: center;
						align-items: center;
						background-color: rgba(230, 239, 255, 1);
						border-radius: 200px;
						color: rgba(24, 104, 241, 1);
						font-weight: bold;
						font-size: 24px;
						span{
							width: 20px;
							height: 20px;
							margin-right: 10px;
							margin-bottom: 5px;
							display: inline-block;
							img{
								width: 100%;
								height: 100%;
							}
						}
					}
					.text{
						width: 100%;
						line-height: 70px;
						font-size: 48px;
						font-weight: bold;
					}
				}
				.zhanshi_img{
					width: 50%;
					transform: translateY(-120px);
					height: auto;
					img{
						width: 100%;
						height: auto;
					}
				}
			}
			
		}
		
	}
	@media screen and (max-width:1024px) {
		.container_box{
			width: 100%;
			min-width: 375px;
			height: 100%;
			background-color: rgba(255, 255, 255, 1);
			.banner_box{
				width: 100%;
				height: 100%;
				.banner_tips{
					width: 100%;
					height: 100%;
					padding: 15px;
					display: flex;
					justify-content: center;
					align-items: center;
					flex-wrap:wrap;
					box-sizing: border-box;
					.left_box{
						width: 100%;
						height: 100%;
						margin-bottom: 30px;
						.btn_box{
							width: 100%;
						}
					}
					.right_box{
						width: 100%;
						height: 100%;
						img{
							width: 100%;
							height: auto;
						}
					}
				}
			}
			.card_box{
				width: 100%;
				height: 100%;
				.card_list{
					width: 100%;
					height: 100%;
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					.card{
						width: 100%;
						height: auto;
						padding: 20px;
						margin-bottom: 15px;
						box-sizing: border-box;
					}
				}
			}
			.biaozhun_body{
				width: 100%;
				height: 100%;
				.biapzhun{
					width: 100%;
					height: 100%;
					padding: 15px;
					box-sizing: border-box;
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					.text_box{
						width: 100%;
						.text{
							font-size: 24px;
							line-height: 36px;
						}
					}
					.zhanshi_img{
						width: 100%;
					}
				}
			
			}
		}
	}

</style>