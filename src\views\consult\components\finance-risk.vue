<template>
<!-- 头部 -->
<div class="risk_head" style="display: flex;justify-content: space-around;">
  <div style="width: 400px;height: 100px;; margin-right: 20px border-right: 1px solid #000;" class="boxx one">
    Vue 是一个框架，也是一个生态。其功能覆盖了大部分前端开发常见的需求。但 Web 世界是十分多样化的，不同的开发者在 Web 上构建的东西可能在形式和规模上会有很大的不同。考虑到这一点，Vue 的设计非常注重灵活性和“可以被逐步集成”这个特点。根据你的需求场景，你可以用不同的方式使用 Vue：
  </div>
  <!-- 第二块 -->
<div class="boxx two" style="display: flex;border-right: 1px solid #000;">
  <div style="margin-right: 20px;">
    <h2>96359</h2>
    <span>ababab</span>
  </div>
  <div style="margin-right: 20px;">
    <h2>96359</h2>
    <span>ababab</span>
  </div>
</div>
<!-- 第三模块 -->
<div class="boxx three" style="display: flex;border-right: 1px solid #000;">
  <div style="margin-right: 20px;">
    <h2>96359</h2>
    <span>ababab</span>
  </div>
  <div style="margin-right: 20px;">
    <h2>96359</h2>
    <span>ababab</span>
  </div>
  <div style="margin-right: 20px;">
    <h2>96359</h2>
    <span>ababab</span>
  </div>
  <div style="margin-right: 20px;">
    <h2>96359</h2>
    <span>ababab</span>
  </div>
</div>
  <!-- 第四模块 -->
  <div class="boxx four" style="display: flex;">
  <div style="margin-right: 20px;">
    <h2>96359</h2>
    <span>ababab</span>
  </div>
</div>
</div>
<!-- 中间数据部分 -->
<div class="data_box" style="display: flex;margin-top: 20px;">
  <!-- 雷达图 -->
  <div class="radar_map" style="display: flex;">
    <p style="width: 200px;">Vue 是一个框架，也是一个生态。其功能覆盖了大部分前端开发常见的需求。但 Web 世界是十分多样化的，不同的开发者在 Web 上构建的东西可能在形式和规模上会有很大的不同。考虑到这一点，Vue 的设计非常注重灵活性和“可以被逐步集成”这个特点。根据你的需求场景，你可以用不同的方式使用 Vue：</p>
    <div ref="echartsTU"></div>
  </div>
  <!-- 数据板块 -->
  <div class="datamodule">
    <div v-for="item in 8" :key="item" class="databoxs">
      <!-- echarts图表 -->
      
      <h4>Market</h4>
      <h4>Market</h4>
      <span>ababab</span>
    </div>
  </div>
</div>
 <!-- 建筑物 -->
 <div class="architecture_box">
    <div class="build_text">
      Vue.js（读音 /vjuː/, 类似于 view） 是一套构建用户界面的渐进式框架。
      Vue 只关注视图层， 采用自底向上增量开发的设计。
      Vue 的目标是通过尽可能简单的 API 实现响应的数据绑定和组合的视图组件。
      Vue 学习起来非常简单，本教程基于 Vue 3.0.5 版本测试。
    </div>
    <div class="build_pic">
      <img src="@/assets/aa.jpg" alt="" class="img">
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
// 雷达图
const echartsTU = ref(null)
// var myChart = echarts.init(document.getElementById('main'));
function initEc() {
 const myChart = echarts.init(echartsTU.value)

 myChart.resize({ width: 300, height: 300 })
 const option = {
  radar: {
    shape: 'circle',
    indicator: [
      { name: 'Sales', max: 6500 },
      { name: 'Administration', max: 16000 },
      { name: 'Information Technology', max: 30000 },
      { name: 'Customer Support', max: 38000 },
      { name: 'Development', max: 52000 },
      { name: 'Marketing', max: 25000 }
    ],
    // 设置雷达图的填充样式  
    areaStyle: {  
            normal: {  
                // 设置阴影颜色和透明度  
                color: 'rgba(0, 0, 0, 0.5)',  
                // 设置阴影模糊大小  
                shadowBlur: 10  
            }  
        }  
  },

  series: [
    {
      name: 'Budget vs spending',
      type: 'radar',
      data: [
        {
          value: [4200, 3000, 20000, 35000, 50000, 18000],
          name: 'Allocated Budget'
        },
        {
          value: [5000, 14000, 28000, 26000, 42000, 21000],
          name: 'Actual Spending'
        }
      ]
    }
  ]
 }
 // // 基于准备好的dom，初始化echarts实例// // 使用刚指定的配置项和数据显示图表。
 myChart.setOption(option)
}
// 折线图
onMounted(() => { // 需要获取到element,所以是onMounted的Hook
      initEc()
      let myChart1 = echarts.init(document.getElementById("myChart1")); 
      // 绘制图表
      myChart1.setOption({  
        xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
        type: 'value'
        },
        series: [
        {
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        type: 'line',
        areaStyle: {},
        }
        ]
      });
})


</script>

<style lang="less" scoped>
/* .chart_box {
  width: 100%;
 }
 .chart {
  height: 400px;
  width: 100%;
 } */
 .finance_table,
 .el-tabs__content {
  background-color: #f0f0f0 !important;
 }
.echarts-inner {
 width: 100%;
}
canvas {
 width: 100%;
 height: 100%;
}
body{
  background-color: #f0f0f0;
}
.risk_head {
  height: 100px;
  background-color: #fff;
  padding: 15px;
  box-shadow: 1px 1px 1px 1px #ccc;
}
.radar_map {
  background-color: #fff;
  padding: 15px;
  box-shadow: 1px 1px 1px 1px #ccc;
  
}
.datamodule {
  // display: flex;
  // white-space: wrap;
  // // float: left;
  // height: 500px;
  display: grid;  
  grid-template-columns: repeat(4, 1fr); // 定义三列的布局。  
  grid-gap: 10px; // 根据实际需求调整。
    
}
.databoxs {
  background-color: #fff;
  padding: 5px;
  margin-left: 58px;
  box-shadow: 1px 1px 1px 1px #ccc;
  width: 295px;
  // width: calc(50% - 10px);
  // height: 140px;
  // &:nth-child(4n+1) {
  //   margin-bottom: 4px;
  // }
}
.architecture_box {
  padding: 15px;
  margin-top: 14px;
  display: flex;
}
.build_text {
  width: 800px;
  background-color: #fff;
  padding: 15px;
  margin-right: 200px;
  box-shadow: 1px 1px 1px 1px #ccc;
}
.build_pic {
  background-color: #fff;
  box-shadow: 1px 1px 1px 1px #ccc;
}
.img {
  width: 800px;
}
.boxx {
  display: flex;
  justify-content: space-between;
}
</style>