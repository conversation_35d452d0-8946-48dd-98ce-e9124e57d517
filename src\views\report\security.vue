<template>
	<div class="main">
		<div class="contionar top">
			<div class="center">
				<p style="margin-left: 40px; font-size: 20px">
					敬呈：<span style="font-weight: bold">{{ basic.buildingPropertyOwner }}</span>
				</p>
				<div style="margin-left: 120px; font-size: 36px; line-height: 50px; text-align: right; width: 40%">
					<span style="">{{ basic.propertyOwnerCompany }}</span
					><span>类REITs </span>
					<div>融资服务方案</div>
				</div>
				<div style="padding: 7% 10% 6%; display: flex; font-size: 20px; align-items: center">
					<span>{{}}机构暂无</span>
					<el-icon style="margin: 0 20px; color: #ea0101; font-size: 30px">
						<CaretRight />
					</el-icon>
					<span>{{ basic.reportDownloadTime }}</span>
				</div>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg2">
			<img class="propertyOwnerLogo" style="width: 80px; height: 70px" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg3"></div>
		<div class="contionar bg4"></div>
		<div class="contionar bg5">
			<div class="jiacu">
				1.1 <span>{{ basic.buildingPropertyOwner }}</span
				>简介
			</div>
			<div style="display: flex; justify-content: space-between; padding: 0 60px; width: 100%">
				<div style="width: 50%; height: 300px; text-align: center; line-height: 300px">用户添加图片</div>
			</div>
			<p>用户填写产权人简介</p>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg5">
			<p class="jiacu">
				1.2 <span>{{ basic.buildingManager }}</span
				>经营状况
			</p>
			<img style="width: 100%; height: 300px" :src="url + backdata?.businessSituation?.propertyOwnerShowImg" alt="" />
			<!-- 暂无 -->
			<p>
				截至2024年一季度底，<span>{{ basic.buildingPropertyOwner }}</span
				>总资产达 --亿元，总负债--亿元，报告期内实现营业收入--亿元，较上年同期减少--%，净利润--万元，较上年减少了--%。
			</p>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg5">
			<p class="jiacu">1.3标的资产情况</p>
			<!-- 暂无 -->
			<div class="flex container-1-3">
				<div class="left-container">
					<p>
						{{ backdata?.assets?.buildingSummary }}
					</p>
					<p>
						根据<span>术木智能信息技术（青岛）有限公司</span> 所出具的房地产初评意见书，本次拟入池的<span>{{
							basic.buildingUniqueCode_dictText
						}}</span
						>估价总值为{{ backdata?.assets?.evaluateTotal }}元。
					</p>
				</div>
				<div class="right-container">
					<div style="display: flex; justify-content: space-between">
						<!--  -->
						<img
							style="width: calc(100% / 3 - 20px); height: 200px"
							v-for="(item, index) in basic?.buildingImgUrls?.split(',')"
							:key="index"
							:src="url + item"
							alt=""
						/>
					</div>
					<div style="display: flex; flex-wrap: wrap">
						<p class="justify">
							物业名称：<span class="red">{{ backdata?.assets?.propertyOwner }}</span>
						</p>
						<p class="justify">
							开业时间：<span class="red">{{ backdata?.assets?.openingDate }}</span>
						</p>
						<p class="justify">
							建筑面积：<span class="red">{{ backdata?.assets?.buildingArea }}</span>
						</p>
						<p class="justify">
							产权归属：<span class="red">{{ backdata?.assets?.propertyOwnerAttribution }}</span>
						</p>
						<p class="justify">
							物业运营方：<span class="red">{{ backdata?.assets?.propertyActualOwner }}</span>
						</p>
						<p class="justify">
							物业收入：<span class="red">{{ backdata?.assets?.propertyOwnerRevenue }}</span>
						</p>
						<p class="justify">
							EBITDA率：<span class="red">{{ backdata?.assets?.ebitda }}</span>
						</p>
					</div>
				</div>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg6"></div>
		<div class="contionar bg7"></div>
		<div class="contionar bg8">
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg9">
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg10">
			<p class="box-10-02">3年期主体AAA级PPN发行利率高于非公开公司债20-30BP， 而3年期债项AAA级ABS产品的发行利率略低于同期发行的非公开公司债券。</p>
			<p class="box-10-03">
				综合以上分析，并对证券现金流的转付结构、加权平均期限等因素进行微调，初步估计本次ABS优先级三年期产品<span>发行利率区间为</span
				>{{ lilv.interestRateInterval }}。
			</p>
			<p class="box-10-04">
				根据利率期限相匹配的原则，预计本次ABS优先级资产支持证券的<span>发行利率为</span>优先A1类{{ lilv.interestRateAAA }}%,优先A2A3类{{
					lilv.interestRateAAPlus
				}}%-{{ lilv.interestRateAA }}%，该利率为根据目前市场情况及上述因素综合预期水平，最终实际发行利率取决于ABS债项评级及发行时市场环境。
			</p>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg11"></div>
		<div class="contionar bg12">
			<p class="jiacu">3.1 拟入池标的资产现金流情况</p>
			<div>
				<div>
					<div style="display: flex">
						<table border="1" cellspacing="0" style="width: 60%">
							<thead>
								<tr>
									<th rowspan="2" style="text-align: center; background-color: #15879e; color: red"></th>
									<th colspan="4" style="text-align: center; background-color: #15879e; color: #fff">资产业态</th>
								</tr>
								<tr style="background-color: #15879e; color: white">
									<!-- <th>状态</th> -->
									<th>面积</th>
									<th>租金</th>
									<th>管理费</th>
									<th>合计</th>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in fencen.rentalSaleFees" :key="index" :style="rowStyle(index)">
									<td>{{ feeTypeLabel(item.feeType) }}</td>
									<td>{{ item.area }}</td>
									<td>{{ item.rent }}</td>
									<td>{{ item.manageFee }}</td>
									<td>{{ item.totalFee }}</td>
								</tr>
							</tbody>
						</table>
						<div style="width: 40%; padding-left: 30px">
							<img style="width: 100%; height: 190px" :src="url + basic?.buildingImgUrls?.split(',')[0]" alt="" />
						</div>
					</div>
					<div style="display: flex; margin-top: 10px">
						<div style="width: 50%">
							<table border="1" cellspacing="0" style="width: 100%">
								<thead>
									<tr>
										<th colspan="4" style="text-align: center; background-color: #15879e; color: #fff">车位</th>
									</tr>
									<tr style="background-color: #15879e; color: white">
										<th>个数</th>
										<th>租金</th>
										<th>管理费</th>
										<th>合计</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>{{ fencen.parkingNum }}</td>
										<td>{{ fencen.parkingFee }}</td>
										<td>{{ fencen.mangeFee }}</td>
										<td>{{ fencen.parkingTotalFee }}</td>
									</tr>
								</tbody>
							</table>
							<table border="1" cellspacing="0" style="width: 100%; margin-top: 10px">
								<thead>
									<tr style="background-color: #15879e; color: white">
										<th>广告费</th>
										<!-- <th>管理费（已售部分）</th> -->
									</tr>
								</thead>
								<tbody>
									<tr>
										<td>{{ fencen.adFee }}</td>
										<!-- <td>{{ fencen.mangeFee }}</td> -->
									</tr>
								</tbody>
							</table>
						</div>
						<div style="width: 50%; padding-left: 30px">
							<img style="width: 100%; height: 190px" :src="url + basic?.buildingImgUrls?.split(',')[1]" alt="" />
						</div>
					</div>
				</div>
				<table border="1" cellspacing="0" style="width: 80%; margin-top: 10px">
					<tbody>
						<tr style="background-color: rgb(218 231 0); color: #000">
							<th rowspan="2" style="width: 18%">{{ fencen.forecastYear }}年预测<br />年收入</th>
							<th>租金</th>
							<th>管理费</th>
							<th>其他</th>
							<th>合计</th>
						</tr>
						<tr>
							<td>{{ fencen.forecastRent }}</td>
							<td>{{ fencen.forecastManageFee }}</td>
							<td>{{ fencen.forecastOtherFee }}</td>
							<td>{{ fencen.forecastTotalFee }}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg13">
			<p class="jiacu">
				3.2 <span>{{ basic.buildingUniqueCode_dictText }}</span
				>未来现金流预测
			</p>
			<div style="padding-left: 15px">
				<p>
					<span>{{ basic.buildingUniqueCode_dictText }}</span
					>未来现金流主要包括商铺、写字楼租金收入和物业管理费收入，地下停车场停车费收入，广告费收入。租金收入构成主要有两部分：一部分是直接向商户收取的租金，另一部分是以<span
						>{{ basic.buildingUniqueCode_dictText }}</span
					>名义对外销售进行销售分成收取的租金。
				</p>
				<p>
					预计{{ basic.buildingUniqueCode_dictText }}在 {{ fencen.futureForecastRemainingYears }}期间租金收入现金流入年增长率分别为{{
						fencen.futureForecastGrowthRate
					}}。
				</p>
				<p>
					预计<span> {{ fencen.futureForecastEstimatedYears }}</span
					>将实现现金流<span style="background-color: yellow"> {{ fencen.futureForecastFutureCashFlows }}</span
					>亿元。
				</p>
			</div>

			<div style="display: flex">
				<img style="width: 50%; height: 270px; padding: 10px" :src="url + basic?.buildingImgUrls?.split(',')[0]" alt="" />
				<img style="width: 50%; height: 270px; padding: 10px" :src="url + basic?.buildingImgUrls?.split(',')[1]" alt="" />
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg14">
			<p class="jiacu">
				3.3 <span>{{ basic.buildingPropertyOwner }}</span> 未来现金流预测（续）
			</p>
			<table border="1" cellspacing="0" cellpadding="10" style="width: 100%; margin-top: 10px">
				<thead>
					<tr style="background-color: #15879e; color: white">
						<th>年份</th>
						<th>资产租金收入</th>
						<th>资产管理费收入</th>
						<th>停车费收入</th>
						<th>广告费收入</th>
						<th>现金流入合计</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="(item, index) in fencen.futureCashFlows" :key="index">
						<td>{{ item.year }}</td>
						<td>{{ item.rentIncome }}</td>
						<td>{{ item.manageFeeRevenue }}</td>
						<td>{{ item.parkingFeeRevenue }}</td>
						<td>{{ item.adFeeRevenue }}</td>
						<td>{{ item.totalCashInflows }}</td>
					</tr>
					<tr style="background-color: #fff">
						<td style="padding: 10px">合计</td>

						<td>{{ totals.rentIncome }}</td>
						<td>{{ totals.manageFeeRevenue }}</td>
						<td>{{ totals.parkingFeeRevenue }}</td>
						<td>{{ totals.adFeeRevenue }}</td>
						<td>{{ totals.totalCashInflows }}</td>
					</tr>
				</tbody>
			</table>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg15"></div>
		<div class="contionar bg16">
			<p class="jiacu">4.1 发行方案</p>
			<div style="display: flex; flex-wrap: wrap">
				<p class="left">产品名称</p>
				<p class="right stripe">
					<strong>{{ basic.buildingUniqueCode_dictText }}资产支持专项计划</strong>
				</p>
				<p class="left">发行总额</p>
				<p class="right">
					<span>{{ arrangement?.issuanceScheme?.totalAmountIssued }}</span> 亿元
				</p>
				<p class="left">证券分层</p>
				<div class="right" style="display: flex; justify-content: space-around">
					<span class="stripe">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.stratified }} </span>
					<div style="width: 2px"></div>
					<span class="stripe">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.stratified }} </span>
				</div>
				<p class="left">规模</p>
				<div class="right" style="display: flex; justify-content: space-around">
					<span>{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.scale }} </span>
					<div style="width: 2px"></div>
					<span>{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.scale }} </span>
				</div>
				<p class="left">期限</p>
				<div class="right" style="display: flex; justify-content: space-around">
					<span class="stripe">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.term }} </span>
					<div style="width: 2px"></div>
					<span class="stripe">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.term }} </span>
				</div>
				<p class="left">预期评级</p>
				<p class="right" style="display: flex; justify-content: space-around">
					<span>{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.expectedRating }} </span
					><span>{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.expectedRating }} </span>
				</p>
				<p class="left">预期收益率</p>
				<div class="right" style="display: flex; justify-content: space-around">
					<span class="stripe">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.expectedIncomeRate }} </span>
					<div style="width: 2px"></div>
					<span class="stripe">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.expectedIncomeRate }} </span>
				</div>
				<p class="left">信用增级方式</p>
				<p class="right" style="text-align: left">{{ arrangement?.issuanceScheme?.creditEnhancement }}</p>
				<p class="left">期限结构</p>
				<div class="right stripe" style="text-align: left">
					{{ arrangement?.issuanceScheme?.termStructure }}
				</div>
				<p class="left">基础资产</p>
				<p class="right" style="text-align: left">{{ arrangement?.issuanceScheme?.underlyingAssets }}</p>
				<p class="left">募集资金用途</p>
				<p class="right stripe" style="text-align: left">{{ arrangement?.issuanceScheme?.useProceeds }}</p>
				<p class="left">还本付息方式</p>
				<p class="right" style="text-align: left">{{ arrangement?.issuanceScheme?.interestPaymentMethod }}</p>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg17" style="position: relative">
			<p class="jiacu">4.2 交易结构</p>
			<div style="display: flex">
				<div class="jiegou_left" style="width: 65%">
					<span class="bg17-abs-1">{{ basic.buildingUniqueCode_dictText }}抵押贷款资产支持专项计划 </span>
					<span class="bg17-abs-2">{{ backdata?.assets?.propertyOwner }}提供担保 </span>
				</div>
				<div style="width: 35%; margin-top: 20px">
					<div class="minjiacu" style="width: 40%; border: 1px dashed #18a3f2">
						<div class="vertical_level_center"><img style="width: 60%" src="@/assets/images/zhengquan/solid-arrow.jpg" alt="" />行为</div>
						<div class="vertical_level_center"><img style="width: 60%" src="@/assets/images/zhengquan/dashed-arrow.jpg" alt="" />资金</div>
					</div>
					<p style="font-size: 22px; border: 1px solid #168ea6; padding: 5px">资管计划单层模式结构介绍</p>
					<p>
						1.原始权益人发放借款给融资人形成债权，物业抵押/资产收益权质押给资产支持专项计划，原始权益人与专项资产管理计划签订《基础资产转让协议》，将资产收益权转让给专项资产管理计划，并提供相应的增信措施(权利维持费&差额补足承诺&回购)；
					</p>
					<p>2.计划管理人设立专项资产管理计划，与投资人签订协议，募集合格投资者资金，转让专项资产管理计划份额；</p>
					<p>3.中证登机构登记托管合格投资者份额，托营银行保管相应合同，到期分配收益。</p>
				</div>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg18">
			<p class="jiacu">4.3 交易流程</p>
			<div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						01
					</p>
					<p>
						认购人通过与管理人签订《认购协议》，将认购资金以专项资产管理方式委托管理人管理，管理人设立并管理专项计划，认购人取得资产支持证券，成为资产支持证券持有人。
					</p>
				</div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						02
					</p>
					<p style="width: 75%">
						管理人根据《基金份额转让协议》的约定，在专项计划设立日向托管人发出付款指令，指示托管人将专项计划发行收入中，等额于基金份额购买价款的部分划拨至原始权益人指定的账户，用于购买基金份额。托管人应根据《基金份额转让协议》及《托管协议》的约定对付款指令中的资金用途及金额进行核对，核对无误后予以付款。
					</p>
				</div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						03
					</p>
					<p>
						管理人购买基金份额后，即成为私募投资基金的LP基金份额持有人，应根据《基金合同》及《基金份额转让协议》的约定，履行实缴基金出资的义务。管理人应于专项计划设立日向托管人发出划款指令，指示托管人将专项计划发行收入中，等额于《基金合同》和《基金份额转让协议》约定的基金出资部分金额划拨至基金账户，托管人核对无误后予以划款。
					</p>
				</div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						04
					</p>
					<p style="width: 80%">
						在每个物业资产运营收入计算日，物业持有人根据《监管协议》的约定，与管理人、基金管理人核实{{
							basic.buildingUniqueCode_dictText
						}}监管账户当期归集的租金、物业管理费等物业资产运营收入及其他收入情况。
					</p>
				</div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						05
					</p>
					<p>在每个物业持有人报告日，物业持有人应向管理人、基金管理人和监管银行出具《当期物业运营报告》。</p>
				</div>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg19">
			<p class="jiacu">4.3 交易流程（续）</p>
			<div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						06
					</p>
					<p>
						在每个物业持有人转付日，监管银行应根据《监管协议》的约定，将该日对应的租金回收期的物业资产运营收入及其他收入，在扣除运营税费后，按顺次将等额于债权收益权当期应付的部分转入基金账户，股东分红转入基金账户，基金托管人在收到物业持有人转付的资金后，应向基金管理人报告基金账户资金到账情况。
					</p>
				</div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						07
					</p>
					<p>
						在每个基金管理人报告日，基金管理人应根据《基金合同》的约定，向专项计划管理人出具有限合伙基金的《当期投资运作报告》及《份额利益分配报告》，并向基金托管人发出划款指令。在每个私募基金转付日，基金管理人应根据《基金合同》和《基金份额转让协议》，指示基金托管人将基金份额分配利益转入专项计划账户。
					</p>
				</div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						08
					</p>
					<p>在权利维持费支付日，优先收购权人应根据《优先收购权协议》的相关规定，将当期实付权利维持费转入专项计划账户。</p>
				</div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						09
					</p>
					<p>
						管理人按照《标准条款》规定的分配顺序拟定当期收入分配方案，制作《收益分配报告》和《资产管理报告》，并于管理人报告日对投资者进行披露。管理人于划款指令发送日向托管人发送划款指令，托管人将当期专项计划应分配的资产支持证券所有收益和本金划入登记托管机构指定账户。
					</p>
				</div>
				<div class="liucheng">
					<p
						style="
							display: inline-block;
							text-align: center;
							background-color: #168ea6;
							color: #fff;
							margin-right: 20px;
							width: 37px;
							height: 37px;
							border-radius: 58%;
						"
					>
						10
					</p>
					<p>
						在每个兑付日，登记托管机构应将相应款项划拨至各证券公司结算备付金账户，各证券公司根据登记托管机构结算数据中的预期支付的明细数据将相应款项划拨至资产支持证券持有人资金账户。
					</p>
				</div>
			</div>

			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg20">
			<p class="jiacu">4.4 交易参与方</p>
			<div style="display: flex; flex-wrap: wrap" class="box-20">
				<p class="left">计划管理人/销售推广机构</p>
				<p class="right stripe">{{ arrangement?.planManager }}</p>
				<p class="left">原始权益人/LP基金份额初始持有人</p>
				<p class="right">{{ arrangement?.originalOwner }}</p>
				<p class="left">项目公司/资产服务机构</p>
				<p class="right stripe">{{ arrangement?.projectCompany }}</p>
				<p class="left">基金管理人</p>
				<p class="right">{{ arrangement?.fundManager }}</p>
				<p class="left">监管银行</p>
				<p class="right stripe">{{ arrangement?.superviseBank }}</p>
				<p class="left">基金托管银行</p>
				<p class="right">{{ arrangement?.fundCustodianBank }}</p>
				<p class="left">专项计划托管银行</p>
				<p class="right stripe">{{ arrangement?.dedicatedPlanCustodianBank }}</p>
				<p class="left">流动性支持承诺人</p>
				<p class="right">{{ arrangement?.supportPledge }}</p>
				<p class="left">优先收购权人</p>
				<p class="right stripe">{{ arrangement?.acquirer }}</p>
				<p class="left">法律顾问</p>
				<p class="right">{{ arrangement?.legalAdviser }}</p>
				<p class="left">评级机构</p>
				<p class="right stripe">{{ arrangement?.ratingAgencies }}</p>
				<p class="left">会计师事务所</p>
				<p class="right">{{ arrangement?.accountingFirms }}</p>
				<p class="left">评估机构</p>
				<p class="right stripe">{{ arrangement?.evaluationAgency }}</p>
				<p class="left">登记托管机构</p>
				<p class="right">{{ arrangement?.regCustodians }}</p>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg21">
			<p class="jiacu">4.5 增信安排说明</p>
			<div style="line-height: 40px; display: flex">
				<p style="margin-right: 20px; width: 70%">
					专项计划设置优先级和次级，次级部分由利群股份或集团内其他主体购买。优先级分为优先A及优先B两个层级，<span>
						总规模<span>{{ arrangement.totalScale }}</span> 亿元。</span
					>
				</p>
				<p>优先-次级分层</p>
			</div>
			<div style="line-height: 40px; display: flex">
				<p style="margin-right: 20px; width: 70%">
					专项计划质押财产（<span>{{ basic.buildingUniqueCode_dictText }}</span> 未来<span> {{ arrangement.remainingYears }}</span
					>年的租金收入及其他物业运营收入）对优先级A级资产支持证券本息的超额覆盖倍数为1.0以上，可有效防范基础资产回收现金流不足风险。
				</p>
				<p>超额现金流覆盖</p>
			</div>
			<div style="line-height: 40px; display: flex">
				<p style="margin-right: 20px; width: 70%">
					若触发差额支付启动事件，差额支付承诺人根据计划管理人的指令向专项计划账户划付资金，对基础资产回收款不足以支付专项计划费用、优先级资产支持证券的预期收益和本金的差额部分承担补足义务。
				</p>
				<p>
					<span>{{ basic.buildingPropertyOwner }}</span
					>差额支付支持
				</p>
			</div>
			<div style="line-height: 40px; display: flex">
				<p style="margin-right: 20px; width: 70%">
					为扩大项目融资规模，本方案基于目标物业运营总收入一定折扣进行预测，专项计划存续期间，<span>{{ basic.buildingPropertyOwner }}</span>
					将对<span>{{ basic.buildingUniqueCode_dictText }}</span
					>相关运用成本提供流动性支持；此外，在专项计划存续期每个拆期节点，如果发生投资者集中回售情况，<span>{{ basic.buildingPropertyOwner }}</span
					>应予以提供流动性支持。
				</p>
				<p>
					<span>{{ basic.buildingPropertyOwner }}</span
					>流动性支持
				</p>
			</div>
			<div style="line-height: 40px; display: flex">
				<p style="margin-right: 20px; width: 70%">
					在触发到期处置目标物业退出方式时，利群股份应对处置资产不足以支付优先级预期收益及本金部分提供差额补足支持。
				</p>
				<p>到期处置价格差额补足</p>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg22">
			<p class="jiacu">4.6 特殊条款</p>
			<div style="display: flex">
				<img src="../../assets/images/zhengquan/one.jpg" alt="" />
				<div style="padding-top: 20px; margin-left: 32px">
					<p class="bold-weight">股权回购条款</p>
					<p style="height: 80%">
						专项计划存续期间 <span>{{ arrangement.planYear }}，{{ arrangement.remainingUsefulLife }}</span
						>利群股份或指定第三方有权从私募投资基金回购部分或全部项目公司股权，回购资金用于完成优先级资产支持证券部分或全部本息偿付。
					</p>
				</div>
			</div>
			<div style="display: flex">
				<img src="../../assets/images/zhengquan/two.jpg" alt="" />
				<div style="padding-top: 20px">
					<p class="bold-weight">票面利率调整权</p>
					<p style="height: 80%">
						在每个拆期节点
						<span>({{ arrangement.remainingUsefulLife }})</span
						>，如果利群股份或指定第三方未行使股权回购权利，则可选择在当前拆期节点是否上调未清偿资产支持证券票面利率及调整额度。
					</p>
				</div>
			</div>
			<div style="display: flex">
				<img src="../../assets/images/zhengquan/three.jpg" alt="" />
				<div style="padding-top: 20px">
					<p class="bold-weight">股权回购条款</p>
					<p style="height: 80%">
						在每个拆期节点(<span> {{ arrangement.remainingUsefulLife }}</span
						>)，利群股份或指定第三方发出是否回购及是否调整票面利率公告后，若仍有未清偿资产支持证券存续，投资者可选择是否将所持有的部分或全部证券进行回售，利群股份对于投资者回售承担流动性支持义务。
					</p>
				</div>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg23">
			<p class="jiacu">4.7 退出渠道</p>
			<div style="padding: 20px 15%">
				<p style="margin-bottom: 20px">
					本方案在每个拆期节点上赋予<span>{{ basic.buildingPropertyOwner }}</span>
					或其指定第三方从私募房地产投资基金回购项目公司股权的方式实现REITs本金的退出。具体有如下三种方式：
				</p>
				<div style="display: flex; margin: 30px 0; margin-bottom: 20px; line-height: 37px">
					<div class="vertical_level_center bold-weight" style="width: 11%; padding: 5px; margin-right: 10px; background: rgb(204, 236, 255)">
						股权回购
					</div>
					<div style="width: 89%">
						<span>{{ basic.buildingPropertyOwner }}</span
						>或指定第三方按照当期拆期节点公允价值或事先约定价值回购项目公司股权，所支付的资金用于优先级资产支持证券的本息兑付。
					</div>
				</div>
				<div style="display: flex; margin: 30px 0; line-height: 37px">
					<div
						class="vertical_level_center bold-weight"
						style="width: 11%; color: #fff; padding: 5px; margin-right: 10px; background: rgb(1, 135, 172)"
					>
						公募REITs
					</div>
					<div style="width: 89%">
						在业内呼吁和酝酿多年之后，我国面向个人投资者的公募REITs产品的启动已经进入轨道。在相关政策出台后，可通过发行公募REITs产品市场化出售基金份额或项目公司股权等来实现退出。
					</div>
				</div>
				<div style="display: flex; margin: 30px 0; line-height: 37px">
					<div
						class="vertical_level_center bold-weight"
						style="width: 11%; color: #fff; padding: 5px; margin-right: 10px; background: rgb(180, 180, 180)"
					>
						标的物业处置
					</div>
					<div style="width: 89%">
						在<span>{{ basic.buildingPropertyOwner }}</span
						>希望转让标的物业权属的情况下，产品到期时，计划管理人、基金管理人及<span>{{ basic.buildingPropertyOwner }}</span
						>可协商在市场处置标的物业，并以出售对价实现优先A、优先B和次级投资人的依次退出。
					</div>
				</div>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg24">
			<p class="jiacu">4.8 税务处理</p>
			<div>
				<p style="margin-bottom: 20px">环节一：资产原持有人以目标不动产出资新设特殊目的公司(SPV)</p>
				<img style="width: 90%" src="../../assets/images/zhengquan/shuione.jpg" alt="" />
				<p style="margin-bottom: 20px">环节二：资产持有人将SPV股权转让给私募房地产投资基金</p>
				<img style="width: 90%" src="../../assets/images/zhengquan/shuitwo.jpg" alt="" />
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg25">
			<p class="jiacu">4.8 税务处理(续)</p>
			<div>
				<p style="margin-bottom: 20px">环节三：私募房地产投资基金份额证券化</p>
				<img style="width: 90%" src="../../assets/images/zhengquan/shuithr.jpg" alt="" />
				<p style="margin-bottom: 20px">环节四：退出环节，私募房地产投资基金处置项目公司股权</p>
				<img style="width: 90%" src="../../assets/images/zhengquan/shuifou.jpg" alt="" />
			</div>

			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg26">
			<p class="jiacu">4.9 账户设置及现金流归集</p>
			<div style="display: flex">
				<div style="width: 45%; display: flex; flex-direction: column; justify-content: space-evenly; line-height: 23px">
					<div style="display: flex">
						<p style="width: 20%; margin-right: 10px; background: rgb(204, 236, 255); height: 60px" class="vertical_level_center bold-weight">
							监管账户
						</p>
						<p style="width: 70%">
							系指资产服务机构开立的专门用于接收物业资产产生的租金等物业资产运营收入及其他收入，并对外进行支付的人民币资金账户。
						</p>
					</div>
					<div style="display: flex">
						<p style="width: 20%; margin-right: 10px; background: rgb(204, 236, 255); height: 60px" class="vertical_level_center bold-weight">
							基金账户
						</p>
						<p style="width: 70%">系指基金管理人在基金托管人处开立的私募基金银行账户，基金的相关货币收支活动应通过该账户进行。</p>
					</div>
					<div style="display: flex">
						<p style="width: 20%; margin-right: 10px; background: rgb(204, 236, 255); height: 60px" class="vertical_level_center bold-weight">
							募集资金专户
						</p>
						<p style="width: 70%">系指管理人开立的专用于发行期接收、存放投资者交付的认购资金的人民币资金账户。</p>
					</div>
					<div style="display: flex">
						<p style="width: 20%; margin-right: 10px; background: rgb(204, 236, 255); height: 60px" class="vertical_level_center bold-weight">
							专项计划账户
						</p>
						<p style="width: 70%">
							系指管理人以专项计划名义在托管人开立的人民币资金账户。专项计划的相关货币收支活动，包括但不限于接收募集资金专户划付的认购资金、支付基础资产购买价款、实缴基金出资、接收所转付的回收款及其他应属专项计划的款项等，均需通过该账户进行。
						</p>
					</div>
				</div>
				<div style="width: 55%">
					<img style="width: 100%" src="../../assets/images/zhengquan/wuye.jpg" alt="" />
				</div>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg27">
			<p class="jiacu">4.10 现金流分配流程（续）</p>
			<p style="margin-bottom: 20px; width: 90%">
				私募投资基金层面优先保障基金份额持有人利益；资产支持专项计划层面依次优先保障优先A类投资人、优先B类投资人及次级投资人利益。
			</p>
			<img style="width: 90%" src="../../assets/images/zhengquan/fenpei.jpg" alt="" />
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg28">
			<!-- <div style="display: flex;justify-content: space-between;padding: 0 60px;">
				<div>
					<p class="jiacu">PART
					</p>
					<p class="jiacu">01</p>
				</div>
				<div>
					<p style="font-size: 28px;font-weight: bold;">本次类REITs融资服务方案
					</p>
					<p>1 项目背景介绍</p>
					<p>2 发行利率估算
					</p>
					<p>3 产品分层测算
					</p>
					<p>4 交易结构安排
					</p>
					<p class="sele">5 发行场所与流程
					</p>
				</div>
			</div> -->
		</div>
		<div class="contionar bg29">
			<p class="jiacu">5.1 上交所发行流程</p>
			<div style="width: 90%">
				<p style="font-size: 24px; font-weight: 700">上交所审核与备案流程</p>
				<p>
					上交所颁布的《资产证券化业务指南》，对资产证券化业务操作做出了细致规定。其后在此版本的基础上又进行了修改，ABS产品发行并在上交所挂牌流程如下图所示。
				</p>
				<img style="width: 100%" src="../../assets/images/zhengquan/faxing.jpg" alt="" />
			</div>

			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg30">
			<p class="jiacu">5.2 深交所发行流程</p>
			<div style="width: 90%">
				<p style="font-size: 24px; font-weight: 700">深交所审核与备案流程</p>
				<p>
					深交所发布修订版《资产证券化业务问答》，从提出符合挂牌条件申请到完成挂牌的完整流程包括挂牌条件确认、专项计划发行、基金业协会备案以及挂牌申请核对等环节均进行了详细规定。
					ABS产品发行并在深交所挂牌流程如下图所示。
				</p>
				<img style="width: 100%" src="../../assets/images/zhengquan/shenjiao.jpg" alt="" />
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg31"></div>
		<div class="contionar bg32"></div>
		<div class="contionar bg36">
			<p class="jiacu">1.1中介机构费用</p>
			<table style="width: 100%">
				<tbody>
					<tr height="54">
						<td class="kso1">收费主体</td>
						<td class="kso1">费用名称</td>
						<td class="kso1">收费标准</td>
						<td class="kso1">收取方式</td>
					</tr>
					<tr height="54">
						<td class="kso23">计划管理人</td>
						<td class="kso23">承销费和计划管理费</td>
						<td class="kso23">商定</td>
						<td class="kso23">一般在发行完成后一次性收取</td>
					</tr>
					<tr height="54">
						<td class="kso23">律师事务所</td>
						<td class="kso23">律师费</td>
						<td class="kso23">70万-100万</td>
						<td class="kso23">发行前收取50%-100%</td>
					</tr>
					<tr height="54">
						<td class="kso23" rowspan="2">评估机构</td>
						<td class="kso23">初始资产评估费</td>
						<td class="kso23">30万左右</td>
						<td class="kso23">发行前全额一次性收取</td>
					</tr>
					<tr height="54">
						<td class="kso23">跟踪评估费</td>
						<td class="kso23">5万/次</td>
						<td class="kso23">按次收取</td>
					</tr>
					<tr height="54">
						<td class="kso23" rowspan="2">评级机构</td>
						<td class="kso23">初始信用评级费</td>
						<td class="kso23">25万-35万</td>
						<td class="kso23">发行前全额一次性收取</td>
					</tr>
					<tr height="54">
						<td class="kso23">跟踪评级费</td>
						<td class="kso23">5万/年</td>
						<td class="kso23">按年收取</td>
					</tr>
					<tr height="54">
						<td class="kso23" rowspan="2">会计师事务所</td>
						<td class="kso23">现金流预测报告和设立验资报告费用</td>
						<td class="kso23">15万-25万</td>
						<td class="kso23">发行前收取50%-100%</td>
					</tr>
					<tr height="54">
						<td class="kso23">专项计划年度审计费和清算报告审计费</td>
						<td class="kso23">3万-5万/年</td>
						<td class="kso23">按年收取</td>
					</tr>
				</tbody>
			</table>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg34"></div>
		<div class="contionar bg35">
			<p class="jiacu">2.1 整体安排</p>
			<p class="minjiacu">本次ABS操作流程整体安排</p>
			<div style="margin-left: 60px">
				<div class="abs">
					<span style="width: 20%; background-color: #15879e; color: #fff">第一阶段</span>
					<span style="width: 40%; background-color: #f5f5f5; margin-right: 2px">前期准备阶段</span>
					<span style="width: 39%; background-color: #f5f5f5">1.5周 </span>
				</div>
				<div class="abs">
					<span style="width: 20%; background-color: #15879e; color: #fff">第二阶段</span>
					<span style="width: 40%; margin-right: 2px">尽职调查、申报材料制作及公司履行内部流程阶段</span>
					<span style="width: 39%">4周 </span>
				</div>
				<div class="abs">
					<span style="width: 20%; background-color: #15879e; color: #fff">第三阶段 </span>
					<span style="width: 40%; background-color: #f5f5f5; margin-right: 2px">主管部门审批阶段 </span>
					<span style="width: 39%; background-color: #f5f5f5">预计4周内获批 </span>
				</div>
				<div class="abs">
					<span style="width: 20%; background-color: #15879e; color: #fff">第四阶段 </span>
					<span style="width: 40%; margin-right: 2px">ABS发行阶段</span>
					<span style="width: 39%">取得批文后择机发行 </span>
				</div>
			</div>

			<p class="minjiacu">主要申报材料分工安排</p>
			<div style="margin-left: 60px">
				<p>计划说明书——中介机构</p>
				<p>法律意见书——律师事务所</p>
				<p>全套交易合同文件——律师事务所</p>
				<p>评级报告——评级机构</p>
				<p>评估报告——评估机构</p>
				<p>现金流预测报告——会计师事务所</p>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg36">
			<p class="jiacu">2.2 具体日程安排（一）</p>

			<table style="width: 100%">
				<tbody>
					<tr height="43">
						<td class="kso1">关键时间点</td>
						<td class="kso1">工作内容</td>
						<td class="kso1">责任方</td>
					</tr>
					<tr height="167">
						<td class="kso9">2周</td>
						<td class="kso9-1">
							{{ basic.buildingPropertyOwner }}内部沟通、汇报ABS情况<br />
							梳理基础资产，进行初步挑选并提出入池建议
						</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }} 中介机构</td>
					</tr>
					<tr height="167">
						<td class="kso9">2周</td>
						<td class="kso9-1">
							商定产品结构设计，确定初步沟通方案<br />
							中介机构就初步方案与监管机构进行预沟通
						</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }} 中介机构</td>
					</tr>
					<tr height="167">
						<td class="kso9">2周</td>
						<td class="kso9-1">
							召开中介机构协调会，确定项目进度安排等相关事项<br />
							中介机构提供尽职材料清单给{{ basic.buildingPropertyOwner }}<br />
							{{ basic.buildingPropertyOwner }}内部开始准备相关的董事会决议
						</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }}中介机构</td>
					</tr>
				</tbody>
			</table>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg36">
			<p class="jiacu">2.2 具体日程安排（二）</p>
			<table style="width: 100%">
				<tbody>
					<tr height="44">
						<td class="kso1">关键时间点</td>
						<td class="kso1">工作内容</td>
						<td class="kso1">责任方</td>
					</tr>
					<tr height="72">
						<td class="kso9" rowspan="2">1周</td>
						<td class="kso9-1">{{ basic.buildingPropertyOwner }}准备尽调清单文件</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }}</td>
					</tr>
					<tr height="114">
						<td class="kso9-1">中介机构开始现场尽职调查，{{ basic.buildingPropertyOwner }}配合各 中介机构尽职调查工作，提供相关数据和材料</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }}&#xb;中介机构</td>
					</tr>
					<tr height="85">
						<td class="kso9" rowspan="3">1周</td>
						<td class="kso9-1">{{ basic.buildingPropertyOwner }}就本次ABS发行相关工作履行内部程序</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }}</td>
					</tr>
					<tr height="77">
						<td class="kso9-1">中介机构与律所共同讨论、起草交易文件协议</td>
						<td class="kso9">中介机构 律所</td>
					</tr>
					<tr height="152">
						<td class="kso9-1">
							评级机构开始进行评级 <br />中介机构开始撰写计划说明书<br />
							律所开始撰写法律意见书<br />
							会计师事务所开始进行现金流分析
						</td>
						<td class="kso9">中介机构</td>
					</tr>
				</tbody>
			</table>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg36">
			<p class="jiacu">2.2 具体日程安排（三）</p>
			<table style="width: 100%">
				<tbody>
					<tr height="45">
						<td class="kso1">关键时间点</td>
						<td class="kso1">工作内容</td>
						<td class="kso1">责任方</td>
					</tr>
					<tr height="183">
						<td class="kso9">1周</td>
						<td class="kso9-1">
							评级机构定稿评级报告<br />
							律师定稿法律意见书 <br />会计师完成现金流分析报告<br />
							中介机构定稿申报材料、交易文件协议
						</td>
						<td class="kso9">中介机构</td>
					</tr>
					<tr height="141">
						<td class="kso9">1周</td>
						<td class="kso9-1">
							{{ basic.buildingPropertyOwner }}完成本次申请发行ABS所有申报文件<br />
							中介机构完成公司内核会流程
						</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }} 中介机构</td>
					</tr>
					<tr height="105">
						<td class="kso9">项目启动 <br />9周内</td>
						<td class="kso9-1">
							{{ basic.buildingPropertyOwner }}就相关协议文件内部报批<br />
							完成整套发行ABS申报材料并报送交易所
						</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }} 中介机构</td>
					</tr>
					<tr height="106">
						<td class="kso9">项目启动 <br />12周内<br />交易所审批</td>
						<td class="kso9-1">
							中介机构配合{{ basic.buildingPropertyOwner }}对交易所反馈进行答复<br />
							预计1个月内获得无异议函
						</td>
						<td class="kso9">{{ basic.buildingPropertyOwner }} 中介机构</td>
					</tr>
				</tbody>
			</table>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg36">
			<p class="jiacu">2.2 具体日程安排（四）</p>

			<table style="width: 100%">
				<tbody>
					<tr height="46">
						<td class="kso1">关键时间点</td>
						<td class="kso1">工作内容</td>
						<td class="kso1">责任方</td>
					</tr>
					<tr height="192">
						<td class="kso7">无异议后 <br />择机发行</td>
						<td class="kso9-1">
							取得交易所无异议函后，安排组织路演推介 <br />
							中介机构与{{ basic.buildingPropertyOwner }}确定发行区间 <br />
							选择合适市场时机完成发行，募集资金划至指定账户
						</td>
						<td class="kso7">{{ basic.buildingPropertyOwner }} 中介机构</td>
					</tr>
					<tr height="97">
						<td class="kso7">备案与上市</td>
						<td class="kso9-1">
							发行完成后向基金业协会备案 <br />
							交易所申请上市
						</td>
						<td class="kso7">{{ basic.buildingPropertyOwner }} 中介机构</td>
					</tr>
				</tbody>
			</table>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg37"></div>
		<div class="contionar bg38">
			<p class="jiacu">1 团队组织及工作机制</p>
			<img style="width: 100%" src="../../assets/images/zhengquan/tuandui.jpg" alt="" />
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
		<div class="contionar bg39">
			<div class="center">
				<!-- <p style="margin-left: 40px; font-size: 20px">
					敬呈：<span style="font-weight: bold">{{ basic.buildingPropertyOwner }}</span>
				</p> -->
				<div class="THANKS" style="font-size: 36px; line-height: 60px">
					<span>THANKS </span>
					<div>感谢您的观看</div>
				</div>
				<div class="date-box" style="display: flex; font-size: 20px; align-items: center">
					<span>{{}}机构暂无</span>
					<el-icon style="margin: 0 20px; color: #ea0101; font-size: 30px">
						<CaretRight />
					</el-icon>
					<span>{{ basic.reportDownloadTime }}</span>
				</div>
			</div>
			<img style="width: 80px; height: 70px" class="propertyOwnerLogo" :src="url + basic?.propertyOwnerLogo" fit="cover" />
		</div>
	</div>
</template>
<script setup>
import { useRoute } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import {
	securitybaisc,
	securityinterestRateEstimate,
	securitybaiscCalculation,
	securityVenueProcess,
	securityBackground,
	securityArrangement,
} from '../../api/baogao';
const prodess = ref('https://oa.biaobiaozhun.com/sm/sys/common/static');
const url = ref('https://static.biaobiaozhun.com/');
const basic = ref({});
const router = useRoute();
const lilv = ref({});
const fencen = ref({ futureCashFlows: [] });
const venue = ref({});
const backdata = ref({});
const arrangement = ref({});
const totals = computed(() => {
	let rentIncome = 0;
	let manageFeeRevenue = 0;
	let parkingFeeRevenue = 0;
	let adFeeRevenue = 0;
	let totalCashInflows = 0;
	if (fencen?.value?.futureCashFlows) {
		fencen.value.futureCashFlows.forEach((flow) => {
			rentIncome += Number(flow.rentIncome.replace(/,/g, '')) || 0;
			manageFeeRevenue += Number(flow.manageFeeRevenue.replace(/,/g, '')) || 0;
			parkingFeeRevenue += Number(flow.parkingFeeRevenue.replace(/,/g, '')) || 0;
			adFeeRevenue += Number(flow.adFeeRevenue.replace(/,/g, '')) || 0;
			totalCashInflows += Number(flow.totalCashInflows.replace(/,/g, '')) || 0;
		});
	}
	return {
		rentIncome: rentIncome.toLocaleString(),
		manageFeeRevenue: manageFeeRevenue.toLocaleString(),
		parkingFeeRevenue: parkingFeeRevenue.toLocaleString(),
		adFeeRevenue: adFeeRevenue.toLocaleString(),
		totalCashInflows: totalCashInflows.toLocaleString(),
	};
});
const getbasic = async () => {
	const res = await securitybaisc({ buildingId: router.query.id });
	if (res.code == 200) {
		basic.value = res.result;
	}
	const lilvres = await securityinterestRateEstimate({ buildingId: router.query.id });
	if (lilvres.code == 200) {
		lilv.value = lilvres.result;
	}
	const fenceres = await securitybaiscCalculation({ buildingId: router.query.id });
	if (fenceres.code == 200) {
		fencen.value = fenceres.result;
	}
	const venueProcess = await securityVenueProcess({ buildingId: router.query.id });
	if (venueProcess.code == 200) {
		venue.value = venueProcess.result;
	}
	const backres = await securityBackground({ buildingId: router.query.id });
	if (backres.code == 200) {
		backdata.value = backres.result;
	}
	const arrangementres = await securityArrangement({ buildingId: router.query.id });
	if (arrangementres.code == 200) {
		arrangement.value = arrangementres.result;
	}
	console.log(backdata.value);

	console.log(arrangement.value);
};
const feeTypeLabel = (feeType) => {
	switch (feeType) {
		case '1':
			return '待租';
		case '2':
			return '已租';
		case '3':
			return '合计';
		default:
			return '';
	}
};
// 样式处理
const rowStyle = (index) => {
	if (index === 1) {
		return {
			backgroundColor: '#f5f5f5',
			color: '#00ffff', // 青色字体
		};
	}
	return {};
};
onMounted(() => {
	getbasic();
});
</script>
<style lang="less" scoped>
.abs {
	span {
		display: inline-block;
		height: 40px;
		line-height: 40px;
		text-align: center;
	}
}

.abs1 {
	display: flex;
}

.bold-weight {
	font-weight: bold; /* 等同于 font-weight: 700; */
}

th,
td {
	text-align: center;
	padding: 7px;
}

.left {
	text-align: center;
	width: 30%;
	background-color: #168ea6;
	color: white;
	font-weight: 700;
	margin: 0 0 1px 0;
	line-height: 1.5;
}

.right {
	margin: 0;
	text-align: center;
	width: 70%;

	span {
		width: 50%;
		// margin: 0 5px;
	}
}

th {
	font-size: 18px;
	font-weight: bold;
}

tbody tr:nth-child(odd) {
	background-color: #e5f7f7;
}

tbody tr:nth-child(even) {
	background-color: #ffffff;
}

.main {
	// width: 1150px;
	padding: 20px 300px;

	.top {
		height: 600px;
		background-image: url(@/assets/images/security/bg1.jpg);
		background-size: 100% 100%;
		position: relative;

		.center {
			padding-top: 12%;
		}

		.propertyOwnerLogo {
			position: absolute;
			left: 65%;
			bottom: 10px;
		}
	}

	.bg2 {
		height: 600px;
		background-image: url(@/assets/images/security/bg2.jpg);
		background-size: 100% 100%;
		position: relative;

		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}

	.bg3 {
		height: 600px;
		background-image: url(@/assets/images/security/bg3.jpg);
		background-size: 100% 100%;
	}

	.bg4 {
		height: 600px;
		background-image: url(@/assets/images/security/bg4.jpg);
		background-size: 100% 100%;
	}

	.bg5 {
		height: 600px;
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		position: relative;

		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}

		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}

		.container-1-3 {
			.left-container {
				width: 50%;
				padding: 0 20px;
			}

			.right-container {
				width: 50%;
			}
		}
	}

	.bg6 {
		height: 600px;
		background-image: url(@/assets/images/security/bg6.jpg);
		background-size: 100% 100%;
	}

	.bg7 {
		height: 600px;
		background-image: url(@/assets/images/security/bg7.jpg);
		background-size: 100% 100%;
	}

	.bg8 {
		height: 600px;
		background-image: url(@/assets/images/security/bg8.jpg);
		background-size: 100% 100%;
		position: relative;

		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}

	.bg9 {
		height: 600px;
		background-image: url(@/assets/images/security/bg9.jpg);
		background-size: 100% 100%;
		position: relative;

		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}

	.bg10 {
		height: 600px;
		background-image: url(@/assets/images/security/bg10.jpg);
		background-size: 100% 100%;
		position: relative;

		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
		.box-10-02 {
			position: absolute;
			bottom: 15%;
			left: 18%;
			width: 28%;
		}
		.box-10-03 {
			position: absolute;
			top: 15%;
			left: 46%;
			width: 32%;
		}
		.box-10-04 {
			position: absolute;
			bottom: 15%;
			right: 10%;
			width: 28%;
		}
	}
	.bg11 {
		height: 600px;
		background-image: url(@/assets/images/security/bg11.jpg);
		background-size: 100% 100%;
	}
	.bg12 {
		height: 600px;
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		position: relative;

		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}
		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}
	.bg13 {
		height: 600px;
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		position: relative;
		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}
		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}
	.bg14 {
		// height: 600px;
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		padding-bottom: 80px;
		position: relative;
		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}
		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}
	.bg15 {
		height: 600px;
		background-image: url(@/assets/images/security/bg12.jpg);
		background-size: 100% 100%;
	}
	.bg16 {
		height: 600px;
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		padding-bottom: 80px;
		position: relative;
		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}
		div {
			p {
				padding: 5px 0;
				line-height: normal;
			}
		}
		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}
	.bg17 {
		// height: 600px;
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		padding-bottom: 80px;
		position: relative;
		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}
		.solid-arrow {
			width: 0;
			height: 0;
			border-top: 5px solid transparent;
			border-bottom: 5px solid transparent;
			border-left: 5px solid black;
			display: inline-block;
		}
		.jiegou_left {
			background-size: 100% 100%;
			background-image: url(@/assets/images/zhengquan/jiegou.jpg);
			position: relative;
			height: 523px;
			.bg17-abs-1 {
				position: absolute;
				top: 30%;
				left: 47%;
				width: 130px;
				font-size: 14px;
				font-weight: 700;
				color: #fff;
			}
			.bg17-abs-2 {
				position: absolute;
				top: 12%;
				right: 5%;
				width: 120px;
				font-size: 14px;
				font-weight: 700;
			}
		}
		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}
	.bg18,
	.bg19 {
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		padding-bottom: 80px;
		position: relative;
		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}
		.liucheng {
			line-height: 40px;
			display: flex;

			p {
				display: inline;
				width: fit-content;
			}
		}
		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}
	.bg20 {
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		padding-bottom: 80px;
		position: relative;
		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}
		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}
	.bg21,
	.bg22,
	.bg23,
	.bg24,
	.bg25,
	.bg26,
	.bg27,
	.bg29,
	.bg30,
	.bg35,
	.bg36,
	.bg38,
	.bg33 {
		background-image: url(@/assets/images/security/bg5.jpg);
		background-size: 100% 100%;
		padding-top: 25px;
		padding-bottom: 80px;
		position: relative;
		.jiacu {
			margin-left: 16%;
			margin-bottom: 30px;
		}
		.propertyOwnerLogo {
			position: absolute;
			right: 10px;
			bottom: 10px;
		}
	}
	.bg28 {
		height: 600px;
		background-image: url(@/assets/images/security/bg13.jpg);
		background-size: 100% 100%;
	}
	.bg31 {
		height: 600px;
		background-image: url(@/assets/images/security/bg14.jpg);
		background-size: 100% 100%;
	}
	.bg32 {
		height: 600px;
		background-image: url(@/assets/images/security/bg15.jpg);
		background-size: 100% 100%;
	}
	.bg34 {
		height: 600px;
		background-image: url(@/assets/images/security/bg16.jpg);
		background-size: 100% 100%;
	}
	.bg36 {
		.kso1 {
			background-color: #15879e;
			color: #fff;
		}
		td {
			background-color: #f5f5f5;
		}
		.kso9-1 {
			text-align: left;
		}
	}
	.bg37 {
		height: 600px;
		background-image: url(@/assets/images/security/bg17.jpg);
		background-size: 100% 100%;
	}
	.bg39 {
		height: 600px;
		background-image: url(@/assets/images/security/bg1.jpg);
		background-size: 100% 100%;
		position: relative;
		.THANKS {
			position: absolute;
			top: 34%;
			left: 7%;
		}
		.date-box {
			position: absolute;
			top: 68%;
			left: 7%;
		}
		.propertyOwnerLogo {
			position: absolute;
			left: 65%;
			bottom: 10px;
		}
	}
}
.vertical_level_center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.contionar {
	margin: 20px 0;
}

.minjiacu {
	margin-left: 60px;
	font-size: 24px;
	font-weight: 700;
}

.red {
	color: red;
}
.stripe {
	padding: 5px 0;
	background-color: #e8f3f7;
}

.sele {
	color: #014456;
	display: inline;
	border-bottom: 1px solid #014456;
}

.flex {
	display: flex;
	justify-content: space-between;
}

.justify {
	width: 50%;
}

.jiacu {
	font-size: 30px;
	font-weight: bold;
}
.contionar {
	page-break-after: always;
	height: 730px !important;
	width: 1046px;
	margin-bottom: 100px;
}
@media screen and (max-width: 1280px) {
	.main {
		padding: 20px 20px;
	}
}

@media screen and (max-width: 1024px) {
	.main {
		padding: 20px 20px;
	}
}

@media screen and (max-width: 768px) {
	.main {
		padding: 20px 20px;
	}
}

@media screen and (max-width: 640px) {
	.main {
		padding: 20px 20px;
	}
}
</style>
