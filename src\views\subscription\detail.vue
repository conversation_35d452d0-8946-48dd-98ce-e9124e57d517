<template>
	<div class="detail_main">
		<div class="title">成为标标准订阅用户</div>
		<div class="swiper_box">
			<swiper :speed="600" :slides-per-view="1" :grabCursor="true" :initialSlide="data.swiperIndex"
				:centeredSlides="false" :slidesPerGroup="1" :space-between="0" class="swiperBox" @slideChange="onSlideChange"
				@swiper="onSwiper">
				<swiper-slide>
					<div class="slide_box">
						<div class="title_box">
							<div class="title1">单项订阅</div>
							<div class="tips">专业人士超值之选</div>
						</div>
						<div class="pay_body" v-if="data.swiperIndex == 0">
							<!-- 金额选择 -->
							<div class="pay_box" v-if="data.pay_list1.length>0">
								<div class="pay_item" :class="data.payArr.indexOf(item.goodsDataList[0].id)>-1?'pay_item_active':''"
									v-for="(item,index) in data.pay_list1" :key="index" @click="changePay(item,index)">
									<div class="pay_name">{{item.typeName}}</div>
									<div class="pay_price"><span>￥{{item.goodsDataList[0].currentPrice}}</span>/每月</div>
									<div class="check_icon" v-if="data.payArr.indexOf(item.goodsDataList[0].id)>-1">
										<el-icon><Select /></el-icon>
									</div>
								</div>
							</div>
							<!-- 支付方式选择 -->
							<div class="payType_box">
								<div class="payType" v-for="(item,index) in data.payTypeList" :key="index" v-show="index === 0"
									@click="changePayType(item,index)">
									<div class="img"><img :src="item.img" alt=""></div>
									<div class="name">{{item.name}}</div>
									<div class="check_icon"><el-icon v-if="data.payTpyeIndex === index">
											<SuccessFilled />
										</el-icon></div>
								</div>
							</div>
							<!-- 到期日期以及金额数 -->
							<div class="time_rule_box">
								<div class="time_box">
									<div class="time_tips">
										<!-- <div class="title1">到期时间：</div>
										<div class="time">2024/07/25</div> -->
									</div>
									<div class="price_tips">
										<div class="title1">实付金额：</div>
										<div class="time">{{allAmount()}}元</div>
									</div>
								</div>
								<div class="rule_box" @click="checkIsRead()">
									<div class="check_icon"><el-icon v-if="data.isRead">
											<SuccessFilled />
										</el-icon></div>
									<div class="rule">已阅读并同意<span>《标标准充值章程及充值协议》</span></div>
								</div>
							</div>
							<!-- 支付按钮 -->
							<div class="btn active" @click="submitOpen">立即支付</div>
							<!-- 说明 -->
							<div class="tips">支付后免费邮寄发票与合同</div>
						</div>
						<div class="pay_body1" v-else>
							<div class="price">￥98<span>/每月</span></div>
							<div class="btn" @click="openSwiper(0)">立即开通</div>
						</div>
					</div>
				</swiper-slide>
				<swiper-slide>
					<div class="slide_box1">
						<div class="title_box">
							<div class="title1">套餐订阅</div>
							<div class="tips">专业人士理想之选</div>
						</div>
						<div class="pay_body" v-if="data.swiperIndex == 1">
							<!-- 金额选择 -->
							<div class="pay_box">
								<div class="pay_item" :class="data.payArrIndex === index?'pay_item_active':''"
									v-for="(item,index) in data.pay_list2" :key="index" @click="changePay1(item,index)">
									<div class="pay_name">一折</div>
									<div class="pay_price"><span>￥{{item.currentPrice}}</span>/{{item.dataName}}</div>
									<div class="check_icon" v-if="data.payArrIndex === index"><el-icon><Select /></el-icon></div>
								</div>
							</div>
							<!-- 支付方式选择 -->
							<div class="payType_box">
								<div class="payType" v-for="(item,index) in data.payTypeList" :key="index" v-show="index === 0"
									@click="changePayType(item,index)">
									<div class="img"><img :src="item.img" alt=""></div>
									<div class="name">{{item.name}}</div>
									<div class="check_icon"><el-icon v-if="data.payTpyeIndex === index">
											<SuccessFilled />
										</el-icon></div>
								</div>
							</div>
							<!-- 到期日期以及金额数 -->
							<div class="time_rule_box">
								<div class="time_box">
									<div class="time_tips">
										<!-- <div class="title1">到期时间：</div>
										<div class="time">2024/07/25</div> -->
									</div>
									<div class="price_tips">
										<div class="title1">实付金额：</div>
										<div class="time">{{allAmount()}}元</div>
									</div>
								</div>
								<div class="rule_box" @click="checkIsRead()">
									<div class="check_icon"><el-icon v-if="data.isRead">
											<SuccessFilled />
										</el-icon></div>
									<div class="rule">已阅读并同意<span>《标标准充值章程及充值协议》</span></div>
								</div>
							</div>
							<!-- 支付按钮 -->
							<div class="btn active" @click="submitOpen">立即支付</div>
							<!-- 说明 -->
							<div class="tips">支付后免费邮寄发票与合同</div>
						</div>
						<div class="pay_body1" v-else>
							<div class="price">￥688<span>/季度</span></div>
							<div class="btn" @click="openSwiper(1)">立即开通</div>
						</div>
					</div>
				</swiper-slide>
			</swiper>
		</div>
		<!-- 权益对比介绍 -->
		<div class="title_box1">
			<div class="h1">
				<div class="line1"></div>
				<div class="text">权益对比介绍</div>
				<div class="line2"></div>
			</div>
			<div class="h2">了解标标准更多权益 </div>
		</div>
		<div class="table_box">
			<div class="title_top">
				<div class="table_name">权益范围</div>
				<div class="table_name1">单项订阅</div>
				<div class="table_name2">套餐订阅</div>
			</div>
			<div class="li_box" v-for="(item,index) in data.pay_list1" :key="index">
				<div class="lable1">{{item.typeName}}</div>
				<div class="check1"><el-icon>
						<Select v-if="data.payArr.indexOf(item.goodsDataList[0].id)>-1" />
						<Close v-else />
					</el-icon></div>
				<div class="check2"><el-icon><Select /></el-icon></div>
			</div>
		</div>

	</div>
</template>

<script setup>
	import {
		Swiper,
		SwiperSlide
	} from 'swiper/vue';
	import 'swiper/css';
	import alipay from "../../assets/images/subscription/alipay.png"
	import wepay from "../../assets/images/subscription/wepay.png"
	import {
		onMounted,
		reactive,
		ref
	} from 'vue';
	import {
		getPCAllGoodsList,
		getPCAllComboGoodsList,
		qrcodePlaceOrder
	} from '../../api/layout.js'
	import {
		useRoute
	} from 'vue-router';
	import {
		ElMessage
	} from 'element-plus';
	const route = useRoute()
	const my_swiper = ref()
	const data = reactive({
		id: null,
		orderStr: '', //支付宝跳转链接
		swiperIndex: 0, //0：单项订阅，1：套餐订阅
		isRead: false, //是否勾选选已阅读
		payIndex: 0, //选中项
		payArr: [], //单项订阅选中项
		payArrIndex: 0, //套餐订阅选中项
		pay_list1: [],
		pay_list2: [],
		payTpyeIndex: 0,
		payTypeList: [{
			name: '支付宝',
			img: alipay,
			value: 'alipay',
			code: '603007'
		}, {
			name: '微信',
			img: wepay,
			value: 'wepey',
			code: '603006',
		}]
	})
	onMounted(() => {
		data.id = Number(route.query.id)
		data.payArr.push(data.id)
		getGoodsAllList()
		getComboGoodsAllList()
	})
	//获取商品列表
	const getGoodsAllList = async () => {
		await getPCAllGoodsList().then(res => {
			console.log(res)
			if (res.status === 100) {
				let list = []
				res.data.forEach(e => {
					if (e.typeName !== '交易统计') {
						list.push(e)
					}
				})
				data.pay_list1 = list
			}

		}).catch(err => {
			console.log('请求失败！')
		})
	}
	//获取商品套餐列表
	const getComboGoodsAllList = async () => {
		await getPCAllComboGoodsList().then(res => {
			if (res.status === 100) {
				data.pay_list2 = res.data[0].goodsDataList
			}

		}).catch(err => {
			console.log('请求失败！')
		})
	}
	//计算订阅价格
	const allAmount = () => {
		if (data.swiperIndex === 0) {
			let res = data.pay_list1.filter(item => {
				return data.payArr.indexOf(item.goodsDataList[0].id) > -1
			})
			let num = 0
			res.forEach(e => {
				num = num + e.goodsDataList[0].currentPrice
			})
			return num
		}
		if (data.swiperIndex === 1) {
			let res = data.pay_list2.filter((item, index) => {
				return index === data.payArrIndex
			})
			return res[0].currentPrice
		}
	}
	const onSwiper = (swiper) => {
		my_swiper.value = swiper;
	}
	//立即开通
	const openSwiper = (index) => {
		my_swiper.value.slideTo(index)
	}

	const onSlideChange = (swiper) => {
		data.swiperIndex = swiper.activeIndex
		console.log('slide change');
	};
	//切换选项
	const changePay = (item, index) => {
		if (data.payArr.indexOf(item.goodsDataList[0].id) > -1) {
			data.payArr.splice(data.payArr.indexOf(item.goodsDataList[0].id), 1)
		} else {
			data.payArr.push(item.goodsDataList[0].id)
		}
	}
	const changePay1 = (item, index) => {
		data.payArrIndex = index
	}
	//切换支付方式选型
	const changePayType = (item, index) => {
		data.payTpyeIndex = index
	}
	//勾选已阅读
	const checkIsRead = () => {
		data.isRead = data.isRead ? false : true
	}
	const submitOpen = async () => {
		if (!data.isRead) {
			ElMessage({
				message: '请先阅读勾选《标标准充值章程及充值协议》',
				type: 'warning',
			})
			return
		}
		let params
		if (data.swiperIndex === 0) {
			params = {
				couponCode: '', //优惠券编码
				goodsId: data.payArr.toString(), //商品id
				orderType: '1', //订单类型1：普通订单，2：优惠订单
				payChannelCode: '709001', //下单渠道：pc:709001,app:709002,小程序：709003
				payType: data.payTypeList[data.payTpyeIndex].code, //支付类型：支付宝：603002，微信：603001
			}
		}
		if (data.swiperIndex === 1) {
			params = {
				couponCode: '', //优惠券编码
				goodsId: data.pay_list2[data.payArrIndex].id, //商品id
				orderType: '1', //订单类型1：普通订单，2：优惠订单
				payChannelCode: '709001', //下单渠道：pc:709001,app:709002,小程序：709003
				payType: data.payTypeList[data.payTpyeIndex].code, //支付类型：支付宝：603002，微信：603001
			}
		}
		await qrcodePlaceOrder(params).then(res => {
			if (res.status === 100) {
				data.orderStr = res.data.orderStr
				onAlipaySubmit(res.data.orderStr,'_blank')
			}else{
				ElMessage.error(res.message)
			}
		}).catch(err => {
			console.log(err)
			console.log('请求失败！')
		})

	}
	//打开跳转新页面
	const onAlipaySubmit = (form, target) => {
		// let iframe = document.createElement("iframe");
		// iframe.name = "alipayframe";
		// iframe.style.display = "none";
		// document.body.appendChild(iframe);

		// // 这里的alipayForm是支付宝返回的form表单字符串
		// let alipayForm = form;

		// // 在iframe中插入form并提交
		// iframe.onload = function() {
		// 	let doc = iframe.contentDocument || iframe.contentWindow.document;
		// 	doc.open();
		// 	doc.writeln(alipayForm);
		// 	doc.close();
		// 	let form = doc.forms[0];
		// 	form.target = target;
		// 	form.submit();
		// };
		
		document.querySelector('body').innerHTML = form;
		// document.forms[0].target = '_blank';
		document.forms[0].submit()
	}
</script>

<style lang="less" scoped>
	.detail_main {
		width: 100%;
		height: 100%;
		min-height: 100vh;
		background: linear-gradient(180deg, #B9D7FCFF 10%, #F6F6F6FF 100%);
		padding-top: 80px;
		box-sizing: border-box;

		.title {
			width: 100%;
			font-size: 30px;
			font-weight: bold;
			text-align: center;
			color: #005193;
		}

		.swiper_box {
			width: 100%;
			height: 100%;
			margin-top: 48px;
			display: flex;
			justify-content: center;
			align-items: center;

			.swiperBox {
				width: 38%;
				height: 100%;
				overflow: initial;

				.swiper-slide {
					transition: .3s all;
					transform: scale(0.6);
				}

				.swiper-slide-active,
				.swiper-slide-duplicate-active {
					transition: .3s all;
					transform: scale(1);
				}
			}

			.slide_box {
				width: 100%;
				height: 100%;
				border-radius: 16px;
				background-color: rgba(255, 255, 255, 1);
				overflow: hidden;
				box-shadow: 0px 8px 10px rgba(143, 179, 211, 0.5);

				.title_box {
					width: 100%;
					height: 170px;
					background: linear-gradient(to right, #6EA8D7 10%, #A8D8FF 100%);
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					position: relative;

					&::after {
						content: '';
						width: 220px;
						height: 170px;
						background-image: url('/src/assets/images/subscription/logo1.png');
						background-size: 100% 100%;
						position: absolute;
						right: 0;
						z-index: 1;
					}

					.title1 {
						width: 100%;
						font-size: 30px;
						font-weight: bold;
						text-align: center;
						color: #005193;
						position: relative;
						z-index: 2;
					}

					.tips {
						font-size: 16px;
						color: #005193;
						margin-top: 10px;
						font-weight: 500;
						z-index: 2;
					}
				}

				.pay_body {
					width: 100%;
					height: 100%;
					padding-bottom: 10px;
					box-sizing: border-box;

					.pay_box {
						width: 100%;
						height: auto;
						display: flex;
						justify-content: center;
						align-items: flex-start;
						flex-wrap: wrap;
						margin-top: 10px;

						.pay_item {
							width: 30%;
							height: 90px;
							display: flex;
							justify-content: center;
							align-items: center;
							flex-direction: column;
							border: 1px solid rgb(184, 184, 184);
							margin: 5px;
							font-size: 14px;
							font-weight: bold;
							color: rgb(73, 73, 73);
							border-radius: 4px;
							position: relative;

							.pay_price {
								span {
									font-size: 20px
								}
							}

							.check_icon {
								width: 20px;
								height: 20px;
								background-image: url('../../assets/images/subscription/check_icon.png');
								background-size: 100% 100%;
								position: absolute;
								right: 0;
								bottom: 0;
								display: flex;
								justify-content: flex-end;
								align-items: flex-end;
								color: rgba(255, 255, 255, 1);
								font-size: 12px;
							}

						}

						.pay_item_active {
							border: 2px solid rgb(56, 96, 154);
							color: rgb(56, 96, 154);
							box-sizing: border-box;
						}
					}

					.payType_box {
						width: 100%;
						height: 60px;
						display: flex;
						justify-content: center;
						align-items: center;
						margin-top: 10px;
						padding: 0 10px;
						box-sizing: border-box;

						.payType {
							width: 48%;
							height: 60px;
							background-color: rgba(255, 255, 255, 1);
							display: flex;
							justify-content: flex-start;
							align-items: center;
							position: relative;
							padding: 10px;
							border: 10px solid rgb(246, 246, 246);
							box-sizing: border-box;
							margin: 0 5px;
							border-radius: 4px;

							.img {
								width: 28px;
								height: 28px;
								margin-right: 15px;

								img {
									width: 100%;
									height: 100%;
								}
							}

							.name {
								font-size: 14px;
							}

							.check_icon {
								width: 20px;
								height: 20px;
								border-radius: 50%;
								border: 1px solid rgb(197, 197, 197);
								box-sizing: border-box;
								display: flex;
								justify-content: center;
								align-items: center;
								color: rgb(2, 103, 230);
								position: absolute;
								right: 10px;
							}
						}
					}

					.time_rule_box {
						width: 100%;
						height: 48px;
						padding: 0 15px;
						box-sizing: border-box;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.time_box {
							width: 50%;

							.time_tips,
							.price_tips {
								width: 100%;
								display: flex;
								justify-content: space-between;
								align-items: center;
							}

							.price_tips {
								.time {
									color: red;
									font-weight: bold;
								}
							}
						}

						.rule_box {
							width: 45%;
							color: rgb(133, 133, 133);
							display: flex;
							justify-content: flex-start;
							align-items: center;

							span {
								color: rgb(71, 121, 184);
							}

							.check_icon {
								width: 18px;
								height: 18px;
								border-radius: 50%;
								border: 1px solid rgb(0, 0, 0);
								box-sizing: border-box;
								margin-right: 5px;
								display: flex;
								justify-content: center;
								align-items: center;
								color: rgb(2, 103, 230);
							}

							.rule {
								width: calc(100% - 25px);
							}
						}
					}

					.btn {
						width: 200px;
						height: 48px;
						display: flex;
						justify-content: center;
						align-items: center;
						color: rgba(255, 255, 255, 1);
						background-color: rgb(90, 168, 235);
						border-radius: 4px;
						margin: 0 auto;
						margin-top: 10px;
					}

					.tips {
						width: 100%;
						text-align: center;
						font-size: 14px;
						color: rgb(134, 134, 134);
						margin-top: 10px;
					}

				}

				.pay_body1 {
					width: 100%;
					height: 300px;
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;

					.price {
						font-size: 30px;
						font-weight: bold;
						color: rgb(42, 130, 213);

						span {
							font-size: 20px;
						}
					}

					.btn {
						width: 200px;
						height: 48px;
						display: flex;
						justify-content: center;
						align-items: center;
						color: rgba(255, 255, 255, 1);
						background-color: rgb(90, 168, 235);
						border-radius: 4px;
						margin-top: 30px;
					}
				}

			}

			.slide_box1 {
				width: 100%;
				height: 100%;
				border-radius: 16px;
				background-color: rgba(255, 255, 255, 1);
				overflow: hidden;
				box-shadow: 0px 8px 10px rgba(143, 179, 211, 0.5);

				.title_box {
					width: 100%;
					height: 170px;
					background: linear-gradient(to right, rgb(229, 189, 114) 10%, rgba(255, 221, 155, 0.53) 100%);
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;
					position: relative;

					&::after {
						content: '';
						width: 220px;
						height: 170px;
						background-image: url('/src/assets/images/subscription/logo2.png');
						background-size: 100% 100%;
						position: absolute;
						right: 0;
						z-index: 1;
					}

					.title1 {
						width: 100%;
						font-size: 30px;
						font-weight: bold;
						text-align: center;
						color: rgb(147, 81, 0);
						position: relative;
						z-index: 2;
					}

					.tips {
						font-size: 16px;
						color: rgb(147, 81, 0);
						margin-top: 10px;
						font-weight: 500;
						z-index: 2;
					}
				}

				.pay_body {
					width: 100%;
					height: 100%;
					padding-bottom: 10px;
					box-sizing: border-box;

					.pay_box {
						width: 100%;
						height: auto;
						display: flex;
						justify-content: center;
						align-items: flex-start;
						flex-wrap: wrap;
						margin-top: 10px;

						.pay_item {
							width: 30%;
							height: 90px;
							display: flex;
							justify-content: center;
							align-items: center;
							flex-direction: column;
							border: 1px solid rgb(184, 184, 184);
							margin: 5px;
							font-size: 14px;
							font-weight: bold;
							color: rgb(73, 73, 73);
							border-radius: 4px;
							position: relative;

							.pay_price {
								span {
									font-size: 20px
								}
							}

							.check_icon {
								width: 20px;
								height: 20px;
								background-image: url('../../assets/images/subscription/check_icon1.png');
								background-size: 100% 100%;
								position: absolute;
								right: 0;
								bottom: 0;
								display: flex;
								justify-content: flex-end;
								align-items: flex-end;
								color: rgba(255, 255, 255, 1);
								font-size: 12px;
							}

						}

						.pay_item_active {
							border: 2px solid rgb(147, 81, 0);
							color: rgb(147, 81, 0);
							box-sizing: border-box;
						}
					}

					.payType_box {
						width: 100%;
						height: 60px;
						display: flex;
						justify-content: center;
						align-items: center;
						margin-top: 10px;
						padding: 0 10px;
						box-sizing: border-box;

						.payType {
							width: 48%;
							height: 60px;
							background-color: rgba(255, 255, 255, 1);
							display: flex;
							justify-content: flex-start;
							align-items: center;
							position: relative;
							padding: 10px;
							border: 10px solid rgb(246, 246, 246);
							box-sizing: border-box;
							margin: 0 5px;
							border-radius: 4px;

							.img {
								width: 28px;
								height: 28px;
								margin-right: 15px;

								img {
									width: 100%;
									height: 100%;
								}
							}

							.name {
								font-size: 14px;
							}

							.check_icon {
								width: 20px;
								height: 20px;
								border-radius: 50%;
								border: 1px solid rgb(197, 197, 197);
								box-sizing: border-box;
								display: flex;
								justify-content: center;
								align-items: center;
								color: rgb(2, 103, 230);
								position: absolute;
								right: 10px;
							}
						}
					}

					.time_rule_box {
						width: 100%;
						height: 48px;
						padding: 0 15px;
						box-sizing: border-box;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.time_box {
							width: 50%;

							.time_tips,
							.price_tips {
								width: 100%;
								display: flex;
								justify-content: space-between;
								align-items: center;
							}

							.price_tips {
								.time {
									color: red;
									font-weight: bold;
								}
							}
						}

						.rule_box {
							width: 45%;
							color: rgb(133, 133, 133);
							display: flex;
							justify-content: flex-start;
							align-items: center;

							span {
								color: rgb(71, 121, 184);
							}

							.check_icon {
								width: 18px;
								height: 18px;
								border-radius: 50%;
								border: 1px solid rgb(0, 0, 0);
								box-sizing: border-box;
								margin-right: 5px;
								display: flex;
								justify-content: center;
								align-items: center;
								color: rgb(2, 103, 230);
							}

							.rule {
								width: calc(100% - 25px);
							}
						}
					}

					.btn {
						width: 200px;
						height: 48px;
						display: flex;
						justify-content: center;
						align-items: center;
						color: rgba(255, 255, 255, 1);
						background-color: rgb(90, 168, 235);
						border-radius: 4px;
						margin: 0 auto;
						margin-top: 10px;
					}

					.tips {
						width: 100%;
						text-align: center;
						font-size: 14px;
						color: rgb(134, 134, 134);
						margin-top: 10px;
					}

				}

				.pay_body1 {
					width: 100%;
					height: 300px;
					display: flex;
					justify-content: center;
					align-items: center;
					flex-direction: column;

					.price {
						font-size: 30px;
						font-weight: bold;
						color: rgb(42, 130, 213);

						span {
							font-size: 20px;
						}
					}

					.btn {
						width: 200px;
						height: 48px;
						display: flex;
						justify-content: center;
						align-items: center;
						color: rgba(255, 255, 255, 1);
						background-color: rgb(90, 168, 235);
						border-radius: 4px;
						margin-top: 30px;
					}
				}

			}
		}

		.title_box1 {
			width: 100%;
			margin-top: 60px;

			.h1 {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;

				.line1 {
					width: 100px;
					height: 10px;
					display: flex;
					justify-content: center;
					align-items: center;

					&:before {
						content: '';
						width: 40px;
						height: 2px;
						background-color: rgb(130, 196, 245);
						margin-right: 10px;
					}

					&::after {
						content: '';
						width: 5px;
						height: 5px;
						background-color: rgb(130, 196, 245);
						transform: rotate(45deg);
					}
				}

				.text {
					width: auto;
					font-size: 30px;
					color: rgb(0, 81, 147);
					font-weight: bold;
				}

				.line2 {
					width: 100px;
					height: 10px;
					display: flex;
					justify-content: center;
					align-items: center;

					&:before {
						content: '';
						width: 5px;
						height: 5px;
						background-color: rgb(130, 196, 245);
						transform: rotate(45deg);
						margin-right: 10px;
					}

					&::after {
						content: '';
						width: 40px;
						height: 2px;
						background-color: rgb(130, 196, 245);
					}
				}
			}

			.h2 {
				width: 100%;
				font-size: 30px;
				color: rgb(0, 81, 147);
				text-align: center;
				margin-top: 5px;
			}
		}

		.table_box {
			width: 800px;
			height: 300px;
			box-sizing: border-box;
			margin: 0 auto;
			margin-top: 30px;

			.title_top {
				width: 100%;
				height: 48px;
				display: flex;
				justify-content: center;
				align-items: center;

				.table_name,
				.table_name1,
				.table_name2 {
					width: 33%;
					height: 48px;
					display: flex;
					justify-content: center;
					align-items: center;
					position: relative;
					color: rgb(0, 0, 0);
					font-size: 18px;
					border: 1px solid rgba(255, 255, 255, 1);
					box-sizing: border-box;
				}

				.table_name {
					background-color: rgb(237, 242, 246);
					font-weight: bold;
				}

				.table_name1 {
					background: linear-gradient(to right, #6EA8D7 10%, #A8D8FF 100%);
					font-family: 'youshebiaotihei';
					color: rgb(0, 81, 147);
					font-size: 22px;

					&::after {
						content: '';
						width: 80px;
						height: 48px;
						background-image: url('/src/assets/images/subscription/logo1.png');
						background-size: 100% 100%;
						position: absolute;
						right: 0;
						z-index: 1;
					}
				}

				.table_name2 {
					background: linear-gradient(to right, rgb(229, 189, 114) 10%, rgba(255, 221, 155, 0.53) 100%);
					font-family: 'youshebiaotihei';
					color: rgb(147, 81, 0);
					font-size: 22px;

					&::after {
						content: '';
						width: 80px;
						height: 48px;
						background-image: url('/src/assets/images/subscription/logo2.png');
						background-size: 100% 100%;
						position: absolute;
						right: 0;
						z-index: 1;
					}
				}
			}

			.li_box {
				width: 100%;
				height: 48px;
				display: flex;
				justify-content: center;
				align-items: center;

				.lable1,
				.check1,
				.check2 {
					width: 33%;
					height: 48px;
					display: flex;
					justify-content: center;
					align-items: center;
					position: relative;
					color: rgb(0, 0, 0);
					font-size: 14px;
					border: 1px solid rgba(255, 255, 255, 1);
					box-sizing: border-box;
				}

				.lable1 {
					background-color: rgb(237, 242, 246);
					font-weight: bold;
				}

				.check1 {
					background-color: rgb(214, 230, 242);
				}

				.check2 {
					background-color: rgb(245, 221, 176);
				}
			}
		}
	}
</style>