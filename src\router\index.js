import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router';
import statistics from '../views/statistics/market_statistics.vue'; //新市场统计，交易统计
import risks from '../views/risks/credit_risks.vue'; //信用风险
import buildDeal from '../views/buildDeal/buildDeal.vue'; //新建交易
import subscription from '../views/subscription/subscription.vue'; //订阅
import subscription_detail from '../views/subscription/detail.vue'; //订阅
import rights from '../views/rights/index.vue'; //权益
import adsDetails from '../views/home/<USER>';
const layoutPage = {
	path: '/',
	name: 'layout',
	// default: '/homepage',
	component: () => import(/* webpackChunkName: "Login" */ '../views/layout/index.vue'),
	children: [
		{
			path: '/',
			name: 'homepage',
			component: () => import('../views/home/<USER>'),
		},
		{
			path: '/shangYutong',
			name: 'shangYutong',
			meta: {
				title: '商宇通',
			},
			component: () => import('../views/shangYutong/layout.vue'),
			children: [
				{
					path: '/shangYutong/statistics',
					name: 'statistics',
					component: statistics,
				},
				{
					path: '/shangYutong/materials',
					name: 'materials',
					redirect: '/shangYutong/materials/calculate/lease',
					children: [
						{
							path: '/shangYutong/materials/calculate',
							name: 'calculate',
							component: () => import('../views/shangYutong/materials_new/components/calculate.vue'),
							children: [
								{
									path: '/shangYutong/materials/calculate/lease',
									name: 'lease',
									component: () => import('../views/shangYutong/materials_new/components/lease.vue'),
								},
								{
									path: '/shangYutong/materials/calculate/buy',
									name: 'buy',
									component: () => import('../views/shangYutong/materials_new/components/buy.vue'),
								},
							],
						},
						{
							path: '/shangYutong/materials/assetComparison',
							name: 'assetComparison',
							component: () => import('../views/shangYutong/materials_new/components/assetComparison.vue'),
						},
						{
							path: '/shangYutong/financial/estateFinance',
							name: 'estateFinance',
							component: () => import('../views/shangYutong/materials_new/components/estateFinance.vue'),
						},
						{
							path: '/shangYutong/materials/cityData',
							name: 'cityData',
							component: () => import('../views/shangYutong/materials_new/components/cityData.vue'),
						},
						{
							path: '/shangYutong/materials/participant',
							name: 'participant',
							component: () => import('../views/shangYutong/participator/components/participant.vue'),
						},
						{
							path: '/shangYutong/materials/comparison',
							name: 'comparison',
							component: () => import('../views/shangYutong/materials_new/components/comparison.vue'),
						},
						{
							path: '/shangYutong/materials/population1',
							name: 'population1',
							component: () => import('../views/shangYutong/materials_new/components/population.vue'),
						},
						{
							path: '/shangYutong/materials/lessee',
							name: 'lessee',
							component: () => import('../views/shangYutong/materials_new/components/lessee.vue'),
						},
						{
							path: '/shangYutong/materials/floor',
							name: 'floor',
							component: () => import('../views/shangYutong/materials_new/components/floor.vue'),
						},
						{
							path: '/shangYutong/materials/businessAnalysis', //商圈分析
							name: 'businessAnalysis',
							component: () => import('../views/shangYutong/materials_new/components/businessAnalysis.vue'),
						},
						{
							path: '/shangYutong/materials/demographic', //人口趋势
							name: 'demographic',
							component: () => import('../views/shangYutong/materials_new/components/retailZone.vue'),
						},
					],
				},
				{
					path: '/shangYutong/participator',
					name: 'participator',
					redirect: '/shangYutong/participator/brokerSearch1',
					children: [
						{
							path: '/shangYutong/participator/brokerSearch1',
							name: 'brokerSearch1',
							component: () => import('../views/shangYutong/participator/components/broker.vue'),
						},
						{
							path: '/shangYutong/participator/brokerDetail1',
							name: 'brokerDetail1',
							component: () => import('../views/shangYutong/participator/components/brokerHome.vue'),
						},
						{
							path: '/shangYutong/participator/property1',
							name: 'property1',
							component: () => import('../views/shangYutong/participator/components/property.vue'),
						},
						{
							path: '/shangYutong/participator/propertyDetails',
							name: 'propertyDetails',
							component: () => import('../views/shangYutong/participator/components/propertyDetails.vue'),
						},
						{
							path: '/shangYutong/participator/manage1',
							name: 'manage1',
							component: () => import('../views/shangYutong/participator/components/manage.vue'),
						},
						{
							path: '/shangYutong/participator/manageDetails',
							name: 'manageDetails',
							component: () => import('../views/shangYutong/participator/components/manageDetails.vue'),
						},
					],
				},
				{
					path: '/shangYutong/financial',
					name: 'financial',
					redirect: '/shangYutong/financial/market1',
					children: [
						{
							path: '/shangYutong/financial/market1',
							name: 'market1',
							component: () => import('../views/shangYutong/financial/components/market.vue'),
						},
						{
							path: '/shangYutong/financial/standard1',
							name: 'standard1',
							component: () => import('../views/shangYutong/financial/components/standard.vue'),
							children: [
								{
									path: '/shangYutong/financial/standard1/abs',
									name: 'abs',
									component: () => import('../views/shangYutong/financial/components/finance-abs.vue'),
								},
								{
									path: '/shangYutong/financial/standard1/reits',
									name: 'reits',
									component: () => import('../views/shangYutong/financial/components/finance-reits.vue'),
								},
							],
						},
					],
				},
				{
					path: '/shangYutong/shangAuto',
					name: 'shangAuto',
					component: () => import('../views/shangAuto/index.vue'),
				},
				{
					path: '/shangYutong/financial/risks',
					name: 'risks',
					component: risks,
				},
			],
		},
		{
			path: '/rights',
			name: 'rights',
			component: rights,
		},
		{
			path: '/adsDetails',
			name: 'adsDetails',
			component: adsDetails,
		},

		{
			path: '/subscription',
			name: 'subscription',
			component: subscription,
		},
		{
			path: '/subscription_detail',
			name: 'subscription_detail',
			component: subscription_detail,
		},
		{
			path: '/buildDeal',
			name: 'buildDeal',
			component: buildDeal,
		},
		{
			path: '/propertyHome',
			name: 'propertyHome',
			component: () => import(/* webpackChunkName: "Login" */ '../views/participator/propertyHome.vue'),
		},
		// {
		// 	path: '/brokerDetail',
		// 	name: 'brokerDetail',
		// 	component: () => import( /* webpackChunkName: "Login" */ '../views/participator/brokerHome.vue'),
		// },
		{
			path: '/relation',
			name: 'relation',
			component: () => import(/* webpackChunkName: "Login" */ '../views/home/<USER>'),
		},
		{
			path: '/shoppingCart',
			name: 'shoppingCart',
			component: () => import(/* webpackChunkName: "shoppingCart" */ '../views/home/<USER>'),
		},
		{
			path: '/newsdetail',
			name: 'newsdetail',
			component: () => import(/* webpackChunkName: "Newsdetail" */ '../views/home/<USER>'),
			meta: {
				title: '新闻详情',
			},
		},
		{
			path: '/introduceMobile',
			name: 'introduceMobile',
			component: () => import(/* webpackChunkName: "introduce" */ '../views/home/<USER>'),
		},
		{
			path: '/introduce',
			name: 'introduce',
			component: () => import(/* webpackChunkName: "introduce" */ '../views/home/<USER>'),
		},
		{
			path: '/zixun',
			name: 'zixun',
			component: () => import(/* webpackChunkName: "Home" */ '../views/home/<USER>'),
		},
		{
			path: '/biaobiaozhun',
			name: 'biaobiaozhun',
			component: () => import(/* webpackChunkName: "Home" */ '../views/home/<USER>'),
		},
		{
			path: '/IntroductionPage',
			name: 'IntroductionPage',
			component: () => import(/* webpackChunkName: "Home" */ '../views/home/<USER>'),
		},
		{
			path: '/mobileEndSyt',
			name: 'mobileEndSyt',
			component: () => import(/* webpackChunkName: "Home" */ '../views/home/<USER>'),
		},
		// 查看更多
		{
			path: '/viewMore',
			name: 'viewMore',
			component: () => import('../views/home/<USER>'),
		},
		// 招聘
		{
			path: '/recruit',
			name: 'recruit',
			component: () => import(/* webpackChunkName: "Recruit" */ '../views/home/<USER>'),
		},
		//提交成功
		{
			path: '/submitSuccess',
			name: 'submitSuccess',
			component: () => import(/* webpackChunkName: "Recruit" */ '../component/submitSuccess/index.vue'),
		},
		{
			path: '/recruitDetail',
			name: 'recruitDetail',
			component: () => import(/* webpackChunkName: "Recruit" */ '../views/home/<USER>'),
		},
		// 我的资源
		{
			path: '/profile',
			name: 'profile',
			component: () => import(/* webpackChunkName: "Profile" */ '../views/profile/layout.vue'),
			// meta: {
			// 	title: '我的资源', // 设置页面标题
			// },
			children: [
				{
					path: '/profile/mymessage',
					name: 'mymessage',
					component: () => import('../views/profile/mymessage.vue'),
				},
				{
					path: '/profile/browsingHistory',
					name: 'browsingHistory',
					component: () => import('../views/profile/browsinghistory.vue'),
				},
				{
					path: '/profile/orderCentre',
					name: 'orderCentre',
					component: () => import('../views/profile/orderCentre.vue'),
				},
				{
					path: '/profile/myinformation',
					name: 'myinformation',
					component: () => import('../views/profile/myinformation.vue'),
				},
			],
		},

		{
			path: '/mobileShangAuto',
			name: 'mobileShangAuto',
			component: () => import('../views/shangAuto/mobileIndex.vue'),
		},
		{
			path: '/mobileDemo',
			name: 'mobileDemo',
			component: () => import('../views/shangAuto/mobileDemo.vue'),
		},
	],
};

const router = createRouter({
	scrollBehavior(to, from, savedPosition) {
		// 切换路由时滚动到顶部
		return { top: 0 };
	},
	history: createWebHashHistory(), // hash模式：createWebHashHistory，history模式：createWebHistory
	routes: [
		layoutPage,
		// {
		// 	path: '/home',
		// 	redirect: '/home',
		// },
		// 登录
		{
			path: '/login',
			name: 'login',
			component: () => import(/* webpackChunkName: "Login" */ '../views/login/login.vue'),
			meta: {
				title: '登录', // 设置页面标题
			},
		},
		// 注册
		{
			path: '/reg',
			name: 'reg',
			component: () => import(/* webpackChunkName: "Login" */ '../views/login/reg.vue'),
			meta: {
				title: '注册', // 设置页面标题
			},
		},
		{
			path: '/h5',
			name: 'h5',
			component: () => import('../views/h5/junpLink.vue'),
		},
		{
			path: '/talk',
			name: 'talk',
			component: () => import('../views/h5/talkAI.vue'),
		},
		{
			path: '/agreement',
			name: 'agreement',
			component: () => import(/* webpackChunkName: "Login" */ '../views/login/agreement.vue'),
		},
		{
			path: '/privacy',
			name: 'privacy',
			component: () => import(/* webpackChunkName: "Privacy" */ '../views/login/privacy.vue'),
		},

		{
			path: '/aboutBbz',
			name: 'aboutBbz',
			component: () => import(/* webpackChunkName: "Login" */ '../views/home/<USER>'),
		},

		{
			path: '/advertising',
			name: 'advertising',
			component: () => import(/* webpackChunkName: "Login" */ '../views/home/<USER>'),
		},
		//首页
		// {
		// 	path: '/home',
		// 	name: 'home',
		// 	component: () => import(/* webpackChunkName: "Home" */ '../views/home/<USER>'),
		// 	meta: {
		// 		title: '新闻资讯', // 设置页面标题
		// 	},
		// },

		{
			path: '/rating',
			name: 'rating',
			component: () => import(/* webpackChunkName: "Rating" */ '../views/rating/Rat.vue'),
			meta: {
				title: '评级通', // 设置页面标题
				keepAlive: true, // 开启页面缓存
			},
		},

		{
			path: '/report/value',
			name: 'value',
			component: () => import(/* webpackChunkName: "value" */ '../views/report/value.vue'),
			meta: {
				title: '估值', // 设置页面标题
			},
		},
		{
			path: '/report/gaikuang',
			name: 'gaikuang',
			component: () => import(/* webpackChunkName: "gaikuang" */ '../views/report/gaikuang.vue'),
			meta: {
				title: '概况', // 设置页面标题
			},
		},
		{
			path: '/report/business',
			name: 'business',
			component: () => import('../views/report/business.vue'),
			meta: {
				title: '招商',
			},
		},
		{
			path: '/report/location',
			name: 'location',
			component: () => import(/* webpackChunkName: "location" */ '../views/report/location.vue'),
			meta: {
				title: '位置', // 设置页面标题
			},
		},
		{
			path: '/report/population',
			name: 'populations',
			component: () => import('../views/report/population.vue'),
			meta: {
				title: '人口',
			},
		},
		{
			path: '/report/populationtwo',
			name: 'populationtwo',
			component: () => import('../views/report/populationtwo.vue'),
			meta: {
				title: '人口',
			},
		},
		{
			path: '/report/security',
			name: 'security',
			component: () => import('../views/report/security.vue'),
			meta: {
				title: '证券化',
			},
		},
		{
			path: '/report/populationthree',
			name: 'populationthree',
			component: () => import('../views/report/populationthree.vue'),
			meta: {
				title: '人口',
			},
		},
		{
			path: '/report/invest_analyze',
			name: 'analyze',
			component: () => import('../views/report/analyze/index.vue'),
			meta: {
				title: '投资报告',
			},
		},
		{
			path: '/main/:id',
			name: 'main',
			component: () => import(/* webpackChunkName: "Rating" */ '../views/rating/Main.vue'),
			meta: {
				title: '评级通', // 设置页面标题
			},
			redirect: '/main/property/:id',
			children: [
				//聊天
				{
					path: '/main/chatrat/:id',
					name: 'chatrat',
					component: () => import(/* webpackChunkName: "Market" */ '../views/about/ChatRat.vue'),
				},
				{
					path: '/main/property/:id',
					name: 'property',
					meta: {
						title: '物业信息', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/rating/Property.vue'),
				},
				{
					path: '/main/manage/:id',
					name: 'manage',
					meta: {
						title: '管理', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/rating/Manage.vue'),
				},
				{
					path: '/main/position/:id',
					name: 'position',
					meta: {
						title: '位置', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/rating/Position.vue'),
				},
				{
					path: '/main/population/:id',
					name: 'population',
					meta: {
						title: '人口', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/rating/Population.vue'),
				},
				{
					path: '/main/canvass/:id',
					name: 'canvass',
					meta: {
						title: '招商', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/rating/Canvass.vue'),
				},
				{
					path: '/main/cost/:id',
					name: 'cost',
					meta: {
						title: '估值', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/rating/Cost.vue'),
				},
				{
					path: '/main/banking/:id',
					name: 'banking',
					meta: {
						title: '证券化', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/rating/Banking.vue'),
				},
				{
					path: '/main/broker/:id',
					name: 'broker',
					meta: {
						title: '咨询', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/rating/Broker.vue'),
				},
			],
		},
		// 琥珀咨询
		{
			path: '/consult',
			name: 'consult',
			component: () => import(/* webpackChunkName: "Index" */ '../views/consult/Index.vue'),
			meta: {
				title: '菜单栏页面', // 设置页面标题
			},
		},
		// 自定义组合
		{
			path: '/custom',
			name: 'custom',
			component: () => import(/* webpackChunkName: "Custom" */ '../views/custom/Custom.vue'),
			meta: {
				title: '自定义组合', // 设置页面标题
			},
		},
		//市场分析market
		{
			path: '/market',
			name: 'market',
			component: () => import(/* webpackChunkName: "Market" */ '../views/consult/components/Market.vue'),
		},
		//交易材料Trading
		{
			path: '/trading',
			name: 'trading',
			meta: {
				title: '交易材料', // 设置页面标题
			},
			component: () => import(/* webpackChunkName: "Trading" */ '../views/consult/components/Trading.vue'),
			redirect: '/trading/trading-calculation',
			children: [
				//产权人下面的
				{
					path: '/trading/peoperty',
					name: 'peoperty',
					component: () => import(/* webpackChunkName: "Market" */ '../views/about/Peoperty.vue'),
				},
				//聊天
				{
					path: '/trading/chat',
					name: 'chat',
					component: () => import(/* webpackChunkName: "Market" */ '../views/about/Chat.vue'),
				},
				//管理人下面的
				{
					path: '/trading/manageres',
					name: 'manageres',
					component: () => import(/* webpackChunkName: "Market" */ '../views/about/ManageRes.vue'),
				},
				{
					path: '/trading/value-comparison',
					name: 'valueComparison',
					meta: {
						title: '价值对比', // 设置页面标题
					},
					// 价值对比组件
					component: () => import(/* webpackChunkName: "valueComparison" */ '../views/consult/components/ValueComparison.vue'),
				},
				{
					path: '/trading/trading-calculation',
					name: 'tradingCalculation',
					meta: {
						title: '交易计算', // 设置页面标题
					},
					// 交易计算组件
					component: () => import(/* webpackChunkName: "TradingCalculation" */ '../views/consult/components/TradingCalculation.vue'),
				},
				{
					path: '/trading/octagonal-arena',
					name: 'octagonalArena',
					// 八角擂台组件
					meta: {
						title: '八角擂台', // 设置页面标题
					},
					component: () => import(/* webpackChunkName: "OctagonalArena" */ '../views/consult/components/OctagonalArena.vue'),
				},
				{
					path: '/trading/floor-plan',
					name: 'floorPlan',
					meta: {
						title: '户型图', // 设置页面标题
					},
					// 户型图组件
					component: () => import(/* webpackChunkName: "FloorPlan" */ '../views/consult/components/FloorPlan.vue'),
				},
				{
					path: '/trading/property-owner',
					name: 'propertyOwner',
					meta: {
						title: '查产权人', // 设置页面标题
					},
					// 产权人组件
					component: () => import(/* webpackChunkName: "PropertyOwner" */ '../views/consult/components/PropertyOwner.vue'),
				},
				{
					path: '/trading/management-personnel',
					name: 'managementPersonnel',
					// 查管理人组件
					component: () => import(/* webpackChunkName: "ManagementPersonnel" */ '../views/consult/components/ManagementPersonnel.vue'),
				},
				{
					path: '/trading/broker-search',
					name: 'brokerSearch',
					// 查经纪人组件
					component: () => import(/* webpackChunkName: "BrokerSearch" */ '../views/consult/components/BrokerSearch.vue'),
				},
				{
					path: '/trading/transaction-history',
					name: 'transactionHistory',
					// 交易历史组件
					component: () => import(/* webpackChunkName: "TransactionHistory" */ '../views/consult/components/TransactionHistory.vue'),
				},
				{
					path: '/trading/tenant-statistics',
					name: 'tenantStatistics',
					// 租户统计组件
					component: () => import(/* webpackChunkName: "TenantStatistics" */ '../views/consult/components/TenantStatistics.vue'),
				},
				{
					path: '/trading/tenant-population',
					name: 'tenantPopulation',
					// 租户分析组件
					component: () => import(/* webpackChunkName: "TenantAnalysis" */ '../views/consult/components/TradingPopulation.vue'),
				},
			],
		},
		// 地产金融finance
		{
			path: '/finance',
			name: 'finance',
			component: () => import(/* webpackChunkName: "Finance" */ '../views/consult/components/Finance.vue'),
		},
		//琥珀新闻
		{
			path: '/news',
			name: 'news',
			component: () => import(/* webpackChunkName: "News" */ '../views/consult/components/News.vue'),
		},
		{
			path: '/:pathMatch(.*)*',
			// redirect: '/404',
			component: () => import(/* webpackChunkName: "News" */ '../views/404.vue'),
		},
		{
			path: '/marketMap',
			name: 'marketMap',
			meta: {
				title: '商宇通',
			},
			component: () => import(/* webpackChunkName: "News" */ '../views/h5/market_statistics.vue'),
		},
		{
			path: '/marketMap_wx',
			name: 'marketMapWx',
			component: () => import(/* webpackChunkName: "News" */ '../views/h5/market_statistics_wx.vue'),
		},
	],
});

export default router;
