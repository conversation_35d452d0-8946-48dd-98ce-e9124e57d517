<template>

    <el-form :model="form" label-width="120px">

      <el-form-item label="关键词搜索">
        <!-- <span>关键词搜索</span> -->
        <el-input
        v-model="form.value"
        class="w-50 m-2"
        placeholder="搜索产品、证券、机构"
        style="width: 300px"
      />
      </el-form-item>

      <el-form-item label="发行日期">
        <!-- <span>发行日期</span> -->
      <el-date-picker
        v-model="form.data"
        type="daterange"
        range-separator="To"
        start-placeholder="Start date"
        end-placeholder="End date"
        style="width: 300px;flex: none;"
      />
      </el-form-item>

      <el-form-item label="基础资产">
       <!-- <span>基础资产</span> -->
      <el-checkbox-group fill="#126bae" v-model="form.checkboxGroup1" size="large">
      <el-checkbox-button v-for="Basicassets in assets" :key="Basicassets" :label="Basicassets">
        {{Basicassets }}
      </el-checkbox-button>
      </el-checkbox-group>
      </el-form-item>

      <el-form-item label="基础资产">
       <!-- <span>基础资产</span> -->
      <el-checkbox-group fill="#126bae" v-model="form.checkboxGroup2" size="large" border="false">
      <el-checkbox-button  v-for="subdivide in assetssub" :key="subdivide" :label="subdivide">
        {{subdivide }}
      </el-checkbox-button>
      </el-checkbox-group>
      </el-form-item>
      <!-- 确认取消按钮 -->
      <el-form-item>
        <el-button type="primary"  @click="onSubmit">确认</el-button>
        <el-button @click="oncancel">取消</el-button>
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table :data="tableData" style="width: 100%" :header-cell-style="{background:'#3170a7',color:'#ffffff '}">
    <el-table-column prop="date" label="Date" width="180" />
    <el-table-column prop="name" label="Name" width="180" />
    <el-table-column prop="address" label="Address" />
  </el-table>
  </template>
  
  <script setup>
  import { reactive ,ref} from 'vue'

  const activeName = ref('first')



  // import { ref } from 'vue'
  // do not use same name with ref
//   表单
  const form = reactive({
   value:'',
   data:'',
   checkboxGroup1:[],
   checkboxGroup2:[]
  })
  const assets = ['基础设施收费收益权', '类Reits', 'CMBS/CMBN', '保障房']
  const assetssub = ['全部', '购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '数据中心','保障房','农贸市场']
  const onSubmit = () => {
    console.log('submit!')
  }
//   表格
  const tableData = [
  {
    date: '2016-05-03',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-02',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-04',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    date: '2016-05-01',
    name: 'Tom',
    address: 'No. 189, Grove St, Los Angeles',
  },
]
// 点击取消
const oncancel = () => {
  console.log('cancel!')
  // 清空表单
  form.value = ''
  form.data = ''
  form.checkboxGroup1 = []
  form.checkboxGroup2 = []
}

  </script>

  <style scoped lang="less">
.el-input__wrapper {
  flex: none;
  flex-grow: 0 !important;
}
.el-checkbox-button {
  margin: 5px;
  // border:1px solid #;
}
.el-table__header-wrapper{
  background-color: #000 !important;
}

</style>