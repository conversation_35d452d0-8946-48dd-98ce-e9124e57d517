<template>
	<div>
		<div class="download-container">
			<div class="logo">
				<div class="logo-image">
					<img src="@/assets/images/home/<USER>" alt="商估通" />
				</div>
			</div>

			<div class="content">
				<div class="title">
					<div class="titleDetails">商业地产数据库</div>
				</div>
				<div class="downloadSubtitle">查阅和分析实时的商业地产行业数据</div>

				<div class="download-btn">
					<div class="download_btn-el" @click="router.push('/shangYutong/statistics')">立即使用</div>

					<el-popover trigger="hover" placement="bottom" popper-class="popoverCarouse" :width="178">
						<template #reference>
							<div class="download_btn-el">APP下载</div>
						</template>
						<img
							src="http://oa.biaobiaozhun.com/sm/v1/qrCode/generateQRCode?category=SYT_APP&source=BBZHUN_WEB"
							style="width: 148px"
							class="banner_img"
						/>
					</el-popover>
					<el-popover trigger="hover" placement="bottom" popper-class="popoverCarouse" :width="178">
						<template #reference>
							<div class="download_btn-el">微信小程序</div>
						</template>
						<img
							:src="xiaochengxuSYT"
							style="width: 148px"
							class="banner_img"
						/>
					</el-popover>
				</div>
			</div>
		</div>

		<div class="home-container">
			<div class="title">商宇通可以帮您完成</div>
			<div class="menu-container-title">
				<!-- 左侧菜单 -->
				<div class="menu-container">
					<div
						:class="`menu-item ${menuActiveIndex === index ? 'menu-item-active' + menuActiveIndex : ''}`"
						@mouseenter="changecard(index)"
						v-for="(item, index) in menuList"
						:key="index"
						v-animate-css="{ classes: 'fadeInLeft', delay: index * 200 }"
					>
						<img :src="menuActiveIndex === index ? item.icon : item.hoverIcon" alt="" />

						<!-- <img v-if="menuActiveIndex === index" :src="handleImg(index, false)" alt="" /> -->
						<!-- <img v-else :src="handleImg(index, true)" alt="" /> -->
						<span style="margin-left: 8px">{{ item.title }}</span>
					</div>
					<div class="menu-container-line">持续更新中...</div>
				</div>

				<!-- 右侧地图区域 -->
				<div class="map-container" v-animate-css="'fadeIn'">
					<!-- 地图内容将在此处添加 -->
					<img :src="card_right" alt="" />
				</div>
			</div>
		</div>

		<!--		<div class="containerInterests">-->
		<!--			<div class="title">根据您的需求灵活搭配</div>-->
		<!--			<p class="subtitle">我们为您提供不同的产品配套选择，您可以根据使用需求和团队规模，选择最适合您的权益版本</p>-->
		<!--			<div class="pricing-cards">-->
		<!--				&lt;!&ndash; 基础版 &ndash;&gt;-->
		<!--				<div class="card basic">-->
		<!--					<div class="card-header">-->
		<!--						<div class="card-header-title-container">-->
		<!--							<div class="card-header-title">基础版</div>-->
		<!--							<div class="type">-->
		<!--								<div class="type-line"></div>-->
		<!--								<div class="type-title">适用于轻度浏览</div>-->
		<!--							</div>-->
		<!--						</div>-->

		<!--						<div class="card-header-image">-->
		<!--							<img src="../../assets/images/home/<USER>" alt="" />-->
		<!--						</div>-->
		<!--					</div>-->
		<!--					<div class="card-content">-->
		<!--						<div class="description">为您提供免费的功能体验，无需付费即可使用商宇通基础功能</div>-->
		<!--						<div class="price">-->
		<!--							<span class="currency">¥</span>-->
		<!--							<span class="amount">0</span>-->
		<!--							<span class="period">元起/月</span>-->
		<!--						</div>-->
		<!--						<button class="btn-free" @click="router.push('/rights')">免费试用</button>-->
		<!--						<div class="features">-->
		<!--							<div class="feature-item">-->
		<!--								<img src="../../assets/regarding.png" style="margin-right: 5px" alt="" />-->
		<!--								<span>市场统计</span>-->
		<!--							</div>-->
		<!--						</div>-->
		<!--					</div>-->
		<!--				</div>-->

		<!--				&lt;!&ndash; 标准版 &ndash;&gt;-->
		<!--				<div class="card standard">-->
		<!--					<div class="card-header card-headert">-->
		<!--						<div class="card-header-title-container">-->
		<!--							<div class="card-header-title">标准版</div>-->
		<!--							<div class="type">-->
		<!--								<div class="type-line"></div>-->
		<!--								<div class="type-title">适用于小微团队</div>-->
		<!--							</div>-->
		<!--						</div>-->

		<!--						<div class="card-header-image">-->
		<!--							<img src="../../assets/images/home/<USER>" alt="" />-->
		<!--						</div>-->
		<!--					</div>-->
		<!--					<div class="card-content">-->
		<!--						<div class="description">为您整合企业团队必备权益套餐，支持灵活定制团队使用人数</div>-->
		<!--						<div class="price">-->
		<!--							<span class="currency">¥</span>-->
		<!--							<span class="amount">199</span>-->
		<!--							<span class="period">元起/月</span>-->
		<!--						</div>-->
		<!--						<button class="btn-standard" @click="router.push('/rights')">立即开通</button>-->
		<!--						<div class="features">-->
		<!--							<div class="feature-item">-->
		<!--								<img src="../../assets/regarding.png" style="margin-right: 5px" alt="" />-->
		<!--								<span>市场统计</span>-->
		<!--							</div>-->
		<!--							<div class="feature-item">-->
		<!--								<img src="../../assets/regarding.png" style="margin-right: 5px" alt="" />-->
		<!--								<span>交易材料</span>-->
		<!--							</div>-->
		<!--						</div>-->
		<!--					</div>-->
		<!--				</div>-->

		<!--				&lt;!&ndash; 臻享版 &ndash;&gt;-->
		<!--				<div class="card premium">-->
		<!--					<div class="card-header card-headerth">-->
		<!--						<div class="card-header-title-container">-->
		<!--							<div class="card-header-title">臻享版</div>-->
		<!--							<div class="type">-->
		<!--								<div class="type-line"></div>-->
		<!--								<div class="type-title">专业团队或企业</div>-->
		<!--							</div>-->
		<!--						</div>-->

		<!--						<div class="card-header-image">-->
		<!--							<img src="../../assets/images/home/<USER>" alt="" />-->
		<!--						</div>-->
		<!--					</div>-->
		<!--					<div class="card-content">-->
		<!--						<div class="description">覆盖全套产品权益，全场景全流程功能，专业数据一键获取</div>-->
		<!--						<div class="price">-->
		<!--							<span class="currency">¥</span>-->
		<!--							<span class="amount">399</span>-->
		<!--							<span class="period">元起/月</span>-->
		<!--						</div>-->
		<!--						<button class="btn-premium" @click="router.push('/rights')">立即开通</button>-->
		<!--						<div class="features">-->
		<!--							<div class="feature-item">-->
		<!--								<img src="../../assets/regarding.png" style="margin-right: 5px" alt="" />-->
		<!--								<span>市场统计</span>-->
		<!--							</div>-->
		<!--							<div class="feature-item">-->
		<!--								<img src="../../assets/regarding.png" style="margin-right: 5px" alt="" />-->
		<!--								<span>交易材料</span>-->
		<!--							</div>-->
		<!--							<div class="feature-item">-->
		<!--								<img src="../../assets/regarding.png" style="margin-right: 5px" alt="" />-->
		<!--								<span>信用风险</span>-->
		<!--							</div>-->
		<!--						</div>-->
		<!--					</div>-->
		<!--				</div>-->
		<!--			</div>-->
		<!--			<div class="bottom-container" @click="router.push('/rights')">-->
		<!--				<div class="bottom-container-title">前往卡券市场开通</div>-->
		<!--				<div><img src="../../assets/images/home/<USER>" alt="" /></div>-->
		<!--			</div>-->
		<!--		</div>-->

		<div class="features-container">
			<div class="features-title">让商业地产行业的研究工作变得简单</div>
			<div class="features-grid">
				<div :class="`feature feature-${index + 1}`" v-for="(item, index) in features" :key="index">
					<h2>{{ item.title }}</h2>
					<p>{{ item.desc }}</p>
				</div>
			</div>
		</div>

		<div class="comments-container">
			<div class="commentsTitle">商宇通用户出于这些原因与我们一直保持合作</div>

			<div class="comments-box">
				<waterfallcard></waterfallcard>
			</div>
		</div>
		<siderBar class="siderBar" :isSYT="true"></siderBar>
	</div>
</template>

<script setup>
import xiaochengxuSYT from '@/assets/images/home/<USER>';
import waterfallcard from '../../component/waterfallcard/index.vue';

import siderBar from '../../component/siderBar/index.vue';
import { useRouter } from 'vue-router';
const router = useRouter();
import function1 from '../../assets/images/home/<USER>/function1.png';
import function2 from '../../assets/images/home/<USER>/function2.png';
import function3 from '../../assets/images/home/<USER>/function3.png';
import function4 from '../../assets/images/home/<USER>/function4.png';
import function5 from '../../assets/images/home/<USER>/function5.png';
import function6 from '../../assets/images/home/<USER>/function6.png';
import functions1 from '../../assets/images/home/<USER>/functions1.png';
import functions2 from '../../assets/images/home/<USER>/functions2.png';
import functions3 from '../../assets/images/home/<USER>/functions3.png';
import functions4 from '../../assets/images/home/<USER>/functions4.png';
import functions5 from '../../assets/images/home/<USER>/functions5.png';
import functions6 from '../../assets/images/home/<USER>/functions6.png';
import funcSyt1 from '../../assets/images/home/<USER>/funcSyt1.png';
import funcSyt2 from '../../assets/images/home/<USER>/funcSyt2.png';
import funcSyt3 from '../../assets/images/home/<USER>/funcSyt3.png';
import funcSyt4 from '../../assets/images/home/<USER>/funcSyt4.png';
import funcSyt5 from '../../assets/images/home/<USER>/funcSyt5.png';
import funcSyt6 from '../../assets/images/home/<USER>/funcSyt6.png';

// import Group1 from '../../assets/images/home/<USER>/Group1.png';
// import Group2 from '../../assets/images/home/<USER>/Group2.png';
// import Group3 from '../../assets/images/home/<USER>/Group3.png';
// import Group4 from '../../assets/images/home/<USER>/Group4.png';
// import Group5 from '../../assets/images/home/<USER>/Group5.png';
// import Group6 from '../../assets/images/home/<USER>/Group6.png';
// import Group7 from '../../assets/images/home/<USER>/Group7.png';
// import Group8 from '../../assets/images/home/<USER>/Group8.png';

const features = [
	{
		title: '自有数据支撑',
		desc: '以商业房地产领域完整的研究团队，采用人口普查级别的方法来收集全面、准确的数据，以支撑商宇通产品。',
		// icon: Group1,
		// hoverIcon: Group2,
	},
	{
		title: '强大且全面的功能',
		desc: '基于平台强大的数据、分析能力，让您只需通过基础的需求数据，便能快速测算获取现金流、证券化等全场景专业数据。',
		// icon: Group3,
		// hoverIcon: Group4,
	},
	{
		title: '友好清晰的体验',
		desc: '平台软件覆盖商业地产投资分析全流程，通过清晰地功能划分与页面交互引导，帮助您快速掌握软件，流畅高效地获取所需数据。',
		// icon: Group5,
		// hoverIcon: Group6,
	},
	{
		title: '科学准确的分析',
		desc: '内嵌多重风险管理、价值评估和金融模型，帮您准确评估投资回报和风险。',
		// icon: Group7,
		// hoverIcon: Group8,
	},
];

const menuActiveIndex = ref(0);
const menuList = [
	{ icon: function1, hoverIcon: functions1, activeIcon: funcSyt1, title: '市场统计｜资产地图｜交易计算' },
	{ icon: function2, hoverIcon: functions2, activeIcon: funcSyt2, title: '楼宇信息｜商圈分析｜资产对比' },
	{ icon: function3, hoverIcon: functions3, activeIcon: funcSyt3, title: '城市数据｜参与者｜户型图' },
	{ icon: function4, hoverIcon: functions4, activeIcon: funcSyt4, title: '标准化产品ABS｜REITs' },
	{ icon: function5, hoverIcon: functions5, activeIcon: funcSyt5, title: '信用风险测算｜现金流测算' },
	{ icon: function6, hoverIcon: functions6, activeIcon: funcSyt6, title: '证券化｜Pre-Reits基金' },
];
const card_right = ref(menuList[0].activeIcon);

const changecard = (index) => {
	menuActiveIndex.value = index;
	card_right.value = menuList[index].activeIcon;
};

// 方法3：如果使用 Webpack，可以这样导入
const requireImage = (name) => {
	return require(`@/assets/images/home/<USER>/function${name}.png`);
};

// const handleImg = (index, active) => {
// 	return new URL(`../../assets/images/home/<USER>/function${active ? 's' : ''}${index + 1}.png`, import.meta.url).href;
// };
</script>

<style scoped lang="less">
.banner_img {
	display: block;
	width: 148px;
	height: 148px;
	margin: 0 auto;
	border-radius: 10px;
}
.comments-box {
	background-image: url('@/assets/images/home/<USER>');
	background-size: 100% 100%;
}
.comments-container {
	padding: 0px 0px 89px 0;
	background: #f3f8fd;
	.commentsTitle {
		text-align: center;
		color: #1d2129;
		font-size: 36px;
		font-weight: 500;
		line-height: 170px;
	}
}

.download-container {
	height: calc(100vh - 60px);
	// background: linear-gradient(180deg, #edf4ff 0%, #f7faff 100%);
	background: #e1eefd;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;

	.logo {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 100%;
		height: 260px;
		background: linear-gradient(106.01deg, #d4f0fc 15.42%, #deefff 37.53%, #d3e7ff 61.01%);
		// background: linear-gradient(180deg, #a1c6ff 0%, rgba(52, 121, 233, 0) 100%);
		.logo-image {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: end;
			img {
				width: 56px;
				z-index: 99;
				object-fit: contain;
			}
		}
	}

	.content {
		background-image: url('../../assets/images/home/<USER>');
		background-size: cover;
		background-repeat: no-repeat;
		width: 100%;
		height: calc(100vh - 0px);
		text-align: center;
		z-index: 2;

		.title {
			color: #1d2129;
			margin: 285px auto 8px auto;
			font-size: 55px;
			font-weight: 600;
			line-height: 84px;
			display: flex;
			justify-content: center;
		}
		.titleDetails {
			background: linear-gradient(90deg, #3aa6ff 0%, #1358f3 100%);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
		.downloadSubtitle {
			color: #23366e;
			letter-spacing: 5px;
			margin-bottom: 28px;
			font-size: 20px;
			font-weight: 400;
			line-height: 28px;
		}

		.download-btn {
			margin-bottom: 40px;
			display: flex;
			justify-content: center;
			gap: 12px;
			.download_btn-el {
				background: linear-gradient(90deg, #2468f2 3.21%, #29acfe 100%);
				font-size: 24px;
				color: #ffffff;
				width: 256px;
				height: 60px;
				line-height: 60px;
				border-radius: 12px;
				cursor: pointer;
			}

			.trial-tip {
				margin-top: 12px;
				color: #7382ad;
				font-size: 16px;
				font-weight: 400;
				line-height: 22.4px;
			}
			.trial {
				font-size: 16px;
				font-weight: 600;
				line-height: 22.4px;
				color: #083ef0;
			}
		}
	}

	.platform-btns {
		display: flex;
		justify-content: center;
		margin-top: 78px;
		gap: 32px;

		.platform-btn {
			display: flex;
			align-items: center;
			padding: 14.8px 20px;
			background: #fff;
			border-radius: 12px;
			cursor: pointer;
			transition: all 0.3s;

			img {
				margin-right: 8px;
			}

			&:hover {
				background: #f5f7fa;
			}
		}
	}
}

.features-container {
	background-image: url('../../assets/images/home/<USER>');
	background-size: cover;
	background-repeat: no-repeat;
	padding: 60px 40px;

	.features-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 30px;
		max-width: 1200px;
		margin: 0 auto;
	}

	.features-title {
		font-weight: 500;
		font-size: 40px;
		line-height: 60px;
		text-align: center;

		grid-column: 1 / -1;
		text-align: center;
		color: #1d2129;
		margin-bottom: 50px;
	}

	.feature {
		width: 508px;
		height: 200px;
		background: #ffffff;
		border-radius: 20px;
		padding: 30px 40px;
		position: relative;
		overflow: hidden;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
		transition: transform 0.3s ease;
		background-size: cover;
		background-position: right bottom;
		background-repeat: no-repeat;
	}

	.feature:hover {
		transform: translateY(-5px);

		h2 {
			color: #1868f1;
		}
		p {
			color: #1d2129;
		}
	}

	.feature h2 {
		z-index: 2;
		position: relative;
		color: #1d2129;
		font-size: 24px;
		margin-bottom: 16px;
		font-weight: 600;
	}

	.feature p {
		position: relative;
		z-index: 2;
		color: #666;
		line-height: 1.8;
		font-size: 16px;
		font-weight: 400;
	}
	.feature-1 {
		background-image: url(../../assets/images/home/<USER>/Group2.png);
	}
	.feature-1:hover {
		background-image: url(../../assets/images/home/<USER>/Group1.png);
	}
	.feature-2 {
		background-image: url(../../assets/images/home/<USER>/Group4.png);
	}
	.feature-2:hover {
		background-image: url(../../assets/images/home/<USER>/Group3.png);
	}
	.feature-3 {
		background-image: url(../../assets/images/home/<USER>/Group6.png);
	}
	.feature-3:hover {
		background-image: url(../../assets/images/home/<USER>/Group5.png);
	}
	.feature-4 {
		background-image: url(../../assets/images/home/<USER>/Group8.png);
	}
	.feature-4:hover {
		background-image: url(../../assets/images/home/<USER>/Group7.png);
	}
}

.home-container {
	height: 86vh;
	background: #fdfeff;
	background-image: url('../../assets/images/home/<USER>');
	background-size: cover;
	background-repeat: no-repeat;
	.menu-container-title {
		display: flex;
		justify-content: center;
		// height: 100vh;
	}
	.title {
		font-weight: 500;
		font-size: 40px;
		line-height: 60px;
		text-align: center;
		color: #1d2129;
		padding: 70px 0 56px 0;
	}
	.menu-container {
		padding: 0 70px 0 0;
		.menu-container-line {
			font-weight: 500;
			font-size: 18px;
			color: #86909c;
			margin: 10px 0 0 16px;
		}
		.menu-item-active0,
		.menu-item-active1,
		.menu-item-active2,
		.menu-item-active3,
		.menu-item-active4,
		.menu-item-active5,
		.menu-item-active6 {
			box-shadow: 0px 8px 30px 0px #b0bfe73d;
			padding: 30px 36px 30px 16px !important;
			background: #ffffff;
			margin-bottom: 32px !important;
			// background: rgba(255, 255, 255, 0.9);
			.decoration-icon {
				color: #fff !important;
			}
			span {
				color: #1868f1 !important;
			}
		}
		// .menu-item:hover {

		// }
		.menu-item {
			width: 368px;
			display: flex;
			align-items: center;
			margin-bottom: 28px;
			padding: 0px 36px 0px 16px;
			border-radius: 8px;
			cursor: pointer;
			transition: all 0.3s ease;

			&:hover {
				transform: translateX(5px);
				box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
			}

			i {
				margin-right: 10px;
				font-size: 20px;
				color: #409eff;
			}

			span {
				font-weight: 500;
				font-size: 22px;
				color: #1d2129;
			}
		}
	}

	.map-container {
		// flex: 1;
		width: 877px;
		min-width: 200px;
		height: 530px;
		img {
			width: 100%;
		}
		// background: #fff;
		// border-radius: 12px;
		// box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	}
}

.containerInterests {
	text-align: center;
	padding: 70px 20px;
	background: linear-gradient(180deg, #ffffff 0%, #f9fbff 101.05%);

	.title {
		font-weight: 500;
		font-size: 40px;
		line-height: 60px;
		text-align: center;
		margin-bottom: 16px;
	}

	.subtitle {
		color: #666;
		max-width: 800px;
		margin: 0 auto 40px;
	}

	.pricing-cards {
		display: flex;
		justify-content: center;
		gap: 24px;
		max-width: 1200px;
		margin: 0 auto;
	}
	.bottom-container {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0 auto;
		margin: 53px 0 0 0;
		cursor: pointer;
		.bottom-container-title {
			font-weight: 500;
			font-size: 18px;
			line-height: 25.2px;
			letter-spacing: 0px;
			text-align: center;
			color: #1868f1;
			margin-right: 3px;
		}
		img {
			margin-top: 4px;
			width: 18px;
		}
	}
	.card {
		border-top-left-radius: 12px;
		border-top-right-radius: 64px;
		border-bottom-right-radius: 12px;
		border-bottom-left-radius: 12px;
		width: 384px;
		height: 415px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
		transition: transform 0.3s;
		position: relative;

		&:hover {
			transform: translateY(-4px);
		}
		.card-headert {
			background: linear-gradient(180deg, #fff3d2 0%, #fff3d2 55%, rgba(255, 243, 210, 0) 100%) !important;
			.card-header-title {
				color: #5d340a !important;
			}
			.type-line {
				background: #af7d49 !important;
			}
			.type-title {
				color: #644a11 !important;
			}
		}
		.card-headerth {
			background: linear-gradient(180deg, #625a93 0%, #625a93 55%, rgba(98, 90, 147, 0) 100%) !important;
			.card-header-title {
				color: #e2cfff !important;
			}
			.type-line {
				background: #e2cfff !important;
			}
			.type-title {
				color: #c5b7e9 !important;
			}
		}
		.card-header {
			border-top-left-radius: 12px;
			border-top-right-radius: 64px;
			display: flex;
			justify-content: space-between;
			height: 193px;
			background: linear-gradient(180deg, #cbe9fe 0%, #cbe9fe 55%, rgba(203, 233, 254, 0) 100%);
			.card-header-title-container {
				display: flex;
				flex-direction: column;
				justify-content: left;
				margin: 28px 0 0 40px;
			}
			.card-header-image {
				width: 140px;
				height: 152px;
				z-index: 22;
				position: absolute;
				right: 9px;
				top: -19px;
			}
			.card-header-title {
				font-weight: 600;
				font-size: 32px;
				line-height: 44.8px;
				color: #143a55;
				margin-left: -35px;
			}
			.type {
				display: flex;
				align-items: center;
				.type-line {
					width: 8px;
					height: 8px;
					border-radius: 50%;
					background: #4491f7;
					margin-right: 8px;
				}
				.type-title {
					font-weight: 400;
					font-size: 16px;
					line-height: 28px;
					color: #00436c;
				}
			}
		}

		.card-content {
			border: 1px solid #fff;
			position: absolute;
			top: 114px;
			left: 20px;
			right: auto;
			width: calc(100% - 80px);
			height: 260px;
			background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 48.8%, rgba(255, 255, 255, 0) 98.58%);
			border-radius: 16px;
			padding: 20px;
			display: flex;
			flex-direction: column;
			align-items: baseline;

			.description {
				font-weight: 400;
				font-size: 16px;
				text-align: left;
				line-height: 32px;
				color: #1d2129;
				margin-bottom: 48px;
			}

			.price {
				margin-bottom: 7px;
				.currency {
					font-size: 20px;
					// vertical-align: top;
				}
				.amount {
					font-size: 36px;
					font-weight: 500;
				}
				.period {
					color: #666;
					font-size: 12px;
					margin-left: 4px;
				}
			}

			button {
				width: 100%;
				padding: 12px;
				border: none;
				border-radius: 8px;
				font-size: 16px;
				cursor: pointer;
				margin-bottom: 16px;
			}

			.features {
				display: flex;
				flex-wrap: wrap;
				width: 100%;
				.feature-item {
					width: 33%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 12px;
					color: #4e5969;
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;

					.check-icon {
						color: #52c41a;
						margin-right: 8px;
					}
				}
			}
		}
	}

	.basic {
		background: #fff;
		.btn-free {
			background: linear-gradient(90deg, #41abf7 0%, #418df7 100%);
			color: white;
		}
	}

	.standard {
		background: #fff;
		.btn-standard {
			background: linear-gradient(90deg, #fcda9d 0%, #fdc669 100%);
			color: #5d340a;
		}
	}

	.premium {
		background: #fff;
		.btn-premium {
			background: linear-gradient(90deg, #575072 0%, #3f3e57 100%);
			color: #fdca73;
		}
	}
}

.siderBar {
	top: 14rem !important;
}
</style>
