import { fetch } from './http';

export function ArticleList(params) {
	//获取新闻列表
	return fetch({
		url: '/api/article/article-list',
		method: 'get',
		params: params,
	});
}
export function ArticleDetail(params) {
	//获取新闻列表
	return fetch({
		url: '/api/article/article-detail',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function AdvertList(params) {
	//获取广告位列表
	return fetch({
		url: '/api/standardspace/space-list',
		method: 'get',
		params: params,
	});
}

export function AdvertDetail(params) {
	//获取广告位详情
	return fetch({
		url: '/api/standardspace/space-detail',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function getFacility(params) {
	//获取配套设施
	return fetch({
		url: '/api/standardspace/buildingSurroundingSupportingInfoCoordinate',
		method: 'post',
		params: params,
		loading: true,
	});
}

export function ArticleReferral(data) {
	//获取新闻推荐
	return fetch({
		url: '/api/article/article-related-recommend',
		method: 'get',
		data: data,
		loading: true,
	});
}

export function updateNickname(data) {
	//更新用户名
	return fetch({
		url: '/api/maps/updateNickname',
		method: 'POST',
		data: data,
		loading: true,
	});
}

export function getArea(params) {
	//获取区域下拉
	return fetch({
		url: '/api/article/insight-area-select',
		method: 'get',
		params: params,
	});
}

export function Addcontact(params) {
	//提交标准空间联系方式
	return fetch({
		url: '/api/standardspace/add-space-link',
		method: 'post',
		params: params,
		loading: true,
	});
}
export function getBearer(params) {
	//获取区域下拉
	return fetch({
		url: '/sm/v1/ai-token/get-access-token',
		method: 'get',
		params: params,
		loading: false,
	});
}

export function getDistrict(params) {
	//获取支持的城市和地区
	return fetch({
		url: '/api/common/city-district',
		method: 'get',
		params: params,
	});
}

export function getPresignedUrl(data) {
	//上传图片
	return fetch({
		url: '/api/user/change-head',
		method: 'PUT',
		data: data,
	});
}

export function getchangeName(data) {
	//修改用户名
	return fetch({
		url: '/api/user/change-name',
		method: 'PUT',
		data: data,
	});
}

export function getChangePhone(data) {
	//修改手机号
	return fetch({
		url: '/api/user/change-phone',
		method: 'PUT',
		data: data,
	});
}

export function getChangePassword(data) {
	//修改密码
	return fetch({
		url: '/api/user/change-password',
		method: 'PUT',
		data: data,
	});
}

export function getChangeHeadpre(params) {
	//上传头像预签名
	return fetch({
		url: '/api/user/change-head-pre',
		method: 'get',
		params: params,
	});
}
