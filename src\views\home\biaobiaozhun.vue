<template>
	<div class="container_box">
		<head>
			<meta name="keywords" content="青岛写字楼出租, 黄岛写字楼出租" />
		</head>
		<div v-if="!isMobile">
			<div class="bannerContent">
				<el-carousel trigger="click">
					<el-carousel-item v-for="(item, index) in 2" :key="index">
						<div class="carouselfirst" v-if="index == 0">
							<div class="leftContent">
								<div class="carouseTopContent">
									<div class="leftCon">
										<img src="@/assets/newLogo.png" alt="" />
									</div>
									<div class="rightCon">标标准APP</div>
								</div>
								<div class="carouseContent">
									<img src="@/assets/introduceBrokerpt.png" alt="" />
								</div>
								<div class="carouseDown">
									<el-popover trigger="hover" placement="bottom" popper-class="popoverCarouse" :width="178">
										<template #reference>
											<div class="carouseDownLeft">
												<img src="@/assets/downphone.png" alt="" style="margin-right: 4px; width: 20px; height: 20px" />
												<img src="@/assets/downappText.png" alt="" style="width: 80px; height: 28px; object-fit: contain" />
											</div>
										</template>
										<img
											src="http://oa.biaobiaozhun.com/sm/v1/qrCode/generateQRCode?category=BBZ_APP&source=BBZHUN_WEB"
											style="width: 148px"
											class="banner_img"
										/>
									</el-popover>

									<el-popover trigger="hover" placement="bottom" popper-class="popoverCarouse" :width="178">
										<template #reference>
											<div class="carouseDownRight">
												<img src="@/assets/downrphone.png" alt="" style="margin-right: 4px; width: 20px; height: 20px" />
												<img src="@/assets/downapprText.png" alt="" style="width: 100px; height: 28px; object-fit: contain" />
											</div>
										</template>
										<img :src="xiaochengxu" style="width: 148px" class="banner_img" />
									</el-popover>
								</div>
							</div>
						</div>
						<div class="carouseltwo" v-else>
							<div class="leftContent">
								<div class="carouseTopContent">
									<div class="rightCon" style="margin-right: 22px">真房源</div>
									<div class="rightCon">真价格</div>
								</div>
								<div class="carouseContent">盛佳联行资深经纪人为您提供一站式合同期服务</div>
								<div class="carouseDown">
									<el-popover trigger="hover" placement="bottom" popper-class="popoverCarouse" :width="178">
										<template #reference>
											<div class="carouseDownLeft">
												<img src="@/assets/downphone2.png" alt="" style="margin-right: 4px; width: 20px; height: 20px" />
												<img src="@/assets/downappText2.png" alt="" style="width: 80px; height: 28px; object-fit: contain" />
											</div>
										</template>
										<img
											src="http://oa.biaobiaozhun.com/sm/v1/qrCode/generateQRCode?category=BBZ_APP&source=BBZHUN_WEB"
											style="width: 148px"
											class="banner_img"
										/>
									</el-popover>
									<el-popover trigger="hover" placement="bottom" popper-class="popoverCarouse" :width="178">
										<template #reference>
											<div class="carouseDownRight">
												<img src="@/assets/downrphone2.png" alt="" style="margin-right: 4px; width: 20px; height: 20px" />
												<img src="@/assets/downapprText2.png" alt="" style="width: 100px; height: 28px; object-fit: contain" />
											</div>
										</template>
										<img :src="xiaochengxu" style="width: 148px" class="banner_img" />
									</el-popover>
								</div>
							</div>
						</div>
					</el-carousel-item>
				</el-carousel>
			</div>
			<!-- <div style="opacity: 0; height: 0">青岛写字楼出租、黄岛写字楼出租</div> -->
			<div class="card_box1">
				<div class="card_list">
					<div class="card_list_left">
						<div
							:class="`card_title ${menuActiveIndex === index ? 'card_titleActive' + menuActiveIndex : ''}`"
							v-for="(item, index) in cardList"
							:key="index"
							@mouseenter="changecard(index)"
							v-animate-css="{ classes: 'fadeInLeft', delay: index * 200 }"
						>
							<div class="card_tag">
								<img :src="menuActiveIndex === index ? item.activeIcon : item.hoverIcon" alt="" />
								<div class="card_tag_title">
									{{ item.title }}
								</div>
							</div>

							<div class="card_tagDetails">{{ item.tips }}</div>
						</div>
					</div>
					<div class="card_list_right">
						<img :src="card_right" alt="" />
					</div>
				</div>
			</div>
		</div>
		<div v-else>
			<div class="mobileBannerContent">
				<el-carousel trigger="click">
					<el-carousel-item v-for="(item, index) in 2" :key="index">
						<div class="carouselfirst" v-if="index == 0">
							<div class="leftContent">
								<div class="carouseTopContent">
									<div class="leftCon">
										<img src="@/assets/newLogo.png" alt="" />
									</div>
									<div class="rightCon">标标准APP</div>
								</div>
								<div class="carouseContent">
									<img src="@/assets/introduceBrokerptmobile.png" alt="" />
								</div>
								<div class="carouseDown">
									<div class="carouseDownLeft" @click="handlemobileOpen('app')">
										<img src="@/assets/downphonemobile.png" alt="" style="width: 12px; object-fit: contain; margin-right: 4px" />
										<img style="width: 46px; object-fit: contain" src="@/assets/downappTextmobile.png" alt="" />
									</div>
									<div class="carouseDownRight" @click="handlemobileOpen('program')">
										<img src="@/assets/downrphonemobile.png" alt="" style="width: 12px; object-fit: contain; margin-right: 4px" />
										<img style="width: 58px; object-fit: contain" src="@/assets/downapprTextmobile.png" alt="" />
									</div>
								</div>
							</div>
						</div>
						<div class="carouseltwo" v-else>
							<div class="leftContent">
								<div class="carouseTopContent">
									<div class="rightCon" style="margin-right: 12px">真房源</div>
									<div class="rightCon">真价格</div>
								</div>
								<div class="carouseContent">盛佳联行资深经纪人为您提供一站式合同期服务</div>
								<div class="carouseDown">
									<div class="carouseDownLeft" @click="handlemobileOpen('app')">
										<img src="@/assets/downphonemobile2.png" alt="" style="width: 12px; object-fit: contain; margin-right: 4px" />
										<img style="width: 46px; object-fit: contain" src="@/assets/downappTextnmobile2.png" alt="" />
									</div>

									<div class="carouseDownRight" @click="handlemobileOpen('program')">
										<img src="@/assets/downrphonemoboile2.png" alt="" style="width: 12px; object-fit: contain; margin-right: 4px" />
										<img style="width: 58px; object-fit: contain" src="@/assets/downapprTextmobile2.png" alt="" />
									</div>
								</div>
							</div>
						</div>
					</el-carousel-item>
				</el-carousel>
				<div class="content_container">
					<el-collapse accordion :model-value="activeNames" @change="handleChange">
						<el-collapse-item v-for="(item, index) in propertyLIst" :key="index" :name="index">
							<template #title>
								<div class="content_title">
									<img :src="activeNames[0] === index ? item.activeIcon : item.image" alt="content" class="content-image" />
									<div :class="{ active: activeNames[0] === index }">{{ item.title }}</div>
								</div>
							</template>
							<div class="content_content">
								<div class="content_content_box">
									<div class="content_content_title">{{ item.content }}</div>
									<img :src="item.img" alt="content" class="content-img" />
								</div>
							</div>
						</el-collapse-item>
					</el-collapse>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted } from 'vue';
import { ref } from 'vue';
import xiaochengxu from '../../assets/images/home/<USER>';
import card6 from '../../assets/SelectionSpace.png';
import card7 from '../../assets/SelectionSpace1.png';
import card8 from '../../assets/SelectionSpace2.png';

import bbzimg1 from '../../assets/images/home/<USER>/bbzimg1.png';
import bbzimg2 from '../../assets/images/home/<USER>/bbzimg2.png';
import bbzimg3 from '../../assets/images/home/<USER>/bbzimg3.png';
import bbzimg4 from '../../assets/images/home/<USER>/bbzimg4.png';
import bbzimg5 from '../../assets/images/home/<USER>/bbzimg5.png';
import bbzimg6 from '../../assets/images/home/<USER>/bbzimg6.png';

import SelectionSpacemobile from '../../assets/SelectionSpacemobile.png';
import SelectionSpacemobile2 from '../../assets/SelectionSpacemobile2.png';
import SelectionSpacemobile3 from '../../assets/SelectionSpacemobile3.png';

const menuActiveIndex = ref(0);
// 菜单栏

const cardList = [
	{
		hoverIcon: bbzimg2,
		activeIcon: bbzimg1,
		icon: card6,
		title: '臻选空间',
		tips: '依托自研数据模型，精准挖掘楼宇价值，资深专员全程服务，助您选址无忧、赢在起点',
	},
	{
		hoverIcon: bbzimg4,
		activeIcon: bbzimg3,
		icon: card7,
		title: '琥珀资讯',
		tips: '聚焦于商业地产行业的时讯要闻与分析，标标准助您洞察趋势',
	},
	{
		hoverIcon: bbzimg6,
		activeIcon: bbzimg5,
		icon: card8,
		title: '数据报告',
		tips: '涵盖全面楼宇数据的自动生成报告，解决您的数据需求',
	},
];

//是否是移动端
const isMobile = ref(false);

const card_right = ref(cardList[0].icon);

const activeNames = ref([0]);

const propertyLIst = ref([
	{
		title: '甄选空间',
		image: bbzimg2,
		activeIcon: bbzimg1,
		img: SelectionSpacemobile,
		content: '依托自研数据模型，精准挖掘楼宇价值，资深专员全程服务，助您选址无忧、赢在起点',
	},
	{
		title: '琥珀资讯',
		image: bbzimg4,
		activeIcon: bbzimg3,
		img: SelectionSpacemobile2,
		content: '聚焦于商业地产行业的时讯要闻与分析，标标准助您洞察趋势',
	},
	{
		title: '数据报告',
		image: bbzimg6,
		activeIcon: bbzimg5,
		img: SelectionSpacemobile3,
		content: '涵盖全面楼宇数据的自动生成报告，解决您的数据需求',
	},
]);

onMounted(() => {
	window.scrollTo(0, 0);
	handleIsMobile(); //判断是否是移动端
});

//判断是否是移动端
const handleIsMobile = () => {
	const innerWidth = window.innerWidth;
	if (innerWidth < 768) {
		isMobile.value = true;
	} else {
		isMobile.value = false;
	}
};

const changecard = (index) => {
	menuActiveIndex.value = index;
	card_right.value = cardList[index].icon;
};

const handleChange = (val) => {
	activeNames.value = [val];
};

function handlemobileOpen(val) {
	if (val == 'app') {
		window.open('https://oa.biaobiaozhun.com/sm/downloadPage/page?category=BBZ_APP&source=BBZHUN_WEB', '_blank');
	} else {
		window.location.href = 'weixin://dl/business/?appid=wx258c72e2b5a4a7dd&path=pages/newtabbar/index';
	}
}
</script>
<style lang="scss">
.popoverCarouse {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12px !important;
	background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%) !important;
	.el-popper__arrow {
		display: none;
	}
}
</style>
<style lang="scss" scoped>
.banner_img {
	display: block;
	width: 148px;
	height: 148px;
	margin: 0 auto;
	border-radius: 10px;
}
.container_box {
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, 1);

	.card_box1 {
		width: 100%;
		height: calc(100vh - 50px);
		margin-bottom: 88px;
		background: linear-gradient(180deg, #ffffff 0%, #f1f6fe 100%);
		.card_list {
			width: 100%;
			height: 100%;
			padding: 0 15px;
			box-sizing: border-box;
			display: flex;
			justify-content: center;
			align-items: center;
			.card_list_left {
				width: 408px;
				height: 466px;
				.card_titleActive0,
				.card_titleActive1,
				.card_titleActive2 {
					width: 408px;
					height: 142px;
					border-radius: 12px;
					box-shadow: 0px 8px 30px 0px #b0bfe73d;
					background: #ffffff;

					.card_tag_title {
						color: #1868f1 !important;
					}
				}
				.card_title {
					transition: all 0.3s ease;
					width: 408px;
					height: 142px;
					margin-bottom: 20px;
					box-sizing: border-box;
					border-radius: 12px;
					cursor: pointer;
					padding: 20px 24px;
					box-sizing: border-box;
					.card_tag {
						height: 40px;
						display: flex;
						margin-bottom: 8px;

						img {
							width: 40px;
							height: 40px;
							margin-right: 8px;
						}
						.card_tag_title {
							font-weight: 500;
							font-size: 24px;
							line-height: 40px;
							color: #1d2129;
						}
					}
					.card_tagDetails {
						font-weight: 400;
						font-size: 18px;
						line-height: 27px;
						color: #86909c;
					}
				}
			}
		}
		.card_list_right {
			width: 784px;
			height: 515px;
			margin-left: 80px;
			img {
				width: 100%;
				height: 515px;
			}
		}
	}
}

.bannerContent {
	width: 100%;
	height: calc(100vh - 50px);
	.el-carousel {
		width: 100%;
		height: calc(100vh - 50px);
		::v-deep .el-carousel__container {
			height: calc(100vh - 50px) !important;
		}
	}

	.el-carousel > :nth-child(4) > :nth-child(n) {
		padding: 0px;
		padding-bottom: 40px;
	}

	.el-carousel > :nth-child(4) {
		::v-deep .is-active > :nth-child(1) {
			background: #4080ff !important;
		}
		// > :nth-child(1) {
		// }
	}

	.el-carousel > :nth-child(4) > :nth-child(n) > :nth-child(1) {
		width: 32px;
		height: 4px;
		border-radius: 6px;
		background-color: rgb(113 102 105 / 50%);
	}

	.carouselfirst {
		height: calc(100vh - 50px);
		background-image: url('@/assets/introduceImg.png');
		background-size: cover;
		background-repeat: no-repeat;
		display: flex;
		padding: 0px 17%;
		align-items: center;

		.leftContent {
			width: 448px;
			height: 289px;
			margin-top: -20px;
			.carouseTopContent {
				display: flex;
				height: 60px;
				.leftCon {
					display: flex;
					align-items: center;
					margin-right: 6px;
					img {
						width: 48px;
						height: 48px;
					}
				}
				.rightCon {
					font-weight: 600;
					font-size: 40px;
					line-height: 60px;
					color: #1d2129;
				}
			}

			.carouseContent {
				width: 100%;
				height: 84px;
				margin-bottom: 47px;
			}
			.carouseDown {
				display: flex;
				.carouseDownLeft {
					cursor: pointer;
					width: 178px;
					height: 56px;
					border-radius: 100px;
					margin-right: 12px;
					border: 1px solid transparent;
					background-clip: padding-box, border-box;
					background-origin: padding-box, border-box;
					background-image: linear-gradient(90deg, #ecf4ff, #ecf4ff 42%, #ecf4ff), linear-gradient(151deg, #0e23de, #1290fa, #1290fa);
					display: flex;
					align-items: center;
					justify-content: center;
					&:hover {
						background-image: linear-gradient(90deg, #c8d3fa, #c8d3fa 42%, #c7e4fd), linear-gradient(151deg, #0e23de, #1290fa, #1290fa);
					}
				}
				.carouseDownRight {
					cursor: pointer;
					width: 178px;
					height: 56px;
					border-radius: 100px;
					border: 1px solid transparent;
					background-clip: padding-box, border-box;
					background-origin: padding-box, border-box;
					background-image: linear-gradient(90deg, #ecf4ff, #ecf4ff 42%, #ecf4ff), linear-gradient(151deg, #0e23de, #1290fa, #1290fa);
					display: flex;
					align-items: center;
					justify-content: center;
					&:hover {
						background-image: linear-gradient(90deg, #c8d3fa, #c8d3fa 42%, #c7e4fd), linear-gradient(151deg, #0e23de, #1290fa, #1290fa);
					}
				}
			}
		}
	}

	.carouseltwo {
		height: calc(100vh - 50px);
		background-image: url('@/assets/introduceImg2.png');
		background-size: cover;
		background-repeat: no-repeat;
		display: flex;
		padding: 0px 17%;
		align-items: center;

		.leftContent {
			width: 560px;
			height: 256px;
			margin-top: -60px;
			.carouseTopContent {
				display: flex;
				height: 80px;
				margin-bottom: 20px;
				.rightCon {
					font-size: 56px;
					font-weight: 500;
					line-height: 80px;
					color: #ffffff;
				}
			}

			.carouseContent {
				width: 100%;
				height: 32px;
				margin-bottom: 64px;
				color: #ffffff;
				font-weight: 500;
				font-size: 24px;
				line-height: 32px;
			}
			.carouseDown {
				display: flex;
				.carouseDownLeft {
					cursor: pointer;
					width: 178px;
					height: 56px;
					border-radius: 100px;
					margin-right: 12px;
					border: 1px solid #ffffff;
					display: flex;
					align-items: center;
					justify-content: center;
					&:hover {
						background: #ffffff4d;
					}
				}
				.carouseDownRight {
					cursor: pointer;
					width: 178px;
					height: 56px;
					border-radius: 100px;
					border: 1px solid #ffffff;
					display: flex;
					align-items: center;
					justify-content: center;
					&:hover {
						background: #ffffff4d;
					}
				}
			}
		}
	}
}

.mobileBannerContent {
	width: 100%;
	padding-bottom: 146px;
	.el-carousel {
		width: 100%;
		::v-deep .el-carousel__container {
			height: 342px !important;
		}
	}
	.el-carousel > :nth-child(4) > :nth-child(n) {
		padding: 0px;
		padding-bottom: 12px;
	}

	.el-carousel > :nth-child(4) {
		::v-deep .is-active > :nth-child(1) {
			background: #4080ff !important;
		}
	}

	.el-carousel > :nth-child(4) > :nth-child(n) > :nth-child(1) {
		width: 16px;
		height: 2px;
		border-radius: 3px;
		background-color: rgb(113 102 105 / 50%);
	}
	.carouselfirst {
		height: 342px;
		background-image: url('@/assets/introduceImgmobile.png');
		background-size: cover;
		background-repeat: no-repeat;

		.leftContent {
			width: 100%;
			height: 205px;
			padding-top: 45px;
			display: flex;
			flex-direction: column;
			align-items: center;
			.carouseTopContent {
				display: flex;
				height: 24px;
				.leftCon {
					display: flex;
					align-items: center;
					margin-right: 4.8px;
					img {
						width: 19.2px;
						height: 19.2px;
					}
				}
				.rightCon {
					font-weight: 600;
					font-size: 16px;
					line-height: 24px;
					color: #1d2129;
				}
			}

			.carouseContent {
				width: 208px;
				height: 39px;
				margin-bottom: 16px;
				img {
					width: 100%;
					height: 100%;
				}
			}
			.carouseDown {
				display: flex;
				.carouseDownLeft {
					cursor: pointer;
					width: 102px;
					height: 32px;
					border-radius: 57px;
					margin-right: 8px;
					border: 1px solid transparent;
					background-clip: padding-box, border-box;
					background-origin: padding-box, border-box;
					background-image: linear-gradient(90deg, #ecf4ff, #ecf4ff 42%, #ecf4ff), linear-gradient(151deg, #0e23de, #1290fa, #1290fa);
					display: flex;
					align-items: center;
					justify-content: center;
				}
				.carouseDownRight {
					cursor: pointer;
					width: 102px;
					height: 32px;
					border-radius: 57px;
					border: 1px solid transparent;
					background-clip: padding-box, border-box;
					background-origin: padding-box, border-box;
					background-image: linear-gradient(90deg, #ecf4ff, #ecf4ff 42%, #ecf4ff), linear-gradient(151deg, #0e23de, #1290fa, #1290fa);
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}

	.carouseltwo {
		height: 342px;
		background-image: url('@/assets/introduceImgmobile2.png');
		background-size: cover;
		background-repeat: no-repeat;
		.leftContent {
			width: 100%;
			padding-top: 64px;
			height: 109px;
			display: flex;
			flex-direction: column;
			align-items: center;
			.carouseTopContent {
				display: flex;
				height: 39px;
				margin-bottom: 4px;
				.rightCon {
					font-weight: 600;
					font-size: 26px;
					line-height: 39px;
					text-align: center;
					color: #ffffff;
				}
			}

			.carouseContent {
				width: 100%;
				height: 18px;
				margin-bottom: 16px;
				text-align: center;
				font-weight: 400;
				font-size: 12px;
				line-height: 18px;
				color: #ffffff;
			}
			.carouseDown {
				display: flex;
				.carouseDownLeft {
					cursor: pointer;
					width: 102px;
					height: 32px;
					border-radius: 57px;
					margin-right: 8px;
					border: 1px solid #ffffff;
					display: flex;
					align-items: center;
					justify-content: center;
				}
				.carouseDownRight {
					cursor: pointer;
					width: 102px;
					height: 32px;
					border-radius: 57px;
					border: 1px solid #ffffff;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}
}

.content_container {
	background-color: #f4f7fc;
	::v-deep .el-collapse .el-collapse-item__content {
		padding-bottom: 0px;
	}
	.content_title {
		padding: 0 16px;
		display: flex;
		align-items: center;
		font-weight: 500;
		font-size: 15px;

		.active {
			color: #1868f1;
		}
	}
	.content-image {
		width: 28px;
		height: 28px;
		margin-right: 4px;
	}
	.content_content {
		display: flex;
		padding: 12px;
		background: #f4f7fc;
		width: calc(100% - 17px);

		.content_content_box {
			width: inherit;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 8px;
			background: #fff;
			padding: 20px 0px 12px 12px;
		}
		.content_content_title {
			margin-bottom: 12px;
			font-weight: 400;
			font-size: 12px;
			line-height: 18px;
			text-align: center;
			color: #4e5969;
			margin-right: 12px;
		}
	}
}

.content-img {
	width: 100%;
	height: 220px;
	object-fit: cover;
}
</style>
