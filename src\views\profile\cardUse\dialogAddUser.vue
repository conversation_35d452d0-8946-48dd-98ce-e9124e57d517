<template>
	<el-dialog v-model="props.dialogVisible" :show-close="false" :before-close="handleClose" style="width: 700px">
		<template #header>
			<div class="dialogHeader">
				<div style="display: flex">
					<div class="dialogHeaderLeft" @click="handleClose">
						<el-icon><ArrowLeftBold /></el-icon>
						<div>添加使用人</div>
					</div>
					<span style="font-size: 14px; color: #999; margin-left: 10px">使用人需为已注册用户</span>
				</div>

				<div class="dialogHeaderRight">
					<el-icon><CloseBold @click="handleClose" /></el-icon>
				</div>
			</div>
		</template>

		<div class="form_content">
			<el-form :model="form" inline="true" label-width="auto" class="demo-form-inline">
				<el-form-item>
					<el-input v-model="form.value" class="indexKeyWord" style="max-width: 300px" placeholder="请准确输入用户名查询">
						<template #prepend>使用人搜索</template>
						<template #append>
							<el-button :icon="Search" @click="handleUserPage" />
							<!-- <el-icon><Search /></el-icon> -->
						</template>
					</el-input>
				</el-form-item>
			</el-form>
			<div>
				<el-table
					:data="tableData.list"
					@selection-change="handleSelectionChange"
					style="width: 100%; border-radius: 6px; border: 1px solid rgba(231, 231, 231, 1)"
					:height="'343px'"
					:header-cell-style="{ background: 'rgba(245, 245, 245, 1)', borderLeft: '1px solid #E7E7E7', color: '#1D2129' }"
					:stripe="false"
					size="small"
					empty-text="暂无数据"
					highlight-current-row
				>
					<el-table-column :ignoreSelectable="true" type="selection" width="44"></el-table-column>

					<el-table-column label="序号" width="172">
						<template #default="scope">{{ scope.$index + 1 }}</template>
					</el-table-column>
					<el-table-column prop="userName" label="使用人" width="172" />
					<el-table-column prop="phone" label="手机号" />
				</el-table>

				<!-- <el-config-provider :locale="customPagination">
					<el-pagination
						layout="total, prev, pager, next,sizes,jumper"
						:page-sizes="[10, 20, 30, 40]"
						:total="total"
						:pager-count="3"
						background
						:current-page="currentPage"
						@current-change="handlepageChange"
						style="justify-content: right"
					>
					</el-pagination>
				</el-config-provider> -->
			</div>
		</div>

		<template #footer>
			<div class="dialog_footer">
				<div>
					<el-button @click="handleClose">取消</el-button>
					<el-button type="primary" color="#1868F1" @click="handleDetermine"> 确定 </el-button>
				</div>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getUserPage } from '@/api/equityTerm.js';
import { Search } from '@element-plus/icons-vue';
const emit = defineEmits();
const props = defineProps({
	// 展示状态
	dialogVisible: {
		type: Boolean,
		default: false,
	},
});
let tableData = reactive({ list: [], multipleSelection: [] });
const total = ref(100);
const currentPage = ref(1);

const form = reactive({
	value: '',
});
let customPagination = reactive({
	el: {
		pagination: {
			pagesize: '页',
			total: `共 ${total.value} 条`,
			goto: '跳至',
			pageClassifier: '',
		},
	},
});

onMounted(() => {
	// 使用人
	handleUserPage();
});

const handleSelectionChange = (val) => {
	tableData.multipleSelection.value = val;
};
// 关闭对话框
function handleClose() {
	tableData.multipleSelection = [];
	emit('handleDialogClose', []);
}
// 切换
function handlepageChange(val) {
	currentPage.value = val;
}

/**
 * @function handleUserPage 使用人
 */
function handleUserPage() {
	getUserPage({ key: form.value }).then((res) => {
		if (res.code === 200) {
			tableData.list = res.data.rows;
		}
	});
}

// 确定
function handleDetermine() {
	emit('handleDialogClose', tableData.multipleSelection);
}
</script>
<style lang="less" scoped>
@import url('./style.less');
</style>
