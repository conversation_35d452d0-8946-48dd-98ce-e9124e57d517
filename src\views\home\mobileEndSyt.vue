<template>
	<div class="mobile_syt">
		<div class="header_wrap">
			<img :src="mobile_bg" class="header_bg" />
			<div class="logo_wrap">
				<el-image :src="logo" class="logo"></el-image>
			</div>
			<div class="flag_wrap">
				<div class="title">
					<!-- <div class="tit1">企业级</div> -->
					<div class="tit2">商业地产数据库</div>
				</div>
				<div class="desc">查阅和分析实时的商业地产行业数据</div>
			</div>
			<div style="padding: 0 20px">
				<div class="prompt_wrap">
					<div class="desc">请用电脑端浏览器搜索“标标准”或打开下方链接体验完整功能</div>
					<div class="link_wrap">
						<img src="https://static.biaobiaozhun.com/mini-program/report/link.png" />
						<div class="link">https://bbzhun.com/#/IntroductionPage</div>
						<div class="copy" @click="copy">复制</div>
					</div>
				</div>
			</div>
		</div>
		<div class="content_wrap">
			<div class="title">商宇通可以帮您完成</div>
			<el-collapse accordion :model-value="activeNames" @change="handleChange">
				<el-collapse-item v-for="(item, index) in propertyList" :key="index" :name="index">
					<template #title>
						<div class="collapse_item_title">
							<img :src="item.icon" alt="content" class="collapse_item_img" />
							<div :class="{ active: activeNames == index }">{{ item.title }}</div>
						</div>
					</template>
					<div class="collapse_content">
						<div class="img_wrap">
							<el-image
								class="collapse_content_img"
								:src="item.img"
								:zoom-rate="1.2"
								:max-scale="3"
								:min-scale="0.2"
								:preview-src-list="srcList"
								show-progress
								:initial-index="index"
								fit="cover"
							/>
						</div>
					</div>
				</el-collapse-item>
			</el-collapse>
			<div class="tips">持续更新中...</div>
<!--			<div class="title" style="padding-top: 32px; padding-bottom: 8px">根据您的需求灵活搭配</div>-->
<!--			<div class="desc">我们为您提供不同的产品配套选择，您可以根据使用需求和团队规模，选择最适合您的权益版本</div>-->
<!--			<div class="common_product">-->
<!--				<img :src="jcb" class="bg" />-->
<!--				<div class="wrap">-->
<!--					<div class="name">基础版</div>-->
<!--					<div class="desc_wrap">-->
<!--						<div class="circle"></div>-->
<!--						<div class="desc_text">适用于轻度浏览</div>-->
<!--					</div>-->
<!--					<div class="detail_bg">-->
<!--						<div class="detail_wrap">-->
<!--							<div class="detail_desc">为您提供免费的功能体验，无需付费即可使用商宇通基础功能</div>-->
<!--							<div class="price_wrap">-->
<!--								<span class="type">￥</span>-->
<!--								<span class="price">0</span>-->
<!--								<span class="unit">元起/月</span>-->
<!--							</div>-->
<!--							<div class="line"></div>-->
<!--							<div class="tag_wrap">-->
<!--								<div class="tag">-->
<!--									<el-icon color="#63C444" size="12"><Select /></el-icon>-->
<!--									<span>市场统计</span>-->
<!--								</div>-->
<!--							</div>-->
<!--						</div>-->
<!--					</div>-->
<!--				</div>-->
<!--			</div>-->
<!--			<div class="common_product">-->
<!--				<img :src="bzb" class="bg" />-->
<!--				<div class="wrap">-->
<!--					<div class="name" style="color: #5d340a">标准版</div>-->
<!--					<div class="desc_wrap">-->
<!--						<div class="circle" style="background-color: #af7d49"></div>-->
<!--						<div class="desc_text" style="color: #644a11">适用于小微团队</div>-->
<!--					</div>-->
<!--					<div class="detail_bg">-->
<!--						<div class="detail_wrap">-->
<!--							<div class="detail_desc">为您整合企业团队必备权益套餐，支持灵活定制团队使用人数</div>-->
<!--							<div class="price_wrap">-->
<!--								<span class="type">￥</span>-->
<!--								<span class="price">199</span>-->
<!--								<span class="unit">元起/月</span>-->
<!--							</div>-->
<!--							<div class="line"></div>-->
<!--							<div class="tag_wrap">-->
<!--								<div class="tag">-->
<!--									<el-icon color="#63C444" size="12"><Select /></el-icon>-->
<!--									<span>市场统计</span>-->
<!--								</div>-->
<!--								<div class="tag">-->
<!--									<el-icon color="#63C444" size="12"><Select /></el-icon>-->
<!--									<span>交易材料</span>-->
<!--								</div>-->
<!--							</div>-->
<!--						</div>-->
<!--					</div>-->
<!--				</div>-->
<!--			</div>-->
<!--			<div class="common_product">-->
<!--				<img :src="zxb" class="bg" />-->
<!--				<div class="wrap">-->
<!--					<div class="name" style="color: #e2cfff">臻享版</div>-->
<!--					<div class="desc_wrap">-->
<!--						<div class="circle" style="background-color: #e2cfff"></div>-->
<!--						<div class="desc_text" style="color: #c5b7e9">专业团队或企业</div>-->
<!--					</div>-->
<!--					<div class="detail_bg">-->
<!--						<div class="detail_wrap">-->
<!--							<div class="detail_desc">覆盖全套产品权益，全场景全流程功能，专业数据一键获取</div>-->
<!--							<div class="price_wrap">-->
<!--								<span class="type">￥</span>-->
<!--								<span class="price">399</span>-->
<!--								<span class="unit">元起/月</span>-->
<!--							</div>-->
<!--							<div class="line"></div>-->
<!--							<div class="tag_wrap">-->
<!--								<div class="tag">-->
<!--									<el-icon color="#63C444" size="12"><Select /></el-icon>-->
<!--									<span>市场统计</span>-->
<!--								</div>-->
<!--								<div class="tag">-->
<!--									<el-icon color="#63C444" size="12"><Select /></el-icon>-->
<!--									<span>交易材料</span>-->
<!--								</div>-->
<!--								<div class="tag">-->
<!--									<el-icon color="#63C444" size="12"><Select /></el-icon>-->
<!--									<span>信用风险</span>-->
<!--								</div>-->
<!--							</div>-->
<!--						</div>-->
<!--					</div>-->
<!--				</div>-->
<!--			</div>-->
			<div class="title">让商业地产行业研究工作变得简单</div>
			<div style="padding: 0 12px">
				<div class="item_wrap" v-for="item in introducedList">
					<img :src="item.bg" class="bg" />
					<div class="name">{{ item.name }}</div>
					<div class="content">{{ item.desc }}</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { Select } from '@element-plus/icons-vue';
import logo from '@/assets/images/home/<USER>/mobile/logo.png';
import mobile_bg from '@/assets/images/home/<USER>/mobile/mobile_bg.png';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import icon1 from '@/assets/images/home/<USER>/mobile/icon1.png';
import icon2 from '@/assets/images/home/<USER>/mobile/icon2.png';
import icon3 from '@/assets/images/home/<USER>/mobile/icon3.png';
import icon4 from '@/assets/images/home/<USER>/mobile/icon4.png';
import icon5 from '@/assets/images/home/<USER>/mobile/icon5.png';
import icon6 from '@/assets/images/home/<USER>/mobile/icon6.png';
import img1 from '@/assets/images/home/<USER>/mobile/img1.png';
import img2 from '@/assets/images/home/<USER>/mobile/img2.png';
import img3 from '@/assets/images/home/<USER>/mobile/img3.png';
import img4 from '@/assets/images/home/<USER>/mobile/img4.png';
import img5 from '@/assets/images/home/<USER>/mobile/img5.png';
import img6 from '@/assets/images/home/<USER>/mobile/img6.png';
import jcb from '@/assets/images/home/<USER>/mobile/jcb.png';
import bzb from '@/assets/images/home/<USER>/mobile/bzb.png';
import zxb from '@/assets/images/home/<USER>/mobile/zxb.png';
import bg1 from '@/assets/images/home/<USER>/mobile/bg1.png';
import bg2 from '@/assets/images/home/<USER>/mobile/bg2.png';
import bg3 from '@/assets/images/home/<USER>/mobile/bg3.png';
import bg4 from '@/assets/images/home/<USER>/mobile/bg4.png';

const activeNames = ref(0);
const propertyList = ref([
	{
		title: '市场统计 | 资产地图 ｜ 交易计算',
		icon: icon1,
		img: img1,
	},
	{
		title: '楼宇信息 | 商圈分析 | 资产对比',
		icon: icon2,
		img: img2,
	},
	{
		title: '城市数据 | 参与者 | 户型图',
		icon: icon3,
		img: img3,
	},
	{
		title: '标准化产品ABS | REITs',
		icon: icon4,
		img: img4,
	},
	{
		title: '信用风险测算 | 现金流测算',
		icon: icon5,
		img: img5,
	},
	{
		title: '证券化 | Pre-Reits基金',
		icon: icon6,
		img: img6,
	},
]);
const introducedList = ref([
	{
		name: '自有数据支撑',
		desc: '以商业房地产领域完整的研究团队，采用人口普查级别的方法来收集全面、准确的数据，以支撑商宇通产品。',
		bg: bg1,
	},
	{
		name: '强大且全面的功能',
		desc: '基于平台强大的数据、分析能力，让您只需通过基础的需求数据，便能快速测算获取现金流、证券化等全场景专业数据。',
		bg: bg2,
	},
	{
		name: '友好清晰的体验',
		desc: '平台软件覆盖商业地产投资分析全流程，通过清晰地功能划分与页面交互引导，帮助您快速掌握软件，流畅高效地获取所需数据。',
		bg: bg3,
	},
	{
		name: '科学准确的分析',
		desc: '内嵌多重风险管理、价值评估和金融模型，帮您准确评估投资回报和风险。',
		bg: bg4,
	},
]);
const srcList = [img1, img2, img3, img4, img5, img6];
function handleChange(val) {
	activeNames.value = val;
}
function copy() {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText('https://bbzhun.com/#/IntroductionPage')
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = 'https://bbzhun.com/#/IntroductionPage';
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}
</script>
<style lang="less" scoped>
.mobile_syt {
	width: 100%;
	padding-bottom: 176px;
	.header_wrap {
		height: 320px;
		position: relative;
		.header_bg {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
		}
		.logo_wrap {
			position: relative;
			padding: 32px 0 8px 0;
			display: flex;
			justify-content: center;
			.logo {
				width: 48px;
				height: 48px;
			}
		}
		.flag_wrap {
			position: relative;
			.title {
				font-size: 26px;
				font-weight: 600;
				color: #1d2129;
				text-align: center;
				margin-bottom: 4px;
				line-height: 1.5;
				display: flex;
				justify-content: center;
				.tit2 {
					background: linear-gradient(90deg, #279dff 0%, #1358f3 100%);
					-webkit-background-clip: text;
					background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
			.desc {
				font-size: 12px;
				text-align: center;
				color: #4e5969;
				font-weight: 400;
				line-height: 1.5;
			}
		}
		.prompt_wrap {
			position: relative;
			margin-top: 74px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			padding: 12px;
			border-radius: 8px;
			background-color: #fff;
			height: 81px;
			box-sizing: border-box;
			.desc {
				font-size: 10px;
				font-weight: 400;
				color: #4e5969;
			}
			.link_wrap {
				background: #f6f7f8;
				height: 36px;
				padding: 6px 8px;
				display: flex;
				align-items: center;
				border-radius: 6px;
				box-sizing: border-box;
				img {
					width: 16px;
					height: 16px;
				}
				.link {
					font-size: 12px;
					font-weight: 400;
					color: #1868f1;
					margin-left: 4px;
					flex: 1;
				}
				.copy {
					font-size: 12px;
					font-weight: 500;
					width: 48px;
					height: 24px;
					line-height: 24px;
					background: #2c6be9;
					color: #fff;
					border-radius: 4px;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}
	.content_wrap {
		background-color: #f4f7fc;
		color: #1d2129;
		padding-bottom: 12px;
		.title {
			padding-top: 48px;
			padding-bottom: 20px;
			text-align: center;
			font-size: 22px;
			font-weight: 500;
			line-height: 1.5;
		}
		:deep(.el-collapse) {
			border-top: 0;
		}
		.collapse_item_title {
			padding: 0 16px;
			display: flex;
			align-items: center;
			font-size: 15px;
			font-weight: 500;
			color: #1d2129;
			.active {
				color: #1868f1;
			}
			.collapse_item_img {
				width: 28px;
				height: 28px;
				margin-right: 4px;
			}
		}

		:deep(.el-collapse-item__content) {
			padding-bottom: 0;
		}
		.collapse_content {
			padding: 12px;
			background: #f4f7fc;
			width: calc(100% - 24px);
			.img_wrap {
				background-color: #fff;
				box-shadow: 0px 4px 20px 0px #001c550a;
				border-radius: 8px;
				display: flex;
				justify-content: center;
				padding-top: 24px;
				padding-bottom: 12px;
				.collapse_content_img {
					height: 204px;
					object-fit: cover;
				}
			}
		}
		.tips {
			font-size: 12px;
			font-weight: 400;
			color: #86909c;
			line-height: 1.5;
			padding-top: 12px;
			padding-left: 16px;
		}
		.desc {
			font-size: 12px;
			font-weight: 400;
			color: #4e5969;
			line-height: 1.5;
			padding: 0 12px;
			text-align: center;
			padding-bottom: 20px;
		}
		.common_product {
			height: 299px;
			padding: 0 12px;
			position: relative;
			margin-bottom: 12px;
			.bg {
				border-radius: 0 0 16px 16px;
				width: calc(100vw - 24px);
				height: 100%;
				position: absolute;
				top: 0;
				left: 12px;
				z-index: 1;
			}
			.wrap {
				position: relative;
				z-index: 99;
				height: 100%;
				display: flex;
				flex-direction: column;
				.name {
					padding-top: 51px;
					padding-left: 36px;
					font-size: 20px;
					font-weight: 500;
					line-height: 1.5;
					color: #143a55;
				}
				.desc_wrap {
					display: flex;
					align-items: center;
					gap: 4px;
					padding-left: 36px;
					margin-bottom: 16px;
					.circle {
						background-color: #4491f7;
						width: 7px;
						height: 7px;
						border-radius: 50%;
					}
					.desc_text {
						font-size: 12px;
						font-weight: 400;
						color: #00436c;
						line-height: 1.5;
					}
				}
				.detail_bg {
					background: linear-gradient(180deg, transparent 0%, #fff 70%);
					padding: 0 16px;
					border-radius: 0 0 16px 16px;
					.detail_wrap {
						flex: 1;
						border: 1px solid #ffffff;
						border-radius: 16px;
						padding: 16px;
						backdrop-filter: blur(20px);
						background: linear-gradient(180deg, transparent 0%, #fff 70%);
						.detail_desc {
							font-size: 14px;
							font-weight: 400;
							line-height: 1.5;
							color: #1d2129;
							margin-bottom: 20px;
						}
						.price_wrap {
							line-height: 1.5;
							color: #000000;
							.type {
								font-size: 16px;
								font-weight: 600;
							}
							.price {
								font-size: 28px;
								font-weight: 600;
								margin: 0 4px 0 2px;
							}
							.unit {
								color: #4e5969;
								font-size: 11px;
								font-weight: 400;
							}
						}
						.line {
							height: 1px;
							border-top: 1px dashed #e5e6eb;
							margin: 8px 0 16px 0;
						}
						.tag_wrap {
							display: flex;
							gap: 40px;
							.tag {
								display: flex;
								align-items: center;
								gap: 4px;
								font-size: 12px;
								font-weight: 400;
								line-height: 1.5;
								color: #4e5969;
							}
						}
					}
				}
			}
		}
		.common_product:last-child {
			margin-bottom: 0;
		}
	}
	.item_wrap {
		height: 126px;
		position: relative;
		background-color: #fff;
		border-radius: 8px;
		padding: 20px 12px;
		margin-bottom: 12px;
		box-sizing: border-box;
		.bg {
			position: absolute;
			top: 0;
			left: 0;
			width: calc(100vw - 24px);
			height: 100%;
		}
		.name {
			font-size: 16px;
			font-weight: 500;
			color: #1d2129;
			line-height: 1.5;
			margin-bottom: 4px;
		}
		.content {
			color: #4e5969;
			font-size: 12px;
			font-weight: 400;
			line-height: 1.5;
		}
	}
}
</style>
