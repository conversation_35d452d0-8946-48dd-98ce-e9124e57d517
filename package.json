{"license": "MIT", "name": "pjt", "version": "0.0.0", "scripts": {"dev": "vite --mode development --open", "build": "vite build --mode production", "build:env": "vite build --mode development", "serve": "vite --mode production"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.3.1", "@microsoft/fetch-event-source": "^2.0.1", "axios": "^1.7.7", "dayjs": "^1.11.10", "echarts": "^5.6.0", "element-china-area-data": "^6.1.0", "element-plus": "^2.4.4", "html2canvas": "^1.4.1", "js-md5": "^0.7.3", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "pinia": "^2.1.7", "postcss-px-to-viewport": "^1.1.1", "qrcode": "^1.5.4", "swiper": "^11.1.4", "unplugin-vue-components": "^0.26.0", "v-copy": "^0.1.0", "video.js": "^8.12.0", "vue": "^3.3.0", "vue-amap": "^0.5.10", "vue-demi": "^0.14.6", "vue-html2pdf": "^1.8.0", "vue-native-websocket-vue3": "^3.1.7", "vue-router": "^4.2.5", "vuex": "^4.0.2", "vuex-persistedstate": "^4.1.0", "wangeditor": "^4.6.15"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@vitejs/plugin-vue": "^5.2.0", "@vue/compiler-sfc": "^3.0.0", "@vuemap/unplugin-resolver": "^2.0.0", "eslint": "^9.13.0", "less": "^4.2.0", "less-loader": "^11.1.4", "lib-flexible-computer": "^1.0.2", "sass": "^1.54.9", "unplugin-auto-import": "^0.17.8", "unplugin-element-plus": "^0.4.1", "vite": "^5.4.11", "vite-plugin-babel-import": "2.0.2"}}