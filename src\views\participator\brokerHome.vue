<template>
	<div class="brokerHome">
		<!-- 左 -->
		<div class="left_box">
			<!-- 经纪人介绍 -->
			<div class="broker_introduce">
				<div class="boxss">
					<img :src="`${proxyAddress}${borkerInfo.avatar}`" alt="" class="avatar" v-if="borkerInfo.avatar" />
					<img src="@/assets/f.png" alt="" class="avatar" v-else />
					<p>{{ borkerInfo.borkerName }}</p>
					<p>{{ borkerInfo.companyName }}</p>
				</div>
			</div>
			<!-- tabs栏 -->
			<div class="tabs_box">
				<div
					class="tabs_item"
					:class="activeIndex == index ? 'active' : ''"
					v-for="(item, index) in buildNameList"
					:key="item"
					@click="onActive(item, index)"
				>
					{{ item.mapName }}
				</div>
			</div>
		</div>
		<div class="right_box">
			<div class="build_item" v-for="item in buildList" :key="item">
				<div class="tab_box">
					<div class="tab_item" :class="selectIndex == 0 ? 'activeed' : ''" @click="onSelect(0)">视频</div>
					<div class="tab_item" :class="selectIndex == 1 ? 'activeed' : ''" @click="onSelect(1)">图片</div>
				</div>
				<div class="swapper_box" v-if="selectIndex == 1">
					<el-carousel height="480px" style="width: 900px" :autoplay="false">
						<el-carousel-item v-for="pic in item.imgs" :key="pic">
							<img :src="`${proxyAddress}${pic}`" alt="" />
						</el-carousel-item>
					</el-carousel>
				</div>
				<div class="redio_box" v-if="selectIndex == 0">
					<video
						:src="`${proxyAddress_video}${item.video}`"
						:controls="true"
						:autoplay="false"
						:loop="true"
						:volume="0"
						width="900px"
						height="480px"
					/>
				</div>

				<div class="property youshebiaotihei">
					<div class="area">
						<span>面积：</span>
						<span>{{ item.area }}m²</span>
					</div>
					<div class="rent">
						<span>租金：</span>
						<span>{{ item.rent }}元/m²/天</span>
					</div>
					<div class="state">租</div>
				</div>
			</div>
			<el-pagination
				v-if="total != ''"
				current-page="currentPage"
				page-size="pageSize"
				:page-sizes="[5, 10, 20, 30]"
				@size-change="pageSizeChange"
				@current-change="handlePageChange"
				layout="->,prev, pager, next, jumper, sizes,total"
				:total="total"
			/>
		</div>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { getBrokerInfo, getBuildBook } from '@/api/participant.js';
const route = useRoute();
import { defineComponent } from 'vue';
// import videojs from "video.js";
//   import 'video.js/dist/video-js.css'

console.log(route.query, 7812);
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref();
const handlePageChange = (page) => {
	console.log(page, 76);
	currentPage.value = page;
	getBrokerInfos();
};
const pageSizeChange = (val) => {
	console.log(val, 'val');
	pageSize.value = val;
	getBrokerInfos(val);
};
const borkerInfo = ref({});
const buildNameList = ref([]);
const buildList = ref([]);
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const proxyAddress_video = ref('https://static.biaobiaozhun.com/'); //视频前缀
const activeIndex = ref(0); //选中的tabs
const imas = ref('');
const imgArr = ref([]);
// 获取经纪人信息
const getBrokerInfos = async () => {
	borkerInfo.value = route.query;
	const queryParams = {
		currentPage: currentPage.value,
		userId: route.query.userId,
	};
	console.log(queryParams, 'queryParams');
	await getBrokerInfo(queryParams)
		.then((res) => {
			console.log(res, 'ss12');
			buildNameList.value = res.data.records;
			console.log(buildNameList.value[0], 'buildNameList');
			total.value = res.data.total;

			//   默认选中第一项
			onActive(buildNameList.value[0], 0);
			// total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
getBrokerInfos();

// 切换tabs
const onActive = async (item, index) => {
	activeIndex.value = index;
	console.log(item, index, 4321);
	const queryParams = {
		currentPage: currentPage.value,
		dealType: 2,
		userId: route.query.userId,
		mapId: item.mapId,
	};
	// 获取楼书
	await getBuildBook(queryParams)
		.then((res) => {
			buildList.value = res.data.records.map((item) => {
				return {
					...item,
					imgs: item.imgs.split(','),
				};
			});
			console.log(buildList.value, 'buildList');
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};

const selectIndex = ref(0);

const onSelect = (tab) => {
	console.log(tab, 73487);
	selectIndex.value = tab;
};
</script>

<style scoped lang="less">
.brokerHome {
	// padding-top: 70px;
	// padding-left: 170px;
	// padding-right: 170PX;
	display: flex;
	.left_box {
		display: flex;
		flex-direction: column;
		position: fixed;
		top: 50%;
		transform: translateY(-50%);

		.broker_introduce {
			width: 237px;
			height: 270px;
			background: rgb(245, 245, 245);
			padding-top: 30px;
			.boxss {
				.avatar {
					width: 89px;
					height: 89px;
					border-radius: 50%;
					margin-left: 75px;
				}
				p {
					color: rgb(0, 0, 0);
					font-family: 微软雅黑;
					font-size: 20px;
					font-weight: 700;
					line-height: 30px;
					letter-spacing: 0px;
					text-align: center;
				}
			}
		}
		.tabs_box {
			width: 237px;
			// height: 221px;
			background: rgb(242, 248, 254);
			margin-top: 26px;
			.tabs_item {
				width: 237px;
				height: 35px;
				text-align: center;
				font-family: 微软雅黑;
				font-size: 15px;
				font-weight: 400;
				line-height: 35px;
				letter-spacing: 0px;
			}
			.active {
				color: rgb(56, 96, 154);
				background: rgb(206, 223, 244);
			}
		}
	}
	.right_box {
		// position: relative;
		margin-left: 280px;
		margin-top: 37px;
		.build_item {
			position: relative;
		}
		.tab_box {
			width: 127px;
			height: 29px;
			display: flex;
			justify-content: space-around;
			border-radius: 15px;
			background: rgba(0, 0, 0, 0.45);
			line-height: 29px;
			color: #fff;
			// text-align: center;
			position: absolute;
			top: 15px;
			left: 50%;
			z-index: 99;
			.tab_item {
				padding: 0 10px;
			}
		}
		.activeed {
			border-radius: 15px;
			width: 40%;
			background: rgba(0, 0, 0, 0.5);
		}
		img {
			width: 900px;
			height: 480px;
			background-size: 100% 100%;
		}
		.redio_box {
			width: 900px;
			height: 480px;
		}
		.property {
			color: rgb(0, 0, 0);
			// font-family: youshebiaotihei;
			font-size: 32px;
			font-weight: 400;
			line-height: 42px;
			letter-spacing: 0px;
			text-align: left;
			display: flex;
			justify-content: space-between;
			padding: 5px 15px;
			.area {
				display: flex;
				// margin-right: 100px;
			}
			.state {
				width: 50px;
				height: 50px;
				border-radius: 15px;
				background: rgb(56, 96, 154);
				color: rgb(255, 255, 255);
				font-family: 微软雅黑;
				font-size: 25px;
				font-weight: 700;
				line-height: 50px;
				letter-spacing: 0px;
				text-align: center;
			}
		}
	}
}
</style>
