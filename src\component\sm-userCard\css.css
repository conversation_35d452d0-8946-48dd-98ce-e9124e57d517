.container_user_card {
	display: flex;
	flex-wrap: wrap;
	.card_Hover {
		&:hover .hovers {
			color: #ec655f;
		}
	}
	.card_content {
		height: 66px;
		width: 208px;
		border-radius: 6px;
		background: #f5f6f7;
		display: flex;
		align-items: center;
		cursor: pointer;
		.card_Img {
			margin: 0 10px 0 13px;
			width: 40px;
			height: 40px;
			img {
				width: 40px;
				height: 40px;
			}
		}

		.card_RightContent {
			width: calc(100% - 73px);
			height: 42px;
			.topContent {
				height: 22px;
				display: flex;
				justify-content: space-between;
			}
			.topContent > :nth-child(1) {
				font-size: 14px;
				font-weight: 500;
				line-height: 22px;
				color: #1d2129;
			}
			.topContent > :nth-child(2) {
				font-size: 10px;
				font-weight: 400;
				line-height: 18px;
			}

			.bottomContent {
				height: 20px;
				font-size: 12px;
				font-weight: 400;
				line-height: 20px;
			}
		}
	}

	.addCard {
    /* position: relative; */
		height: 66px;
		width: 208px;
		border-radius: 6px;
		background: #f5f6f7;
		display: flex;
		align-items: center;
		cursor: pointer;
    &:hover{
        background-color: rgba(201, 198, 198, 0.2); /* 灰色叠加20%不透明度 */
      }
		.el-icon {
			font-size: 23px;
			margin: 0 21px 0 23px;
			color: #1868f1;
		}
		.addMan {
			height: 24px;
			font-size: 16px;
			font-weight: 500;
			line-height: 24px;
			text-align: center;
			color: #1868f1;
		}
	}
}