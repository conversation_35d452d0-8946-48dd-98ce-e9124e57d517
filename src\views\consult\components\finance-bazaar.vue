<template>
    <div class="data_box">
        <div class="data_item">
            <div class="top items">
                <span class="datas">上证指数</span>
                <span class="ratio">+19.60</span>
            </div>
            <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div>
        </div>
        <div class="data_item">
            <div class="top items">
                <span class="datas">纳斯达克</span>
                <span class="ratio">+19.60</span>
            </div>
            <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div>
        </div>
        <div class="data_item">
            <div class="top items">
                <span class="datas">美元指数</span>
                <span class="ratio">+19.60</span>
            </div>
            <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div>
        </div>
        <div class="data_item">
            <div class="top items">
                <span class="datas">恒生指数</span>
                <span class="ratio">+19.60</span>
            </div>
            <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div>
        </div>
        <div class="data_item">
            <div class="top items">
                <span class="datas">在岸人民币</span>
                <span class="ratio">+19.60</span>
            </div>
            <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div>
        </div>
        <div class="data_item">
            <div class="top items">
                <span class="datas">离岸人民币</span>
                <span class="ratio">+19.60</span>
            </div>
            <div class="bottom items">
                <span  class="datas">2947.69</span>
                <span class="ratio">+19.60</span>
            </div>
        </div>
    </div>
  <div class="echarts_box">
      <div id="myChart1" class="charts" :style="{width: '800px', height: '550px'}"></div>
      <div id="myChart2" class="charts" :style="{width: '800px', height: '550px'}"></div>
      <div id="myChart3" class="charts" :style="{width: '800px', height: '550px'}"></div>
      <div id="myChart4" class="charts" :style="{width: '800px', height: '550px'}"></div>
  </div>
</template>

<script setup>
// 引入echarts
import * as echarts from 'echarts'
import { onMounted , ref,toRaw } from "vue"
const data = ref([])
// getData();
import http from "@/utils/http";
    onMounted(() => { // 需要获取到element,所以是onMounted的Hook
        let myChart1 = echarts.init(document.getElementById("myChart1"));
        let myChart2 = echarts.init(document.getElementById("myChart2"));
        let myChart3 = echarts.init(document.getElementById("myChart3"));
        let myChart4 = echarts.init(document.getElementById("myChart4"));
        // 绘制图表
        myChart1.setOption({
        title: {  
            text:data.title,  
            left: 'center', // 文字说明的位置，默认为居中  
            bottom: 10, // 距离底部的距离，单位为像素  
            // backgroundColor: '#999',
            width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度
            
            textStyle: { // 文字样式  
                color: '#000',  
                fontSize: 18,
            }  
        },  
        xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
        type: 'value'
        },
        series: [
        {
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        type: 'line',
        areaStyle: {},
        }
        ]
        });

        // 2
        myChart2.setOption({
        title: {
            text: '阿萨大大十大阿萨大阿萨大22',  
            left: 'center', // 文字说明的位置，默认为居中  
            bottom: 10, // 距离底部的距离，单位为像素  
            // backgroundColor: '#999',
            width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度
            
            textStyle: { // 文字样式  
                color: '#000',  
                fontSize: 18,
            }  
        }, 
            xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            },
            yAxis: {
            type: 'value'
            },
            series: [
            {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            areaStyle: {},
            }
            ]
        });

        // 3
        myChart3.setOption({
        title: {  
            text: '阿萨大大十大阿萨大阿萨大22',  
            left: 'center', // 文字说明的位置，默认为居中  
            bottom: 10, // 距离底部的距离，单位为像素  
            // backgroundColor: '#999',
            width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度
            
            textStyle: { // 文字样式  
                color: '#000',  
                fontSize: 18,
                
            }  
        }, 
            xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            },
            yAxis: {
            type: 'value'
            },
            series: [
            {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            areaStyle: {},
            }
            ]
        });
        // 4
        myChart4.setOption({
        title: {  
            text: '阿萨大大十大阿萨大阿萨大22',  
            left: 'center', // 文字说明的位置，默认为居中  
            bottom: 10, // 距离底部的距离，单位为像素  
            // backgroundColor: '#999',
            width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度
            textStyle: { // 文字样式  
                color: '#000',  
                fontSize: 18,
            }  
        }, 
            xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            },
            yAxis: {
            type: 'value'
            },
            series: [
            {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            areaStyle: {},
            }
            ]
        });
       
    });
    // 获取数据
    const getData = async () => {
        const res = await http.get('gis/a_new_mm_sc.do?act=getFinancialMarketTableList');
        console.log('金融市场',res);
        data.value = toRaw(res.list)
        console.log('sc',data.value);
    }
    onMounted(() => {
        getData()
    }
    )
  </script>

  <style scoped lang="less">
      .echarts_box {
          display: grid;  
          grid-template-columns: repeat(2, 1fr); // 定义三列的布局。  
          grid-gap: 10px; // 根据实际需求调整。  
      }
      .charts {
          padding: 5px;
           margin-left: 15px;
           box-shadow: 1px 1px 1px 1px #ccc;
           margin-top: 25px;
      }
      .data_box {
        display: flex;
      }
      .data_item {
        background-color: #fff;
        // border: 1px solid #ccc;
        box-shadow: 1px 1px 1px 1px #ccc;
        margin-right: 25px;
      }
      .items {
        // flex: 1;
        width: 280px;
        display: flex;
        vertical-align: middle;
      }
      .ratio {
        color:green;
        margin-left: 15px;
      }
      .datas {
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: 600;
      }
  </style>  