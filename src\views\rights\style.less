.rights-container {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  background: #f5f8fd;
  box-sizing: border-box;

  // padding: 15px;

  .rights-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 120px;
    background: url("../../assets/rightsback.png") no-repeat top center;
    background-size: 100% 9.8%;
    padding-bottom: 110px;

    .title {
      font-size: 48px;
      font-weight: 700;
      line-height: 67.2px;
      text-align: center;
      height: 68px;
      color: rgba(29, 33, 41, 1);
      margin-bottom: 60px;
    }

    .select-city {
      display: flex;
      align-items: center;
      // margin: 30px 0 70px 0;
      .city_name{
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
        text-align: center;
        color: #1D2129;
        margin-right: 40px;
      }
      .el-dropdown-link{
        display: flex;
        align-items: center;
      }

      .name{
        cursor: pointer;
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
        text-align: center;
        color: #1D2129;
      }
      .el-dropdown {
        span {
          font-size: 24px;
          color: rgba(29, 33, 41, 1);
          ;
        }
      }
    }

    .tab-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      flex-direction: column;
      align-items: center;

      .el-radio-group {
        width: 686px;
        height: 72px;
        background: rgba(0, 0, 0, 0.08);
        border-radius: 8px;

        .el-radio-button {
          // width: 33.3%;
          display: flex;
          flex: 1;
          height: 100%;
          border: none;

          ::v-deep .el-radio-button__inner {
            width: 100%;
            height: 100%;
            font-size: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: rgba(245, 245, 245, 1);
            border: none;
            background: transparent;

          }

          ::v-deep .el-radio-button__inner:hover {
            text-decoration: none;
            color: inherit;
          }
        }



        .is-active {
          border: none;

          ::v-deep .el-radio-button__inner {
            background: rgba(255, 255, 255, 0.8);
            color: rgba(29, 33, 41, 1);
            border: none;
            border-radius: 8px;
            overflow: auto;
            box-shadow: none
          }
        }
      }

      .details-container {
        .set-meal-container {
          .set-meal {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-end;
          }

            .set-meal-box {
              width: 342px;
              height: 583px;
              background: rgba(255, 255, 255, 1);
              margin: 0 42px;
              box-shadow: 0px 6px 32px 0px rgba(38, 38, 38, 0.16);
              border-radius: 8px;

              .back-top {
                width: 100%;
                overflow: auto;
                border-radius: 8px 8px 0 0;

              }

              .set-container {
                height: 360px;

                .set-title-box {
                  height: 69px;
                  display: flex;
                  justify-content: center;
                  margin-top: 45px;

                  .title-box-container {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: center;

                    .set-title {
                      font-size: 32px;
                      font-weight: 700;
                      line-height: 32px;
                      position: relative;

                      .set-city {
                        width: 42px;
                        height: 26px;
                        line-height: 26px;
                        margin-left: 5px;
                        text-align: center;
                        font-size: 13px;
                        font-weight: 700;
                        border-radius: 12px 4px 12px 4px;
                        position: absolute;
                        right: -52px;
                        top: 0;

                      }

                    }

                    .permission {
                      height: 28px;
                      font-size: 20px;
                      font-weight: 400;
                      line-height: 28px;
                      text-align: center;
                      color: rgba(78, 89, 105, 1);


                    }
                  }



                }

                .permission-details {

                  margin: 0 16px;
                  margin-top: 80px;
                  border-bottom: 1px solid rgba(245, 246, 247, 1);

                  .contain-rights {
                    height: 32px;
                    background: rgba(245, 246, 247, 1);
                    border-radius: 6px;
                    line-height: 32px;
                    font-size: 15px;
                    font-weight: 600;
                    text-align: left;
                    padding-left: 10px;

                  }

                  .content-name {
                    height: 24px;
                    padding-left: 10px;
                    position: relative;
                    line-height: 24px;
                    margin-left: 18px;
                    margin-top: 10px;
                    margin-bottom: 10px;


                  }

                  .content-name:after {
                    content: ' ';
                    position: absolute;
                    left: -4px;
                    top: 9px;
                    width: 5px;
                    height: 5px;
                    background-color: rgba(78, 89, 105, 1);
                    border-radius: 4px;
                  }

                  .view-rights {
                    height: 32px;
                    padding-left: 10px;
                    line-height: 32px;
                    font-size: 15px;
                    font-weight: 600;
                    text-align: left;
                    padding-left: 10px;
                    margin-bottom: 10px;
                    cursor: pointer;

                    /* 去除el-anchor的点样式 */
                    ::v-deep .el-anchor .el-anchor__list a {

                      color: rgba(29, 33, 41, 1);

                    }
                  }
                }
              }

              .set-button {
                margin: 0 30px;

                .buy-button {
                  height: 48px;
                  width: 100%;
                  border-radius: 4px;
                  font-size: 20px;
                  font-weight: 500;
                  text-align: center;
                  line-height: 48px;
                  margin-top: 10px;
                  cursor: pointer;
                  // line-height: 28px;

                  }
              }



            }

            .PACKAGE_BASE {
              .back-top {
                height: 12px;
                background: linear-gradient(267.76deg, #EDEDED 12.93%, #EAEAEA 79.03%);
              }

              .set-city {
                background: rgba(231, 231, 231, 1);
                color: rgba(78, 89, 105, 1);
              }

              .set-button {
                .buy-button {
                  cursor: pointer;
                  border: 1px solid rgba(231, 231, 231, 1);
                  color: rgba(134, 144, 156, 1);
                }
              }

            }

            .PACKAGE_STANDARD {
              .back-top {
                height: 12px;
                background: linear-gradient(87.85deg, #1868F1 15.48%, #2183F1 88.16%);

              }

              .set-city {
                background: rgba(24, 104, 241, 1);
                color: rgba(255, 255, 255, 1);

              }

              .set-button {
                .month {
                  border: 1px solid rgba(24, 104, 241, 1);
                  color: rgba(24, 104, 241, 1);
                  cursor: pointer;
                  }
                  .shoppingCart{
                    background:url(../../assets/shoppingCart3.png) no-repeat;
                    background-size: cover;
                  }
                  
                  .shoppingCart:hover{
                    background:url(../../assets/shoppingCart4.png) no-repeat;
                    background-size: cover;
                  }
                .year {
                  background: rgba(24, 104, 241, 1);
                  color: rgba(255, 255, 255, 1);
                  cursor: pointer;

                }
              }

            }

            .PACKAGE_PREMIUM {
              height: 609px;


              .back-top {
                height: 38px;
                background: linear-gradient(267.76deg, #4F5052 12.93%, #333333 79.03%);
                text-align: center;
                line-height: 38px;
                color: #F9C29B;

              }

              .set-city {
                background: linear-gradient(244.08deg, #4F5052 8.86%, #333333 89.49%);
                color: #F9C29B;
              }

              .set-button {
                .month {
                  border: 1px solid rgba(79, 80, 82, 1);
                  color: rgba(29, 33, 41, 1);
                  cursor: pointer;

                } 
                 .shoppingCart{
                  background:url(../../assets/shoppingCart1.png) no-repeat;
                  background-size: cover;
                }
                .shoppingCart:hover{
                  background:url(../../assets/shoppingCart2.png) no-repeat;
                  background-size: cover;
                }

                .year {
                  background: linear-gradient(267.76deg, #4F5052 12.93%, #333333 79.03%);
                  color: #F9C29B;
                  cursor: pointer;
                }
              }
            }

          .contrast-list {
            margin-top: 150px;
            width: calc(100% - 84px);
            margin-left: 42px;
            margin-right: 42px;
            position: relative;

            .contrast-top {
              height: 56px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              margin-bottom: 50px;

              .contrast-title {
                font-size: 40px;
                font-weight: 500;
                line-height: 56px;
                text-align: center;

              }

              .card-roll {
                position: absolute;
                right: 4px;
                text-align: right;
                height: 28px;
                line-height: 28px;
                color: rgba(24, 104, 241, 1);
                font-size: 18px;
                font-weight: 700;
                line-height: 28px;
                cursor: pointer;
              }
            }

            .contrast-container {

              .table-header {
                display: flex;
                height: 108px;
                align-items: center;
                border-bottom: 1px solid rgba(231, 231, 231, 1);

                div {
                  width: calc(75% / 3);
                  text-align: center;
                  font-size: 24px;
                  font-weight: 1000;
                  line-height: 28px;
                  color: rgba(29, 33, 41, 1);


                }

                div:first-child {
                  width: 25%;
                  text-align: left;
                }
              }

              .table-title {
                border-bottom: 1px solid rgba(231, 231, 231, 1);
                background: rgba(245, 246, 247, 1);
                height: 68px;
                line-height: 68px;
                color: rgba(29, 33, 41, 1);
                font-size: 18px;
                font-weight: 1000;
                padding-left: 10px;


              }

              .table-box {
                display: flex;
                height: 68px;
                align-items: center;

                div {
                  width: calc(75% / 3);
                  text-align: center;

                }

                .name {
                  font-size: 18px;
                  width: 25%;
                  text-align: left;
                  padding-left: 10px;
                  color: rgba(29, 33, 41, 1);

                }
              }

              .table-box {
                border-bottom: 1px solid rgba(231, 231, 231, 1)
              }
            }



          }
        }
        .user_vip{
          width: calc(342px * 3 + 3 * 84px);
          display: flex;
          flex-wrap: wrap;
          .user_vip_footer{
            width:100%;
            height: 132px;
            display: flex;
            justify-content: center;
            align-items: center;
            .btn{
              text-align: center;
              line-height: 52px;
              width: 316px;
              height:52px;
              border-radius: 8px;
              background: linear-gradient(90deg, #FF504C 0%, #FE8042 100%);
              color: #fff;
              cursor: pointer;
              &:hover{
                background: linear-gradient(90deg, #FF504C 0%, #FF6F56 100%);
              }
            }
          }
          .user_vip_box{
            width: 342px;
            height: 408px;
            background: rgba(255, 255, 255, 1);
            margin: 0 42px;
            box-shadow: 0px 6px 32px 0px rgba(38, 38, 38, 0.16);
            border-radius: 8px;
            margin-bottom: 30px;

            .back-top{
              width: 100%;
              height: 38px;
              overflow: auto;
              border-radius: 8px 8px 0 0;
              background: linear-gradient(90deg, #FF504C 0%, #FE8042 100%);
              font-size: 14px;
              font-weight: 700;
              text-align: center;
              line-height: 38px;
              color: #FFFFFF;
            }

            .set-container{
              // height: 310px;
              margin: 40px 0 20px 0;
                  .set-title{
                    text-align:center ;
                    font-size: 32px;
                    font-weight: 700;
                    line-height: 32px;
                    color: #1D2129;
                    margin-bottom: 16px;
                  }
                  .set-city{
                    font-size: 18px;
                    font-weight: 600;
                    line-height: 26px;
                    height: 26px;
                    text-align: center;
                    color: #4E5969;
                  }

              .permission-details{
                margin: 0 18px;
                margin-top: 40px;

                .contain-rights{
                  padding: 0 24px;
                  height: 32px;
                  background: #F5F6F7;
                  display: flex;
                  align-items: center;
                  font-size: 15px;
                  font-weight: 700;
                  span{
                    letter-spacing: 0px;
                    color: #FF514C;
                  }
                }
                .content-box{
                  display: flex;
                  // align-items: center;
                  flex-wrap: wrap;
                  .content-name{
                    width: 43%;
                    margin-top:12px ;
                    padding: 0 10px;
                    font-size: 15px;
                    font-weight: 700;
                    color: #4E5969;
                    height: 24px;
                    line-height: 24px;
                    display: flex;
                    .content-icon{
                      margin-right: 8px;
                      margin-top: -1px;
                      img{
                        width: 8px;
                        height: 8px;
                      }
                    }
                   
                  }
                }

              }
            }
          }
        }

        .monobloc-units {
          width: calc(342px * 3 + 3 * 84px);
          display: flex;
          flex-wrap: wrap;

          .monobloc-units-box {
            width: 342px;
            height: 468px;
            background: rgba(255, 255, 255, 1);
            margin: 0 42px;
            box-shadow: 0px 6px 32px 0px rgba(38, 38, 38, 0.16);
            border-radius: 8px;
            margin-bottom: 30px;

            .back-top {
              width: 100%;
              overflow: auto;
              border-radius: 8px 8px 0 0;
              height: 12px;
              background: rgba(230, 239, 255, 1);

            }

            .set-container {
              height: 325px;

              .set-title-box {
                height: 69px;
                display: flex;
                justify-content: center;
                margin-top: 45px;

                .title-box-container {
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  align-items: center;

                  .set-title {
                    font-size: 32px;
                    font-weight: 700;
                    line-height: 32px;
                    position: relative;

                    .set-city {
                      width: 42px;
                      height: 26px;
                      line-height: 26px;
                      margin-left: 5px;
                      text-align: center;
                      font-size: 13px;
                      font-weight: 700;
                      border-radius: 12px 4px 12px 4px;
                      background: rgba(24, 104, 241, 1);
                      color: rgba(255, 255, 255, 1);
                      position: absolute;
                      right: -52px;
                      top: 0;


                    }

                  }

                  .permission {
                    height: 28px;
                    font-size: 20px;
                    font-weight: 400;
                    line-height: 28px;
                    text-align: center;
                    color: rgba(78, 89, 105, 1);


                  }
                }



              }

              .permission-details {

                margin: 0 16px;
                margin-top: 80px;
                border-bottom: 1px solid rgba(245, 246, 247, 1);

                .contain-rights {
                  height: 32px;
                  background: rgba(245, 246, 247, 1);
                  border-radius: 6px;
                  line-height: 32px;
                  font-size: 15px;
                  font-weight: 600;
                  text-align: left;
                  padding-left: 10px;

                }

                .content-name {
                  height: 24px;
                  padding-left: 10px;
                  position: relative;
                  line-height: 24px;
                  margin-left: 18px;
                  margin-top: 5px;
                  margin-bottom: 5px;
                  width: calc(50% - 28px);

                }

                :after {
                  content: ' ';
                  position: absolute;
                  left: -4px;
                  top: 9px;
                  width: 5px;
                  height: 5px;
                  background-color: rgba(24, 104, 241, 1);
                  border-radius: 4px;
                }

              }
            }

            .set-button {
              display: flex;
              align-items: flex-end;
              margin: 0 30px;
              .shoppingCart{
                background:url(../../assets/shoppingCart3.png) no-repeat;
                background-size: cover;
              }
              
              .shoppingCart:hover{
                background:url(../../assets/shoppingCart4.png) no-repeat;
                background-size: cover;
              }
              .buy-button {
                cursor: pointer;
                height: 48px;
                width: 100%;
                border-radius: 4px;
                font-size: 20px;
                font-weight: 500;
                text-align: center;
                line-height: 48px;
                margin-top: 10px;
                background: rgba(24, 104, 241, 1);
                color: rgba(255, 255, 255, 1);

              
              }
            }



          }

          .monobloc-units-box.monobloc-type-1 {
            .title-box-container {

              .set-title,
              .permission {
                color: #86909C !important;

              }
            }

            .buy-button {
              background: #E7E7E7;
              color: #C9CDD4;

            }
          }
        }
      }
    }
  }

  .prosperity {
    width: 100%;

    .prosperity-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px 40px 14px 40px;

      img {
        width: 50px;
        height: 50px;
      }

      .title {
        font-weight: 700;
        font-size: 20px;
        margin-top: 8px;
        margin-bottom: 16px;
        line-height: 32px;
        height: 32px;
        color: #1D2129;
      }

      .prompt-content {
        height: 66px;
        display: flex;
        flex-direction: column;
        align-items: center;
        line-height: 22px;

        div {
          // margin: 5px 0;
          color: #4E5969;
          font-size: 14px;


        }
      }


      .el-button {
        margin-top: 88px;
        padding: 0 40px;
        width: 306px;
        height: 48px;
        background: #1868F1;

      }
    }

  }

}

.purchase-container {


  .category-container {
    display: flex;
    align-items: center;

    .monobloc-units-box {
      width: 209.5px;
      height: 204px;
      background: rgba(255, 255, 255, 1);
      // border: 2px solid rgba(24, 104, 241, 1);
      border: 1px solid rgba(231, 231, 231, 1);
      border-radius: 11px;
      margin: 0 10px;
      position: relative;

      .back-top {
        width: 100%;
        overflow: auto;
        border-radius: 8px 8px 0 0;
        height: 12px;
        // background: rgba(230, 239, 255, 1);

      }

      .set-container {
        // height: 325px;
        display: flex;
        flex-direction: column;

        .set-title-box {
          height: 60px;
          display: flex;
          justify-content: center;
          margin-top: 25px;
          width: 100%;

          .title-box-container {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;

            .set-title {
              font-size: 22px;
              font-weight: 700;
              color: rgba(29, 33, 41, 1);

            }

            .permission {
              height: 28px;
              font-size: 16px;
              font-weight: 400;
              line-height: 28px;
              text-align: center;
              color: rgba(78, 89, 105, 1);


            }
          }


          .set-city {
            width: 42px;
            height: 26px;
            line-height: 26px;
            margin-left: 5px;
            text-align: center;
            font-size: 13px;
            font-weight: 700;
            border-radius: 12px 4px 12px 4px;
            // background: rgba(24, 104, 241, 1);



          }
        }

        .money {
          margin-top: 25px;
          margin-left: 20px;

          .num {
            // color: rgba(24, 104, 241, 1);
            font-size: 28px;

            .mo {
              font-size: 16px;
              font-weight: 700;
            }

            .units {
              font-weight: 700;
              font-size: 16px;
              color: rgba(29, 33, 41, 1);

            }
          }

          .prompt {
            font-size: 15px;
            font-weight: 600;
            color: rgba(78, 89, 105, 1);
            height: 24px;
            margin: 5px 0;

            .mo {
              font-size: 16px;
              font-weight: 700;
            }
          }
        }
      }

      .recommend {
        position: absolute;
        width: 50px;
        height: 26px;
        padding: 3px 12px 3px 12px;
        border-radius: 8px 0px 5px 0px;
        background: rgba(249, 194, 155, 1);
        right: 0px;
        bottom: 0px;
        text-align: center;
        line-height: 26px;
        color: rgba(29, 33, 41, 1);
        font-size: 13px;
        font-weight: 700;


      }
    }

    .type_PACKAGE_PREMIUM {
      border: 1px solid rgba(249, 194, 155, 1);

      .back-top {
        background: rgba(249, 194, 155, 1);

      }

      .money {
        .num {
          color: rgba(249, 194, 155, 1);


        }
      }

      .set-city {
        color: rgba(78, 89, 105, 1);
        background: rgba(249, 194, 155, 1);

      }
    }

    .type_PACKAGE_STANDARD {
      border: 1px solid rgba(231, 231, 231, 1);

      .back-top {
        background: rgba(230, 239, 255, 1);


      }

      .money {
        .num {
          color: rgba(24, 104, 241, 1);
        }
      }

      .set-city {
        color: rgba(255, 255, 255, 1);
        background: rgba(24, 104, 241, 1);


      }
    }

    .true.type_PACKAGE_STANDARD {
      border: 2px solid rgba(24, 104, 241, 1);
      height: 220px;

      .back-top {
        width: 100%;
        overflow: auto;
        border-radius: 8px 8px 0 0;
        height: 28px;
        background: rgba(24, 104, 241, 1);
        display: flex;
        justify-content: center;
        align-items: center;

        .selected-true {
          height: 100%;
          display: flex;
          align-items: center;
          font-size: 13px;
          font-weight: 700;
          color: rgba(255, 255, 255, 1);
          line-height: 28px;

          img {
            width: 14px;
            height: 14px;
            margin-right: 5px;
          }
        }
      }

    }

    .true.type_PACKAGE_PREMIUM {
      border: 2px solid rgba(249, 194, 155, 1);
      height: 220px;

      .back-top {
        width: 100%;
        overflow: auto;
        border-radius: 8px 8px 0 0;
        height: 28px;
        background: rgba(249, 194, 155, 1);
        display: flex;
        justify-content: center;
        align-items: center;

        .selected-true {
          height: 100%;
          display: flex;
          align-items: center;
          font-size: 13px;
          font-weight: 700;
          color: rgba(29, 33, 41, 1);

          line-height: 28px;

          img {
            width: 14px;
            height: 14px;
            margin-right: 5px;
          }
        }

      }

    }

    .monobloc-units-box:first-child {
      margin-left: 0px;
    }

    .monobloc-units-box:last-child {
      margin-right: 0px;
    }


  }

  .count-add {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;

    .burning {
      height: 42px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .burning-time {
        text-align: right;

        span {
          font-size: 14px;
          font-weight: 700;
          line-height: 22px;
          color: rgba(29, 33, 41, 1);


        }
      }

      .prompt {}
    }


  }

  .tcp-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 72px;
  }

  .single-box {
    height: 80px;
    display: flex;
    border: 2px solid rgba(24, 104, 241, 1);
    border-radius: 8px;

    .left-back {
      width: 92px;
      height: 100%;
      background: rgba(24, 104, 241, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;


      .single-tltle {
        font-size: 13px;
        font-weight: 700;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);

      }

      .city {
        width: 42px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 1);
        color: rgba(24, 104, 241, 1);


      }



    }

    .single-details {
      height: 100%;
      width: calc(100% - 92px);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;

      .name-box {
        height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .name {
          font-size: 22px;
          font-weight: 700;
          color: rgba(29, 33, 41, 1);

        }

        .equity {
          font-size: 16px;
          font-weight: 700;
          color: rgba(78, 89, 105, 1);

        }
      }

      .money {
        font-size: 16px;
        font-weight: 700;
        color: rgba(29, 33, 41, 1);


        .mo {
          color: rgba(24, 104, 241, 1);
        }

        .num {
          font-size: 28px;
          font-weight: 500;
          color: rgba(24, 104, 241, 1);


        }
      }

    }
  }

  .qr-code-box {
    height: 128px;
    display: flex;
    align-items: center;

    img {
      width: 120px;
      height: 120px;
    }

    .payment-box {
      height: 56px;
      margin-left: 10px;

      .payment {

        font-size: 16px;
        font-weight: 700;
        line-height: 24px;
        color: rgba(29, 33, 41, 1);

        .num {
          font-size: 28px;
          font-weight: 500;
          line-height: 24px;
          color: rgba(24, 104, 241, 1);

          .mo {
            font-size: 16px;
            font-weight: 700;

          }
        }
      }

      .manner>:nth-child(1) {
        display: flex;
        color: rgba(29, 33, 41, 1);
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;

        // div {
        //   width: 16px;
        //   height: 16px;
        //   border: 1px solid rgba(29, 33, 41, 0.5);
        //   margin-right: 5px;
        // }
      }
    }
  }
}

.qrcode{
  // overflow: hidden; transform: scale(0.6); 
  // transform-origin: 100px 50px;
  width: 148px;
  height: 148px;
  margin: -32px 28px 0 24px;
}

.month_year,.set_button1{
  display: flex;
  align-items: flex-end;
  &:hover .shoppingCart{
    display: block;
  }

  &:hover .buy-button{
    width: calc(100% - 61px)!important;
  }
}


.shoppingCart{
  cursor: pointer;
  margin:0 10px 0px 0;
  width: 49px;
  height: 49px;
  display: none;
}

.shoppingCart1{
  background:url(../../assets/shoppingCart3.png) no-repeat;
  background-size: cover;
}

.receiveSuccess{
  position: fixed;
  top: 0%;
  right: 0%;
  width: 100%;
  height: 100%;
  background: #fff;
  .box_receive{
    width: 600px;
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    .content{
      width:600px;
      height: 232px;
      display: flex;
      flex-direction: column;
      align-items: center;
      img{
        width: 80px;
        height: 80px;
      }
      .content1{
        margin: 8px 0 16px 0;
        font-size: 22px;
        font-weight: 700;
        line-height: 32px;
        color: #1D2129;
      }
      .contentTwo{
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 15px;
        font-weight: 400;
        line-height: 32px;
        color: #4E5969;
      }
    }
    .box_Btn{
      margin-top: 103px;
      width: 306px;
      height: 48px;
      border-radius: 4px;
      line-height: 48px;
      text-align: center;
      background: #1868F1;
      color: #fff;
      cursor: pointer;
    }
  }
}

.prosperityCoucher{
  width: calc(100% - -32px);
  margin: 0 -16px;
  height: 134px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 1px solid #E7E7E7;
  .titleDetails{
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      text-align: center;
      color: #86909C;
      margin: 12px 0 10px 0;
  }
}


.purchase-box {
  display: flex;
  height: 64px;
  align-items: center;


  ::v-deep .el-input-number {
    width: 110px !important;
    flex-shrink: 0;
    .el-icon {
      font-size: large;
      width: 20px;
      color: #000;
    }
    .el-input-number__decrease,
    .el-input-number__increase {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      background: #ffffff;
      border: 1px solid #e7e7e7;
      &:hover {
        background: #f5f5f5;
      }
    }
    .el-input__wrapper {
      height: 24px;
      line-height: 24px;
      background: #F5F8FD;
    }
    .el-input {
      --el-input-border-color: none;
      --el-input-hover-border-color: none;
      --el-input-clear-hover-color: none;
      --el-input-focus-border-color: none;

      .el-input__inner {
        color: #1d2129;
        font-size: 20px;
        font-weight: 500;
      }
    }
  }
  
  .name {
    color: rgba(78, 89, 105, 1);
    font-size: 14px;
  }

  .value {
    margin-left: 20px;
  }

  .people-box {
    display: flex;
    align-items: center;
    margin-left: 10px;

    .people {
      width: 32px;
      height: 44px;
      line-height: 44px;
      color: rgba(78, 89, 105, 1);
      text-align: center;
      margin: 15px;
      cursor: pointer;
    }

    .true {
      color: rgba(29, 33, 41, 1);
      border-bottom: 3px solid rgba(24, 104, 241, 1);
      border-radius: 2px;
    }
  }
}

.tab_line{
  width: calc(100% - 90px);
  height: 104px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F5F8FD;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  box-shadow: 0px -4px 4px 0px #9E90F029;
  margin-bottom: 40px;
  .tab_Rright{
    display: flex;
    align-items: center;
    margin-left: 80px;
   .name{
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
      letter-spacing: 0%;
      text-align: center;
     color: #1D2129;
     margin-right: 40px;
   }
  }
}

.city_list{
  border-radius: 12px;
  background: #fff;
  .header_list{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 4px 16px 4px;
    .header_list_name{
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: #1D2129;
      }
      .header_list_btn{
        display: flex;
        align-items: center;
        .all{
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          color: #86909C;
          margin-right: 24px;
          cursor: pointer;
        }
        .header_close{
          margin-top: 3px;
          cursor: pointer;
        }
      }
  }
.cityListF{
  display: flex;
  flex-wrap: wrap;
  .active_item{
    background: #EDF4FF!important;
  }
  .cityList_item{
    background: #F5F8FD;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 124px;
    height: 40px;
    border-radius: 8px;
    margin:0 4px  8px 4px;
    .cityList_item_name{
      margin-left: 8px;
    }
  }
}

.bottom_btn{
  height: 46px;
  display: flex;
  // align-items: center;
  justify-content: end;
  div{
    width: 80px;
    height: 38px;
    border-radius: 12px;
    background: #1868F1;
    line-height: 38px;
    text-align: center;
    cursor: pointer;
font-weight: 500;
font-size: 14px;
color: #FFFFFF;
  }
}

}

.set_container{
  height: auto!important;
}

.set_meal_box{
  height: auto!important;
}

.set_meal_box{
  width: 342px;
  margin: 0 42px;
  background: #fff;
  border-radius: 8px;
  .back_top{
    border-radius: 8px 8px 0 0;
  }
  .set_container{
    height: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 18px;
    font-weight: bold;
    color: #1D2129;
  }
  .permission{
    height: 20px;
    text-align: center;
    line-height: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }
}

.set_button{
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  .set_Yuan{
font-weight: 700;
font-size: 16px;
line-height: 24px;
color: #1868F1;
  }
  .set_number{
font-weight: 500;
font-size: 20px;
line-height: 24px;
color: #1868F1;

  }
}
