<template>
	<div class="table-container" v-if="itemCoupons.couponsType === '1'">
		<div class="details-box">
			<div class="operate">
				<div class="status">
					<img src="/src/assets/car.png" alt="" />
				</div>
				<div class="button-data">
					<div class="button_top_content">
						<div>{{ itemCoupons.name }}</div>
						<!-- <div>{{ itemCoupons.desc }}</div> -->
					</div>
					<div class="button_content_two">{{ itemCoupons.desc }}</div>
				</div>
			</div>
			<div class="center_line"></div>
			<div class="right-content">
				<div class="right_top_content">优惠好礼</div>
				<div class="right_bottom_content" @click="handleAddCrad">立即使用</div>
			</div>
		</div>
	</div>

	<div class="table-container" v-if="itemCoupons.couponsType === '2' || itemCoupons.couponsType === '5' || itemCoupons.couponsType === '6'">
		<div class="details-box details_box_notUsed">
			<div class="operate">
				<div class="status">
					<div class="type notUsed">{{ itemCoupons.statusName }}</div>
				</div>
				<div class="button-data">
					<div class="button_top_content">
						<div>{{ itemCoupons.name }}</div>
						<div>{{ itemCoupons.brandName }}</div>
					</div>
					<!-- <div class="button_content_two">有效期至：{{ itemCoupons.expirationTime }}</div> -->
				</div>
			</div>
			<div class="center_line"></div>
			<div class="right-content">
				<div class="right_top_content" v-if="itemCoupons.couponsType === '2'" @click="handledetails">查看详情</div>
				<div class="right_bottom_content" v-if="itemCoupons.couponsType === '2'" @click="handleAddCrad">立即使用</div>
				<div class="notUsedRight" style="color: #86909c" v-if="itemCoupons.couponsType === '6'" @click="handledetails">查看详情</div>
				<!-- 详情--->
				<div class="notUsedRight" v-if="itemCoupons.couponsType === '5'">{{ itemCoupons.statusName }}</div>
			</div>
		</div>
	</div>

	<div class="table-container" v-if="itemCoupons.couponsType === '3' || itemCoupons.couponsType === '4'">
		<div class="details-box usedAlready">
			<div class="operate">
				<div class="status">
					<div class="type notUsed">{{ itemCoupons.statusName }}</div>
				</div>
				<div class="button-data">
					<div class="button_top_content">
						<div>{{ itemCoupons.name }}</div>
						<div>{{ itemCoupons.brandName }}</div>
					</div>
					<!-- <div class="button_content_two">{{ itemCoupons.expirationTime }}{{ itemCoupons.statusName }}</div> -->
				</div>
			</div>
			<div class="center_line"></div>
			<div class="right-content">
				<div class="notUsedRight" @click="handledetails">查看详情</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { reactive } from 'vue';
const emit = defineEmits(['handleAddCrad']);
const props = defineProps({
	itemCoupons: {
		type: Object,
		default: () => {
			// 劵类型 1：发放劵 2：未使用劵 3：已使用劵 4：已过期劵（暂时用不到） 5：详情未使用劵 6：发放中
			// couponsType: '',
		},
	},
});
let list = reactive({ dataListCard: [] });
/**
 * @function handleAddCrad 使用人
 */
function handleListCard(data) {
	list.dataListCard = data;
}

/**
 * @function handledetails 跳转卡券详情
 */
function handledetails() {
	emit('handleWelfareDetails', props.itemCoupons);
}

//立即使用
function handleAddCrad() {
	emit('handleWelfareAddCrad', props.itemCoupons);
}

defineExpose({ handleListCard, list });
</script>

<style lang="scss" scoped>
@import url('./css.css');
</style>
