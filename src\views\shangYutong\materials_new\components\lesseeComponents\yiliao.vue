<template>
	<div class="lingshou_wrap">
		<template v-if="buildData && buildData.length > 1">
			<div class="double_box_wrap" style="margin-bottom: 16px">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">品牌分布</div>
					</div>
					<!-- <div class="content_wrap">

					</div> -->
					<div class="empty_wrap">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">品牌分析</div>
					</div>
					<!-- <div class="content_wrap" >

					</div> -->
					<div class="empty_wrap">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">经营分析</div>
					</div>
					<!-- <div class="content_wrap" >

					</div> -->
					<div class="empty_wrap">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
			<div class="double_box_wrap">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">品牌分布</div>
					</div>
					<!-- <div class="content_wrap" >

					</div> -->
					<div class="empty_wrap">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">品牌分析</div>
					</div>
					<!-- <div class="content_wrap" >

					</div> -->
					<div class="empty_wrap">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">经营分析</div>
					</div>
					<!-- <div class="content_wrap" >

					</div> -->
					<div class="empty_wrap">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</template>
		<template v-else>
			<div class="box_wrap">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">品牌分布</div>
						</div>
						<!-- <div class="content_wrap" >

						</div> -->
						<div class="empty_wrap">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">品牌分析</div>
						</div>
						<!-- <div class="content_wrap" >

						</div> -->
						<div class="empty_wrap">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">经营分析</div>
						</div>
						<!-- <div class="content_wrap" >

						</div> -->
						<div class="empty_wrap">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';

const props = defineProps({
	buildData: {
		type: Array,
		default: () => [],
	},
});
watch(
	() => props.buildData,
	(newVal) => {
		if (newVal.length > 0) {
			console.log('props', newVal);
		}
	},
	{
		deep: true,
	}
);

const leftData = ref({});
const rightData = ref({});
</script>
<style lang="less" scoped>
.lingshou_wrap {
    flex: 1;
    display: flex;
    gap: 16px;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
		.single_wrap {
			box-sizing: border-box;
			min-height: 300px;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.content_wrap {
				padding: 20px 16px;
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
	.double_box_wrap {
        flex: 1;
		display: flex;
        flex-direction: column;
		gap: 16px;
		.single_wrap {
			flex: 1;
			box-sizing: border-box;
			min-height: 300px;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.content_wrap {
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
}
</style>
