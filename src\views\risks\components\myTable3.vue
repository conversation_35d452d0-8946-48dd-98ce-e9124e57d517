<template>
    <div class="fc" >
        <div class="title">{{ data.title}}</div>
        <div class="fcc">
            <div   v-for="(item, key) of data" :key="key">
                <div v-if="dict[key]" class="fr">
                    <span  class="label">{{ dict[key] }}</span> <span class="val">{{ parseFloat(item)?parseFloat(item).toFixed(2):parseFloat(item) }}</span>

                </div>  </div>
        </div>
       
    </div>
</template>
<style scoped>
    .fc {
        margin-top: 30px;
        display: flex;
        box-sizing: border-box;
        flex-direction: column;
        padding: 0 200px;
        justify-content: space-between;
        align-items: flex-star;
        width: 100%;
        height: 100%;
        font-size: 13px;
        .title{
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        }
        .fcc{
            color: #343232;
        }
    }
    .fr {
        display: flex;
        height: 30px;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        }
        .val{
            font-weight: bold;
        }
</style>
<script setup>
import { ref,defineProps,onMounted} from 'vue';
const obj = ref("")
const props = defineProps({
  data: {
    type: Object,
    default: undefined
  },
  
})
const dict = {
    totalCost:"综合成本",
    issuanceSize:"发行规模",
    totalCostRate:"综合成本率",
}

</script>