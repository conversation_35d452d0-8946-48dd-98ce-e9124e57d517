<template>
	<div>
		<div class="information_content_top">
			<div class="containerPcenter">
				<div class="content">个人中心</div>
			</div>
			<div class="content_top">
				<div class="content_Img">
					<img src="@/assets/rectangles.png" alt="" v-if="!imageUrl" />

					<el-upload
						method="get"
						:show-file-list="false"
						class="avatar-uploader"
						action="#"
						:http-request="uploadFile"
						:before-upload="beforeAvatarUpload"
					>
						<div v-if="imageUrl">
							<img :src="imageUrl" class="avatar" />
						</div>
						<div v-else class="updataName">上传头像</div>
					</el-upload>
				</div>
				<div class="content_center">
					<div>
						<div class="titleName">用户名</div>
						<div class="container_Input">
							<el-input v-model="storeUserInfo.userName" :disabled="userNameStatus" style="" size="large" placeholder="用户名" />
							<div @click="handleUpdataName()">{{ userNameStatus ? '修改用户名' : '保存修改' }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="information_content_bottom">
			<div class="containerPcenter">
				<div class="content">安全设置</div>
			</div>
			<div class="content_center">
				<div>
					<div class="titleName">手机号</div>
					<div class="container_Input">
						<el-input v-model="storeUserInfo.phone" disabled size="large" placeholder="手机号" />
						<div @click="handleUpdataPhone">修改手机号</div>
					</div>
				</div>
				<div>
					<div class="titleName">密码</div>
					<div class="container_Input">
						<el-input v-model="passwords" type="password" disabled size="large" placeholder="密码" />
						<div @click="wordDialogVisible = true">修改密码</div>
					</div>
				</div>
			</div>
		</div>

		<el-dialog
			v-model="dialogVisible"
			:close-on-click-modal="false"
			:destroy-on-close="true"
			:show-close="false"
			align-center="center"
			:before-close="handleClose"
			style="width: 448px"
		>
			<div class="form_content">
				<div class="content">
					<div class="title">修改手机号</div>
					<div class="contentDetails">已向手机号{{ storeUserInfo.phone }}发送验证码，请查收</div>
				</div>
				<div class="content_banner container_Input">
					<el-input v-model="informationObj.oldCode" style="width: 100%" size="large" placeholder="请输入验证码">
						<template #suffix>
							<div class="verificationCode" @click="onGetCodeOld()" v-if="isSendOld">发送验证码</div>
							<div class="getCode" v-else style="margin-right: 8px">重新发送{{ timeOld }}s</div>
						</template>
					</el-input>
					<el-input v-model="informationObj.newPhone" style="width: 100%" size="large" placeholder="请输入新手机号" />
					<el-input v-model="informationObj.newCode" style="width: 100%" size="large" placeholder="请输入验证码">
						<template #suffix>
							<div class="verificationCode" @click="onGetCode()" v-if="isSend">发送验证码</div>
							<div class="getCode" v-else style="margin-right: 8px">重新发送{{ time }}s</div>
						</template>
					</el-input>
				</div>
			</div>
			<template #footer>
				<div class="dialog_footer">
					<div>
						<el-button @click="handleCancel(2)">取消</el-button>
						<el-button type="primary" color="#1868F1" @click="handleDetermine">修改</el-button>
					</div>
				</div>
			</template>
		</el-dialog>

		<el-dialog
			v-model="wordDialogVisible"
			:destroy-on-close="true"
			:close-on-click-modal="false"
			:show-close="false"
			align-center="center"
			:before-close="handleClose"
			style="width: 448px"
		>
			<div class="form_content">
				<div class="content contentpassword">
					<div class="title">修改密码</div>
				</div>
				<div class="content_banner container_Inputs miyaoWrap">
					<el-input
						v-model="passwordInformationObj.oldPassword"
						type="text"
						:class="oldPassword ? 'no-autofill-pwd' : 'no-auto'"
						style="width: 100%"
						size="large"
						placeholder="请输入旧密码"
					>
						<template #suffix>
							<div style="display: flex; cursor: pointer">
								<img v-if="oldPassword" src="@/assets/hideIcon.png" alt="" @click="handleShowPwdlod" />
								<img v-else src="@/assets/showIcon.png" alt="" @click="handleShowPwdlod" />
							</div>
						</template>
					</el-input>
					<el-input
						v-model="passwordInformationObj.password"
						type="text"
						:class="password ? 'no-autofill-pwd' : 'no-auto'"
						style="width: 100%"
						size="large"
						placeholder="请输入新密码"
					>
						<template #suffix>
							<div style="display: flex; cursor: pointer">
								<img v-if="password" src="@/assets/hideIcon.png" alt="" @click="handleShowPwd" />
								<img v-else src="@/assets/showIcon.png" alt="" @click="handleShowPwd" />
							</div>
						</template>
					</el-input>
					<div class="passwordLimit">
						<div>
							<el-icon><Warning /></el-icon>
						</div>
						<div>登录密码限6-20个英文字符，且包含至少一个数字，请勿使用空格</div>
					</div>
					<el-input
						v-model="passwordInformationObj.passwords"
						:class="passwordspwd ? 'no-autofill-pwd' : 'no-auto'"
						type="text"
						style="width: 100%"
						size="large"
						placeholder="请重复新密码"
					>
						<template #suffix>
							<div style="display: flex; cursor: pointer">
								<img v-if="passwordspwd" src="@/assets/hideIcon.png" alt="" @click="showPwd" />
								<img v-else src="@/assets/showIcon.png" alt="" @click="showPwd" />
							</div>
						</template>
					</el-input>
				</div>
			</div>
			<template #footer>
				<div class="dialog_footer">
					<div>
						<el-button @click="handleCancel(1)">取消</el-button>
						<el-button type="primary" color="#1868F1" @click="handleDeterminePassword">修改</el-button>
					</div>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { getCodes } from 'REQUEST_API';
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { getPresignedUrl, getchangeName, getChangePhone, getChangePassword, getChangeHeadpre } from '../../api/home';
import { vuexStore, useStore } from '../../store';
// import { vuexStore, useStore } from '../../api/';
import axios from 'axios';
console.log(vuexStore, 'store');
const store = useStore();
const storeUserInfo = vuexStore.state.userInfo;
// 密码弹窗展示状态
const wordDialogVisible = ref(false);
// 修改手机号码弹窗展示状态
const dialogVisible = ref(false);
// 获取当前手机号验证码秒数
const timeOld = ref(60);
// 获取当前手机号验证码
const isSendOld = ref(true);
// 变更验证码秒数
const time = ref(60);
// 变更验证码
const isSend = ref(true);

const userNameStatus = ref(true);

const imageUrl = ref('');

let passwords = ref('88888888');

const passwordspwd = ref(true);

const oldPassword = ref(true);

const password = ref(true);

let informationObj = reactive({
	oldCode: null, //用户名
	newPhone: null, //手机号
	newCode: null, //密码
});

let passwordInformationObj = reactive({
	oldPassword: null, // 旧密码
	password: null, //新密码
	passwords: null, //重复新密码
});

onMounted(() => {
	// upDate();
	if (storeUserInfo.headImage) {
		imageUrl.value = store.imagePathPrefix + storeUserInfo.headImage;
	}
});

function handleShowPwd() {
	password.value = !password.value;
}

function handleShowPwdlod() {
	oldPassword.value = !oldPassword.value;
}

function showPwd() {
	passwordspwd.value = !passwordspwd.value;
}

// 手机号修改
function handleUpdataPhone() {
	dialogVisible.value = true;
	handleCodes(storeUserInfo.phone, 1); // 获取当前手机号验证码
}

function onGetCodeOld() {
	handleCodes(storeUserInfo.phone, 1); // 获取当前手机号验证码
}

// 获取变更手机号验证码
const onGetCode = async () => {
	const newPhone = informationObj.newPhone;

	if (!newPhone) {
		return ElMessage({
			message: '请输入手机号',
			type: 'warning',
		});
	}
	if (storeUserInfo.phone === newPhone) {
		return ElMessage({
			message: '请输入要变更的手机号',
			type: 'warning',
		});
	}
	// 校验手机号
	const reg = /^1[3-9]\d{9}$/;
	if (!reg.test(newPhone)) {
		return ElMessage({
			message: '请输入正确的手机号',
			type: 'warning',
		});
	}
	handleCodes(newPhone, 2);
};
// 发送验证码
async function handleCodes(phone, type) {
	await getCodes({ phone: phone })
		.then((res) => {
			console.log(res);
			// 发送成功提示
			ElMessage({
				message: '验证码发送成功,请注意查收~',
				type: 'success',
			});
			if (type === 1) {
				isSendOld.value = false;
				const timer = setInterval(() => {
					timeOld.value--;
					if (timeOld.value == 0) {
						clearInterval(timer);
						isSendOld.value = true;
						timeOld.value = 60;
					}
				}, 1000);
			} else {
				isSend.value = false;
				const timer = setInterval(() => {
					time.value--;
					if (time.value == 0) {
						clearInterval(timer);
						isSend.value = true;
						time.value = 60;
					}
				}, 1000);
			}
		})
		.catch((err) => {
			console.log('err', err);
			// 显示错误信息给用户
		});
}

// 更新用户名
function handleUpdataName() {
	userNameStatus.value = !userNameStatus.value;
	if (userNameStatus.value) {
		getchangeName({ userName: storeUserInfo.userName }).then((res) => {
			store.getUserInfo();
		});
	}
}

// 自行实现上传文件的请求
const uploadFile = async (item) => {
	const index = item.file.type.indexOf('/'); // 找到第一个 '/' 的位置
	const result = item.file.type.substring(index + 1);
	handleHeadPre('.' + result, item.file);
};

// 上传文件之前的钩子
function beforeAvatarUpload(rawFile) {
	if (rawFile.type !== 'image/png' && rawFile.type !== 'image/jpg' && rawFile.type !== 'image/jpeg') {
		ElMessage.warning('请上传png/jpg格式的图片');
		return false;
	}
	return true;
}

function handleHeadPre(params, rawFile) {
	getChangeHeadpre({ suffix: params }).then((res) => {
		axios
			.put(res.data.uploadUrl, rawFile, {
				headers: {
					'Content-Type': rawFile.type,
				},
			})
			.then(() => {
				getPresignedUrl({ path: res.data.path }).then((resData) => {
					if (resData.code === 200) {
						store.getUserInfo();
						let randomInt = Math.floor(Math.random() * 100) + 1;
						imageUrl.value = store.imagePathPrefix + res.data.path + '?v=' + randomInt;
						ElMessage({
							message: '上传成功',
							type: 'success',
						});
					}
				});
			});
	});
}
// 关闭对话框
function handleClose() {
	dialogVisible.value = false;
	wordDialogVisible.value = false;
}

/**
 * @function handleDetermine 确定修改手机号
 */
function handleDetermine() {
	getChangePhone({ ...informationObj }).then((res) => {
		if (res.code === 200) {
			dialogVisible.value = false;
			ElMessage({
				message: '修改成功',
				type: 'success',
			});
			store.getUserInfo();
			storeUserInfo.phone = informationObj.newPhone;
		}
	});
}

function handleCancel(type) {
	passwordspwd.value = true;
	oldPassword.value = true;
	password.value = true;
	if (type === 1) {
		wordDialogVisible.value = false;
		passwordInformationObj.oldPassword = '';
		passwordInformationObj.password = '';
		passwordInformationObj.passwords = '';
	} else {
		dialogVisible.value = false;
		informationObj.oldCode = '';
		informationObj.newPhone = '';
		informationObj.newCode = '';
	}
}

// 密码修改
function handleDeterminePassword() {
	if (passwordInformationObj.password !== passwordInformationObj.passwords) {
		ElMessage.warning('请输入相同的密码');
		return false;
	}
	getChangePassword({ ...passwordInformationObj }).then((res) => {
		if (res.code === 200) {
			wordDialogVisible.value = false;
		}
	});
}
</script>

<style lang="scss" scoped>
.containerPcenter {
	width: 100%;
	.content {
		width: calc(100% - 32px);
		height: 69px;
		border-bottom: 1px solid #e7e7e7;
		margin: 0 16px;
		font-size: 14px;
		font-weight: 500;
		line-height: 69px;
		color: #1d2129;
	}
}

.information_content_top {
	width: 100%;
	height: 340px;
	border-radius: 6px;
	background: #ffffff;
	margin-bottom: 8px;
	.content_top {
		width: 100%;
		height: 270px;
		.content_Img {
			height: 80px;
			margin: 20px 32px 40px 32px;
			display: flex;
			img {
				width: 80px;
				height: 80px;
				border-radius: 13px;
			}
			.updataName {
				height: 80px;
				line-height: 80px;
				margin-left: 7px;
				font-size: 14px;
				font-weight: 500;
				color: #1868f1;
				cursor: pointer;
			}
		}
	}
}

.information_content_bottom {
	width: 100%;
	height: calc(100vh - 468px);
	border-radius: 6px;
	background: #ffffff;
}

.content_center {
	height: 90px;
	display: flex;
	margin: 20px 32px 0 32px;
	.titleName {
		margin-bottom: 16px;
		height: 20px;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		color: #1d2129;
	}
}

.content_center > :nth-child(2) {
	margin-left: 80px;
}

::v-deep .el-dialog {
	padding: 0;
}

.form_content {
	margin: 0 24px;
	width: calc(100% - 48px);
	height: 300px;
	.content {
		height: 56px;
		.title {
			font-size: 16px;
			font-weight: 700;
			line-height: 24px;
			color: #1d2129;
			margin-bottom: 10px;
		}
		.contentDetails {
			font-size: 14px;
			font-weight: 500;
			line-height: 22px;
			color: #4e5969;
		}
	}

	.content_banner {
		height: 258px;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-start;
		.el-input {
			margin: 24px 0 0 0;
		}
	}
}
::v-deep .container_Input {
	display: flex;
	.el-input {
		width: 275px;
		height: 54px;
		margin-right: 16px;
	}
	.el-input,
	.is-disabled {
		.el-input__wrapper {
			background: #fff !important;
			color: #c9cdd4 !important;
		}
	}
	div {
		cursor: pointer;
		line-height: 54px;
		font-size: 14px;
		font-weight: 500;
		color: #1868f1;
	}
}

.dialog_footer {
	height: 48px;
	width: calc(100% - 48px);
	padding: 0 24px;
	.el-button {
		padding: 0 24px;
	}
}

.passwordLimit {
	margin-top: 8px;
	width: 100%;
	font-size: 12px;
	font-weight: 400;
	line-height: 20px;
	height: 20px !important;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #86909c;
}

.contentpassword {
	height: 24px !important;
	div {
		height: 24px !important;
	}
}

.container_Inputs {
	display: flex;
	.el-input {
		width: 275px;
		height: 54px;
		margin-right: 16px;
	}
	.el-input,
	.is-disabled {
		.el-input__wrapper {
			background: #fff !important;
			color: #c9cdd4 !important;
		}
	}
}

.miyaoWrap {
	.no-autofill-pwd {
		-webkit-text-security: disc !important;
	}
	.no-auto {
		-webkit-text-security: none !important;
	}
}
</style>
