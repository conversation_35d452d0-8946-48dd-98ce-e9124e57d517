<template>
	<div style="display: flex; justify-content: center">
		<arco-table :pagination="false" :data="tableData" style="width: 100%" :lazy="true" :bordered="{ cell: true }" :class="classname">
			<template #columns>
				<arco-table-column title="二、存续期内支出" align="center">
					<arco-table-column
						resizable
						v-for="column in tableColumns"
						:key="column.prop"
						:data-index="column.prop"
						:title="column.label"
						:width="column.width"
					>
						<template #cell="scope">
							<div v-if="column.prop == 'assumptionStr'">
								<el-tooltip v-if="assumptionIs(scope.record.title)" effect="dark" :content="handleContent(scope.record.title)" placement="left">
									<arco-input-number
										class="proportionColumn"
										hide-button
										size="small"
										v-model="scope.record.assumptionStr"
										@change="assumption(scope.record, $event)"
									>
										<template #suffix><div v-if="handleAssumptionIs(scope.record.title)">%</div> </template>
									</arco-input-number>
								</el-tooltip>

								<span v-else>{{ $utils.handleNumber(scope.record.assumptionStr * 100) }}</span>
							</div>
							<div v-else>
								{{ column.label === '计算（元）' ? $formattedMoney($utils.handleNumber(scope.record[column.prop])) : scope.record[column.prop] }}
							</div>
						</template>
					</arco-table-column>
				</arco-table-column>
			</template>
		</arco-table>
	</div>
</template>
<script setup>
import { defineProps, defineEmits, onMounted } from 'vue';
import { ref } from 'vue';
const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
	classname: {
		type: String,
		default: [],
	},
});

const tableData = ref([]);

const emit = defineEmits(['search']);
const tableColumns = [
	{ prop: 'title', label: '科目', width: 256 },
	{ prop: 'calc', label: '计算（元）', width: 256 },
	{ prop: 'assumptionStr', label: '假设（%）' },
];

const list = ref([
	{ name: '专项计划管理费', key: 'planManagementFeeRate', value: '' },
	{ name: '专项计划托管费', key: 'planCustodyFeeRate', value: '' },
	{ name: '付息兑付手续费', key: 'redemptionFee', value: '' },
	{ name: '跟踪评级', key: 'trackingRating', value: '' },
	{ name: '跟踪审计', key: 'trackingAudit', value: '' },
	{ name: '跟踪评估', key: 'trackingAssessment', value: '' },
	{ name: '保证金投资收益', key: 'marginInvestmentReturnRate', value: '' },
]);

onMounted(() => {
	tableData.value = [];
	handleCombined();
});

// 组合数据
function handleCombined() {
	props.data.forEach((element, index) => {
		element.assumptionStr = Number(element.assumptionStr);
		element.key = index + 1;
		if (element.title == '基金层面费用/年') {
			tableData.value.push({
				...element,
				children: [],
			});
		}
		if (element.title == '项目公司实际投资金额') {
			tableData.value.push({
				...element,
			});
		}
		if (element.title == '专项计划层面支出/年') {
			tableData.value.push({
				...element,
				children: [],
			});
		}
		if (element.title == '基金管理费' || element.title == '基金托管费' || element.title == '基金特别管理费') {
			tableData.value[0]['children'].push(element);
		}
		if (
			element.title == '专项计划管理费' ||
			element.title == '专项计划托管费' ||
			element.title == '付息兑付手续费' ||
			element.title == '跟踪评级' ||
			element.title == '跟踪审计' ||
			element.title == '跟踪评估'
		) {
			tableData.value[2]['children'].push(element);
		}

		if (element.title == '优先A1收益率' || element.title == '优先A2收益率' || element.title == '优先A3收益率' || element.title == '保证金投资收益') {
			tableData.value.push({
				...element,
			});
		}
	});
}

function handleContent(params) {
	let name = '';
	if (params == '专项计划管理费' || params == '专项计划托管费') {
		name = '0<x≤0.93';
	}

	if (params == '付息兑付手续费' || params == '跟踪评级' || params == '跟踪审计' || params == '跟踪评估') {
		name = '大于等于0';
	}
	if (params == '保证金投资收益') {
		name = '3.65<x≤8.00';
	}

	let str = `输入${name}的数值输入后点击左上角“保存并计算”重新计算数据`;
	return str;
}

const assumption = (val, event) => {
	list.value.forEach((item, index) => {
		if (item.name === val.title) {
			item.value = val.assumptionStr;
			console.log(list.value, 'list.value');
			console.log(list.value[index], props.classname, 'list.value');

			emit('search', list.value[index], props.classname);
		}
	});
};
const assumptionIs = (val) => {
	if (list.value.filter((item) => item.name == val).length > 0) {
		return true;
	} else {
		return false;
	}
};

function handleAssumptionIs(val) {
	if (val !== '付息兑付手续费' && val !== '跟踪评级' && val !== '跟踪审计' && val !== '跟踪评估') {
		return true;
	}
	return false;
}
</script>
<style scoped lang="less">
::v-deep .arco-table-tr .arco-table-cell {
	padding: 8.5px 11px;
}

::v-deep tbody .arco-table-tr > :nth-child(1) .arco-table-cell > :nth-child(1) {
	padding-left: 0px !important;
}

::v-deep tbody .arco-table-tr > :nth-child(1) .arco-table-cell-inline-icon {
	padding: unset !important;
	margin-left: 0px !important;
	margin-right: 8px !important;
	.arco-table-expand-btn {
		background: #fff !important;
		border: 1.33px solid #4e5969 !important;
	}
}

::v-deep tbody .arco-table-tr > :nth-child(1) .arco-table-cell > :nth-child(1) {
	padding: 0px !important;
	margin-left: 21px;
}

::v-deep .arco-table-td .arco-table-cell {
	height: 40px;
	box-sizing: border-box;
}
::v-deep .proportionColumn {
	height: 28px;
	// padding: 0 16px!important;
}
</style>
