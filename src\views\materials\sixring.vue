<template>
	<div>
		<div ref="echartsContaine" style="width: 500px; height: 550px; padding: 0 80px"></div>
	</div>
</template>

<script setup>
	import {
		ref,
		reactive,
		toRefs,
		onMounted,
		watch,
		nextTick
	} from 'vue'
	import * as echarts from 'echarts';
	const echartsContaine = ref(null);
	let myChart = null;
	const newArr = ref([])
	const props = defineProps({
		sixRingDateList: {
			type: Array,
			default: []
		}
	});
	const legend1 = ref('数据1')
	const legend2 = ref('数据2')
	console.log(props.sixRingDateList, 323455);
	onMounted(() => {
		nextTick(()=>{
			// 初始化 ECharts 实例
			myChart = echarts.init(echartsContaine.value);
			// 设置图表选项
			myChart.setOption({
				title: {
					text: '六角擂台',
					left: 'center',
				},
				legend: {
					data: ['数据1','数据2'],
				},
				radar: {
					indicator: [{
						name: '维护情况',
					}, {
						name: '周边配套',
					}, {
						name: '区域潜力',
					}, {
						name: '人均消费能力',
					}, {
						name: '商业活力',
					}, {
						name: '评估结果',
					}]
				},
				series: [{
					name: '六角擂台',
					type: 'radar',
					data: [{
							value: [4300, 10000, 28000, 35000, 50000, 19000],
							name: '数据1',
						},
						{
							value: [5000, 14000, 28000, 31000, 42000, 21000],
							name: '数据2',
						},
					],
				}, ],
			});
		})
	
	});
	const echartsData = () => {
		console.log(props.sixRingDateList[0], 7247812);
		 newArr.value = props.sixRingDateList.map((item) => {
			return [item.six01.维护情况, item.six02.周边配套, item.six03.区域潜力, item.six04.人均消费能力, item.six05.商业活力, item
				.six06.评估结果
			];
		})
		legend1.value = props.sixRingDateList[0].name
		legend2.value = props.sixRingDateList[1].name
		console.log(newArr.value, 72147812);
		updateEcharts()
	}
	const updateEcharts = () => {
		console.log(props.sixRingDateList.length, 77812);
		if (props.sixRingDateList.length > 0) {
			myChart.setOption({
				title: {
					text: '六角擂台',
					left: 'center',
				},
				legend: {
					data: [legend1.value,legend2.value],
				},
				radar: {
					indicator: [{
						name: '维护情况'
					}, {
						name: '周边配套'
					}, {
						name: '区域潜力'
					}, {
						name: '人均消费能力'
					}, {
						name: '商业活力'
					}, {
						name: '评估结果'
					}],
				},
				series: [{
					type: 'radar',
					data: [{
							value: newArr.value[0],
							name: legend1.value,
						},
						{
							value: newArr.value[1],
							name: legend2.value,
						},
					],
				}, ],
			});
		}
	}
	watch(() => props.sixRingDateList, (newVal, oldVal) => {
		echartsData()

	}, {
		deep: true
	});
</script>
<style scoped lang="less">
</style>