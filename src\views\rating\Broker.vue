<template>
	<!-- <el-card class="box-card">
		<el-row :gutter="20">
			<el-col :span="12">
				<el-text class="mx-1" tag="p" style="margin-bottom: 10px">经纪人</el-text>
				<el-image style="width: 100px; height: 100px" :src="url" fit="scale-down" />
			</el-col>
			<el-col :span="12">
				<el-text class="content mx-1" tag="p">{{ data.broker_name }}管家为您服务</el-text>
				<el-text class="content  mx-1" tag="p">毕业学校：{{ data.graduated_school }}</el-text>
				<el-text class="content mx-1" tag="p">服务商圈：{{ data.service_industry }}</el-text>
				<el-text class="content mx-1" tag="p">维护楼宇：{{ data.maintain_building }}</el-text>
				<el-text class="content mx-1" tag="p">房源情况：{{ data.house_condition }}</el-text>
				<el-button style="margin-top: 8vbgfpx" type="primary" round @click="handle">在线沟通</el-button>
			</el-col>
		</el-row>
	</el-card> -->
	<el-card class="box-card">
		<el-text class="mx-1 title" size="large" @click="handle">咨询地产经纪人</el-text>
		<el-row :gutter="20" style="margin: 15px 0">
			<el-col :span="6"> <el-avatar :size="80" fit="scale-down" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" /></el-col>
			<el-col :span="18">
				<el-text class="content" size="large" tag="p" type="success">{{ data.broker_name }}</el-text>
				<el-text class="content" tag="p">公司名暂无</el-text>
				<el-text class="content" tag="p">{{ data.maintain_building }}下xx套在售xx套在租</el-text>
			</el-col>
		</el-row>
		<el-input v-model="textarea" :rows="5" type="textarea" placeholder="请输入" @keyup.enter.stop="handleSend()" />
		<div style="margin-top: 10px">
			<el-button type="primary" plain @click="handleSend('您好，我想知道更多细节。')">您好，我想知道更多细节。</el-button>
			<el-button type="primary" plain @click="handleSend('您好，能看房吗？')">您好，能看房吗？</el-button>
			<el-button type="primary" plain @click="handleSend('您好，还有房源吗？')">您好，还有房源吗？</el-button>
		</div>
	</el-card>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import http from '@/utils/http';
import { useRoute, useRouter } from 'vue-router';
import { useWebSocket } from '@vueuse/core';
import { ElMessage } from 'element-plus';
const socket = useWebSocket(`ws://*************:8080/api/chat/1`);
const handleSend = (val) => {
	const message = {
		sendUser: '1',
		toUser: '2',
		message: val || textarea.value.trim(),
		isCurrentUser: true, // 标识消息是否为当前用户发送
	};
	if (val) {
		socket.send(JSON.stringify(message));
		return;
	}
	if (textarea.value.trim()) {
		socket.send(JSON.stringify(message));
		textarea.value = '';
	}
	ElMessage({
    showClose: true,
    message: '发送成功！',
    type: 'success',
  })
};
const data = ref([]);
const router = useRouter();
const route = useRoute();
const textarea = ref('');

const handle = () => {
	// debugger;
	router.push(`/main/chatrat/${route.params.id}`);
};
onUnmounted(() => {
	socket.close();
});
onMounted(async () => {
	const res = await http.get('api/a_maps.do?act=ratingPassDetailsAction', { params: { action: 'consultBroker', maps_id: route.params.id } });
	if (res && res.list.length > 0) {
		data.value = res.list[0];
	}
});
</script>
<style scoped lang="less">
.box-card {
	margin-top: 36px;
	border-radius: 20px;
	width: 567px;
	
}
.content {
		font-size: 14px;
		margin-top: 8px;
	}
.title {
	font-size: 20px;
	font-weight: 500;
}
</style>
