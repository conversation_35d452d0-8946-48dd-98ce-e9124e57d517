<template>
	<div class="tab_boxts">
		<div ref="echartsContainer1" id="main" style="width: 100%; height: 395px"></div>
	</div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
const myChart = ref(null);
const echartsContainer1 = ref(null);
const props = defineProps({
	valueDateList: {
		type: Array,
		default: 300,
	},
});

onMounted(() => {
	// myChart.value = echarts.init(echartsContainer1.value);
	// 设置图表选项
	// myChart.value.setOption({
	// 	color: ['#249eff', '#37e2e2'],
	// 	tooltip: {
	// 		trigger: 'axis',
	// 	},
	// 	legend: {
	// 		data: [{ name: legend1.value }, { name: legend2.value }],
	// 		// 点击图例，可以隐藏或显示对应的数据
	// 	},
	// 	xAxis: {
	// 		type: 'category',
	// 		data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
	// 	},
	// 	yAxis: [
	// 		{
	// 			type: 'value',
	// 			nameTextStyle: {
	// 				padding: [0, 40, 8, 0],
	// 			},
	// 			name: '单价（元/㎡）',
	// 			// 其他轴的配置...
	// 		},
	// 	],
	// 	series: [
	// 		{
	// 			// name: '数据1',
	// 			type: 'line',
	// 			yAxisIndex: 0, // 使用左侧纵轴
	// 			data: [],
	// 		},
	// 	],
	// });
});

const dealPrice1 = ref([]);
const dealPrice2 = ref([]);
const newarr = ref([]);
const echartsData = () => {
	newarr.value = [];
	dealPrice1.value = [];
	dealPrice2.value = [];
	if (props.valueDateList[0]) {
		props.valueDateList[0].dealPrice.map((item) => {
			dealPrice1.value.push((item.dealPrice / 10000).toFixed(2));
			newarr.value.push(item.month);
		});
		if (props.valueDateList.length > 1) {
			if (props.valueDateList[1]) {
				props.valueDateList[1].dealPrice.map((item) => {
					dealPrice2.value.push((item.dealPrice / 10000).toFixed(2));
				});
			} else {
				dealPrice2.value = [];
			}
		} else {
			dealPrice2.value = [];
		}
	} else {
		dealPrice1.value = [];
	}

	updateEcharts();
};

const updateEcharts = () => {
	if (props.valueDateList.length > 0) {
		const option = {
			grid: {
				left: '8%',
				top: '14%',
				right: '5%',
				bottom: '10%',
			},
			color: ['#249eff', '#37e2e2'],
			tooltip: {
				trigger: 'axis',
				show: true,
			},
			xAxis: {
				type: 'category',
				data: newarr.value,
			},
			yAxis: {
				type: 'value',
				name: '单价(万元/㎡)',
				splitLine: {
					show: true, // 虚拟线
					lineStyle: {
						color: '#E5E6EB',
						type: 'dashed',
					},
				},
			},
			series: [
				{
					name: props.valueDateList[0]?.buildingName,
					data: dealPrice1.value,
					type: 'line',
					smooth: true,
					symbol: 'circle', // 去掉数据点的圆圈
					label: {
						show: true,
					},
					lineStyle: {
						color: '#249eff', // 设置第一条折线的颜色
					},
				},
				{
					name: props.valueDateList[1]?.buildingName || '',
					data: dealPrice2.value,
					type: 'line',
					smooth: true,
					symbol: 'circle', // 去掉数据点的圆圈
					lineStyle: {
						color: '#37e2e2', // 设置第二条折线的颜色
					},
					label: {
						show: true,
					},
				},
			],
		};
		myChart.value = echarts.init(echartsContainer1.value);
		myChart.value.setOption(option);
	}
};
function init() {
	myChart.value = echarts.init(echartsContainer1.value);
	echartsData();
	nextTick(() => {
		updateEcharts();
	});
}
watch(
	() => props.valueDateList,
	(newVal, oldVal) => {
		echartsData();
	},
	{ deep: true }
);
defineExpose({
	init,
});
</script>
<style scoped lang="less">
// .tab_boxts {
// 	position: relative;
// 	> :nth-child(1) {
// 		width: 100% !important;
// 		> :nth-child(1) {
// 			width: 100% !important;
// 			> :nth-child(1) {
// 				width: 100% !important;
// 			}
// 		}
// 	}
// }
</style>
