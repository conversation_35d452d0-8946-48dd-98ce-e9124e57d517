<template>
	<div style="display: flex; justify-content: center">
		<el-table :data="data" style="width: 100%" :lazy="true" :class="classname" empty-text="暂无数据">
			<el-table-column label="三、退出期收支" align="center" label-class-name="fixed_header">
				<el-table-column
					resizable
					v-for="column in tableColumns"
					:key="column.prop"
					:prop="column.prop"
					:label="column.label"
					:width="column.width"
					label-class-name="fixed_header"
					show-overflow-tooltip
					style="height: 100px"
				>
					<template #default="scope">
						<div v-if="column.prop == 'assumptionStr'">
							<el-tooltip
								v-if="assumptionIs(scope.row.title)"
								effect="dark"
								content="输入0<x≤5.00的数值输入后点击左上角“保存并计算”重新计算数据"
								placement="left"
							>
								<el-input
									v-model="scope.row.assumptionStr"
									title=""
									placeholder="请输入"
									class="editNum"
									type="number"
									@input="assumption(scope.row, $event)"
								>
									<template #suffix> % </template>
								</el-input>
							</el-tooltip>
							<span v-else>{{ $utils.handleNumber(scope.row.assumptionStr) }}</span>
						</div>

						<div v-else style="width: 160px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
							{{ column.label === '计算（元）' ? $formattedMoney($utils.handleNumber(scope.row[column.prop])) : scope.row[column.prop] }}
						</div>
					</template>
				</el-table-column>
			</el-table-column>
		</el-table>
	</div>
</template>
<script setup>
import { defineProps, defineEmits } from 'vue';
import { ref } from 'vue';
const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
	classname: {
		type: String,
		default: [],
	},
});
const emit = defineEmits(['search']);

const tableColumns = [
	{ prop: 'title', label: '科目', width: '180px' },
	{ prop: 'calc', label: '计算（元）' },
	{ prop: 'assumptionStr', label: '假设（%）' },
];

const list = ref([{ name: '上市处置费用', key: 'listingDisposalFeeRate', value: '' }]);

const assumption = (val, event) => {
	list.value.forEach((item, index) => {
		if (item.name === val.title) {
			item.value = val.assumptionStr;
			emit('search', list.value[index], props.classname);
		}
	});
};
const assumptionIs = (val) => {
	if (list.value.filter((item) => item.name == val).length > 0) {
		return true;
	} else {
		return false;
	}
};
</script>
<style scoped lang="less">
::v-deep .fixed_header .cell {
	font-weight: 500;
	font-size: 14px;
	line-height: 22px;
	color: #1d2129;
}

::v-deep .fixed_header {
	height: 40px;
	background: #f7f8fa !important;
}

.el-table {
	--el-table-border-color: #e5e6eb;
	border-radius: 4px;
	.el-table__row {
		background: #fff !important;
	}
}
</style>
