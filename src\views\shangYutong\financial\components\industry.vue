<template>
	<div class="content_box">
		<div class="industryContainer">
			<div class="industryTitle">涨幅前十名的REITs基金</div>
			<arco-table :columns="tableColumns" :data="overviewData.tops" :pagination="false" :bordered="{ cell: true }">
				<template #empty>
					<div class="table_empty">暂无数据</div>
				</template>
				<template #columns>
					<arco-table-column v-for="(item, index) in tableColumns" :key="index" :data-index="item.dataIndex" :title="item.title" :width="item.width">
						<template #cell="scope">
							<div v-if="item.dataIndex == 'priceChange'" style="color: #f53f3f">
								{{ $utils.isEmpty(scope.record[item.dataIndex]) ? '-' : scope.record[item.dataIndex] }}
							</div>
							<div v-else class="columnsClass">{{ $utils.isEmpty(scope.record[item.dataIndex]) ? '-' : scope.record[item.dataIndex] }}</div>
						</template>
					</arco-table-column>
				</template>
			</arco-table>
		</div>

		<div class="echars_main">
			<div class="industryTitle">产品各分类市值占比</div>
			<div class="box_">
				<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
					<icon-download class="downIcon" />
				</div>
				<div style="width: 100%; height: 458px">
					<div class="title1">REITs产品各分类总市值构成占比</div>
					<div class="pie_wrap" v-if="overviewData?.reitsTotal?.length > 0">
						<echartPie ref="reitsTotalRef" :pieData="{ data: overviewData?.reitsTotal }" :otherOpts="reitsTotalColor"></echartPie>

						<div class="legend_wrap">
							<div class="legend_item" v-for="(item, index) in jiuyeLegend" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-show="overviewData?.reitsTotal?.length == 0">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>

			<div class="box_">
				<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
					<icon-download />
				</div>
				<div style="width: 100%; height: 458px">
					<div class="title1">ABS产品各分类总市值构成占比</div>
					<div class="pie_wrap" v-if="overviewData?.absTotal?.length > 0">
						<echartPie ref="absTotalRef" :pieData="{ data: overviewData?.absTotal }" :otherOpts="absTotalColor"></echartPie>
						<div class="legend_wrap" style="margin-left: -14px">
							<div class="legend_item" v-for="(item, index) in absTotalList" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
						<div class="legend_wrap">
							<div class="legend_item" v-for="(item, index) in absTotalList1" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-show="overviewData?.absTotal?.length == 0">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</div>

		<div class="echars_main">
			<div class="industryTitle">产品各分类数量占比</div>
			<div class="box_">
				<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
					<icon-download class="downIcon" />
				</div>
				<div style="width: 100%; height: 458px">
					<div class="title1">REITs产品各分类数量构成占比</div>
					<div class="pie_wrap" v-show="overviewData?.reitsCount?.length > 0">
						<echartPie ref="reitsCountRef" :pieData="{ data: overviewData?.reitsCount }" :otherOpts="reitsCountColor"></echartPie>

						<div class="legend_wrap">
							<div class="legend_item" v-for="(item, index) in jiuyeLegend" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-show="overviewData?.reitsCount?.length == 0">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>

			<div class="box_">
				<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
					<icon-download />
				</div>
				<div style="width: 100%; height: 458px">
					<div class="title1">ABS产品各分类数量构成占比</div>
					<div class="pie_wrap" v-show="overviewData?.absCount?.length > 0">
						<echartPie ref="absCountRef" :pieData="{ data: overviewData?.absCount }" :otherOpts="absTotalColor"></echartPie>

						<div class="legend_wrap" style="margin-left: -14px">
							<div class="legend_item" v-for="(item, index) in absTotalList" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>

						<div class="legend_wrap">
							<div class="legend_item" v-for="(item, index) in absTotalList1" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-show="overviewData?.absCount?.length == 0">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</div>

		<div class="echars_main">
			<div class="industryTitle">其他行业信息(截至2025年2月)</div>
			<div class="box_">
				<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
					<icon-download class="downIcon" />
				</div>
				<div style="width: 100%; height: 458px">
					<div class="title1">我国上市公募REITs类别</div>
					<div class="pie_wrap" v-show="overviewData?.reitsType?.length > 0">
						<echartPie ref="reitsTypeRef" :pieData="{ data: overviewData?.reitsType }" :otherOpts="reitsTypeColor"></echartPie>

						<div class="legend_wrap">
							<div class="legend_item" v-for="(item, index) in reitsTypeList" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
						<div class="legend_wrap" style="margin-left: -42px">
							<div class="legend_item" v-for="(item, index) in reitsTypeList1" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-show="overviewData?.reitsType?.length == 0">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>

			<div class="box_">
				<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
					<icon-download />
				</div>
				<div style="width: 100%; height: 458px">
					<div class="title1">美国上市REITs市值按底层资产类型分布</div>
					<div class="pie_wrap" v-show="overviewData?.reitsAssetType?.length > 0">
						<echartPie ref="reitsAssetTypeRef" :pieData="{ data: overviewData?.reitsAssetType }" :otherOpts="reitsAssetTypeColor"></echartPie>

						<div class="legend_wrap">
							<div class="legend_item" v-for="(item, index) in reitsAssetTypeList" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
						<div class="legend_wrap" style="margin-left: 19px">
							<div class="legend_item" v-for="(item, index) in reitsAssetTypeList1" :key="index">
								<div class="circle" :style="{ backgroundColor: item.color }"></div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-show="overviewData?.reitsAssetType?.length == 0">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>

			<div class="box_ box_Container">
				<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
					<icon-download />
				</div>
				<div style="width: 100%; height: 458px">
					<div class="title1">全球主要REITs市场市值(亿美元)和公募REITs数量(个)</div>
					<div class="pie_wrap" style="padding: 0px" v-show="overviewData?.reitsAssetAndCount?.length > 0">
						<div id="myChart1" v-show="overviewData?.reitsAssetAndCount?.length > 0" class="charts"></div>
					</div>
					<div class="empty_wrap" v-show="overviewData?.reitsAssetAndCount?.length == 0">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { onMounted, ref, nextTick } from 'vue';
import { realEstateFinance } from '@/api/finance.js';
import echartPie from '@/views/shangYutong/materials_new/components/echart/pie.vue';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
const tableColumns = [
	{
		title: '产品名称',
		width: 324,
		dataIndex: 'name',
	},
	{
		title: '发行日期',
		dataIndex: 'establishmentDate',
	},
  {
    title: '产品分类',
    dataIndex: 'assetCategory',
  },
	{
		title: '发行价格(元)',
		dataIndex: 'issuePrice',
	},
  {
    title: '当前价格(元)',
    dataIndex: 'closingPrice',
  },
	{
		title: '涨跌幅',
		dataIndex: 'priceChange',
	},
];

const jiuyeLegend = [
	{
		color: '#378EFF',
		name: '保障性租赁住房',
	},
	{
		color: '#37B7FF',
		name: '仓储物流',
	},
	{
		color: '#39DDE8',
		name: '产业园区',
	},
	{
		color: '#2FE2AC',
		name: '消费基础设施',
	},
];

const absTotalList = [
	{
		color: '#378EFF',
		name: '办公物业',
	},
	{
		color: '#37B7FF',
		name: '零售物业',
	},
	{
		color: '#39DDE8',
		name: '混合',
	},
	{
		color: '#2FE2AC',
		name: '酒店',
	},
];
const absTotalList1 = [
	{
		color: '#63DC6F',
		name: '物流仓储',
	},
	{
		color: '#ADE369',
		name: '混合类',
	},
	{
		color: '#FFCF63',
		name: '公寓',
	},
	{
		color: '#FF9A2E',
		name: '基础设施',
	},
];

const reitsTypeList = [
	{
		color: '#378EFF',
		name: '保障性租赁住房',
	},
	{
		color: '#37B7FF',
		name: '仓储物流',
	},
	{
		color: '#39DDE8',
		name: '园区基础设施',
	},
	{
		color: '#2FE2AC',
		name: '交通基础设施',
	},
];

const reitsTypeList1 = [
	{
		color: '#63DC81',
		name: '消费基础设施',
	},
	{
		color: '#ADE369',
		name: '水利设施',
	},
	{
		color: '#FFCF63',
		name: '能源基础设施',
	},
	{
		color: '#FF9A2E',
		name: '生态环保',
	},
];

const reitsAssetTypeList = [
	{
		color: '#378EFF',
		name: '多元资产',
	},
	{
		color: '#37B7FF',
		name: '基础设施',
	},
	{
		color: '#39DDE8',
		name: '公寓',
	},
	{
		color: '#2FE2AC',
		name: '林木',
	},
	{
		color: '#63DC6F',
		name: '零售',
	},
	{
		color: '#ADE369',
		name: '医疗',
	},
	{
		color: '#FFCF63',
		name: '工业',
	},
];

const reitsAssetTypeList1 = [
	{
		color: '#FF9A2E',
		name: '自助仓储',
	},
	{
		color: '#F76060',
		name: '酒店与度假村',
	},
	{
		color: '#F088CA',
		name: '按揭贷款',
	},
	{
		color: '#B380F4',
		name: '数据中心',
	},
	{
		color: '#9EA7FF',
		name: '办公',
	},
	{
		color: '#637BFA',
		name: '其他',
	},
];
let myChart1;
const overviewData = ref({});

const reitsTotalColor = ref({ color: ['#39DDE8', '#2FE2AC', '#37B7FF', '#378EFF'] });
const reitsCountColor = ref({ color: ['#378EFF', '#39DDE8', '#37B7FF', '#2FE2AC'] });
const absTotalColor = ref({ color: ['#ADE369', '#37B7FF', '#FF9A2E', '#378EFF', '#2FE2AC', '#FFCF63', '#63DC6F', '#39DDE8'] });

const reitsTypeColor = ref({
	color: ['#378EFF', '#37B7FF', '#39DDE8', '#2FE2AC', '#63DC81', '#ADE369', '#FFCF63', '#FF9A2E'],
});

const reitsAssetTypeColor = ref({
	color: [
		'#378EFF',
		'#37B7FF',
		'#39DDE8',
		'#63DC6F',
		'#FFCF63',
		'#F76060',
		'#B380F4',
		'#637BFA',
		'#2FE2AC',
		'#ADE369',
		'#FF9A2E',
		'#F088CA',
		'#9EA7FF',
	],
});
const reitsTotalRef = ref();
const absTotalRef = ref();
const reitsCountRef = ref();
const absCountRef = ref();
const reitsTypeRef = ref();
const reitsAssetTypeRef = ref();

onMounted(() => {
	handleRealEstateFinance();
});

function handleRealEstateFinance() {
	realEstateFinance()
		.then((res) => {
			res.data.reitsTotal.forEach((element) => {
				element.value = Number(element.value.slice(0, element.value.length - 1));
			});

			res.data.absTotal.forEach((element) => {
				element.value = Number(element.value.slice(0, element.value.length - 1));
			});

			res.data.reitsAssetType.forEach((element) => {
				element.value = Number(element.value.slice(0, element.value.length - 1));
			});

			overviewData.value = res.data;
			let yAxisList = [];
			let barList = [];
			let lineList = [];

			res.data.reitsAssetAndCount.forEach((element) => {
				yAxisList.push(element.name);
				barList.push(element.value);
				lineList.push(element.count);
			});
			handlereitsAssetAnd(yAxisList, barList, lineList);
			nextTick(() => {
				reitsTotalRef.value.init();
				absTotalRef.value.init();
				reitsCountRef.value.init();
				absCountRef.value.init();
				reitsTypeRef.value.init();
				reitsAssetTypeRef.value.init();
			});
		})
		.catch((err) => {
			console.log(err);
		});
}

// 柱状图
function handlereitsAssetAnd(yAxisList, barList, lineList) {
	myChart1 = echarts.init(document.getElementById('myChart1'));
	myChart1.resize({
		width: 1616,
		height: 430,
	});
	myChart1.setOption({
		color: ['#00B2FF', '#37E2E2'],
		grid: {
			left: '3%', // 左侧留白
			right: '3%', // 右侧留白，增加这个值可以让右侧Y轴更靠近右侧边缘
			bottom: '9%', // 下方留白
			containLabel: true, // 保证坐标轴刻度标签等内容不被遮挡
		},
		tooltip: {
			trigger: 'axis',
		},
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(244, 247, 252, .8)',
			borderColor: 'transparent',
			borderRadius: 4,
			formatter: function (params) {
				let str = '';
				params.forEach((item, index) => {
					str += `<div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
				<div style="width:8px;height:8px;background-color:${item.color};margin-right: 8px;border-radius: 50%;">
				</div>
				<div style="width:100%;display:flex;align-items: center;">
          <div style="font-size: 14px; color: #1D2129; font-weight: 500;margin-top:1px;">
          ${item.value}
            </div>
				<div style="font-size: 14px; color: #1D2129; font-weight: 500">
					${index === 0 ? '市值(亿美元)' : '数量(个)'}
				</div>
				</div>
				</div>`;
				});
				return `
			<div style="font-size: 14px; color: #1D2129; font-weight: 600;margin-bottom: 4px">${params[0].name}</div>
			<div style="display: flex;
			flex-direction: column;
			gap: 4px;">
			${str}
			</div>
			`;
				// return `
				// <div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
				//   <div style="width:8px;height:8px;background-color:#249EFF;margin-right: 8px;border-radius: 4px;">
				//   </div>
				//   <div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
				//    ${params[0].name}
				//   </div>
				//   <div style="font-size: 14px; color: #1D2129; font-weight: 500">
				//   ${params[0].value}市值(亿美元)
				//   </div>
				// </div>
				// `;
			},
		},
		xAxis: {
			type: 'category',
			boundaryGap: true,
			data: yAxisList,
			axisLabel: {
				textStyle: {
					color: '#86909C', //坐标值得具体的颜色
				},
				interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
			},
			axisLine: {
				//x轴线的颜色以及宽度
				show: true,
				lineStyle: {
					color: '#C9CDD4',
					width: 1,
					type: 'solid',
				},
			},
			axisTick: {
				show: true,
				length: 4,
				lineStyle: {
					color: '#C9CDD4',
					type: 'solid',
					width: 1,
				},
				alignWithLabel: true,
			},
		},
		yAxis: [
			{
				alignTicks: true,
				type: 'value',
				name: '市值(亿美元)',
				splitLine: {
					show: true, // 虚拟线
					lineStyle: {
						color: '#E5E6EB',
						type: 'dashed',
					},
				},
				nameTextStyle: {
					padding: [0, 22, 0, 0],
				},
			},
			{
				alignTicks: true,
				splitLine: {
					show: true, // 虚拟线
					lineStyle: {
						color: '#E5E6EB',
						type: 'dashed',
					},
				},
				type: 'value',
				name: '数量(个)',
				nameTextStyle: {
					padding: [0, 0, 0, 16],
				},
			},
		],
		series: [
			{
				barWidth: 24,
				type: 'bar',
				data: barList,
				itemStyle: {
					borderRadius: [4, 4, 0, 0], // 设置四个圆角的半径，顺序为左上、右上、右下、左下
				},
			},
			{
				// emphasis: {
				// 	focus: 'series',
				// 	itemStyle: { showSymbol: true, color: '#33D1C9', symbolSize: 16, borderColor: '#D3ECFF', borderWidth: 2 },
				// },
				type: 'line',
				yAxisIndex: 1,
				data: lineList,
			},
		],
	});
}

function handleParams(params) {
	console.log(params, 'params');
}
</script>
<style lang="scss" scoped>
.content_box {
	height: 100%;
}

.industryTitle {
	width: 100%;
	height: 60px;
	font-weight: 500;
	font-size: 20px;
	line-height: 60px;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	color: #1d2129;
	&::before {
		content: '';
		width: 5px;
		height: 14px;
		background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
		border-radius: 10px;
		margin-right: 8px;
	}
}

.industryContainer {
	background-color: rgba(255, 255, 255, 1);
	width: 100%;
	height: 100%;
	padding: 4px 16px 20px 16px;
	box-sizing: border-box;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
	border-bottom-left-radius: 4px;
	::v-deep .arco-table-container {
		border-radius: 4px !important;
		.arco-table-body {
			border-radius: 4px !important;
		}
		.arco-table-content {
			border-radius: 4px !important;
			tbody > :nth-last-child(1) > :nth-last-child(1) {
				border-bottom-right-radius: 4px !important;
			}
			tbody > :nth-last-child(1) > :nth-child(1) {
				border-bottom-left-radius: 4px !important;
			}
		}
	}
	.table_empty {
		width: 100%;
		display: flex;
		justify-content: center;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #86909c;
	}
	.columnsClass {
		color: #4e5969;
	}
}

.echars_main {
	margin-top: 16px;
	border-radius: 4px;
	background-color: rgba(255, 255, 255, 1);
	width: 100%;
	height: 100%;
	padding: 4px 16px 0px 16px;
	box-sizing: border-box;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	.box_Container {
		width: 100% !important;
	}
	.box_ {
		width: 49.5%;
		height: 472px;
		margin-bottom: 15px;
		border: 1px solid rgba(231, 231, 231, 1);
		border-radius: 6px;
		box-sizing: border-box;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: column;
		position: relative;
		.download {
			position: absolute;
			top: 12px;
			right: 20px;
			cursor: pointer;
			color: #333333;
			font-size: 20px;
			font-weight: 600;
			&:hover {
				color: #1868f1;
			}
			::v-deep .arco-icon {
				font-size: 18px;
				stroke-width: 3px;
				color: #1868f1;
			}
		}
		.title1 {
			padding-left: 20px;
			width: calc(100% - 20px);
			height: 47px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			font-weight: 500;
			font-size: 16px;
			color: #1d2129;
			background-color: #f7f8fa;
			border-bottom: 1px solid #e5e6eb;
		}
	}
}

// .charts {
// 	height: 458px;
// 	> :nth-child(1) {
// 		width: 100% !important;
// 		> :nth-child(1) {
// 			width: 100% !important;
// 		}
// 	}
// }

.empty_wrap {
	height: 410px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 400;
	color: #86909c;
	img {
		width: 80px;
		height: 80px;
	}
}

.pie_wrap {
	padding-top: 38px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	.pie_box {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		> :nth-child(n) {
			display: flex;
			justify-content: center;
		}
	}
	.legend_wrap {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 12px;
		gap: 12px 20px;
		.legend_item {
			display: flex;
			align-items: center;
			gap: 8px;
			.circle {
				width: 8px;
				height: 8px;
				border-radius: 50%;
			}
			.name {
				font-size: 14px;
				font-weight: 600;
				line-height: 22px;
				color: #4e5969;
			}
		}
	}
}
</style>
