<template>
	<div class="credit_risks">
		<div style="background: #fff; padding-bottom: 16px; border-radius: 4px">
			<div style="display: flex; justify-content: space-between; align-items: center; background: #fff">
				<div class="card_box">
					<div class="mainTitle1">风险测评</div>
				</div>

				<div class="right_top">
					<div class="title_btn" @click="handleDownLoadRisk">下载风险评估报告<img src="@/assets/download.png" alt="" /></div>
				</div>
			</div>

			<div class="container_box add-building">
				<div class="table">
					<div class="tag_box_details">添加楼宇进行计算</div>
					<div class="tag_box_Table">
						<div class="operate">
							<div v-if="!props.riskType">
								<arco-button @click="onAdd" class="add" :disabled="buildingIndex != 0" style="background: #1868f1; color: #fff; border-radius: 4px"
									>添加</arco-button
								>
								<!-- <arco-button
									:style="{
										marginLeft: '8px',
										borderRadius: '4px',
										background: multipleSelection.length !== 0 ? '#F2F3F5' : '#E8F3FF',
										color: multipleSelection.length !== 0 ? '#C9CDD4' : '#1868F1',
									}"
									@click="openDialog"
									:disabled="multipleSelection.length !== 0"
									>新建</arco-button
								> -->
							</div>
							<el-button type="primary" class="btn_box active" color="#1868F1" @click="submit1()">开始计算</el-button>
						</div>
						<arco-table
							row-key="id"
							:scroll="{ y: 240 }"
							:data="buildingList"
							ref="buildingTable"
							:pagination="false"
							:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
						>
							<template #columns>
								<arco-table-column title="序号" ellipsis tooltip :width="65">
									<template #cell="scope">
										{{ scope.rowIndex + 1 }}
									</template>
								</arco-table-column>
								<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip></arco-table-column>
								<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip></arco-table-column>
								<arco-table-column title="城市" data-index="city" ellipsis tooltip></arco-table-column>
								<arco-table-column title="购买面积比例" ellipsis tooltip>
									<template #cell="scope">
										<arco-select
											v-model="scope.record.proportion"
											ref="proportion"
											size="small"
											class="proportionColumn"
											style="width: 100%"
											placeholder="0"
										>
											<arco-option
												:style="{ color: item1.price === scope.record.proportion ? '#1868F1' : '' }"
												v-for="(item1, index1) in scope.record.proportion_options"
												:key="index1"
												:label="item1.label"
												:value="item1.price"
											/>
										</arco-select>
									</template>
								</arco-table-column>

								<arco-table-column title="购买面积" ellipsis tooltip>
									<template #cell="scope">
										{{ scope.record.proportion ? (Number(scope.record.buildingSize) * scope.record.proportion).toFixed(2) + '㎡' : '' }}
									</template>
								</arco-table-column>

								<arco-table-column title="操作" ellipsis tooltip :width="160">
									<template #cell="scope">
										<div class="deleteIconCon" @click="handleDelete(scope.rowIndex)">
											<div class="tableDeleteIcon">
												<img src="@/assets/tableDeleteIcon.png" alt="" />
											</div>
											<div class="tableDelete">删除</div>
										</div>
									</template>
								</arco-table-column>
							</template>

							<template #empty>
								<div class="empty_wrap">
									<img src="@/assets/images/shangYutong/buildInfo/empty.png" />
									<div>暂无数据</div>
								</div>
							</template>
						</arco-table>
					</div>
				</div>
			</div>
			<div class="details-top-container">
				<div class="left_box">
					<div class="container_box building-details">
						<div class="tag_box_details">楼宇信息</div>
						<arco-carousel
							indicator-type="never"
							v-if="activeData?.creditRiskAssetVoList?.length > 0"
							:style="{
								width: '800',
								height: '240px',
							}"
						>
							<arco-carousel-item v-for="item in activeData?.creditRiskAssetVoList" :key="item">
								<div class="slideshow">
									<div class="boxss">
										<div class="head-left-details">
											<img v-if="item.imgUrlList[0]" :src="`${proxyAddress}${item.imgUrlList[0]}`" alt="" />
											<img v-else src="@/assets/images/risks/buildingImg.png" alt="" />

											<div class="head-right_details">
												<div class="right_details">
													<div class="top_tent">
														<div class="top_text">{{ item.name }}</div>
														<div class="topt_text" :style="handlebuildingRateColor(item.assetRatings)">{{ item.assetRatings }}级</div>
														<div class="topth_text">{{ item.assetType }}</div>
													</div>
													<div class="center_tent">
														<div class="center_tent_left">
															<el-icon><Location /></el-icon>
															<div class="center_tent_left_text">{{ item.address }}</div>
														</div>
													</div>
												</div>

												<div class="items_box_border">
													<div class="index_left_item">
														<div class="value">{{ item.buildYear }}</div>
														<div class="title">建成年份</div>
														<div class="lingIndex"></div>
													</div>
													<div class="index_left_item">
														<div class="value" :style="handleKeyValue(item.maintenance, '维护情况')">{{ item.maintenance }}</div>
														<div class="title">维护情况</div>
														<div class="lingIndex"></div>
													</div>
													<div class="index_left_item">
														<div class="value" :style="handleKeyValue(item.regionalPotential, '区域潜力')">{{ item.regionalPotential }}</div>
														<div class="title">区域潜力</div>
													</div>
													<div class="index_left_item">
														<div class="value" :style="handleKeyValue(item.businessDynamism, '商业活力')">{{ item.businessDynamism }}</div>
														<div class="title">商业活力</div>
														<div class="lingIndex"></div>
													</div>
													<div class="index_left_item">
														<div class="value">{{ $formattedMoney(item.spendingPower) }}</div>
														<div class="title">人均消费(元)</div>
														<div class="lingIndex"></div>
													</div>
													<div class="index_left_item">
														<div class="value">{{ item.presentValueOfAsset ? parseFloat(item.presentValueOfAsset).toFixed(2) : 0 }}</div>
														<div class="title">估值(亿)</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</arco-carousel-item>
						</arco-carousel>
						<div style="color: #909399; font-size: 12px; display: flex; height: 240px; align-items: center; justify-content: center" v-else>
							<div class="empty_wrap">
								<img src="@/assets/images/shangYutong/buildInfo/empty.png" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>

					<div class="right-top-container">
						<div class="tag_box_details">宏观利率与政策核心指标</div>
						<div class="card_boxss">
							<div class="card-details-box" :class="`card_details_box${index}`" v-for="(item, index) in finDataList" :key="index">
								<img class="details_boxImg" :src="item.img" alt="" />
								<div class="details_box">
									<div class="value">{{ $utils.handleNumber(item.value) }}</div>
									<div class="topRight_bf">%</div>
								</div>
								<div class="name">{{ item.name }}</div>
							</div>
						</div>
					</div>
				</div>
				<div class="right_box">
					<div class="container_box">
						<div class="card_boxs">
							<div class="tag_box_details">权益指数</div>
							<div class="left_box">
								<div class="left_item">
									<div class="item_qy">
										<div class="item_qy_title">市场法价格</div>
										<div class="item_qy_value">{{ activeData?.marketPriceIndex ? $formattedMoney(activeData?.marketPriceIndex) : '0.00' }}万元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">收益法价格</div>
										<div class="item_qy_value">{{ activeData?.incomePriceIndex ? $formattedMoney(activeData?.incomePriceIndex) : '0.00' }}万元</div>
									</div>

									<div class="item_qy">
										<div class="item_qy_title">市场法比例</div>
										<arco-select
											v-model="proportionValue"
											size="medium"
											@change="onChangeProportion"
											style="width: 160px"
											placeholder="请选择市场法比例"
										>
											<arco-option
												:style="{ color: item.price === proportionValue2 ? '#1868F1' : '' }"
												v-for="item in options"
												:key="item.value"
												:label="item.label"
												:value="item.price"
											/>
										</arco-select>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">收益法比例</div>
										<arco-select
											v-model="proportionValue2"
											size="medium"
											@change="onChangeProportion2"
											style="width: 160px"
											placeholder="请选择收益法比例"
										>
											<arco-option
												:style="{ color: item.price === proportionValue2 ? '#1868F1' : '' }"
												v-for="item in options"
												:key="item.value"
												:label="item.label"
												:value="item.price"
											/>
										</arco-select>
									</div>

									<div class="item_qy">
										<div class="item_qy_title">评估值</div>
										<div>{{ activeData?.presentValueOfAsset ? activeData?.presentValueOfAsset : '0.00' }}亿元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">折旧</div>
										<div>{{ activeData?.depreciation ? $formattedMoney(activeData?.depreciation) : '0.00' }}万元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">EBITDA</div>
										<div>{{ activeData?.ebitda ? $formattedMoney(activeData?.ebitda) : '0.00' }}万元</div>
									</div>
								</div>
								<div class="left_items">
									<div ref="echartsContainer" style="width: 100%; height: 100%"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="details_bottom_container">
				<div class="tag_box_details">风险评估核心指标</div>
				<div class="details-bottom-container" v-if="activeData?.creditRiskAssetVoList?.length > 0">
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.salePrice ? $formattedMoney(activeData?.salePrice) : 0 }}</div>
							<div class="name">价格（万元/㎡）</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price.png" alt="" />
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.icr || 0 }}</div>
							<div class="name">利息覆盖倍数ICR</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price1.png" alt="" />
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.incomeGrowthRate || 0 }}%</div>
							<div class="name">收入/租金增长率</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price2.png" alt="" />
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.capitalizationRate || 0 }}%</div>
							<div class="name">资本化率</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price3.png" alt="" />
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.ltv || 0 }}%</div>
							<div class="name">抵押率（LTV）</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price4.png" alt="" />
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.defaultRate || 0 }}%</div>
							<div class="name">违约率（租户）</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price5.png" alt="" />
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.rentSaleRatio || 0 }}%</div>
							<div class="name">租售价格比</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price6.png" alt="" />
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value" style="font-weight: 500">
								{{ handleoperations(activeData?.operationalRisk) || 0 }}
								<img
									v-if="
										(0 < activeData?.operationalRisk && activeData?.operationalRisk <= 3) ||
										(4.5 < activeData?.operationalRisk && activeData?.operationalRisk <= 6) ||
										(8 < activeData?.operationalRisk && activeData?.operationalRisk <= 9)
									"
									src="@/assets/arrowtopimg.png"
									class="arrowtopimg"
									alt=""
								/>
								<img
									v-if="
										(3 < activeData?.operationalRisk && activeData?.operationalRisk <= 4.5) ||
										(6 < activeData?.operationalRisk && activeData?.operationalRisk <= 8) ||
										(9 < activeData?.operationalRisk && activeData?.operationalRisk <= 10)
									"
									src="@/assets/arrowtopimg1.png"
									class="arrowtopimg"
									alt=""
								/>
							</div>
							<div class="name">运营风险</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price7.png" alt="" />
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.presentValueOfAsset ? $formattedMoney(activeData?.presentValueOfAsset) : 0 }}</div>
							<div class="name">资产现值（亿元）</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price8.png" alt="" />
						</div>
					</div>
					<div class="card-details-boxs tax-v2">
						<div class="card_details">
							<div class="details_box">
								<div class="value">{{ activeData?.transactionCost ? $formattedMoney(activeData?.transactionCost) : 0 }}</div>
								<div class="name">交易成本（万元）</div>
							</div>
							<div class="line"></div>
						</div>
						<div class="details_box_fee">
							<div class="box_fee">
								<div class="box_fee_circle">
									<div class="box_fee_circle_img">
										<img src="@/assets/infoCircle.png" alt="" />
									</div>
									<div class="box_fee_circle_si">税(万元)</div>
									<div class="tax_feetax">
										{{ activeData?.tax ? $formattedMoney(activeData?.tax) : 0 }}
									</div>
								</div>

								<div class="box_fee_circleFx">
									<div class="box_fee_title">费(万元)</div>
									<div class="box_fee_value">
										{{ activeData?.fee ? $formattedMoney(activeData?.fee) : 0 }}
									</div>
								</div>
							</div>
							<div class="details_boxImg">
								<img src="@/assets/images/risks/price9.png" alt="" />
							</div>
						</div>
					</div>
					<div class="card-details-box">
						<div class="details_box">
							<div class="value">{{ activeData?.investmentAmount ? $formattedMoney(activeData?.investmentAmount) : 0 }}</div>
							<div class="name">总投资额(亿元)</div>
						</div>
						<div class="details_boxImg">
							<img src="@/assets/images/risks/price10.png" alt="" />
						</div>
					</div>
				</div>
				<div class="details-bottom-container" v-else style="justify-content: center">
					<div class="empty_wrap">
						<img src="@/assets/images/shangYutong/buildInfo/empty.png" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</div>

		<div class="table-counter-container">
			<div class="container_box" style="padding: 20px 16px !important">
				<div class="mainTitle1" style="margin: 0 !important">现金流/证券化/Pre-Reits基金计算器</div>
				<div class="form-box">
					<div class="financing-interest">
						<div class="interest_name">融资比例</div>
						<el-tooltip effect="dark" placement="top" content="输入0≤x≤100的数值">
							<arco-input-number :style="{ width: '180px' }" @change="loanRatioChange" v-model="loanRatio" placeholder="请输入百分比" hide-button>
								<template #suffix> % </template>
							</arco-input-number>
						</el-tooltip>
					</div>

					<div class="financing-limit">
						<div class="financing_limitTitle">融资额度</div>
						<div class="financing_limitValue">
							{{ activeData?.creditRiskInvestVo?.financingLimit ? activeData?.creditRiskInvestVo?.financingLimit * (loanRatio / 100) : 0 }} 亿元
						</div>
					</div>
					<div class="financing-interest">
						<div class="interest_name">融资利率</div>
						<el-tooltip effect="dark" placement="top" content="输入2.25≤x≤10的数值">
							<arco-input-number :style="{ width: '180px' }" v-model="interestRate" placeholder="请输入百分比" hide-button>
								<template #suffix> % </template>
							</arco-input-number>
						</el-tooltip>
					</div>
					<div class="start-count"><el-button type="primary" color="#1868F1" @click="handleChangeRate">开始计算</el-button></div>

					<div class="download" @click="handleDownload" v-if="activeData?.creditRiskInvestVo?.cashFlows?.length > 0">
						下载报表<img src="@/assets/download.png" alt="" />
					</div>
				</div>

				<arco-tabs v-model="activeName" @change="handleChangeTabs">
					<arco-tab-pane key="first">
						<template #title>
							<div class="financeImg">
								<img v-if="activeName == 'first'" src="@/assets/images/risks/financeActive.png" alt="" />
								<img v-if="activeName !== 'first'" src="@/assets/images/risks/finance.png" alt="" />
								<div>现金流</div>
							</div>
						</template>
						<div class="headContent">
							<div class="financing-interest">
								<div class="interest_name">假设租金增长率</div>
								<arco-input-number
									:style="{ width: '180px' }"
									@input="handleRentalInput($event, 'rentalGrowthRate')"
									v-model="rentalGrowthRate"
									placeholder="请输入百分比"
									hide-button
								>
									<template #suffix> % </template>
								</arco-input-number>
							</div>
							<div class="financing-interest">
								<div class="interest_name">假设支出变化</div>
								<arco-input-number
									:style="{ width: '180px' }"
									@input="handleRentalInput($event, 'expenditureChange')"
									v-model="expenditureChange"
									placeholder="请输入百分比"
									hide-button
								>
									<template #suffix> % </template>
								</arco-input-number>
							</div>
							<div class="Btn">
								<el-button type="primary" color="#1868F1" @click="handleChangeRental">计算</el-button>
							</div>
						</div>
						<div class="table" style="margin: 0 20px 0px 0">
							<arco-table
								:pagination="false"
								:data="activeData?.creditRiskInvestVo?.cashFlows"
								:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
							>
								<template #columns>
									<arco-table-column
										v-for="column in cashFlowTable"
										:key="column.prop"
										:dataIndex="column.prop"
										:title="column.label"
										:width="column.width"
									>
										<template #cell="scope">
											{{ formatterAmount(scope) }}
										</template>
									</arco-table-column>
								</template>
								<template #empty>
									<div class="empty_wrap">
										<img src="@/assets/images/shangYutong/buildInfo/empty.png" />
										<div>暂无数据</div>
									</div>
								</template>
							</arco-table>
						</div>
					</arco-tab-pane>
					<arco-tab-pane key="second">
						<template #title>
							<div class="financeImg">
								<img v-if="activeName == 'second'" src="@/assets/images/risks/align_right_oneActive.png" alt="" />
								<img v-if="activeName !== 'second'" src="@/assets/images/risks/align_right_one.png" alt="" />
								<div>证券化</div>
							</div>
						</template>
						<!-- <div style="display: flex; justify-content: flex-end; margin-right: 24px" v-if="securedData.length > 0">
							<el-button type="primary" @click="handleSaveSecurity">保存并计算</el-button>
						</div> -->

						<div class="securitzation" v-if="activeData?.creditRiskInvestVo?.securitization">
							<div class="left">
								<div class="title">有担保</div>
								<div class="securitzation_content">
									<div class="classnames">
										<securitzation :classname="'have'" :data="activeData?.creditRiskInvestVo?.securitization?.secured?.types"></securitzation>
									</div>

									<div class="table-container classnames">
										<div class="table-title">综合成本率测算</div>
										<template v-for="(item, index) in activeData?.creditRiskInvestVo?.securitization?.secured" :key="index">
											<div class="details-box" v-if="costRate[index]">
												<div class="name">{{ costRate[index] }}</div>
												<div class="value">
													{{
														costRate[index].includes('率')
															? $formattedMoney($utils.handleNumber(item * 100)) + '%'
															: $formattedMoney($utils.handleNumber(item)) + '万元'
													}}
												</div>
											</div>
										</template>
									</div>

									<template v-if="activeData?.creditRiskInvestVo?.securitization?.secured?.netCashFlows?.length > 0">
										<div class="table-container classnames">
											<div class="table-title">净现金流测算(万元)</div>
											<div style="display: flex; justify-content: space-around">
												<div
													:class="`active-${index}`"
													v-for="(item, index) in activeData?.creditRiskInvestVo?.securitization?.secured?.netCashFlows"
													:key="index"
												>
													<div class="table-title" style="background: #f7f8fa; overflow: auto; border-radius: 0px">{{ item.title }}</div>
													<template v-for="(ite, key) of item" :key="key">
														<div class="details-box" v-if="flowCalculation[key]">
															<div class="name">{{ flowCalculation[key] }}</div>
															<div class="value" style="margin-left: 5px">{{ $formattedMoney($utils.handleNumber(ite)) }}</div>
														</div>
													</template>
												</div>
											</div>
										</div>
									</template>

									<div class="table-container">
										<div class="table-title">测算前提</div>
										<div class="details-box" v-for="(item, index) in preSecuredData" :key="index" v-show="item.label">
											<div class="name">{{ item.label }}</div>
											<div class="value" v-if="!item.type">
												{{ item.value }}
												{{
													item.label === 'EBITDA' || item.label === '评估值'
														? '亿'
														: item.label === '承销费' || item.label === '计划管理费'
														? '万元'
														: ''
												}}
											</div>
											<div class="value" v-else>{{ item.value + '%' }}</div>

											<!--<el-tooltip v-else effect="light" content="输入后点击右上角“保存并计算”重新计算数据">
											<el-input class="boxItemTooltip" v-model="item.value" placeholder="请输入" style="width: 100px"
												><template #append>%</template>
											</el-input>
										</el-tooltip> -->
										</div>
									</div>

									<!-- <div class="classnames">
										<myTable2 :classname="'have'" :data="activeData?.creditRiskInvestVo?.securitization?.secured?.types.slice(0, 3)"></myTable2>
									</div> -->
								</div>
							</div>
							<div class="right">
								<div class="title">无担保</div>
								<div class="securitzation_contents">
									<div class="classnames">
										<securitzation :classname="'nothing'" :data="activeData?.creditRiskInvestVo?.securitization?.unsecured?.types"></securitzation>
									</div>

									<div class="table-container classnames">
										<div class="table-title">综合成本率测算</div>
										<template v-for="(item, index) in activeData?.creditRiskInvestVo?.securitization?.unsecured" :key="index">
											<div class="details-box" v-if="costRate[index]">
												<div class="name">{{ costRate[index] }}</div>

												<div class="value">
													{{
														costRate[index].includes('率')
															? $formattedMoney($utils.handleNumber(item * 100)) + '%'
															: $formattedMoney($utils.handleNumber(item)) + '万元'
													}}
												</div>
											</div>
										</template>
									</div>
									<template v-if="activeData?.creditRiskInvestVo?.securitization?.unsecured?.netCashFlows?.length > 0">
										<div class="table-container classnames">
											<div class="table-title">净现金流测算(万元)</div>
											<div style="display: flex; justify-content: space-around">
												<div
													:class="`active-${index}`"
													v-for="(item, index) in activeData?.creditRiskInvestVo?.securitization?.unsecured?.netCashFlows"
													:key="index"
												>
													<div class="table-title" style="background: #f7f8fa; border-radius: 0px">{{ item.title }}</div>
													<template v-for="(ite, key) of item" :key="key">
														<div class="details-box" v-if="flowCalculation[key]">
															<div class="name">{{ flowCalculation[key] }}</div>
															<div class="value" style="margin-left: 5px">{{ $formattedMoney($utils.handleNumber(ite)) }}</div>
														</div>
													</template>
												</div>
											</div>
										</div>
									</template>

									<div class="table-container">
										<div class="table-title">测算前提</div>
										<div class="details-box" v-for="(item, index) in preUnSecuredData" :key="index" v-show="item.label">
											<div class="name">{{ item.label }}</div>
											<div class="value" v-if="!item.type">
												{{ item.value }}
												{{
													item.label === 'EBITDA' || item.label === '评估值'
														? '亿'
														: item.label === '承销费' || item.label === '计划管理费'
														? '万元'
														: ''
												}}
											</div>
											<div class="value" v-else>{{ item.value + '%' }}</div>
											<!-- <div class="value" v-if="!item.type">{{ item.value }}</div>
										<el-tooltip v-else effect="light" content="输入后点击右上角“保存并计算”重新计算数据">
											<el-input class="boxItemTooltip" v-model="item.value" placeholder="请输入" style="width: 100px"
												><template #append>%</template>
											</el-input>
										</el-tooltip> -->
										</div>
									</div>
									<!-- <div class="classnames">
										<myTable2 :classname="'nothing'" :data="activeData?.creditRiskInvestVo?.securitization?.unsecured?.types.slice(0, 3)"></myTable2>
									</div> -->
								</div>
							</div>
						</div>
					</arco-tab-pane>
					<arco-tab-pane key="reits">
						<template #title>
							<div class="financeImg">
								<img v-if="activeName == 'reits'" src="@/assets/images/risks/couponActive.png" alt="" />
								<img v-if="activeName !== 'reits'" src="@/assets/images/risks/coupon.png" alt="" />
								<div>Pre-Reits基金</div>
							</div>
						</template>
						<div style="display: flex; justify-content: flex-start" v-if="activeData?.creditRiskInvestVo?.securitization">
							<el-button type="primary" color="#1868F1" @click="saveCompute">保存并计算</el-button>
						</div>
						<div class="securitzation" v-if="activeData?.creditRiskInvestVo?.securitization">
							<div class="left">
								<div class="title">有担保</div>
								<div class="securitzation_content">
									<div v-if="activeData?.creditRiskInvestVo?.basicAssumption?.secured?.length > 0" class="classnames">
										<myTable4
											@search="handleSearch"
											:classname="'have'"
											:data="activeData?.creditRiskInvestVo?.basicAssumption?.secured.slice(0, 9)"
										></myTable4>
									</div>
									<div v-if="activeData?.creditRiskInvestVo?.basicAssumption?.secured?.length > 0" class="classnames">
										<myTable5
											:classname="'have'"
											:data="activeData?.creditRiskInvestVo?.basicAssumption?.secured.slice(9, 25)"
											@search="handleSearch"
										></myTable5>
									</div>
									<div v-if="activeData?.creditRiskInvestVo?.basicAssumption?.secured?.length > 0" class="classnames">
										<myTable6
											:classname="'have'"
											:data="[
												...activeData?.creditRiskInvestVo?.basicAssumption?.secured.slice(25, 26),
												...activeData?.creditRiskInvestVo?.basicAssumption?.secured.slice(27, 30),
											]"
											@search="handleSearch"
										></myTable6>
									</div>
									<div v-if="activeData?.creditRiskInvestVo?.basicAssumption?.secured?.length > 0" class="classnames">
										<myTable7
											:classname="'have'"
											:data="activeData?.creditRiskInvestVo?.basicAssumption?.secured.slice(30, 32)"
											@search="handleSearch"
										></myTable7>
									</div>
									<div v-if="activeData?.creditRiskInvestVo?.cashFlowEstimation?.secured?.length > 0">
										<myTable8 :classname="'have'" :data="activeData?.creditRiskInvestVo?.cashFlowEstimation?.secured"></myTable8>
									</div>
								</div>
							</div>
							<div class="right">
								<div class="title">无担保</div>
								<div class="securitzation_contents">
									<div class="classnames" v-if="activeData?.creditRiskInvestVo?.basicAssumption?.unsecured?.length > 0">
										<myTable4
											:classname="'nothing'"
											@search="handleSearch"
											:data="activeData?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(0, 9)"
										></myTable4>
									</div>
									<div class="classnames" v-if="activeData?.creditRiskInvestVo?.basicAssumption?.unsecured?.length > 0">
										<myTable5
											:classname="'nothing'"
											:data="activeData?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(9, 25)"
											@search="handleSearch"
										></myTable5>
									</div>
									<div class="classnames" v-if="activeData?.creditRiskInvestVo?.basicAssumption?.unsecured?.length > 0">
										<myTable6
											:classname="'nothing'"
											:data="[
												...activeData?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(25, 26),
												...activeData?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(27, 30),
											]"
											@search="handleSearch"
										></myTable6>
									</div>
									<div class="classnames" v-if="activeData?.creditRiskInvestVo?.basicAssumption?.unsecured?.length > 0">
										<myTable7
											:classname="'nothing'"
											:data="activeData?.creditRiskInvestVo?.basicAssumption?.unsecured.slice(30, 32)"
											@search="handleSearch"
										></myTable7>
									</div>
									<div v-if="activeData?.creditRiskInvestVo?.cashFlowEstimation?.unsecured?.length > 0">
										<myTable8 :classname="'nothing'" :data="activeData?.creditRiskInvestVo?.cashFlowEstimation?.unsecured"></myTable8>
									</div>
								</div>
							</div>
						</div>
					</arco-tab-pane>
				</arco-tabs>
			</div>
		</div>
	</div>

	<buildSelect
		key="all"
		v-model="dialogVisible"
		:maxSelectNum="9999"
		:selectedData="multipleSelection"
		title="添加楼宇"
		dialogType="building"
		:searchTypes="['position', 'type', 'keyword']"
		:tableColumns="tableColumns"
		tagLabelKey="buildingName"
		@confirm="handleBuildConfirm"
	></buildSelect>

	<BuildingFormDialog v-if="dialogVisibleBuilding" :visible="dialogVisibleBuilding" @handleUpdateClose="handleUpdateClose" />
</template>

<script setup>
const props = defineProps({
	riskType: {
		type: String,
		default: '',
	},
});

import buildSelect from '@/component/buildSelect/index.vue';
import federalReserve from '@/assets/FederalReserve.png';
import CPI from '@/assets/CPI.png';
import decadeTB from '@/assets/decadeTB.png';
import LPR from '@/assets/LPR.png';
import BuildingFormDialog from './components/BuildingFormDialog.vue';
import axios from 'axios';
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import securitzation from './components/securitization.vue';
import myTable2 from './components/myTable2.vue';
import { financeDataList } from '../../api/finance.js';
import myTable4 from './components/myTable4.vue';
import myTable5 from './components/myTable5.vue';
import myTable6 from './components/myTable6.vue';
import myTable7 from './components/myTable7.vue';
import myTable8 from './components/myTable8.vue';
import { formattedMoney } from 'UTILS'; // 千分符
import {
	getCreditRisks, // 楼宇信息
	getCreditRisksManual, // 获取信用风险
} from '../../api/risks.js';

const buildingIndex = ref(0);
// 选择对比资产
const activeName = ref('first');
// 选择对比资产
const dialogVisible = ref(false);
// 新建楼宇
const dialogVisibleBuilding = ref(false);

// 市场法
const proportionValue = ref();
// 收益法比例
const proportionValue2 = ref();
// echarts绑定DOM
const echartsContainer = ref(null);
// 选中资产
const activeData = ref();
// 基金
const preList = ref([
	{ name: '专项计划管理费', key: 'planManagementFeeRate', value: '' },
	{ name: '专项计划托管费', key: 'planCustodyFeeRate', value: '' },
	{ name: '付息兑付手续费', key: 'redemptionFee', value: '' },
	{ name: '跟踪评级', key: 'trackingRating', value: '' },
	{ name: '跟踪审计', key: 'trackingAudit', value: '' },
	{ name: '跟踪评估', key: 'trackingAssessment', value: '' },
	{ name: '保证金投资收益', key: 'marginInvestmentReturnRate', value: '' },
	{ name: '上市处置费用', key: 'listingDisposalFeeRate', value: '' },
	{ name: '企业所得税率', key: 'corporateIncomeTaxRate', value: '' },
	{ name: '法定盈余公积', key: 'statutorySurplusReserve', value: '' },
	{ name: '股东贷款利率上限', key: 'loanRate', value: '' },
	{ name: '股东贷款', key: 'loan', value: '' },
]);

// 金融市场数据
const finDataList = ref([
	{
		id: 'tenYearBond',
		name: '十年国债',
		img: decadeTB,
		value: '',
	},
	{ id: 'lpr', name: 'LPR', img: federalReserve, value: '' },
	{ id: 'federalReserveBankRate', name: '美联储', img: LPR, value: '' },
	{ id: 'cpi', name: 'CPI', img: CPI, value: '' },
]);
// 资产对比弹出内table
const tableColumns = [
  { title: '资产名称', dataIndex: 'buildingName', width: '220' },
  { title: '所在城市', dataIndex: 'city', width: '120' },
  { title: '资产类型', dataIndex: 'buildingType', width: '100' },
  { title: '地址', dataIndex: 'street' },
];

//现金流table
const cashFlowTable = [
	// { prop: 'index', label: '序号' },
	{ prop: 'year', label: '年份' },
	{ prop: 'totalRevenue', label: '收入合计（万）' },
	{ prop: 'mangeExpense', label: '物业及运营管理支出（万）', width: '200px' },
	{ prop: 'totalTax', label: '税金合计（万）' },
	{ prop: 'borrowCost', label: '借款成本（万）' },
	{ prop: 'ebitda', label: 'EBITDA' },
];
// 证券化 测算前提
const dict = {
	ebitda: 'EBITDA',
	evaluateValue: '评估值',
	securedRate: '评级公司预估，若A3担保主体为AAA评级，则利率约为',
	securedRatio: '担保公司表示，担保费率约为发行规模的',
	// loanExchangeRate: '贷款汇率',
	custodianFeeRate: '托管费率',
	fundFeeRate: '基金管理费率',
	underwritingFee: '承销费',
	planFee: '计划管理费',
};

// 证券化 净现金流测算
const flowCalculation = {
	totalIssuance: '发行总规模',
	loan: '贷款',
	margin: '保证金',
	guaranteeFee: '担保费',
	custodianFee: '托管费',
	fundFee: '基金管理费',
	underwritingFee: '承销费',
	planFee: '计划管理费',
	netCashFlow: '净现金流',
};

// 证券化 综合成本率测算
const costRate = {
	totalCost: '综合成本',
	issuanceSize: '发行规模',
	totalCostRate: '综合成本率',
};

//证券化测算前提无担保
const preUnSecuredData = ref([]);
//证券化测算前提有担保
const preSecuredData = ref([]);
// 权益指数图表
const myChart = ref(null);
// 融资利率
const interestRate = ref(0);
//融资比例
const loanRatio = ref(100);
//假设支出变化
const expenditureChange = ref(1.92);
//假设租金增长率
const rentalGrowthRate = ref(3.8);
//占比
const proportion = ref('');
// 图表前缀
const proxyAddress = ref('https://static.biaobiaozhun.com/');
// 购买面积比例
const options = [
	{
		value: '1',
		label: '10%',
		price: 0.1,
	},
	{
		value: '2',
		label: '20%',
		price: 0.2,
	},
	{
		value: '3',
		label: '30%',
		price: 0.3,
	},
	{
		value: '4',
		label: '40%',
		price: 0.4,
	},
	{
		value: '5',
		label: '50%',
		price: 0.5,
	},
	{
		value: '6',
		label: '60%',
		price: 0.6,
	},
	{
		value: '7',
		label: '70%',
		price: 0.7,
	},
	{
		value: '8',
		label: '80%',
		price: 0.8,
	},
	{
		value: '9',
		label: '90%',
		price: 0.9,
	},
	{
		value: '10',
		label: '100%',
		price: 1,
	},
];
// 存放资产
const buildingList = ref([]);
// 选择资产
const multipleSelection = ref([]);
//存放选中的id
const idsArr = ref([]);
// 计算保存时存放对象
const Description = ref({
	secured: {
		planManagementFeeRate: '', // 专项计划管理费
		planCustodyFeeRate: '', //  专项计划托管费
		redemptionFee: '', //付息兑付手续费
		trackingRating: '', //跟踪评级
		trackingAudit: '', //跟踪审计
		trackingAssessment: '', //跟踪评估
		marginInvestmentReturnRate: '', //保证金投资收益
		listingDisposalFeeRate: '', //上市处置费用
		corporateIncomeTaxRate: '', //企业所得税率
		statutorySurplusReserve: '', //法定盈余公积
		loan: '', //  股东贷款
		loanRate: '', //  股东贷款利率上限

		// securedRate: '', // 有担保综合成本率
		// securedRatio: '', // 有担保综合成本率
		// custodianFeeRate: '', // 有担保托管费率
		// fundFeeRate: '', // 有担保基金管理费率
	},
	unsecured: {
		planManagementFeeRate: '', // 专项计划管理费
		planCustodyFeeRate: '', //  专项计划托管费
		redemptionFee: '', //付息兑付手续费
		trackingRating: '', //跟踪评级
		trackingAudit: '', //跟踪审计
		trackingAssessment: '', //跟踪评估
		marginInvestmentReturnRate: '', //保证金投资收益
		listingDisposalFeeRate: '', //上市处置费用
		corporateIncomeTaxRate: '', //企业所得税率
		statutorySurplusReserve: '', //法定盈余公积
		loan: '', //  股东贷款
		loanRate: '', //  股东贷款利率上限

		// securedRate: '', // 无担保综合成本率
		// securedRatio: '', // 无担保综合成本率
		// custodianFeeRate: '', // 无担保托管费率
		// fundFeeRate: '', // 无担保基金管理费率
	},
});

onMounted(() => {
	getfinData(); // 金融市场数据
	handleInitEcharts(); // 初始化 ECharts 实例
});

// 打开新建楼宇弹窗
const openDialog = () => {
	dialogVisibleBuilding.value = true;
};
// 关闭新建楼宇弹窗
const handleUpdateClose = (val) => {
	if (val.form?.buildingName) {
		console.log('val.form111111', val.form);
		if (!val.form.proportion_options) {
			val.form.proportion = 0;
			val.form.proportion_options = options;
		}
		val.form.buildingSize = val.form.buildingArea;
		if (!proportionValue.value) {
			proportionValue.value = 0.7;
			proportionValue2.value = 0.3;
		}
		buildingList.value.push(val.form);
		buildingIndex.value += 1;
	}
	dialogVisibleBuilding.value = val.visible;
};

function handleChangeTabs(params) {
	activeName.value = params;
}

// 金额格式化
const formatterAmount = (row) => {
	if (row.column.dataIndex !== 'year') {
		return formattedMoney(parseFloat(row.record[row.column.dataIndex] * 10000).toFixed(2));
	}
	return row.record[row.column.dataIndex];
};

// 融资比例
const loanRatioChange = (e) => {
	// 数值不在0-100之间则进行提示
	loanRatio.value = e;
	if (e < 0 || e > 100) {
		ElMessage.warning('融资比例请输入0≤x≤100的数值');
		return;
	}
	if (activeData?.value?.creditRiskAssetVoList) {
		getInvesRiskList();
		return;
	}
};

function handleKeyValue(key, name) {
	if (name === '维护情况') {
		if (key == '无维护') {
			return {
				color: `#9FD4FD`,
			};
		} else if (key == '一般') {
			return {
				color: `#57A9FB`,
			};
		} else if (key == '良好') {
			return {
				color: `#3491FA`,
			};
		} else if (key == '优秀') {
			return {
				color: `#206CCF`,
			};
		} else if (key == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
	if (name === '区域潜力') {
		if (key == '上升') {
			return {
				color: `#89D178`,
			};
		} else if (key == '维持') {
			return {
				color: `#0FC6C2`,
			};
		} else if (key == '稳定') {
			return {
				color: `#3491FA`,
			};
		} else if (key == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
	if (name === '商业活力') {
		if (key == '初期') {
			return {
				color: `#89D178`,
			};
		} else if (key == '活跃') {
			return {
				color: `#FF9A2E`,
			};
		} else if (key == '繁荣') {
			return {
				color: `#F76560`,
			};
		} else if (key == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
}

function handlebuildingRateColor(level) {
	if (level == 'S') {
		return {
			background: `linear-gradient(90deg, #9D71DA 0%, #722ED1 100%)`,
		};
	}
	if (level == 'A+') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}
	if (level == 'A') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}

	if (level == 'B+') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}
	if (level == 'B') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}

	if (level == 'C') {
		return {
			background: `linear-gradient(90deg, #FFA44D 0%, #FF7D00 100%)`,
		};
	}
}

// 确定
const submit1 = () => {
	if (buildingIndex.value !== 0 && buildingList.value.length > 0) {
		let status = true;
		buildingList.value.forEach((element) => {
			if (!element.proportion) {
				status = false;
			}
		});
		if (status) {
			// 楼宇信息
			getInvesRiskList();
		} else {
			ElMessage.warning('请选择购买面积');
		}
	} else {
		if (multipleSelection.value.length === 0) {
			ElMessage.warning('请添加楼宇');
			return;
		}

		let status = true;
		multipleSelection.value.forEach((element) => {
			if (!element.proportion) {
				status = false;
			}
		});

		idsArr.value = multipleSelection.value.map((item) => item.uniqueCode);

		if (status) {
			// 楼宇信息
			getInvesRiskList();
		} else {
			ElMessage.warning('请选择购买面积');
		}
	}
};

// 添加
const onAdd = () => {
	dialogVisible.value = true;
};

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	multipleSelection.value.forEach((e) => {
		if (!e.proportion_options) {
			// e.proportion = 0;
			e.proportion_options = options;
		}
	});
	onConfirm();
}

// 关闭对话框
const onConfirm = () => {
	dialogVisible.value = false;
	if (!proportionValue.value) {
		proportionValue.value = 0.7;
		proportionValue2.value = 0.3;
	}
	buildingList.value = multipleSelection.value;
};

const onChangeProportion = (val) => {
	proportionValue.value = val;
	proportionValue2.value = Number((1 - val).toFixed(1));
	// 判断是否有数据
	if (activeData?.value?.creditRiskAssetVoList) {
		getInvesRiskList(); // 楼宇信息
	}
};
const onChangeProportion2 = (val) => {
	proportionValue2.value = val;
	proportionValue.value = Number((1 - val).toFixed(1));
	// 判断是否有数据
	if (activeData?.value?.creditRiskAssetVoList) {
		getInvesRiskList(); // 楼宇信息
	}
};

// 楼宇信息
function getInvesRiskList(type) {
	// 测算前提清空
	preSecuredData.value = [];
	preUnSecuredData.value = [];

	let ratios = '';
	// 新建楼宇 购买面积比例
	if (buildingIndex.value !== 0 && buildingList.value.length > 0) {
		ratios = buildingList.value.map((item) => item.proportion).join(',');
	} else {
		if (multipleSelection.value.length > 0) {
			ratios = multipleSelection.value.map((item) => item.proportion).join(',');
		}
	}

	// 计算保存
	let description = {};
	if (type == 'save') {
		description = Description.value;
	}
	if (buildingIndex.value === 0 && multipleSelection.value.length > 0) {
		const params = ref({
			ids: idsArr.value.toString(),
			ratios,
			...description,
			interestRate: interestRate.value / 100,
			loanRatio: loanRatio.value / 100,
			marketRatio: proportionValue.value,
			incomeRatio: proportionValue2.value,
			rentalGrowthRate: (rentalGrowthRate.value / 100).toFixed(4), // 假设租金增长率
			expenditureChange: (expenditureChange.value / 100).toFixed(4), // 假设支出变化
		});

		getCreditRisks({ ...params.value }).then((res) => {
			if (res.code == 200) {
				handleData(res);
			}
		});
	} else {
		buildingList.value.forEach((element) => {
			if (!element.ratio) {
				element.ratio = element.proportion;
				element.propertyRatio = (Number(element.propertyRatio) / 100).toFixed(4);
				element.grossRate = (Number(element.grossRate) / 100).toFixed(4);
				element.rentalRate = (Number(element.rentalRate) / 100).toFixed(4);
				delete element.address;
				// delete element.proportion_options;
			}
		});
		// 计算保存
		const params = ref({
			ratios,
			...description,
			interestRate: interestRate.value / 100,
			loanRatio: loanRatio.value / 100,
			marketRatio: proportionValue.value,
			incomeRatio: proportionValue2.value,
			rentalGrowthRate: rentalGrowthRate.value / 100, // 假设租金增长率
			expenditureChange: expenditureChange.value / 100, // 假设支出变化
			creditRiskInvestRequestManualVoList: buildingList.value,
		});
		getCreditRisksManual({ ...params.value }).then((res) => {
			if (res.code == 200) {
				handleData(res);
			}
		});
	}
}

// 数据处理
function handleData(res) {
	activeData.value = res.data;
	console.log(res.data, '1111111111111');
	// 测算前提数据重新组装
	const securitization = activeData.value?.creditRiskInvestVo?.securitization;
	const basicAssumptions = activeData.value?.creditRiskInvestVo?.basicAssumption;
	if (securitization) {
		const secureds = ['secured', 'unsecured'];

		secureds.forEach((element) => {
			basicAssumptions[element].forEach((items) => {
				preList.value.forEach((its) => {
					if (items.title == its.name) {
						Description.value[element][its.key] = items.assumptionStr == '0' ? '' : items.assumptionStr;
						if (items.assumptionStr !== '0') {
							if (its.key !== 'redemptionFee' && its.key !== 'trackingRating' && its.key !== 'trackingAudit' && its.key !== 'trackingAssessment')
								items.assumptionStr = (Number(items.assumptionStr) * 100).toFixed(2);
						}
					}
				});
			});

			for (let key in securitization[element].assume) {
				let type = null;
				if (key === 'securedRate' || key === 'securedRatio' || key === 'custodianFeeRate' || key === 'fundFeeRate') {
					type = '1'; // 证券化保存并计算
				}
				// 测算前提数据截取
				if (element == 'secured') {
					preSecuredData.value.push({
						label: dict[key],
						ykey: key,
						value: securitization[element].assume[key]
							? type
								? (securitization[element].assume[key] * 100).toFixed(2)
								: securitization[element].assume[key].toFixed(2)
							: '',
						type: type,
					});
				} else {
					preUnSecuredData.value.push({
						label: dict[key],
						ykey: key,
						value: securitization[element].assume[key]
							? type
								? (securitization[element].assume[key] * 100).toFixed(2)
								: securitization[element].assume[key].toFixed(2)
							: '',
						type: type,
					});
				}
			}
			if (element == 'secured') {
				preSecuredData.value.push({ label: '劣后级为过桥资金，需要从净现金流中扣除', value: '' });
			} else {
				preUnSecuredData.value.push({ label: '劣后级为过桥资金，需要从净现金流中扣除', value: '' });
			}
		});
	}
	// echarts图表
	generateEcharts();
}

// 假设租金增长率 假设支出变化计算
function handleChangeRate() {
	if (!activeData?.value?.creditRiskAssetVoList) {
		ElMessage.warning('请先选择楼宇信息');
		return;
	}
	// interestRate数值是否在大于等于2.25小于等于10之间
	if (interestRate.value < 2.25 || interestRate.value > 10) {
		ElMessage.warning('融资利率请输入2.25≤x≤10的数值');
		return;
	}

	setTimeout(() => {
		getInvesRiskList();
	}, 500);
}

// 假设租金增长率
function handleChangeRental() {
	if (!activeData?.value?.creditRiskAssetVoList) {
		ElMessage.warning('请先选择楼宇信息');
		return;
	}
	setTimeout(() => {
		getInvesRiskList();
	}, 500);
}
function handleRentalInput(val, key) {
	// 判断输入的值是否在0-100之间
	if (val < 0 || val > 100) {
		ElMessage.warning('请输入0≤x≤100的数值');
		return;
	}
	[key].value = val;
}

// 初始化图表
function handleInitEcharts() {
	myChart.value = echarts.init(echartsContainer.value);
	window.addEventListener('resize', () => {
		myChart.value.resize(); // 窗口发生改变就更新echarts
	});
	// 设置图表选项
	myChart.value.setOption({
		radar: {
			center: ['52%', '52%'],
			radius: 100,
			axisName: {
				formatter: '{value}',
				color: '#4E5969',
			},
			splitArea: {
				areaStyle: {
					color: ['#fff', '#fff', '#fff', '#fff', '#fff'],
				},
			},
			indicator: [{ name: '市场法价格' }, { name: '收益法价格' }, { name: '评估值' }, { name: '折旧' }, { name: 'EBITDA' }],
		},
		series: [
			{
				type: 'radar',
				areaStyle: {},
				data: [],
			},
		],
	});
}

// echarts生成
const generateEcharts = () => {
	myChart.value.setOption({
		color: '#249EFF',
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(244, 247, 252, .8)',
			borderColor: 'transparent',
			borderRadius: 4,
			formatter: (params) => {
				let arr = [
					{
						name: '市场法价格',
						value: '',
						unit: '万元',
					},
					{
						name: '收益法价格',
						value: '',
						unit: '万元',
					},
					{
						name: '评估值',
						value: '',
						unit: '亿元',
					},
					{
						name: '折旧',
						value: '',
						unit: '万元',
					},
					{
						name: 'EBITDA',
						value: '',
						unit: '万元',
					},
				];
				params.value.forEach((items, index) => {
					arr.forEach((element, indexs) => {
						if (index === indexs) {
							element.value = items;
						}
					});
				});
				console.log(arr, 'arrarrarrarr');
				let str = '';
				arr.forEach((item) => {
					str += `<div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
				<div style="width:8px;height:8px;background-color:#249EFF;margin-right: 8px;border-radius: 50%;">
				</div>
				<div style="width:100%;display:flex;justify-content: space-between;align-items: center;">
					<div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
					${item.name}
				</div>
				<div style="font-size: 14px; color: #1D2129; font-weight: 600">
					${item.value}${item.unit}
				</div>
				</div>
				</div>`;
				});
				return `
        <div style="display: flex;
            flex-direction: column;
            gap: 4px;">
            ${str}
        </div>
        `;
			},
		},
		radar: {
			indicator: [{ name: '市场法价格' }, { name: '收益法价格' }, { name: '评估值' }, { name: '折旧' }, { name: 'EBITDA' }],
		},
		series: [
			{
				type: 'radar',
				symbolSize: 6,
				backgroundColor: 'rgba(0,23,11,0.3)',
				tooltip: {
					trigger: 'item',
				},
				itemStyle: {
					borderWidth: 2,
					borderColor: '#d3ecff',
				},
				areaStyle: {
					color: '#a7d8ff',
				},
				data: [
					{
						value: [
							activeData?.value?.marketPriceIndex,
							activeData?.value?.incomePriceIndex,
							activeData?.value?.presentValueOfAsset,
							activeData?.value?.depreciation,
							activeData?.value?.ebitda,
						],
					},
				],
			},
		],
	});
};

// 导出
function handleExport(params, url, name) {
	axios({
		url: url,
		method: 'POST',
		responseType: 'blob',
		headers: {
			'Content-Type': 'application/json',
			Authorization: localStorage.getItem('token'),
		},
		data: params,
	})
		.then((response) => {
			const blob = new Blob([response.data], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			});
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `${name}.xlsx`;
			a.click();
			window.URL.revokeObjectURL(url);
			ElMessage.success('导出成功');
		})
		.catch((error) => {
			ElMessage.warning(error);
		});
}

// 导出
function handleDownload() {
	if (activeName.value === 'first') {
		let params = {
			// ids: newinvestArr.value,
			total: activeData?.value?.investmentAmount, //总投资额
			loanRatio: loanRatio.value / 100, //融资比例
			loanQuota: activeData?.value?.creditRiskInvestVo?.financingLimit, //融资额度
			interestRate: interestRate.value / 100, //融资利率
			rentalGrowthRate: (rentalGrowthRate.value / 100).toFixed(4), // 假设租金增长率
			expenditureChange: (expenditureChange.value / 100).toFixed(4), // 假设支出变化
			cashFlows: activeData?.value?.creditRiskInvestVo?.cashFlows, //现金流数据
		};
		let url = 'https://bbzhun.com/api/credit-risk/export-cash-flow';
		handleExport(params, url, '现金流'); //现金流导出
	} else if (activeName.value === 'second') {
		let ratios = '';
		if (multipleSelection.value.length > 0) {
			ratios = multipleSelection.value.map((item) => item.proportion).join(',');
		}
		let params = {
			ids: idsArr.value.toString(),
			ratios: ratios,
			loanRatio: loanRatio.value / 100, //融资比例
			interestRate: interestRate.value / 100, //融资利率
		};
		let url = 'https://bbzhun.com/api/credit-risk/export-securitization?ids=' + idsArr.value.toString();
		handleExport(params, url, '证券化'); //证券化导出
	} else if (activeName.value === 'reits') {
		let ratios = '';
		if (multipleSelection.value.length > 0) {
			ratios = multipleSelection.value.map((item) => item.proportion).join(',');
		}
		let params = {
			ids: idsArr.value.toString(),
			ratios: ratios,
			loanRatio: loanRatio.value / 100, //融资比例
			interestRate: interestRate.value / 100, //融资利率
		};
		let url = 'https://bbzhun.com/api/credit-risk/export-reits?ids=' + idsArr.value.toString();
		handleExport(params, url, 'Pre-Reits基金'); //Pre-Reits基金导出
	}
}

// 下载风险评估报告
function handleDownLoadRisk() {
	let arr = [];
	if (!activeData?.value?.creditRiskAssetVoList) {
		// 提示
		ElMessage({
			message: '请先添加楼宇',
			type: 'warning',
		});
		return;
	}
	activeData?.value?.creditRiskAssetVoList.forEach((element) => {
		arr.push({
			name: element.name,
			assetType: element.assetType,
			address: element.address,
			buildingArea: element.buildingSize,
			buildYear: element.buildYear,
			regionalPotential: element.regionalPotential,
			businessDynamism: element.businessDynamism,
			spendingPower: element.spendingPower,
		});
	});
	let params = {
		marketPriceIndex: activeData?.value?.marketPriceIndex, // 市场法价格
		marketPriceRatio: proportionValue.value, // 市场法价格比
		incomePriceIndex: activeData?.value?.incomePriceIndex, //收益法价格
		incomePriceRatio: proportionValue2.value, // 收益法价格比
		assess: activeData?.value?.presentValueOfAsset * 10000, //评估值
		salePrice: activeData?.value?.salePrice * 10000, // 价格（万元/㎡）
		depreciation: activeData?.value?.depreciation, //折旧
		ebitda: activeData?.value?.ebitda, // EBITDA
		capitalizationRate: activeData?.value?.capitalizationRate, // 资本化率
		ltv: activeData?.value?.ltv, //抵押率
		defaultRate: activeData?.value?.defaultRate, //  违约率（租户）
		icr: activeData?.value?.icr, //利息覆盖倍数ICR
		incomeGrowthRate: activeData?.value?.incomeGrowthRate, //收入/租金增长率
		rentSaleRatio: activeData?.value?.rentSaleRatio, // 租售价格比
		operationalRisk: handleoperations(activeData?.value?.operationalRisk), //运营风险
		presentValueOfAsset: activeData?.value?.presentValueOfAsset, // 资产现值（亿元）
		transactionCost: activeData?.value?.transactionCost, //交易成本
		tax: activeData?.value?.tax, // 税金
		fee: activeData?.value?.fee, //费用
		total: activeData?.value?.investmentAmount, //总投资额
		asserts: arr, // 楼宇信息
	};
	let url = 'https://bbzhun.com/api/credit-risk/export-risk';
	handleExport(params, url, '风险评估'); //风险评估报告导出
}

//金融市场数据
const getfinData = async () => {
	await financeDataList()
		.then((res) => {
			finDataList.value.forEach((item) => {
				if (res.data.hasOwnProperty(item.id)) {
					item.value = res.data[item.id];
				}
			});
		})
		.catch((err) => {
			console.log(err);
		});
};

// 运营风险数值转化中文
function handleoperations(val) {
	if (0 < val && val <= 3) {
		return '低';
	} else if (3 < val && val <= 4.5) {
		return '低';
	} else if (4.5 < val && val <= 6) {
		return '中';
	} else if (6 < val && val <= 8) {
		return '中';
	} else if (8 < val && val <= 9) {
		return '高';
	} else if (9 < val && val <= 10) {
		return '高';
	}
}

// 保存并计算
const saveCompute = () => {
	if (!activeData?.value?.creditRiskAssetVoList) {
		// 提示
		ElMessage({
			message: '请先选择要计算的楼宇',
			type: 'warning',
		});
		return;
	}

	getInvesRiskList('save');
};
// 证券化
const handleSearch = (val, type) => {
	if (type == 'have') {
		console.log(val, 'val');
		if (
			val.key !== 'redemptionFee' &&
			val.key !== 'trackingRating' &&
			val.key !== 'trackingAudit' &&
			val.key !== 'trackingAssessment' &&
			val.key !== 'loan'
		) {
			Description.value.secured[val.key] = val.value ? (Number(val.value) / 100).toFixed(4) : '';
		} else {
			Description.value.secured[val.key] = val.value;
		}
	} else if (type == 'nothing') {
		if (
			val.key !== 'redemptionFee' &&
			val.key !== 'trackingRating' &&
			val.key !== 'trackingAudit' &&
			val.key !== 'trackingAssessment' &&
			val.key !== 'loan'
		) {
			Description.value.unsecured[val.key] = val.value ? (Number(val.value) / 100).toFixed(4) : '';
		} else {
			Description.value.unsecured[val.key] = val.value;
		}
	}

	console.log(Description.value, 'Description.value');
};

// 证券化保存并计算
function handleSaveSecurity() {
	//将测算前提输入框内容保存到Description中
	preSecuredData.value.forEach((item) => {
		if (Description.value.secured[item.ykey] !== undefined) {
			Description.value.secured[item.ykey] = item.value;
		}
	});
	preUnSecuredData.value.forEach((item) => {
		if (Description.value.unsecured[item.ykey] !== undefined) {
			Description.value.unsecured[item.ykey] = item.value;
		}
	});
	// 存在证券化数据时，调用证券化接口
	if (activeData?.value?.creditRiskInvestVo?.securitization?.secured?.types.length > 0) {
		// 接口
	}
}

// 删除
function handleDelete(index) {
	if (buildingList.value?.length === 1) {
		activeData.value = {};
		buildingIndex.value = 0;
	}
	buildingList.value[index].proportion = '';
	// if (multipleSelection.value.length > 0) {
	// multipleSelection.value.splice(index, 1);
	// multipleSelection.value.forEach((row, indexs) => {
	// 	multipleTableRef.value.toggleRowSelection(row, index === indexs ? false : true);
	// });
	// }

	buildingList.value.splice(index, 1);
}

defineExpose({ handleUpdateClose });
</script>
<style scoped lang="less">
@import url('./style.less');
</style>
<style lang="scss">
.editNum {
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}
	input[type='number'] {
		-moz-appearance: textfield;
	}
}
</style>
