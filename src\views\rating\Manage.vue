<template>
	<div class="bigbody">
		<el-row :gutter="20" class="el-rowele">
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					现场调查>>
					<el-divider class="divider" />
				</el-text>
				<el-text class="mx-1" tag="p" style="padding-bottom: 10px; line-height: 1.5">{{ scene.combined_evaluation }}</el-text>
				<div v-if="type === '1'">
					<el-row class="box">
						<el-col class="item" :span="9">事项</el-col>
						<el-col class="item" :span="3">得分</el-col>
						<el-col class="item" :span="9">事项</el-col>
						<el-col class="item no-left-border" :span="3">得分</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">建筑内外物管员工密度</el-col>
						<el-col class="item" :span="3">{{ scene.material_density }}</el-col>
						<el-col class="itemTwo" :span="9">建筑物外观新旧程度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.appearance_new_old }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">建筑物内部新旧程度</el-col>
						<el-col class="item" :span="3">{{ scene.building_new_old }}</el-col>
						<el-col class="itemTwo" :span="9">设备情况:扶梯、直梯品牌</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.equipment_condition }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">电梯数量，等待时间</el-col>
						<el-col class="item" :span="3">{{ scene.elevator_waiting }}</el-col>
						<el-col class="itemTwo" :span="9">设施配套(充电、母婴、垃圾箱、灭火器)便利度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.facility_convenient }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">物管人员专业度</el-col>
						<el-col class="item" :span="3">{{ scene.property_specialization }}</el-col>
						<el-col class="itemTwo" :span="9">物管人员服务态度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.service_attitude }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">周边商圈的繁荣程度</el-col>
						<el-col class="item" :span="3">{{ scene.peripheral_prosperity }}</el-col>
						<el-col class="itemTwo" :span="9">建筑物美观程度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.building_aesthetics }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">建筑物外部景观建设</el-col>
						<el-col class="item" :span="3">{{ scene.landscape_construct }}</el-col>
						<el-col class="itemTwo" :span="9">内部陈设舒适度，美观度、拥挤度、气味</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.internal_comfort }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">购物便捷度</el-col>
						<el-col class="item" :span="3">{{ scene.shopping_convenient }}</el-col>
						<el-col class="itemTwo" :span="9">洗手间观感</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.bathroom_perception }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">停车场观感</el-col>
						<el-col class="item" :span="3">{{ scene.parking_perception }}</el-col>
						<el-col class="itemTwo" :span="9">内部安全度观感</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.safety_perception }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">交通方便程度</el-col>
						<el-col class="item" :span="3">{{ scene.traffic_convenience }}</el-col>
						<el-col class="itemTwo" :span="9">区域安全度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.regional_security }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">区域繁荣度</el-col>
						<el-col class="item" :span="3">{{ scene.regional_prosperity }}</el-col>
						<el-col class="itemTwo" :span="9">人流消费力观感</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.consumption }}</el-col>
					</el-row>
				</div>
				<div v-if="type === '2'">
					<el-row class="box">
						<el-col class="item" :span="9">事项</el-col>
						<el-col class="item" :span="3">得分</el-col>
						<el-col class="item" :span="9">事项</el-col>
						<el-col class="item no-left-border" :span="3">得分</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">建筑内外物管员工密度</el-col>
						<el-col class="item" :span="3">{{ scene.material_density }}</el-col>
						<el-col class="itemTwo" :span="9">建筑物外观新旧程度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.appearance_new_old }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">建筑物内部新旧程度</el-col>
						<el-col class="item" :span="3">{{ scene.building_new_old }}</el-col>
						<el-col class="itemTwo" :span="9">设备情况:扶梯、直梯品牌</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.equipment_condition }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">电梯数量，等待时间</el-col>
						<el-col class="item" :span="3">{{ scene.elevator_waiting }}</el-col>
						<el-col class="itemTwo" :span="9">设施配套(充电、母婴、垃圾箱、灭火器)便利度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.facility_convenient }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">物管人员专业度</el-col>
						<el-col class="item" :span="3">{{ scene.property_specialization }}</el-col>
						<el-col class="itemTwo" :span="9">物管人员服务态度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.service_attitude }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">周边商圈的繁荣程度</el-col>
						<el-col class="item" :span="3">{{ scene.peripheral_prosperity }}</el-col>
						<el-col class="itemTwo" :span="9">建筑物美观程度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.building_aesthetics }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">建筑物外部景观建设</el-col>
						<el-col class="item" :span="3">{{ scene.landscape_construct }}</el-col>
						<el-col class="itemTwo" :span="9">内部陈设舒适度，美观度、拥挤度、气味</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.internal_comfort }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">购物便捷度</el-col>
						<el-col class="item" :span="3">{{ scene.shopping_convenient }}</el-col>
						<el-col class="itemTwo" :span="9">洗手间观感</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.bathroom_perception }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">停车场观感</el-col>
						<el-col class="item" :span="3">{{ scene.parking_perception }}</el-col>
						<el-col class="itemTwo" :span="9">内部安全度观感</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.safety_perception }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">交通方便程度</el-col>
						<el-col class="item" :span="3">{{ scene.traffic_convenience }}</el-col>
						<el-col class="itemTwo" :span="9">区域安全度</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.regional_security }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">区域繁荣度</el-col>
						<el-col class="item" :span="3">{{ scene.regional_prosperity }}</el-col>
						<el-col class="itemTwo" :span="9">人流消费力观感</el-col>
						<el-col class="item no-left-border" :span="3">{{ scene.consumption }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">是否有门禁</el-col>

						<el-col class="item no-left-border" :span="15">{{ scene.exist_entrance_guard }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">显著的优点</el-col>

						<el-col class="item no-left-border" :span="15">{{ scene.advantage }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="9">显著的缺点</el-col>

						<el-col class="item no-left-border" :span="15">{{ scene.shortcoming }}</el-col>
					</el-row>
					<el-text class="mx-itemTwo" style="margin-top: 10px; color: #000" tag="p">平均分为【{{ average }}】</el-text>
				</div>
				<el-text tag="p" class="Title" style="font-size: 16px"
					>物业管理人情况>>
					<el-divider class="divider" />
				</el-text>
				<el-text tag="p" class="TitleTwo">
					基本资料>>
					<el-divider class="divider" />
				</el-text>
				<div>
					<el-row class="box">
						<el-col class="itemTwo" :span="4">企业名称</el-col>
						<el-col class="item" :span="8">{{ basic.enterprise_name }}</el-col>
						<el-col class="itemTwo" :span="4">英文名称</el-col>
						<el-col class="item no-left-border" :span="8">{{ basic.english_name }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">法定代表人</el-col>
						<el-col class="item" :span="8">{{ basic.legal_representative }}</el-col>
						<el-col class="itemTwo" :span="4">成立时间</el-col>
						<el-col class="item no-left-border" :span="8">{{ basic.establishment_time }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">企业类型</el-col>
						<el-col class="item" :span="8">{{ basic.enterprise_type }}</el-col>
						<el-col class="itemTwo" :span="4">注册资本</el-col>
						<el-col class="item no-left-border" :span="8">{{ basic.reg_capital }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">员工人数</el-col>
						<el-col class="item" :span="8">{{ basic.employee_num }}</el-col>
						<el-col class="itemTwo" :span="4">官网</el-col>
						<el-col class="item no-left-border" :span="8">{{ basic.official_website }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">注册地址</el-col>
						<el-col class="item no-left-border" :span="20">{{ basic.reg_address }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">经营范围</el-col>
						<el-col class="item no-left-border" :span="20">{{ basic.business_scope }}</el-col>
					</el-row>
				</div>
				<el-text tag="p" class="TitleTwo">
					{{ basic.building_name }}股权结构如下所示:>>
					<el-divider class="divider" />
				</el-text>

				<el-table :data="stock" stripe style="width: 100%; color: #000" border>
					<el-table-column prop="number" label="序号" width="150" />
					<el-table-column prop="shareholder_name" label="股东名称" width="300" />
					<el-table-column prop="reg_capital" label="注册资本(万元)" width="280" />
					<el-table-column prop="shareholding_ratio" label="持股比例(%)" />
				</el-table>
				<el-text tag="p" class="TitleTwo">
					同业分析>>
					<el-divider class="divider" />
				</el-text>
				<el-text class="mx-1 minititle" tag="p">1.注册资本</el-text>
				<el-text class="mx-1 content" tag="p">{{ sametrade.reg_capital }}</el-text>
				<el-text class="mx-1 minititle" tag="p">2.进入市场时期</el-text>
				<el-text class="mx-1 content" tag="p">{{ sametrade.in_marketplace }}</el-text>
				<el-text class="mx-1 minititle" tag="p">3.地域分布</el-text>
				<el-text class="mx-1 content" tag="p">{{ sametrade.regional_distribution }}</el-text>
				<el-text tag="p" class="TitleTwo">
					法律诉讼>>
					<el-divider class="divider" />
				</el-text>

				<el-text class="mx-1 minititle" tag="p">1、汇总统计:</el-text>
				<el-text class="mx-1 content" tag="p">
					全部{{ law[3]?.incidents_number }}起案件，其中作为原告{{ law[0]?.incidents_number }}起，被告{{ law[1]?.incidents_number }}起，其他当事人{{
						law[2]?.incidents_number
					}}起，分别占 {{ law[0]?.incidents_proportion }}，{{ law[1]?.incidents_proportion }}，{{ law[2]?.incidents_proportion }}。 作为被告涉及金额{{
						law[1]?.amount_involved
					}}万，占{{ law[1]?.amount_proportion }}。作为其他当事人涉及金额{{ law[2]?.amount_involved }}万，占{{ law[2]?.amount_proportion }}。
				</el-text>
			</el-col>
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />

				<el-text tag="p" class="Title">
					产权人情况>>
					<el-divider class="divider" />
				</el-text>

				<el-text tag="p" class="TitleTwo">
					基本资料>>
					<el-divider class="divider" />
				</el-text>
				<div>
					<el-row class="box">
						<el-col class="itemTwo" :span="4">企业名称</el-col>
						<el-col class="item" :span="8">{{ basicProperty.enterprise_name }}</el-col>
						<el-col class="itemTwo" :span="4">英文名称</el-col>
						<el-col class="item no-left-border" :span="8">{{ basicProperty.english_name }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">法定代表人</el-col>
						<el-col class="item" :span="8">{{ basicProperty.legal_representative }}</el-col>
						<el-col class="itemTwo" :span="4">成立时间</el-col>
						<el-col class="item no-left-border" :span="8">{{ basicProperty.establishment_time }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">企业类型</el-col>
						<el-col class="item" :span="8">{{ basicProperty.enterprise_type }}</el-col>
						<el-col class="itemTwo" :span="4">注册资本</el-col>
						<el-col class="item no-left-border" :span="8">{{ basicProperty.reg_capital }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">员工人数</el-col>
						<el-col class="item" :span="8">{{ basicProperty.employee_num }}</el-col>
						<el-col class="itemTwo" :span="4">官网</el-col>
						<el-col class="item no-left-border" :span="8">{{ basicProperty.official_website }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">注册地址</el-col>
						<el-col class="item no-left-border" :span="20">{{ basicProperty.reg_address }}</el-col>
					</el-row>
					<el-row class="box no-top-border">
						<el-col class="itemTwo" :span="4">经营范围</el-col>
						<el-col class="item no-left-border" :span="20">{{ basicProperty.business_scope }}</el-col>
					</el-row>
				</div>
				<el-text tag="p" class="TitleTwo">
					{{ basicProperty.building_name }}股权结构如下所示:>>
					<el-divider class="divider" />
				</el-text>

				<el-table :data="stockProperty" stripe style="width: 100%; color: #000" border>
					<el-table-column prop="number" label="序号" width="150" />
					<el-table-column prop="shareholder_name" label="股东名称" width="300" />
					<el-table-column prop="reg_capital" label="注册资本(万元)" width="280" />
					<el-table-column prop="shareholding_ratio" label="持股比例(%)" />
				</el-table>
				<el-text tag="p" class="TitleTwo">
					同业分析:>>
					<el-divider class="divider" />
				</el-text>

				<el-text class="mx-1 minititle" tag="p">1.注册资本</el-text>
				<el-text class="mx-1 content" tag="p">{{ sametradeProperty.reg_capital }}</el-text>
				<el-text class="mx-1 minititle" tag="p">2.进入市场时期</el-text>
				<el-text class="mx-1 content" tag="p">{{ sametradeProperty.in_marketplace }}</el-text>
				<el-text class="mx-1 minititle" tag="p">3.地域分布</el-text>
				<el-text class="mx-1 content" tag="p">{{ sametradeProperty.regional_distribution }}</el-text>
				<el-text tag="p" class="TitleTwo">
					法律诉讼:>>
					<el-divider class="divider" />
				</el-text>

				<el-text class="mx-1 minititle" tag="p">1、汇总统计:</el-text>
				<el-text class="mx-1 content" tag="p">
					全部{{ lawPeoperty[3]?.incidents_number }}起案件，其中作为原告{{ lawPeoperty[0]?.incidents_number }}起，被告{{
						lawPeoperty[1]?.incidents_number
					}}起，其他当事人{{ lawPeoperty[2]?.incidents_number }}起，分别占 {{ lawPeoperty[0]?.incidents_proportion }}，{{
						lawPeoperty[1]?.incidents_proportion
					}}，{{ lawPeoperty[2]?.incidents_proportion }}。 作为被告涉及金额{{ lawPeoperty[1]?.amount_involved }}万，占{{
						lawPeoperty[1]?.amount_proportion
					}}。作为其他当事人涉及金额{{ lawPeoperty[2]?.amount_involved }}万，占{{ lawPeoperty[2]?.amount_proportion }}。
				</el-text>
			</el-col>
		</el-row>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http'; // 验证路径是否正确
import { useRoute } from 'vue-router';

const route = useRoute();
const basic = ref({});
const stock = ref([]);
const stockProperty = ref([]);

const sametrade = ref({});
const sametradeProperty = ref({});

const law = ref([]);
const lawPeoperty = ref([]);

const scene = ref([]);
const basicProperty = ref({});
const type = ref('');
const average = ref('');
onMounted(async () => {
	try {
		const res = await http.get('/api/a_maps.do?act=ratingPassDetailsAction', {
			params: { maps_id: route.params.id, action: 'manage' },
		});
		if (res && res.list.length > 0 && res.list[0].length > 0) {
			average.value = res.list[2].divide_equally;
			basic.value = res.list[0][0];
			stock.value = res.list[0][1];
			sametrade.value = res.list[0][2][0];
			law.value = res.list[0][3];
			scene.value = res.list[2];
			basicProperty.value = res.list[1][0];
			stockProperty.value = res.list[1][1];
			sametradeProperty.value = res.list[1][2][0];
			lawPeoperty.value = res.list[1][3];
			type.value = res.de_type_code;
		}
		
	} catch (error) {
		console.log(error, 'error');
	}
});
</script>

<style lang="less" scoped>
.el-table:deep(.cell) {
	color: #000;
}
.content {
	line-height: 1.5;
}
.minititle {
	border-bottom: 1px solid #f1f1f1;
	padding-bottom: 10px;
	color: #000;
}
.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
	margin-top: 36px;
}
.TitleTwo {
	font-size: 14px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
	margin-top: 36px;
}
.divider {
	flex-grow: 1;
	margin: 16px 0 16px 8px;
	border-color: #3483ce;
}
.box {
	border: 1px solid #ebeef5;
	text-align: center;
	.item {
		box-sizing: border-box;
		border-right: 1px solid #ebeef5;
		display: flex;
		font-size: 14px;
		align-items: center;
		// justify-content: center;
		margin-right: 0;
	}
	.no-left-border {
		border-right: none !important;
	}
	.itemTwo {
		box-sizing: border-box;
		display: flex;
		align-items: center;
		border-right: 1px solid #ebeef5;
		margin-right: 0;
		font-size: 14px;
	}
}
.no-top-border {
	border-top: none !important;
}
.el-rowele {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
</style>
