<template>
	<div class="common-layout">
		<!-- <div class="tab_box">
			<div class="title">交易计算</div>
			<div class="tab tabAct" @click="handleClick('租赁')">租赁</div>
			<div class="tab" @click="handleClick('购买')">购买</div>
		</div> -->
		<div class="container_box">
			<div class="card_box">
				<div class="tag_box">总金额： {{ $formattedMoney($utils.handleNumber(loanAmount * buildingPrice)) }}</div>
				<div class="select_box">
					<div class="label_">起止日期</div>
					<el-date-picker
						:teleported="false"
						style="width: 95%"
						format="YYYY-MM-DD"
						v-model="value1"
						date-format="YYYY-MM-DD"
						time-format="HH:mm:ss"
						type="datetimerange"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
					>
					</el-date-picker>
				</div>
				<div class="select_box">
					<div class="label_">城市</div>
					<el-cascader
						placeholder="请选择城市"
						:options="$vuexStore.state.cityArray"
						@change="handleChange"
						:props="{ value: 'label' }"
						style="width: 100%"
					>
					</el-cascader>
				</div>
				<div class="select_box">
					<div class="label_">商圈</div>
					<el-select v-model="business" placeholder="全部商圈" size="default" @change="Searchbuild">
						<el-option v-for="(item, index) in businessList" :key="index" :label="item.businessDistrictName" :value="item.id" />
					</el-select>
				</div>
				<div class="select_box">
					<div class="label_">建筑物</div>

					<el-select size="default" v-model="building" @change="handleItemChange" placeholder="请选择建筑物">
						<el-option v-for="item in buildingList" :key="item.buildingName" :label="item.buildingName" :value="item.uniqueCode"></el-option>
					</el-select>
				</div>
				<div class="select_box">
					<div class="label_">面积(㎡)：</div>
					<el-input style="width: 100%" v-model="loanAmount" type="number" :min="0" @input="loanAmountInput" placeholder="请输入面积"></el-input>
				</div>
			</div>
			<!-- 贷款计算器 -->
			<div class="computer_box">
				<div class="tag_box">
					贷款计算器 总应还款金额： {{ $formattedMoney(totalValB) }} 每月还款金额：{{ years ? (months !== 'NaN' ? $formattedMoney(months) : 0) : 0 }}
				</div>
				<div class="select_box">
					<div class="label_">贷款总额(元)：</div>
					<el-input style="width: 100%" v-model="totalMoney" placeholder="请输入贷款总额"></el-input>
				</div>
				<div class="select_box">
					<div class="label_">贷款期限(年)：</div>
					<el-input style="width: 100%" @input="handleInput" type="number" v-model="years" placeholder="请输入贷款期限"></el-input>
				</div>
				<div class="select_box">
					<div class="label_">基准利率：{{ baseRate }}%</div>
					<el-select style="width: 100%" v-model="interestRate" placeholder="基准利率">
						<el-option v-for="(rate, index) in interestRates" :key="index" :label="rate.label" :value="rate.value"></el-option>
					</el-select>
				</div>
				<div class="select_box">
					<div class="label_">贷款方式：</div>
					<el-select style="width: 100%" v-model="loanMethod" placeholder="请选择贷款方式" @change="changeVal">
						<el-option label="等额本金" value="benjin"></el-option>
						<el-option label="等额本息" value="benxi"></el-option>
					</el-select>
				</div>
			</div>
			<!-- 还款计划 -->
			<div class="plain_box">
				<div class="tag_box">还款计划</div>
				<el-table :data="tableData" height="300px" style="width: 100%" border :key="new Date()">
					<el-table-column v-for="item in cloumns" :prop="item.prop" :label="item.label" :key="item.prop">
						<template #default="scope">{{ item.prop !== 'month' ? $formattedMoney(scope.row[item.prop]) : scope.row[item.prop] }}</template>
					</el-table-column>
				</el-table>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { financeDataList } from '@/api/finance.js';
import { getBuildingSoldPrice, getBusinessDistrictList, getBuildingBusinessDistrictList } from '@/api/syt.js';
import 'dayjs/locale/zh-cn';
const years = ref();
const interestRate = ref('1'); //利率折扣，默认不打折
const loanAmount = ref('');
const totalMoney = ref(''); //// 贷款总额

const interest = ref('6.37');
const business = ref(); //商圈
const building = ref(); //建筑物
const loanMethod = ref('benxi');
const months = ref(0);
let totalRepayment;
const totalValB = computed(() => {
	const yearsVal = parseFloat(years.value);
	const totalMoneyVal = parseFloat(totalMoney.value);
	const monthlyPayment = parseFloat(months.value);
	console.log('🚀 ~ totalValB ~ monthlyPayment:', monthlyPayment);

	const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 12 / 100; //每月利率
	const totalMonths = yearsVal * 12; // 总还款月数
	console.log('🚀 ~ totalValB ~ totalMonths:', totalMonths);

	// 计算总还款额
	let totalRepayment = 0;

	if (loanMethod.value === 'benxi') {
		// 计算等额本息方式的总还款额
		totalRepayment = monthlyPayment * totalMonths;
	} else if (loanMethod.value === 'benjin') {
		// 计算等额本金方式的总还款额
		totalRepayment = 0; // 初始化总还款额

		for (let i = 0; i < totalMonths; i++) {
			const monthbenjin = totalMoneyVal / totalMonths; // 每月本金
			const monthlyInterest = (totalMoneyVal - i * monthbenjin) * monthlyInterestRate; // 每月利息

			totalRepayment += monthbenjin + monthlyInterest; // 每月本金加上利息
		}
	}

	if (isNaN(totalRepayment)) {
		return 0; // 或者其他你认为合适的默认值
	}

	return totalRepayment.toFixed(2);
});

const changeVal = () => {
	if (loanMethod.value === 'benxi' || loanMethod.value === 'benjin') {
		updateTableData(); // 更新表格数据
	}
};

const calculateMonthlyPayment = () => {
	// 将年利率转换为月利率
	const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;

	// 贷款总额
	const loanAmountValue = parseFloat(totalMoney.value);

	// 贷款期限（以月为单位）
	const loanTermMonths = parseInt(years.value) * 12;

	// 计算月还款额
	let monthlyPayment;
	if (loanMethod.value === 'benxi') {
		// 等额本息计算公式
		monthlyPayment = (loanAmountValue * monthlyInterestRate) / (1 - Math.pow(1 + monthlyInterestRate, -loanTermMonths));
	} else if (loanMethod.value === 'benjin') {
		// 等额本金计算公式
		monthlyPayment = loanAmountValue / loanTermMonths + loanAmountValue * monthlyInterestRate;
	}

	// 更新月还款的ref
	months.value = monthlyPayment.toFixed(2);
};

// 监听相关数据的变化，一旦变化就重新计算月还款
watch([totalMoney, years, interest, loanMethod, interestRate], () => {
	calculateMonthlyPayment();
});

function handleInput() {
	// 只允许输入数字小于30大于0的数字
	if (years.value > 30) {
		years.value = 30;
	}
	if (years.value < 1) {
		years.value = 1;
	}
}

const Type = ref('');
onMounted(() => {});

const cloumns = [
	{
		label: '月数',
		prop: 'month',
	},
	{
		label: '期初余额（元）',
		prop: 'initialbalance',
	},
	{
		label: '偿还本息（元）',
		prop: 'Monthlypay',
	},

	{
		label: '利息（元）',
		prop: 'accrual',
	},
	{
		label: '本金（元）',
		prop: 'capital',
	},
	{
		label: '剩余本金（元）',
		prop: 'terminal',
	},
];
const tableData = ref([]);

const updateTableData = () => {
	tableData.value = [];
	const loanAmountValue = parseFloat(totalMoney.value);
	const yearsVal = parseInt(years.value);
	const totalMonths = yearsVal * 12;

	if (isNaN(loanAmountValue) || isNaN(yearsVal)) {
		return; // 处理无效数据
	}

	// 初始化剩余本金
	let remainingPrincipal = loanAmountValue;

	if (loanMethod.value === 'benxi') {
		// 等额本息的表格数据
		for (let i = 1; i <= totalMonths; i++) {
			const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;

			// 计算每月还款额（等额本息方式）
			const monthlyPaymentBenxi = (loanAmountValue * monthlyInterestRate) / (1 - Math.pow(1 + monthlyInterestRate, -totalMonths));

			// 计算利息
			const interestAccrualBenxi = remainingPrincipal * monthlyInterestRate;

			// 计算本金还款
			const capitalRepaymentBenxi = monthlyPaymentBenxi - interestAccrualBenxi;

			// 计算期末余额
			remainingPrincipal -= capitalRepaymentBenxi;
			const rowDataBenxi = {
				month: i,
				initialbalance: loanAmountValue.toFixed(2),
				// initialbalance: Number(monthlyPaymentBenxi.toFixed(2)) + Number(remainingPrincipal.toFixed(2)),
				Monthlypay: monthlyPaymentBenxi.toFixed(2),
				capital: capitalRepaymentBenxi.toFixed(2),
				accrual: interestAccrualBenxi.toFixed(2),
				terminal: Number(remainingPrincipal.toFixed(2)) === -0 ? 0 : remainingPrincipal.toFixed(2),
			};
			// 将数据推入 tableData 数组
			tableData.value.push(rowDataBenxi);
		}
	} else {
		// 等额本金的还款数据
		for (let i = 1; i <= totalMonths; i++) {
			const totalMoneyVal = parseFloat(totalMoney.value);
			const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;
			const monthbenjin = totalMoneyVal / totalMonths; // 每月本金

			// 计算利息
			const interestAccrualBenjin = remainingPrincipal * monthlyInterestRate;

			// 计算本金还款
			const capitalRepaymentBenjin = monthbenjin;

			// 计算剩余本金
			remainingPrincipal -= monthbenjin;

			const rowDataBenjin = {
				month: i,
				initialbalance: loanAmountValue.toFixed(2),
				// initialbalance: Number((capitalRepaymentBenjin + interestAccrualBenjin).toFixed(2)) + Number(remainingPrincipal.toFixed(2)),
				Monthlypay: (capitalRepaymentBenjin + interestAccrualBenjin).toFixed(2),
				capital: capitalRepaymentBenjin.toFixed(2),
				accrual: interestAccrualBenjin.toFixed(2),
				terminal: Number(remainingPrincipal.toFixed(2)) === -0 ? 0 : remainingPrincipal.toFixed(2),
			};
			tableData.value.push(rowDataBenjin);
		}
	}
};

watch([years, totalMoney, interest, loanMethod, interest, interestRate], () => {
	updateTableData();
});
// 通过循环生成基准利率的选项
const interestRates = [
	{
		label: '不打折',
		value: '1',
	},
	{
		label: '9折',
		value: '0.9',
	},
	{
		label: '8.5折',
		value: '0.85',
	},
	{
		label: '8折',
		value: '0.8',
	},
];
const province = ref('');
const city = ref('');
const handleChange = (val) => {
	province.value = val[1];
	city.value = val[0];
	business.value = null;
	building.value = null;
	selectedItem.value = null;
	loanAmount.value = '';
	totalMoney.value = '';
	getBusiness();
};
// 根据城市获取商圈
const businessList = ref([]);
const getBusiness = async () => {
	let queryParams = {
		district: province.value,
		city: city.value,
	};
	const res = await getBusinessDistrictList(queryParams);
	businessList.value = res.data.rows;
	buildingTypes.value = res.data.rows.map((item) => item.businessDistrictName);
};

const Searchbuild = (val) => {
	building.value = null;
	loanAmount.value = '';
	totalMoney.value = '';
	getBuilding(val);
};
// 根据商圈获取具体楼宇
const buildingList = ref([]);
const getBuilding = async (val) => {
	let queryParams = {
		businessDistrictId: val,
	};
	const res = await getBuildingBusinessDistrictList(queryParams);
	buildingList.value = res.data.rows;
};

const value1 = ref([]);

const buildingTypes = ref();
const selectedItem = ref(null);

const handleItemChange = (val) => {
	getBuildingPrice(val, 1);
};
// 根据楼宇获取成交价格
const buildingPrice = ref('');
const getBuildingPrice = async (val, type) => {
	let queryParams = {
		buildingUniqueCode: val + '',
		dealType: Type.value || 2,

		issueDateStart: formatDate(value1?.value[0]),
		issueDateEnd: formatDate(value1?.value[1]),
	};
	const res = await getBuildingSoldPrice(queryParams);

	buildingTypes.value = res.data.dealPrice;
	buildingPrice.value = res.data.dealPrice;

	// 如果存在面积,则根据面积计算总价
	if (loanAmount.value && type) {
		loanAmountInput(loanAmount.value);
	}
};
const baseRate = ref('');

const getfinData = async () => {
	await financeDataList()
		.then((res) => {
			baseRate.value = res.result.lpr;
		})
		.catch((err) => {
			console.log(err);
		});
};
const loanAmountInput = (value) => {
	if (buildingPrice.value != '') {
		totalMoney.value = (value * buildingPrice.value).toFixed(2);
	}
};
const formatDate = (date) => {
	if (!date) return '';
	const year = date.getFullYear();
	const month = (date.getMonth() + 1).toString().padStart(2, '0');
	const day = date.getDate().toString().padStart(2, '0');
	return `${year}-${month}-${day}`;
};
getfinData();

// 监听 selectedItem 的变化
watch(selectedItem, () => {
	handleItemChange(selectedItem);
});
</script>

<style lang="less" scoped>
.common-layout {
	width: 100%;
	height: 100%;
	box-sizing: border-box;

	.tab_box {
		width: 100%;
		height: 56px;
		padding: 0 15px;
		box-sizing: border-box;
		font-size: 14px;
		font-weight: 600;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		background-color: rgba(255, 255, 255, 1);

		.title {
			margin-right: 15px;
		}

		.tab {
			width: 60px;
			height: 56px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
		}

		.tabAct {
			width: 60px;
			height: 56px;

			&::after {
				content: '';
				width: 24px;
				height: 3px;
				position: absolute;
				bottom: 0;
				background-color: rgba(3, 93, 255, 1);
			}
		}
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;
		background-color: rgba(245, 245, 245, 1);

		.card_box,
		.computer_box,
		.plain_box {
			width: 100%;
			height: 258px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: wrap;
			background-color: rgba(255, 255, 255, 1);
			border-radius: 6px;
			padding: 50px 35px 35px 35px;
			margin-bottom: 10px;
			box-sizing: border-box;
			position: relative;

			.tag_box {
				width: auto;
				height: 16px;
				position: absolute;
				left: 0;
				top: 20px;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
			}

			.select_box {
				width: 33%;
				padding: 0 10px;
				box-sizing: border-box;
				::v-deep .el-date-range-picker__time-header .el-date-range-picker__editors-wrap > :nth-child(2) {
					display: none;
				}
				::v-deep .el-date-range-picker__time-header .el-date-range-picker__editors-wrap .el-date-range-picker__time-picker-wrap {
					width: 280px;
					padding: 0 10px;
				}
				.label_ {
					font-size: 12px;
					margin-bottom: 10px;
					font-weight: bold;
				}
			}
		}
		.plain_box {
			width: 100%;
			height: 400px;
			margin-bottom: 0px !important;
			&::v-deep .el-table--fit {
				border-radius: 8px;
			}

			&::v-deep .el-table th {
				background-color: rgba(245, 245, 245, 1);
			}
		}
	}

	.amount {
		width: 100%;
		height: auto;
		background-color: rgba(255, 255, 255, 1);
	}
}

.item {
	padding: 18px 0;
}

.box-card {
	width: 100%;
	height: 99%;
}
</style>
