export interface ObjectType {
  [key: string]: any;
}
export interface TableData {
  data: Array<any>;
  loading: boolean;
  treeProps?: any;
  total?: number;
  currentPage?: number;
  layout?: string;
  pageSize?: number;
  pageSizes?: number[];
  background?: boolean;
  small?: boo;
  sum?: Array<any>;
}
export type ColumnType = ObjectType;
export type ColumnIndex = number;
export type RowIndex = number;
export type RowType = ObjectType;
export interface RowObjectType {
  row: RowType;
  rowIndex: RowIndex;
}
export interface ColumnObjectType {
  column: ColumnType;
  columnIndex: ColumnIndex;
}

export interface ColumnProps {
  slot?: string;
  type?: string;
  index?: (index: number) => void | number;
  label?: string;
  dataName?: string;
  width?: string | number;
  minWidth?: string | number;
  fixed?: string | boolean;
  renderHeader?: ({ column, $index }: ColumnObjectType) => void;
  sortable?: boolean | string;
  sortMethod?: (a: ObjectType, b: ObjectType) => void;
  formatter?: (
    row: ObjectType,
    column: ObjectType,
    cellValue: string | number,
    index: number
  ) => void;
  showOverflowTooltip?: boolean;
  align?: string;
  headerAlign?: string;
  className?: string;
  selectable?: (row: RowObjectType, index: RowIndex) => void;
  reserveSelection?: boolean;
  child?: ColumnProps[];
  isShow?: boolean;
  childSlots?: string[];
  num?: string[];
}
//ColumnProps属性：
// slot,不声明时，直接带入dataName中数据，内容不可操作，亦无须声明插槽
// slot声明时，需要放入插槽，dataName无法显示数据，插槽样式与内容由插槽标签操纵
