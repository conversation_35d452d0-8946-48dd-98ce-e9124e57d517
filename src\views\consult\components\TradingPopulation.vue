<template>
	<div class="content">
		<div>
			<el-row :gutter="20">
				<el-col :span="4">
					<el-cascader
						:key="myCascader"
						placeholder="请选择城市"
						@change="handleChange"
						:options="$vuexStore.state.cityArray"
						:props="{ value: 'label' }"
					>
					</el-cascader>
				</el-col>
				<el-col :span="4">
					<el-select v-model="buildValue" placeholder="选择资产类型" style="width: 240px">
						<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" />
					</el-select>
				</el-col>
				<el-col :span="4">
					<el-input v-model="keyword" placeholder="请输入关键字"></el-input>
				</el-col>
				<el-col :span="5"></el-col>
				<el-col :span="4">
					<el-button @click="search" type="primary">查询</el-button>
					<el-button @click="reset" type="primary">重置</el-button>
				</el-col>
			</el-row>
			<el-table :data="tableData" style="width: 80%" @selection-change="handleSelectionChange" stripe>
				<el-table-column type="selection" width="55" ref="multipleTableRef" />
				<el-table-column prop="name" label="名字" />
				<el-table-column prop="de_type" label="资产类型" />
			</el-table>
			<div style="display: flex; justify-content: space-around; align-items: center">
				<el-pagination @current-change="pageChange" :current-page="currentPage" small background layout="prev, pager, next" :total="total" />
				<el-button style="margin-top: 20px" size="small" type="primary" @click="comparison">对比</el-button>
			</div>
		</div>
		<div v-if="leftData" style="margin-top: 15px">
			<el-row :gutter="20">
				<el-col :span="11">
					<el-text class="Title" tag="p">{{ leftData.building_name }}</el-text>
					<el-row :gutter="20">
						<el-col :span="6">
							<el-descriptions title="关键事实" column="1" direction="horizontal">
								<el-descriptions-item label="总人口">{{ leftData?.total_population }}</el-descriptions-item>
								<el-descriptions-item label="年龄中位数">{{ leftData?.age_mid }}</el-descriptions-item>
								<el-descriptions-item label="家庭数">{{ leftData?.households_num }}</el-descriptions-item>
								<el-descriptions-item label="人均支配(万元)">{{ leftData?.disposable_income }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="教育" column="1">
								<el-descriptions-item label="高中以下">{{ leftData?.educational_level1 }}</el-descriptions-item>
								<el-descriptions-item label="高中">{{ leftData?.educational_level2 }}</el-descriptions-item>
								<el-descriptions-item label="专科">{{ leftData?.educational_level3 }}</el-descriptions-item>
								<el-descriptions-item label="学士/硕士/博士">{{ leftData?.educational_level4 }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="就业情况" column="1">
								<el-descriptions-item label="白领">{{ leftData?.white_collar }}</el-descriptions-item>
								<el-descriptions-item label="蓝领">{{ leftData?.blue_collar }}</el-descriptions-item>
								<el-descriptions-item label="公共事业">{{ leftData?.public_utilities }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="年龄情况" column="1">
								<el-descriptions-item label="0-14岁">{{ leftData?.age_range1 }}</el-descriptions-item>
								<el-descriptions-item label="15-64岁">{{ leftData?.age_range2 }}</el-descriptions-item>
								<el-descriptions-item label="65岁以上">{{ leftData?.age_range3 }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
					</el-row>
				</el-col>
				<el-divider border-style="solid" direction="vertical" style="height: 200px; margin-right: 60px" />
				<el-col :span="11">
					<el-text class="Title" tag="p">{{ rightData.building_name }}</el-text>
					<el-row :gutter="20">
						<el-col :span="6">
							<el-descriptions title="关键事实" column="1" direction="horizontal">
								<el-descriptions-item label="总人口">{{ rightData?.total_population }}</el-descriptions-item>
								<el-descriptions-item label="年龄中位数">{{ rightData?.age_mid }}</el-descriptions-item>
								<el-descriptions-item label="家庭数">{{ rightData?.households_num }}</el-descriptions-item>
								<el-descriptions-item label="人均支配(万元)">{{ rightData?.disposable_income }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="教育" column="1">
								<el-descriptions-item label="高中以下">{{ rightData?.educational_level1 }}</el-descriptions-item>
								<el-descriptions-item label="高中">{{ rightData?.educational_level2 }}</el-descriptions-item>
								<el-descriptions-item label="专科">{{ rightData?.educational_level3 }}</el-descriptions-item>
								<el-descriptions-item label="学士/硕士/博士">{{ rightData?.educational_level4 }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="就业情况" column="1">
								<el-descriptions-item label="白领">{{ rightData?.white_collar }}</el-descriptions-item>
								<el-descriptions-item label="蓝领">{{ rightData?.blue_collar }}</el-descriptions-item>
								<el-descriptions-item label="公共事业">{{ rightData?.public_utilities }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="年龄情况" column="1">
								<el-descriptions-item label="0-14岁">{{ rightData?.age_range1 }}</el-descriptions-item>
								<el-descriptions-item label="15-64岁">{{ rightData?.age_range2 }}</el-descriptions-item>
								<el-descriptions-item label="65岁以上">{{ rightData?.age_range3 }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
					</el-row>
				</el-col>
			</el-row>
		</div>
	</div>
</template>
<script setup>
import { ref } from 'vue';
import http from '@/utils/http';
import { ElMessage } from 'element-plus';
const city = ref('');
const county = ref('');
const myCascader = ref(0);
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '保障房', '综合市场', '不限'];
const buildValue = ref('');
const keyword = ref('');
const tableData = ref([]);
const currentPage = ref(1);
const total = ref('');
const handleChange = (val) => {
	console.log(val, 'ss');
	city.value = val[0];
	county.value = val[1];
};
const reset = () => {
	myCascader.value++;
	city.value = '';
	county.value = '';
	buildValue.value = '';
	keyword.value = '';
	currentPage.value = 1;
};
const search = async () => {
	const res = await http.get('/api/a_maps.do?act=getMap', {
		params: { city: city.value, county: county.value, de_type: buildValue.value, keywords: keyword.value, currentPage: currentPage.value },
	});
	tableData.value = res.list;
	total.value = res.total;
	console.log(tableData.value, 'res');
};
const multipleSelection = ref([]);
let leftData = ref();
let rightData = ref();
const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};

const comparison = async () => {
	console.log(multipleSelection.value, 'dsad');
	if (multipleSelection.value.length !== 2) {
		ElMessage.warning('只能选择两个！！！');
		return;
	}
	const idarray = [];
	multipleSelection.value.map((item) => {
		idarray.push(item.id);
	});
	console.log(idarray, 'idarray');
	const res = await http.get('/api/a_maps.do?act=populationContrast', {
		params: { ids: idarray.join(',') },
	});
	leftData.value = res.list[0];
	rightData.value = res.list[1];
};
const pageChange = (val) => {
	currentPage.value = val;
	search();
};
</script>

<style lang="less" scoped>
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}
.content {
	margin: 20px 0 0 20px;
	padding: 20px 0 0 20px;
}

.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	text-align: center;
	margin-bottom: 15px;
}
</style>
