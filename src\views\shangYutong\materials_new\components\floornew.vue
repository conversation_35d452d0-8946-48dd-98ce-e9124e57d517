<template>
	<div class="comparison_box">
		<div class="common_wrap">
			<div class="left_empty_wrap" v-if="tableDataLeft.length == 0">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择资产
				</arco-button>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0" class="left_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('left')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataLeft"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip >
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0 && tableDataRight.length == 0" class="right_empty_wrap">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择对比资产
				</arco-button>
			</div>
			<div v-if="tableDataRight && tableDataRight.length > 0" class="right_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('right')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataRight"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip >
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
		</div>
		<div class="chart_wrap">
			<div class="header_wrap">
				<span class="line"></span>
				<span class="title">户型图</span>
				<span class="summary_desc"> 目前有{{ planCount?.totalBuildings }}栋楼宇的{{ planCount?.totalImages }}张户型图 </span>
			</div>
			<div class="content_wrap">
				<div class="thumbnail_wrap" v-if="!$utils.isEmpty(leftData)">
					<div class="view_wrap">
						<view
							class="img_wrap"
							@click="changeView('left', index)"
							:class="{ img_wrap_active: leftViewIndex == index }"
							v-for="(item, index) in leftData.imgList"
						>
							<img class="view_img" :src="item" />
						</view>
					</div>
					<div class="view_content">
						<img class="view_img" :src="leftData.imgList[leftViewIndex]" @click="imgView('left')" />
						<div class="view_btn">
							<div class="view"><IconDragArrow :rotate="45" class="icon_btn" @click="imgView('left')" /></div>
							<!-- <div class="download"><IconDownload class="icon_btn" @click="imgDown('left')" /></div> -->
						</div>
					</div>
				</div>
				<div class="thumbnail_wrap" v-if="!$utils.isEmpty(rightData)">
					<div class="view_wrap">
						<view
							class="img_wrap"
							@click="changeView('right', index)"
							:class="{ img_wrap_active: rightViewIndex == index }"
							v-for="(item, index) in rightData.imgList"
						>
							<img class="view_img" :src="item" />
						</view>
					</div>
					<div class="view_content">
						<img class="view_img" :src="rightData.imgList[rightViewIndex]" @click="imgView('right')" />
						<div class="view_btn">
							<div class="view"><IconDragArrow :rotate="45" class="icon_btn" @click="imgView('right')" /></div>
							<!-- <div class="download"><IconDownload class="icon_btn" @click="imgDown('right')" /></div> -->
						</div>
					</div>
				</div>
				<div class="empty_wrap" v-if="$utils.isEmpty(leftData) && $utils.isEmpty(rightData)">
					<img :src="empty" />
					<div>暂无数据</div>
				</div>
			</div>
		</div>
	</div>
	<buildSelect
		dialogType="house"
		v-model="dialogTableVisible"
		:selectedData="multipleSelection"
		:maxSelectNum="2"
		@confirm="handleBuildConfirm"
	></buildSelect>
	<arco-image-preview-group
		v-if="leftData && Object.keys(leftData).length > 0"
		v-model:visible="leftImgPreviewShow"
		v-model:current="leftImgPreviewCurrent"
		infinite
		:srcList="leftData.imgList"
	/>
	<arco-image-preview-group
		v-if="rightData && Object.keys(rightData).length > 0"
		v-model:visible="rightImgPreviewShow"
		v-model:current="rightImgPreviewCurrent"
		infinite
		:srcList="rightData.imgList"
	/>
	<!-- <buildSelect key="single_left" v-model="dialogSingleLeftVisible" :maxSelectNum="1" @confirm="handleBuildLeftConfirm"></buildSelect>
	<buildSelect key="single_right" v-model="dialogSingleRightVisible" :maxSelectNum="1" @confirm="handleBuildRightConfirm"></buildSelect> -->
</template>

<script setup>
import { ref, onMounted, computed, nextTick, getCurrentInstance } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import { getFloorNew, getDictList, getFloorPlanCount } from '@/api/syt.js';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import { IconPlus, IconDragArrow, IconDownload } from '@arco-design/web-vue/es/icon';
import buildSelect from '@/component/buildSelect/index.vue';
import { downloadFile } from '@/utils/index';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
const emit = defineEmits(['handleBuildingId']);
const { proxy } = getCurrentInstance();
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const loading = ref();
const dialogTableVisible = ref(false); //对话框显示
const dialogSingleLeftVisible = ref(false); //对话框显示
const dialogSingleRightVisible = ref(false); //对话框显示
const rate = ref([]);
const buildingTypes = ref([]);
const multipleSelection = ref([]);
const leftData = ref({});
const rightData = ref({});
const tableDataLeft = ref([]);
const tableDataRight = ref([]);
const tableColumns = [
	{
		title: '资产名称',
		dataIndex: 'buildingName',
		width: '150',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '资产类型',
		dataIndex: 'buildingType',
		width: '100',
	},
	{
		title: '地址',
		dataIndex: 'street',
		width: '300',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '建筑面积',
		dataIndex: 'buildingSize',
		width: '100',
	},
	{
		title: '维护情况',
		dataIndex: 'maintenance',
		width: '100',
	},
	{
		title: '单价',
		dataIndex: 'absoluteValue',
		width: '100',
	},
];
const planCount = ref({});
const leftViewIndex = ref(0);
const rightViewIndex = ref(0);
const leftImgPreviewShow = ref(false);
const leftImgPreviewCurrent = ref(0);
const leftImgPreviewList = ref([]);
const rightImgPreviewShow = ref(false);
const rightImgPreviewCurrent = ref(0);
const rightImgPreviewList = ref([]);

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
	handlePlanCount();
});
function imgView(type) {
	if (type == 'left') {
		leftImgPreviewShow.value = true;
		leftImgPreviewCurrent.value = leftViewIndex.value;
	} else {
		rightImgPreviewShow.value = true;
		rightImgPreviewCurrent.value = rightViewIndex.value;
	}
}
async function downloadImage(url) {
	try {
		const imageUrl = url;
		const response = await fetch(imageUrl);
		console.log('🚀 ~ downloadImage ~ response:', response);

		// 检查响应状态和内容类型
		if (!response.ok) throw new Error('网络请求失败');
		if (!response.headers.get('Content-Type').startsWith('image/')) throw new Error('非图片类型资源');

		const blob = await response.blob();
		const downloadLink = document.createElement('a');
		downloadLink.href = URL.createObjectURL(blob);
		downloadLink.download = 'image.webp'; // 指定文件名及扩展名
		document.body.appendChild(downloadLink);
		downloadLink.click();
		document.body.removeChild(downloadLink);
		URL.revokeObjectURL(downloadLink.href); // 释放内存
	} catch (error) {
		console.error('下载失败:', error);
		alert('下载失败，请检查控制台日志');
	}
}
function downloadImageLegacy(url, fileName) {
	const xhr = new XMLHttpRequest();
	// xhr.open('GET', 'https://img0.baidu.com/it/u=3607865720,773719330&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=750', true);
	xhr.open('GET', url, true);
	xhr.responseType = 'blob';
	xhr.onload = function () {
		if (xhr.status === 200) {
			const blob = xhr.response;
			const link = document.createElement('a');
			link.href = URL.createObjectURL(blob);
			link.download = fileName || 'img.webp';
			link.click();
			URL.revokeObjectURL(link.href);
		}
	};
	xhr.send();
}
function imgDown(type) {
	if (type == 'left') {
		downloadImageLegacy(leftData.value.imgList[leftViewIndex.value], `${tableDataLeft.value[0].buildingName}户型图.webp`);
	} else {
		downloadImageLegacy(rightData.value.imgList[rightViewIndex.value], `${tableDataRight.value[0].buildingName}户型图.png`);
	}
}

//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	console.log('🚀 ~ handleAssetsIds ~ obj:', obj);
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getFloorNew({
		buildingIds: obj.ids,
	})
		.then((res) => {
			console.log('🚀 ~ .then ~ res:', res);
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			// 重置所有数据
			tableDataLeft.value = [];
			tableDataRight.value = [];
			leftData.value = null;
			rightData.value = null;
			multipleSelection.value = obj.arr;
			// 同步更新数据和表格
			if (multipleSelection.value.length === 1) {
				if (res.data[0] && res.data[0]?.floorPlanUrls) {
					leftData.value = res.data[0];
					leftData.value.imgList = leftData.value.floorPlanUrls.split(',');
					leftData.value.imgList = leftData.value.imgList.map((item) => `https://static.bbzhun.com/${item}`);
				} else {
					leftData.value = null;
				}
				tableDataLeft.value = [multipleSelection.value[0]];
			} else if (multipleSelection.value.length === 2) {
				if (res.data[0] && res.data[0]?.floorPlanUrls) {
					leftData.value = res.data[0];
					leftData.value.imgList = leftData.value.floorPlanUrls.split(',');
					leftData.value.imgList = leftData.value.imgList.map((item) => `https://static.bbzhun.com/${item}`);
				} else {
					leftData.value = null;
				}
				if (res.data[1] && res.data[1]?.floorPlanUrls) {
					rightData.value = res.data[1];
					rightData.value.imgList = rightData.value.floorPlanUrls.split(',');
					rightData.value.imgList = rightData.value.imgList.map((item) => `https://static.bbzhun.com/${item}`);
				} else {
					leftData.value = null;
					rightData.value = null;
				}
				tableDataLeft.value = [multipleSelection.value[0]];
				tableDataRight.value = [multipleSelection.value[1]];
			}
			loading.value.close();
		})

		.catch((err) => {
			console.log(err, 'err');
		});
}

const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		leftData.value = null;
		rightData.value = null;
		emit('handleBuildingId', { ids: null, arr: [] });
		return;
	}
	let ids = multipleSelection.value.map((item) => item.id).join(',');
	getFloorNew({
		// buildingIds: '1833699716997005315,1833699717017976833',
		buildingIds: ids,
	})
		.then((res) => {
			// 重置所有数据
			tableDataLeft.value = [];
			tableDataRight.value = [];
			leftData.value = null;
			rightData.value = null;
			// 同步更新数据和表格
			if (multipleSelection.value.length === 1) {
				if (res.data[0] && res.data[0]?.floorPlanUrls) {
					leftData.value = res.data[0];
					leftData.value.imgList = leftData.value.floorPlanUrls.split(',');
					leftData.value.imgList = leftData.value.imgList.map((item) => `https://static.bbzhun.com/${item}`);
				} else {
					leftData.value = null;
				}
				tableDataLeft.value = [multipleSelection.value[0]];
			} else if (multipleSelection.value.length === 2) {
				if (res.data[0] && res.data[0]?.floorPlanUrls) {
					leftData.value = res.data[0];
					leftData.value.imgList = leftData.value.floorPlanUrls.split(',');
					leftData.value.imgList = leftData.value.imgList.map((item) => `https://static.bbzhun.com/${item}`);
					// leftData.value.imgList = leftData.value.imgList.map((item) => `https://static.biaobiaozhun.com/${item}`);
				} else {
					leftData.value = null;
				}
				if (res.data[1] && res.data[1]?.floorPlanUrls) {
					rightData.value = res.data[1];
					rightData.value.imgList = rightData.value.floorPlanUrls.split(',');
					rightData.value.imgList = rightData.value.imgList.map((item) => `https://static.bbzhun.com/${item}`);
				} else {
					leftData.value = null;
					rightData.value = null;
				}
				tableDataLeft.value = [multipleSelection.value[0]];
				tableDataRight.value = [multipleSelection.value[1]];
			}
			emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
			dialogTableVisible.value = false;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const removePercentSigns = (str) => {
	return Number(str);
};
function handlePlanCount() {
	getFloorPlanCount().then((res) => {
		if (res.code === 200) {
			planCount.value = res.data;
		}
	});
}
// 获取字典
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();
function changeView(type, index) {
	if (type == 'left') {
		leftViewIndex.value = index;
	} else {
		rightViewIndex.value = index;
	}
}
function handleBuildConfirm(data) {
	multipleSelection.value = data;
	save();
}
function handleBuildLeftConfirm(data) {
	multipleSelection.value[0] = data[0];
	save();
}
function handleBuildRightConfirm(data) {
	multipleSelection.value[1] = data[0];
	save();
}
function clear(type) {
	if (type == 'left') {
		tableDataLeft.value = [];
		multipleSelection.value.shift();
	} else {
		tableDataRight.value = [];
		multipleSelection.value.pop();
	}
	save();
}
</script>
<style lang="less" scoped>
.comparison_box {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.common_wrap {
		padding: 20px 16px;
		background-color: #fff;
		display: flex;
		gap: 16px;
		border-radius: 0px 4px 4px 4px;
		.left_empty_wrap,
		.left_content_wrap,
		.right_empty_wrap,
		.right_content_wrap {
			flex: 1;
		}
		.left_empty_wrap,
		.right_empty_wrap {
			border: 1px solid #e5e6eb;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 4px;
			padding: 54px 0;
			.icon {
				width: 64px;
				height: 64px;
			}
		}
		.title_wrap {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;
		}
		.table_wrap {
		}
		.desc_wrap {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20px;
			border-radius: 4px;
			overflow: hidden;
			margin-top: 16px;
			.desc {
				max-width: 660px;
				z-index: 9;
				font-size: 14px;
				font-weight: 600;
				line-height: 22px;
				color: #1d2129;
			}
			.copy {
				z-index: 9;
				padding: 5px 16px;
				border: 1px solid #1868f1;
				color: #1868f1;
				background: #e8f3ff;
				border-radius: 4px;
				cursor: pointer;
			}
			.bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}
	}
	.chart_wrap {
		flex: 1;
		border-radius: 4px;
		margin-top: 16px;
		padding: 20px 16px 16px 16px;
		background-color: #fff;
		.header_wrap {
			display: flex;
			align-items: center;
			.line {
				width: 4px;
				height: 14px;
				background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
				border-radius: 4px;
			}
			.title {
				color: #1d2129;
				font-size: 20px;
				font-weight: 600;
				margin-left: 8px;
				margin-right: 12px;
				line-height: 28px;
			}
			.summary_desc {
				color: #4e5969;
				font-size: 14px;
				font-weight: 400;
			}
		}
		.content_wrap {
			display: flex;
			margin-top: 16px;
			gap: 16px;
			.thumbnail_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				.view_wrap {
					display: flex;
					gap: 8px;
					margin-bottom: 8px;
					.img_wrap {
						display: flex;
						width: 124px;
						height: 80px;
						border: 1px solid #e5e6eb;
						border-radius: 4px;
						padding: 8px 5px;
						box-sizing: border-box;
						cursor: pointer;
						.view_img {
							width: 100%;
							height: 100%;
						}
					}
					.img_wrap_active {
						border: 2px solid #1868f1;
					}
				}
				.view_content {
					width: 100%;
					height: 516px;
					border: 1px solid #e5e6eb;
					border-radius: 4px;
					padding: 43px 16px;
					box-sizing: border-box;
					overflow: hidden;
					position: relative;
					.view_img {
						width: 100%;
						height: 100%;
					}
					.view_btn {
						position: absolute;
						top: 16px;
						right: 20px;
						display: flex;
						gap: 8px;
						.view,
						.download {
							width: 24px;
							height: 24px;
							border-radius: 50%;
							background-color: #e8f3ff;
							display: flex;
							align-items: center;
							justify-content: center;
							cursor: pointer;
							.icon_btn {
								color: #1868f1;
							}
						}
					}
				}
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				height: 300px;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}

	.arco-btn-size-medium {
		border-radius: 4px;
	}
}
</style>
