<template>
	<div class="mian">
		<div class="search">
			<el-row :gutter="20">
				<el-col :span="6" justify="center" align="center"
					><div class="grid-content ep-bg-purple" />
					<el-cascader placeholder="请选择城市" size="large" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }">
					</el-cascader>
				</el-col>
				<el-col :span="6"
					><div class="grid-content ep-bg-purple" />
					<el-select style="margin-left: 20px; width: 220px" v-model="buildingTypesValue" placeholder="全部资产" size="large">
						<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" /> </el-select
				></el-col>
				<el-col :span="6"
					><div class="grid-content ep-bg-purple" />
					<el-input v-model="essential" placeholder="请输入关键字" size="large" style="width: 220px"></el-input
				></el-col>
				<el-col :span="6"
					><div class="grid-content ep-bg-purple" />
					<el-button type="primary" size="large" @click="initData()">搜索</el-button>
				</el-col>
			</el-row>
		</div>
		<div class="content">
			<el-card v-for="item in data" :key="item.id" class="box-card" @click="handle(item.propertyOwner)">
				<div class="text item">{{ item.propertyOwner }}</div>
			</el-card>
		</div>
		<el-pagination
			layout="prev, pager, next"
			:total="total"
			v-model:page-size="size"
			v-model:current-page="currentPage"
			@current-change="initData()"
			style="justify-content: center"
		/>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http';
import { provinceAndCityData } from 'element-china-area-data';
import { useRouter } from 'vue-router';
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '数据中心', '保障房', '农贸市场'];
const buildingTypesValue = ref('');
const data = ref([]);
const currentPage = ref(1);
const selectedOptions = ref([]);
const essential = ref('');
const router = useRouter();
const size = ref(30);
const handle = (item) => {
	console.log(item);
	router.push({
		path: '/trading/peoperty',
		query: { id: item },
	});
};
onMounted(() => {
	initData();
});
const province = ref('');
const city = ref('');
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const total = ref(0);
const initData = async () => {
	const res = await http.get('/api/a_new_jiaoyi.do?act=getMapList', {
		params: {
			currentPage: currentPage.value,
			province: province.value,
			city: city.value,
			de_type: buildingTypesValue.value,
			keywords: essential.value,
		},
	});
	data.value = res.list;
	total.value = res.total;
	console.log(res, 'ss');
};
</script>
<style lang="less" scoped>
.mian {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	text-align: center;
	.content {
		padding: 40px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-wrap: wrap;
		.box-card {
			width: 30%;
			height: 100px;
			margin: 20px;
			border-radius: 25px;
		}
	}
}
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 36px;
}
.text {
	font-size: 14px;
}

.item {
	padding: 18px 0;
}

.box-card {
	width: 480px;
}
</style>
