<template>
	<div class="shangauto_container">
		<div class="content_wrap" @scroll="handleScroll">
			<div class="logo_wrap">
				<el-image :src="logo"></el-image>
			</div>
			<div class="flag_wrap" :class="{ hidden: isHidden }">
				<div class="title">投资分析 洞见未来</div>
				<div class="desc">60000+商业地产专业报告供您选择</div>
			</div>
			<div class="build_select_wrap no-select">
				<div class="placeholder" v-if="!selectBuild">选择您要查询的资产</div>
				<div v-else class="select_item">
					<span>{{ selectBuild.buildingName }}</span>
					<el-icon style="cursor: pointer" size="16" color="#1E5AD2" @click="handleBuildClose"><Close /></el-icon>
				</div>
				<div class="btn_wrap" @click="handleSelectBuild">
					<el-icon color="#fff" size="20"><Plus /></el-icon>
					<div class="text">{{ selectBuild ? '重新选择' : '添加' }}</div>
				</div>
			</div>
			<!-- @mouseleave="onMouseLeave" -->
			<div class="report_wrap" v-if="reportData && reportData.length > 0">
				<div class="single_wrap" :class="{ active: hoverIndex === 1 }" @mouseenter="onMouseEnter(1)">
					<div class="title">单项报告</div>
					<div class="desc">为您提供多种单项报告类型选择</div>
					<div class="tag">累计下载1000+次</div>
				</div>
				<div
					class="analyze_wrap"
					:class="{ active: hoverIndex === 2, grayscale: !reportData.find((item) => item.reportType == 'invest_analyze').availableState }"
					@mouseenter="onMouseEnter(2)"
				>
					<div class="title">投资分析报告</div>
					<div class="desc">资产数据分析，投资风险评估</div>
				</div>
				<div
					class="package_wrap"
					:class="{ active: hoverIndex === 3, grayscale: !reportData.find((item) => item.reportType == 'combo_report').availableState }"
					@mouseenter="onMouseEnter(3)"
				>
					<div class="title">报告套餐</div>
					<div class="desc">投资分析报告+全套单项报告</div>
				</div>
			</div>
			<div class="report_select_wrap">
				<div class="report_list_wrap" v-if="hoverIndex === 1">
					<!-- @click="handleSelectReportType(item)" -->
					<div
						class="report_list_item"
						:style="{ cursor: item.availableState ? 'pointer' : 'not-allowed' }"
						v-for="item in reportData.filter((item) => ['population', 'populationtwo', 'populationthree', 'business'].includes(item.reportType))"
						:key="item.reportType"
						@mouseenter="handleHoverReportType(item)"
						@mouseleave="handleHideReportType(item)"
					>
						<el-image style="width: 40px; height: 40px" :src="reportTypeImgs[item.reportType]"></el-image>
						<span>{{ item.name }}</span>
						<div class="hovered" :class="{ hidden: Object.keys(hoverReportItem).length == 0 ? true : hoverReportItem.reportType != item.reportType }">
							<div class="btn" @click="handleReportDemo">
								<el-icon size="16" class="icon"><VideoPlay /></el-icon>演示Demo
							</div>
						</div>
					</div>
				</div>
				<!-- <div
					v-if="hoverIndex === 1"
					@click="handleReportDemo"
					class="demo_tips"
					:style="{ cursor: selectReportItem ? 'pointer' : 'not-allowed', marginTop: hoverIndex != 1 ? '72px' : '' }"
				>
					<el-icon style="margin-right: 4px; width: 16px; height: 16px"><VideoPlay /></el-icon>
					点击查看{{ selectReportItem ? ` [${selectReportItem.name}] ` : '' }}演示Demo
				</div> -->
				<div
					v-if="hoverIndex === 2"
					@click="handleReportDemo"
					class="demo_tips"
					:style="{ cursor: 'pointer', marginTop: hoverIndex != 1 ? '72px' : '' }"
				>
					<el-icon style="margin-right: 4px; width: 16px; height: 16px"><VideoPlay /></el-icon>
					点击查看投资分析报告演示Demo
				</div>
				<div
					class="report_btn"
					:class="{ available: reportDownLoadAvaiable }"
					:style="{ cursor: reportDownLoadAvaiable ? 'pointer' : 'not-allowed' }"
					@click="handleOpenReportModal"
				>
					<el-image :src="reportDownLoadAvaiable ? btn_icon_select : btn_icon"></el-image>
					<span v-if="hoverIndex === 1">获取投资单项报告</span>
					<span v-if="hoverIndex === 2">获取投资分析报告</span>
					<span v-if="hoverIndex === 3">获取投资报告套餐</span>
				</div>
				<template v-if="hoverIndex !== 1 && selectBuild">
					<div v-if="isReportUnavailable" class="build_none">【{{ selectBuild.buildingName }}】的投资报告暂未收录，如您需要请联系我们</div>
				</template>
			</div>
			<div class="tooltips">
				<div class="text">没有找到合适的报告？</div>
				<div class="desc">请向我们描述您的需求，我们将为您提供帮助。</div>
			</div>
			<div class="connect_wrap">
				<div class="call_wrap">
					<div class="fw500 fs20 mb20">电话咨询</div>
					<div class="mb10">服务电话：</div>
					<div class="fw600 mb26">400-677-8895</div>
					<div class="mb10">服务时间：</div>
					<div class="fw600">周一至周五北京时间上午AM8:00-PM5:30</div>
				</div>
				<div class="form_wrap">
					<div class="fw500 fs20">提交需求</div>
					<el-form ref="formEl" style="margin-top: 20px" :model="formData" :rules="rules" label-position="top" size="large">
						<el-row :gutter="20">
							<el-col :span="12">
								<el-form-item label="您的姓名" prop="name">
									<el-input v-model="formData.name" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="您的联系电话" prop="tel">
									<el-input v-model="formData.tel" clearable />
								</el-form-item>
							</el-col>
						</el-row>

						<el-form-item label="您的电子邮箱">
							<el-input v-model="formData.email" clearable />
						</el-form-item>
						<el-form-item label="您想要的报告内容" prop="demand">
							<el-input v-model="formData.demand" type="textarea" maxlength="200" show-word-limit clearable />
						</el-form-item>
					</el-form>
					<div class="submit_wrap">
						<div class="submit_btn" @click="formSubmit">提交</div>
					</div>
				</div>
			</div>
		</div>
		<buildSelectDialog v-model="buildDialogShow" @confirm="handleBuildConfirm"></buildSelectDialog>
		<getReportDialog
			v-model="reportDialogShow"
			:reportData="reportData"
			:dialogType="reportType"
			:buildInfo="selectBuild"
			@success="paySuccess"
		></getReportDialog>
		<reportDemo
			v-if="selectReportItem"
			ref="reportDemoDom"
			:reportType="selectReportItem.reportType"
			:reportTypeName="selectReportItem.reportTypeName"
			@close="handleReportClose"
		></reportDemo>
		<successDialog v-model="successShow" :payData="payData" @close="handleSuccessClose"></successDialog>
	</div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { getReportList, addReportReq } from '@/api/shangauto';
import buildSelectDialog from '@/component/buildSelectDialog/index.vue';
import reportDemo from './components/reportDemo/index.vue';
import getReportDialog from './components/getReportDialog/index.vue';
import successDialog from './components/successDialog/index.vue';
import logo from '@/assets/images/shangauto/logo.png';
import business from '@/assets/images/shangauto/business.png';
import gaikuang from '@/assets/images/shangauto/gaikuang.png';
import location from '@/assets/images/shangauto/location.png';
import population from '@/assets/images/shangauto/population.png';
import populationthree from '@/assets/images/shangauto/populationthree.png';
import populationtwo from '@/assets/images/shangauto/populationtwo.png';
import security from '@/assets/images/shangauto/security.png';
import value from '@/assets/images/shangauto/value.png';
import btn_icon_select from '@/assets/images/shangauto/btn_icon_select.png';
import btn_icon from '@/assets/images/shangauto/btn_icon.png';
import mySelect from '@/component/arcoComponents/select/index.vue';
const reportTypeImgs = ref({
	business,
	gaikuang,
	location,
	population,
	populationthree,
	populationtwo,
	security,
	value,
});
const buildDialogShow = ref(false); // 资产选择弹窗
const reportDialogShow = ref(false);
const successShow = ref(false);
const payData = ref({});
const selectBuild = ref(null); // 选择的资产
const hoverIndex = ref(1); // 鼠标移入的索引-报告类型
const selectedIndex = ref(1); // 鼠标选中的索引-报告类型
const reportData = ref([]); // 报告列表数据
const selectReportItem = ref(null); // 选择的单项报告类型
const hoverReportItem = ref({}); // 选择的单项报告类型
const formEl = ref();
const reportDemoDom = ref();
const formData = ref({
	name: '',
	tel: '',
	email: '',
	demand: '',
});
const rules = ref({
	name: [{ required: true, message: '请输入您的姓名', trigger: 'manual' }],
	tel: [
		{ required: true, message: '请输入您的联系电话', trigger: 'manual' },
		{ pattern: /^\d+$/, message: '电话只能输入数字', trigger: 'blur' },
		{ pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' },
	],
	demand: [{ required: true, message: '请输入您想要的报告内容', trigger: 'manual' }],
});

watch(
	() => hoverIndex.value,
	(newValue, oldValue) => {
		if (newValue == 2) {
			selectReportItem.value = reportData.value.find((item) => item.reportType == 'invest_analyze');
		} else {
			selectReportItem.value = null;
		}
	}
);

// 报告下载按钮是否可用
const reportDownLoadAvaiable = computed(() => {
	return selectBuild.value && Object.keys(selectBuild.value).length > 0;
});
// 分析报告和报告套餐是否可用
const isReportUnavailable = computed(() => {
	if (hoverIndex.value === 2 && reportData.value.find((item) => item.reportType == 'invest_analyze')) {
		return !reportData.value.find((item) => item.reportType == 'invest_analyze').availableState;
	}
	if (hoverIndex.value === 3 && reportData.value.find((item) => item.reportType == 'combo_report').availableState) {
		return !reportData.value.find((item) => item.reportType == 'combo_report').availableState.availableState;
	}
});
// 根据hoverIndex判断报告类型
const reportType = computed(() => {
	if (hoverIndex.value === 1) {
		return 'single';
	}
	if (hoverIndex.value === 2) {
		return 'analyze';
	}
	if (hoverIndex.value === 3) {
		return 'package';
	}
});

const isHidden = ref(false);
function handleScroll() {
	console.log('handleScroll');
	const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
	console.log(currentScrollPosition, 'currentScrollPosition');
	if (currentScrollPosition > 104) {
		// 向下滚动
		isHidden.value = true;
	} else {
		// 向上滚动
		isHidden.value = false;
	}
}
onMounted(() => {
	window.scrollTo(0, 0);
});

getReportData();
// 获取报告列表
async function getReportData() {
	const { data } = await getReportList(selectBuild.value ? selectBuild.value.id : '');
	reportData.value = data;
}
// 选择资产
function handleSelectBuild() {
	buildDialogShow.value = true;
}
// 选择资产确认
function handleBuildConfirm(data) {
	if (data) {
		selectBuild.value = data;
		getReportData();
	}
}
// 删除选择资产
function handleBuildClose() {
	selectBuild.value = null;
	getReportData();
}
// 报告类型鼠标移入
function onMouseEnter(index) {
	hoverIndex.value = index;
}
// 报告类型鼠标移出
function onMouseLeave() {
	hoverIndex.value = selectedIndex.value;
}
// 报告类型选择
function handleTypeSelect(type) {
	selectedIndex.value = type;
}
// 单项报告类型选择
function handleSelectReportType(item) {
	if (!item.availableState) return;
	selectReportItem.value = item;
}
function handleHoverReportType(item) {
	if (!item.availableState) return;
	selectReportItem.value = item;
	hoverReportItem.value = item;
}
function handleHideReportType(item) {
	hoverReportItem.value = {};
}
function paySuccess(data) {
	payData.value = data;
	successShow.value = true;
}
function handleSuccessClose() {
	payData.value = {};
}
// 需求表单提交
async function formSubmit() {
	if (!formEl.value) return;
	await formEl.value.validate(async (valid, fields) => {
		if (valid) {
			try {
				const res = await addReportReq(formData.value);
				resetFormData();
				if (res.code == 200) {
					ElMessage({
						message: '提交成功',
						type: 'success',
						offset: 300,
					});
				}
			} catch (error) {}
		} else {
			console.log('error submit!', fields);
		}
	});
}
function resetFormData() {
	formEl.value.resetFields();
}
// 获取投资分析报告
function handleOpenReportModal() {
	// 判断点击按钮
	if (hoverIndex.value === 1) {
		// 判断报告是否可用
		if (reportDownLoadAvaiable.value) {
			openReportModal();
		} else {
			showNoAssetSelectedMessage();
		}
	} else {
		// 判断是否选择资产
		if (reportDownLoadAvaiable.value) {
			handleReportTypeSelection();
		} else {
			showNoAssetSelectedMessage();
		}
	}
}

function openReportModal() {
	console.log('🚀 ~ openReportModal ~ openReportModal:');
	reportDialogShow.value = true;
}

function showNoAssetSelectedMessage() {
	let message = '请您先选择资产';
	ElMessage({
		message,
		type: 'info',
		offset: 300,
	});
}

function handleReportTypeSelection() {
	if (hoverIndex.value === 2) {
		handleReportType('invest_analyze', `[${selectBuild.value.buildingName}]的投资分析报告暂未收录，如您需要请联系我们`);
	} else if (hoverIndex.value === 3) {
		handleReportType('combo_report', `[${selectBuild.value.buildingName}]的投资报告暂未收录，如您需要请联系我们`);
	}
}

function handleReportType(type, unavailableMessage) {
	if (reportData.value.find((item) => item.reportType == type) && reportData.value.find((item) => item.reportType == type).availableState) {
		openReportModal();
	} else {
		ElMessage({
			message: unavailableMessage,
			type: 'info',
			offset: 300,
		});
	}
}
// 查看当前报告demo
function handleReportDemo() {
	if (hoverIndex.value == 1 && !selectReportItem.value) {
		ElMessage({
			message: '请先选择报告类型',
			type: 'info',
			offset: 300,
		});
		return;
	}
	reportDemoDom.value.generateReport();
}
// 关闭报告
function handleReportClose() {
	reportDemoDom.value.closeReport();
}
</script>

<style lang="less" scoped>
.shangauto_container {
	width: 100%;
	background-image: url('@/assets/images/shangauto/bg.png');
	background-attachment: fixed; /* 背景固定 */
	.content_wrap {
		width: 1200px;
		margin: 0 auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		.logo_wrap {
			padding: 30px 0;
			display: flex;
			justify-content: center;
			position: sticky;
			top: 6px;
			z-index: 99;
		}
		.flag_wrap {
			transition: opacity 0.5s ease-in-out;

			.title {
				font-size: 46px;
				font-weight: 500;
				color: #1d2129;
				text-align: center;
				margin-bottom: 8px;
				line-height: 64px;
			}
			.desc {
				text-align: center;
				color: #23366e;
				font-weight: 400;
				line-height: 22px;
			}
		}
		.flag_wrap.hidden {
			opacity: 0;
		}
		.no-select {
			-webkit-user-select: none; /* Chrome, Safari */
			-moz-user-select: none; /* Firefox */
			-ms-user-select: none; /* Internet Explorer */
			user-select: none; /* 标准 */
		}
		.build_select_wrap {
			width: 715px;
			height: 64px;
			margin-top: 36px;
			background: #ffffffcc;
			border: 2px solid #0e47f2;
			border-radius: 16px;
			box-shadow: 0px 8px 20px 0px #0a23581a;
			backdrop-filter: blur(100px);
			z-index: 99;
			position: sticky;
			top: 123px;
			padding: 0 14px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.placeholder {
				font-size: 18px;
				font-weight: 400;
				color: #536a97;
			}
			.select_item {
				display: flex;
				align-items: center;
				height: 48px;
				gap: 14px;
				padding: 0 16px;
				background: #d9e7ff;
				border-radius: 14px;
				span {
					color: #1e5ad2;
				}
			}
			.btn_wrap {
				cursor: pointer;
				background: linear-gradient(111.61deg, #307eff 35.81%, #083ef0 96.36%);
				backdrop-filter: blur(20px);
				box-shadow: 0px 8px 20px 0px #0a23581a;
				padding: 0 16px;
				height: 48px;
				border-radius: 14px;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 8px;
				.text {
					font-size: 20px;
					font-weight: 400;
					color: #fff;
				}
			}
		}
		.report_wrap {
			box-sizing: border-box;
			margin-top: 110px;
			width: 100%;
			padding: 0 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 20px;
			.single_wrap,
			.analyze_wrap,
			.package_wrap {
				box-sizing: border-box;
				flex: 1;
				height: 272px;
				width: 360px;
				transition: transform 0.3s ease; /* 平滑过渡 */
				padding: 50px 40px;
				color: #fff;
				.title {
					font-size: 26px;
					font-weight: 600;
					line-height: 36px;
				}
				.desc {
					font-size: 16px;
					font-weight: 400;
					line-height: 22px;
				}
				.tag {
					margin-top: 15px;
					padding: 4px 8px;
					background: linear-gradient(90deg, #ffcea8 0%, #ffebe0 100%);
					color: #944e1e;
					font-size: 13px;
					font-weight: 500;
					border-radius: 2px 8px 8px 2px;
					width: 124px;
					height: 26px;
					box-sizing: border-box;
					display: flex;
					align-items: center;
				}
			}
			.single_wrap {
				background-image: url(/src/assets/images/shangauto/single_bg.png);
			}
			.analyze_wrap {
				background-image: url(/src/assets/images/shangauto/analyze_bg.png);
			}
			.package_wrap {
				background-image: url(/src/assets/images/shangauto/package_bg.png);
			}
			.active {
				transform: translateY(-50px); /* 上移10px */
			}
			.grayscale {
				filter: grayscale(100%);
			}
		}
		.report_select_wrap {
			box-sizing: border-box;
			position: relative;
			width: 100%;
			height: 267px;
			background-image: url(/src/assets/images/shangauto/type_bg.png);
			background-repeat: no-repeat;
			margin-top: -84px;
			z-index: 9;
			padding: 43px 60px 24px 60px;
			display: flex;
			flex-direction: column;
			align-items: center;
			.report_list_wrap {
				display: flex;
				gap: 40px;
				margin-bottom: 16px;
				.report_list_item {
					width: 100px;
					height: 92px;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					position: relative;
					span {
						font-size: 13px;
						font-weight: 400;
						color: #4b5872;
						line-height: 18px;
					}
					.hovered {
						width: 100%;
						height: 100%;
						background: rgba(255, 255, 255, 0.4);
						border-radius: 17px;
						backdrop-filter: blur(10px);
						transition: all 0.3s ease;
						position: absolute;
						top: 0;
						left: 0;
						display: flex;
						align-items: center;
						justify-content: center;
						.btn {
							width: 100%;
							height: 33px;
							background: linear-gradient(111.61deg, #307eff 35.81%, #083ef0 96.36%);
							border-radius: 27px;
							color: #fff;
							font-size: 12px;
							font-weight: 400;
							display: flex;
							justify-content: center;
							align-items: center;
							.icon {
								margin-right: 4px;
								margin-top: -2px;
							}
						}
					}
					.hidden {
						opacity: 0;
					}
				}
				.selected {
					background-color: #ffffff;
					border-radius: 17px;
					transition: background-color 0.3s ease;
				}
			}
			.demo_tips {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #1451f4;
			}
			.report_btn {
				box-sizing: border-box;
				margin-top: 12px;
				margin-bottom: 16px;
				width: 306px;
				height: 60px;
				padding: 16px 40px;
				border: 1px solid #8e9bb3;
				border-radius: 14px;
				color: #8e9bb3;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;
				font-size: 20px;
				font-weight: 500;
			}
			.available {
				color: #fff;
				background: linear-gradient(111.61deg, #307eff 35.81%, #083ef0 96.36%);
				backdrop-filter: blur(20px);
				box-shadow: 0px 8px 20px 0px #0a23581a;
			}
			.build_none {
				font-size: 14px;
				font-weight: 400;
				color: #8e9bb3;
			}
		}
		.tooltips {
			margin-top: 57px;
			font-size: 28px;
			font-weight: 400;
			text-align: center;
			line-height: 39px;
			.text {
				color: #1d2129;
			}
			.desc {
				color: #3c475c;
			}
		}
		.connect_wrap {
			width: 100%;
			box-sizing: border-box;
			margin-top: 40px;
			margin-bottom: 102px;
			display: flex;
			justify-content: center;
			border: 1px solid #e8e8f1;
			border-radius: 24px;
			background: #f8fafd;
			padding: 46px 80px;
			.call_wrap,
			.form_wrap {
				flex: 1;
				height: 100%;
				color: #000;
				font-size: 14px;
				font-weight: 400;
				.submit_wrap {
					margin-top: 24px;
					width: 100%;
					display: flex;
					justify-content: flex-end;
					.submit_btn {
						border-radius: 6px;
						background: linear-gradient(111.61deg, #307eff 35.81%, #083ef0 96.36%);
						width: 88px;
						height: 32px;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 14px;
						font-weight: 400;
						color: #fff;
						cursor: pointer;
					}
				}
				.fw500 {
					font-weight: 500;
				}
				.fw600 {
					font-weight: 600;
				}
				.fs20 {
					font-size: 20px;
				}
				.mb20 {
					margin-bottom: 20px;
				}
				.mb10 {
					margin-bottom: 10px;
				}
				.mb26 {
					margin-bottom: 26px;
				}
				:deep(.el-form-item__label) {
					color: #000;
					margin-bottom: 4px;
				}
			}
		}
	}
}
</style>
