<template>
	<div class="waterfall-container">
		<!-- 第一行 -->
		<div class="waterfall-row">
			<div class="cards-wrapper">
				<div class="card" v-for="(item, index) in row1Cards" :key="'row1-' + index" @mouseenter="pauseAnimation" @mouseleave="resumeAnimation">
					<div class="card-content">
						<div class="card-title">{{ item.title }}</div>
					</div>
				</div>
				<!-- 复制一份实现无缝滚动 -->
				<div class="card" v-for="(item, index) in row1Cards" :key="'row1-copy-' + index" @mouseenter="pauseAnimation" @mouseleave="resumeAnimation">
					<div class="card-content">
						<div class="card-title">{{ item.title }}</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 第二行 -->
		<div class="waterfall-row reverse">
			<div class="cards-wrapper">
				<div class="card" v-for="(item, index) in row2Cards" :key="'row2-' + index" @mouseenter="pauseAnimation" @mouseleave="resumeAnimation">
					<div class="card-content">
						<div class="card-title">{{ item.title }}</div>
					</div>
				</div>
				<!-- 复制一份实现无缝滚动 -->
				<div class="card" v-for="(item, index) in row2Cards" :key="'row2-copy-' + index" @mouseenter="pauseAnimation" @mouseleave="resumeAnimation">
					<div class="card-content">
						<div class="card-title">{{ item.title }}</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 第三行 -->
		<div class="waterfall-row">
			<div class="cards-wrapper">
				<div class="card" v-for="(item, index) in row3Cards" :key="'row3-' + index" @mouseenter="pauseAnimation" @mouseleave="resumeAnimation">
					<div class="card-content">
						<div class="card-title">{{ item.title }}</div>
					</div>
				</div>
				<!-- 复制一份实现无缝滚动 -->
				<div class="card" v-for="(item, index) in row3Cards" :key="'row3-copy-' + index" @mouseenter="pauseAnimation" @mouseleave="resumeAnimation">
					<div class="card-content">
						<div class="card-title">{{ item.title }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref } from 'vue';
// 更新为图片中显示的文本内容
const row1Cards = ref([
	{ title: '“商宇通可以挖掘新的潜在客户和合作伙伴”' },
	{ title: '“商宇通帮我找到了场外投资机会”' },
	{ title: '“分析近期租赁和销售交易的细节这方面做的很详尽”' },
	{ title: '“评估市场和子市场 KPI 趋势和预测”' },
]);

const row2Cards = ref([
	{ title: '“分析近期租赁和销售交易的细节这方面做的很详尽”' },
	{ title: '“商宇通准确的评估了物业价值和潜在租赁收入”' },
	{ title: '“评估市场和子市场 KPI 趋势和预测”' },
	{ title: '“帮我了解资产的基本财务表现”' },
	{ title: '“商宇通帮我找到了场外投资机会”' },
]);

const row3Cards = ref([
	{ title: '“了解资产的基本财务表现”' },
	{ title: '“分析近期租赁和销售交易的细节这方面做的很详尽”' },
	{ title: '“抢占租户搬迁、租约到期和续约”' },
	{ title: '“商宇通可以挖掘新的潜在客户和合作伙伴”' },
	{ title: '“帮我了解资产的基本财务表现”' },
]);

const pauseAnimation = () => {
	const wrappers = document.querySelectorAll('.cards-wrapper');
	wrappers.forEach((wrapper) => {
		wrapper.style.animationPlayState = 'paused';
	});
};

const resumeAnimation = () => {
	const wrappers = document.querySelectorAll('.cards-wrapper');
	wrappers.forEach((wrapper) => {
		wrapper.style.animationPlayState = 'running';
	});
};
</script>

<style scoped lang="scss">
.waterfall-container {
	width: 100%;
	height: auto;
	display: flex;
	flex-direction: column;
	gap: 24px;
	padding: 16px;
}

.waterfall-row {
	width: 100%;
	overflow: hidden;
	position: relative;
}

.cards-wrapper {
	display: flex;
	gap: 24px 32px;
	animation: scrollLeft 25s linear infinite;
}

.reverse .cards-wrapper {
	animation: scrollRight 25s linear infinite;
}

@keyframes scrollLeft {
	0% {
		transform: translateX(0);
	}
	100% {
		transform: translateX(-50%);
	}
}

@keyframes scrollRight {
	0% {
		transform: translateX(-50%);
	}
	100% {
		transform: translateX(0);
	}
}

.card {
	height: 76px;
	background: white;
	border-radius: 12px;
	padding: 24px 32px;
	box-sizing: border-box;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;
}

.card:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
	.card-title {
		font-weight: 500;
		color: #1d2129;
	}
}

.card-content {
	height: 100%;
	display: flex;
	align-items: center;
}

.card-title {
	width: max-content;
	font-weight: 400;
	font-size: 20px;
	color: #4e5969;
}

/* 渐变遮罩 */
.waterfall-row::before,
.waterfall-row::after {
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	width: 80px;
	z-index: 1;
	pointer-events: none;
}

.waterfall-row::before {
	left: 0;
	background: linear-gradient(to right, #f5f5f5 0%, transparent 100%);
}

.waterfall-row::after {
	right: 0;
	background: linear-gradient(to left, #f5f5f5 0%, transparent 100%);
}
</style>
