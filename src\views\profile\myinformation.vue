<template>
    <div class="information">
        <div class="information_box" v-for="item in informationList" :key="item">
            <div class="icon_box">
                <el-icon><BellFilled/></el-icon>
            </div>
            <div class="text">
                <h3>系统通知</h3>
                <span>{{ item.body }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import http from '@/utils/http';
const informationList = ref([]);
// 获取我的消息
const getInformation = async() => {
    const res = await http.get('/api/api.do?act=listMSG');
    console.log('消息',res);
    informationList.value = res.list
}
// getInformation();
</script>

<style scoped>
.information_box {
    /* width: 500px; */
    /* border-radius: 15px; */
    /* margin: 15px; */
    padding: 15px;
    display: flex;
    text-align: center;
    vertical-align:middle; 
    /* background-color: #fff; */
    border-bottom: 1px solid #ccc;
}
.icon_box {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3170a7;
    position: relative;
    margin-top: 20px;
    margin-right: 20px;
}
.icon_box {

    vertical-align:middle; 
}
.el-icon {
    font-size: 20px;
    color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
    vertical-align:middle;
}
</style>