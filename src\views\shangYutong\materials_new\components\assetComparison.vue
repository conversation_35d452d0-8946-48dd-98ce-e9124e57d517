<template>
	<div style="padding: 16px 16px 16px 0" :key="route.query?.assetsIds">
		<div class="tab_box">
			<div class="tab" :class="activeName == route.name ? 'tabAct' : ''" v-for="route in routes" @click="handleTabClick(route)" :key="route.name">
				<img :src="activeName == route.name ? route.icon_active : route.icon" />
				{{ route.name }}
			</div>
		</div>
    <el-skeleton :loading="loading">
      <component :is="componentNames" :assetsIds="assetsIds" @handleBuildingId="handleBuildingId" />
    </el-skeleton>
		<div class="calc_wrap" @click="handleOpenCalc">
			<img :src="calc_icon" class="icon" />
			<div class="label">交易计算</div>
		</div>
		<calcDrawer v-model="calcDrawerVisible"></calcDrawer>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import comparison from './comparisonnew.vue';
import population from './populationnew.vue';
import lessee from './lesseenew.vue';
import position from './positionnew.vue';
import securitization from './securitizationnew.vue';
import floor from './floornew.vue';
import creditRisks from './creditRisks.vue';
import tab_icon1 from '@/assets/images/shangYutong/buildInfo/tab_icon1.png';
import tab_icon2 from '@/assets/images/shangYutong/buildInfo/tab_icon2.png';
import tab_icon3 from '@/assets/images/shangYutong/buildInfo/tab_icon3.png';
import tab_icon4 from '@/assets/images/shangYutong/buildInfo/tab_icon4.png';
import tab_icon5 from '@/assets/images/shangYutong/buildInfo/tab_icon5.png';
import tab_icon6 from '@/assets/images/shangYutong/buildInfo/tab_icon6.png';
import tab_icon7 from '@/assets/images/shangYutong/menu/estateFinance.png';
import tab_icon1_active from '@/assets/images/shangYutong/buildInfo/tab_icon1_active.png';
import tab_icon2_active from '@/assets/images/shangYutong/buildInfo/tab_icon2_active.png';
import tab_icon3_active from '@/assets/images/shangYutong/buildInfo/tab_icon3_active.png';
import tab_icon4_active from '@/assets/images/shangYutong/buildInfo/tab_icon4_active.png';
import tab_icon5_active from '@/assets/images/shangYutong/buildInfo/tab_icon5_active.png';
import tab_icon6_active from '@/assets/images/shangYutong/buildInfo/tab_icon6_active.png';
import tab_icon7_active from '@/assets/images/shangYutong/menu/estateFinanceAct.png';
import calc_icon from '@/assets/images/shangYutong/buildInfo/calc_icon.png';
import calcDrawer from './calcDrawer.vue';
import { useRoute } from 'vue-router';
import {getBuildingListById, getComparativeOverviews} from "REQUEST_API";
const route = useRoute()
const routes = [
	{
		name: '概况',
		componentName: comparison,
		icon: tab_icon1,
		icon_active: tab_icon1_active,
	},
	{
		name: '人口',
		componentName: population,
		icon: tab_icon2,
		icon_active: tab_icon2_active,
	},
	{
		name: '位置',
		componentName: position,
		icon: tab_icon3,
		icon_active: tab_icon3_active,
	},
	{
		name: '估值与证券化',
		componentName: securitization,
		icon: tab_icon4,
		icon_active: tab_icon4_active,
	},
	{
		name: '租户',
		componentName: lessee,
		icon: tab_icon5,
		icon_active: tab_icon5_active,
	},
	// {
	// 	name: '户型图',
	// 	componentName: floor,
	// 	icon: tab_icon6,
	// 	icon_active: tab_icon6_active,
	// },
	{
		name: '信用风险',
		componentName: creditRisks,
		icon: tab_icon7,
		icon_active: tab_icon7_active,
	},
];
const componentNames = ref(comparison);
const activeName = ref('概况');
const assetsIds = ref('');
const buildingIds = ref({});
const calcDrawerVisible = ref(false);
const loading = ref(true);

watch(
    () => route.query?.assetsIds,
    () => {
      loadRouteAssets();
    },
    { immediate: true },
  )

const handleTabClick = (item) => {
	assetsIds.value = buildingIds.value;
	componentNames.value = item.componentName;
	activeName.value = item.name;
	buildingIds.value = {};
};

const handleBuildingId = (val) => {
	buildingIds.value = val;
};

function handleOpenCalc() {
	calcDrawerVisible.value = true;
}

async function loadRouteAssets() {
  loading.value = true;
  if (route.query?.assetsIds) {
    const res = await getBuildingListById({
      buildingId: route.query?.assetsIds,
    });
    assetsIds.value = {
      ids: route.query?.assetsIds,
      arr: res?.data?.rows?.[0] ? [{...res.data.rows[0], id: route.query?.assetsIds }] : [],
    };
    loading.value = false;
  } else {
    loading.value = false;
  }
}
</script>

<style lang="scss" scoped>
.calc_wrap {
	position: absolute;
	z-index: 999;
	bottom: 120px;
	right: 20px;
	width: 68px;
	height: 68px;
	border-radius: 8px;
	background: linear-gradient(243.85deg, #4580f3 11.45%, #8bbffd 89.17%);
	box-shadow: 1px 10px 16px 0px #0a33821a;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 6px;
	color: #fff;
	.icon {
		width: 24px;
		height: 24px;
	}
	.label {
		line-height: 18px;
		font-size: 12px;
		font-weight: 600;
	}
}
.tab_box {
	width: 100%;
	box-sizing: border-box;
	font-size: 14px;
	font-weight: 600;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	background-color: transparent;
	.title {
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		margin-right: 15px;
		color: #1a1a1a;
	}

	.tab {
		width: 216px;
		height: 48px;
		padding: 0 16px;
		display: flex;
		align-items: center;
		cursor: pointer;
		font-size: 16px;
		font-weight: 500;
		line-height: 24px;
		color: #4e5969;
		background-color: #f2f3f5;
		border-radius: 4px 4px 0 0;
		img {
			width: 16px;
			height: 16px;
			margin-right: 4px;
		}
	}

	.tabAct {
		font-size: 16px;
		color: #1868f1;
		background-color: #fff;
	}
}
</style>
