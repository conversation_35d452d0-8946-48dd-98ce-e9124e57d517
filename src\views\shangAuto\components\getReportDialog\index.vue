<template>
	<el-dialog v-model="visible" @close="handleClose" width="950" :close-on-click-modal="false">
		<template #header>
			<span class="dialog_title">{{ title }}</span>
		</template>
		<div class="dialog_container">
			<div class="label_wrap align-center">
				<div class="label_title">接收邮箱</div>
				<div class="label_content">
					<el-input
						v-model="email"
						@change="handleEmailChange"
						style="width: 400px"
						placeholder="请填写接收邮箱地址(支付后报告会发送至该邮箱)"
					></el-input>
				</div>
			</div>
			<div class="label_wrap">
				<div class="label_title">已选资产</div>
				<div class="label_content">{{ buildInfo.buildingName }}</div>
			</div>
			<div class="label_wrap" v-if="['single', 'analyze'].includes(dialogType)">
				<div class="label_title">报告周期</div>
				<div class="label_content">最新季度报告</div>
			</div>
			<div class="label_wrap" v-if="['package'].includes(dialogType)">
				<div class="label_title">套餐详情</div>
				<div class="label_content">
					包含已选择资产的【1份当前最新季度报告、3份后续三个季度的报告和8份单项报告】
					当前最新季度的报告会尽快发送至您预留的接收邮箱中，后续每过一个季度我们会向您发送 一份最新的季度报告，请注意查收
				</div>
			</div>
			<div class="label_wrap">
				<div class="label_title">报告类型</div>
				<div class="type_wrap" v-if="['single'].includes(dialogType)">
					<div
						class="type_item"
						v-for="(item, index) in reportDataClone.filter((item) =>
							['population', 'populationtwo', 'populationthree', 'business'].includes(item.reportType)
						)"
						:key="item.reportType"
						:class="{ active: item.selected, disabled: !item.availableState }"
						:style="{ cursor: item.availableState ? 'pointer' : 'not-allowed' }"
						@click="changeReportType(item)"
					>
						<div class="checked_icon" v-if="item.selected">
							<el-icon color="#1868f1" size="18"><CircleCheckFilled /></el-icon>
						</div>
						<div class="type_item_title">{{ item.name }}</div>
						<div class="type_item_price"><span>￥</span>{{ item.price }}</div>
					</div>
				</div>
				<div class="type_wrap" v-if="['package', 'analyze'].includes(dialogType)">
					<div class="type_item active flex-row" :style="{ cursor: 'pointer' }">
						<div class="checked_icon">
							<el-icon color="#1868f1" size="18"><CircleCheckFilled /></el-icon>
						</div>
						<div class="type_item_title">{{ selectedReport[0].name }}</div>
						<div class="type_item_price"><span>￥</span>{{ selectedReport[0].price }}</div>
					</div>
				</div>
			</div>
			<div class="label_wrap">
				<div class="label_title">扫码支付</div>
				<div>支付成功后可在"个人中心-订单中心"开具发票</div>
			</div>
			<div class="pay_wrap">
				<div class="code_wrap">
					<el-skeleton style="width: 100%; height: 100%" v-if="!paymentStatus">
						<template #template>
							<el-skeleton-item variant="image" style="width: 100%; height: 100%" />
						</template>
					</el-skeleton>
					<iframe
						:src="paymentURL"
						v-show="paymentStatus == 'ALI_PC' && checkbox"
						frameborder="no"
						border="0"
						marginwidth="0"
						marginheight="0"
						scrolling="no"
						width="200"
						height="200"
						style="overflow: hidden; transform: scale(0.6); margin: -20px 0 0 -40px; transform-origin: 100px 50px; /* 确保从左上角开始缩放 */"
					>
					</iframe>
					<canvas v-show="paymentStatus === 'WX_NATIVE' && checkbox" ref="qrcodeCanvas" class="qrcode"></canvas>
				</div>
				<div class="action_wrap">
					<el-checkbox value="Agree" v-model="checkbox" @change="agreementChange" name="type" class="check_boxAgreement">
						我已阅读并同意<span class="blueSpan" @click="viewAgreement">《商报auto服务协议》</span>
					</el-checkbox>
					<div class="content_price">
						<div>扫码支付</div>
						<div>¥</div>
						<div>{{ selectedReportPrice }}</div>
					</div>
					<div class="content_bottom">
						<el-radio-group v-model="paymentStatus" @change="handlePayTypeChange">
							<el-radio value="ALI_PC" size="large">支付宝</el-radio>
							<el-radio value="WX_NATIVE" size="large">微信</el-radio>
						</el-radio-group>
					</div>
				</div>
			</div>
		</div>
		<agreeDialog v-model="agreeShow"></agreeDialog>
	</el-dialog>
</template>

<script setup>
import QRCode from 'qrcode';
import { ElMessage, ElMessageBox } from 'element-plus';
import { orderOreate, orderStatus } from '@/api/rights';
import agreeDialog from '../agreeDialog/index.vue';
import _ from 'lodash';
import { computed } from 'vue';

const props = defineProps({
	// 控制弹窗
	modelValue: {
		type: Boolean,
		required: true,
	},
	// 弹窗类型
	dialogType: {
		type: String,
		default: 'single',
	},
	// 资产信息
	buildInfo: {
		type: Object,
		default: () => {},
	},
	// 报告列表信息
	reportData: {
		type: Array,
		default: () => [],
	},
});

const emit = defineEmits(['update:modelValue', 'success']);

const paymentStatus = ref(''); //支付方式 WX_NATIVE 微信 ALI_PC 支付宝
const paymentURL = ref(''); //支付二维码
const checkbox = ref(false); // 同意协议
const email = ref(''); // 邮箱
const orderId = ref(''); // 订单号
const qrcodeCanvas = ref(); // 微信二维码
const timerId = ref(null); // 订单轮询定时器
const agreeShow = ref(false);
const reportDataClone = ref([]);

// 根据类型显示标题
const title = computed(() => {
	if (props.dialogType === 'single') {
		return '购买单项报告';
	}
	if (props.dialogType === 'analyze') {
		return '购买投资分析报告';
	}
	if (props.dialogType === 'package') {
		return '购买报告套餐';
	}
});

const selectReportType = ref({});

watch(
	() => props.reportData,
	(val) => {
		if (val && val.length > 0) {
			reportDataClone.value = _.cloneDeep(val);
			console.log('🚀 ~ reportDataClone.value:', reportDataClone.value);
			reportDataClone.value.forEach((item) => {
				item.selected = false;
			});
			if (props.dialogType === 'single') {
				reportDataClone.value[0].selected = true;
			} else if (props.dialogType === 'analyze') {
				reportDataClone.value.find((item) => item.reportType == 'invest_analyze').selected = true;
			} else if (props.dialogType === 'package') {
				reportDataClone.value.find((item) => item.reportType == 'combo_report').selected = true;
			}
		}
	},
	{ immediate: true }
);
watch(
	() => props.dialogType,
	(val) => {
		if (val) {
			if (val && Object.keys(val).length > 0 && reportDataClone.value.length > 0) {
				reportDataClone.value.forEach((item) => {
					item.selected = false;
				});
				if (props.dialogType === 'single') {
					reportDataClone.value[0].selected = true;
				} else if (props.dialogType === 'analyze') {
					reportDataClone.value.find((item) => item.reportType == 'invest_analyze').selected = true;
				} else if (props.dialogType === 'package') {
					reportDataClone.value.find((item) => item.reportType == 'combo_report').selected = true;
				}
			}
		}
	},
	{ immediate: true }
);
const visible = ref(false);

watch(
	() => props.modelValue,
	(newVal) => {
		visible.value = newVal;
	}
);

const selectedReport = computed(() => {
	return reportDataClone.value.filter((item) => item.selected);
});
const selectedReportPrice = computed(() => {
	// 计算总价格
	return reportDataClone.value.filter((item) => item.selected).reduce((sum, item) => sum + item.price, 0);
});

const handleClose = () => {
	resetPayInfo();
	email.value = '';
	checkbox.value = false;
	visible.value = false;
	emit('update:modelValue', false);
};
function changeReportType(item) {
	if (item.availableState) {
		item.selected = !item.selected;
		// 重置支付信息
		resetPayInfo();
	}
}
function handleEmailChange() {
	resetPayInfo();
}
function resetPayInfo() {
	clearInterval(timerId.value);
	paymentStatus.value = '';
	orderId.value = '';
	paymentURL.value = '';
}
function agreementChange(e) {
	if (!e) {
		resetPayInfo();
	}
}
function handlePayTypeChange() {
	clearInterval(timerId.value);
	if (payCheck()) {
		getPayCode();
	} else {
		paymentStatus.value = '';
	}
}
function payCheck() {
	if (!checkbox.value) {
		ElMessage({
			message: '请先阅读并同意《商报auto服务协议》',
			type: 'warning',
			offset: 300,
		});
		return false;
	}

	// 判断邮箱是否为空
	if (email.value === '') {
		ElMessage({
			message: '请输入邮箱',
			type: 'warning',
			offset: 300,
		});
		return false;
	}

	// 判断报告类型是否为空
	if (selectedReport.value.length === 0) {
		ElMessage({
			message: '请选择报告类型',
			type: 'warning',
			offset: 300,
		});
		return false;
	}

	if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email.value)) {
		ElMessage({
			message: '请输入正确的邮箱',
			type: 'warning',
			offset: 300,
		});
		return false;
	}
	if (paymentStatus.value === '') {
		ElMessage({
			message: '请选择支付方式',
			type: 'warning',
			offset: 300,
		});
		return false;
	}
	return true;
}
async function getPayCode() {
	let orderDetails = selectedReport.value.map((item) => {
		return {
			orderCount: 1,
			businessType: 'REPORT_ORDER',
			buildingId: props.buildInfo.id,
			reportType: item.reportType,
			email: email.value,
			totalAmount: item.price,
		};
	});
	let params = {
		payType: paymentStatus.value,
		orderCount: 1,
		totalAmount: selectedReportPrice.value,
		discountAmount: selectedReportPrice.value,
		payableAmount: selectedReportPrice.value,
		orderDetails: orderDetails,
	};
	const res = await orderOreate(params);
	if (res.code == 200) {
		let data = res.data;
		if (paymentStatus.value === 'ALI_PC') {
			// 支付宝
			paymentURL.value = data.url;
		} else {
			// 微信
			const qrCodeDiv = qrcodeCanvas.value;
			QRCode.toCanvas(qrCodeDiv, data.url, (error) => {
				if (error) console.error(error);
			});
		}
		if (data.outTradeNo) {
			orderId.value = data.outTradeNo;
			// 开始轮询
			timerId.value = setInterval(() => {
				pollOrderStatus(orderId.value);
			}, 1500);
		}
	} else {
		//清空支付方式
		paymentStatus.value = '';
		//清空邮箱
		email.value = '';
	}
}
// 订单轮询获取支付状态
async function pollOrderStatus(orderId) {
	if (paymentStatus.value === '' || !checkbox.value) {
		return;
	}
	try {
		const response = await orderStatus(orderId);
		if (orderId === '') {
			return; // 提前返回，不执行后续代码
		}
		//PENDING("待支付"),        // 待支付
		// PAID("已支付"),          // 已支付
		// FAILED("支付失败"),      // 支付失败
		// CANCELLED("已取消"),     // 已取消
		// REFUNDED("已退款");      // 已退款
		if (response.code == 200) {
			let message = '支付成功';
			// 根据返回的状态更新状态提示信息
			switch (response.data) {
				case 'PAID':
					ElMessage({
						message: `支付成功`,
						type: 'success',
						offset: 300,
					});
					clearInterval(timerId.value);
					emit('success', {
						name: selectedReport.value.map((item) => item.name).join(),
						price: selectedReportPrice.value,
						type: props.dialogType,
						buildingName: props.buildInfo.buildingName,
						payType: paymentStatus.value == 'ALI_PC' ? '支付宝' : '微信',
					});
					// 关闭弹框
					handleClose();
					break;
				case 'FAILED':
					ElMessage({
						message: `支付失败`,
						type: 'error',
						offset: 300,
					});
					clearInterval(timerId.value);
					// 关闭弹框
					handleClose();
					break;
				case 'CANCELLED':
					ElMessage({
						message: `订单已取消`,
						type: 'warning',
						offset: 300,
					});
					clearInterval(timerId.value);
					break;
				case 'REFUNDED':
					ElMessage({
						message: `已退款`,
						type: 'warning',
						offset: 300,
					});
					clearInterval(timerId.value);
					break;
				default:
					break;
			}
		} else {
			clearInterval(timerId.value);
		}
	} catch (error) {
		clearInterval(timerId.value);
	}
}
function viewAgreement() {
	agreeShow.value = true;
}
</script>

<style scoped lang="less">
.dialog_title {
	font-size: 16px;
}
.dialog_container {
	padding: 0 16px;
	.align-center {
		align-items: center;
	}
	.label_wrap {
		padding: 12px 0;
		display: flex;
		gap: 24px;
		.label_title {
			min-width: 60px;
			font-weight: 400;
			color: #4e5969;
		}
		.label_content {
			font-weight: 600;
			color: #1d2129;
		}
		.type_wrap {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			gap: 10px;
			.type_item {
				position: relative;
				box-sizing: border-box;
				flex: 1 1 calc(25% - 10px);
				height: 110px;
				border: 1px solid #e6efff;
				border-top: 12px solid #e6efff;
				border-radius: 8px;
				padding: 12px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				.type_item_title {
					font-size: 16px;
					color: #1d2129;
					font-weight: 600;
				}
				.type_item_price {
					font-size: 28px;
					font-weight: 500;
					color: #1868f1;
					span {
						font-size: 16px;
					}
				}
				.checked_icon {
					position: absolute;
					top: 12px;
					right: 12px;
				}
			}
			.flex-row {
				flex-direction: row;
				align-items: center;
			}
			.active {
				border-color: #1868f1;
			}
			.disabled {
				border-color: gray;
				.type_item_title {
					font-size: 16px;
					color: gray;
					font-weight: 600;
				}
				.type_item_price {
					font-size: 28px;
					font-weight: 500;
					color: gray;
					span {
						font-size: 16px;
					}
				}
			}
		}
	}
	.pay_wrap {
		display: flex;
		.code_wrap {
			width: 120px;
			height: 120px;
			margin-right: 16px;
			.qrcode {
				height: 120px !important;
				width: 120px !important;
			}
		}
		.action_wrap {
			.check_boxAgreement {
				height: 22px;
				font-size: 14px;
				font-weight: 700;
				line-height: 22px;
				margin-bottom: 34px;
				.blueSpan {
					font-size: 14px;
					font-weight: 700;
					line-height: 22px;
					color: rgba(24, 104, 241, 1);
				}
			}
			.content_price {
				display: flex;
				height: 24px;
				margin-bottom: 10px;
				& > :nth-child(1) {
					font-size: 16px;
					font-weight: 700;
					line-height: 27px;
					color: #1d2129;
				}
				& > :nth-child(2) {
					font-size: 16px;
					font-weight: 700;
					line-height: 29px;
					color: #1868f1;
					margin: 0 8px;
				}
				& > :nth-child(3) {
					font-size: 28px;
					font-weight: 500;
					line-height: 24px;
					color: #1868f1;
					margin-bottom: -2px;
				}
			}
			.content_bottom {
				.el-radio-group {
					& > :nth-child(n) {
						height: 32px;
						margin-right: 10px;
					}
				}
			}
		}
	}
}
</style>
