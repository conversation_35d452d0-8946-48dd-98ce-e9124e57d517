<template>
	<!-- <div>自定义组合</div> -->
	<div class="body_box">
		<div class="custom_box">
			<!-- 评级通 -->
			<div class="rate custom_item">
				<div class="rate_head" style="display: flex;">
					<h2 style="margin: 0;margin-right: 50PX;">评级通</h2>
					<!-- <h3>资产列表</h3> -->
					<el-button plain @click="dialogVisible = true">
						自定义控件
						<el-icon style="margin-left: 20px;"><Plus color="#409EFF"/></el-icon>
					</el-button>
				</div>
				<!-- 已选择 -->
				<div class="rate_body">
					<div class="select" v-for="item in pitchonrate" :key="item">
						<p>{{ item.name }}</p>
					</div>
				</div>
				<!-- 对话框 -->
				<el-dialog
					v-model="dialogVisible"
					title="评级通"
					width="500"
					:before-close="handleClose"
				>
					<span>
						<el-table :data="rateList" style="width: 100%" @selection-change="handleSelectionChange" stripe>
							<el-table-column type="selection" width="55" ref="multipleTableRef" />
							<el-table-column
								v-for="(column, index) in tableColumns"
								:key="index"
								:label="column.label"
								:prop="column.prop"
								:width="column.width"
							/>
						</el-table>
						<el-pagination 
							layout="prev, pager, next" 
							:page-size="pageSize"
							:total="total" 
							@current-change="pageChange"
							:current-page="pageChange"
							/>
					</span>
					<template #footer>
					<div class="dialog-footer">
						<el-button type="primary" @click="onConfirm()">确定</el-button>
						<el-button  @click="dialogVisible = false">取消</el-button>
					</div>
					</template>
				</el-dialog>
			</div>
			<!-- 琥珀咨询 -->
			<div class="hupo custom_item">
				<h2>琥珀咨询</h2>
				<div class="hupoitem">
					<div class="hupohead">
						<h4 style="margin: 0;margin-right: 20PX;margin-top: 10PX;">市场分析</h4>
						<el-button plain @click="openHupo">
							自定义控件
						</el-button>
					</div>
					<el-scrollbar>
						<div class="scrollbar-flex-content">
						<div class="body_item"  v-for="item in 100" :key="item" style="width: 200px;">
							<p style="width: 200px;">名字</p>
							<p style="width: 200px;">描述</p>
							
						</div>
						</div>
					</el-scrollbar>
					
				</div>
				<div class="hupoitem">
					<div class="hupohead">
						<h4 style="margin: 0;margin-right: 20PX;margin-top: 10PX;">交易材料</h4>
						<el-button plain @click="dialogVisible = true">
							自定义控件
						</el-button>
					</div>
				</div>
				<div class="hupoitem">
					<div class="hupohead">
						<h4 style="margin: 0;margin-right: 20PX;margin-top: 10PX;">琥珀新闻</h4>
						<el-button plain @click="dialogVisible = true">
							自定义控件
						</el-button>
					</div>
				</div>
				<div class="hupoitem">
					<div class="hupohead">
						<h4 style="margin: 0;margin-right: 20PX;margin-top: 10PX;">地产金融</h4>
						<el-button plain @click="dialogVisible = true">
							自定义控件
						</el-button>
					</div>
				</div>
			</div>
			
		</div>
	</div>
</template>

<script setup>
import { ref ,onMounted} from 'vue'
import { ElMessageBox } from 'element-plus'
import http from '@/utils/http';
const dialogVisible = ref(false)
const tableColumns = [
	{ label: '名字', prop: 'name', width: '300' },
	{ label: '描述', prop: 'description', width: '500' },
];
const pageSize = ref(10)
const total = ref(0)
const handleClose = (done) => {
	done()
}
onMounted(() => {
	getRateList()
	getHupoList()
})
// 获取评级通列表
const rateList = ref([])
const history = ref([])
const getRateList = async() => {
	
    const res = await http.get('/api/api.do?act=customBuildingList')
	console.log('评级通列表',res);
	rateList.value = res.list
	// 从本地获取
	// data.value = localStorage.getItem('pitchonrate')
	// 获取历史记录
	history.value = JSON.parse(localStorage.getItem('historyrate'))
	// total.value = history.value.length
	// rateList.value = rateList.value.concat(history.value)
	total.value = rateList.value.length
	
}
// 获取琥珀咨询列表
const hupoList = ref([])
const getHupoList = async() => {
    const res = await http.get('/api/api.do?act=customAmberList')
	console.log('琥珀咨询列表',res);
}
const multipleSelection = ref([]);
const handleSelectionChange = (val) => {
	multipleSelection.value = val;
}
// 实现点击切换
// const pageChange = (val) => {
// 	console.log('pageChange',val);
// 	getRateList()
// }
const page = ref(1)
const currentPage = ref(1)
const pageChange = (val) => {
    console.log(11,val);
	currentPage.value = val;
	page.value = val;
	getRateList()
}

// 点击确定评级通
const pitchonrate = ref([])
const onConfirm = async() => {
    // 拿到选择的数据
	pitchonrate.value = multipleSelection.value
	dialogVisible.value = false
	console.log('选择',pitchonrate.value);
	// 存入本地
	localStorage.setItem('pitchonrate',JSON.stringify(pitchonrate.value))
}
</script>

<style lang="less" scoped>
.body_box {
	display: flex;
	background-color: #f0f0f0;
	width: 100vw;
	height: 100vh;
}
.custom_box{
	// width: 100px;
	// height: 100px;
	margin:100px auto;
	display: flex;

}
.custom_item {
	margin: 20px;
	padding: 20px;
}
.rate {
	width: 400px;
	height: 100%;
	background-color: #fff;
	border: 1px solid #000;
	border-radius: 15px;

	overflow-y:auto
}
.hupo {
	width: 500px;
	height: 100%;
	background-color: #fff;
	border: 1px solid #000;
	border-radius: 15px;
	margin-left: 200px;
}
.hupoitem {
	// display: flex;
	// justify-content: space-between;
	margin: 20px;
	vertical-align: middle;
}
.hupohead {
	display: flex;
}
.rate_body {
	margin-top: 30px;
}
.select {
	width: 300px;
	height: 50px;
	border: #000 solid 1px;
	border-radius: 15px;
	// padding: 10px;
	margin: 10px;
	padding-left: 10px;
}
.hupo_body {
	display: flex;
	// overflow-x: auto;
}
.body_item {
	// width: 300px !important;
	// height: 70px;
	border: #000 solid 1px;
	border-radius: 15px;
	margin-right: 10px;
}
.scrollbar-flex-content {
  display: flex;
}
.scrollbar-demo-item {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 50px;
  margin: 10px;
  text-align: center;
  border-radius: 4px;
  background: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}
</style>