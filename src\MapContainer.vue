<template>
	<div :id="containerId" class="map_box" style="position: absolute; top: 0px; height: calc(100vh - 56px); width: 100%"></div>

	<el-dialog v-model="dialogVisible" :width="630" :close-on-click-modal="false" :show-close="true" align-center class="map_dialog">
		<div class="map_dialogDetail">
			<div class="header">
				<div class="left">
					<img src="@/assets/mobileEndYut.png" v-if="!activeMarker.buildingMainImg" class="property-img" />
					<el-image
						:src="`${http_oa}${activeMarker.buildingMainImg}`"
						:initial-index="0"
						class="property-img"
						v-else
						fit="cover"
						:preview-src-list="[`${http_oa}${activeMarker.buildingMainImg}`]"
					/>
				</div>
				<div class="right">
					<div class="rightFlex">
						<div class="title">{{ activeMarker.buildingName }}</div>
						<div class="level" :style="handleLevelColor(activeMarker.buildingRate)">{{ activeMarker.buildingRate }}级</div>
						<div class="certified" v-if="activeMarker.securitization">已证券化</div>
						<div class="uncertified" v-if="!activeMarker.securitization">未证券化</div>
					</div>
					<div class="address">
						<el-icon><Location /></el-icon>
						{{ activeMarker.address }}
					</div>
					<div class="address_send">
						<img src="@/assets/send.png" alt="" />
						{{ activeMarker.businessDistrict }}
					</div>
				</div>
			</div>

			<!-- 商圈信息 -->
			<!-- <div class="business-area">
				<div class="title">所在商圈</div>
				<div class="address">{{ activeMarker.businessDistrict }}</div>
			</div> -->

			<div class="business-area">
				<div class="title_box">
					<div class="title_left"></div>
					<div class="title_right">建筑物介绍</div>
				</div>
				<div class="address">
					{{ activeMarker.buildingSummary }}
					<div class="copyIcon" @click="handlerCopy(activeMarker.buildingSummary)">
						<div class="copyIcon_box">
							<div class="copyIcon_img">
								<img src="@/assets/copy.png" alt="" />
							</div>
							<div class="copyIcon_text">复制</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 商圈信息 -->
			<div class="business-area">
				<div class="title_box">
					<div class="title_left"></div>
					<div class="title_right">基本信息</div>
				</div>
				<div class="info-grid">
					<div class="info-item" v-show="activeMarker[item.value]" v-for="(item, index) in activeMarkerBasic" :key="index">
						<div class="value">{{ activeMarker[item.value] }}{{ handleMontage(item.label) }}</div>
						<div class="label">{{ item.label }}</div>
					</div>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script setup>
import coordinate from '@/assets/coordinate.png';
import coordinateTray from '@/assets/coordinateTray.png';
import { onMounted, onUnmounted, ref, defineEmits, defineProps, nextTick, reactive, defineExpose } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useStore } from './store';
import { getBuildingOverview } from '@/api/syt.js';

const store = useStore();
const { map_key, http_oa } = storeToRefs(store);
const emit = defineEmits(['handleMapClick', 'updateRadius', 'handlePolygon', 'handleupDateCity']);
const props = defineProps({
	radius: {
		type: Number,
		default: 300,
	},
	markers: {
		type: Array,
		default: [],
	},
	radioDraw: {
		type: String,
		default: '',
	},
});

const dialogVisible = ref(false);
const mouseEnterHover = ref(false);
const containerId = 'container'; //地图容器id
let map = null; //地图
let circle = null; //圆
let infoWindow = null; //信息弹窗
let circleEditor = null; // Move circleEditor definition to the outer scope
const maxRadius = 1000; // 设置最大半径值
const minRadius = 100; // 设置最小半径值
let rangeNum = ref(1);
let clickPositionObj = reactive({}); //点击位置
var mouseTool = null; // 鼠标工具 多边形使用
const coordinates = ref([]); //多边形数组
const coordinatess = ref([]);
let boundsPosition = null;
let polygon = null;
const polygonEditor = ref(false);
const lastMarker = ref(null);
const lastMarkerContent = ref(null);
// 选中的marker
const activeMarker = ref(null);
const activeMarkerBasic = ref([
	{
		label: '建筑面积',
		value: 'buildingArea',
	},
	{
		label: '资产类型',
		value: 'buildingType',
	},
	{
		label: '停车场',
		value: 'carPark',
	},
	{
		label: '得房率',
		value: 'roomRate',
	},
	{
		label: '总层数',
		value: 'totalFloors',
	},
	{
		label: '车位费',
		value: 'parkingFee',
	},
	{
		label: '物业费',
		value: 'propertyFee',
	},
	{
		label: '租金',
		value: 'rental',
	},
]);

const data = reactive({
	//点位信息列表
	marker_list: [],
});
onMounted(() => {
	initMap();
});
//销毁地图
onUnmounted(() => {
	if (map) {
		map.destroy();
	}
	// 移除圆
	if (circle) {
		circle.setMap(null);
	}
});

watch(
	() => props.radioDraw,
	(val, oldVal) => {
		if (val === 'circle') {
			// 取消监听地图事件
			coordinates.value = [];
			map.clearMap();
			// 监听地图点击事件
			map.on('click', handleMapClick);
		} else if (val === 'polygon') {
			drawPolygon();
			map.setStatus({
				dragEnable: false,
			});
			map.off('click', handleMapClick);
		} else {
			map.setStatus({
				dragEnable: true,
			});
			map.clearMap();
			coordinates.value = [];
			map.off('click', handleMapClick);
		}
	}
);

//初始化地图
const initMap = () => {
	window._AMapSecurityConfig = {
		securityJsCode: '1995e7d7d4e5cd203f1a78aa3d417154',
	};
	AMapLoader.load({
		key: map_key.value,
		version: '2.0',
		plugins: ['AMap.CircleEditor', 'AMap.Geolocation', 'AMap.Geocoder'],
	})
		.then((AMap) => {
			map = new AMap.Map(containerId, {
				viewMode: '2D',
				zoom: 16,
				zooms: [14, 18],
				// center: [121.487852, 31.237693],
			});
			map.on('complete', () => {
				getLoaction();
			});

			map.on('moveend', () => {
				rangeNum.value++;
				if (polygonEditor.value) {
					polygonEditor.value = false;
				} else {
					handleGetBounds(clickPositionObj, rangeNum.value);
				}
			});

			//异步加载控件
			AMap.plugin(['AMap.MouseTool'], () => {
				mouseTool = new AMap.MouseTool(map);
				// mouseTool.enable();
			});

			//监听缩放级别
			map.on('zoomend', changeZoomend);

			if (mouseTool) {
				mouseTool.on('draw', function (e) {
					//通过覆盖物的事件来获取多边形的坐标
					coordinatess.value = e.obj;
					coordinates.value = e.obj.getPath().map((point) => [point.lng, point.lat]);
					// addMarker();
					polygon = new AMap.Polygon({
						path: coordinates.value,
						strokeColor: '#FF0000',
						strokeWeight: 2,
						fillColor: '#FFFFFF',
						fillOpacity: 0.5,
					});
					map.setStatus({
						dragEnable: true,
					});
					boundsPosition = polygon.getBounds();
					emit('clickChild', { bounds: boundsPosition, type: '1' });
					mouseTool.close();
				});
			}
		})
		.catch((e) => {
			console.log(e);
		});
};

function handleMontage(label) {
	if (label === '建筑面积') {
		return '㎡';
	}
	if (label === '得房率') {
		return '%';
	}
	if (label === '物业费') {
		return '元/㎡/月';
	}
	if (label === '租金') {
		return '元/㎡/天';
	}

	return '';
}

function handleLevelColor(level) {
	if (level == 'S') {
		return {
			background: `linear-gradient(90deg, #9D71DA 0%, #722ED1 100%)`,
		};
	}

	if (level == 'A+') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}
	if (level == 'A') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}

	if (level == 'B+') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}
	if (level == 'B') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}

	if (level == 'C') {
		return {
			background: `linear-gradient(90deg, #FFA44D 0%, #FF7D00 100%)`,
		};
	}
}

// 复制
function handlerCopy(name) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(name)
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = name;
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

// 通过坐标获取地址信息
function getAddressInfo(lng, lat) {
	AMap.plugin(['AMap.Geocoder'], function () {
		const geocoder = new AMap.Geocoder({
			radius: 1000,
			extensions: 'all',
		});

		geocoder.getAddress([lng, lat], (status, result) => {
			if (status === 'complete' && result.info === 'OK') {
				const addressComponent = result.regeocode.addressComponent;
				const address = {
					province: addressComponent.province,
					city: addressComponent.city || addressComponent.province,
					district: addressComponent.district,
					address: addressComponent.formattedAddress,
					location: {
						lng,
						lat,
					},
				};
				emit('handleupDateCity', address);
				handleSetCenter([lng, lat]);
			} else {
				reject(new Error('获取地址信息失败'));
			}
		});
	});
}

//获取当前坐标
const getLoaction = () => {
	// 添加定位
	AMap.plugin(['AMap.Geolocation'], function () {
		const geolocation = new AMap.Geolocation({
			enableHighAccuracy: true,
			timeout: 2000,
			showMarker: false,
			showCircle: false,
			convert: true, // 自动偏移坐标，偏移后的坐标为高德坐标
		});
		map.addControl(geolocation);
		// 先尝试浏览器定位
		geolocation.getCurrentPosition(function (status, result) {
			if (status === 'complete') {
				const { position } = result;
				getAddressInfo(position.lng, position.lat);
			} else {
				getAddressInfo(121.487852, 31.237693);
			}
		});
	});
};

/**
 * @function handleGetBounds 获取地图四个角经纬度
 */
function handleGetBounds(clickPosition, type) {
	// 获取四个角的经纬度
	var bounds = map.getBounds();
	nextTick(() => {
		emit('clickChild', { clickPosition, bounds, type });
	});
}

// 地图点击事件
const handleMapClick = (e) => {
	// 获取点击位置经纬度
	clickPositionObj = e.lnglat;
	if (!circle) {
		emit('updateRadius', 300);
	}
	// handleGetBounds(clickPositionObj, {});
	map.setCenter(clickPositionObj);
	setTimeout(() => {
		creatCircle(clickPositionObj);
		clearMarker();
		//添加点位信息
		addMarker();
	}, 500);
};

//设置地图中心点
function handleSetCenter(params, type, num) {
	if (num) {
		polygonEditor.value = true;
	}
	if (params) {
		map.setCenter(params);
	}

	if (type) {
		handleGetBounds(clickPositionObj, {});
	}
}

const changeZoomend = () => {
	var currentZoom = map.getZoom();
	if (currentZoom == 17 || currentZoom == 16) {
		addMarker();
	}
};

//清除资产项点位
const clearMarker = () => {
	if (infoWindow) {
		infoWindow.close();
	}
	data.marker_list.forEach((a) => {
		a.setMap(null);
	});
	data.marker_list = [];
};
//清除圆
function handleCircle() {
	rangeNum.value = 0;
	// 如果存在圆，先移除
	if (circle) {
		// 关闭圆编辑器
		circleEditor.close();
		// 移除上一个圆
		circle.setMap(null);
	}
	circle = null;
}

//渲染点位
const addMarker = () => {
	clearMarker();
	//点标记显示内容
	if (props.radioDraw !== 'polygon') {
		//正常显示 圆形范围内的楼宇
		props.markers.forEach((e) => {
			let position;
			if (e.lng) {
				position = new AMap.LngLat(e.lng, e.lat);
			}
			var currentZoom = map.getZoom(); //获取当前地图级别
			let community = `<div class="community" >${e.buildingName}<div class="arrow_up"></div></div>`;
			let icon_img = `<div class="icon_img"><img class="icon_imgs" src="${http_oa.value + e.mainImgUrl}"></img></div>`;
			//判断当前地图级别是否大于等于17级
			const content = currentZoom >= 17 ? icon_img : community; //根据地图级别显示不同的内容
			const marker = new AMap.Marker({
				position: position,
				// content: markerContent, //将 html 传给 content
				content: content,
				offset: new AMap.Pixel(-35, -30), //以 icon 的 [center bottom] 为原点
			});
			marker.data = e;
			marker.on('click', clickMarker);
			marker.on('rightclick', rightClickMarker);
			marker.on('mouseover', mouseoverMarker);
			marker.on('mouseout', mouseoutMarker);
			marker.setMap(map);
			data.marker_list.push(marker);
			e.markerObj = marker;
		});
	} else {
		//多边形显示
		let filteredMarkers = [];

		props.markers.forEach((e) => {
			let position;
			if (e.lng) {
				position = new AMap.LngLat(e.lng, e.lat);
			}
			var currentZoom = map.getZoom(); //获取当前地图级别
			let community = `<div class="community">${e.buildingName}<div class="arrow_up"></div></div>`;
			let icon_img = `<div class="icon_img"><img class="icon_imgs" src="${http_oa.value + e.mainImgUrl}"></img></div>`;
			//判断当前地图级别是否大于等于17级
			const content = currentZoom >= 17 ? icon_img : community; //根据地图级别显示不同的内容

			var marker1 = new AMap.Marker({
				map: map,
				position: position,
				content: content,
				offset: new AMap.Pixel(-35, -30), //以 icon 的 [center bottom] 为原点
			});
			var marker1InPolygon = coordinatess.value.contains(marker1.getPosition()); //是否包含marker1

			marker1.data = e;
			marker1.on('click', clickMarker);
			if (!marker1InPolygon) {
				//清楚marker
				marker1.setMap(null);
			} else {
				filteredMarkers.push(e);
				marker1.setMap(map);
			}
			data.marker_list.push(marker1);
			e.markerObj = marker1;
		});

		emit('handlePolygon', filteredMarkers);
	}
};

//获取楼宇概览
function handlegetBuildingOverview(buildingId) {
	getBuildingOverview({ buildingId: buildingId }).then((res) => {
		if (res.code === 200) {
			nextTick(() => {
				dialogVisible.value = true;
				activeMarker.value = res.data;
			});
		}
	});
}

function rightClickMarker(e, type) {
	console.log('🚀 ~ rightClickMarker ~ e, type:', e, type);
}
function mouseoverMarker(e) {
	console.log("🚀 ~ mouseoverMarker ~ e:", e)
	console.log("🚀 ~ mouseoverMarker ~ e.target.dom.getBoundingClientRect():", e.target.dom.getBoundingClientRect())
	emit('mouseoverMarker', e.pixel);
}
function mouseoutMarker(e) {
	emit('mouseoutMarker', e.pixel);
}

//点击点位打开信息弹窗
const clickMarker = (e, type) => {
	// 通过点击marker存放上一次点击的marker和值，点击第二个时，第一个恢复原来样式
	if (e.target.getContent) {
		if (lastMarker.value && lastMarker.value !== e.target.data.buildingId) {
			lastMarker.value.setOffset(new AMap.Pixel(-35, -30));
			lastMarker.value.setOptions({ zIndex: 10 }); // 提高层级显示在更前面
			lastMarker.value.setContent(lastMarkerContent.value);
			// 新的marker存放
			lastMarker.value = e.target;
			lastMarkerContent.value = e.target.getContent();
			e.target.setContent(
				`<div class="zcbuilding_Content"><div class="content_title">${e.target.data.buildingName}</div><img class="zcbuilding" src="${coordinate}" alt="" ></img>
        <img class="coordinateTray" src="${coordinateTray}" alt=""></img></div>`
			);
			e.target.setOptions({ zIndex: 22 }); // 提高层级显示在更前面
			e.target.setOffset(new AMap.Pixel(-60, -70));
		} else {
			lastMarker.value = e.target; // 存放标点
			lastMarkerContent.value = e.target.getContent(); // 存放内容
			e.target.setContent(
				`<div class="zcbuilding_Content"><div class="content_title">${e.target.data.buildingName}</div><img class="zcbuilding" src="${coordinate}" alt="" ></img>
        <img class="coordinateTray" src="${coordinateTray}" alt=""></img></div>`
			);
			e.target.setOptions({ zIndex: 22 }); // 提高层级显示在更前面
			e.target.setOffset(new AMap.Pixel(-70, -70));
		}
		emit('handleParentScroll', e.target.data.buildingId);
	} else {
		if (type) {
			if (lastMarker.value && lastMarker.value !== e.target.data.buildingId) {
				lastMarker.value.setOffset(new AMap.Pixel(-35, -30));
				lastMarker.value.setOptions({ zIndex: 10 }); // 提高层级显示在更前面
				lastMarker.value.setContent(lastMarkerContent.value);
				// 新的marker存放
				lastMarker.value = e.target.data.markerObj;
				lastMarkerContent.value = e.target.data.markerObj.getContent();
				e.target.data.markerObj.setContent(
					`<div class="zcbuilding_Content"><div class="content_title">${e.target.data.buildingName}</div><img class="zcbuilding" src="${coordinate}" alt="" ></img>
        <img class="coordinateTray" src="${coordinateTray}" alt=""></img></div>`
				);
				e.target.data.markerObj.setOptions({ zIndex: 22 }); // 提高层级显示在更前面
				e.target.data.markerObj.setOffset(new AMap.Pixel(-60, -70));
			} else {
				lastMarker.value = e.target.data.markerObj; // 存放标点
				lastMarkerContent.value = e.target.data.markerObj.getContent(); // 存放内容
				e.target.data.markerObj.setContent(
					`<div class="zcbuilding_Content"><div class="content_title">${e.target.data.buildingName}</div><img class="zcbuilding" src="${coordinate}" alt="" ></img>
        <img class="coordinateTray" src="${coordinateTray}" alt=""></img></div>`
				);
				e.target.data.markerObj.setOptions({ zIndex: 22 }); // 提高层级显示在更前面
				e.target.data.markerObj.setOffset(new AMap.Pixel(-70, -70));
			}
		}
		// if (lastMarker.value) {
		// 	lastMarker.value.setOffset(new AMap.Pixel(-35, -30));
		// 	lastMarker.value.setContent(lastMarkerContent.value);
		// 	lastMarker.value = null;
		// 	lastMarkerContent.value = null;
		// }
	}
	polygonEditor.value = true;
	map.setCenter([e.target.data.lng, e.target.data.lat]);
	// if (type == '1') return;
	// handlegetBuildingOverview(e.target.data.buildingId);
};

//多边形基础配置
function drawPolygon() {
	if (map) {
		mouseTool.polygon({
			// path: coordinates.value,
			strokeColor: '#1868f1', //线颜色
			strokeOpacity: 1, //线透明度
			// strokeWeight: 6,
			fillColor: '#409EFF', //填充颜色
			fillOpacity: 0.4, //填充透明度
			// 线样式还支持 'dashed'
			strokeStyle: 'solid',
			// strokeStyle是dashed时有效
			// strokeDasharray: [30,10],//补充线样式
		});
	}
}

//创建圆
const creatCircle = (clickPosition, number) => {
	if (!clickPositionObj.lat) {
		ElMessage.warning('请先选择中心点！');
		return;
	}

	// 如果存在圆，先移除
	if (circle) {
		// 关闭圆编辑器
		circleEditor.close();
		// 移除上一个圆
		circle.setMap(null);
	}

	// 创建新的圆
	circle = new AMap.Circle({
		center: clickPositionObj || clickPosition, // 圆心位置为点击位置
		radius: number || props.radius, // 半径（单位：米）
		strokeColor: '#67C23A',
		strokeOpacity: 1,
		strokeWeight: 3,
		fillColor: '#409EFF',
		fillOpacity: 0.35,
	});

	// 初始化圆编辑器
	circleEditor = new AMap.CircleEditor(map, circle);
	circleEditor.open();

	// 在编辑器调整半径时，手动检查并调整
	circleEditor.on('adjust', (e) => {
		const currentRadius = circle.getRadius();
		const newRadius = Math.min(Math.max(currentRadius, minRadius), maxRadius);
		emit('updateRadius', newRadius);
		emit('searchMap');
		if (currentRadius > 1000) {
			ElMessage.warning('半径不能超过1000');
			circleEditor.close();
			return;
		}
		if (currentRadius !== newRadius) {
			circle.setRadius(newRadius);
		}
	});

	// 将新的圆添加到地图
	circle.setMap(map);
};

function setZoom(type) {
	let currentZoom = map.getZoom();
	if (type === 'add') {
		if (currentZoom < 18) {
			currentZoom = currentZoom + 1;
		} else {
			return;
		}
	} else {
		if (currentZoom > 14) {
			currentZoom = currentZoom - 1;
		} else {
			return;
		}
	}

	map.setZoom(currentZoom);
	setTimeout(() => {
		handleGetBounds(clickPositionObj, rangeNum.value);
	}, 100);
}

defineExpose({
	setZoom, //设置地图缩放
	addMarker, //添加点位
	creatCircle, //创建圆
	clickMarker, //点击点位打开信息弹窗
	clearMarker, //清除资产项点位
	handleCircle, //清除圆
	handleSetCenter, //设置地图中心点
});
</script>

<style lang="scss">
.marker_main {
	width: 336px;
	height: 104px;
	box-sizing: border-box;
	position: relative;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	z-index: 3;
}

.marker_main {
	.img {
		width: 96px;
		height: 96px;
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
		border-radius: 2px;
		img {
			width: auto;
			height: 100%;
		}
	}
}

.community {
	white-space: nowrap;
	padding: 8px 12px;
	color: #fff;
	border-radius: 15px;
	font-family: PingFangSC-Medium;
	font-size: 12px;
	letter-spacing: 0;
	height: 15px;
	line-height: 15px;
	background: #1868f1 !important;
	&:hover {
		background: #4080ff !important;
	}
}
.icon_img {
	width: 48px;
	height: 48px;
	background: #86b1f1;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	overflow: hidden;
	border: 2px solid rgba(255, 255, 255, 1);
	box-sizing: border-box;
}
.icon_imgs {
	width: auto;
	height: 100%;
}
.arrow_up {
	border: 4px solid transparent;
	display: block;
	width: 0;
	height: 0;
	margin: 8px auto 0;
	transition: all 0.15s ease-in-out;
	border-top-color: #3072f6 !important;
}
.map_box {
	cursor: pointer !important;
	.amap-logo {
		display: none !important;
	}
	.amap-copyright {
		display: none !important;
	}
}

.tips_boxts {
	width: 222px;
	height: 104px;
	text-align: left;
	margin-left: 8px;
	padding: 5px 0;
	box-sizing: border-box;

	.title {
		width: 100%;
		font-size: 17px;
		font-weight: bold;
		display: flex;
		justify-content: flex-start;
		align-items: center;

		.text {
			width: 120px;
		}

		span {
			font-size: 12px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			color: rgba(134, 144, 156, 1);
			margin-left: 5px;

			&::before {
				content: '';
				width: 5px;
				height: 5px;
				display: inline-block;
				background-color: rgba(201, 205, 212, 1);
				margin-right: 5px;
			}
		}
	}

	.address {
		max-width: calc(100% - 20px);
		// width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		font-size: 10px;
		color: rgba(134, 144, 156, 1);
		white-space: nowrap; /* 防止文本换行 */
		overflow: hidden; /* 隐藏超出容器的内容 */
		text-overflow: ellipsis; /* 超出容器的内容用省略号表示 */
	}

	.tag_list {
		width: 100%;
		margin-top: 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;

		.tag1 {
			white-space: nowrap;
			padding: 2px 5px;
			box-sizing: border-box;
			font-size: 10px;
			margin: 0 4px 0 0;
			background-color: rgba(231, 231, 231, 1);
			color: rgba(55, 125, 243, 1);
		}
	}

	.money {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		font-size: 12px;
		margin-top: 12px;
		height: 22px;
		span {
			font-size: 14px;
			font-weight: bold;
			color: rgba(24, 104, 241, 1);
		}
	}
}
.amap-info-content {
	padding: 0px 4px 0px 4px !important;
	border-radius: 4px;
}

.amap-info-close {
	display: none;
}

.map_dialog {
	border-radius: 8px;
	padding: 24px !important;

	.el-dialog__header {
		margin: 0;
		padding: 0;
		.el-dialog__title {
			font-size: 16px;
			font-weight: 500;
		}
	}
}
.zcbuilding_Content {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 48px;
	.content_title {
		width: max-content;
		height: 24px;
		padding: 10px 16px;
		background: #e8f3ff;
		border: 2px solid #4080ff;
		border-radius: 24px;
		font-weight: 500;
		font-size: 20px;
		line-height: 24px;
		color: #1868f1;
	}
	.zcbuilding {
		width: 45px;
		margin-top: -10px;
		object-fit: contain;
	}
	.coordinateTray {
		width: 36px;
		margin-top: -11px;
		object-fit: contain;
	}
}
</style>
<style lang="scss" scoped>
.map_dialogDetail {
	.header {
		display: flex;
		gap: 16px;
		margin-bottom: 27px;
		.property-img {
			width: 128px;
			height: 128px;
			border-radius: 4px;
		}
		.title {
			font-weight: 500;
			font-size: 24px;
			line-height: 32px;
			color: #1d2129;
		}
		.address {
			font-weight: 400;
			font-size: 14px;
			line-height: 22px;
			color: #4e5969;
			display: flex;
			margin-bottom: 8px;
			align-items: center;
			.el-icon {
				margin-right: 4px;
			}
		}
	}

	.business-area {
		position: relative;
		.title_box {
			height: 40px;
			background: #f7f8fa;
			border-radius: 4px;
			display: flex;
			align-items: center;
			.title_left {
				width: 4px;
				height: 12px;
				border-radius: 4px;
				margin: -1px 6px 0 12px;
				background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
			}
			.title_right {
				font-weight: 500;
				font-size: 16px;
				line-height: 24px;
				color: #1d2129;
			}
		}
		.address {
			font-weight: 400;
			font-size: 14px;
			color: #4e5969;
			padding: 0px 22px;
			margin: 16px 0 24px 0;
			line-height: 22px;
			border-radius: 4px;
		}
		.copyIcon {
			width: 50px;
			height: 14px;
			position: relative;
			font-size: larger;
			cursor: pointer;
			display: flex;
			align-items: center;
			.copyIcon_box {
				display: flex;
				align-items: center;
				position: absolute;
				top: -3px;
			}
			.copyIcon_img {
				width: 16px;
				height: 16px;
				margin-right: 2px;
				img {
					width: 16px;
					height: 16px;
				}
			}
			.copyIcon_text {
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: #1868f1;
			}
			display: none;
		}
	}
	.business-area:hover {
		.copyIcon {
			display: inline-block;
		}
	}
	.right {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	.rightFlex {
		display: flex;
		align-items: center;
		margin-bottom: 12px;
		.title {
			font-weight: 500;
			font-size: 24px;
			line-height: 32px;
			color: #1d2129;
		}
		.level {
			font-weight: bold;
			font-size: 14px;
			height: 22px;
			border-radius: 4px;
			color: #fff;
			text-align: center;
			line-height: 22px;
			padding: 1px 9px;
			margin-left: 8px;
		}
		.certified {
			color: #1868f1;
			height: 22px;
			font-size: 14px;
			padding: 0 9px;
			border: 1px solid #1868f1;
			background: #e8f3ff;
			border-radius: 4px;
			line-height: 22px;
			margin-left: 4px;
		}
		.uncertified {
			font-weight: 500;
			font-size: 14px;
			margin-left: 4px;
			color: #1d2129;
			height: 22px;
			padding: 0 9px;
			background: #f2f3f5;
			border: 1px solid #7f7f7f;
			border-radius: 4px;
			line-height: 22px;
		}
	}
	.address_send {
		color: #4e5969;
		font-size: 14px;
		margin-bottom: 8px;
		display: flex;
		align-items: center;
		img {
			width: 16px;
			height: 16px;
			margin-right: 4px;
		}
	}

	.info-grid {
		border-radius: 4px;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 16px 77px;
		padding: 14px 10px;
		margin-top: 16px;
		.info-item {
			text-align: center;
			.value {
				height: 16.09px;
				font-size: 14px;
				font-weight: bold;
				color: #333;
			}

			.label {
				font-size: 12px;
				color: #666;
				margin-top: 4px;
			}
		}
	}
}
</style>
