<template>
	<div class="tubiao_box" ref="echartsContaine">
		
	</div>
</template>
<script setup>
	import * as echarts from 'echarts';
import { onMounted, ref } from 'vue';
	const echartsContaine = ref(null);
	let myChart = null;
	const newArr = ref([])
	const props = defineProps({
		sixRingDateList:{
			type:Array,
			default:[]
		}
	})
	const legend1 = ref('')
	const legend2 = ref('')
	let chartOption = {

		legend: {
			data: ['数据1','数据2'],
		},
		radar: {
			indicator: [{
				name: '维护情况',
			}, {
				name: '周边配套',
			}, {
				name: '区域潜力',
			}, {
				name: '人均消费能力',
			}, {
				name: '商业活力',
			}, {
				name: '评估结果',
			}]
		},
		series: [{
			
			type: 'radar',
			data: [],
			name: '数据1',

		},{
			type: 'radar',
			data: [],
			name: '数据2',

		} ],
	}
	onMounted(()=>{
		myChart = echarts.init(echartsContaine.value);
		myChart.setOption(chartOption);
	})
	const echartsData = (sixRingDateList) => {
		console.log(sixRingDateList[0], 7247812);
		 newArr.value = sixRingDateList.map((item) => {
			return [item.six01.维护情况, item.six02.周边配套, item.six03.区域潜力, item.six04.人均消费能力, item.six05.商业活力, item
				.six06.评估结果
			];
		})
		legend1.value = sixRingDateList[0].name
		legend2.value = sixRingDateList[1].name
		console.log(newArr.value, 72147812);
		updateEcharts(sixRingDateList)
	}
	const updateEcharts = (sixRingDateList) => {
		console.log(sixRingDateList.length, 77812);
		if (sixRingDateList.length > 0) {
			// chartOption.title.top = 30
			chartOption.legend = {
					data: [legend1.value,legend2.value],
				}
				chartOption.series =  [{
		
					highlight: {
						itemStyle: {
							borderWidth: 4,
							borderColor: '#1E90FF',
						},
					},

					type: 'radar',
					data: [{
							value: newArr.value[0],
							name: legend1.value,
						},
						{
							value: newArr.value[1],
							name: legend2.value,
						},
					],
				}]
			myChart.setOption(chartOption);
		}
	}
	defineExpose({
		echartsData
	})
	
	
</script>
<style lang="less" scoped>
	.tubiao_box{
		width: 500px;
		min-height: 550px;
		padding: 0 80px;
		box-sizing: border-box;
	}
</style>