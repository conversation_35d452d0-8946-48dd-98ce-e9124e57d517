<template>
	<div :id="containerId" :style="props.style"></div>
</template>

<script setup>
import icon1 from './assets/images/statistics/add_icon.png';
let newIcon = ref(null);
import { onMounted, onUnmounted, ref, toRefs, defineProps, watch, nextTick } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { useStore } from './store';
import { storeToRefs } from 'pinia';
import { set } from 'vue-demi';
const emit = defineEmits(['handleMapClick']);
const props = defineProps({
	//地图的宽高
	style: {
		type: Object,
		default: {
			width: '900px',
			height: '400px',
		},
	},
	//半径
	radius: Number,
	//坐标
	coordinate: {
		type: String,
		default: '120.112711,36.018996',
	},
	//地图id
	containerIds: {
		type: String,
		default: 'mian',
	},
	//多边形
	polygonArr: {
		type: Array,
		default: [],
	},
});
// console.log(props.coordinate.split(',')[0],1234);
const { radius } = toRefs(props);
const store = useStore();
const { map_key } = storeToRefs(store);

const containerId = ref('mian');
let map = null;
let circle = null;

// 监听polygonArr变化
watch(
	() => props.polygonArr,
	() => {
		if (props.containerIds) {
			var timestamp = new Date().getTime();
			containerId.value = props.containerIds + timestamp;
		}
		setTimeout(() => {
			initMap();
		});
	},
	{ deep: true }
);

onMounted(() => {
	// 多个地图时传不同的id
	if (props.containerIds) {
		var timestamp = new Date().getTime();
		containerId.value = props.containerIds + timestamp;
	}
	setTimeout(() => {
		initMap();
	});
});

onUnmounted(() => {
	if (map) {
		map.destroy();
	}

	if (circle) {
		circle.setMap(null);
	}
});
const initMap = () => {
	AMapLoader.load({
		key: map_key.value,
		version: '2.0',
		plugins: [],
	})
		.then((AMap) => {
			map = new AMap.Map(containerId.value, {
				viewMode: '2D',
				zoom: 15,
				showControl: false,
				// center: [props.coordinate.split(',')[0], props.coordinate.split(',')[1]],
			});
			//如果存在多边形，绘制多边形
			if (props.polygonArr.length > 0) {
				new AMap.Polygon({
					map: map,
					path: props.polygonArr, //设置多边形边界路径
					strokeColor: '#1868F1', //线颜色
					strokeOpacity: 0.8, //线透明度
					strokeWeight: 2, //线宽
					fillColor: '#1868F1', //填充色
					fillOpacity: 0.27, //填充透明度
				});
				map.setFitView();
			} else {
				map.setZoomAndCenter(15, [props.coordinate.split(',')[0], props.coordinate.split(',')[1]]);
				// 监听地图点击事件
				map.on('click', handleMapClick);
				setTimeout(() => {
					//添加点位信息
					addMarker();
				}, 500);
			}
		})
		.catch((e) => {
			console.log(e);
		});
};
//添加点位
const addMarker = () => {
	const position = new AMap.LngLat(props.coordinate.split(',')[0], props.coordinate.split(',')[1]);
	newIcon = new AMap.Icon({
		// 图标的取图地址
		image: icon1,
		// 图标所用图片大小
		imageSize: new AMap.Size(14, 20),
	});
	const marker = new AMap.Marker({
		position: position,
		// content: markerContent, //将 html 传给 content
		icon: newIcon,
		offset: new AMap.Pixel(-13, -30), //以 icon 的 [center bottom] 为原点
	});
	marker.setMap(map);
};
function handleMapClick(e) {
	const clickPosition = e.lnglat;
	const radiusValue = radius.value || 1000;
	emit('clickChildReal', clickPosition.lng, clickPosition.lat);
	emit('searchMapReal');

	// // 如果存在圆，先移除
	// if (circle) {
	// 	circle.setMap(null);
	// }

	// // 创建新的圆
	// circle = new AMap.Circle({
	// 	center: clickPosition, // 圆心位置为点击位置
	// 	radius: radiusValue, // 半径（单位：米）
	// 	strokeColor: '#67C23A',
	// 	strokeOpacity: 1,
	// 	strokeWeight: 3,
	// 	fillColor: '#409EFF',
	// 	fillOpacity: 0.35,
	// });

	// // 将新的圆添加到地图
	// circle.setMap(map);
}
</script>

<style lang="less" scoped></style>
