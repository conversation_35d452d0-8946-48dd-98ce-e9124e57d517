<template>
	<div class="content">
		<div class="title">
			户型图
			<div style="margin-left: 20px">目前有{{ planCount?.totalBuildings }}栋楼宇的{{ planCount?.totalImages }}张户型图</div>
		</div>
		<div class="container_box">
			<div class="table_main">
				<div class="table_">
					<div class="tag_box">选择资产</div>
					<div class="table_1" v-if="tableDatao.length > 0">
						<el-table border :data="tableDatao" height="80px" style="width: 100%">
							<el-table-column v-for="(column, index) in tableColumns" :key="index" :label="column.label" :prop="column.prop" :width="column.width" />
						</el-table>
					</div>
					<div class="add active" @click="search()" v-else>+ 选择资产</div>
					<div class="clear active" v-if="tableDatao.length > 0" @click="clearDate(1)">× 清空</div>
				</div>
			</div>
			<!-- 图片展示 -->
			<div class="img_box">
				<div class="tag_box">户型图</div>
				<div class="img_list">
					<div class="img" :class="imgIndex === index ? 'imgAct' : ''" v-for="(item, index) in imgList" :key="index" @click="changeImg(item, index)">
						<img :src="`${proxyAddress}${item}`" alt="" />
					</div>
				</div>
				<div class="pic_box" v-if="imgList.length > 0">
					<el-image
						:src="`${proxyAddress}${imgList[imgIndex]}`"
						:initial-index="imgIndex"
						fit="cover"
						:preview-src-list="[`${proxyAddress}${imgList[imgIndex]}`]"
					/>
				</div>
			</div>
		</div>
	</div>

	<!-- 对话框 -->
	<el-dialog v-model="dialogVisible" title="户型图" width="800" :before-close="handleClose">
		<div class="search_box">
			<div class="box_1">
				<div class="label">城市</div>
				<el-cascader
					placeholder="请选择城市"
					v-model="selectedCity"
					:options="$vuexStore.state.cityArray"
					@change="handleChange"
					:props="{ value: 'label' }"
				>
				</el-cascader>
			</div>
			<div class="box_1">
				<div class="label">资产类型</div>
				<el-select v-model="buildValue" placeholder="全部资产类型">
					<el-option v-for="(item, value) in buildingTypes" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">关键词</div>
				<el-input v-model="keyword" placeholder="请输入关键字"></el-input>
			</div>
			<div class="box_2">
				<el-button type="primary" @click="search()">查询</el-button>
			</div>
		</div>

		<!-- 	<el-row :gutter="20">
			<el-col :span="8">
				<el-cascader placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }"> </el-cascader>
			</el-col>
			<el-col :span="8">
				<el-select style="margin-left: 20px; width: 200px" v-model="buildValue" placeholder="全部资产评级">
					<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" />
				</el-select>
			</el-col>
			<el-col :span="6">
				<el-input v-model="essential" placeholder="请输入关键字"></el-input>
			</el-col>
			<el-col :span="2">
				<el-button @click="search">查询</el-button>
			</el-col>
		</el-row> -->
		<el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange" stripe>
			<el-table-column type="selection" width="55" ref="multipleTableRef" />
			<el-table-column v-for="(column, index) in tableColumns" :key="index" :label="column.label" :prop="column.prop" :width="column.width" />
		</el-table>
		<!-- 分页 -->
		<el-pagination @current-change="pageChange" :current-page="currentPage" small background layout="prev, pager, next" class="mt-4" :total="total" />

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogVisible = false"> 取消 </el-button>
				<el-button type="primary" @click="onConfirm()">确认</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import http from '@/utils/http';
// import { pcaTextArr } from 'element-china-area-data';
import { getFloorPlan, getDictList, getFloorPlanCount } from '@/api/syt.js';

import { ElMessage } from 'element-plus';
const province = ref('');
const imgIndex = ref(0);
const city = ref('');
const county = ref('');
const imgList = ref([]);
const imgLists = ref('');
const buildValue = ref('');
const keyword = ref('');
const total = ref(0);
const selectedCity = ref([]);
const tableDatao = ref([]);
const multipleTableRef = ref(null);

const search = async () => {
	const queryParams = {
		city: city.value,
		district: county.value,
		buildingType: buildValue.value,
		keyword: keyword.value,
		currentPage: currentPage.value,
		pageSize: 10,
	};
	// getFloorPlan
	await getFloorPlan(queryParams)
		.then((res) => {
			tableData.value = res.data.rows;
			dialogVisible.value = true;
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const handleChange = (val) => {
	city.value = val[0];
	county.value = val[1];
};
const rateValue = ref('');
const essential = ref('');
const proxyAddress = 'https://static.biaobiaozhun.com/';
// const proxyAddress = 'http://*************:8088/sm/sys/common/static/'
// const proxyAddress = 'http://**************:8086/'
const buildingTypes = ref([]);
const dialogVisible = ref(false);
// const familyList = ref([])
const currentPage = ref(1);
// const total = ref(0)
const tableData = ref([]);
const planCount = ref();
// 选中的值
const selectValue = ref([]);
const idss = ref(''); //id存放
const multipleSelection = ref([]);
const tableColumns = [
	{
		label: '地图名字',
		prop: 'buildingName',
	},
	{
		label: '类型',
		prop: 'buildingType',
		width: '300',
	},
];
//
const pageChange = (val) => {
	currentPage.value = val;
	search();
};
const reset = () => {
	selectedCity.value = [];
	province.value = '';
	city.value = '';
	county.value = '';
	rateValue.value = '';
	keyword.value = '';
	buildValue.value = '';
	currentPage.value = 1;
	search();
};
const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};

// };
// 点击确定
// const imgList = ref([]);
// const imgLists = ref('');
const onConfirm = () => {
	// 检查是否选择了多个项目
	if (multipleSelection.value.length !== 1) {
		ElMessage.warning('只能选择一个噢');
		return;
	}
	imgLists.value = multipleSelection.value[0].floorPlanUrls;
	imgList.value = imgLists.value.split(',');
	// 两个数组 一个数组里面放一个
	let list1 = [];
	list1.push(multipleSelection.value[0]);
	tableDatao.value = list1;
	dialogVisible.value = false;
};
const changeImg = (item, index) => {
	imgIndex.value = index;
};
const clearDate = () => {
	imgLists.value = [];
	imgList.value = [];
	multipleSelection.value = [];
	tableDatao.value = [];
};
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();

onMounted(() => {
	handlePlanCount();
});

function handlePlanCount() {
	getFloorPlanCount().then((res) => {
		if (res.code === 200) {
			planCount.value = res.data;
		}
	});
}
</script>
<style lang="less" scoped>
.content {
	width: 100%;
	height: 100%;
	min-height: 100vh;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;

		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		margin-right: 15px;
		color: #1a1a1a;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;

		.table_main {
			width: 100%;
			height: 162px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.table_ {
				width: 49.5%;
				height: 162px;
				border-radius: 6px;
				background-color: rgba(255, 255, 255, 1);
				position: relative;

				.tag_box {
					width: auto;
					height: 16px;
					position: absolute;
					left: 0;
					top: 20px;
					font-size: 14px;
					font-weight: bold;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&::before {
						content: '';
						width: 4px;
						height: 16px;
						background-color: rgba(24, 104, 241, 1);
						margin-right: 10px;
					}
				}

				.table_1 {
					width: 96%;
					position: absolute;
					bottom: 10px;
					left: 2%;

					&::v-deep .el-table--fit {
						border-radius: 8px;
					}

					&::v-deep .el-table th {
						background-color: rgba(245, 245, 245, 1);
					}
				}

				.add {
					width: 96%;
					height: 90px;
					position: absolute;
					bottom: 10px;
					left: 2%;
					border-radius: 6px;
					border: 1px solid rgba(231, 231, 231, 1);
					display: flex;
					justify-content: center;
					align-items: center;
					color: rgba(3, 93, 255, 1);
					font-size: 14px;
					font-weight: bold;
				}

				.clear {
					width: auto;
					height: 20px;
					position: absolute;
					top: 15px;
					right: 15px;
					font-size: 14px;
					color: rgba(24, 104, 241, 1);
				}
			}
		}

		.img_box {
			width: 100%;
			min-height: 672px;
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			border-radius: 6px;
			padding: 50px 15px;
			box-sizing: border-box;
			position: relative;

			.tag_box {
				width: auto;
				height: 16px;
				position: absolute;
				left: 0;
				top: 20px;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
			}

			.img_list {
				width: 100%;
				height: 64px;

				display: flex;
				justify-content: flex-start;
				align-items: center;

				.img {
					width: 64px;
					height: 64px;
					border: 2px solid rgba(24, 104, 241, 0);
					border-radius: 4px;
					box-sizing: border-box;
					margin: 0 5px;
					opacity: 0.6;
					overflow: hidden;
					cursor: pointer;

					img {
						width: 100%;
						height: 100%;
					}
				}

				.imgAct {
					width: 64px;
					height: 64px;
					border-radius: 4px;
					box-sizing: border-box;
					border: 2px solid rgba(24, 104, 241, 1);
					opacity: 1;
				}
			}
			.pic_box {
				width: 400px;
				height: 400px;
				margin-top: 15px;
				border: 1px solid rgba(231, 231, 231, 1);
				border-radius: 6px;
				padding: 5px;
				box-sizing: border-box;
				.el-image {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
}

.search_box {
	width: 100%;
	height: auto;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;

	.box_1 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		border: 2px solid rgba(231, 231, 231, 1);
		box-sizing: border-box;

		::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
			box-shadow: 0;
		}

		.label {
			width: 50%;
			height: 100%;
			font-size: 14px;
			color: rgba(134, 144, 156, 1);
			background-color: rgba(245, 246, 247, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.box_2 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		box-sizing: border-box;
	}
}

.table_2 {
	width: 100%;
	height: 308px;

	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}

// .body_box {
// 	text-align: center;
// 	width: 100%;
// }

.box {
	width: 48%;
	/* 使每个 box 占据容器宽度的一半，留出一些间隔 */
	margin-bottom: 100px;
}

.getbtn {
	width: 500px;
	height: 40px;
	margin: 30px auto;
}

.el-carousel__item h3 {
	color: #475669;
	opacity: 0.75;
	line-height: 300px;
	margin: 0;
	text-align: center;
}

.el-carousel__item:nth-child(2n) {
	background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
	background-color: #d3dce6;
}
</style>
