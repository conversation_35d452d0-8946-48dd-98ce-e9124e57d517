<template>
	<el-dialog
		v-model="visible"
		width="460px"
		:close-on-click-modal="false"
		:show-close="true"
		align-center
		class="purchase-success-dialog"
		custom-class="purchase-success-dialog"
	>
		<div class="success-content">
			<!-- 标题 -->
			<div class="dialog-header">
				<i class="success-icon" v-if="!successObj?.paymentState">
					<el-icon color="#4CD964" :size="24">
						<CircleCheckFilled />
					</el-icon>
				</i>
				<div v-else><img style="width: 24px; margin-top: 2px" src="../../assets/closeCircleFill.png" alt="" /></div>
				<div class="title">{{ !successObj?.paymentState ? '购买成功' : '继续支付' }}</div>
			</div>
			<div class="details" v-if="successObj?.paymentState">商品已放入待支付订单，您可以前往订单中心完成支付</div>
			<!-- 订单信息 -->
			<div class="order-info">
				<div class="info-item" v-if="successObj?.name">
					<span class="label">购买商品：</span>
					<span class="value">{{ successObj?.name }}</span>
				</div>
				<!-- <div class="info-item">
					<span class="label">总购买时长：</span>
					<span class="value">使用后根据购买时长相应延长各项权益的有效期限</span>
				</div> -->
				<div class="info-item">
					<span class="label"> {{ !successObj?.paymentState ? '实际支付：' : '应付金额：' }} </span>
					<span class="value">¥{{ successObj?.payableAmount }}</span>
				</div>
				<div class="info-item">
					<span class="label">支付方式：</span>
					<span class="value">{{ successObj?.payType === 'ALI_PC' ? '支付宝' : '微信' }}</span>
				</div>
			</div>

			<!-- 操作按钮 -->
			<div
				class="action-buttons"
				:style="{ border: successObj?.couponDetail ? '' : 'none', marginBottom: successObj?.couponDetail ? '24px' : '0px' }"
			>
				<el-button class="outline-btn" @click="goToRightsCenter" v-if="!successObj?.paymentState"> 前往权益中心使用 </el-button>
				<el-button type="primary" class="buy-btn" @click="useNow" v-if="!successObj?.paymentState"> 立即使用 </el-button>
				<el-button type="primary" class="buy-btn" style="background: #1868f1; border: 0px" @click="handleGoClose" v-if="successObj?.paymentState">
					前往订单中心完成支付
				</el-button>
			</div>

			<!-- 优惠券信息 -->
			<div class="bonus-section" v-if="successObj?.couponDetail">
				<div class="bonus-title">
					使用权益卡得优惠好礼
					<div class="bonus-subtitle">优惠券已发放至"个人中心-权益中心-福利卡券"</div>
				</div>

				<!-- 优惠券卡片 -->
				<div class="coupon-card">
					<div class="coupon-left">
						<div class="used-tag" @click="handleGoUse">去使用</div>
					</div>
					<div class="coupon-right">
						<div class="amount">
							<div class="currency">¥</div>
							<div class="number">{{ successObj.couponDetail.name.split('元')[0] }}</div>
							<div class="desc">打车券礼包</div>
							<div class="tagd">
								<div class="tag">{{ successObj.couponDetail.useLimit }}</div>
							</div>
						</div>
						<div class="rules">{{ successObj.couponDetail.desc }}</div>
					</div>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import { CircleCheckFilled } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
const emit = defineEmits(['handleUseNow']);
const router = useRouter();
const visible = ref(false);
const successObj = ref({});

// 父组件传递过来的方法 显示
const show = (params) => {
	console.log(params);
	successObj.value = params;
	visible.value = true;
};

// 父组件传递过来的方法 隐藏
const hide = () => {
	visible.value = false;
};

const goToRightsCenter = () => {
	// 跳转到权益中心
	router.push({
		path: '/profile/browsingHistory',
	});
};

function handleGoClose() {
	hide();
	router.push({
		path: '/profile/orderCentre',
	});
}
// 去使用
function handleGoUse() {
	router.push({
		path: '/profile/browsingHistory',
		query: { type: 'third' },
	});
}

const useNow = () => {
	// 立即使用逻辑
	emit('handleUseNow');
};

// 暴露方法给父组件
defineExpose({ show, hide });
</script>
<style lang="scss">
.purchase-success-dialog {
	border-radius: 16px;

	.el-dialog__header {
		padding: 4px 0;
	}
}
</style>
<style scoped lang="scss">
.purchase-success-dialog {
	:deep(.el-dialog__body) {
		padding: 0;
	}
}

.success-content {
	padding: 0px 8px 8px 8px;

	.dialog-header {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;
		margin-bottom: 8px;
		.success-icon {
			display: flex;
		}
		.title {
			font-size: 20px;
			font-weight: 500;
			line-height: 28px;
			color: #1d2129;
		}
	}
	.details {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		color: #7f7f7f;
		margin-bottom: 24px;
		text-align: center;
	}
	.order-info {
		.info-item {
			display: flex;
			justify-content: space-between;
			font-size: 14px;

			.label {
				color: #1d2129;
				font-size: 14px;
				font-weight: 400;
				line-height: 24px;
				min-width: 84px;
			}

			.value {
				color: #1d2129;
				font-size: 14px;
				font-weight: 400;
				line-height: 24px;
				text-align: right;
			}
		}
	}

	.action-buttons {
		display: flex;
		justify-content: center;
		gap: 6px;
		margin: 24px 0 24px 0;
		border-bottom: 1px dashed #e8e8f1;

		.el-button {
			padding: 0 21px;
			border-radius: 4px;
			height: 32px;
		}

		.outline-btn {
			border-color: #2b5bfe;
			color: #2b5bfe;
		}
		.buy-btn {
			background: #0a42f1;
			border-color: #0a42f1;
		}
	}

	.bonus-section {
		.bonus-title {
			line-height: 19.6px;
			font-size: 14px;
			font-weight: 500;
			margin-bottom: 24px;
			color: #1d2129;
			.bonus-subtitle {
				font-size: 12px;
				font-weight: 400;
				line-height: 16.8px;
				color: #7281ab;
				font-weight: normal;
				margin-top: 4px;
			}
		}

		.coupon-card {
			background-image: url(../../assets/benefitImg.png);
			background-size: 100% 100%;
			border-radius: 8px;
			width: 430px;
			height: 114px;
			display: flex;
			margin: -8px;
			align-items: center;

			.coupon-left {
				width: 104px;
				padding-top: 40px;
				display: flex;
				flex-direction: column;
				align-items: center;
				.used-tag {
					cursor: pointer;
					width: 58px;
					height: 25px;
					border-radius: 40px;
					color: #fff;
					border: 1px solid #ffffff;
					font-size: 12px;
					font-weight: 400;
					line-height: 25px;
					text-align: center;
					margin-left: 19px;
				}
			}

			.coupon-right {
				width: calc(100% - 134px);
				height: 100%;
				padding-left: 30px;
				margin-top: 50px;
				.amount {
					display: flex;
					height: 42px;
					// align-items: baseline;

					.currency {
						font-size: 20px;
						font-weight: 510;
						line-height: 20.87px;
						color: #c59266;
						display: flex;
						align-items: end;
					}

					.number {
						font-size: 40px;
						font-weight: 700;
						line-height: 33.73px;
						color: #c59266;
						margin: 0 5px 0 4px;
						letter-spacing: 0.5px;
						display: flex;
						align-items: end;
					}

					.desc {
						font-size: 14px;
						font-weight: 600;
						line-height: 23.6px;
						letter-spacing: 0.5px;
						margin-right: 8px;
						color: #1d2129;
						display: flex;
						align-items: end;
					}
					.tagd {
						display: flex;
						align-items: end;
						margin-bottom: 4px;
					}
					.tag {
						border-radius: 4px;
						background: #c592660f;
						border: 0.4px solid #c59266;
						font-size: 8px;
						padding: 2px 3.5px;
						color: #c59266;
					}
				}

				.rules {
					font-size: 10px;
					font-weight: 400;
					line-height: 14px;
					color: #c59266;
					letter-spacing: 0.5px;
					margin-top: 4px;
					padding: 0 15px 0 16px;
				}
			}
		}
	}
}
</style>
