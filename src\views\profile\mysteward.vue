<template>
    <div class="steward">
        <div class="steward_item" v-for="item in stewardList" :key="item">
            <div class="steward_pic">
                <el-avatar
                    src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
                />
            </div>
            <div class="steward_text">
                <p>{{ item.body }}</p>
                <p>{{ item.addedtime }}</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import http from '@/utils/http';
const stewardList = ref([]);
const getStewardList = async () => {
    const res = await http.get('/api/api.do?act=listMSG ');
    console.log('管家',res);
    stewardList.value = res.list;
}
// getStewardList();
</script>

<style scoped>
.demo-type {
  display: flex;
}
.demo-type > div {
  flex: 1;
  text-align: center;
}

.demo-type > div:not(:last-child) {
  border-right: 1px solid var(--el-border-color);
}
.steward_item {
    display: flex;
    /* width: 500px; */
    padding: 10px;
    margin: 10px;
    /* background-color: #fff; */
    /* border-radius: 15px; */
    border-bottom: 1px solid #ccc;
}
.steward_pic {
    margin-right: 15px;
}
</style>