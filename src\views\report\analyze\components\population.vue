<template>
	<div class="population">
		<div class="oneTit">三、建筑物人口</div>
		<div class="oneTit">3.1社区人口</div>
		<table class="MsoTableGrid" style="width: 100%" v-for="(item, index) in arr" :key="index" :style="{ marginBottom: index == 2 ? '0px' : '20px' }">
			<tbody>
				<tr>
					<td valign="top" colspan="15" class style="text-align: left">
						<div class="tab-tltle" style="margin-bottom: 5px">{{ item.res.buildingName }}辐射半径人口概况</div>
						<div style="margin-bottom: 5px">{{ item.res.buildingName }} , {{ item.res.city }} , {{ item.res.districts }}</div>
						<div>开车时间: {{ item.res.arrivalTime }} 分钟半径</div>
					</td>
				</tr>
				<tr>
					<td valign="center" rowspan="2" class="tab-tltle">教育</td>
					<td valign="top" colspan="3" class>无高中文凭</td>
					<td valign="top" colspan="4" class>高中毕业</td>
					<td valign="top" colspan="4" class>大专学历</td>
					<td valign="top" colspan="3" class>学士/硕士生/博士生</td>
				</tr>
				<tr>
					<td valign="top" colspan="3" class>{{ item.degree?.educationNoHigh }}</td>
					<td valign="top" colspan="4" class>{{ item.degree?.educationHigh }}</td>
					<td valign="top" colspan="4" class>{{ item.degree?.educationJuniorCollege }}</td>
					<td valign="top" colspan="3" class>{{ item.degree?.educationBachelorMasterDoctor }}</td>
				</tr>
				<tr>
					<td valign="center" rowspan="2" class="tab-tltle">就业</td>
					<td valign="top" colspan="3" class>白领</td>
					<td valign="top" colspan="4" class>蓝领</td>
					<td valign="top" colspan="4" class>公共事业</td>
					<td valign="top" colspan="3" class>失业率</td>
				</tr>
				<tr>
					<td valign="top" colspan="3" class>{{ item.degree?.whiteCollar }}</td>
					<td valign="top" colspan="4" class>{{ item.degree?.blueCollar }}</td>
					<td valign="top" colspan="4" class>{{ item.degree?.publicUtilities }}</td>
					<td valign="top" colspan="3" class>{{ item.unemployedRate }}</td>
				</tr>
				<tr>
					<td valign="center" rowspan="2" class="tab-tltle">关键事实</td>
					<td valign="top" colspan="3" class>流动人口</td>
					<td valign="top" colspan="4" class>年龄中位</td>
					<td valign="top" colspan="4" class>家庭数</td>
					<td valign="top" colspan="3" class>人均可支配收入中位数</td>
				</tr>
				<tr>
					<td valign="top" colspan="3" class>{{ item.degree?.floatingPopulation }}</td>
					<td valign="top" colspan="4" class>{{ item.degree?.ageMid }}</td>
					<td valign="top" colspan="4" class>{{ item.degree?.householdsNum }}</td>
					<td valign="top" colspan="3" class>{{ item.degree?.disposableIncomeMid }}</td>
				</tr>
				<tr>
					<td valign="center" rowspan="2" class="tab-tltle">收入</td>
					<td valign="top" colspan="5" class>平均家庭收入</td>
					<td valign="top" colspan="5" class>人均收入</td>
					<td valign="top" colspan="4" class>人均资产净值中位数</td>
				</tr>
				<tr>
					<td valign="top" colspan="5" class>{{ item.degree?.householdIncomeAvg }}</td>
					<td valign="top" colspan="5" class>{{ item.degree?.perCapitaIncome }}</td>
					<td valign="top" colspan="4" class>{{ item.degree?.perCapitaAssetMid }}</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="center" rowspan="2">家庭收入（￥）</td>
					<td valign="top" class>200000 +</td>
					<td valign="top" class>100000 - 200000</td>
					<td valign="top" colspan="2" class>75000 - 100000</td>
					<td valign="top" colspan="2" class>50000 - 74999</td>
					<td valign="top" colspan="2" class>35000 - 49999</td>
					<td valign="top" class>25000 - 34999</td>
					<td valign="top" colspan="3" class>15000 - 24999</td>
					<td valign="top" class>5000 - 14999</td>
					<td valign="top" class>0 - 4999</td>
				</tr>
				<tr>
					<td valign="top" class>{{ item.res?.householdIncome?.wealthyIncome }}</td>
					<td valign="top" class>{{ item.res?.householdIncome?.affluentIncome }}</td>
					<td valign="top" colspan="2" class>{{ item.res?.householdIncome?.higherIncome }}</td>
					<td valign="top" colspan="2" class>{{ item.res?.householdIncome?.highIncome }}</td>
					<td valign="top" colspan="2" class>{{ item.res?.householdIncome?.moderateIncome }}</td>
					<td valign="top" class>{{ item.res?.householdIncome?.upperMiddleIncome }}</td>
					<td valign="top" colspan="3" class>{{ item.res?.householdIncome?.middleIncome }}</td>
					<td valign="top" class>{{ item.res?.householdIncome?.lowerMiddleIncome }}</td>
					<td valign="top" class>{{ item.res?.householdIncome?.lowIncome }}</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>
<style lang="less" scoped>
.population {
	table {
		border-top: 1px solid #333;
		border-left: 1px solid #333;
		border-spacing: 0;
		background-color: #fff;
		width: 100%;
		td {
			text-align: center;
			border-bottom: 1px solid #333;
			border-right: 1px solid #333;
			font-size: 13px;
			padding: 10px;
		}
		.tab-tltle {
			td {
				font-weight: 700;
				font-size: 15px;
			}
		}
		.tab-tltle {
			font-weight: 700;
			font-size: 15px;
		}
	}
	.oneTit {
		margin-top: 30px;
		font-size: 24px;
		font-weight: 700;
	}
}
</style>
<script setup>
import { getPopulationData } from '@/api/baogao';
import { onMounted, reactive } from 'vue';
const props = defineProps({
	id: {
		type: String,
		default: null,
		required: true,
	},
});
const arr = reactive([
	{
		res: {},
	},
	{
		res: {},
	},
	{
		res: {},
	},
]);
onMounted(() => {
	if (props.id) {
		arr.forEach(async (item, index) => {
			let param = {
				buildingId: props.id,
				cityType: 1,
				radius: index == 0 ? 1000 : index == 1 ? 2500 : 5000,
			};
			let res = await getPopulationData(param);
			let temDegree = {
				educationNoHigh: res.result['educationNoHigh'] + '%', // 未上过高中
				educationHigh: res.result['educationHigh'] + '%', // 高中
				educationJuniorCollege: res.result['educationJuniorCollege'] + '%', // 专科
				educationBachelorMasterDoctor: res.result['educationBachelorMasterDoctor'] + '%', // 本科及以上
				whiteCollar: res.result['whiteCollar'] + '%', // 蓝领
				blueCollar: res.result['blueCollar'] + '%', // 白领
				publicUtilities: res.result['publicUtilities'] + '%', // 公用事业
				householdIncomeAvg: '¥' + res.result['householdIncomeAvg'], // 人均收入
				perCapitaIncome: '¥' + res.result['perCapitaIncome'], // 人均资产
				perCapitaAssetMid: '¥' + res.result['perCapitaAssetMid'], // 人均资产中位数
				disposableIncomeMid: '¥' + res.result['disposableIncomeMid'], // 可支配收入中位数

				floatingPopulation: res.result['floatingPopulation'], // 流动人口
				ageMid: res.result['ageMid'], // 年龄中位数
				householdsNum: res.result['householdsNum'], // 户数
			};
			item.degree = temDegree;
			item.res = res.result;
			item.unemployedRate = res.result.unemployedRate + '%';
			initMap(item, index);
		});
	}
});
</script>
