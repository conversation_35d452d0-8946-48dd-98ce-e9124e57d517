.carduse-container {
  height: 100%;
  background: #FFFFFF;
  border-radius: 6px;
  overflow: hidden;
  .title-box {
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 24px;
    .name {
      font-size: 14px;
      font-weight: 400;
      width: 110px;
    }

    .value {
      font-size: 14px;
      color: #86909C;
    }

    .accountUser{
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 16px;
      font-size: 12px;
      font-weight: 500;
      line-height: 20px;
      color: #1868F1;
      margin-top: -1px;
      div{
        margin-top: 2px;
      }
    }
  }

  .container_top {
    height: 70px;
    display: flex;
    align-items: center;
    padding-left: 16px;
    border-bottom: 1px solid rgba(231, 231, 231, 1);

    .container_top_elion {
      color: rgba(24, 104, 241, 1);
      cursor: pointer;
    }

    .container_top_title {
      margin: 0 12px 0 8px;
      color: rgba(134, 144, 156, 1);
      font-weight: 500;
      font-size: 14px;
      cursor: pointer;
    }

    .container_top_line {
      height: 16px;
      width: 1px;
      margin-top: 2px;
      background: rgba(231, 231, 231, 1);
    }

    .container_top_prompt {
      margin-left: 12px;
      color: #4E5969;
      font-weight: 500;
      font-size: 14px;
    }
  }

  .prompt {
    margin-left: 110px;
    background: #F5F6F7;
    height: 36px;
    line-height: 36px;
    border-radius: 8px;
    margin-top: 10px;
    width: calc(100% - 220px);
    color: #1868F1;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

  }

  .details-box {
    display: flex;
    height: 82px;
    width: 363px;
    margin: 20px 8px 32px 110px !important;
    position: relative;
    &:hover{
        background: rgba(245, 246, 247);
      }
    .operates {
      // background-color: rgba(245, 246, 247, 1) !important;
    }

    .operate {
      width: 251px;
      height: 82px;
      // background-color: rgba(255, 255, 255, 1);
      border-radius: 8px;
      border: 1px solid rgba(201, 205, 212, 1);
      border-right: 0px solid rgba(201, 205, 212, 1);
      display: flex;
      // align-items: center;
      // flex-direction: column;
      justify-content: space-between;
      .type {
        width: 15px;
        height: 82px;
        display: flex;
        padding-left: 4px;
        align-items: center;
        justify-items: center;
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        color: #ffffff;
        // font-family: Alibaba PuHuiTi;
        font-size: 11px;
        font-weight: 1000;
      }

      .unused {
        background-color: rgba(201, 205, 212, 1);

      }
  
      .occupied {
        color: rgba(249, 194, 155, 1);
        background: rgba(29, 33, 41, 1);
      }

      .xse {
        background-color: rgba(24, 104, 241, 1);
      }
      .invalid {
        color: rgba(201, 205, 212, 1);
        background-color: rgba(245, 246, 247, 1);
      }
      .button-data {
        width: 232px;
        height: 74px;
        padding: 4px 16px;
        cursor: pointer;

        .button_top_contents>:nth-child(1) {
          color: rgba(134, 144, 156, 1);
        }

        .invalid_content {
          color: rgba(29, 33, 41, 1);
          font-size: 12px;
          height: 20px;
          line-height: 20px;
        }

        .button_content_twos {
          color: rgba(134, 144, 156, 1) !important;
        }

        .button_top_content {
          margin-top: 4px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: end;
          margin-bottom: 1px;
        }

        .button_top_content>:nth-child(1) {
          font-size: 16px;
          font-weight: 700;
        }

        .button_top_content>:nth-child(2) {
          width: 36px;
          height: 20px;
          border-radius: 10px 2px;
          line-height: 20px;
          font-weight: 400;
          font-size: 12px;
          text-align: center;

        }

        .button_content_two {
          height: 22px;
          font-weight: 700;
          line-height: 22px;
          font-size: 14px;
          color: rgba(78, 89, 105, 1);
          margin-bottom: 8px;
        }

        .button_contents {
          height: 20px;
          font-weight: 400;
          color: rgba(134, 144, 156, 1);
          line-height: 20px;
          font-size: 12px;
        }

        .button_contentts {
          color: rgba(24, 104, 241, 1);
        }

        // div {
        //   background-color: rgba(0, 121, 254, 0.24705882352941178);
        //   margin-bottom: 10px;
        //   color: #0079FE;
        //   width: 100px;
        //   height: 30px;
        //   border-radius: 18px;
        //   font-size: 14px;
        //   text-align: center;
        //   line-height: 30px;
        // }

      }
    }

    .center_line {
      position: absolute;
      top: 9px;
      right: 110px;
      width: 2px;
      height: 64px;
      background: linear-gradient(180deg, rgba(231, 231, 231, 0) 0%, #E7E7E7 50%, rgba(231, 231, 231, 0) 100%);
    }

    .right-content {
      width: 112px;
      height: 66px;
      border-radius: 6px;
      border: 1px solid rgba(201, 205, 212, 1);
      border-left: 0px solid rgba(201, 205, 212, 1);
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      // border-color: rgba(24, 104, 241, 1);

      
      .status_name {
        font-size: 14px;
        font-weight: 700;
      }
    }
  }

  .details-box.status_1 {
    .status {
      background: rgba(24, 104, 241, 1);
      padding: 0 10px;
    }

    .operate {
      border-color: rgba(24, 104, 241, 1);
    }

    .occupied {
      color: #FFFFFF;
      background: rgba(24, 104, 241, 1);
    }

    .right-content {
      border-color: rgba(24, 104, 241, 1);
    }

  }

  .details-box.status_2 {
    .status {
      padding: 0 10px;
      background: rgba(201, 205, 212, 1);
    }

    .operate {
      border-color: rgba(201, 205, 212, 1);
    }

    .occupied {
      color: #FFFFFF;
      background: rgba(24, 104, 241, 1);
    }

    .right-content {
      border-color: rgba(201, 205, 212, 1);
    }

  }

  .details-box.status_3 {

    .operate {
      background-color: #f5f6f7;
      border-color: 1px solid #c9cdd4;
    }

    .occupied {
      color: #FFFFFF;
      background: rgba(24, 104, 241, 1);
    }

    .right-content {
      border-color: #c9cdd4;
      background-color: #f5f6f7;

    }
  }

  .details-box.type_1 {
    .status {
      // padding: 0 10px;
      color: #f9c29b;
      background: #1d2129;
      // overflow: auto;
      border-radius: 6px 0 0 6px;
    }

    .operate {
      border-color: #1D2129;

      .button-data {
        .occupied {
          color: rgba(249, 194, 155, 1);
          background: rgba(29, 33, 41, 1);
        }
      }
    }

    .right-content {
      border-color: #1D2129;
    }

  }

  .details-box.type_2 {
    .status {
      background: rgba(24, 104, 241, 1);
      border-radius: 6px 0 0 6px;
    }

    .operate {
      border-color: rgba(24, 104, 241, 1);
    }

    .button-data {
      .occupied {
        color: #FFFFFF;
        background: rgba(24, 104, 241, 1);
      }
    }

    .right-content {
      border-color: rgba(24, 104, 241, 1);
    }
  }

  .details-box.newUser {
    .status {
      background:linear-gradient(90deg, #FF504C 0%, #FE8042 100%);
      border-radius: 6px 0 0 6px;
    }

    .operate {
      border-color:#FF514C;
    }
    
    .button-data {
      .occupied {
        color: #FFFFFF;
        background:linear-gradient(90deg, #FF504C 0%, #FE8042 100%);
      }
    }

    .right-content {
      color: #FF514C;
      border-color:#FF514C;
    }
  }

  .contain-rights {
    display: flex;
    margin: 20px 0;
    padding: 0 24px;
    .name {
      font-size: 14px;
      font-weight: 400;
      width: 110px;

    }

    .value {
      display: flex;
      flex-wrap: wrap;
      width: calc(100% - 110px);

      div {
        font-size: 14px;
        padding: 0 10px;
        height: 38px;
        border-radius: 8px;
        margin-right: 10px;
        line-height: 38px;
        color: #4E5969;
        margin-bottom: 10px;
        background-color: #F5F8FD;


      }
    }

  }

  .user-selection {
    height: 350px;
    display: flex;
    margin-left: 110px;
    flex-wrap: wrap;
    // height: 400px;

    .user-box {
      width: 260px;
      height: 72px;
      display: flex;
      align-items: center;
      background-color: #F5F6F7;
      border-radius: 6px;
      margin-right: 10px;
      margin-bottom: 10px;


      .t-box {
        height: 46px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .user-name,
        .mobile {
          display: flex;
          font-size: 13px;

          // .value {
          //   margin-left: 5px;
          // }
        }

        .user-name {
          font-size: 16px;
          font-weight: 500;
          color: #1D2129;

        }

        .mobile {
          font-size: 14px;
          font-weight: 400;
          color: #4E5969;


        }
      }

      img {
        width: 36px;
        height: 36px;
        margin: 0 10px;
      }

      .change {
        width: 66px;
        height: 66px;
        background-color: rgb(215, 215, 215);
        text-align: center;
        line-height: 66px;
        border-radius: 0px 10px 10px 0px;
      }
    }

    .add-user {
      width: 260px;
      height: 72px;
      background-color: #F5F6F7;
      text-align: center;
      line-height: 66px;
      color: rgb(0, 121, 254);
      border-radius: 6px;
      display: flex;
      justify-content: flex-start;
      align-items: center;


      img {
        width: 48px;
        height: 48px;
        margin-left: 20px;
      }



    }


  }


 
}


.dialogHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  height: 56px;
  line-height: 56px;
  padding: 0 16px;
  font-size: 16px;
  color: rgba(29, 33, 41, 1);
  border-bottom: 1px solid rgba(231, 231, 231, 1);
}

.dialogHeaders {
  border-bottom: none !important;
  font-weight: 700 !important;

}

.dialogHeaderLeft {
  display: flex;
  align-items: center;
  color: #000000D9;
  font-size: 16px;
  cursor: pointer;
}

.dialogHeaderLeft>:nth-child(1) {
  margin: 0 5px 0 0;
}

.dialogHeaderRight>:nth-child(1) {
  cursor: pointer;
  margin: 19px 0px 0 0;
}

::v-deep .el-dialog {
  padding: 0;

  .el-pagination {
    margin: 16px -8px 0 0 !important;
  }
}

.form_content {
  padding: 0 16px;
}

.dialog_footer {
  height: 76px;
  width: 100%;
  border-top: 1px solid rgba(231, 231, 231, 1);
}

.dialog_footer>:nth-child(1) {
  height: 76px;
  display: flex;
  margin-right: 16px;
  align-items: center;
  justify-content: flex-end;
}


::v-deep .el-pagination .el-select {
  width: 83px;
  /* 设置你想要的宽度 */
}

::v-deep .el-pagination {
  --el-pagination-button-bg-color: #FFFFFF !important;

  .el-pager li {
    border: 1px solid #E7E7E7;
  }

  .is-active {
    background: #fff !important;
    color: #035DFF !important;
    border: 1px solid #035DFF !important;
  }

  // background-color: red !important;
}

::v-deep .el-table {
  .el-checkbox {
    --el-checkbox-checked-bg-color: rgb(24, 104, 241) !important;
  }
}

.indexKeyWord{
  ::v-deep .el-input-group__append{
    padding: 0 10px;
    background: #FFFFFF;
  }
}

.dialogHeader{
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  height: 56px;
  line-height: 56px;
  padding:0 16px;
  font-size: 16px;
  color: rgba(29, 33, 41, 1);
  border-bottom: 1px solid rgba(231, 231, 231, 1);
}
.dialogHeaders{
  border-bottom:none!important;
  font-weight: 700!important;

}
.dialogHeaderLeft{
  display: flex;
  align-items: center;
  color: #000000D9;
  font-size: 16px;
}

.dialogRomove{
  height: 44px;
  padding: 0 24px;
}
.dialogRomove>:nth-child(n){
  height: 22px;
  line-height: 22px;
  color: #4E5969;
  font-size: 14px;
  font-weight: 500;
}

.dialog_footer{
  height: 76px;
  width: 100%;
  border-top:1px solid rgba(231, 231, 231, 1) ;
}

.dialog_footers{
  border-top:none!important
}
.dialog_footer>:nth-child(1){
  height: 76px;
  display: flex;
  margin-right:16px ;
  align-items: center;
  justify-content: flex-end;
}