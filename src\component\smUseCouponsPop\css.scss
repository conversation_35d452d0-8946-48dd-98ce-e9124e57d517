.form_content {
	height: 296px;
	margin: 0 24px;
}
.dialogHeaderRight {
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.dialogHeaderLeft {
		font-size: 16px;
		font-weight: 700;
		line-height: 24px;
		color: #1d2129;
	}
}

.el-icon {
	font-size: 17px;
	cursor: pointer;
}
.content {
	margin: 10px 0;
	font-size: 14px;
	font-weight: 500;
	line-height: 22px;
	text-align: left;
	color: #4e5969;
}
.contentk {
	.form_right {
		height: 36px;
		display: flex;
		align-items: center;
		margin-bottom: 8px;
		background: #f5f6f7;
		.form_right_img {
			width: 24px;
			height: 36px;
			img {
				width: 12px;
				height: 12px;
				margin: 12px 4px 12px 8px;
			}
		}

		.form_right_content {
			font-size: 12px;
			font-weight: 400;
			line-height: 20px;
			text-align: left;
			color: #1868f1;
		}
	}
}

.dialog_footer {
	height: 34px;
	margin: 0 24px 16px 0px;
	width: calc(100% - 24px);
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.el-button {
	width: 108px;
	height: 34px;
	font-size: 20px;
	background: #1868f1;
	font-size: 14px;
	font-weight: 500;
	line-height: 22px;
}

::v-deep .content_banner {
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;
	.el-input {
		width: 275px;
		height: 54px;
		margin: 16px 0px 0 0;
	}
	.el-input,
	.is-disabled {
		.el-input__wrapper {
			background: #fff !important;
			color: #c9cdd4 !important;
		}
	}
	div {
		cursor: pointer;
		line-height: 54px;
		font-size: 14px;
		font-weight: 500;
	}
}

.getCode {
	color: #1868f1;
}
