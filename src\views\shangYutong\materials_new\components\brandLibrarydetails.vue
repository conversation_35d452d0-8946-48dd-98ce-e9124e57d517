<template>
	<div style="margin-right: 5px">
		<div class="brand-detail-container">
			<img src="@/assets/returnBtn.png" class="brand_returnBtn" alt="" @click="handleReturnBtn" />
			<!-- 品牌展示区域 -->
			<div class="detail-layout">
				<!-- 左侧图片展示区 -->
				<div class="image-gallery">
					<!-- 主图区域 -->
					<div class="main-image">
						<arco-image :src="brandObj.basicInfo.brandPics[selectedIndex]" />
					</div>

					<!-- 缩略图区域 -->
					<div class="thumbnails">
						<div class="thumbnails-wrapper">
							<div v-for="(image, index) in brandObj.basicInfo.brandPics" :key="index" class="thumbnail-item" @click="selectImage(index)">
								<img :src="image" :alt="`缩略图 ${index + 1}`" />
								<div class="overlay" v-if="selectedIndex !== index"></div>
							</div>
						</div>
						<div class="thumb-nav-btn prev" @click="scrollThumbnails('prev')"><icon-left /></div>
						<div class="thumb-nav-btn next" @click="scrollThumbnails('next')"><icon-right /></div>
					</div>
				</div>

				<!-- 右侧品牌信息区 -->
				<div class="brand-info">
					<div class="brand-header">
						<div class="brand-logo">
							<img :src="brandObj.basicInfo.brandLogo" alt="Apple Logo" />
						</div>
						<div class="brand-title">
							<h1>
								{{ brandObj?.basicInfo?.brandName }}
								<div class="tag" v-for="(item, index) in brandObj?.basicInfo?.tagName.split(',')" :key="index">{{ item }}</div>
							</h1>
							<p class="company">{{ brandObj?.basicInfo?.companyName }}</p>
						</div>
					</div>

					<!-- 品牌详细信息表格 -->
					<div class="brand-details">
						<div class="info-row">
							<div class="info-label">业态类别</div>
							<div class="info-value">
								{{ brandObj?.basicInfo?.listIndustryType }} | {{ brandObj?.basicInfo?.listIndustryType2 }} |
								{{ brandObj?.basicInfo?.listIndustryType3 }}
							</div>
						</div>
						<div class="info-row">
							<div class="info-label">成立时间</div>
							<div class="info-value">{{ brandObj?.basicInfo?.foundingTime }}</div>
						</div>
						<div class="info-row">
							<div class="info-label">经营模式</div>
							<div class="info-value">{{ brandObj?.basicInfo?.openStoreMethod }}</div>
						</div>
						<div class="info-row">
							<div class="info-label">合作期限</div>
							<div class="info-value">{{ brandObj?.basicInfo?.cooperateTime }}</div>
						</div>
						<div class="info-row">
							<div class="info-label">面积要求</div>
							<div class="info-value">{{ brandObj?.basicInfo?.areaRequirements }}</div>
						</div>
						<div class="info-row">
							<div class="info-label">计划拓展</div>
							<div class="info-value">{{ brandObj?.basicInfo?.planExpand }}</div>
						</div>
						<div class="info-row">
							<div class="info-label">客单价</div>
							<div class="info-value">{{ brandObj?.basicInfo?.customerPrice }}</div>
						</div>
						<div class="info-row">
							<div class="info-label">门店数</div>
							<div class="info-value">{{ brandObj?.basicInfo?.enteredShoppingMallNum }}</div>
						</div>
					</div>

					<!-- 操作按钮区 -->
					<div class="action-buttons">
						<button class="action-btn primary" v-show="!isCollected" @click="handleBrandCollect()">
							<img src="@/assets/interestAdd.png" style="width: 16px; height: 16px; margin-right: 4px" alt="" />关注
						</button>
						<button class="action-btn primarys" v-show="isCollected">已关注</button>
						<button class="action-btn secondary" @click="handlePartners">对接品牌方</button>
					</div>
				</div>
			</div>
		</div>

		<div class="detail_container">
			<arco-tabs v-model="arcoTabActive" @tab-click="handleClickTab">
				<arco-tab-pane key="1">
					<template #title>
						<div class="flex_content">
							<div class="content_notes">
								<img src="@/assets/notesAct.png" v-show="arcoTabActive == '1'" class="notes" alt="" />
								<img v-show="arcoTabActive !== '1'" src="@/assets/notes.png" class="notes" alt="" />
							</div>
							<div>品牌介绍</div>
						</div>
					</template>
					<div class="content_c">
						<div class="content_title">品牌概况</div>
						<div class="content_text">
							{{ brandObj?.introInfo?.brandDesc }}
						</div>
					</div>
					<div class="content_c">
						<div class="content_title">物业要求</div>
						<div class="content_text">
							{{ brandObj?.introInfo?.propertyRequirements }}
						</div>
					</div>
					<div class="content_c">
						<div class="content_title">其他要求</div>
						<div class="content_text">{{ brandObj?.introInfo?.otherRequirements }}</div>
					</div>
				</arco-tab-pane>
				<arco-tab-pane key="2">
					<template #title>
						<div class="flex_content">
							<div class="content_notes">
								<img src="@/assets/splitBranch.png" v-show="arcoTabActive == '1'" class="notes" alt="" />
								<img v-show="arcoTabActive !== '1'" src="@/assets/splitBranchAct.png" class="notes" alt="" />
							</div>
							<div>拓展区域</div>
						</div>
					</template>
					<div class="tabBranchContent">
						<div class="region_Content">
							<div
								class="region_group"
								:class="parseInIndex == index ? 'region_groupActive' : ''"
								v-for="(item, index) in brandObj.areaInfo"
								:key="index"
								@click="handleAreaInfo(index)"
							>
								{{ item.area }}
							</div>
						</div>
						<!-- 区域选择部分 -->
						<div class="region-selector">
							<div class="region-group" v-for="(group, index) in brandObj.areaInfo[parseInIndex].provinceList" :key="index">
								<div class="region-list">
									<a-tag>
										{{ group.province }}
									</a-tag>
									<a-tag v-for="(cityItem, cityIndex) in group.city.split(',')" :key="cityIndex">
										{{ cityItem }}{{ group.city.split(',').length - 1 !== cityIndex ? '、' : '' }}
									</a-tag>
								</div>
							</div>
						</div>
					</div>
				</arco-tab-pane>
			</arco-tabs>
		</div>
	</div>

	<curator v-model="dialogVisible" title="拓展区域负责人" :brandId="brandObj.basicInfo.brandId" @onConfirm="handleBuildConfirm"></curator>
</template>

<script setup>
import curator from '@/component/curator/index.vue';
import { ElMessage } from 'element-plus';
import { getbrandCollect } from '@/api/syt.js';
import { onMounted, ref } from 'vue';
const emit = defineEmits();
const props = defineProps({
	brandObj: {
		type: Object,
		default: {},
	},
});

onMounted(() => {
	isCollected.value = props.brandObj.basicInfo.isCollected;
});

// 选择对比资产
const dialogVisible = ref(false);
const parseInIndex = ref(0);
const isCollected = ref();
const handleBuildConfirm = () => {
	dialogVisible.value = false;
};

// 当前选中的图片索引
const selectedIndex = ref(0);
const arcoTabActive = ref('1');

function handleClickTab(cal) {
	arcoTabActive.value = cal;
}

function handlePartners() {
	dialogVisible.value = true;
}
// 选择图片
const selectImage = (index) => {
	selectedIndex.value = index;
};

// 滚动缩略图
const scrollThumbnails = (direction) => {
	if (direction == 'next' && props.brandObj.basicInfo.brandPics.length - 1 !== selectedIndex.value) {
		selectedIndex.value = selectedIndex.value + 1;
	}
	if (direction == 'prev' && Number(selectedIndex.value) > 0) {
		selectedIndex.value = selectedIndex.value - 1;
	}
	const thumbnailsContainer = document.querySelector('.thumbnails-wrapper');
	if (thumbnailsContainer) {
		const scrollAmount = direction === 'next' ? 128 : -128;
		thumbnailsContainer.scrollBy({ left: scrollAmount, behavior: 'smooth' });
	}
};

function handleReturnBtn() {
	emit('handleReturnBtn');
}

function handleAreaInfo(index) {
	parseInIndex.value = index;
}

function handleBrandCollect() {
	getbrandCollect({
		brandId: props.brandObj.basicInfo.brandId,
		type: 1,
	}).then((res) => {
		// 提示
		if (res.code == 200) {
			emit('handleBrand');
			isCollected.value = true;
			ElMessage({
				message: '已关注',
				type: 'success',
			});
		}
	});
}
</script>

<style scoped lang="scss">
.brand-detail-container {
	position: relative;
	padding: 20px 16px 30px 56px;
	box-sizing: border-box;
	height: 532px;
	background-color: #fff;
	.brand_returnBtn {
		cursor: pointer;
		width: 24px;
		height: 24px;
		position: absolute;
		top: 20px;
		left: 16px;
	}
	.detail-layout {
		display: flex;
		gap: 56px;
		background-color: #fff;
		border-radius: 4px;
		overflow: hidden;
	}

	/* 左侧图片区域样式 */
	.image-gallery {
		width: 712px;
		height: 478px;
		display: flex;
		flex-direction: column;
	}

	.main-image {
		width: 100%;
		height: 394px;
		overflow: hidden;
		border-radius: 4px;
		position: relative;
	}

	.main-image > :nth-child(1) {
		width: 100%;
		height: 100%;
	}

	.main-image > :nth-child(1) > :nth-child(1) {
		width: 100%;
		height: 100%;
		border-radius: 4px;
		object-fit: cover;
	}

	/* 缩略图区域 */
	.thumbnails {
		margin-top: 8px;
		height: 80px;
		position: relative;
		overflow: hidden;
	}

	.thumbnails-wrapper {
		margin: 0 40px;
		display: flex;
		gap: 8px;
		overflow-x: auto;
		scrollbar-width: none; /* Firefox */
		-ms-overflow-style: none; /* IE and Edge */
		scroll-behavior: smooth;
	}

	.thumbnails-wrapper::-webkit-scrollbar {
		display: none; /* Chrome, Safari, Opera */
	}

	.thumbnail-item {
		min-width: 120px;
		width: 120px;
		height: 80px;
		border-radius: 4px;
		overflow: hidden;
		position: relative;
		cursor: pointer;
		flex-shrink: 0;
	}

	.thumbnail-item img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	/* 未选中的图片蒙层 */
	.overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.5);
	}

	/* 缩略图导航按钮 */
	.thumb-nav-btn {
		position: absolute;
		color: #c9cdd4;
		top: 50%;
		transform: translateY(-50%);
		width: 32px;
		height: 80px;
		border-radius: 4px;
		background-color: #f2f3f5;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 18px;
		cursor: pointer;
		z-index: 10;
	}

	.thumb-nav-btn.prev {
		left: 0;
	}

	.thumb-nav-btn.next {
		right: 0;
	}

	/* 右侧信息区域样式 */
	.brand-info {
		flex: 1;
		padding: 24px;
		display: flex;
		flex-direction: column;
	}

	.brand-header {
		display: flex;
		align-items: center;
		margin-bottom: 24px;
	}

	.brand-logo {
		width: 72px;
		height: 72px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 12px;
		border-radius: 4px;
	}

	.brand-logo img {
		width: 72px;
		height: 72px;
		object-fit: contain;
		border-radius: 4px;
	}

	.brand-title h1 {
		font-size: 20px;
		font-weight: 600;
		margin: 0 0 8px 0;
		display: flex;
		align-items: center;
	}

	.tag {
		white-space: nowrap;
		font-weight: 500;
		font-size: 14px;
		color: #1677ff;
		background-color: #e8f3ff;
		padding: 1px 8px;
		border-radius: 2px;
		line-height: 22px;
		margin-left: 12px;
	}

	.company {
		font-size: 14px;
		color: #86909c;
		margin: 0;
	}

	/* 详细信息表格样式 */
	.brand-details {
		margin-bottom: 20px;
	}

	.info-row {
		display: flex;
		line-height: 24px;
		margin-bottom: 12px;
	}

	.info-row:last-child {
		border-bottom: none;
	}

	.info-label {
		width: 80px;
		color: #86909c;
		font-size: 14px;
	}

	.info-value {
		flex: 1;
		color: #1d2129;
		font-size: 14px;
	}

	/* 操作按钮样式 */
	.action-buttons {
		display: flex;
		gap: 8px;
		margin-top: auto;
	}

	.action-btn {
		height: 40px;
		width: 120px;
		border-radius: 4px;
		font-size: 14px;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
	}

	.action-btn.primary {
		background-color: #1868f1;
		color: white;
		border: none;
	}
	.primarys {
		background-color: #bedaff;
		color: #1868f1;
		border: none;
	}

	.action-btn.secondary {
		background-color: #fff;
		color: #1868f1;
		border: 1px solid #1868f1;
	}
}

.detail_container {
	margin-top: 16px;
	padding: 20px 16px;
	background: #fff;
	.content_c {
		margin: 4px 0 40px 0px;
		.content_title {
			width: 100%;
			height: 28px;
			font-weight: 500;
			font-size: 20px;
			line-height: 28px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			color: #1d2129;
			&::before {
				content: '';
				width: 5px;
				height: 14px;
				background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
				border-radius: 10px;
				margin-right: 8px;
			}
		}

		.content_text {
			padding: 12px 0 0 13px;
			font-weight: 400;
			font-size: 14px;
			line-height: 22px;
			color: #4e5969;
		}
	}
	.flex_content {
		display: flex;
	}
	.content_notes {
		display: inline-block;
		margin-top: 3px;
		.notes {
			margin-right: 4px;
			width: 16px;
			height: 16px;
		}
	}
}

.tabBranchContent {
	padding: 0;
	.region_Content {
		display: flex;
		gap: 8px;
		margin-bottom: 14px;
		.region_group {
			line-height: 22px;
			padding: 5px 19px;
			cursor: pointer;
			border-radius: 30px;
			font-weight: 500;
			font-size: 14px;
			line-height: 22px;
			color: #86909c;
			background: #f2f3f5;
		}
		.region_groupActive {
			background: #e8f3ff !important;
			color: #1868f1 !important;
		}
	}
	.region-selector {
		.region-group {
			margin-bottom: 8px;

			.region-label {
				font-size: 14px;
				color: #86909c;
				margin-bottom: 12px;
			}

			.region-list {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;
			}

			.region-list > :nth-child(1) {
				color: #4e5969 !important;
				width: 42px;
				margin-right: 20px !important;
			}

			.region-list > :nth-child(n) {
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: #1d2129;
				margin-right: -6px;
			}
		}
	}
}

:deep(.arco-tabs-tab) {
	padding: 6px 0 !important;
}
</style>
