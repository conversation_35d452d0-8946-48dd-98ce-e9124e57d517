<template>
	<!-- <el-dialog title="新建楼宇" v-model="props.visible" width="800px" @close="resetForm"> -->
	<myModal :width="856" :visible="props.visible" unmount-on-close mask-closable @cancel="resetForm">
		<template #title>
			<div class="modal_title">新建楼宇</div>
		</template>
		<el-form :model="form" label-width="120px" label-position="top" ref="formRef" :rules="rules">
			<div class="container_build">
				<div class="content_1">
					<el-form-item label="建筑名称" prop="buildingName">
						<el-input v-model="form.buildingName" maxlength="20" placeholder="请输入建筑名称"></el-input>
					</el-form-item>

					<el-form-item label="建成年份" prop="buildYear">
						<el-date-picker
							format="YYYY"
							v-model="form.buildYear"
							style="width: 100%"
							type="year"
							value-format="YYYY"
							placeholder="请选择建成年份"
						></el-date-picker>
					</el-form-item>

					<el-form-item label="维护情况" prop="maintenanceStatus">
						<el-select v-model="form.maintenanceStatus" placeholder="请选择维护情况">
							<el-option label="优秀" value="优秀"></el-option>
							<el-option label="良好" value="良好"></el-option>
							<el-option label="一般" value="一般"></el-option>
							<el-option label="无维护" value="无维护"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="租金(元/m²/年)" prop="rentCost">
						<el-input v-model="form.rentCost" @input="handleBuildingInput($event, 'rentCost')" placeholder="请输入租金"></el-input>
					</el-form-item>

					<el-form-item label="物业费用增加" prop="propertyRatio">
						<el-input v-model="form.propertyRatio" @input="handleInput($event, 'propertyRatio')" placeholder="请输入物业费用增加">
							<template #append>%</template></el-input
						>
					</el-form-item>
				</div>
				<div class="content_1">
					<el-form-item label="资产类型" prop="buildingType">
						<el-select v-model="form.buildingType" placeholder="请选择资产类型">
							<el-option v-for="type in assetTypes" :key="type.key" :label="type.name" :value="type.name"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="地址" prop="address">
						<el-cascader
							v-model="form.address"
							style="width: 100%"
							placeholder="请选择城市"
							:options="$vuexStore.state.cityArray"
							@change="handleChange"
							:props="{ value: 'label' }"
						></el-cascader>
					</el-form-item>
					<el-form-item label="完整年度收入总和(元)" prop="totalIncome">
						<el-input v-model="form.totalIncome" @input="handleBuildingInput($event, 'totalIncome')" placeholder="请输入完整年度收入总和"></el-input>
					</el-form-item>
					<el-form-item label="售价(元/m²)" prop="sellingPrice">
						<el-input v-model="form.sellingPrice" @input="handleBuildingInput($event, 'sellingPrice')" placeholder="请输入售价"></el-input>
					</el-form-item>
					<el-form-item label="收入/租金增长率" prop="rentalRate">
						<el-input v-model="form.rentalRate" @input="handleInput($event, 'rentalRate')" placeholder="请输入收入/租金增长率"
							><template #append>%</template></el-input
						>
					</el-form-item>
				</div>
				<div class="content_1">
					<el-form-item label="建筑面积(m²)" prop="buildingArea">
						<el-input v-model="form.buildingArea" @input="handleBuildingInput($event, 'buildingArea')" placeholder="请输入建筑面积"></el-input>
					</el-form-item>
					<el-form-item label="详细地址" prop="street">
						<el-input v-model="form.street" maxlength="50" placeholder="请输入详细地址"></el-input>
					</el-form-item>
					<el-form-item label="完整年度物业费用(元)" prop="propertyCost">
						<el-input
							v-model="form.propertyCost"
							@input="handleBuildingInput($event, 'propertyCost')"
							placeholder="请输入完整年度物业费用"
						></el-input>
					</el-form-item>
					<el-form-item label="毛报酬率" prop="grossRate">
						<el-input v-model="form.grossRate" @input="handleInput($event, 'grossRate')" placeholder="请输入毛报酬率"
							><template #append>%</template></el-input
						>
					</el-form-item>
				</div>
			</div>

			<!-- <el-form-item class="dialog_footerSubmit">
				<el-button @click="resetForm">取消</el-button>
				<el-button type="primary" @click="submitForm">确定</el-button>
			</el-form-item> -->
		</el-form>
		<div class="btn_wrap">
			<el-button @click="resetForm" color="#F2F3F5" style="color: #4e5969">取消</el-button>
			<el-button type="primary" @click="submitForm" color="#1868F1">确定</el-button>
		</div>
		<!-- </el-dialog> -->
	</myModal>
</template>

<script setup>
import myModal from '@/component/arcoComponents/modal/index.vue';
import { defineProps, defineEmits, ref } from 'vue';
const emit = defineEmits(['handleUpdateClose']);

const props = defineProps({
	visible: {
		type: Boolean,
		required: true,
	},
});

const form = ref({
	buildingName: '',
	buildingArea: '',
	address: [],
	city: '',
	district: '',
	maintenanceStatus: '',
	propertyCost: '',
	sellingPrice: '',
	propertyRatio: '',
	buildingType: '',
	buildYear: '',
	street: '',
	totalIncome: '',
	rentCost: '',
	grossRate: '',
	rentalRate: '',
});
const assetTypes = ref([
	{
		name: '写字楼',
		key: '1',
	},
	{
		name: '零售',
		key: '2',
	},
	{
		name: '产业园区',
		key: '3',
	},
	{
		name: '仓储物流',
		key: '4',
	},
	{
		name: '酒店',
		key: '5',
	},
	{
		name: '长租公寓',
		key: '6',
	},
	{
		name: '医疗',
		key: '7',
	},
	{
		name: '综合市场',
		key: '8',
	},
]);
const formRef = ref();
const rules = ref({
	buildingName: [{ required: true, message: '请输入建筑名称', trigger: 'blur' }],
	buildingArea: [{ required: true, message: '请输入建筑面积', trigger: 'blur' }],
	address: [{ required: true, message: '请选择城市', trigger: 'change' }],
	maintenanceStatus: [{ required: true, message: '请选择维护情况', trigger: 'change' }],
	propertyCost: [{ required: true, message: '请输入完整年度物业费用', trigger: 'blur' }],
	sellingPrice: [{ required: true, message: '请输入售价', trigger: 'blur' }],
	propertyRatio: [{ required: true, message: '请输入物业费用增加', trigger: 'blur' }],
	buildingType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
	buildYear: [{ required: true, message: '请选择建成年份', trigger: 'change' }],
	// street: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
	totalIncome: [{ required: true, message: '请输入完整年度收入总和', trigger: 'blur' }],
	rentCost: [{ required: true, message: '请输入租金', trigger: 'blur' }],
	grossRate: [{ required: true, message: '请输入毛报酬率', trigger: 'blur' }],
	rentalRate: [{ required: true, message: '请输入收入/租金增长率', trigger: 'blur' }],
});

const handleChange = (val) => {
	form.value.city = val[0];
	form.value.district = val[1];
};

const handleInput = (val, key) => {
	// 判断小数点后位数超出两位后，只保留两位
	const valArr = val.split('.');
	if (valArr[1]?.length > 2) {
		form.value[key] = valArr[0] + '.' + valArr[1].slice(0, 2);
	} else {
		form.value[key] = val;
	}
};

const handleBuildingInput = (val, key) => {
	// 输入限制大于0的数字，小数点后两位
	const reg = /^[0-9]+(\.[0-9]{1,2})?$/;
	if (!reg.test(val)) {
		form.value[key] = val.replace(/[^\d.]/g, '');
		// 判断小数点后位数超出两位后，只保留两位
		const valArr = val.split('.');
		if (valArr[1]?.length > 2) {
			form.value[key] = valArr[0] + '.' + valArr[1].slice(0, 2);
		}
	} else {
		form.value[key] = val;
	}
};

const submitForm = () => {
	formRef.value.validate((valid) => {
		if (valid) {
			emit('handleUpdateClose', {
				visible: false,
				form: form.value,
			});
		}
	});
};
const resetForm = () => {
	emit('handleUpdateClose', {
		visible: false,
		form: {},
	});
};
</script>

<style scoped>
.dialog_footerSubmit > :nth-child(1) {
	display: flex;
	justify-content: center;
}
/* 添加自定义样式 */
</style>
<style lang="scss" scoped>
::v-deep .el-form-item {
	margin-bottom: 16px;
}
.modal_title {
	text-align: left;
	width: 100%;
	font-size: 20px;
	font-weight: 500;
	color: #1d2129;
}
.container_build {
	display: flex;
	flex-direction: row;
	gap: 40px;
	.content_1 {
		width: 243px;
		height: 374px;
	}
}
.btn_wrap {
	margin-top: 24px;
	display: flex;
	justify-content: flex-end;
}
</style>
