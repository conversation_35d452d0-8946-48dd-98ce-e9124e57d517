<template>
	<transition name="fade-scale">
		<div class="success-toast" v-if="visible">
			<div class="success-icon">
				<svg viewBox="0 0 1024 1024" width="22" height="22">
					<path
						d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
						fill="#52c41a"
					/>
				</svg>
			</div>
			<span class="success-text">{{ props.message }}</span>
		</div>
	</transition>
</template>

<script setup>
import { ref } from 'vue';
const visible = ref(false);
const props = defineProps({
	duration: {
		type: Number,
		default: 2000, // 显示持续时间，单位毫秒
	},
	message: {
		type: String,
		default: '使用成功',
	},
});

const show = () => {
	visible.value = true;
	setTimeout(() => {
		visible.value = false;
	}, props.duration);
};

// 暴露方法给父组件
defineExpose({ show });
</script>

<style scoped lang="scss">
.success-toast {
	position: fixed;
	top: 12%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 142px;
	height: 60px;
	background: #ffffff;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
	border-radius: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 4px;
	z-index: 9999;

	.success-icon {
		margin-top: -2px;
		height: 22px;
	}

	.success-text {
		font-size: 16px;
		font-weight: 400;
		line-height: 22.4px;
		color: #1d2129;
	}
}

// 动画相关样式
.fade-scale-enter-active,
.fade-scale-leave-active {
	transition: all 0.3s ease;
}

.fade-scale-enter-from,
.fade-scale-leave-to {
	opacity: 0;
	transform: translate(-50%, -50%) scale(0.8);
}

// SVG 动画样式
.checkmark {
	width: 24px;
	height: 24px;

	&-circle {
		fill: none;
		stroke: #4cd964;
		stroke-width: 2;
		stroke-dasharray: 70;
		stroke-dashoffset: 70;
		animation: circle 0.6s ease-in-out forwards;
	}

	&-check {
		fill: none;
		stroke: #4cd964;
		stroke-width: 2;
		stroke-dasharray: 20;
		stroke-dashoffset: 20;
		animation: check 0.3s 0.3s ease-in-out forwards;
	}
}

@keyframes circle {
	to {
		stroke-dashoffset: 0;
	}
}

@keyframes check {
	to {
		stroke-dashoffset: 0;
	}
}
</style>
