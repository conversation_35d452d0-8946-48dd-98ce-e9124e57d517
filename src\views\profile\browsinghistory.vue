<template>
	<div class="browsinghistory">
		<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" v-if="!componentNames">
			<el-tab-pane label="我的权益卡卷" name="first">
				<el-tabs type="border-card" class="demo_tabs" v-model="activeName1" @tab-change="handleChange">
					<el-form :model="form" inline="true" label-width="auto" class="demo-form-inline card_search_content">
						<div class="form_left">
							<el-form-item>
								<el-select v-model="form.status" placeholder="全部状态" @change="handleSearch" style="width: 150px">
									<el-option label="全部状态" value="" />
									<el-option label="未使用" value="UNUSED" />
									<el-option label="生效中" value="ACTIVE" />
									<el-option label="已失效" value="EXPIRED" />
								</el-select>
							</el-form-item>
							<el-form-item v-if="activeName1 == 'first'">
								<el-select v-model="form.type" placeholder="全部类型" @change="handleSearch" style="width: 150px">
									<el-option label="全部类型" value="" />
									<el-option label="套餐权益" value="PACKAGE" />
									<el-option label="单组权益" value="SINGLE" />
								</el-select>
							</el-form-item>
							<el-form-item v-if="activeName1 == 'first'">
								<el-select v-model="form.city" placeholder="全部城市" @change="handleSearch" style="width: 150px">
									<el-option v-for="item in data.cityList" :key="item.city" :label="item.cityName" :value="item.city" />
								</el-select>
							</el-form-item>
							<!-- <el-form-item>
								<el-select v-model="form.region" placeholder="全部团队人数" style="width: 150px">
									<el-option label="全部状态" value="0" />
									<el-option label="未使用" value="1" />
									<el-option label="生效中" value="2" />
									<el-option label="已使用" value="3" />
								</el-select>
							</el-form-item> -->
						</div>
						<div class="form_right">
							<div @click="handleIconSort(1)">
								<div :style="{ color: indexIconSort === 1 ? '#1D2129' : '' }">购买时间</div>
								<div class="icon_sortf">
									<img :src="`/src/assets/${indexIconSort === 1 ? 'sortones' : 'sorttwos'}.png`" alt="" />
								</div>
							</div>
							<div @click="handleIconSort(2)">
								<div :style="{ color: indexIconSort === 2 ? '#1D2129' : '' }">使用时间</div>
								<div class="icon_sort">
									<img :src="`/src/assets/${indexIconSort === 2 ? 'sortones' : 'sorttwos'}.png`" alt="" />
								</div>
							</div>
						</div>
					</el-form>
					<el-tab-pane label="商宇通" name="first">
						<div>
							<div class="table-container table_container" @scroll="handleScroll" v-if="tableDataTerm.cardRollList.length > 0">
								<div class="details-box" v-for="(item, index) in tableDataTerm.cardRollList" :key="index">
									<div class="operate" :class="item.cardStatus == '4' ? 'operates' : ''" :style="handleBorder(item)">
										<div class="status">
											<div class="type unused" v-if="item.cardStatus == '1'">生效中</div>
											<div class="type occupied" v-if="item.cardStatus == '2'">未使用</div>
											<div class="type xse" v-if="item.cardStatus == '3'">未使用</div>
											<div class="type newUser" v-if="item.cardStatus == 'newUser'">未使用</div>
											<div class="type invalid" v-if="item.cardStatus == '4'">已失效</div>
										</div>
										<div class="button-data">
											<div class="button_top_content" :class="item.cardStatus == '4' ? 'button_top_contents' : ''">
												<div>{{ item.description }}</div>
												<div :class="item.cardStatus == '2' ? 'occupied' : item.cardStatus == 'newUser' ? 'newUser' : ''">
													{{ item.cityName.replace('市', '') }}
												</div>
											</div>
											<div class="button_content_two" :class="item.cardStatus == '4' ? 'button_content_twos' : ''">{{ item.periodName }}</div>
											<div
												class="button_contents"
												:class="item.cardStatus == '1' ? 'button_contentts' : item.cardStatus == '4' ? 'invalid_content' : ''"
											>
												{{
													item.cardStatus == '1'
														? item.useTime + '起生效'
														: item.cardStatus == '4'
														? item.expirationTime + '失效'
														: item.buyTime + '购买'
												}}
											</div>
										</div>
									</div>
									<div class="center_line"></div>
									<div class="right-content" :style="handleBorder(item, '1')" v-show="item.cardStatus !== '4'">
										<div class="right_top_content" @click="handledetails(item)">查看详情</div>
										<div
											@click="handleManageUsers(item)"
											v-if="item.cardStatus == '1'"
											class="right_bottom_content"
											:class="
												item.cardStatus == '1'
													? ' right_bottom_content_border'
													: item.cardStatus == '2'
													? 'right_bottom_content_noble'
													: 'right_bottom_content_ordinary'
											"
										>
											管理使用人
										</div>
										<div
											v-if="item.cardStatus == '3' || item.cardStatus == '2' || item.cardStatus == 'newUser'"
											class="right_bottom_content"
											:class="
												item.cardStatus == '1'
													? ' right_bottom_content_border'
													: item.cardStatus == '2'
													? 'right_bottom_content_noble'
													: item.cardStatus == 'newUser'
													? 'right_bottom_content_nobleNewUser'
													: 'right_bottom_content_ordinary'
											"
											@click="handleUser(item)"
										>
											立即使用
										</div>
									</div>
									<div class="right_content" @click="handledetails(item)" v-show="item.cardStatus === '4'">查看详情</div>
								</div>
							</div>

							<div class="unavailable table_container" v-else>暂无数据</div>
						</div>
					</el-tab-pane>
					<el-tab-pane label="商估通" name="second">
						<div>
							<div class="table-container table_container" @scroll="handleScroll" v-if="tableDataTerm.cardRollLists.length > 0">
								<div class="details-box" v-for="(item, index) in tableDataTerm.cardRollLists" :key="index">
									<div class="operate" :class="item.cardStatus == '4' ? 'operates' : ''" :style="handleBorder(item)">
										<div class="status">
											<div class="type unused" v-if="item.cardStatus == '1'">生效中</div>
											<div class="type occupied" v-if="item.cardStatus == '2'">未使用</div>
											<div class="type xse" v-if="item.cardStatus == '3'">未使用</div>
											<div class="type newUser" v-if="item.cardStatus == 'newUser'">未使用</div>
											<div class="type invalid" v-if="item.cardStatus == '4'">已失效</div>
										</div>
										<div class="button-data">
											<div class="button_top_content" :class="item.cardStatus == '4' ? 'button_top_contents' : ''">
												<div>{{ item.name }}</div>
												<div v-if="item.cityName" :class="item.cardStatus == '2' ? 'occupied' : item.cardStatus == 'newUser' ? 'newUser' : ''">
													{{ item.cityName?.replace('市', '') }}
												</div>
											</div>
											<div class="button_content_two" :class="item.cardStatus == '4' ? 'button_content_twos' : ''">{{ item.periodName }}</div>
											<div
												class="button_contents"
												:class="item.cardStatus == '1' ? 'button_contentts' : item.cardStatus == '4' ? 'invalid_content' : ''"
											>
												{{
													item.cardStatus == '1'
														? item.useTime + '起生效'
														: item.cardStatus == '4'
														? item.expirationTime + '失效'
														: item.buyTime + '购买'
												}}
											</div>
										</div>
									</div>
									<div class="center_line"></div>
									<div class="right-content" :style="handleBorder(item, '1')" v-show="item.cardStatus !== '4'">
										<div
											class="right_top_content"
											:style="{ height: item.cardStatus == '1' ? '66px' : '28px', lineHeight: item.cardStatus == '1' ? '66px' : '28px' }"
											@click="handledetailes(item)"
										>
											查看详情
										</div>
										<!-- <div
											@click="handleManageUsers(item)"
											v-if="item.cardStatus == '1'"
											class="right_bottom_content"
											:class="
												item.cardStatus == '1'
													? ' right_bottom_content_border'
													: item.cardStatus == '2'
													? 'right_bottom_content_noble'
													: 'right_bottom_content_ordinary'
											"
										>
											管理使用人
										</div> -->
										<div
											v-if="item.cardStatus == '3' || item.cardStatus == '2' || item.cardStatus == 'newUser'"
											class="right_bottom_content"
											:class="
												item.cardStatus == '1'
													? ' right_bottom_content_border'
													: item.cardStatus == '2'
													? 'right_bottom_content_noble'
													: item.cardStatus == 'newUser'
													? 'right_bottom_content_nobleNewUser'
													: 'right_bottom_content_ordinary'
											"
											@click="handleUserGt(item)"
										>
											立即使用
										</div>
									</div>
									<div class="right_content" @click="handledetailes(item)" v-show="item.cardStatus === '4'">查看详情</div>
								</div>
							</div>

							<div class="unavailable table_container" v-else>暂无数据</div>
						</div></el-tab-pane
					>
				</el-tabs>
			</el-tab-pane>
			<el-tab-pane label="权益期限" name="second">
				<div class="tag_box">
					<div class="tag_boxTitle">商宇通</div>
					<el-table
						:data="tableDataTerm.tableData"
						class="custom_table_height"
						:row-style="{ height: '64px', background: 'rgba(245, 246, 247, 1)' }"
						:header-cell-style="{ height: '64px' }"
						style="width: 100%"
						:header-align="'center'"
						:border="false"
					>
						<el-table-column property="specsName" label="权益组" align="center" show-overflow-tooltip />
						<el-table-column property="includeDesc" label="包含权益" align="center" show-overflow-tooltip />
						<el-table-column property="cityName" label="城市" align="center" show-overflow-tooltip />
						<el-table-column property="endTimeStr" label="有效期至" align="center" show-overflow-tooltip />
					</el-table>

					<div class="tag_boxTitle">商估通</div>
					<el-table
						:data="tableDataTerm.tableDatas"
						class="custom_table_height"
						:row-style="{ height: '64px', background: 'rgba(245, 246, 247, 1)' }"
						:header-cell-style="{ height: '64px' }"
						style="width: 100%"
						:header-align="'center'"
						:border="false"
					>
						<el-table-column property="name" label="权益组" align="center" show-overflow-tooltip />
						<el-table-column property="includeDesc" label="包含权益" align="center" show-overflow-tooltip />
						<el-table-column property="endTimeStr" label="有效期至" align="center" show-overflow-tooltip />
					</el-table>
				</div>
			</el-tab-pane>
			<el-tab-pane label="福利卡劵" name="third">
				<div>
					<el-form :model="formWelfareCoupons" inline="true" label-width="auto" class="demo-form-inline card_search_content">
						<div class="form_left">
							<el-form-item>
								<el-select v-model="formWelfareCoupons.status" placeholder="全部状态" @change="handleCouponsSearch" style="width: 150px">
									<el-option label="全部状态" value="" />
									<el-option label="未使用" value="UNUSED" />
									<el-option label="发放中" value="SENDING" />
									<el-option label="已使用" value="USED" />
								</el-select>
							</el-form-item>
							<el-form-item>
								<el-select v-model="formWelfareCoupons.couponType" placeholder="全部类型" @change="handleCouponsSearch" style="width: 150px">
									<el-option label="全部类型" value="" />
									<el-option label="打车劵" value="RIDE" />
								</el-select>
							</el-form-item>
							<el-form-item>
								<el-select v-model="formWelfareCoupons.brand" placeholder="全部城市" @change="handleCouponsSearch" style="width: 150px">
									<el-option label="全部品牌" value="" />
									<el-option label="滴滴" value="DIDI" />
									<el-option label="高德" value="GAODE" />
								</el-select>
							</el-form-item>
						</div>
						<div class="form_rights">
							<!-- <div>
								<div :style="{ color: indexIconSort === 1 ? '#1D2129' : '' }">到期时间</div>
								<div class="icon_sortf">
									<img :src="`/src/assets/${indexIconSort === 1 ? 'sortones' : 'sorttwos'}.png`" alt="" />
								</div>
							</div> -->
							<div @click="handleWelfareIconSort(1)">
								<div :style="{ color: welfareIndexIconSort === 1 ? '#1D2129' : '' }">使用时间</div>
								<div class="icon_sort">
									<img :src="`/src/assets/${welfareIndexIconSort === 1 ? 'sortones' : 'sorttwos'}.png`" alt="" />
								</div>
							</div>
						</div>
					</el-form>
					<div class="table-container table_container welfare" @scroll="handleWelfareScroll" v-if="welfareCoupons.couponsList.length > 0">
						<smCoucher
							:itemCoupons="item"
							v-for="(item, index) in welfareCoupons.couponsList"
							:key="index"
							@handleWelfareDetails="handleWelfareDetails"
							@handleWelfareAddCrad="handleWelfareAddCrad"
						></smCoucher>
					</div>

					<div v-else class="unavailable table_container">暂无数据</div>
				</div>
			</el-tab-pane>
		</el-tabs>

		<!-- 管理使用人 -->
		<el-dialog v-model="dialogVisible" :show-close="false" :before-close="handleClose" style="width: 700px">
			<template #header>
				<div class="dialogHeader">
					<div class="dialogHeaderLeft">
						<div>管理使用人</div>
					</div>

					<div class="dialogHeaderRight">
						<el-icon><CloseBold @click="handleClose" /></el-icon>
					</div>
				</div>
			</template>
			<div class="container_contentDia">
				<div class="contentDia">
					<div class="value">最多可添加{{ tableDataTerm.userCheckObj.team }}名使用人</div>
					<div class="accountUser" @click="handleAddAccount">
						<img src="@/assets/account.png" alt="" />
						<div>添加本账号为使用人</div>
					</div>
				</div>
				<sm-userCard
					ref="smUserCards"
					:team="tableDataTerm.userCheckObj.team"
					@handleAddCrad="handleAddCrad"
					@handleRemove="handleRemove"
				></sm-userCard>
			</div>

			<template #footer>
				<div class="dialog_footer">
					<div>
						<el-button @click="dialogVisible = false">取消</el-button>
						<el-button type="primary" @click="handleDetermine" color="#1868F1"> 确定 </el-button>
					</div>
				</div>
			</template>
		</el-dialog>

		<el-dialog v-model="dialogVisibleRomove" :show-close="false" top="30vh" width="448" :before-close="handleCloseRomove">
			<template #header>
				<div class="dialogHeader dialogHeaders">
					<div class="dialogHeaderLeft">
						<div>提示</div>
					</div>
				</div>
			</template>
			<div class="dialogRomove">
				<div>确认移除该使用人吗？</div>
				<div>移除后您可添加其他使用人或重新添加该使用人</div>
			</div>
			<template #footer>
				<div class="dialog_footer dialog_footers">
					<div>
						<el-button @click="dialogVisibleRomove = false"> 取消 </el-button>
						<el-button type="danger" @click="handleConfirm"> 移除 </el-button>
					</div>
				</div>
			</template>
		</el-dialog>
		<smUseCouponsPop
			:dialogVisible="smUseCouponsPops"
			:welfareCouponsAddCrad="welfareCouponsAddCrad"
			@handleWelfareClose="handleWelfareClose"
		></smUseCouponsPop>
		<dialogAddUser :dialogVisible="dialogVisibles" @handleDialogClose="handleDialogClose"></dialogAddUser>

		<component :is="componentNames" @handleComponentClose="handleComponentClose" :params="isActiveObj" />

		<cardPopUp ref="cardPopUps" @handleConfirm="handleConfirms" />
		<SuccessToast ref="successToast" />
	</div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import cardPopUp from '../../component/cardPopUp/index.vue';
import SuccessToast from '../../component/SuccessToast/index.vue';
import { useRoute, useRouter } from 'vue-router';
import dialogAddUser from './cardUse/dialogAddUser.vue';
import smCoucher from '../../component/smCoucher/index.vue';
import smUseCouponsPop from '../../component/smUseCouponsPop/index.vue';
import { ElMessage } from 'element-plus';
import {
	getSgtEquityTerm,
	getEquityTerm,
	getUserCoupon,
	getCommonCity,
	getUsersCouponPage,
	getSgtCouponPage,
	getSetUser,
	activeCoupon,
	getCommonCouponPage,
	useCommonCoupon,
} from '@/api/equityTerm.js';
import smUserCard from '../../component/sm-userCard/index.vue';
import { vuexStore } from '../../store/index.js';
import rightsDetails from './RightsDetails/index.vue';
import cardUse from './cardUse/index.vue';
import welfareCouponsdex from './welfareCoupons/index.vue';
import passDetails from './passDetails/index.vue';

const componentNames = ref();
const isActiveObj = ref({});
//公用提示弹框
const successToast = ref(null);
//卡卷确定使用弹框
const cardPopUps = ref(null);
// tabs
const activeName = ref('first');
const activeName1 = ref('first');
const router = useRouter();
const route = useRoute();
// 使用人卡片dom
const smUserCards = ref(null);
// 隐藏卡卷
const showStatus = ref(false);
// 使用人弹窗
const dialogVisibles = ref(false);
// 使用人弹窗
const dialogVisible = ref(false);
// 福利卡劵弹窗
const smUseCouponsPops = ref(false);
// 移除弹窗
const dialogVisibleRomove = ref(false);
let cardIndex = ref(null);
// 购买时间
let indexIconSort = ref(1);

// 福利卡劵使用时间
let welfareIndexIconSort = ref(0);
// 当前使用福利卡劵
const welfareCouponsAddCrad = ref({});
// 搜索条件
const data = reactive({
	cityList: [],
	statusList: [],
	typeList: [],
});
// 权益期限 权益卡劵
let tableDataTerm = reactive({
	tableDatas: [], // 商估通权益期限
	tableData: [], // 商宇通权益期限
	cardRollList: [], // 商宇通权益卡劵
	cardRollLists: [], // 商估通权益卡劵
	userCheckObj: {},
	page: {
		total: 0,
		currentPage: 1,
		pageSize: 18,
	},
});

// 福利卡劵
let welfareCoupons = reactive({
	couponsList: [],
	total: 0,
	page: {
		currentPage: 1,
		pageSize: 18,
	},
});

// 获取数据
const form = reactive({
	status: '',
	type: '',
	city: '',
	orderByColumn: 'buyTime',
});

// 福利卡劵搜索条件参数
const formWelfareCoupons = reactive({
	status: '',
	couponType: '',
	brand: '',
	orderByColumn: '',
});

onMounted(() => {
	if (route.query.type) {
		router.replace({ path: '/profile/browsingHistory', query: {} });
		activeName.value = route.query.type;
		handleCommonCouponPage(); // 获取福利卡劵
	}
	// 获取权益卡卷
	handleUsersCouponPage();
	// 权益期限
	handleEquityTerm();
	// 获取城市
	handleCity();
});

function handleScroll(event) {
	const { scrollTop, clientHeight, scrollHeight } = event.target;
	if (scrollTop + clientHeight >= scrollHeight) {
		if (tableDataTerm.page.currentPage * tableDataTerm.page.pageSize >= tableDataTerm.page.total) {
			// debugger;
			return;
		}
		tableDataTerm.page.currentPage++;
		handleUsersCouponPage('1');
		// 滚动条到达底部，触发懒加载
	}
}
// 福利卡劵搜索
function handleCouponsSearch() {
	welfareCoupons.page.currentPage = 1;
	handleCommonCouponPage(); // 获取福利卡劵
}

// 搜索
function handleSearch() {
	tableDataTerm.page.currentPage = 1;
	handleUsersCouponPage();
}
// 福利卡劵滚动加载
function handleWelfareScroll(event) {
	const { scrollTop, clientHeight, scrollHeight } = event.target;
	if (scrollTop + clientHeight >= scrollHeight) {
		if (welfareCoupons.page.currentPage * welfareCoupons.page.pageSize >= welfareCoupons.total) {
			// debugger;
			return;
		}
		welfareCoupons.page.currentPage++;
		handleCommonCouponPage('1');
		// 滚动条到达底部，触发懒加载
	}
}

/**
 * @function handleCommonCouponPage 分页获取福利卡劵
 */
function handleCommonCouponPage(type) {
	getCommonCouponPage({ ...formWelfareCoupons, current: welfareCoupons.page.currentPage, size: welfareCoupons.page.pageSize }).then((res) => {
		if (res.code === 200) {
			let arr = [];
			res.data.rows.forEach((item) => {
				// 未使用
				if (item.status === 'UNUSED') {
					item['couponsType'] = '2';
				}

				// 生效中
				if (item.status === 'SENDING') {
					item['couponsType'] = '6';
				}

				// 已使用
				if (item.status === 'USED') {
					item['couponsType'] = '3';
				}
				arr.push(item);
			});
			if (type) {
				welfareCoupons.couponsList = [...welfareCoupons.couponsList, ...arr];
			} else {
				welfareCoupons.couponsList = arr;
				console.log(welfareCoupons.couponsList, 'welfareCoupons.couponsList');
			}

			welfareCoupons.total = res.data.total;
		}
	});
}

/**
 * @function handleUsersCouponPage 获取权益卡卷
 */
function handleUsersCouponPage(type) {
	if (activeName1.value == 'first') {
		getUsersCouponPage({ ...form, current: tableDataTerm.page.currentPage, size: tableDataTerm.page.pageSize }).then((res) => {
			if (res.code === 200) {
				handleCouponPagety(type, res);
			}
		});
	} else {
		getSgtCouponPage({ ...form, current: tableDataTerm.page.currentPage, size: tableDataTerm.page.pageSize }).then((res) => {
			if (res.code === 200) {
				handleCouponPagety(type, res, 'sgt');
			}
		});
	}
}
// 商宇通权益卡劵
function handleCouponPagety(type, res, isSgt) {
	let arr = [];
	res.data.rows.forEach((item) => {
		// 未使用
		if (item.status === 'UNUSED') {
			if (item.premium) {
				// 臻享版
				item['cardStatus'] = '2';
			} else if (item.newUser) {
				// 新用户专享版
				item['cardStatus'] = 'newUser';
			} else {
				// 普通版
				item['cardStatus'] = '3';
			}
		}
		// 已失效
		if (item.status === 'EXPIRED') {
			item['cardStatus'] = '4';
		}
		// 生效中
		if (item.status === 'ACTIVE') {
			item['cardStatus'] = '1';
		}
		arr.push(item);
	});
	if (isSgt) {
		if (type) {
			tableDataTerm.cardRollLists = [...tableDataTerm.cardRollLists, ...arr];
		} else {
			tableDataTerm.cardRollLists = arr;
		}
	} else {
		if (type) {
			tableDataTerm.cardRollList = [...tableDataTerm.cardRollList, ...arr];
		} else {
			tableDataTerm.cardRollList = arr;
		}
	}

	tableDataTerm.page.total = res.data.total;
}

// 去重
function handleUnique(arr) {
	const res = new Map();
	return arr.filter((arr) => !res.has(arr.phone) && res.set(arr.phone, 1));
}

/**
 * @function handleDialogClose 关闭添加使用人弹窗
 * @param list 选中的使用人数组
 */
function handleDialogClose(list) {
	if (list && list.value) {
		let arr = [];
		arr = [...smUserCards.value.list.dataListCard, ...list.value];
		let arr1 = handleUnique(arr);
		smUserCards.value.handleListCard(arr1);
	}

	dialogVisibles.value = false;
}

// 添加本账号
function handleAddAccount() {
	handleDialogClose({ value: [vuexStore.state.userInfo] });
}

/**
 * @function handleCity 获取城市
 */
function handleCity() {
	getCommonCity().then((res) => {
		if (res.code === 200) {
			data.cityList = [
				{
					city: '',
					cityName: '全部城市',
				},
				...res.data,
			];
		}
	});
}
// 切换tabs
const handleClick = (tab, event) => {
	if (tab.props.label === '权益期限') {
		// 权益期限
		handleEquityTerm();
		handlegetSgtEquityTerm();
	} else if (tab.props.label === '福利卡劵') {
		handleCommonCouponPage();
	}
};
// 切换
function handleChange() {
	// 清空搜索条件
	form.status = '';
	form.type = '';
	form.city = '';
	indexIconSort.value = 1;
	tableDataTerm.page.currentPage = 1;
	handleUsersCouponPage();
}
/**
 * @function handleRemove 移除使用人
 * @param index 移除的索引
 */
function handleRemove(index) {
	cardIndex.value = index;
	dialogVisibleRomove.value = true;
}
// 排序
function handleIconSort(index) {
	indexIconSort.value = index;
	tableDataTerm.page.currentPage = 1;
	if (index === 1) {
		form.orderByColumn = 'buyTime';
	}
	if (index === 2) {
		form.orderByColumn = 'useTime';
	}
	handleUsersCouponPage(); // 获取数据
}
// 福利卡卷使用时间搜索
function handleWelfareIconSort(index) {
	welfareIndexIconSort.value = index;
	welfareCoupons.page.currentPage = 1;
	formWelfareCoupons.orderByColumn = 'useTime';
	handleCommonCouponPage();
}

function handleBorder(item, type) {
	if (item.cardStatus === '1') {
		if (type) {
			return 'border:1px solid #C9CDD4;border-left: 0pxsolid #c9cdd4;';
		}
		return 'border:1px solid #C9CDD4;border-right: 0px solid #c9cdd4;';
	}
	if (item.cardStatus === '2') {
		if (type) {
			return 'border:1px solid #1D2129;border-left: 0px solid #1D2129;';
		}
		return 'border: 1px solid #1D2129;border-right: 0px solid #1D2129;';
	}
	if (item.cardStatus === '3') {
		if (type) {
			return 'border:1px solid #1868F1;border-left: 0px solid #1868F1;';
		}
		return 'border: 1px solid #1868F1;border-right: 0px solid #1868F1;';
	}

	if (item.cardStatus === 'newUser') {
		if (type) {
			return 'border:1px solid #FF514C;border-left: 0px solid #FF514C;';
		}
		return 'border: 1px solid #FF514C;border-right: 0px solid #FF514C;';
	}
}
// 添加使用人
function handleAddCrad() {
	dialogVisibles.value = true;
}

/**
 * @function handleShow 展示已失效的卡卷
 */
function handleShow() {
	showStatus.value = !showStatus.value;
}

// 关闭提示
function handleCloseRomove() {
	dialogVisibleRomove.value = false;
}
// 确定移除
function handleConfirm() {
	smUserCards.value.handleSureRemoves(cardIndex.value);
	dialogVisibleRomove.value = false;
}
// 确定
function handleConfirms() {
	// 处理确认逻辑
	cardPopUps.value.hide(); //确认使用弹窗
	handleActiveCoupon();
}

//激活
function handleActiveCoupon() {
	activeCoupon({ id: isActiveObj.value.id }).then((res) => {
		if (res.code == 200) {
			successToast.value.show(); //成功提示
			// 获取权益卡卷
			handleUsersCouponPage();
		}
	});
}

// 关闭对话框
function handleClose() {
	dialogVisible.value = false;
}

/**
 * @function handleDetermine 管理使用人
 */
function handleDetermine() {
	// 判断是否选择使用人未选择则进行提示
	if (smUserCards?.value?.list?.dataListCard?.length === 0) {
		ElMessage({
			message: '请至少保留一名使用人',
			type: 'warning',
		});
		return;
	}
	handleSetUser();
	dialogVisible.value = false;
}

/**
 * @function handleSetUser 添加使用人
 */
function handleSetUser() {
	let arr = [];
	smUserCards.value.list.dataListCard.forEach((item) => {
		arr.push(item.id);
	});
	let param = {
		userIds: arr,
		userCouponId: tableDataTerm.userCheckObj.id,
		ignoreWarn: false,
	};
	getSetUser(param).then((res) => {
		if (res.code === 200) {
			smUserCards.value.handleListCard(res.data.rows);
		}
	});
}
/**
 * @function handleManageUsers 跳转管理使用人
 */

function handleManageUsers(item) {
	tableDataTerm.userCheckObj = item;
	handleUserPage();
	dialogVisible.value = true;
}
/**
 * @function handleEquityTerm 权限期限
 */
function handleEquityTerm() {
	getEquityTerm().then((res) => {
		if (res.code === 200) {
			tableDataTerm.tableData = res.data;
		}
	});
}
// 商估通期限
function handlegetSgtEquityTerm() {
	getSgtEquityTerm().then((res) => {
		if (res.code === 200) {
			tableDataTerm.tableDatas = res.data;
		}
	});
}

/**
 * @function handleUserPage 管理使用人
 */
function handleUserPage() {
	getUserCoupon({ id: tableDataTerm.userCheckObj.id }).then((res) => {
		if (res.code === 200) {
			smUserCards.value.handleListCard(res.data);
		}
	});
}

// 福利卡劵详情
const handleWelfareDetails = (item) => {
	isActiveObj.value = item;
	componentNames.value = welfareCouponsdex;
};
// 福利卡劵使用
function handleWelfareAddCrad(item) {
	welfareCouponsAddCrad.value = item; //即将要使用的福利卡劵
	smUseCouponsPops.value = true;
}

// 关闭福利卡劵使用
function handleWelfareClose(param) {
	if (param?.type === 1) {
		// 条件符合则为确定
		// 使用卡卷
		handleuseCommonCoupon(param);
	} else {
		smUseCouponsPops.value = false;
	}
}

// 使用福利卡劵
function handleuseCommonCoupon(param) {
	let params = {
		couponUserId: welfareCouponsAddCrad.value.id,
		...param.informationObj,
	};
	useCommonCoupon(params).then((res) => {
		if (res.code === 200) {
			// 使用成功提示
			ElMessage({
				message: '使用成功',
				type: 'success',
			});
			smUseCouponsPops.value = false;
			handleCommonCouponPage();
		}
	});
}
/**
 * @function handledetails 跳转卡卷详情商估通
 */
function handledetailes(item) {
	isActiveObj.value = item;
	componentNames.value = passDetails;
}
/**
 * @function handledetails 跳转卡卷详情商宇通
 */
function handledetails(item) {
	isActiveObj.value = item;
	componentNames.value = rightsDetails;
}

function handleComponentClose(param) {
	if (param === 1) {
		handleUsersCouponPage();
	}
	componentNames.value = null;
}
// 商估通立即使用
const handleUserGt = (item) => {
	isActiveObj.value = item;
	cardPopUps.value.show();
};

// 跳转立即使用页面
const handleUser = (item) => {
	isActiveObj.value = item;
	componentNames.value = cardUse;
};
</script>

<style lang="less" scoped>
@import url('./browsinghistory.less');
@import url('./cardUse/style.less');
</style>
