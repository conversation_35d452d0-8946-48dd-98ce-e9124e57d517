<template>
	<div class="rights-details-container">
		<div class="container_top">
			<el-icon @click="goBack" class="container_top_elion"><ArrowLeftBold /></el-icon>
			<div @click="goBack" class="container_top_title">权益卡券详情</div>
			<div class="container_top_line"></div>
		</div>
		<div style="height: calc(100% - 96px)">
			<div class="contaner_content_">
				<div
					class="details-box"
					:class="`${
						data.objCrad.cardStatus === '1' || data.objCrad.cardStatus === '3'
							? 'type_2'
							: data.objCrad.cardStatus == '2'
							? 'type_1'
							: data.objCrad.cardStatus == 'newUser'
							? 'newUser'
							: 'status_3'
					}`"
				>
					<div class="operate">
						<div class="status"></div>
						<div class="button-data">
							<div class="button_top_content">
								<div>{{ data.objCrad.description }}</div>
								<div class="occupied">{{ data.objCrad.cityName?.replace('市', '') }}</div>
							</div>
							<div class="button_content_two">{{ data.objCrad.periodName }}</div>
						</div>
					</div>
					<div class="center_line"></div>
					<div class="right-content">
						<div class="status_name">{{ data.objCrad.statusName }}</div>
					</div>
				</div>
			</div>

			<div class="t-box">
				<div
					class="Rights-box"
					v-for="(item, index) in RightsData"
					:style="{ display: data.objCrad.type === 'SINGLE' && index === 2 ? 'none' : '' }"
					:key="index"
				>
					<div class="name">{{ item.name }}</div>
					<div class="value">{{ item.value }}</div>
				</div>
			</div>
			<div class="contain-rights">
				<div class="name">包含权益</div>
				<div class="value">
					<div v-for="(item, index) in data.objCrad.includeDescArray" :key="index">{{ item }}</div>
				</div>
			</div>
			<div class="title-box">
				<div class="name">使用人记录</div>
				<div class="value">按添加顺序由近至远展示</div>
			</div>
			<div>
				<el-table
					:data="data.tableData"
					style="width: calc(100% - 132px); margin-left: 110px; margin-right: 24px; border-radius: 6px; border: 1px solid rgba(231, 231, 231, 1)"
					:height="'225px'"
					header-row-class-name="tableHeader"
					:header-cell-style="{ background: 'rgba(245, 245, 245, 1)', borderLeft: '1px solid #E7E7E7', color: '#1D2129' }"
					:stripe="false"
					:lazy="true"
					size="small"
					empty-text="暂无数据"
				>
					<el-table-column label="序号" width="100">
						<template #default="scope">{{ scope.$index + 1 }}</template>
					</el-table-column>
					<el-table-column v-for="item in tableColumns" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.width"
						><template v-if="item.prop == 'inUse'" #default="scope"
							><div v-if="item.prop == 'inUse'" :style="`color: ${scope.row.inUse ? ' #1868F1' : '#4E5969'}`">
								{{ scope.row.inUse ? '使用中' : '已移除' }}
							</div>
						</template></el-table-column
					>
				</el-table>
				<!-- <el-pagination
				layout="prev, pager, next"
				v-model:page-size="size"
				:total="total"
				:current-page="currentPage"
				@current-change="pageChange()"
				style="justify-content: right"
			/> -->
				<el-config-provider :locale="customPagination">
					<el-pagination
						layout="total, prev, pager, next,jumper"
						:pager-count="3"
						background
						v-model:current-page="data.page.currentPage"
						v-model:page-size="data.page.pageSize"
						:total="data.page.total"
						@change="handlepageChange"
						style="justify-content: right"
					>
					</el-pagination>
				</el-config-provider>
			</div>
		</div>
	</div>
</template>
<script setup>
import { reactive, onMounted } from 'vue';
import { getUserCouponCctive } from '@/api/equityTerm.js';
import { useRouter } from 'vue-router';
const emit = defineEmits(['handleComponentClose']);
const router = useRouter();
const props = defineProps({
	// 当前卡卷对象
	params: {
		type: Object,
		default: {},
	},
});

let data = reactive({
	objCrad: {},
	tableData: [],
	page: {
		total: 0,
		currentPage: 1,
		pageSize: 10,
	},
});

let customPagination = reactive({
	el: {
		pagination: {
			pagesize: '页',
			total: `共 ${data.page.total} 条`,
			goto: '跳至',
			pageClassifier: '',
		},
	},
});

const RightsData = reactive([
	{
		name: '卡券类型：',
		typeName: 'description',
		value: '1',
		id: 1,
	},
	{
		name: '适用城市：',
		typeName: 'cityName',
		value: '1',
		id: 1,
	},
	{
		name: '团队人数：',
		typeName: 'team',
		value: '1',
		id: 1,
	},
	{
		name: '购买时间：',
		typeName: 'buyTime',
		value: '1',
		id: 1,
	},
	{
		name: '购买时长：',
		typeName: 'periodName',
		value: '1',
		id: 1,
	},
	{
		name: '卡券状态：',
		typeName: 'statusName',
		value: '1',
		id: 1,
	},
	{
		name: '使用时间：',
		typeName: 'useTime',
		value: '1',
		id: 1,
	},
	{
		name: '剩余时长：',
		typeName: 'expirationTime',
		value: '1',
		id: 1,
	},
]);

const tableColumns = [
	{
		prop: 'userName',
		label: '用户名',
		// width: '120',
	},
	{
		prop: 'inUse',
		label: '状态',
		// width: '120',
	},
	{
		prop: 'phone',
		label: '手机号',
		// width: '180',
	},
	{
		prop: 'startTime',
		label: '添加时间',
		// width: '180',
	},
	{
		prop: 'endTime',
		label: '结束时间',
		// width: '180',
	},
];

onMounted(() => {
	if (!props.params) return;
	// 处理数据
	handleProcessing();
	// 使用人记录
	handleCouponCctive();
});

// current-page 或 page-size 更改时触发
function handlepageChange(currentPage, pageSize) {
	data.page.currentPage = currentPage;
	handleCouponCctive();
}

// 处理数据
function handleProcessing() {
	data.objCrad = props.params;
	data.objCrad['includeDescArray'] = data.objCrad.includeDesc.split('；');
	RightsData.forEach((item) => {
		item.value = data.objCrad[item.typeName];
	});
}

/**
 * @function handleCouponCctive 使用人记录
 */
function handleCouponCctive() {
	getUserCouponCctive({ id: data.objCrad.id, current: data.page.currentPage, size: data.page.pageSize }).then((res) => {
		if (res.code === 200) {
			data.tableData = res.data.rows;
			data.page.total = res.data.total;
			customPagination.el.pagination.total = `共 ${res.data.total} 条`;
		}
	});
}

const goBack = () => {
	emit('handleComponentClose');
};
</script>

<style lang="less" scoped>
@import url('./style.less');
</style>
