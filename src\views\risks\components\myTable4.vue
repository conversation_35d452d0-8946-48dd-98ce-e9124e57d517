<template>
	<div style="display: flex; justify-content: center">
		<arco-table :pagination="false" :bordered="{ cell: true }" :data="tableData" style="width: 100%" :class="classname">
			<template #columns>
				<arco-table-column title="一、期初募集支出" align="center">
					<arco-table-column
						resizable
						v-for="column in tableColumns"
						:key="column.id"
						:data-index="column.prop"
						:title="column.label"
						:width="column.width"
					>
						<template #cell="scope">
							<div v-if="column.prop == 'assumptionStr' && scope.record.title === '股东贷款利率上限'">
								<el-tooltip effect="dark" :content="handleContent(scope.record.title)" placement="left">
									<arco-input-number
										class="proportionColumn"
										hide-button
										size="small"
										v-model="scope.record.assumptionStr"
										@change="assumption(scope.record, $event)"
									>
										<template #suffix><div>%</div> </template>
									</arco-input-number>
								</el-tooltip>
							</div>
							<div v-else-if="column.prop == 'calc' && scope.record.title === '股东贷款'">
								<el-tooltip effect="dark" :content="handleContent(scope.record.title)" placement="left">
									<arco-input-number
										class="proportionColumn"
										hide-button
										size="small"
										v-model="scope.record.calc"
										@change="assumption(scope.record, $event)"
									>
									</arco-input-number>
								</el-tooltip>
							</div>
							<div v-else>
								{{
									column.label === '计算（元）'
										? $formattedMoney($utils.handleNumber(scope.record[column.prop]))
										: column.label === '假设（%）'
										? $formattedMoney($utils.handleNumber(scope.record[column.prop] * 100))
										: scope.record[column.prop]
								}}
							</div>
						</template>
					</arco-table-column>
				</arco-table-column>
			</template>
		</arco-table>
	</div>
</template>
<script setup>
import { defineProps, onMounted, ref } from 'vue';
const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
	classname: {
		type: String,
		default: [],
	},
});
const tableData = ref([]);
const emit = defineEmits(['search']);
// 基金募集金额
const fundRaising = ref();

const list = ref([
	{ name: '股东贷款', key: 'loan', value: '' },
	{ name: '股东贷款利率上限', key: 'loanRate', value: '' },
]);

onMounted(() => {
	tableData.value = [];
	handleCombined();
});

function handleContent(params) {
	let name = '';
	if (params == '股东贷款') {
		name = '0<x≤基金募集金额';
	}

	if (params == '股东贷款利率上限') {
		name = '0<x≤100';
	}

	let str = `输入${name}的数值输入后点击左上角“保存并计算”重新计算数据`;
	return str;
}

const assumption = (val, event) => {
	list.value.forEach((item, index) => {
		if (item.name === val.title) {
			if (val.title === '股东贷款利率上限') {
				item.value = val.assumptionStr;
			} else {
				item.value = val.calc;
			}
			console.log(list.value, 'list.value');
			console.log(list.value[index], props.classname, 'list.value');
			emit('search', list.value[index], props.classname);
		}
	});
};

// 组合数据
function handleCombined() {
	props.data.forEach((element, index) => {
		element.assumptionStr = Number(element.assumptionStr);
		if (element.title === '基金募集金额') {
			fundRaising.value = element.calc;
		}
		element.key = index + 1;
		if (index == 0) {
			tableData.value.push({
				...element,
				children: [],
			});
		}
		if (index == 5) {
			tableData.value.push({
				...element,
				children: [],
			});
		}
		if (index > 0 && index < 5) {
			tableData.value[0]['children'].push(element);
		}
		if (index > 5 && index < 9) {
			tableData.value[1]['children'].push(element);
		}
	});
}

const tableColumns = [
	{ prop: 'title', label: '科目', width: 255, id: 1 },
	{ prop: 'calc', label: '计算（元）', width: 255, id: 2 },
	{ prop: 'assumptionStr', label: '假设（%）', id: 3 },
];
</script>
<style scoped lang="less">
::v-deep .arco-table-tr .arco-table-cell {
	padding: 8.5px 11px;
}

::v-deep tbody .arco-table-tr > :nth-child(1) .arco-table-cell > :nth-child(1) {
	padding-left: 0px !important;
}

::v-deep tbody .arco-table-tr > :nth-child(1) .arco-table-cell-inline-icon {
	padding: unset !important;
	margin-left: 0px !important;
	margin-right: 8px !important;
	.arco-table-expand-btn {
		background: #fff !important;
		border: 1.33px solid #4e5969 !important;
	}
}

::v-deep tbody .arco-table-tr > :nth-child(1) .arco-table-cell > :nth-child(1) {
	padding: 0px !important;
	margin-left: 21px;
}
</style>
