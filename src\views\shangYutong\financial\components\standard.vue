<template>
	<div>
		<div class="data_box">
			<div class="data_item">
				<div class="standcontent">
					<div class="standcontentTop">
						<div class="standcontentTopText">
							{{ formattedMoney(finDataList.shanghaiCompositeIndex, 2) }}
						</div>
						<div class="standcontentTopImg">
							<img v-if="finDataList.shanghaiCompositeIndexTrend > 0" src="@/assets/arrowtopimg1.png" alt="" />
							<img v-else src="@/assets/arrowtopimg.png" alt="" />
						</div>
						<div class="standTextdetailsItem" :style="finDataList.shanghaiCompositeIndexTrend > 0 ? 'color: #F53F3F' : 'color: #00B89B'">
							{{
								finDataList.shanghaiCompositeIndexTrend > 0
									? '+' + formattedMoney(finDataList.shanghaiCompositeIndexTrendNum)
									: formattedMoney(finDataList.shanghaiCompositeIndexTrendNum)
							}}
							{{
								finDataList.shanghaiCompositeIndexTrend > 0
									? '+' + formattedMoney(finDataList.shanghaiCompositeIndexPercent, 2) + '%'
									: formattedMoney(finDataList.shanghaiCompositeIndexPercent, 2) + '%'
							}}
						</div>
					</div>
					<div class="standcontentname">上证指数</div>
				</div>
				<div class="standardization">
					<img src="@/assets/standardizationIndex.png" alt="" />
				</div>
			</div>
			<div class="data_item" style="background: linear-gradient(180deg, #dcecff 0%, #f2f9ff 100%)">
				<div class="standcontent">
					<div class="standcontentTop">
						<div class="standcontentTopText">
							{{ formattedMoney(finDataList.nasdaq, 2) }}
						</div>
						<div class="standcontentTopImg">
							<img v-if="finDataList.nasdaqTrend > 0" src="@/assets/arrowtopimg1.png" alt="" />
							<img v-else src="@/assets/arrowtopimg.png" alt="" />
						</div>
						<div class="standTextdetailsItem" :style="finDataList.nasdaqTrend > 0 ? 'color: #F53F3F' : 'color: #00B89B'">
							{{ finDataList.nasdaqTrend > 0 ? '+' + formattedMoney(finDataList.nasdaqTrendNum) : formattedMoney(finDataList.nasdaqTrendNum) }}
							{{
								finDataList.nasdaqTrend > 0
									? '+' + formattedMoney(finDataList.nasdaqPercent, 2) + '%'
									: formattedMoney(finDataList.nasdaqPercent, 2) + '%'
							}}
						</div>
					</div>
					<div class="standcontentname">纳斯达克</div>
				</div>
				<div class="standardization1 standardization">
					<img src="@/assets/standardizationIndex1.png" alt="" />
				</div>
			</div>
			<div class="data_item" style="background: linear-gradient(180deg, #e2e5ff 0%, #f5f7ff 100%)">
				<div class="standcontent">
					<div class="standcontentTop">
						<div class="standcontentTopText">
							{{ formattedMoney(finDataList.usDollarIndex, 2) }}
						</div>
						<div class="standcontentTopImg">
							<img v-if="finDataList.usDollarIndexTrend > 0" src="@/assets/arrowtopimg1.png" alt="" />
							<img v-else src="@/assets/arrowtopimg.png" alt="" />
						</div>
						<div class="standTextdetailsItem" :style="finDataList.usDollarIndexTrend > 0 ? 'color: #F53F3F' : 'color: #00B89B'">
							{{
								finDataList.usDollarIndexTrend > 0
									? '+' + formattedMoney(finDataList.usDollarIndexTrendNum)
									: formattedMoney(finDataList.usDollarIndexTrendNum)
							}}
							{{
								finDataList.usDollarIndexTrend > 0
									? '+' + formattedMoney(finDataList.usDollarIndexPercent, 2) + '%'
									: formattedMoney(finDataList.usDollarIndexPercent, 2) + '%'
							}}
						</div>
					</div>
					<div class="standcontentname">美元指数</div>
				</div>
				<div class="standardization standardization2">
					<img src="@/assets/standardizationIndex2.png" alt="" />
				</div>
			</div>
			<div class="data_item" style="background: linear-gradient(180deg, #e2e5ff 0%, #f5f7ff 100%)">
				<div class="standcontent">
					<div class="standcontentTop">
						<div class="standcontentTopText">
							{{ formattedMoney(finDataList.hangSengIndex, 2) }}
						</div>
						<div class="standcontentTopImg">
							<img v-if="finDataList.hangSengIndexTrend > 0" src="@/assets/arrowtopimg1.png" alt="" />
							<img v-else src="@/assets/arrowtopimg.png" alt="" />
						</div>
						<div class="standTextdetailsItem" :style="finDataList.hangSengIndexTrend > 0 ? 'color: #F53F3F' : 'color: #00B89B'">
							{{
								finDataList.hangSengIndexTrend > 0
									? '+' + formattedMoney(finDataList.hangSengIndexTrendNum)
									: formattedMoney(finDataList.hangSengIndexTrendNum)
							}}
							{{
								finDataList.hangSengIndexTrend > 0
									? '+' + formattedMoney(finDataList.hangSengIndexPercent, 2) + '%'
									: formattedMoney(finDataList.hangSengIndexPercent, 2) + '%'
							}}
						</div>
					</div>
					<div class="standcontentname">恒生指数</div>
				</div>
				<div class="standardization standardization3">
					<img src="@/assets/standardizationIndex3.png" alt="" />
				</div>
			</div>
			<div class="data_item" style="background: linear-gradient(180deg, #daf6ff 0%, #f2fcff 100%)">
				<div class="standcontent">
					<div class="standcontentTop">
						<div class="standcontentTopText">
							{{ formattedMoney(finDataList.onshoreRmb, 4) }}
						</div>
						<div class="standcontentTopImg">
							<img v-if="finDataList.onshoreRmbTrend > 0" src="@/assets/arrowtopimg1.png" alt="" />
							<img v-else src="@/assets/arrowtopimg.png" alt="" />
						</div>
						<div class="standTextdetailsItem" :style="finDataList.onshoreRmbTrend > 0 ? 'color: #F53F3F' : 'color: #00B89B'">
							{{
								finDataList.onshoreRmbTrend > 0
									? '+' + formattedMoney(finDataList.onshoreRmbTrendNum)
									: formattedMoney(finDataList.onshoreRmbTrendNum)
							}}
							{{
								finDataList.onshoreRmbTrend > 0
									? '+' + formattedMoney(finDataList.onshoreRmbPercent, 2) + '%'
									: formattedMoney(finDataList.onshoreRmbPercent, 2) + '%'
							}}
						</div>
					</div>
					<div class="standcontentname">在岸人民币</div>
				</div>
				<div class="standardization standardization4" style="">
					<img src="@/assets/standardizationIndex4.png" alt="" />
				</div>
			</div>
			<div class="data_item" style="background: linear-gradient(180deg, #daf6ff 0%, #f2fcff 100%)">
				<div class="standcontent">
					<div class="standcontentTop">
						<div class="standcontentTopText">
							{{ formattedMoney(finDataList.offshoreRmb, 4) }}
						</div>
						<div class="standcontentTopImg">
							<img v-if="finDataList.offshoreRmbTrend > 0" src="@/assets/arrowtopimg1.png" alt="" />
							<img v-else src="@/assets/arrowtopimg.png" alt="" />
						</div>
						<div class="standTextdetailsItem" :style="finDataList.offshoreRmbTrend > 0 ? 'color: #F53F3F' : 'color: #00B89B'">
							{{
								finDataList.offshoreRmbTrend > 0
									? '+' + formattedMoney(finDataList.offshoreRmbTrendNum)
									: formattedMoney(finDataList.offshoreRmbTrendNum)
							}}
							{{
								finDataList.offshoreRmbTrend > 0
									? '+' + formattedMoney(finDataList.offshoreRmbPercent, 2) + '%'
									: formattedMoney(finDataList.offshoreRmbPercent, 2) + '%'
							}}
						</div>
					</div>
					<div class="standcontentname">离岸人民币</div>
				</div>
				<div class="standardization standardization5">
					<img src="@/assets/standardizationIndex5.png" alt="" />
				</div>
			</div>
		</div>
		<div class="tab_box">
			<!-- <div class="title">标准化产品</div> -->

			<div class="tab" :class="activeName === route.name ? 'tabAct' : ''" v-for="route in routes" @click="handleTabClick(route)" :key="route.name">
				<img :src="activeName == route.name ? route.icon_active : route.icon" />
				{{ route.name }}
			</div>
		</div>
		<component :is="componentNames" />
	</div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import ABSimgActive from '@/assets/ABSimgActive.png';
import ABSimg from '@/assets/ABSimg.png';
import REITsimgActive from '@/assets/REITsimgActive.png';
import REITsimg from '@/assets/REITsimg.png';
import industryActiveImg from '@/assets/industryActiveImg.png';
import industryImg from '@/assets/industryImg.png';
import industry from './industry.vue';
import financeAbs from './finance-abs.vue';
import financeReits from './finance-reits.vue';
import { formattedMoney } from 'UTILS';
import { getFinanceIndex } from '@/api/syt.js';

const finDataList = ref([]);
const routes = [
	{
		name: '行业概况',
		componentName: industry,
		icon: industryImg,
		icon_active: industryActiveImg,
	},
	{
		name: 'REITs',
		componentName: financeReits,
		icon: REITsimg,
		icon_active: REITsimgActive,
	},
	{
		name: 'ABS',
		componentName: financeAbs,
		icon: ABSimg,
		icon_active: ABSimgActive,
	},
];
const componentNames = ref(industry);
const activeName = ref('行业概况');

const handleTabClick = (item) => {
	componentNames.value = item.componentName;
	activeName.value = item.name;
};

onMounted(() => {
	getfinData();
});

const getfinData = async () => {
	await getFinanceIndex()
		.then((res) => {
			finDataList.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
</script>

<style lang="scss" scoped>
.tab_box {
	width: 100%;
	box-sizing: border-box;
	font-size: 14px;
	font-weight: 600;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	background-color: transparent;

	.title {
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		margin-right: 15px;
		color: #1a1a1a;
	}

	.tab {
		width: 216px;
		height: 48px;
		padding: 0 16px;
		display: flex;
		align-items: center;
		cursor: pointer;
		font-size: 16px;
		font-weight: 500;
		line-height: 24px;
		color: #4e5969;
		background-color: #f2f3f5;
		border-radius: 4px 4px 0 0;
		img {
			width: 16px;
			height: 16px;
			margin-top: -3px;
			margin-right: 4px;
		}
	}

	.tabAct {
		font-size: 16px;
		color: #1868f1;
		background-color: #fff;
	}
}

.data_box {
	width: 100%;
	height: 168px;
	display: flex;
	align-items: center;
	gap: 16px;
	padding: 20px;
	background: #ffffff;
	box-sizing: border-box;
	margin-bottom: 16px;
	.data_item {
		width: 254.56px;
		height: 128px;
		padding: 35px 10px 35px 24px;
		border-radius: 4px;
		background: linear-gradient(180deg, #dcecff 0%, #f2f9ff 100%);
		box-sizing: border-box;
		position: relative;
		.standcontent {
			height: 58px;
			.standcontentTop {
				display: flex;
				margin-bottom: 4px;
				height: 32px;
				align-items: flex-end;
				.standcontentTopText {
					letter-spacing: -2px;
					font-weight: 600;
					font-size: 32px;
					line-height: 32px;
					color: #0f2860;
				}
				.standcontentTopImg {
					width: 14px;
					height: 14px;
					margin: 0 0 5px 4px;
					img {
						width: 100%;
						height: 100%;
					}
				}
				.standTextdetailsItem {
					font-weight: 600;
					font-size: 14px;
					margin-left: 1px;
					line-height: 24px;
					white-space: nowrap;
					color: #f53f3f;
				}
			}
			.standcontentname {
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				height: 22px;
				color: #0f2860;
			}
		}
		.standardization {
			position: absolute;
			right: 7.08px;
			bottom: -3px;
			width: 115.6px;
			height: 70px;
			img {
				width: 100%;
				height: 100%;
			}
		}
		.standardization1 {
			width: 118px;
			height: 71px;
			right: 5.3px;
			bottom: -2.5px;
		}
		.standardization2 {
			width: 114.4px;
			height: 76.3px;
			right: 14.8px;
			bottom: 11.7px;
		}
		.standardization3 {
			width: 104.2px;
			height: 61.67px;
			right: 14.67px;
			bottom: 10.15px;
		}
		.standardization4 {
			width: 135.43px;
			height: 51px;
			right: 12.91px;
			bottom: 10px;
		}
		.standardization5 {
			width: 135.57px;
			height: 45.32px;
			right: 12.4px;
			bottom: 9.68px;
		}
	}
}
</style>
