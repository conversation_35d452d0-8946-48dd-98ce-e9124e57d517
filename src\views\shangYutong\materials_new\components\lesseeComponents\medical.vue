<template>
	<div class="cangchu_wrap">
		<template v-if="leftData && leftData.length > 1">
			<div class="double_box_wrap" v-for="(item, index) in leftData" :key="index">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">基本信息</div>
					</div>
					<div class="content_wrap" v-if="item.id">
						<div class="content_" v-for="(childitem, childIndex) in informationList" :key="childIndex">
							<div class="content_left">{{ childitem.name }}</div>
							<div class="content_right">{{ item[childitem.key] }}</div>
						</div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 122px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">服务内容</div>
					</div>
					<div class="content_wrap" v-if="item.id">
						<div class="content_" v-for="(childitem, childIndex) in serviceContentList" :key="childIndex">
							<div class="content_left">{{ childitem.name }}</div>
							<div class="content_right">{{ item[childitem.key] }}</div>
						</div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 354px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>

				<div class="single_wrap">
					<div class="title1">
						<div class="title">地理环境</div>
					</div>
					<div class="content_wrap" style="padding-bottom: 20px" v-if="item.id">
						<div class="geographic">
							{{ item.environment }}
						</div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 102px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>

				<div class="single_wrap">
					<div class="title1">
						<div class="title">机构特色</div>
					</div>
					<div class="content_wrap" style="padding-bottom: 20px" v-if="item.id">
						<div class="features">
							<div class="features_Item" v-for="(childitem, childIndex) in featuresList" :key="childIndex">
								<img class="item_img" :src="item[childitem.key] === '1' ? childitem.activeImg : childitem.img" alt="" />
								<div class="item_content">
									<div class="item_content_l">
										<img class="item_content_img" :src="item[childitem.key] === '1' ? featuresCheck : featuresClose" alt="" />
									</div>
									<div class="item_content_text" :style="{ color: item[childitem.key] === '1' ? '#1D2129' : '#86909C' }">{{ childitem.name }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 136px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>

				<div class="single_wrap">
					<div class="title1">
						<div class="title">配套设施</div>
					</div>
					<div class="content_wrap" v-if="item.id">
						<div class="content_" v-for="(childitem, childIndex) in facilitiesList" :key="childIndex">
							<div class="content_left">{{ childitem.name }}</div>
							<div class="content_right">{{ item[childitem.key] }}</div>
						</div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 248px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>

				<div class="single_wrap">
					<div class="title1">
						<div class="title">入住须知</div>
					</div>
					<div class="content_wrap" style="padding-bottom: 20px" v-if="item.id">
						<div class="geographic" v-html="item.checkInNotes"></div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 146px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</template>
		<template v-else>
			<div class="box_wrap" v-for="(item, index) in leftData" :key="index">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">基本信息</div>
						</div>
						<div class="content_wrap" v-if="item.id">
							<div class="content_" v-for="(childitem, childIndex) in informationList" :key="childIndex">
								<div class="content_left">{{ childitem.name }}</div>
								<div class="content_right">{{ item[childitem.key] }}</div>
							</div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 122px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>

					<div class="single_wrap">
						<div class="title1">
							<div class="title">地理环境</div>
						</div>
						<div class="content_wrap" style="padding-bottom: 20px" v-if="item.id">
							<div class="geographic">
								{{ item.environment }}
							</div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 102px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">机构特色</div>
						</div>
						<div class="content_wrap" style="padding-bottom: 20px" v-if="item.id">
							<div class="features">
								<div class="features_Item" v-for="(childitem, childIndex) in featuresList" :key="childIndex">
									<img class="item_img" :src="item[childitem.key] === '1' ? childitem.activeImg : childitem.img" alt="" />
									<div class="item_content">
										<div class="item_content_l">
											<img class="item_content_img" :src="item[childitem.key] === '1' ? featuresCheck : featuresClose" alt="" />
										</div>
										<div class="item_content_text" :style="{ color: item[childitem.key] === '1' ? '#1D2129' : '#86909C' }">{{ childitem.name }}</div>
									</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 136px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">入住须知</div>
						</div>
						<div class="content_wrap" style="padding-bottom: 20px" v-if="item.id">
							<div class="geographic" v-html="item.checkInNotes"></div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 146px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">服务内容</div>
						</div>
						<div class="content_wrap" v-if="item.id">
							<div class="content_" v-for="(childitem, childIndex) in serviceContentList" :key="childIndex">
								<div class="content_left">{{ childitem.name }}</div>
								<div class="content_right">{{ item[childitem.key] }}</div>
							</div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 354px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">配套设施</div>
						</div>
						<div class="content_wrap" v-if="item.id">
							<div class="content_" v-for="(childitem, childIndex) in facilitiesList" :key="childIndex">
								<div class="content_left">{{ childitem.name }}</div>
								<div class="content_right">{{ item[childitem.key] }}</div>
							</div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 248px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import medicalimg from '@/assets/images/shangYutong/buildInfo/medicalimg.png';
import insurance from '@/assets/images/shangYutong/buildInfo/insurance.png';
import accommodationact from '@/assets/images/shangYutong/buildInfo/accommodationact.png';
import accommodation from '@/assets/images/shangYutong/buildInfo/accommodation.png';
import experienceact from '@/assets/images/shangYutong/buildInfo/experienceact.png';
import experience from '@/assets/images/shangYutong/buildInfo/experience.png';
import migratoryact from '@/assets/images/shangYutong/buildInfo/migratoryact.png';
import migratory from '@/assets/images/shangYutong/buildInfo/migratory.png';
import brandChainact from '@/assets/images/shangYutong/buildInfo/brandChainact.png';
import brandChain from '@/assets/images/shangYutong/buildInfo/brandChain.png';
import featuresCheck from '@/assets/featuresCheck.png';
import featuresClose from '@/assets/featuresClose.png';

import { getMedicalInfo } from '@/api/syt.js';
import { defineExpose } from 'vue';
const props = defineProps({
	buildData: {
		type: Array,
		default: () => [],
	},
});
watch(
	() => props.buildData,
	(newVal) => {},
	{
		deep: true,
	}
);

const informationList = ref([
	{
		name: '收费标准',
		key: 'charge',
	},
	{
		name: '收住对象',
		key: 'receiver',
	},
]);

const serviceContentList = ref([
	{
		name: '生活照料',
		key: 'lifeCare',
	},
	{
		name: '营养膳食',
		key: 'nutritionDiet',
	},
	{
		name: '失能照护',
		key: 'disabilityCare',
	},
	{
		name: '康复训练',
		key: 'rehealthyTraining',
	},
	{
		name: '文化娱乐',
		key: 'entertainment',
	},
	{
		name: '智慧养老服务',
		key: 'smartService',
	},
	{
		name: '个性化服务',
		key: 'personalizedService',
	},
]);

const facilitiesList = ref([
	{
		name: '老人房间设施',
		key: 'roomFacility',
	},
	{
		name: '休闲娱乐设施',
		key: 'recreationalFacility',
	},
	{
		name: '公共服务设施',
		key: 'publicServiceFacility',
	},
	{
		name: '医疗康复设施',
		key: 'medicalRehabilitationFacility',
	},
	{
		name: '安全保障设施',
		key: 'securityFacility',
	},
]);

const featuresList = ref([
	{
		name: '医保定点',
		key: 'medicalInsuranceDesignated',
		img: insurance,
		activeImg: medicalimg,
	},
	{
		name: '异地收住',
		key: 'remoteAccommodation',
		img: accommodation,
		activeImg: accommodationact,
	},
	{
		name: '试住体验',
		key: 'trialStayExperience',
		img: experience,
		activeImg: experienceact,
	},
	{
		name: '候鸟旅居',
		key: 'migratoryBirdsResidence',
		img: migratory,
		activeImg: migratoryact,
	},
	{
		name: '品牌连锁',
		key: 'brandChain',
		img: brandChain,
		activeImg: brandChainact,
	},
]);

const leftData = ref([]);

const getData = async () => {
	let ids = props.buildData.map((item) => item.id).join(',');
	const res = await getMedicalInfo({
		buildingIds: ids,
	});
	if (res.code === 200) {
		if (res.data.length > 0) {
			res.data.forEach((element) => {
				let checkInNotes = element.checkInNotes.replace(/\n/g, '<br>');
				element.checkInNotes = checkInNotes;
			});
		}
		if ((res.data.length > 1 && props.buildData.length > 1) || (res.data.length === 1 && props.buildData.length === 1)) {
			leftData.value = res.data;
		} else {
			if (props.buildData.length > 1 && res.data.length === 1) {
				leftData.value = [...res.data, { id: null }];
			} else if (props.buildData.length === 1 && res.data.length === 0) {
				leftData.value = [{ id: null }];
			} else if (props.buildData.length > 1 && res.data.length === 0) {
				leftData.value = [{ id: null }, { id: null }];
			}
		}
	}
};

defineExpose({
	getData,
});
</script>
<style lang="less" scoped>
.cangchu_wrap {
	flex: 1;
	display: flex;
	gap: 16px;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
	}
	.double_box_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16px;
	}
}

.single_wrap {
	// flex: 1;
	box-sizing: border-box;
	// min-height: 300px;
	border: 1px solid #e5e6eb;
	border-radius: 4px;
	display: flex;
	flex-direction: column;
	.title1 {
		box-sizing: border-box;
		padding: 0 20px;
		width: 100%;
		height: 48px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #f7f8fa;
		border-bottom: 1px solid #e5e6eb;
		.title {
			font-size: 16px;
			font-weight: 600;
			color: #1d2129;
		}
	}
	.content_wrap {
		padding: 20px 16px 18px 16px;
		.features {
			display: flex;
			gap: 16px;
			.features_Item {
				width: calc(100% / 5);
				height: 96px;
				padding: 21px 16px;
				border-radius: 4px;
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				align-items: center;
				background: #f7f8fa;
				.item_img {
					width: 28px;
					height: 28px;
					margin-bottom: 4px;
				}
				.item_content {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 22px;
					.item_content_l {
						width: 16px;
						height: 16px;
						margin: 3px 4px 3px 0;
						.item_content_img {
							width: 16px;
							height: 16px;
						}
					}
					.item_content_text {
						font-weight: 400;
						font-size: 14px;
						line-height: 22px;
						text-align: center;
						color: #1d2129;
					}
				}
			}
		}
		.geographic {
			background: #f7f8fa;
			width: calc(100% - 32px);
			border-top-right-radius: 4px;
			border-bottom-right-radius: 4px;
			padding: 9px 16px;
			font-weight: 400;
			font-size: 14px;
			line-height: 22px;
			color: #4e5969;
		}
		.content_ {
			display: flex;
			.content_left {
				box-sizing: border-box;
				padding: 9px 16px;
				background: #e8f3ff;
				border-top-left-radius: 4px;
				border-bottom-left-radius: 4px;
				font-weight: 500;
				font-size: 14px;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 116px;
				margin: 0 2px 2px 0;
				color: #1868f1;
			}
			.content_right {
				width: calc(100% - 118px);
				background: #f7f8fa;
				border-top-right-radius: 4px;
				border-bottom-right-radius: 4px;
				box-sizing: border-box;
				margin: 0 0 2px 0;
				padding: 9px 16px;
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: #4e5969;
			}
		}
	}
	.empty_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-weight: 400;
		color: #86909c;
		img {
			width: 80px;
			height: 80px;
		}
	}
}
</style>
