<template>
	<div class="viewMore">
		<h1 style="padding-left: 20px; margin: 0px">{{ title }}</h1>
		<div v-for="item in newsList" :key="item.id" class="newsItem" @click="toDetail(item.id)">
			<div class="characters">
				<div class="timeAndAuthor">
					<span>{{ item.date }}</span>
					<span>|</span>
					<div style="margin-left: 8px; line-height: 16px">作者：{{ item.articleAuthor }}</div>
				</div>
				<p>{{ item.articleTitle }}</p>
				<p class="summary">{{ item.summary }}</p>
			</div>
			<img class="img" :src="item.displayImg" alt="" />
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, watch, nextTick } from 'vue';
import { ArticleList } from '../../../src/api/home.js';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const newsList = ref([]);
let tableDataTerm = reactive({
	page: {
		currentPage: 1,
		pageSize: 10,
		total: 0,
	},
});

const getNewsList = async (type) => {
	let params = {
		articleType: route.query.articleType,
		pageNo: tableDataTerm.page.currentPage,
		pageSize: tableDataTerm.page.pageSize,
		area: route.query.area,
	};
	await ArticleList(params).then((res) => {
		console.log('新闻', res);
		if (type) {
			newsList.value = [...newsList.value, ...res.data.rows];
		} else {
			newsList.value = res.data.rows;
		}
		tableDataTerm.page.total = res.data.total;
	});
};

watch(
	() => route.query.articleType,
	(oldVal, newVal) => {
		window.scrollTo(0, 0);

		if (route.query.articleType == 0) {
			title.value = '解析';
		}
		if (route.query.articleType == 1) {
			title.value = '洞察';
		}
		if (route.query.articleType == 2) {
			title.value = '数据';
		}

		getNewsList();
	}
);

const title = ref('');
onMounted(() => {
	// 滚动加载
	window.addEventListener('scroll', handleScroll);
	console.log(2142342, route.query.articleType);
	if (route.query.articleType == 0) {
		title.value = '解析';
	}
	if (route.query.articleType == 1) {
		title.value = '洞察';
	}
	if (route.query.articleType == 2) {
		title.value = '数据';
	}
	getNewsList();
	window.scrollTo(0, 0);
});

const router = useRouter();
const toDetail = (id) => {
	router.push({ path: '/newsdetail', query: { id: id } });
};

function handleScroll() {
	const scrollTop = document.documentElement.scrollTop;
	const clientHeight = document.documentElement.clientHeight;
	const scrollHeight = document.documentElement.scrollHeight;
	if (scrollTop + clientHeight >= scrollHeight) {
		if (tableDataTerm.page.currentPage * tableDataTerm.page.pageSize >= tableDataTerm.page.total) {
			// 没有更多数据了
			return;
		}
		tableDataTerm.page.currentPage++;
		getNewsList('1');
		// 滚动条到达底部，触发懒加载
	}
}
</script>
<style scoped lang="less">
.viewMore {
	width: 100%;
	height: 100%;
	min-height: 100vh;
	background: #fff;
	padding: 20px 0;
	.newsItem {
		display: flex;
		justify-content: space-between;
		padding: 20px 20px 10px 20px;
		border-bottom: 1px solid #eee;
		margin: 0 auto;

		.characters {
			width: calc(100% - 120px);
			.timeAndAuthor {
				display: flex;
				color: rgb(117, 117, 117);
				font-size: 12px;
			}
			.summary {
				color: rgb(117, 117, 117);
				font-size: 14px;
			}
		}
		img {
			object-fit: contain;
			width: 100px;
		}
	}
}
</style>
