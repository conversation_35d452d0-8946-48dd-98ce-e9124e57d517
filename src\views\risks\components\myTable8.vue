<template>
	<div>
		<el-table :data="data" :lazy="true" ref="tableRef" :class="classname" empty-text="暂无数据">
			<el-table-column label="现金流测算" align="center" label-class-name="fixed_header">
				<el-table-column
					resizable
					v-for="column in tableColumns"
					:key="column.prop"
					label-class-name="fixed_header"
					:prop="column.prop"
					:label="column.label"
					:fixed="column.fixed"
					:width="column.width"
					show-overflow-tooltip
				>
					<template #default="scope">
						<div v-if="column.label === '科目'">
							{{ scope.row[column.prop] }}
						</div>
						<div v-else>
							{{ $formattedMoney($utils.handleNumber(scope.row[column.prop])) }}
						</div>
					</template>
				</el-table-column>
			</el-table-column>
		</el-table>
	</div>
</template>
<script setup>
import { defineProps, reactive, ref, onMounted } from 'vue';
const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
	classname: {
		type: String,
		default: [],
	},
});

let tableRef = ref(null);

const { proxy } = getCurrentInstance();
nextTick(() => {
	proxy?.$dragTable(tableRef);
});
const tableColumns = [
	{ prop: 'title', label: '科目', width: '220' },
	{ prop: 'sum', label: '（元）', width: '124' },
	{ prop: 'y0', label: 'Y0（元）', width: '124' },
	{ prop: 'y1', label: 'Y1（元）', width: '124' },
	{ prop: 'y2', label: 'Y2（元）', width: '124' },
	{ prop: 'y3', label: 'Y3（元）', width: '124' },
	{ prop: 'y4', label: 'Y4（元）', width: '124' },
	{ prop: 'y5', label: 'Y5（元）', width: '124' },
	{ prop: 'y6', label: 'Y6（元）', width: '124', fixed: 'right' },
];

onMounted(() => {});
</script>
<style scoped lang="scss">
// .top-scroll {
// 	overflow-y: hidden;
// 	overflow-x: auto;

// 	.top-scroll-content {
// 		background-color: #fff;
// 		height: 1px;
// 	}
// }

::v-deep .fixed_header .cell {
	font-weight: 500;
	font-size: 14px;
	line-height: 22px;
	color: #1d2129;
}

::v-deep .fixed_header {
	height: 40px;
	background: #f7f8fa !important;
}

.el-table {
	--el-table-border-color: #e5e6eb;
	border-radius: 4px;
	.el-table__row {
		background: #fff !important;
	}
}
</style>
