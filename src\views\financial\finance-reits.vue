<template>
	<el-form :model="form" label-width="120px">
		<el-form-item label="关键词搜索">
			<!-- <span>关键词搜索</span> -->
			<el-input v-model="form.value" class="w-50 m-2" placeholder="搜索产品、证券、机构" style="width: 300px" />
		</el-form-item>

		<el-form-item label="发行日期">
			<!-- <span>发行日期</span> -->
			<el-date-picker
				v-model="form.data"
				type="daterange"
				range-separator="To"
				start-placeholder="Start date"
				end-placeholder="End date"
				style="width: 300px; flex: none"
			/>
		</el-form-item>

		<el-form-item label="产品状态">
			<!-- <span>基础资产</span> -->
			<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup0" size="large">
				<el-checkbox-button v-for="(product, index) in productList" :key="index" :label="product">
					{{ product }}
				</el-checkbox-button>
			</el-checkbox-group>
		</el-form-item>

		<el-form-item label="申报类型">
			<!-- <span>基础资产</span> -->
			<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup1" size="large">
				<el-checkbox-button v-for="(declare, index) in declareList" :key="index" :label="declare">
					{{ declare }}
				</el-checkbox-button>
			</el-checkbox-group>
		</el-form-item>

		<el-form-item label="资产业态">
			<!-- <span>基础资产</span> -->
			<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup2" size="large" border="false">
				<el-checkbox-button v-for="(subdivide, index) in assetssub" :key="index" :label="subdivide">
					{{ subdivide }}
				</el-checkbox-button>
			</el-checkbox-group>
		</el-form-item>

		<el-form-item label="交易场所">
			<!-- <span>基础资产</span> -->
			<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup3" size="large" border="false">
				<el-checkbox-button v-for="(place, index) in placeList" :key="index" :label="place">
					{{ place }}
				</el-checkbox-button>
			</el-checkbox-group>
		</el-form-item>
		<!-- 确认取消按钮 -->
		<el-form-item>
			<el-button type="primary" @click="onSubmit">确认</el-button>
			<el-button @click="oncancel">取消</el-button>
		</el-form-item>
	</el-form>
	<!-- 表格 -->
	<el-table :data="ReitsList" style="width: 100%" :header-cell-style="{ background: '#3170a7', color: '#ffffff ' }">
		<el-table-column prop="name" label="产品名称" />
		<el-table-column prop="code" label="代码" />
		<el-table-column prop="closingPrice" label="最新收盘价" />
		<el-table-column prop="percentChange" label="最新收盘涨跌幅" />
		<el-table-column prop="tradeDate" label="最新收盘日" />
		<el-table-column prop="assetCategory" label="资产业态" />
		<el-table-column prop="exchange" label="交易场所" />
		<el-table-column prop="totalMarketValue" label="总市值(亿)" />
		<el-table-column prop="status" label="产品状态" />
		<el-table-column prop="applicationType" label="申报类型" />
		<el-table-column prop="raiseShare" label="最新募集份额(亿份)" />
		<el-table-column prop="issuePrice" label="最新发行价格(元)" />
		<el-table-column prop="issueSize" label="最新发行规模(亿)" />
		<el-table-column prop="establishmentDate" label="成立日期" />
		<el-table-column prop="originator" label="原始权益人" />
		<el-table-column prop="fundManager" label="公募基金管理人" />
		<el-table-column prop="manager" label="基金经理" />
		<el-table-column prop="proportionOfSpecialPlan" label="投资专项计划规模占比" />
	</el-table>
	<el-pagination
		@current-change="handleCurrentChange"
		current-page="currentPage"
		layout="prev, pager, next,total, jumper"
		:page-size="10"
		class="mt-4"
		:total="total"
	/>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { getReitsListApi } from '@/api/finance.js';
// do not use same name with ref
//   表单
const form = reactive({
	value: '',
	data: '',
	checkboxGroup0: [],
	checkboxGroup1: [],
	checkboxGroup2: [],
	checkboxGroup3: [],
});
const currentPage = ref(1);
const total = ref(0);
const handleCurrentChange = (val) => {
	currentPage.value = val;
	getReitsList();
};
const productList = ['全部', '已申报', '募集中', '已成立', '已清算', '已取消', '已受理', '已通过'];
const assetssub = ['保障性租赁住房', '仓储物流', '产业园区', '消费基础设施'];
const declareList = ['全部', '首发', '扩募'];
const placeList = ['全部', '上海证券交易所', '深圳证券交易所'];

const onSubmit = () => {
	console.log('submit!');
	getReitsList();
};
//   表格
const tableData = [
	{
		date: '2016-05-03',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-02',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-04',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-01',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
];
// 点击取消
const oncancel = () => {
	console.log('cancel!');
	// 清空表单
	form.value = '';
	form.data = '';
	form.checkboxGroup0 = [];
	form.checkboxGroup1 = [];
	form.checkboxGroup2 = [];
	form.checkboxGroup3 = [];
};

// 发请求
const ReitsList = ref([]);
const getReitsList = async () => {
	const params = {
		keyword: form.value,
		issueDateStart: form.data[0],
		issueDateEnd: form.data[1],
		productStatus: form.checkboxGroup0.toString(),
		applicationType: form.checkboxGroup1.toString(), //申报类型
		assetCategory: form.checkboxGroup2.toString(), //资产业态
		exchange: form.checkboxGroup3.toString(), //资产业态
		pageNo: currentPage.value,
		pageSize: 10,
	};
	const res = await getReitsListApi(params)
		.then((res) => {
			console.log(res.result.records, 12345);
			ReitsList.value = res.result.records;

			total.value = res.result.total;
			return res;
		})
		.catch((err) => {
			console.log(err, 12345);
			return err;
		});
	// console.log(res,2346);
};
getReitsList();
</script>

<style scoped lang="less">
.el-input__wrapper {
	flex: none;
	flex-grow: 0 !important;
}
.el-checkbox-button {
	margin: 5px;
	// border:1px solid #;
}
.el-table__header-wrapper {
	background-color: #000 !important;
}
</style>
