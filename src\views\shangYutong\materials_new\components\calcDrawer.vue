<template>
	<arco-drawer
		:width="1180"
		@cancel="handlePropertyclose"
		unmountOnClose
		:drawerStyle="{ overflow: 'unset' }"
		:visible="drawerVisible"
		body-class="drwer_content"
		:header="false"
		:footer="false"
	>
		<div class="propertyclose" @click="handlePropertyclose">
			<img src="@/assets/propertyclose.png" />
		</div>
		<div class="container_drwer">
			<div class="mainTitle1">交易计算</div>
			<div class="tab_box">
				<div class="tab" :class="activeTabKey == route.key ? 'tabAct' : ''" v-for="route in routes" @click="handleTabClick(route)" :key="route.key">
					<img :src="activeTabKey == route.key ? route.icon_active : route.icon" />
					{{ route.name }}
				</div>
			</div>
			<div class="content_wrap">
				<div class="common_wrap">
					<div class="title">总金额</div>
					<div class="row" style="margin-bottom: 16px">
						<div class="col" v-if="activeTabKey == 'rent'">
							<div class="label">起止日期</div>
							<div class="form_item">
								<arco-range-picker v-model="dateValue" @change="onDateChange" style="width: 100%" />
							</div>
						</div>
						<div class="col">
							<div class="label">选择城市</div>
							<div class="form_item">
								<arco-cascader
									v-model="positionValue"
									:default-value="positionValue"
									@change="handleChange"
									placeholder="请选择城市"
									path-mode
									:fieldNames="{ label: 'label', value: 'label', children: 'children' }"
									ref="cascaderDom"
									:options="$vuexStore.state.cityArray"
									:style="{ width: '100%' }"
								/>
							</div>
						</div>
						<div class="col">
							<div class="label">商圈</div>
							<div class="form_item">
								<arco-select v-model="business" placeholder="请选择商圈" @change="Searchbuild">
									<arco-option
										v-for="(item, value) in businessList"
										:key="value"
										:label="item.businessDistrictName"
										:value="item.id"
										:style="{ color: item.id == business ? '#1868F1' : '#1D2129' }"
									/>
								</arco-select>
							</div>
						</div>
						<div class="col" v-if="activeTabKey == 'buy'">
							<div class="label">建筑物</div>
							<div class="form_item">
								<arco-select @change="handleItemChange" v-model="building" placeholder="请选择建筑物">
									<arco-option
										v-for="(item, value) in buildingList"
										:key="value"
										:label="item.buildingName"
										:value="item.uniqueCode"
										:style="{ color: item.uniqueCode == building ? '#1868F1' : '#1D2129' }"
									/>
								</arco-select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col" v-if="activeTabKey == 'rent'">
							<div class="label">建筑物</div>
							<div class="form_item">
								<arco-select @change="handleItemChange" v-model="building" placeholder="请选择建筑物">
									<arco-option
										v-for="(item, value) in buildingList"
										:key="value"
										:label="item.buildingName"
										:value="item.uniqueCode"
										:style="{ color: item.uniqueCode == building ? '#1868F1' : '#1D2129' }"
									/>
								</arco-select>
							</div>
						</div>
						<div class="col">
							<div class="label">面积(㎡)</div>
							<div class="form_item">
								<arco-input-number
									v-model="loanAmount"
									placeholder="请输入面积"
									style="background-color: none"
									:min="0"
									@input="loanAmountInput"
								></arco-input-number>
							</div>
						</div>
						<div class="col" v-if="activeTabKey == 'rent'">
							<div class="col">
								<div class="label">总金额(元)</div>
								<div class="form_item">
									<arco-input
										disabled
										v-model="totalMoney"
										placeholder="选择条件计算得出"
										style="background-color: none"
									></arco-input>
								</div>
							</div>
						</div>
						<div class="col" v-if="activeTabKey == 'buy'"></div>
					</div>
				</div>
				<div class="common_wrap" v-if="activeTabKey == 'buy'">
					<div class="title">贷款计算器</div>
					<div class="border_wrap">
						<div class="left">
							<div class="row" style="margin-bottom: 16px">
								<div class="col">
									<div class="label">贷款总额(元)</div>
									<div class="form_item">
										<arco-input v-model="totalMoney" placeholder="请输入贷款总额" style="background-color: none" type="number" :min="0"></arco-input>
									</div>
								</div>
								<div class="col">
									<div class="label">基准利率(%)</div>
									<div class="form_item">
										<arco-select v-model="interestRate" placeholder="请选择折扣">
											<arco-option
												v-for="(item, value) in interestRates"
												:key="value"
												:label="item.label"
												:value="item.value"
												:style="{ color: item.value == interestRate ? '#1868F1' : '#1D2129' }"
											/>
										</arco-select>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col">
									<div class="label">贷款期限(年)</div>
									<div class="form_item">
										<arco-input-number
											v-model="years"
											placeholder="请输入贷款期限"
											style="background-color: none"
											:min="1"
											:max="30"
										></arco-input-number>
									</div>
								</div>
								<div class="col">
									<div class="label">贷款方式</div>
									<div class="form_item">
										<arco-select style="width: 100%" v-model="loanMethod" placeholder="请选择贷款方式">
											<arco-option label="等额本金" value="benjin"></arco-option>
											<arco-option label="等额本息" value="benxi"></arco-option>
										</arco-select>
									</div>
								</div>
							</div>
						</div>
						<div class="right">
							<div class="total_wrap">
								{{ loanMethod == 'benjin' ? '首月' : '每月' }}应还
								<div class="total_money">{{ years ? (!isNaN(months) ? $formattedMoney(months) : 0) : 0 }}</div>
								元
							</div>
							<div class="loans_wrap">
								<div class="circle" style="background-color: #378eff"></div>
								<div>贷款总额：{{ $formattedMoney(totalMoney) || 0 }}元</div>
							</div>
							<div class="total_yh">
								<div class="circle" style="background-color: #2fe2ac"></div>
								<div>总应还款：{{ $formattedMoney(totalYh) }}元</div>
							</div>
							<div class="loans_year">
								<div class="circle" style="background-color: #ffcf63"></div>
								<div>贷款年限：{{ years || 0 }}年</div>
							</div>
						</div>
						<div class="btn_wrap">
							<div class="left_mask"></div>
							<arco-button type="primary" style="border-radius: 4px; z-index: 9" @click="calculate">开始计算</arco-button>
						</div>
					</div>
				</div>
				<div class="common_wrap" v-if="activeTabKey == 'buy'">
					<div class="title">还款计划</div>
					<div class="table_wrap">
						<arco-table
							row-key="id"
							:columns="cloumns"
							:data="tableData"
							:pagination="false"
							:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
						>
						</arco-table>
					</div>
				</div>
			</div>
		</div>
	</arco-drawer>
</template>
<script setup>
import calc_tab1 from '@/assets/images/shangYutong/buildInfo/calc_tab1.png';
import calc_tab1_active from '@/assets/images/shangYutong/buildInfo/calc_tab1_active.png';
import calc_tab2 from '@/assets/images/shangYutong/buildInfo/calc_tab2.png';
import calc_tab2_active from '@/assets/images/shangYutong/buildInfo/calc_tab2_active.png';
import { financeDataList } from '@/api/finance.js';
import { getBuildingSoldPrice, getBusinessDistrictList, getBuildingBusinessDistrictList } from '@/api/syt.js';
import { nextTick, getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance();

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
});
const emit = defineEmits(['update:modelValue', 'confirm']);
watch(
	() => props.modelValue,
	(newVal) => {
		drawerVisible.value = newVal;
	}
);
const drawerVisible = ref(false);
watch(drawerVisible, (newVal) => {
	emit('update:modelValue', newVal);
});
const cascaderDom = ref();
const activeTabKey = ref('buy');
const routes = [
	{
		name: '购买',
		key: 'buy',
		icon: calc_tab2,
		icon_active: calc_tab2_active,
	},
	{
		name: '租赁',
		key: 'rent',
		icon: calc_tab1,
		icon_active: calc_tab1_active,
	},
];
const years = ref();
const interestRate = ref(1); //利率折扣，默认不打折
const loanAmount = ref('');
const totalMoney = ref(''); //// 贷款总额

const interest = ref('3.95');
const business = ref(); //商圈
const building = ref(); //建筑物
const loanMethod = ref('benxi');
const months = ref(0);
// 根据商圈获取具体楼宇
const buildingList = ref([]);
const dateValue = ref([]);
const province = ref('');
const city = ref('');

const interestRates = ref([
	{
		label: '不打折',
		value: 1,
	},
	{
		label: '9折',
		value: 0.9,
	},
	{
		label: '8.5折',
		value: 0.85,
	},
	{
		label: '8折',
		value: 0.8,
	},
]);

const cloumns = [
	{
		title: '月数',
		dataIndex: 'month',
	},
	{
		title: '期初余额（元）',
		dataIndex: 'initialbalance',
	},
	{
		title: '偿还本息（元）',
		dataIndex: 'Monthlypay',
	},

	{
		title: '利息（元）',
		dataIndex: 'accrual',
	},
	{
		title: '本金（元）',
		dataIndex: 'capital',
	},
	{
		title: '剩余本金（元）',
		dataIndex: 'terminal',
	},
];
const tableData = ref([]);
const positionValue = ref([]);
const totalYh = ref(0);
function totalValB() {
	const yearsVal = parseFloat(years.value);
	const totalMoneyVal = parseFloat(totalMoney.value);
	const monthlyPayment = parseFloat(months.value);
	const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 12 / 100; //每月利率
	const totalMonths = yearsVal * 12; // 总还款月数
	// 计算总还款额
	let totalRepayment = 0;
	if (loanMethod.value === 'benxi') {
		// 计算等额本息方式的总还款额
		totalRepayment = monthlyPayment * totalMonths;
	} else if (loanMethod.value === 'benjin') {
		// 计算等额本金方式的总还款额
		totalRepayment = 0; // 初始化总还款额
		for (let i = 0; i < totalMonths; i++) {
			const monthbenjin = totalMoneyVal / totalMonths; // 每月本金
			const monthlyInterest = (totalMoneyVal - i * monthbenjin) * monthlyInterestRate; // 每月利息
			totalRepayment += monthbenjin + monthlyInterest; // 每月本金加上利息
		}
	}
	if (isNaN(totalRepayment)) {
		return 0; // 或者其他你认为合适的默认值
	}
	totalYh.value = totalRepayment.toFixed(2);
}

const updateTableData = () => {
	tableData.value = [];
	const loanAmountValue = parseFloat(totalMoney.value);
	const yearsVal = parseInt(years.value);
	const totalMonths = yearsVal * 12;
	if (isNaN(loanAmountValue) || isNaN(yearsVal)) {
		return; // 处理无效数据
	}
	// 初始化剩余本金
	let remainingPrincipal = loanAmountValue;
	if (loanMethod.value === 'benxi') {
		// 等额本息的表格数据
		for (let i = 1; i <= totalMonths; i++) {
			const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;
			// 计算每月还款额（等额本息方式）
			const monthlyPaymentBenxi = (loanAmountValue * monthlyInterestRate) / (1 - Math.pow(1 + monthlyInterestRate, -totalMonths));
			// 计算利息
			const interestAccrualBenxi = remainingPrincipal * monthlyInterestRate;
			// 计算本金还款
			const capitalRepaymentBenxi = monthlyPaymentBenxi - interestAccrualBenxi;
			// 计算期末余额
			remainingPrincipal -= capitalRepaymentBenxi;
			const rowDataBenxi = {
				month: i,
				initialbalance: proxy.$formattedMoney(
					(Number(capitalRepaymentBenxi) + Number(Number(remainingPrincipal) === -0 ? 0 : remainingPrincipal)).toFixed(2)
				),
				// initialbalance: Number(monthlyPaymentBenxi.toFixed(2)) + Number(remainingPrincipal.toFixed(2)),
				Monthlypay: proxy.$formattedMoney(monthlyPaymentBenxi.toFixed(2)),
				capital: proxy.$formattedMoney(capitalRepaymentBenxi.toFixed(2)),
				accrual: proxy.$formattedMoney(interestAccrualBenxi.toFixed(2)),
				terminal: proxy.$formattedMoney(Number(remainingPrincipal.toFixed(2)) === -0 ? 0 : remainingPrincipal.toFixed(2)),
			};
			// 将数据推入 tableData 数组
			tableData.value.push(rowDataBenxi);
		}
	} else {
		// 等额本金的还款数据
		for (let i = 1; i <= totalMonths; i++) {
			const totalMoneyVal = parseFloat(totalMoney.value);
			const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;
			const monthbenjin = totalMoneyVal / totalMonths; // 每月本金

			// 计算利息
			const interestAccrualBenjin = remainingPrincipal * monthlyInterestRate;

			// 计算本金还款
			const capitalRepaymentBenjin = monthbenjin;

			// 计算剩余本金
			remainingPrincipal -= monthbenjin;

			const rowDataBenjin = {
				month: i,
				initialbalance: proxy.$formattedMoney(
					(Number(capitalRepaymentBenjin) + Number(Number(remainingPrincipal) === -0 ? 0 : remainingPrincipal)).toFixed(2)
				),
				// initialbalance: Number((capitalRepaymentBenjin + interestAccrualBenjin).toFixed(2)) + Number(remainingPrincipal.toFixed(2)),
				Monthlypay: proxy.$formattedMoney((capitalRepaymentBenjin + interestAccrualBenjin).toFixed(2)),
				capital: proxy.$formattedMoney(capitalRepaymentBenjin.toFixed(2)),
				accrual: proxy.$formattedMoney(interestAccrualBenjin.toFixed(2)),
				terminal: proxy.$formattedMoney(Number(remainingPrincipal.toFixed(2)) === -0 ? 0 : remainingPrincipal.toFixed(2)),
			};
			tableData.value.push(rowDataBenjin);
		}
	}
};
const calculate = () => {
	updateTableData();
	calculateMonthlyPayment();
	totalValB();
};
const changeVal = () => {
	if (loanMethod.value === 'benxi' || loanMethod.value === 'benjin') {
		updateTableData(); // 更新表格数据
	}
};

const calculateMonthlyPayment = () => {
	// 将年利率转换为月利率
	const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;

	// 贷款总额
	const loanAmountValue = parseFloat(totalMoney.value);

	// 贷款期限（以月为单位）
	const loanTermMonths = parseInt(years.value) * 12;

	// 计算月还款额
	let monthlyPayment;
	if (loanMethod.value === 'benxi') {
		// 等额本息计算公式
		monthlyPayment = (loanAmountValue * monthlyInterestRate) / (1 - Math.pow(1 + monthlyInterestRate, -loanTermMonths));
	} else if (loanMethod.value === 'benjin') {
		// 等额本金计算公式
		monthlyPayment = loanAmountValue / loanTermMonths + loanAmountValue * monthlyInterestRate;
	}

	// 更新月还款的ref
	months.value = monthlyPayment.toFixed(2);
};
const getBuilding = async (val) => {
	let queryParams = {
		businessDistrictId: val,
	};
	const res = await getBuildingBusinessDistrictList(queryParams);
	buildingList.value = res.data.rows;
};
function handlePropertyclose() {
	drawerVisible.value = false;
	activeTabKey.value = 'buy';
	reset();
}
const handleTabClick = (item) => {
	activeTabKey.value = item.key;
	reset();
};
const handleChange = (val) => {
	if (val) {
		city.value = val[0];
		province.value = val[1];
		getBusiness();
	} else {
		city.value = '';
		province.value = '';
	}
	business.value = null;
	building.value = null;
	loanAmount.value = '';
	totalMoney.value = '';
};
// 根据城市获取商圈
const businessList = ref([]);
const getBusiness = async () => {
	let queryParams = {
		district: province.value,
		city: city.value,
	};
	const res = await getBusinessDistrictList(queryParams);
	businessList.value = res.data.rows;
};
const Searchbuild = (val) => {
	building.value = null;
	loanAmount.value = '';
	totalMoney.value = '';
	getBuilding(val);
};
const selectBuild = ref(null);
const handleItemChange = (val) => {
	selectBuild.value = val;
	getBuildingPrice(val, 1);
};
// 根据楼宇获取成交价格
const buildingPrice = ref('');
const getBuildingPrice = async (val, type) => {
	let queryParams = {
		buildingUniqueCode: val + '',
		dealType: activeTabKey.value == 'rent' ? 2 : 1,
		issueDateStart: formatDate(dateValue?.value[0]),
		issueDateEnd: formatDate(dateValue?.value[1]),
	};
	const res = await getBuildingSoldPrice(queryParams);
	console.log('🚀 ~ getBuildingPrice ~ res:', res);

	buildingPrice.value = res.data.dealPrice;

	// 如果存在面积,则根据面积计算总价
	if (loanAmount.value && type) {
		loanAmountInput(loanAmount.value);
	}
};
const baseRate = ref('');

async function getfinData() {
	const res = await financeDataList();
	baseRate.value = res.data.lpr;
}
const loanAmountInput = (value) => {
	if (buildingPrice.value != '') {
		totalMoney.value = (value * buildingPrice.value).toFixed(2);
	}
};
function onDateChange(dateString, date) {
	dateValue.value = date;
	if (dateValue.value && dateValue.value.length > 0 && selectBuild.value) {
		getBuildingPrice(selectBuild.value, 1);
	}
}
const formatDate = (date) => {
	if (!date) return '';
	const year = date.getFullYear();
	const month = (date.getMonth() + 1).toString().padStart(2, '0');
	const day = date.getDate().toString().padStart(2, '0');
	return `${year}-${month}-${day}`;
};
function reset() {
	positionValue.value = [];
	dateValue.value = [];
	tableData.value = [];
	totalMoney.value = '';
	buildingPrice.value = 0;
	business.value = null;
	businessList.value = [];
	building.value = null;
	loanAmount.value = '';
	totalYh.value = 0;
	months.value = 0;
	city.value = '';
	province.value = '';
	years.value = '';
	nextTick(() => {
		const e = {
			stopPropagation: () => {
				console.log('清除选中数据');
			},
		};
		// 级联选择框实例的handleClear方法需要传递一个时间对象阻止冒泡
		// 由于我们外部触发清空，所以不需要阻止冒泡
		// 但是需要传递给他一个包含阻止冒泡方法的参数
		cascaderDom.value?.handleClear(e);
	});
}
getfinData();
</script>
<style lang="scss">
.drwer_content {
	position: relative;
	background: #f7f8fa;
	overflow: unset;
	padding: 0px;
}
.arco-cascader-option-active {
	color: #1868f1 !important;
}
.arco-select-view-single,
.arco-input-wrapper {
	background: none !important;
	border: 1px solid #e5e6eb;
	border-radius: 4px;
}
.arco-select-view-single:hover,
.arco-input-wrapper:hover {
	border: 1px solid #1868f1;
}
.arco-picker {
	background-color: #fff;
	border: 1px solid #e5e6eb;
	border-radius: 4px;
}
.arco-picker-focused .arco-picker-input-active input,
.arco-picker-focused:hover .arco-picker-input-active input {
	background-color: #fff;
}
</style>
<style lang="less" scoped>
.propertyclose {
	position: absolute;
	top: 0px;
	left: -24px;
	img {
		cursor: pointer;
		width: 24px;
		height: 40px;
	}
}
.container_drwer {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	background: url(@/assets/img_drwer.png);
	background-size: cover;
	background-repeat: no-repeat;
	padding: 30px 16px 0px 16px;
	.mainTitle1 {
		width: 100%;
		height: 28px;
		font-weight: 500;
		font-size: 20px;
		line-height: 28px;
		display: flex;
		padding-bottom: 16px;
		justify-content: flex-start;
		align-items: center;
		color: #1d2129;
		&::before {
			content: '';
			width: 5px;
			height: 14px;
			background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
			border-radius: 10px;
			margin-right: 8px;
		}
	}
	.tab_box {
		width: 100%;
		box-sizing: border-box;
		font-size: 14px;
		font-weight: 600;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		background-color: transparent;
		.title {
			font-size: 16px;
			font-weight: 400;
			line-height: 24px;
			margin-right: 15px;
			color: #1a1a1a;
		}

		.tab {
			width: 216px;
			height: 48px;
			padding: 0 16px;
			display: flex;
			align-items: center;
			cursor: pointer;
			font-size: 16px;
			font-weight: 500;
			line-height: 24px;
			color: #4e5969;
			background-color: #f2f3f5;
			border-radius: 4px 4px 0 0;
			img {
				width: 16px;
				height: 16px;
				margin-right: 4px;
			}
		}

		.tabAct {
			font-size: 16px;
			color: #1868f1;
			background-color: #fff;
		}
	}
	.content_wrap {
		display: flex;
		flex-direction: column;
		gap: 16px;
		height: 785px;
		overflow: auto;
		.common_wrap {
			box-sizing: border-box;
			width: 100%;
			border-radius: 4px;
			background-color: #fff;
			padding: 20px 16px;
			.title {
				color: #1d2129;
				font-size: 16px;
				font-weight: 600;
				line-height: 24px;
				margin-bottom: 16px;
			}
			.row {
				display: flex;
				gap: 72px;
				align-items: center;
				.col {
					flex: 1;
					.label {
						color: #4e5969;
						margin-bottom: 8px;
						font-size: 14px;
						font-weight: 400;
						line-height: 22px;
					}
					.form_item {
					}
				}
			}
			.border_wrap {
				position: relative;
				display: flex;
				border: 1px solid #e5e6eb;
				border-radius: 4px;
				.left,
				.right {
					flex: 1;
				}
				.left {
					padding: 20px 0 20px 24px;
					.row {
						width: 440px;
						display: flex;
						gap: 40px;
						align-items: center;
						.col {
							flex: 1;
							.label {
								color: #4e5969;
								margin-bottom: 8px;
								font-size: 14px;
								font-weight: 400;
								line-height: 22px;
							}
							.form_item {
							}
						}
					}
				}
				.right {
					background: #f7f8fa;
					border-left: 1px solid #e5e6eb;
					display: flex;
					justify-content: center;
					flex-direction: column;
					.total_wrap {
						margin-left: 176px;
						color: #1d2129;
						font-size: 16px;
						line-height: 24px;
						font-weight: 500;
						display: flex;
						align-items: flex-end;
						min-width: 210px;
						margin-bottom: 12px;
						.total_money {
							margin-left: 4px;
							font-size: 32px;
							font-weight: 600;
							line-height: 32px;
						}
					}
					.loans_wrap,
					.total_yh,
					.loans_year {
						margin-left: 176px;
						display: flex;
						align-items: center;
						gap: 8px;
						margin-bottom: 8px;
						width: 210px;
						color: #4e5969;
						font-size: 14px;
						font-weight: 400;
						line-height: 22px;
						.circle {
							width: 8px;
							height: 8px;
							border-radius: 50%;
						}
					}
				}
				.btn_wrap {
					box-sizing: border-box;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-43px, -50%);
					width: 110px;
					height: 48px;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 5px;
					border: 1px solid #e5e6eb;
					background-color: #fff;
					// border-image: linear-gradient(to right, white 0%, white 50%, #e5e6eb 50%, #e5e6eb 100%) 1;
					.left_mask {
						background-color: #fff;
						width: 56px;
						height: 55px;
						position: absolute;
						top: -2px;
						left: -2px;
						box-sizing: border-box;
						z-index: 1;
					}
				}
			}
		}
	}
}
</style>
