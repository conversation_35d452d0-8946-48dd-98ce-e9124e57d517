<template>
	<el-dialog
		v-model="props.dialogInvoiceRowVisible"
		:close-on-click-modal="false"
		:show-close="false"
		align-center="center"
		:before-close="handleClose"
		style="width: 560px"
	>
		<template #header>
			<div class="dialogHeader">
				<div class="dialogHeaderLeft">
					<el-icon><ArrowLeftBold @click="handleClose" /></el-icon>
					<div>开具发票</div>
					<div class="details_text">共1个订单</div>
				</div>

				<div class="dialogHeaderRight">
					<el-icon><CloseBold @click="handleClose" /></el-icon>
				</div>
			</div>
		</template>
		<div class="form_content">
			<el-form label-position="left" label-width="96px" ref="formLabelAlignRefs" :model="formLabelAlign" class="demo_forminline">
				<el-form-item label="发票类型" prop="invoiceType">
					<el-radio-group v-model="formLabelAlign.invoiceType" @change="handleInvoiceType">
						<el-radio value="1" size="large" fill="#1868F1">电子-普通发票</el-radio>
						<el-radio value="2" size="large" fill="#1868F1" :disabled="formLabelAlign.headerType == 2 && formLabelAlign.invoiceType == 1"
							>电子-专用发票</el-radio
						>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="抬头类型" prop="headerType">
					<el-radio-group v-model="formLabelAlign.headerType">
						<el-radio value="1" size="large">企业单位</el-radio>
						<el-radio value="2" :disabled="formLabelAlign.invoiceType == 2" size="large">个人/非企业单位</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="发票抬头" class="invoice_name" prop="headerName">
					<el-select
						v-model="formLabelAlign.headerName"
						@change="loadMore"
						filterable
						@visible-change="handlevisibleChange($event)"
						placeholder="填写发票抬头（必填）"
						style="margin-right: 8px; width: 300px"
					>
						<el-option v-for="item in headeroptions" :key="item.headerName" :label="item.headerName" :value="item.id">
							<div class="actionSelect">
								<div class="actionName" :title="item.headerName">{{ item.headerName }}</div>
								<div class="actionBtn"><img v-if="item.defaultHeader === '1'" src="@/assets/invoiceBtn.png" alt="" /></div>
							</div>
						</el-option>
					</el-select>
					<el-button class="invoice_button" @click="handleInvoice">管理抬头</el-button>
				</el-form-item>
				<el-form-item label="税号" prop="taxNum">
					<el-input v-model="formLabelAlign.taxNum" @input="limitPhoneNum($event, 'taxNum')" maxlength="20" placeholder="填写税号（必填）" />
				</el-form-item>

				<el-form-item label="发票内容" prop="invoiceContent">
					<el-radio-group v-model="formLabelAlign.invoiceContent">
						<el-radio value="技术服务费" size="large">技术服务费</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="发票金额"> ￥{{ props.activeObj.payableAmount }} </el-form-item>
				<div class="invoice_text">发票金额为订单实付款金额，优惠、折扣等不在开票范围内</div>
				<el-form-item label="开户银行" prop="openBank">
					<el-input
						v-model="formLabelAlign.openBank"
						maxlength="20"
						:placeholder="formLabelAlign.invoiceType === '1' ? '填写开户银行（选填）' : '填写开户银行（必填）'"
					/>
				</el-form-item>
				<el-form-item label="银行账号" prop="bankAccount">
					<el-input
						v-model="formLabelAlign.bankAccount"
						@input="limitPhoneNum($event, 'bankAccount')"
						maxlength="21"
						:placeholder="formLabelAlign.invoiceType === '1' ? '填写银行账号（选填）' : '填写银行账号（必填）'"
					/>
				</el-form-item>
				<el-form-item label="注册地址" prop="address">
					<el-input
						v-model="formLabelAlign.address"
						maxlength="20"
						:placeholder="formLabelAlign.invoiceType === '1' ? '填写注册地址（选填）' : '填写注册地址（必填）'"
					/>
				</el-form-item>
				<el-form-item label="注册电话" prop="registerPhone">
					<el-input
						v-model="formLabelAlign.registerPhone"
						@input="limitPhoneNum($event, 'registerPhone')"
						maxlength="11"
						:placeholder="formLabelAlign.invoiceType === '1' ? '填写注册电话（选填）' : '填写注册电话（必填）'"
					/>
				</el-form-item>
				<el-form-item label="备注说明" prop="invoiceDesc">
					<el-input v-model="formLabelAlign.invoiceDesc" maxlength="50" placeholder="填写备注说明（选填）" />
				</el-form-item>

				<div class="invoice_Line"></div>
				<el-form-item label="接收邮箱" prop="receiveEmail">
					<el-input v-model="formLabelAlign.receiveEmail" maxlength="20" placeholder="填写接受邮箱（必填）" />
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog_footer">
				<div>
					<el-button @click="handleClose(1)"> 开具电子发票 </el-button>
				</div>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';
import { getInvoicePage, addInvoiceRecord } from '@/api/equityTerm.js';
import { ElMessage } from 'element-plus';
import { vuexStore } from '@/store/index.js';
const emit = defineEmits();
const props = defineProps({
	dialogInvoiceRowVisible: {
		type: Boolean,
		default: false,
	},
	activeObj: {
		type: Object,
		default: () => {},
	},
});
// 发票抬头
const headeroptions = ref([]);
// 发票表单
const formLabelAlign = ref({
	invoiceType: '1', // 发票类型
	headerType: '1', // 抬头类型
	headerName: '', // 发票抬头
	taxNum: '', // 税号
	invoiceContent: '技术服务费', // 发票内容
	openBank: '', // 开户银行
	bankAccount: '', // 银行账号
	address: '', // 注册地址
	registerPhone: '', // 注册电话
	invoiceDesc: '', //备注
	receiveEmail: '', //接收邮箱
});

const formLabelAlignRefs = ref();

const pageObj = ref({
	total: 0,
	page: {
		pageNo: 1,
		pageSize: 1000,
	},
});

// 关闭对话框
function handleClose(type) {
	if (type === 1) {
		// 必填提示
		if (!formLabelAlign.value.headerName) {
			ElMessage({
				message: '发票抬头不能为空',
				type: 'warning',
			});
			return;
		}
		if (!formLabelAlign.value.taxNum) {
			ElMessage({
				message: '税号不能为空',
				type: 'warning',
			});
			return;
		}
		if (!formLabelAlign.value.receiveEmail) {
			ElMessage({
				message: '接收邮箱不能为空',
				type: 'warning',
			});
			return;
		}
		if (formLabelAlign.value.invoiceType === '2') {
			if (!formLabelAlign.value.openBank) {
				ElMessage({
					message: '开户银行不能为空',
					type: 'warning',
				});
				return;
			}
			if (!formLabelAlign.value.bankAccount) {
				ElMessage({
					message: '银行账号不能为空',
					type: 'warning',
				});
				return;
			}
			if (!formLabelAlign.value.address) {
				ElMessage({
					message: '注册地址不能为空',
					type: 'warning',
				});
				return;
			}
			if (!formLabelAlign.value.registerPhone) {
				ElMessage({
					message: '注册电话不能为空',
					type: 'warning',
				});
				return;
			}
		}

		handleIssuance();
	}
	nextTick(() => {
		formLabelAlignRefs.value.resetFields();
	});
	emit('handleInvoiceClose');
}
// 确认开票
function handleIssuance() {
	addInvoiceRecord({
		userId: vuexStore.state.userInfo.id,
		userName: vuexStore.state.userInfo.userName,
		relateOrders: props.activeObj.outTradeNo,
		...formLabelAlign.value,
		invoiceMoney: props.activeObj.payableAmount, // 发票金额
	}).then((res) => {
		if (res.code === 200) {
			nextTick(() => {
				formLabelAlignRefs.value.resetFields();
			});
			emit('handleInvoiceClose');
		}
	});
}

// 限制
function limitPhoneNum(value, name) {
	formLabelAlign.value[name] = value.replace(/\D/g, '');
}

// 管理抬头
function handleInvoice() {
	emit('handleInvoice');
}

// 发票类型
function handleInvoiceType(val) {
	if (val == 2) {
		formLabelAlign.value.headerType = '1';
	}
}
// 加载更多
function loadMore(params) {
	headeroptions.value.forEach((element) => {
		if (element.id == params) {
			formLabelAlign.value.headerName = element.headerName; // 发票抬头
			formLabelAlign.value.taxNum = element.taxNum; // 税号
			formLabelAlign.value.openBank = element.openBank; // 开户银行
			formLabelAlign.value.bankAccount = element.bankAccount; // 银行账号
			formLabelAlign.value.address = element.address; // 注册地址
			formLabelAlign.value.registerPhone = element.registerPhone; // 注册电话
		}
	});
}
// 选择抬头
function handlevisibleChange(val) {
	if (val) {
		handlelookUpList();
	}
}
// 获取发票抬头
function handlelookUpList(type) {
	getInvoicePage({ ...pageObj.value.page, userId: props.activeObj.userId }).then((res) => {
		if (res.code === 200) {
			if (type) {
				headeroptions.value = [...headeroptions.value, ...res.result.records];
			} else {
				headeroptions.value = res.result.records;
			}
			pageObj.value.total = res.result.total;
		}
	});
}
</script>

<style lang="scss" scoped>
.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	color: rgba(29, 33, 41, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	.el-icon {
		cursor: pointer;
	}

	.dialogHeaderLeft {
		display: flex;
		align-items: center;
		.details_text {
			margin-left: 10px;
			font-size: 12px;
			color: #86909c;
		}
	}
}

.form_content {
	padding: 0 16px;
	.demo_forminline {
		margin: 0px 0px 0 16px;
		max-width: 484px;
		overflow-y: scroll;
		padding-right: 30px;
		height: 500px;
	}
	.el-form-item {
		align-items: center;
	}
	::v-deep .el-radio__input.is-checked .el-radio__inner {
		border-color: #1868f1;
		background: #1868f1;
	}

	::v-deep .el-radio-group .is-checked .el-radio__label {
		color: #606662;
	}

	.el-radio {
		--el-radio-input-border-color-hover: #1868f1;
	}
	.invoice_name > :nth-child(2) {
		display: flex;
		flex-wrap: nowrap;
	}
	.invoice_button {
		color: #1868f1;
		background: #edf4ff;
		font-size: 12px;
		width: 80px;
		border: 1px solid #e7e7e7;
	}

	.invoice_text {
		font-size: 10px;
		font-weight: 400;
		line-height: 18px;
		color: #86909c;
		margin: -18px 0 16px 0;
	}
	.invoice_Line {
		height: 1px;
		margin: 40px 0px;
		width: calc(100% - -31px);
		background: #e7e7e7;
	}
}

.dialog_footer {
	height: 74px;
	width: calc(100% - 16px);
	border-top: 1px solid rgba(231, 231, 231, 1);
	display: flex;
	align-items: center;
	justify-content: end;
	padding-right: 16px;
	button {
		height: 34px;
		width: 131px;
	}
}

.actionSelect {
	display: flex;
	align-items: center;
	justify-content: space-between;
	.actionName {
		width: 200px;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		margin-right: 10px;
	}
	.actionBtn {
		width: 26px;
		img {
			width: 26px;
		}
	}
}
</style>
