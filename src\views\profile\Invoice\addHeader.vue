<template>
	<el-dialog
		v-model="props.dialogInvoiceRowVisible"
		:close-on-click-modal="false"
		:show-close="false"
		align-center="center"
		:before-close="handleClose"
		style="width: 560px"
	>
		<template #header>
			<div class="dialogHeader">
				<div class="dialogHeaderLeft">
					<el-icon><ArrowLeftBold @click="handleClose" /></el-icon>
					<div>{{ props.headerEdit }}抬头</div>
				</div>

				<div class="dialogHeaderRight">
					<el-icon><CloseBold @click="handleClose" /></el-icon>
				</div>
			</div>
		</template>
		<div class="form_content">
			<el-form label-position="left" label-width="96px" ref="formLabelAlignRef" :model="formLabelAlign" class="demo_forminline">
				<el-form-item label="抬头类型" prop="headerType">
					<el-radio-group v-model="formLabelAlign.headerType" @change="handleSearch">
						<el-radio value="1" size="large" fill="#1868F1">企业单位</el-radio>
						<el-radio value="2" size="large" fill="#1868F1">个人/非企业单位</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="发票抬头" prop="headerName">
					<el-input v-model="formLabelAlign.headerName" maxlength="20" placeholder="填写发票抬头（必填）" />
				</el-form-item>

				<el-form-item label="税号" prop="taxNum" v-if="formLabelAlign.headerType == 1">
					<el-input v-model="formLabelAlign.taxNum" @input="limitPhoneNum($event, 'taxNum')" maxlength="20" placeholder="填写税号(必填)" />
				</el-form-item>
				<el-form-item label="开户银行" prop="openBank" v-if="formLabelAlign.headerType == 1">
					<el-input v-model="formLabelAlign.openBank" maxlength="20" placeholder="填写开户银行（选填）" />
				</el-form-item>
				<el-form-item label="银行账号" prop="bankAccount" v-if="formLabelAlign.headerType == 1">
					<el-input
						v-model.number="formLabelAlign.bankAccount"
						@input="limitPhoneNum($event, 'bankAccount')"
						maxlength="21"
						placeholder="填写银行账号（选填）"
					/>
				</el-form-item>
				<el-form-item label="注册地址" prop="address" v-if="formLabelAlign.headerType == 1">
					<el-input v-model="formLabelAlign.address" maxlength="20" placeholder="填写注册地址（选填）" />
				</el-form-item>
				<el-form-item label="注册电话" prop="registerPhone" v-if="formLabelAlign.headerType == 1">
					<el-input
						v-model="formLabelAlign.registerPhone"
						@input="limitPhoneNum($event, 'registerPhone')"
						maxlength="11"
						placeholder="填写注册电话（选填）"
					/>
				</el-form-item>
				<el-form-item label="设为默认抬头" prop="defaultHeader">
					<el-switch v-model="formLabelAlign.defaultHeader" style="--el-switch-on-color: #035dff" />
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<div class="dialog_footer">
				<div>
					<el-button @click="handleClose(1)" color="#1868F1"> 确定 </el-button>
				</div>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { nextTick, ref, watch } from 'vue';
import { addInvoice, getInvoiceById, editInvoice } from '@/api/equityTerm.js';
import { ElMessage } from 'element-plus';

const emit = defineEmits();
const props = defineProps({
	dialogInvoiceRowVisible: {
		type: Boolean,
		default: false,
	},
	activeObj: {
		type: Object,
		default: () => {},
	},
	headerEdit: {
		type: String,
		default: '',
	},
	headerEditId: {
		type: String,
		default: '',
	},
});

const formLabelAlign = ref({
	address: '',
	bankAccount: null,
	defaultHeader: true,
	headerName: '',
	headerType: '1',
	openBank: '',
	registerPhone: '',
	taxNum: '',
});

const formLabelAlignRef = ref();

watch(
	() => props.headerEditId,
	() => {
		if (props.headerEditId) {
			handlegetInvoiceById();
		}
	}
);
// 获取发票详情
function handlegetInvoiceById() {
	getInvoiceById({ id: props.headerEditId }).then((res) => {
		if (res.code === 200) {
			formLabelAlign.value = res.result;
			formLabelAlign.value.defaultHeader = res.result.defaultHeader == '1';
		}
	});
}
// 关闭对话框
function handleClose(type) {
	if (props.headerEdit === '新增' && type === 1) {
		// 存在则为确定操作
		// 必填提示效验
		if (!formLabelAlign.value.headerName) {
			ElMessage({
				type: 'warning',
				message: '发票抬头不能为空',
			});
			return;
		}
		// 个人
		if (formLabelAlign.value.headerType == '1') {
			if (!formLabelAlign.value.taxNum) {
				ElMessage({
					type: 'warning',
					message: '税号不能为空',
				});
				return;
			}
		}

		handleAddHeader(type);
	} else if (props.headerEdit === '编辑') {
		handleEditHeader(type);
	} else {
		nextTick(() => {
			formLabelAlignRef.value.resetFields();
		});
		emit('handleInvoiceClose');
	}
}
// 限制
function limitPhoneNum(value, name) {
	formLabelAlign.value[name] = value.replace(/\D/g, '');
}

function handleSearch(val) {
	if (val === '2') {
		// 个人时清空不显示的字段
		formLabelAlign.value.taxNum = '';
		formLabelAlign.value.openBank = '';
		formLabelAlign.value.bankAccount = null;
		formLabelAlign.value.address = '';
		formLabelAlign.value.registerPhone = '';
	}
}
// 编辑
function handleEditHeader(type) {
	editInvoice({
		...formLabelAlign.value,
		defaultHeader: formLabelAlign.value.defaultHeader ? 1 : 0,
	}).then((res) => {
		formLabelAlignRef.value.resetFields();
		emit('handleInvoiceClose', type === 1);
	});
}

// 新增
function handleAddHeader(type) {
	addInvoice({
		userId: props.activeObj.userId,
		...formLabelAlign.value,
		defaultHeader: formLabelAlign.value.defaultHeader ? 1 : 0,
	}).then((res) => {
		formLabelAlignRef.value.resetFields();
		emit('handleInvoiceClose', type === 1);
	});
}
</script>

<style lang="scss" scoped>
.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	color: rgba(29, 33, 41, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	.el-icon {
		cursor: pointer;
	}

	.dialogHeaderLeft {
		display: flex;
		align-items: center;
		.details_text {
			margin-left: 10px;
			font-size: 12px;
			color: #86909c;
		}
	}
}

.form_content {
	padding: 0 16px;
	margin-bottom: -16px;
	.demo_forminline {
		margin: 0px 0px 0 16px;
		max-width: 484px;
		padding-right: 30px;
	}
	.el-form-item {
		align-items: center;
	}
	::v-deep .el-radio__input.is-checked .el-radio__inner {
		border-color: #1868f1;
		background: #1868f1;
	}

	::v-deep .el-radio-group .is-checked .el-radio__label {
		color: #606662;
	}

	.el-radio {
		--el-radio-input-border-color-hover: #1868f1;
	}
	.invoice_name > :nth-child(2) {
		display: flex;
		flex-wrap: nowrap;
	}
	.invoice_button {
		color: #1868f1;
		background: #edf4ff;
		font-size: 12px;
		width: 80px;
		border: 1px solid #e7e7e7;
	}

	.invoice_text {
		font-size: 10px;
		font-weight: 400;
		line-height: 18px;
		color: #86909c;
		margin: -18px 0 16px 0;
	}
	.invoice_Line {
		height: 1px;
		margin: 40px 0px;
		width: calc(100% - -31px);
		background: #e7e7e7;
	}
}

.dialog_footer {
	height: 74px;
	width: calc(100% - 16px);
	border-top: 1px solid rgba(231, 231, 231, 1);
	display: flex;
	align-items: center;
	justify-content: end;
	padding-right: 16px;
	button {
		height: 34px;
		width: 131px;
	}
}
</style>
