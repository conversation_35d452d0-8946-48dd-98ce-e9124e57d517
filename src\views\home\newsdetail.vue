<template>
	<div class="body_box">
		<div class="newsdetail_box">
			<div>
				<img class="main" :src="`${proxyAddress}${newsdetail.displayImg}`" alt="" />
				<div class="content_box" v-html="newsdetail.content"></div>
			</div>

			<div class="right_box">
				<div class="guanggao_main">
					<ads />
				</div>
				<!-- 相关推荐 -->
				<div class="recommend">
					<div class="recommend_title">相关推荐</div>
					<div class="recommend_list" v-for="(item, index) in newsLists" :key="index" @click="goNewsdetail(item.id)">{{ item.articleTitle }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { onMounted, ref, toRaw, watch } from 'vue';
import ads from './ads.vue';
const proxyAddress = ref('https://static.biaobiaozhun.com/');
import { ArticleReferral, ArticleDetail } from '../../../src/api/home.js';
import { useRouter } from 'vue-router';
import { AdvertList } from '../../../src/api/home.js';
// 新闻信息
const newsdetail = ref([]);
const router = useRouter();
const route = useRoute();
onMounted(() => {
	getnewsdetail();
	getAdvertList();
});
// 获取新闻信息
const getnewsdetail = async () => {
	const res = await ArticleDetail({ articleId: route.query.id })
		.then((res) => {
			console.log(res, 'cg');
			newsdetail.value = res.data;
			getNewsList(res.data.articleType);
		})
		.catch((err) => {
			console.log(err, 'shibai');
		});
};
// 获取广告位
const AdvertLists = ref([]);
const getAdvertList = async () => {
	let params = {
		// articleType: articleType,
		pageNo: 1,
		pageSize: 10,
	};
	await AdvertList(params)
		.then((res) => {
			console.log('广告', res);
			AdvertLists.value = res.data.rows;
			console.log(AdvertLists.value, 88812);
		})
		.catch((err) => {
			console.log('请求失败！');
		});
};
const goUrl = (url) => {
	console.log(url, 2323);
	router.push({ path: '/advertising', query: { id: url } });
};
//获取新闻列表
const newsLists = ref([]);
const getNewsList = async (articleType) => {
	console.log(route.query, 325423);
	let params = {
		articleType: articleType,
		area: route.query.area,
		articleId: route.query.id,
		pageNo: 1,
		pageSize: 10,
	};
	await ArticleReferral(params)
		.then((res) => {
			console.log('新闻', res);
			newsLists.value = res.data.rows;
		})
		.catch((err) => {
			console.log('请求失败！');
		});
};
const goNewsdetail = (id) => {
	console.log(id);
	router.push({ path: '/newsdetail', query: { id: id } });
};
// 监听
watch(
	() => route.query.id,
	(newVal, oldVal) => {
		getnewsdetail();
	}
);
</script>

<style lang="less">
.body_box {
	width: 100%;
	max-width: 1440px;
	margin: 0 auto;
}
.newsdetail_box {
	display: flex;
	padding: 50px !important;
	margin-left: 10px !important;
	.main {
		width: 90%;
	}
	/* margin-top: 40px !important; */
}
.title_box {
	text-align: center;
}
.content_box {
	width: 70%;
}
.right_box {
	.guanggao_main {
		width: 100%;
		// height: 115px;

		.guanggao_img {
			// height: 115px;
			max-width: 307px;
			margin-bottom: 10px;
			// height: auto;

			img {
				width: 100%;
				// height: 115px;
			}
		}
	}
	.recommend {
		width: 100%;
		height: 100%;
		color: rgb(0, 0, 0);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;

		letter-spacing: 0px;
		text-align: left;
		margin-top: 30px;

		.recommend_title {
			width: 100%;
			height: 40px;
			background: rgb(241, 241, 241);
			line-height: 40px;
			padding: 0 14px;
			box-sizing: border-box;
		}
		.recommend_list {
			width: 100%;
			padding: 0 14px;
			height: 53px;
			box-sizing: border-box;
			border-bottom: 1px solid rgb(203, 202, 202);
			background: rgb(255, 255, 255);
			line-height: 53px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
}
.content_box {
	p {
		img {
			width: 100% !important;
		}
	}
}
@media screen and (max-width: 991px) {
	.newsdetail_box {
		flex-direction: column;
		.main {
			width: 100%;
		}
		.content_box {
			width: 100%;
		}
	}
	.right_box {
		width: 100%;
		margin: 0;
		.guanggao_main {
			div {
				width: 100%;
				.guanggao_img {
					width: 100%;
					max-width: 100%;
					margin: 0;
				}
			}
		}
	}
}
</style>
