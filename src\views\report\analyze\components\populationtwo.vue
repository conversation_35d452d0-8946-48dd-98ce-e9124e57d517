<template>
	<div>
		<div class="oneTit">3.2街道人口</div>
		<table class="MsoTableGrid" align="center" style="width: 100%; margin-bottom: 20px" v-for="(item, index) in populationtwo" :key="index">
			<tbody>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>社区概况</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>人口总数</td>
					<td valign="center" colspan="7" class>人口增长</td>
					<td valign="center" colspan="6" class>平均HH大小</td>
					<td valign="center" colspan="7" class>多样性指数</td>
					<td valign="center" colspan="6" class>中位年龄</td>
					<td valign="center" colspan="7" class>家庭收入中位数</td>
					<td valign="center" colspan="4" class>房屋中值</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>{{ item.populationBasic.populationTotal }}</td>
					<td valign="center" colspan="7" class>{{ item.populationBasic.populationGrowth }}%</td>
					<td valign="center" colspan="6" class>{{ item.populationBasic.avgHhSize }}</td>
					<td valign="center" colspan="7" class>{{ item.populationBasic.diversityIndex }}</td>
					<td valign="center" colspan="6" class>{{ item.populationBasic.ageMid }}</td>
					<td valign="center" colspan="7" class>${{ item.populationBasic.householdIncomeMid }}</td>
					<td valign="center" colspan="4" class>${{ item.populationBasic.houseMidValue }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>中值净值</td>
					<td valign="center" colspan="7" class>18岁以下</td>
					<td valign="center" colspan="6" class>18-65岁</td>
					<td valign="center" colspan="7" class>66岁+</td>
					<td valign="center" colspan="6" class>公共事业</td>
					<td valign="center" colspan="7" class>蓝领</td>
					<td valign="center" colspan="4" class>白领</td>
				</tr>
				<tr>
					<td valign="center" colspan="4" class>${{ item.populationBasic.medianNetValue }}</td>
					<td valign="center" colspan="7" class>{{ item.populationBasic.before18 }}%</td>
					<td valign="center" colspan="6" class>{{ item.populationBasic.between1865 }}%</td>
					<td valign="center" colspan="7" class>{{ item.populationBasic.after66 }}%</td>
					<td valign="center" colspan="6" class>{{ item.populationBasic.publicUtilities }}%</td>
					<td valign="center" colspan="7" class>{{ item.populationBasic.blueCollar }}%</td>
					<td valign="center" colspan="4" class>{{ item.populationBasic.whiteCollar }}%</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>抵押贷款占工资的百分比</td>
				</tr>
				<tr>
					<td valign="center" colspan="3" class>&lt;10</td>
					<td valign="center" colspan="5" class>10-14</td>
					<td valign="center" colspan="5" class>15-19</td>
					<td valign="center" colspan="5" class>20-24</td>
					<td valign="center" colspan="5" class>25-29</td>
					<td valign="center" colspan="4" class>30-34</td>
					<td valign="center" colspan="6" class>35-39</td>
					<td valign="center" colspan="5" class>40-49</td>
					<td valign="center" colspan="3" class>50+</td>
				</tr>
				<tr>
					<td valign="center" colspan="3" class>{{ item?.mortgagePercentage.mortgageProportionLt10 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.mortgagePercentage.mortgageProportionBetween1014 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.mortgagePercentage.mortgageProportionBetween1519 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.mortgagePercentage.mortgageProportionBetween2024 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.mortgagePercentage.mortgageProportionBetween2529 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.mortgagePercentage.mortgageProportionBetween3034 }}%</td>
					<td valign="center" colspan="6" class>{{ item?.mortgagePercentage.mortgageProportionBetween3539 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.mortgagePercentage.mortgageProportionBetween4049 }}%</td>
					<td valign="center" colspan="3" class>{{ item?.mortgagePercentage.mortgageProportionGt50 }}%</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>房屋价值</td>
				</tr>
				<tr>
					<td valign="center" colspan="2" class>&lt;$50000</td>
					<td valign="center" colspan="4" class>$100,000</td>
					<td valign="center" colspan="4" class>$150,000</td>
					<td valign="center" colspan="5" class>$200,000</td>
					<td valign="center" colspan="4" class>$250,000</td>
					<td valign="center" colspan="3" class>$300,000</td>
					<td valign="center" colspan="4" class>$400,000</td>
					<td valign="center" colspan="5" class>$550,000</td>
					<td valign="center" colspan="4" class>$750,000</td>
					<td valign="center" colspan="5" class>$800,000</td>
					<td valign="center" class>$1000000+</td>
				</tr>
				<tr>
					<td valign="center" colspan="2" class>{{ item?.houseValuation.houseValuationLt50000 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.houseValuation.houseValuation100000 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.houseValuation.houseValuation150000 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.houseValuation.houseValuation200000 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.houseValuation.houseValuation250000 }}%</td>
					<td valign="center" colspan="3" class>{{ item?.houseValuation.houseValuation300000 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.houseValuation.houseValuation400000 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.houseValuation.houseValuation550000 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.houseValuation.houseValuation750000 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.houseValuation.houseValuation900000 }}%</td>
					<td valign="center" class>{{ item?.houseValuation.houseValuationGt1000000 }}%</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>家庭收入</td>
				</tr>
				<tr>
					<td valign="center" colspan="3" class>&lt;$15000</td>
					<td valign="center" colspan="5" class>$25,000</td>
					<td valign="center" colspan="5" class>$35,000</td>
					<td valign="center" colspan="5" class>$65,000</td>
					<td valign="center" colspan="5" class>$75,000</td>
					<td valign="center" colspan="4" class>$110,000</td>
					<td valign="center" colspan="6" class>$150,000</td>
					<td valign="center" colspan="5" class>$175,000</td>
					<td valign="center" colspan="3" class>$200000+</td>
				</tr>
				<tr>
					<td valign="center" colspan="3" class>{{ item?.householdIncome.householdIncomeLt15000 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.householdIncome.householdIncome25000 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.householdIncome.householdIncome35000 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.householdIncome.householdIncome65000 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.householdIncome.householdIncome75000 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.householdIncome.householdIncome110000 }}%</td>
					<td valign="center" colspan="6" class>{{ item?.householdIncome.householdIncome150000 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.householdIncome.householdIncome175000 }}%</td>
					<td valign="center" colspan="3" class>{{ item?.householdIncome.householdIncomeGt200000 }}%</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>年龄简介：5年递增</td>
				</tr>
				<tr>
					<td valign="center" rowspan="4" class>{{ item?.buildingName }}所在社区各年龄段所占比例</td>
					<td valign="center" colspan="6" class>0-5岁</td>
					<td valign="center" colspan="5" class>6-10岁</td>
					<td valign="center" colspan="4" class>11-15岁</td>
					<td valign="center" colspan="4" class>16-20岁</td>
					<td valign="center" colspan="5" class>21-25岁</td>
					<td valign="center" colspan="4" class>26-30岁</td>
					<td valign="center" colspan="5" class>31-35岁</td>
					<td valign="center" colspan="5" class>36-40岁</td>
					<td valign="center" colspan="2" class>41-45岁</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>{{ item?.ageProfileRight.ageRightLt4 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileRight.ageRightBetween59 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileRight.ageRightBetween1014 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileRight.ageRightBetween1519 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileRight.ageRightBetween2024 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileRight.ageRightBetween2529 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileRight.ageRightBetween3034 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileRight.ageRightBetween3539 }}%</td>
					<td valign="center" colspan="2" class>{{ item?.ageProfileRight.ageRightBetween4044 }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>46-50岁</td>
					<td valign="center" colspan="5" class>51-55岁</td>
					<td valign="center" colspan="4" class>56-60岁</td>
					<td valign="center" colspan="4" class>61-65岁</td>
					<td valign="center" colspan="5" class>66-70岁</td>
					<td valign="center" colspan="4" class>71-75岁</td>
					<td valign="center" colspan="5" class>76-80岁</td>
					<td valign="center" colspan="5" class>81-85岁</td>
					<td valign="center" colspan="2" class>86-90岁</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>{{ item?.ageProfileRight.ageRightBetween4549 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileRight.ageRightBetween5054 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileRight.ageRightBetween5559 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileRight.ageRightBetween6064 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileRight.ageRightBetween6569 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileRight.ageRightBetween7074 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileRight.ageRightBetween7579 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileRight.ageRightBetween8084 }}%</td>
					<td valign="center" colspan="2" class>{{ item?.ageProfileRight.ageRightGt85 }}%</td>
				</tr>
				<tr>
					<td valign="center" rowspan="4" class>{{ item?.buildingName }}所在{{ item?.district }}各年龄段所占比例</td>
					<td valign="center" colspan="6" class>0-5岁</td>
					<td valign="center" colspan="5" class>6-10岁</td>
					<td valign="center" colspan="4" class>11-15岁</td>
					<td valign="center" colspan="4" class>16-20岁</td>
					<td valign="center" colspan="5" class>21-25岁</td>
					<td valign="center" colspan="4" class>26-30岁</td>
					<td valign="center" colspan="5" class>31-35岁</td>
					<td valign="center" colspan="5" class>36-40岁</td>
					<td valign="center" colspan="2" class>41-45岁</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>{{ item?.ageProfileLeft.ageLeftLt4 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileLeft.ageLeftBetween59 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileLeft.ageLeftBetween1014 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileLeft.ageLeftBetween1519 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileLeft.ageLeftBetween2024 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileLeft.ageLeftBetween2529 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileLeft.ageLeftBetween3034 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileLeft.ageLeftBetween3539 }}%</td>
					<td valign="center" colspan="2" class>{{ item?.ageProfileLeft.ageLeftBetween4044 }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>46-50岁</td>
					<td valign="center" colspan="5" class>51-55岁</td>
					<td valign="center" colspan="4" class>56-60岁</td>
					<td valign="center" colspan="4" class>61-65岁</td>
					<td valign="center" colspan="5" class>66-70岁</td>
					<td valign="center" colspan="4" class>71-75岁</td>
					<td valign="center" colspan="5" class>76-80岁</td>
					<td valign="center" colspan="5" class>81-85岁</td>
					<td valign="center" colspan="2" class>86-90岁</td>
				</tr>
				<tr>
					<td valign="center" colspan="6" class>{{ item?.ageProfileLeft.ageLeftBetween4549 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileLeft.ageLeftBetween5054 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileLeft.ageLeftBetween5559 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileLeft.ageLeftBetween6064 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileLeft.ageLeftBetween6569 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.ageProfileLeft.ageLeftBetween7074 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileLeft.ageLeftBetween7579 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.ageProfileLeft.ageLeftBetween8084 }}%</td>
					<td valign="center" colspan="2" class>{{ item?.ageProfileLeft.ageLeftGt85 }}%</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>居者有其屋</td>
				</tr>
				<tr>
					<td valign="center" colspan="14" class>主人</td>
					<td valign="center" colspan="14" class>出租人</td>
					<td valign="center" colspan="13" class>空缺的</td>
				</tr>

				<tr>
					<td valign="center" colspan="14" class>{{ item?.homeOwnershipStatusVo.owner }}%</td>
					<td valign="center" colspan="14" class>{{ item?.homeOwnershipStatusVo.lessor }}%</td>
					<td valign="center" colspan="13" class>{{ item?.homeOwnershipStatusVo.vacant }}%</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>住房：建造年份（省级）</td>
				</tr>
				<tr>
					<td valign="center" colspan="3" class>&lt;1949</td>
					<td valign="center" colspan="5" class>1949-59</td>
					<td valign="center" colspan="5" class>1960-69</td>
					<td valign="center" colspan="5" class>1970-79</td>
					<td valign="center" colspan="5" class>1980-89</td>
					<td valign="center" colspan="4" class>1990-99</td>
					<td valign="center" colspan="6" class>2000-09</td>
					<td valign="center" colspan="5" class>2010-14</td>
					<td valign="center" colspan="3" class>2015+</td>
				</tr>
				<tr>
					<td valign="center" colspan="3" class>{{ item?.buildingYear.before1949 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.buildingYear.between19491959 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.buildingYear.between19601969 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.buildingYear.between19701979 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.buildingYear.between19801989 }}%</td>
					<td valign="center" colspan="4" class>{{ item?.buildingYear.between19901999 }}%</td>
					<td valign="center" colspan="6" class>{{ item?.buildingYear.between20002009 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.buildingYear.between20102014 }}%</td>
					<td valign="center" colspan="3" class>{{ item?.buildingYear.after2015 }}%</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>教育程度</td>
				</tr>
				<tr>
					<td valign="center" colspan="9" class>无高中文凭</td>
					<td valign="center" colspan="12" class>高中毕业</td>
					<td valign="center" colspan="11" class>大专学历</td>
					<td valign="center" colspan="9" class>学士、硕士、博士</td>
				</tr>
				<tr>
					<td valign="center" colspan="9" class>{{ item?.educationalLevel.noHighSchoolDiploma }}%</td>
					<td valign="center" colspan="12" class>{{ item?.educationalLevel.highSchoolDiploma }}%</td>
					<td valign="center" colspan="11" class>{{ item?.educationalLevel.collegeDiploma }}%</td>
					<td valign="center" colspan="9" class>{{ item?.educationalLevel.bachelorMasterDoctorate }}%</td>
				</tr>
				<tr class="tab-tltle">
					<td valign="top" colspan="41" class>通勤时间：分钟</td>
				</tr>
				<tr>
					<td valign="center" colspan="5" class>&lt;5</td>
					<td valign="center" colspan="9" class>5-9</td>
					<td valign="center" colspan="7" class>10-14</td>
					<td valign="center" colspan="7" class>15-19</td>
					<td valign="center" colspan="8" class>20-24</td>
					<td valign="center" colspan="5" class>25-29</td>
				</tr>
				<tr>
					<td valign="center" colspan="5" class>{{ item?.commutingTime.commuteTimeLt5 }}%</td>
					<td valign="center" colspan="9" class>{{ item?.commutingTime.commuteTimeBetween59 }}%</td>
					<td valign="center" colspan="7" class>{{ item?.commutingTime.commuteTimeBetween1014 }}%</td>
					<td valign="center" colspan="7" class>{{ item?.commutingTime.commuteTimeBetween1519 }}%</td>
					<td valign="center" colspan="8" class>{{ item?.commutingTime.commuteTimeBetween2024 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.commutingTime.commuteTimeBetween2529 }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="5" class>30-34</td>
					<td valign="center" colspan="9" class>35-39</td>
					<td valign="center" colspan="7" class>40-44</td>
					<td valign="center" colspan="7" class>45-59</td>
					<td valign="center" colspan="8" class>60-89</td>
					<td valign="center" colspan="5" class>90+</td>
				</tr>
				<tr>
					<td valign="center" colspan="5" class>{{ item?.commutingTime.commuteTimeBetween3034 }}%</td>
					<td valign="center" colspan="9" class>{{ item?.commutingTime.commuteTimeBetween3539 }}%</td>
					<td valign="center" colspan="7" class>{{ item?.commutingTime.commuteTimeBetween4044 }}%</td>
					<td valign="center" colspan="7" class>{{ item?.commutingTime.commuteTimeBetween4559 }}%</td>
					<td valign="center" colspan="8" class>{{ item?.commutingTime.commuteTimeBetween6089 }}%</td>
					<td valign="center" colspan="5" class>{{ item?.commutingTime.commuteTimeGt90 }}%</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>
<script setup>
import { perponStreet } from '@/api/baogao';
import { ref, onMounted } from 'vue';
const props = defineProps({
	id: {
		type: String,
		default: null,
		required: true,
	},
});
const populationtwo = ref([]);
const getdata = async () => {
	const res = await perponStreet({
		buildingId: props.id,
	});
	if (res.code === 200) {
		populationtwo.value = res.result;
	}
};
onMounted(() => {
	getdata();
});
</script>

<style lang="less" scoped>
table {
	border-top: 1px solid #333;
	border-left: 1px solid #333;
	border-spacing: 0;
	background-color: #fff;
	width: 100%;
	td {
		text-align: center;
		border-bottom: 1px solid #333;
		border-right: 1px solid #333;
		font-size: 13px;
		padding: 10px;
	}
	.tab-tltle {
		td {
			text-align: left;
			font-weight: 700;
			font-size: 15px;
		}
	}
	.tab-tltle {
		font-weight: 700;
		font-size: 15px;
	}
}
.oneTit {
	margin-top: 30px;
	font-size: 24px;
	font-weight: 700;
}
</style>
