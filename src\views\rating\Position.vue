<template>
	<div class="bigbody">
		<el-row :gutter="20">
			<el-col :span="12">
				<div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					城市介绍>>
					<el-divider class="divider" />
				</el-text>
				<el-text class="mx-1 basic" tag="p">{{ basic.city_introduce }}</el-text>
				<el-text tag="p" class="Title" style="margin-top: 36px">
					城市指标>>
					<el-divider class="divider" />
				</el-text>
				<el-text class="miniTitle" size="samll" tag="p">工业企业(本表格数据及排名摘自2020年统计数据)</el-text>
				<el-table :data="cityInfo[0]" stripe style="width: 100%">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
				<el-text class="miniTitle" size="samll" tag="p">水资源及环境</el-text>
				<el-table :data="cityInfo[1]" stripe style="width: 100%">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
				<el-text class="miniTitle" size="samll" tag="p">生产总值</el-text>
				<el-table :data="cityInfo[2]" stripe style="width: 100%">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
				<el-text class="miniTitle" size="samll" tag="p">能源</el-text>
				<el-table :data="cityInfo[3]" stripe style="width: 100%">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
				<el-text class="miniTitle" size="samll" tag="p">交通及运输</el-text>
				<el-table :data="cityInfo[4]" stripe style="width: 100%">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>

				<el-row
					><el-col :span="14"><el-text class="miniTitle" size="samll" tag="p">房地产</el-text></el-col
					><el-col :span="10"> <el-text class="miniTitle" size="samll" tag="p" type="warning">消费品零售批发</el-text></el-col></el-row
				>

				<el-table :data="cityInfo[5]" stripe style="width: 100%" :cell-style="changeColor">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
				<el-row
					><el-col :span="17"><el-text class="miniTitle" size="samll" tag="p">学校</el-text></el-col
					><el-col :span="7"> <el-text class="miniTitle" size="samll" tag="p" type="success">对外经济贸易</el-text></el-col></el-row
				>

				<el-table :data="cityInfo[6]" stripe style="width: 100%" :cell-style="changeColorSchool">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
				<el-row
					><el-col :span="17"><el-text class="miniTitle" size="samll" tag="p">土地</el-text></el-col
					><el-col :span="7"> <el-text class="miniTitle" size="samll" tag="p" type="success">文化及公共事业</el-text></el-col></el-row
				>

				<el-table :data="cityInfo[7]" stripe style="width: 100%" :cell-style="changeColorSchool">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
				<el-row
					><el-col :span="20"><el-text class="miniTitle" size="samll" tag="p">医院及社会招聘</el-text></el-col
					><el-col :span="4"> <el-text class="miniTitle" size="samll" tag="p" style="color: #8faadc">邮局及电信</el-text></el-col></el-row
				>

				<el-table :data="cityInfo[8]" stripe style="width: 100%" :cell-style="changeColorHos">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
				<el-row
					><el-col :span="20"><el-text class="miniTitle" size="samll" tag="p">财政及金融</el-text></el-col
					><el-col :span="4"> <el-text class="miniTitle" size="samll" tag="p" style="color: #8faadc">科技创新情况</el-text></el-col></el-row
				>

				<el-table :data="cityInfo[9]" stripe style="width: 100%" :cell-style="changeColorHos">
					<el-table-column v-for="column in cloumns" :key="column.prop" :prop="column.prop" />
				</el-table>
			</el-col>
			<el-col :span="12">
				<div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					商圈介绍>>
					<el-divider class="divider" />
				</el-text>
				<el-text class="mx-1 basic" tag="p">{{ basic.business_reference }}</el-text>
				<div class="grid-content ep-bg-purple" />
				<el-text tag="p" class="Title">
					商圈评估表>>
					<el-divider class="divider" />
				</el-text>
				<el-descriptions class="tablel" :column="1" border>
					<el-descriptions-item label="商业设施齐全程度" label-align="left" align="center"> {{ busCir.fully_equipped }} </el-descriptions-item>
					<el-descriptions-item label="商业覆盖度" label-align="left" align="center"> {{ busCir.commercial_coverage }} </el-descriptions-item>
					<el-descriptions-item label="商业氛围" label-align="left" align="center"> {{ busCir.business_climate }} </el-descriptions-item>
					<el-descriptions-item label="交通网络" label-align="left" align="center"> {{ busCir.traffic_network }} </el-descriptions-item>
					<el-descriptions-item label="交通拥堵情况" label-align="left" align="center"> {{ busCir.traffic_jam }} </el-descriptions-item>
					<el-descriptions-item label="人流量" label-align="left" align="center"> {{ busCir.visitor_flow_rate }} </el-descriptions-item>
					<el-descriptions-item label="消费人群特征" label-align="left" align="center"> {{ busCir.consumer_group }} </el-descriptions-item>
					<el-descriptions-item label="配套设施" label-align="left" align="center"> {{ busCir.supporting_facilities }} </el-descriptions-item>
					<el-descriptions-item label="商圈管理" label-align="left" align="center"> {{ busCir.business_manage }} </el-descriptions-item>
					<el-descriptions-item label="竞争品牌数量" label-align="left" align="center"> {{ busCir.competing_brand }} </el-descriptions-item>
					<el-descriptions-item label="竞争形式" label-align="left" align="center"> {{ busCir.competition_from }} </el-descriptions-item>
					<el-descriptions-item label="总分" label-align="left" align="center"> {{ busCir.total_points }} </el-descriptions-item>
					<el-descriptions-item label="商圈亮点" label-align="left" align="center"> {{ busCir.bright_spot }} </el-descriptions-item>
					<el-descriptions-item label="商圈不足" label-align="left" align="center"> {{ busCir.insufficient }} </el-descriptions-item>
					<el-descriptions-item label="商圈评价" label-align="left" align="center"> {{ busCir.evaluate }} </el-descriptions-item>
				</el-descriptions>
			</el-col>
		</el-row>
		<el-row :gutter="20">
			<el-col :span="12">
				<div class="grid-content ep-bg-purple" />
				<!-- <el-text class="mx-1 title" size="large" tag="p">商圈评估表</el-text> -->
				<!-- <div class="top">
					<el-text class="mx-1 basic" tag="p"></el-text>
					<el-text class="mx-1 basic" tag="p"></el-text>
				</div> -->
				<!-- <el-descriptions class="tablel" title="商圈评估表" :column="1" border>
					<el-descriptions-item label="商业设施齐全程度" label-align="left" align="center"> {{ busCir.fully_equipped }} </el-descriptions-item>
					<el-descriptions-item label="商业覆盖度" label-align="left" align="center"> {{ busCir.commercial_coverage }} </el-descriptions-item>
					<el-descriptions-item label="商业氛围" label-align="left" align="center"> {{ busCir.business_climate }} </el-descriptions-item>
					<el-descriptions-item label="交通网络" label-align="left" align="center"> {{ busCir.traffic_network }} </el-descriptions-item>
					<el-descriptions-item label="交通拥堵情况" label-align="left" align="center"> {{ busCir.traffic_jam }} </el-descriptions-item>
					<el-descriptions-item label="人流量" label-align="left" align="center"> {{ busCir.visitor_flow_rate }} </el-descriptions-item>
					<el-descriptions-item label="消费人群特征" label-align="left" align="center"> {{ busCir.consumer_group }} </el-descriptions-item>
					<el-descriptions-item label="配套设施" label-align="left" align="center"> {{ busCir.supporting_facilities }} </el-descriptions-item>
					<el-descriptions-item label="商圈管理" label-align="left" align="center"> {{ busCir.business_manage }} </el-descriptions-item>
					<el-descriptions-item label="竞争品牌数量" label-align="left" align="center"> {{ busCir.competing_brand }} </el-descriptions-item>
					<el-descriptions-item label="竞争形式" label-align="left" align="center"> {{ busCir.competition_from }} </el-descriptions-item>
					<el-descriptions-item label="总分" label-align="left" align="center"> {{ busCir.total_points }} </el-descriptions-item>
					<el-descriptions-item label="商圈亮点" label-align="left" align="center"> {{ busCir.bright_spot }} </el-descriptions-item>
					<el-descriptions-item label="商圈不足" label-align="left" align="center"> {{ busCir.insufficient }} </el-descriptions-item>
					<el-descriptions-item label="商圈评价" label-align="left" align="center"> {{ busCir.evaluate }} </el-descriptions-item>
				</el-descriptions> -->
			</el-col>
		</el-row>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http'; // 验证路径是否正确
import { useRoute } from 'vue-router';

const route = useRoute();
const basic = ref({});
const busCir = ref({});
const cityInfo = ref([]);
const cloumns = [
	{
		prop: 'category1',
	},
	{
		prop: 'category2',
	},
	{
		prop: 'category3',
	},
	{
		prop: 'category4',
	},
	{
		prop: 'category5',
	},
	{
		prop: 'category6',
	},
	{
		prop: 'category7',
	},
];
const changeColor = ({ row, column, rowIndex, columnIndex }) => {
	if (columnIndex == 5 || columnIndex == 4 || columnIndex == 6) {
		return { background: '#FFF8DC' };
	}

	return {};
};
const changeColorSchool = ({ row, column, rowIndex, columnIndex }) => {
	if (columnIndex == 5 || columnIndex == 6) {
		return { background: '#a9d18e' };
	}
	return {};
};
const changeColorHos = ({ row, column, rowIndex, columnIndex }) => {
	if (columnIndex == 6) {
		return { background: '#8faaDC' };
	}
	return {};
};
onMounted(async () => {
	try {
		const res = await http.get('/api/a_maps.do?act=ratingPassDetailsAction', {
			params: { maps_id: route.params.id, action: 'location' },
		});
		if (res.list.length > 0) {
			basic.value = res.list[0];
			busCir.value = res.list[1];
			cityInfo.value = res.list[2];
		}
	
	} catch (error) {
		console.log(error, 'error');
	}
});
</script>

<style lang="less" scoped>
.tablel :deep(.el-descriptions__label) {
	width: 150px;
}
.tablel :deep(.el-descriptions__cell) {
	font-size: 12px;
}
.tablel :deep(.el-descriptions__header) {
	padding-bottom: 10px;
	border-bottom: 1px solid #f1f1f1;
	font-weight: 700;
	font-size: 12px;
	width: 50%;
	.el-descriptions__title {
		color: #000;
	}
}
.miniTitle {
	padding-top: 15px;
	text-align: center;
	font-size: 14px;
	font-weight: 500;
}
.title {
	padding-bottom: 10px;
	border-bottom: 1px solid #f1f1f1;
	font-weight: 700;
	width: 50%;
}
.basic {
	padding-top: 10px;
	line-height: 1.5;
}
.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
}
.divider {
	flex-grow: 1;
	margin: 16px 0 16px 8px;
	border-color: #3483ce;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 36px;
}
</style>
