<template>
	<div class="comparison_box">
		<div class="container_box">
			<div class="table_main">
				<div class="table_">
					<div class="top_boxFirst">
						<div class="tag_boxTitle">对比资产一</div>
						<div class="tag_boxCenter">
							<div class="tag_boxRight" v-if="tableDatao[0]" @click="clearDate(tableDatao[0], 1)">× 清空</div>
							<!-- <div class="tag_boxLeft" v-if="tableDatao.length > 0" @click="handleDownload(tableDatao[0].id, 'security')">
								<el-icon style="margin-right: 2px"><Download /></el-icon>证券化报告
							</div> -->
						</div>
					</div>
					<div class="table_1" v-if="tableDatao[0]">
						<el-table border :data="tableDatao" height="80px" style="width: 100%">
							<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
							<el-table-column prop="buildingType" label="资产类型" width="" />
							<el-table-column prop="street" label="地址" show-overflow-tooltip>
								<template #default="scope">
									<!-- {{ scope.row.province ? scope.row.province : '' }} -->
									{{ scope.row?.city ? scope.row.city : '' }}
									{{ scope.row?.district ? scope.row.district : '' }}
									{{ scope.row?.street ? scope.row.street : '' }}
								</template>
							</el-table-column>
							<el-table-column prop="buildingSize" label="建筑面积" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
									</div>
								</template>
							</el-table-column>
							<el-table-column prop="maintenance" label="维护情况" width="" />
							<el-table-column prop="absoluteValue" label="单价" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="add active" @click="choose(1)" v-else>+ 选择对比资产</div>
				</div>
				<div class="table_">
					<div class="top_box">
						<div class="top_boxFirst">
							<div class="tag_boxTitle">对比资产二</div>
							<div class="tag_boxCenter">
								<div class="tag_boxRight" v-if="tableDatat[0]" @click="clearDate(tableDatat[0], 2)">× 清空</div>
								<!-- <div class="tag_boxLeft" v-if="tableDatat.length > 0" @click="handleDownload(tableDatat[0].id, 'security')">
									<el-icon style="margin-right: 2px"><Download /></el-icon>证券化报告
								</div> -->
							</div>
						</div>
						<div class="table_1" v-if="tableDatat[0]">
							<el-table :data="tableDatat" border height="80px" style="width: 100%">
								<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
								<el-table-column prop="buildingType" label="资产类型" width="" />
								<el-table-column prop="street" label="地址" show-overflow-tooltip>
									<template #default="scope">
										<!-- {{ scope.row.province ? scope.row.province : '' }} -->
										{{ scope.row?.city ? scope.row.city : '' }}
										{{ scope.row?.district ? scope.row.district : '' }}
										{{ scope.row?.street ? scope.row.street : '' }}
									</template>
								</el-table-column>
								<el-table-column prop="buildingSize" label="建筑面积" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="maintenance" label="维护情况" width="" />
								<el-table-column prop="absoluteValue" label="单价" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="add active" @click="choose(2)" v-else>+ 选择对比资产</div>
					</div>
				</div>
			</div>

			<!-- 对比图 -->
			<div class="echars_box">
				<div class="tag_box">估值结果</div>
				<div class="echars_main">
					<div
						class="box_"
						:style="{ width: sixRingDateList.length > 1 ? 'calc(50% - 8px)' : '100%' }"
						v-for="(item, index) in sixRingDateList"
						:key="index"
					>
						<div class="title1">{{ item.businessDistrictName }}</div>
						<div class="box_Contents">
							<div class="box_Content" v-for="(items, index) in 2" :key="index">
								<div class="big_title" v-if="index === 0">{{ item?.agg?.appraise?.evaluateValue }}亿元</div>
								<div class="big_title" v-if="index === 1" style="color: #333">
									{{ item?.agg?.appraise?.comparableValueRank }} / {{ item?.agg?.appraise?.comparableValueRankTotal }}
								</div>
								<div class="big_detail" v-if="index === 0">评估值</div>
								<div class="big_detail" v-if="index === 1">排行</div>
							</div>
						</div>

						<div class="img_box">
							<div style="width: -webkit-fill-available" class="btn_boxDetails">
								{{ handleValuationRanking(item) }}
							</div>
							<div class="box_copyContent" v-if="handleValuationRanking(item).length > 0">
								<div @click="handlerCopy(handleValuationRanking(item))">复制</div>
							</div>
						</div>
					</div>
				</div>
				<div class="echars_main">
					<div
						class="box_"
						:style="{ width: sixRingDateList.length > 1 ? 'calc(50% - 8px)' : '100%' }"
						v-for="(item, index) in sixRingDateList"
						:key="index"
					>
						<div class="box_left">
							<div class="boxTitle">评估对象</div>
							<div class="img_box">
								<div style="width: -webkit-fill-available" class="btn_boxDetails">
									{{ item?.agg?.appraise?.evaluateObjects }}
								</div>
								<div class="box_copyContent" v-if="item?.agg?.appraise?.evaluateObjects">
									<div @click="handlerCopy(item?.agg?.appraise?.evaluateObjects)">复制</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="echars_main">
					<div
						class="box_"
						v-for="(item, index) in sixRingDateList"
						:key="index"
						:style="{ width: sixRingDateList.length > 1 ? 'calc(50% - 8px)' : '100%' }"
					>
						<div class="box_left">
							<div class="boxTitle">
								收益法
								<div class="line_box">|</div>
								<span style="font-size: 12px; color: #1868f1; margin-left: 5px; font-weight: 700"
									>{{ $utils.handleNumber(item?.agg?.appraise?.incomeApproach) }}
								</span>
							</div>
							<div class="img_box">
								<div style="width: -webkit-fill-available" class="btn_boxDetails">
									{{ handleApproach(item) }}
								</div>
								<div class="box_copyContent" v-if="handleApproach(item).length > 0">
									<div @click="handlerCopy(handleApproach(item))">复制</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="echars_main">
					<div
						class="box_"
						v-for="(item, index) in sixRingDateList"
						:key="index"
						:style="{ width: sixRingDateList.length > 1 ? 'calc(50% - 8px)' : '100%' }"
					>
						<div class="box_left">
							<div class="boxTitle">
								市场法
								<div class="line_box">|</div>
								<span style="font-size: 12px; color: #1868f1; margin-left: 5px; font-weight: 700"
									>{{ $utils.handleNumber(item?.agg?.appraise?.marketApproach) }}
								</span>
							</div>
							<div class="img_box">
								<div style="width: -webkit-fill-available" class="btn_boxDetails">
									{{ handleMarketApproach(item) }}
								</div>
								<div class="box_copyContent" v-if="handleMarketApproach(item).length > 0">
									<div @click="handlerCopy(handleMarketApproach(item))">复制</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="tag_box">证券化</div>

				<div class="echars_main">
					<div
						class="box_"
						v-for="(item, index) in sixRingDateList"
						:key="index"
						:style="{ width: sixRingDateList.length > 1 ? 'calc(50% - 8px)' : '100%' }"
					>
						<div class="box_left" style="padding: 0">
							<div class="img_box">
								<div class="tag_boxTitlec">
									<div class="tag_boxTitle">证券化排名(已统计的同类资产)</div>
									<div class="ranking">
										{{ item?.agg?.securitization?.comparableValueRank }} / {{ item?.agg?.securitization?.comparableValueRankTotal }}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="echars_main">
					<div
						class="box_"
						v-for="(item, index) in sixRingDateList"
						:key="index"
						:style="{ width: sixRingDateList.length > 1 ? 'calc(50% - 8px)' : '100%' }"
					>
						<div class="box_left" v-if="handleSecuritization(item).length > 0">
							<div class="img_box">
								<div style="width: -webkit-fill-available" class="btn_boxDetails">
									{{ handleSecuritization(item) }}
								</div>
								<div class="box_copyContent">
									<div @click="handlerCopy(handleSecuritization(item))">复制</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="echars_main">
					<div
						class="box_"
						v-for="(item, index) in sixRingDateList"
						:key="index"
						:style="{ width: sixRingDateList.length > 1 ? 'calc(50% - 8px)' : '100%' }"
					>
						<div class="box_left">
							<div class="img_box">
								<div class="tag_boxTitles">{{ handleTitleMontage(item) }}</div>
								<el-table border :data="item.securitiesProgramList" style="width: 100%">
									<el-table-column prop="name" label="分层" width="" show-overflow-tooltip />
									<el-table-column prop="rating" label="评级" width="" />
									<el-table-column prop="scale" label="规模(亿)" width="">
										<template #default="scope">
											<div style="text-align: center; width: max-content">
												{{
													scope.row.name === '发行规模'
														? formattedMoney(scope.row.scale, 2) + '亿'
														: scope.row.name === '综合成本率'
														? formattedMoney(scope.row.scale, 2) + '%'
														: formattedMoney(scope.row.scale, 2)
												}}
											</div>
										</template>
									</el-table-column>
									<el-table-column prop="interestRate" label="利率" width="" />
								</el-table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- 对话框 -->
	<el-dialog v-model="dialogTableVisible" width="800" title="选择对比资产" :close-on-click-modal="false">
		<div class="title_box">
			<div class="tab" v-for="(item, index) in multipleSelection" :key="index">
				<div style="text-wrap: nowrap">对比资产</div>
				{{ $utils.chineseNumber(index) }}：
				<div :title="item.buildingName" style="text-wrap: nowrap; width: 116px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
					{{ item.buildingName }}
				</div>
				<div class="det" @click="clearDate(item, 0, index)">×</div>
			</div>
		</div>
		<div class="search_box">
			<div class="box_1">
				<div class="label">城市</div>
				<el-cascader
					placeholder="请选择城市"
					:options="$vuexStore.state.cityArray"
					v-model="selectedCity"
					@change="handleChange"
					:props="{ value: 'label' }"
				>
				</el-cascader>
			</div>
			<div class="box_1">
				<div class="label">资产类型</div>
				<el-select v-model="buildValue" placeholder="全部资产">
					<el-option v-for="item in buildingTypes" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">关键词</div>
				<el-input v-model="essential" placeholder="请输入关键字"></el-input>
			</div>
			<div class="box_2">
				<el-button type="primary" @click="Comparedt()">查询</el-button>
				<el-button type="primary" @click="reset()">重置</el-button>
			</div>
		</div>
		<div class="table_2">
			<el-table
				:data="tableData"
				style="width: 100%"
				height="308px"
				border
				ref="multipleTableRef"
				@selection-change="(selection) => handleSelectionChange(selection)"
				stripe
			>
				<el-table-column type="selection" width="55" />
				<el-table-column
					v-for="(column, index) in tableColumns"
					:key="index"
					:label="column.label"
					:prop="column.prop"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table>
		</div>

		<el-pagination
			@current-change="handleCurrentChange"
			:current-page="currentPage"
			small
			background
			layout="prev, pager, next"
			class="mt-4"
			:total="total"
		/>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogTableVisible = false">取消</el-button>
				<el-button type="primary" @click="save()"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import getReport from '../../../../component/getReport/index.vue';
import { formattedMoney } from 'UTILS'; // 千分符
import { getBuildingListByMultiCondition, getDictList, getBuildingAppraisedValue } from '@/api/syt.js';
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const loading = ref();
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const getReportRef = ref(); //获取报告
const dialogTableVisible = ref(false); //对话框显示
const province = ref(''); //省
const city = ref(''); //市
const buildingTypes = ref([]); //资产类型
const buildValue = ref(''); //资产类型
const essential = ref(''); //关键词
const currentPage = ref(1); //当前页
const total = ref(0); //总条数
const multipleTableRef = ref(null); //多选
const tableData = ref([]); //表格数据
const multipleSelection = ref([]); //选中的数据
const selectedCity = ref([]); //选中的城市

const sixRingDateList = ref([
	//商圈数据
	{},
	{},
]);

// 表格
const tableColumns = [
	{
		label: '商圈名称',
		prop: 'buildingName',
		width: '200',
		showOverflowTooltip: true,
	},
	{
		label: '资产类型',
		prop: 'buildingType',
		showOverflowTooltip: true,
	},
	{
		showOverflowTooltip: true,
		label: '地址',
		prop: 'street',
		width: '300',
	},
];
const tableDatao = ref([]); //商圈一
const tableDatat = ref([]); //商圈二
const reset = () => {
	selectedCity.value = []; //选中的城市
	province.value = ''; //省
	city.value = ''; //市
	buildValue.value = '';
	essential.value = ''; //关键词
	currentPage.value = 1; //当前页
	Compared(); //查询
};

function Comparedt() {
	currentPage.value = 1; //当前页
	Compared(); //查询
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}

//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}
// 选中
const handleSelectionChange = (val) => {
	setTimeout(() => {
		let mergedSet = new Set([...multipleSelection.value, ...val]);
		let mergedArray = Array.from(mergedSet);
		let uniqueById = Array.from(new Set(mergedArray.map((item) => item.id))).map((id) => {
			return mergedArray.find((item) => item.id === id);
		});
		// 当前页 tableData.value
		// 当前页选中 val
		// 当前页选中和之前选中的重复的去掉的 uniqueById
		tableData.value.map((item) => {
			uniqueById.map((uniqueItem, index) => {
				if (item.id == uniqueItem.id) {
					const foundInVal = val.some((v) => v.id === uniqueItem.id);
					if (!foundInVal) {
						uniqueById.splice(index, 1);
					}
				}
			});
		});
		multipleSelection.value = uniqueById;
	}, 100);
};

// 点击选择对比资产
const choose = (item) => {
	dialogTableVisible.value = true;
};

//市场法内容拼接
function handleMarketApproach(item) {
	if (!item?.agg?.appraise?.grossReturn) {
		return '';
	}
	return `
本次估价取备实例毛报酬率的简单算术平均数作为估计对象的毛报酬率，即${item?.agg?.appraise?.grossReturn || 0}%，本次估价采取最终毛报酬率为${
		item?.agg?.appraise?.grossReturn || 0
	}%，故本次估价最终的净报酬率为${item?.agg?.appraise?.netInterestRate || 0}%。 按照评估基准日${item?.agg?.appraise?.createTime}中国十年期国债收益率${
		item?.agg?.appraise?.decadeCountriesEarnings
	}%为无风险报酬率，对应折现率为${item?.agg?.appraise?.discountRate || 0}%。通过现金流量折现法计算得出，市场法估价结果为${
		item?.agg?.appraise?.marketApproachEvaluateResult || 0
	}万元。`;
}

//收益法内容拼接
function handleApproach(item) {
	if (!item?.agg?.appraise?.grossReturn) {
		return '';
	}
	return `本次估价取备实例毛报酬率的简单算术平均数作为估计对象的毛报酬率，即${item?.agg?.appraise?.grossReturn || 0}%，本次估价采取最终毛报酬率为${
		item?.agg?.appraise?.grossReturn || 0
	}%，故本次估价最终的净报酬率为${item?.agg?.appraise?.netInterestRate || 0}%。 按照评估基准日${item?.agg?.appraise?.createTime}中国十年期国债收益率${
		item?.agg?.appraise?.decadeCountriesEarnings
	}%为无风险报酬率，对应折现率为${item?.agg?.appraise?.discountRate || 0}%。通过现金流量折现法计算得出，收益法估价结果为${
		item?.agg?.appraise?.incomeApproachEvaluateResult || 0
	}元/平方米`;
}

// 估值排名详情内容拼接
function handleValuationRanking(item) {
	if (!item?.agg?.appraise?.createTime) {
		return '';
	}
	return `于${item.agg.appraise.createTime}，选取可比实例为${item?.agg?.appraise?.comparableInstance}。术木智能基于出租率为80%的情形，评估资产价值。${
		item.businessDistrictName
	}估值结果为${item?.agg?.appraise?.evaluateResult || 0}元/平方米，在同资产类型中排名${item.agg?.appraise?.comparableValueRank || 0}。`;
}

//证券化详情内容拼接
function handleSecuritization(item) {
	if (!item?.agg?.securitization?.evaluateValue && !item?.agg?.securitization?.ebidta) {
		return '';
	}

	let name = `根据《${item.businessDistrictName}单价评估报告》，${item.businessDistrictName}的评估值为${item?.agg?.securitization?.evaluateValue}亿元。根据术木智能EBITDA系数模型，结合${item.businessDistrictName}的面积、运维情况，${item.businessDistrictName}EBITDA系数为${item?.agg?.securitization?.ebidta}。`;
	return name;
}

// 术木智能金融项目风险评估模型内容拼接
function handleTitleMontage(item) {
	if (!item.businessDistrictName) {
		return '';
	}
	return `根据术木智能金融项目风险评估模型，${item.businessDistrictName}权益型金融产品参数如下：`;
}

// 复制
function handlerCopy(name) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(name)
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = name;
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

// 修改城市
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const queryParams = computed(() => {
	return {
		city: province.value,
		district: city.value,
		keyword: essential.value,
		buildingType: buildValue.value,
		currentPage: currentPage.value,
		// year: 2024,
		pageSize: 10,
	};
});
onMounted(() => {
	getDict();
	Compared();
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
});

//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getBuildingAppraisedValue({ buildingIds: obj.ids }).then((res) => {
		sixRingDateList.value = [];
		tableDatao.value = [];
		tableDatat.value = [];
		if (res.code === 200) {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			sixRingDateList.value = res.data;
			multipleSelection.value = obj.arr;
			handleUpdate(1);
			sixRingDateList.value.forEach((element, index) => {
				if (index == 0) element.businessDistrictName = obj.arr[0].buildingName;
				if (index == 1) element.businessDistrictName = obj.arr?.[1]?.buildingName;
			});
			tableDatao.value.push(obj.arr[0]);
			tableDatat.value.push(obj.arr?.[1]);
			loading.value.close();
		}
	});
}
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
//对比资产分页查询
const handleCurrentChange = (val) => {
	currentPage.value = val;
	Compared();
};
// 查询
const Compared = async () => {
	await getBuildingListByMultiCondition(queryParams.value)
		.then((res) => {
			tableData.value = [];
			tableData.value = res.data.rows;
			nextTick(() => {
				tableData.value.map((v) => {
					multipleSelection.value.map((i) => {
						if (v.id == i.id) {
							multipleTableRef.value.toggleRowSelection(v, true);
						}
					});
				});
			});
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
// 全选
const toggleSelection = (rows, isSelect) => {
	if (rows) {
		rows.forEach((row) => {
			multipleTableRef.value.toggleRowSelection(row, undefined, isSelect);
		});
	} else {
		multipleTableRef.value.clearSelection();
	}
};

// 清空
function handelClear(row) {
	multipleSelection.value.forEach((item, indexs) => {
		if (item.id == row.id) {
			multipleSelection.value.splice(indexs, 1);
		}
	});
}

//清空资产选项
const clearDate = (row, type, index) => {
	if (type === 1) {
		//资产一清空
		tableDatao.value = [];
		handelClear(row);
		save();
	} else if (type === 2) {
		//资产二清空
		tableDatat.value = [];
		handelClear(row);
		save();
	} else {
		// 弹窗内资产清空
		multipleSelection.value.splice(index, 1);
	}
	if (tableData.value?.length > 0) {
		// 删除table选中的数据后，清空table选中的数据
		tableData.value.forEach((item) => {
			if (item.id == row.id) {
				toggleSelection([row]);
			}
		});
	}
};

//组装数据
function handleUpdate(type) {
	sixRingDateList.value.forEach((element, index) => {
		if (!type) {
			multipleSelection.value.forEach((item) => {
				if (item.id === element.id && index === 0) {
					element.businessDistrictName = item.buildingName;
					tableDatao.value.push(item);
				}
				if (item.id === element.id && index === 1) {
					element.businessDistrictName = item.buildingName;
					tableDatat.value.push(item);
				}
			});
		}
		// 拼接证券化表格数据
		let arr = [
			...element.agg.securitization.securitiesProgramList,
			{
				name: '发行规模',
			},
			{
				name: '综合成本率',
			},
		];

		arr.forEach((childItems, childIndex) => {
			if (childIndex == 0) {
				childItems['name'] = '优先级A1';
			} else if (childIndex == 1) {
				childItems['name'] = '优先级A2';
			} else if (childIndex == 2) {
				childItems['name'] = '优先级A3';
			} else if (childIndex == 3) {
				childItems['scale'] = element.agg?.securitization?.securitiesFinancialParam?.issueScale || 0;
				childItems['name'] = '发行规模';
			} else if (childIndex == 4) {
				childItems['scale'] = element.agg?.securitization?.securitiesFinancialParam?.combinedRatio || 0;
				childItems['name'] = '综合成本率';
			}
		});
		element.securitiesProgramList = arr;
	});
}
// 确定
const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		ElMessage({
			message: multipleSelection.value.length == 0 ? '至少选一个资产' : '最多选择两个资产',
			type: 'error',
		});
	} else {
		let ids = multipleSelection.value.map((item) => item.id).join(',');
		getBuildingAppraisedValue({ buildingIds: ids }).then((res) => {
			sixRingDateList.value = [];
			tableDatao.value = [];
			tableDatat.value = [];
			if (res.code === 200) {
				emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
				sixRingDateList.value = res.data;
				handleUpdate();
			}
			dialogTableVisible.value = false;
		});
	}
};
</script>
<style scoped lang="less">
.comparison_box {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;

		.table_main {
			width: 100%;
			// height: 162px;
			display: flex;
			justify-content: space-between;
			.table_ {
				width: calc(50% - 8px);
				height: 162px;
				border-radius: 6px;
				background-color: rgba(255, 255, 255, 1);
				position: relative;

				.tag_box {
					width: auto;
					height: 16px;
					position: absolute;
					left: 0;
					top: 20px;
					font-size: 14px;
					font-weight: bold;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&::before {
						content: '';
						width: 4px;
						height: 16px;
						background-color: rgba(24, 104, 241, 1);
						margin-right: 10px;
					}
				}

				.table_1 {
					width: 96%;
					position: absolute;
					bottom: 10px;
					left: 2%;
					&::v-deep .el-table--fit {
						border-radius: 8px;
					}

					&::v-deep .el-table th {
						background-color: rgba(245, 245, 245, 1);
					}
				}

				.add {
					width: 96%;
					height: 90px;
					position: absolute;
					bottom: 10px;
					left: 2%;
					border-radius: 6px;
					border: 1px solid rgba(231, 231, 231, 1);
					display: flex;
					justify-content: center;
					align-items: center;
					color: rgba(3, 93, 255, 1);
					font-size: 14px;
					font-weight: bold;
				}
			}
		}
		.echars_box {
			width: calc(100% - 32px);
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			padding: 0px 16px;
			border-radius: 6px;
			.tag_box {
				width: auto;
				height: 24px;
				padding: 16px 0 0 0;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
				span {
					margin-left: 10px;
					font-size: 12px;
					display: flex;
					line-height: normal;
					// justify-content: flex-start;
					// align-items: center;
					&:first-child {
						&::after {
							content: '';
							width: 8px;
							height: 12px;
							margin-top: 2.5px;
							margin-left: 10px;
							background-color: rgba(4, 80, 218, 1);
						}
					}
					&:last-child {
						&::after {
							content: '';
							width: 8px;
							margin-top: 2.5px;
							height: 12px;
							margin-left: 10px;
							background-color: rgba(30, 170, 117, 1);
						}
					}
				}
			}
			.echars_main {
				width: 100%;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				height: 100%;
				.box_ {
					width: calc(50% - 8px);
					box-sizing: border-box;
					height: 100%;
					.title1 {
						width: 100%;
						height: 44px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 12px;
						background-color: rgba(245, 245, 245, 1);
						border-top-left-radius: 3px;
						border-top-right-radius: 3px;
					}
					.box_Contents {
						width: calc(100% - 0px);
						display: flex;
						justify-content: space-between;
						margin-bottom: 16px;
					}
					.box_Content {
						width: calc(50% - 8px);
						border-radius: 3px;
						border-top-left-radius: 0px;
						border-top-right-radius: 0px;
						border: 1px solid rgba(231, 231, 231, 1);
						height: 56px;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						font-size: 14px;
						.big_title {
							font-size: 16px;
							color: rgba(3, 93, 255, 1);
							font-weight: bold;
						}
						.big_detail {
							font-size: 12px;
							margin: 2px 0 -2px 0;
							color: #333;
							font-weight: bold;
						}
					}
				}
			}
		}
	}
}
.title_box {
	width: 100%;
	max-height: 100px;
	overflow: scroll;
	border-top: 1px solid rgba(231, 231, 231, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	box-sizing: border-box;
	display: flex;
	flex-wrap: wrap;
	.tab {
		width: 31%;
		height: 32px;
		margin: 8px 15px 8px 0;
		background-color: rgba(245, 246, 247, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
		position: relative;
		text-wrap: nowrap;
		.det {
			width: 10px;
			height: 10px;
			position: absolute;
			right: 10px;
			font-size: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: rgba(201, 205, 212, 1);
			cursor: pointer;
		}
	}
}
.search_box {
	width: 100%;
	height: auto;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
	.box_1 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		border: 1px solid rgba(231, 231, 231, 1);
		box-sizing: border-box;

		::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
			box-shadow: 0;
		}

		.label {
			width: 50%;
			height: 100%;
			font-size: 14px;
			color: rgba(134, 144, 156, 1);
			background-color: rgba(245, 246, 247, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.box_2 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		box-sizing: border-box;
	}
}
.table_2 {
	width: 100%;
	height: 308px;
	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}

.top_boxFirst {
	display: flex;
	justify-content: space-between;
	height: 56px;
	align-items: center;
	.tag_boxTitle {
		width: auto;
		height: 16px;
		font-size: 14px;
		font-weight: bold;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		&::before {
			content: '';
			width: 4px;
			height: 16px;
			background-color: #1868f1;
			margin-right: 10px;
		}
	}

	.tag_boxCenter {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		font-size: 14px;
		color: #1868f1;
		padding-right: 16px;
		> :nth-child(n) {
			cursor: pointer;
		}
		.tag_boxLeft {
			display: flex;
			align-items: center;
			margin: 0 0px 0 24px;
		}
	}
}

.box_left {
	padding: 16px 0 0 0;
}

.boxTitle {
	font-size: 14px;
	line-height: 20px;
	color: #393d43;
	font-weight: 600;
	display: flex;
	margin-bottom: 3px;
}
.box_copyContent {
	width: 100%;
	display: flex;
	justify-content: end;
	margin-bottom: -10px;
}

.box_copyContent > :nth-child(n) {
	cursor: pointer;
	color: #1868f1;
	font-size: 12px;
	font-weight: 400;
	line-height: 20px;
}

.btn_boxDetails {
	font-size: 12px;
	font-weight: 400;
	line-height: 20px;
	color: #4e5969;
}

.img_box {
	padding: 16px 18px;
	border-radius: 3px;
	border: 1px solid #e7e7e7;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}
.tag_boxTitlec {
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-radius: 4px;
	.tag_boxTitle {
		font-size: 14px;
		font-weight: bold;
		color: #393d43;
	}
	.ranking {
		font-weight: bold;
		font-size: 14px;
		color: #1868f1;
	}
}

.tag_boxTitles {
	font-size: 14px;
	font-weight: bold;
	color: #4e5969;
	margin-bottom: 8px;
}

.line_box {
	margin: -2px 0 0 4px;
}
</style>
