<template>
	<el-dialog
		title="请描述您的需求"
		v-model="dialogVisible"
		width="640px"
		class="purchase_dialogs"
		style="border-radius: 16px; background: linear-gradient(180deg, #ecf3fb 0%, #f5f7f9 100%)"
		:show-close="true"
		:close-on-click-modal="false"
		@close="handleClose"
		custom-class="purchase-dialog"
	>
		<div class="content_box">
			<el-form :model="form" label-width="auto" label-position="top">
				<el-form-item>
					<template #label>
						<div class="label_box">
							<div class="label_text">您的姓名</div>
							<div class="label_required">*</div>
						</div>
					</template>
					<el-input v-model="form.name" placeholder="请输入您的姓名" />
				</el-form-item>
				<el-form-item>
					<template #label>
						<div class="label_box">
							<div class="label_text">您的联系电话</div>
							<div class="label_required">*</div>
						</div>
					</template>
					<el-input v-model="form.phone" placeholder="请输入您的联系电话" />
				</el-form-item>
				<el-form-item label="您的电子邮箱">
					<el-input v-model="form.email" placeholder="请输入您的电子邮箱" />
				</el-form-item>
				<el-form-item label="您的公司">
					<el-input v-model="form.company" placeholder="请输入您的公司" />
				</el-form-item>
				<el-form-item label="您的行业">
					<el-input v-model="form.industry" placeholder="请输入您的行业" />
				</el-form-item>
				<el-form-item label="您要咨询的内容">
					<el-input
						v-model="form.description"
						maxlength="300"
						rows="4"
						resize="none"
						type="textarea"
						show-word-limit
						placeholder="请输入您要咨询的内容"
					/>
				</el-form-item>
				<div style="margin-top: 24px">
					<el-button type="primary" style="width: 100%; height: 40px" @click="onSubmit" color="#1868F1">提交</el-button>
				</div>
			</el-form>
		</div>
	</el-dialog>
</template>

<script setup>
import { defineEmits, defineExpose, ref } from 'vue';
const emit = defineEmits(['close']);
const dialogVisible = ref(false);
import { Addcontact } from '../../api/home.js';
import { ElMessage } from 'element-plus';
const form = ref({
	name: '',
	industry: '',
	description: '', //咨询问题
	company: '',
	phone: '',
	email: '',
});

// 父组件传递过来的方法 显示
const show = () => {
	dialogVisible.value = true;
};

const handleClose = () => {
	dialogVisible.value = false;
};

const onSubmit = async () => {
	// 校验表单都不为空
	if (!form.value.name || !form.value.phone) {
		ElMessage.warning('必填项不可为空');
		return;
	}
	await Addcontact(form.value)
		.then((res) => {
			console.log(res);
			if (res.code === 200) {
				// 清空表单
				form.value = {
					name: '',
					phone: '',
					email: '',
					company: '',
					industry: '',
					description: '',
				};
				emit('close');
				handleClose();
			}
		})
		.catch((err) => {
			console.log('请求失败！');
		});
};

// 暴露方法给父组件
defineExpose({ show });
</script>
<style lang="scss">
.purchase_dialogs {
	.el-dialog__header {
		padding: 8px 16px 10px 16px;
		margin: 0;

		.el-dialog__title {
			font-size: 20px;
			font-weight: 500;
			color: #1d2129;
		}
		.el-dialog__headerbtn {
			height: 76px;
			right: 20px;
		}
	}

	.el-dialog__body {
		padding: 2px 8px 8px 8px;
	}
}
</style>
<style scoped lang="scss">
::v-deep .el-form-item {
	margin-bottom: 16px !important;
}

.label_box {
	display: flex;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	.label_text {
		color: #4e5969;
	}
	.label_required {
		color: #ff4d4f;
	}
}
</style>
