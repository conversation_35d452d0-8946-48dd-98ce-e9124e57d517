<template>
	<div class="system-table" :id="tableId ?? 'system-table'">
		<div class="system-table-container">
			<div class="system-table-content">
				<el-table
					ref="system_table"
					v-loading="tableData.loading"
					element-loading-text="拼命加载中"
					element-loading-background="rgba(0, 0, 0, 0.4)"
					:data="tableData.data"
					:tree-props="tableData.treeProps || {}"
					:size="size || 'default'"
					:border="border"
					:stripe="stripe"
					:lazy="lazy"
					:show-summary="showSummary"
					:default-sort="defaultSort"
					:highlight-current-row="highlightCurrentRow"
					:reserve-selection="reserveSelection"
					:height="height || 200"
					:row-key="rowKey"
					:empty-text="emptyText ?? '暂无数据'"
					:tooltip-effect="tooltipEffect ?? 'light'"
					:span-method="spanMethod"
					:row-class-name="rowClassName ?? tableRowClassName"
					:row-style="rowStyle"
					:cell-class-name="cellClassName"
					:cell-style="cellStyle"
					:show-header="true"
					:header-cell-style="headerCellStyle"
					:header-cell-class-name="headerCellClassName"
					:header-row-class-name="headerRowClassName"
					:header-row-style="headerRowStyle"
					@select="select"
					@select-all="selectAll"
					@selection-change="selectionChange"
					@row-click="rowClick"
					@row-dblclick="rowDblclick"
					@cell-click="cellClick"
				>
					<template v-for="(item, index) in columns">
						<template v-if="item.isShow != false">
							<Column v-if="!item.childSlots" :key="`${index}`" :column="item">
								<template v-if="item.slot" #[item.slot]="{ row }">
									<slot :name="item.slot" :row="row"> </slot>
								</template>
							</Column>
							<!-- childSlots多级表头子级插槽 -->
							<Column v-else :key="index" :column="item">
								<template v-for="slots in item.childSlots" #[slots]="{ row }">
									<slot :name="slots" :row="row"></slot>
								</template>
							</Column>
						</template>
					</template>
				</el-table>
			</div>
		</div>
		<div class="pagination" v-if="tableData.total as number > 0">
			<el-pagination
				:background="tableData.background ?? true"
				:small="tableData.small"
				:currnet-page="tableData.currentPage || 1"
				:layout="tableData.layout || 'total, sizes, prev, pager, next, jumper'"
				:total="tableData.total"
				:page-size="tableData.pageSize ?? 10"
				:page-sizes="tableData.pageSizes || [10, 20, 30, 40, 50, 100]"
				@current-change="currentChange"
				@size-change="handleSizeChange"
			></el-pagination>
		</div>
	</div>
</template>

<script lang="ts" setup>
import Column from './Column/index.vue';
import { TableData, RowObjectType, ColumnProps, RowType, ColumnType, ObjectType, ColumnObjectType } from './types';
interface Props {
	tableData: TableData;
	tableId?: string;
	size?: string;
	border?: boolean;
	stripe?: boolean;
	lazy?: boolean;
	showSummary?: boolean;
	defaultSort?: ObjectType;
	highlightCurrentRow?: boolean;
	reserveSelection?: boolean;
	height?: string | number;
	rowKey?: string | ((row: ObjectType) => void | string);
	emptyText?: string;
	tooltipEffect?: string;
	showHeader?: boolean;
	rowIndex?: string;
	spanMethod?: ({ row, column, rowIndex, columnIndex }: RowObjectType & ColumnObjectType) => void;
	rowClassName?: ({ row, rowIndex }: RowObjectType) => void | string;
	rowStyle?: ({ row, rowIndex }: RowObjectType) => void | ObjectType;
	cellClassName?: ({ row, column, rowIndex, columnIndex }: RowObjectType & ColumnObjectType) => void | string;
	cellStyle?: ({ row, column, rowIndex, columnIndex }: RowObjectType & ColumnObjectType) => void | ObjectType;
	headerCellStyle?: ({ row, column, rowIndex, columnIndex }: RowObjectType & ColumnObjectType) => void | ObjectType;
	headerCellClassName?: ({ row, column, rowIndex, columnIndex }: RowObjectType & ColumnObjectType) => void | string;
	headerRowClassName?: ({ row, rowIndex }: RowObjectType) => void | string;
	headerRowStyle?: ({ row, rowIndex }: RowObjectType) => void | ObjectType;
	columns?: ColumnProps[];
}
const props = defineProps<Props>();
const emit = defineEmits(['select', 'selectAll', 'selectionChange', 'rowClick', 'rowDblclick', 'cellClick', 'currentChange', 'handleSizeChange']);
// 当用户手动勾选数据行的 Checkbox 时触发的事件
const select = (selection: ObjectType, row: RowObjectType) => {
	emit('select', selection, row);
};
// 当用户手动勾选全选 Checkbox 时触发的事件
const selectAll = (selection: ObjectType) => {
	emit('selectAll', selection);
};
// 当选择项发生变化时会触发该事件
const selectionChange = (selection: ObjectType) => {
	emit('selectionChange', selection);
};
// 当某一行被点击时会触发该事件
const rowClick = (row: RowType, column: ColumnType, event: any) => {
	emit('rowClick', row, column, event);
};
// 当某一行被双击时会触发该事件
const rowDblclick = (row: RowType, column: ColumnType, event: any) => {
	emit('rowDblclick', row, column, event);
};
// 当某个单元格被点击时会触发该事件
const cellClick = (row: RowType, column: ColumnType, cell: string | number, event: any) => {
	emit('cellClick', row, column, cell, event);
};
// currentPage 改变时会触发
const currentChange = (val: number) => {
	emit('currentChange', val);
};
// pageSize 改变时会触发
const handleSizeChange = (val: number) => {
	emit('handleSizeChange', val);
};
// 行样式
const tableRowClassName = ({ rowIndex }: ObjectType) => {
	if (props.rowIndex == rowIndex) {
		return props.rowClassName;
	} else {
		if (rowIndex % 2 === 1) {
			return 'evenNumberRow';
		}
	}
};
</script>
<style lang="less">
@import './style.less';
</style>
