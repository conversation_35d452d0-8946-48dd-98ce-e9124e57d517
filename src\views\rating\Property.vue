<template>
	<el-row :gutter="80" style="margin-left: 10px">
		<el-col :span="16" class="left">
			<el-row :gutter="80" v-if="tableData" justify="cneter" style="padding-top: 34px">
				<el-col style="padding-left: 10px" :span="12">
					<div class="info">
						<el-text tag="p" class="Title">
							建筑物简介>>
							<el-divider class="divider" />
						</el-text>

						<el-text class="mx-1" style="line-height: 1.5; margin-top: 10px; font-size: 14px" tag="p">{{
							tableData.amd1_sp1 ? tableData.amd1_sp1[1] : ''
						}}</el-text>
					</div>
				</el-col>
				<el-col :span="12">
					<el-text tag="p" class="Title">基本信息>><el-divider class="divider" /></el-text>
					<!-- <el-table :data="condition" style="width: 100%; color: #000; margin-top: 20px">
						<el-table-column prop="column1" width="300" style="color: #000" />
						<el-table-column prop="column2" width="" style="color: #000" />
					</el-table> -->
					<el-descriptions class="margin-top" :column="1" :size="size" :style="blockMargin">
						<el-descriptions-item label-class-name="my-label" v-for="item in condition" :key="item.column1" :label="item.column1">{{
							item.column2
						}}</el-descriptions-item>
					</el-descriptions>
				</el-col>
			</el-row>
			<el-row :gutter="80" style="margin-top: 20px">
				<el-col :span="12" style="padding-left: 10px"
					><div class="grid-content ep-bg-purple" />
					<el-text class="Title" size="large" tag="p">{{ type }}入驻费用>><el-divider class="divider" /></el-text>
					<el-table :data="rentData" style="width: 100%; margin-top: 20px; color: black">
						<el-table-column prop="area" label="面积(m)" style="color: #000" />
						<el-table-column prop="rent" label="租金(元/天/㎡)" style="color: #000" />
						<el-table-column prop="property_fee" label="物业费(元/月/㎡)" style="color: #000" />
						<el-table-column prop="gross_rent" label="总租金(元/月)" style="color: #000" />
						<el-table-column prop="total_property_charge" label="总物业费(元/月)" style="color: #000" />
						<el-table-column prop="rental_charge" label="租金加物业费(元)" style="color: #000" />
					</el-table>
				</el-col>
				<el-col :span="12"
					><div class="grid-content ep-bg-purple" />
					<el-text class="Title" size="large" tag="p">配套情况(半径一公里)>><el-divider class="divider" /></el-text>

					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">商圈名称</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.business_name }}</el-text></el-col
						>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">周边交通要道</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.traffic_artery }}</el-text></el-col
						>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">住宅区</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.living_quarters }}</el-text></el-col
						>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">银行</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.bank }}</el-text></el-col
						>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">购物中心</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.shopping_center }}</el-text></el-col
						>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">写字楼</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.office_building }}</el-text></el-col
						>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">餐饮</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.catering }}</el-text></el-col
						>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">星级酒店</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.star_hotel }}</el-text></el-col
						>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="4"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">公交线路</el-text></el-col
						>

						<el-col :span="20"
							><div class="grid-content ep-bg-purple" />
							<el-text class="mx-1 title" tag="p">{{ infrastructure.bus_route }}</el-text></el-col
						>
					</el-row>
				</el-col>
			</el-row>
		</el-col>

		<el-col :span="8" class="right">
			<el-image :src="`${proxyAddress}${img1}`" style="" fit="scale-down" />
			<el-image style="margin-top: 10px" fit="scale-down" :src="`${proxyAddress}${img2}`" />
			<el-image style="margin-top: 10px" fit="scale-down" :src="`${proxyAddress}${img3}`" />
		</el-col>
	</el-row>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import http from '@/utils/http';
const proxyAddress = process.env.NODE_ENV === 'development' ? 'http://**************:8081/' : 'http://**************:8081/';

const route = useRoute();
const tableData = ref([]);
const condition = ref([]);
const rentData = ref([]);
const infrastructure = ref({});
const name = ref('');
const type = ref('');
const img1 = ref('');
const img2 = ref('');
const img3 = ref('');

onMounted(() => {
	http
		.get('/api/a_maps.do?act=ratingPassDetailsAction', {
			params: {
				maps_id: route.params.id,
				action: 'property',
			},
		})
		.then((res) => {
			if (res || res.list.length > 0) {
				type.value = res.list[0].am_deType;
				tableData.value = res.list[0];
				rentData.value = res.list[2];
				infrastructure.value = res.list[1];
				name.value = res.list[0].am_name;

				condition.value = tableData.value.amd1_sp2.map((item) => {
					const [column1, column2] = item.split(',');
					return {
						column1: column1,
						column2: column2 || '',
					};
				});
			}
			img1.value = res.list[3][0].file_url;
			img2.value = res.list[3][1].file_url;
			img3.value = res.list[3][2].file_url;
		});
});
</script>

<style lang="less" scoped>
.el-row {
	margin-bottom: 10px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}
.el-table:deep(.cell) {
	color: #000;
}
.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
}
.divider {
	flex-grow: 1;
	margin: 16px 0 16px 8px;
	border-color: #3483ce;
}
.grid-content {
	border-radius: 4px;
	min-height: 18px;
}
:deep(.my-label) {
	background: var(--el-color-danger-light-9);
}
.right {
	padding-left: 190px !important;
}
</style>
