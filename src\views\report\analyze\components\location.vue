<template>
	<div class="loacation">
		<div>
			<h1>二、市场调查</h1>
			<template v-if="locatype == '青岛市'">
				<qingdao></qingdao>
			</template>
			<template v-else-if="locatype == '北京市'">
				<beijing></beijing>
			</template>
			<template v-else-if="locatype == '烟台市'">
				<yantai></yantai>
			</template>
			<template v-else-if="locatype == '济南市'">
				<jinan></jinan>
			</template>
			<template v-else-if="locatype == '上海市'">
				<shanghai></shanghai>
			</template>
			<h1><strong>2.3 可比商圈概况</strong></h1>
			<h2><strong>2.3.1所在商圈</strong></h2>
			<h3>
				<strong>{{ locadata?.businessDistrictReportVO?.businessDistrict?.businessDistrictName }}</strong>
			</h3>
			<table class="MsoNormalTable" style="width: 100%">
				<tbody>
					<tr class="tab-tltle">
						<td valign="center" nowrap class>商圈名称</td>
						<td valign="center" nowrap class>商圈位置</td>
						<td valign="center" nowrap class>商圈面积</td>
						<td valign="center" nowrap class>商圈定位</td>
						<td valign="center" nowrap class>商圈排名</td>
					</tr>
					<tr>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessDistrict?.businessDistrictName }}</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessDistrict?.district }}</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessDistrict?.area }}万平方米</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessDistrict?.positioning }}</td>
						<td valign="center" nowrap class>
							{{ locadata?.businessDistrictReportVO?.businessDistrict?.businessRank }}/{{ locadata?.businessRankTotal }}
						</td>
					</tr>
					<tr>
						<td valign="center" nowrap colspan="5" class></td>
					</tr>
					<tr>
						<td valign="center" nowrap colspan="1" rowspan="3" class>
							<img
								style="width: 300px; height: 180px; object-fit: cover"
								:src="`${store.imagePathPrefix}${locadata?.businessDistrictReportVO?.businessDistrict?.businessDistrictPics.split(',')[0]}`"
								alt=""
							/>
						</td>
						<td valign="center" colspan="2" rowspan="3" class>
							{{ locadata?.businessDistrictReportVO?.businessDistrict?.locationRange }}
						</td>
						<td valign="center" nowrap colspan="2" rowspan="3" class>
							<rat
								class="rat"
								v-if="locadata?.businessDistrictReportVO?.businessCoordinateList.length > 0"
								:containerIds="'contianer1' + locadata?.businessDistrictReportVO?.businessDistrict?.id"
								:polygonArr="polygonArrGet(locadata?.businessDistrictReportVO?.businessCoordinateList)"
								style="width: 100%; height: 180px; border-radius: 4px"
							></rat>
						</td>
					</tr>
					<tr></tr>
					<tr></tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap colspan="2" class>商圈实拍</td>
						<td valign="center" nowrap class>商圈范围</td>
						<td valign="center" nowrap colspan="2" class>商圈边界</td>
					</tr>
					<tr>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class></td>
					</tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap colspan="5" class>商圈评估表</td>
					</tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap colspan="5" class>商圈亮点</td>
					</tr>
					<tr>
						<td valign="center" colspan="5" class="desc_width">
							{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.businessHighlight }}
						</td>
					</tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap colspan="5" class>商圈不足</td>
					</tr>
					<tr>
						<td valign="center" colspan="5" class="desc_width">{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.businessDefect }}</td>
					</tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap colspan="5" class>商圈评价</td>
					</tr>
					<tr>
						<td valign="center" colspan="5" class="desc_width">{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.businessEvaluateDesc }}</td>
					</tr>
					<tr>
						<td valign="center" nowrap class>商业设施齐全程度</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.facilityLevel }}</td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class>商业覆盖度</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.businessCover }}</td>
					</tr>
					<tr>
						<td valign="center" nowrap class>商业氛围</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.businessAtmo }}</td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class>交通网络</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.trafficNetwork }}</td>
					</tr>
					<tr>
						<td valign="center" nowrap class>交通拥堵情况</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.trafficJam }}</td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class>人流量</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.peopleTraffic }}</td>
					</tr>
					<tr>
						<td valign="center" nowrap class>消费人群特征</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.spendPeopleFeature }}</td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class>配套设施</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.facilitySupport }}</td>
					</tr>
					<tr>
						<td valign="center" nowrap class>商圈管理</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.businessManage }}</td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class>竞争品牌数量</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.brandCompeteNum }}</td>
					</tr>
					<tr>
						<td valign="center" nowrap class>竞争形式</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.businessCompete }}</td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class>总分</td>
						<td valign="center" nowrap class>{{ locadata?.businessDistrictReportVO?.businessEvaluateInfo?.totalScore }}</td>
					</tr>
					<tr>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class></td>
						<td valign="center" nowrap class></td>
					</tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap colspan="5" class>商圈内建筑一览</td>
					</tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap class>建筑名称</td>
						<td valign="center" nowrap class>业态</td>
						<td valign="center" nowrap class>租金/㎡/天</td>
						<td valign="center" nowrap class>物业费/㎡/月</td>
						<td valign="center" nowrap class>单价</td>
					</tr>
					<tr v-for="(item, index) in locadata?.businessDistrictReportVO?.buildBusinessDistrictList" :key="index">
						<td valign="center" nowrap class>{{ item.buildingName }}</td>
						<td valign="center" nowrap class>{{ item.buildingType }}</td>
						<td valign="center" nowrap class>{{ item.rental }}</td>
						<td valign="center" nowrap class>{{ item.propertyFee }}</td>
						<td valign="center" nowrap class>{{ item.absoluteValue }}</td>
					</tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap colspan="5" class>商圈均价统计</td>
					</tr>
					<tr class="tab-tltle">
						<td valign="center" nowrap class>业态</td>
						<td valign="center" nowrap class>租金均价</td>
						<td valign="center" nowrap class>物业费均价</td>
						<td valign="center" colspan="2" nowrap class>单价均价</td>
					</tr>
					<tr v-for="(item, index) in locadata?.businessDistrictReportVO?.buildBusinessDistrictAvgList" :key="index">
						<td valign="center" nowrap class>{{ item.buildingType }}</td>
						<td valign="center" nowrap class>{{ item.rental }}</td>
						<td valign="center" nowrap class>{{ item.propertyFee }}</td>
						<td valign="center" colspan="2" nowrap class>{{ item.absoluteValue }}</td>
					</tr>
				</tbody>
			</table>
			<h2><strong>2.3.2</strong><strong>可比商圈</strong></h2>
			<template v-for="(ite, ind) in locadata?.comparableBusinessDistrictReportVOList" :key="ind">
				<h3 v-if="ind == 0">
					<strong>2.3.2.1{{ ite?.businessDistrict?.businessDistrictName }}</strong>
				</h3>
				<h3 v-if="ind == 1">
					<strong>2.3.2.2{{ ite?.businessDistrict?.businessDistrictName }}</strong>
				</h3>
				<table class="MsoNormalTable" style="width: 100%">
					<tbody>
						<tr class="tab-tltle">
							<td valign="center" nowrap class>商圈名称</td>
							<td valign="center" nowrap class>商圈位置</td>
							<td valign="center" nowrap class>商圈面积</td>
							<td valign="center" nowrap class>商圈定位</td>
							<td valign="center" nowrap class>商圈排名</td>
						</tr>
						<tr>
							<td valign="center" nowrap class>{{ ite?.businessDistrict?.businessDistrictName }}</td>
							<td valign="center" nowrap class>{{ ite?.businessDistrict?.district }}</td>
							<td valign="center" nowrap class>{{ ite?.businessDistrict?.area }}万平方米</td>
							<td valign="center" nowrap class>{{ ite?.businessDistrict?.positioning }}</td>
							<td valign="center" nowrap class>{{ ite?.businessDistrict?.businessRank }}/{{ locadata?.businessRankTotal }}</td>
						</tr>
						<tr>
							<td valign="center" nowrap colspan="5" class></td>
						</tr>
						<tr>
							<td valign="center" nowrap colspan="1" rowspan="3">
								<img
									style="width: 300px; height: 180px; object-fit: cover"
									:src="`${store.imagePathPrefix}${ite?.businessDistrict?.businessDistrictPics.split(',')[0]}`"
									alt=""
								/>
							</td>
							<td valign="center" colspan="2" rowspan="3">
								{{ ite?.businessDistrict?.locationRange }}
							</td>
							<td valign="center" nowrap colspan="2" rowspan="3" class>
								<rat
									class="rat"
									v-if="ite?.businessCoordinateList.length > 0"
									:containerIds="'contianer1' + ite?.businessDistrict?.id"
									:polygonArr="polygonArrGet(ite?.businessCoordinateList)"
									style="width: 100%; height: 180px; border-radius: 4px"
								></rat>
							</td>
						</tr>
						<tr></tr>
						<tr></tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap colspan="2" class>商圈实拍</td>
							<td valign="center" nowrap class>商圈范围</td>
							<td valign="center" nowrap colspan="2" class>商圈边界</td>
						</tr>
						<tr>
							<td valign="center" colspan="5" nowrap class></td>
						</tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap colspan="5" class>商圈评估表</td>
						</tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap colspan="5" class>商圈亮点</td>
						</tr>
						<tr>
							<td valign="center" colspan="5">
								<span class="desc_width">{{ ite?.businessEvaluateInfo?.businessHighlight }}</span>
							</td>
						</tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap colspan="5" class>商圈不足</td>
						</tr>
						<tr>
							<td valign="center" colspan="5" class="desc_width">{{ ite?.businessEvaluateInfo?.businessDefect }}</td>
						</tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap colspan="5" class>商圈评价</td>
						</tr>
						<tr>
							<td valign="center" colspan="5" class="desc_width">{{ ite?.businessEvaluateInfo?.businessEvaluateDesc }}</td>
						</tr>
						<tr>
							<td valign="center" nowrap class>商业设施齐全程度</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.facilityLevel }}</td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class>商业覆盖度</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.businessCover }}</td>
						</tr>
						<tr>
							<td valign="center" nowrap class>商业氛围</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.businessAtmo }}</td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class>交通网络</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.trafficNetwork }}</td>
						</tr>
						<tr>
							<td valign="center" nowrap class>交通拥堵情况</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.trafficJam }}</td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class>人流量</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.peopleTraffic }}</td>
						</tr>
						<tr>
							<td valign="center" nowrap class>消费人群特征</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.spendPeopleFeature }}</td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class>配套设施</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.facilitySupport }}</td>
						</tr>
						<tr>
							<td valign="center" nowrap class>商圈管理</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.businessManage }}</td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class>竞争品牌数量</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.brandCompeteNum }}</td>
						</tr>
						<tr>
							<td valign="center" nowrap class>竞争形式</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.businessCompete }}</td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class>总分</td>
							<td valign="center" nowrap class>{{ ite?.businessEvaluateInfo?.totalScore }}</td>
						</tr>
						<tr>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class></td>
							<td valign="center" nowrap class></td>
						</tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap colspan="5" class>商圈内建筑一览</td>
						</tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap class>建筑名称</td>
							<td valign="center" nowrap class>业态</td>
							<td valign="center" nowrap class>租金/㎡/天</td>
							<td valign="center" nowrap class>物业费/㎡/月</td>
							<td valign="center" nowrap class>单价</td>
						</tr>
						<tr v-for="(item, index) in ite?.buildBusinessDistrictList" :key="index">
							<td valign="center" nowrap class>{{ item.buildingName }}</td>
							<td valign="center" nowrap class>{{ item.buildingType }}</td>
							<td valign="center" nowrap class>{{ item.rental }}</td>
							<td valign="center" nowrap class>{{ item.propertyFee }}</td>
							<td valign="center" nowrap class>{{ item.absoluteValue }}</td>
						</tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap colspan="5" class>商圈均价统计</td>
						</tr>
						<tr class="tab-tltle">
							<td valign="center" nowrap class>业态</td>
							<td valign="center" nowrap class>租金均价</td>
							<td valign="center" nowrap class>物业费均价</td>
							<td valign="center" colspan="2" nowrap class>单价均价</td>
						</tr>
						<tr v-for="(item, index) in ite?.buildBusinessDistrictAvgList" :key="index">
							<td valign="center" nowrap class>{{ item.buildingType }}</td>
							<td valign="center" nowrap class>{{ item.rental }}</td>
							<td valign="center" nowrap class>{{ item.propertyFee }}</td>
							<td valign="center" colspan="2" nowrap class>{{ item.absoluteValue }}</td>
						</tr>
					</tbody>
				</table>
			</template>
		</div>
	</div>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import { location } from '@/api/baogao';
import qingdao from '../locationComponent/qingdao.vue';
import shanghai from '../locationComponent/shanghai.vue';
import beijing from '../locationComponent/beijing.vue';
import yantai from '../locationComponent/yantai.vue';
import jinan from '../locationComponent/jinan.vue';
import { useStore } from '@/store';
import rat from '@/RatMap.vue'; //地图
const store = useStore();
const props = defineProps({
	id: {
		type: String,
		default: null,
		required: true,
	},
});

const id = ref('');
const locadata = ref({});
const locatype = ref('');
const getdata = async () => {
	const res = await location({ buildingId: id.value });
	if (res.code === 200) {
		locadata.value = res.result;
		locatype.value = res.result.city;
	}
};
const polygonArrGet = (data) => {
	let arr = [];
	data.forEach((item) => {
		arr.push([item.lng, item.lat]);
	});
	return arr;
};
onMounted(() => {
	id.value = props.id;
	getdata();
});
</script>
<style lang="less" scoped>
.loacation {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	overflow: hidden;
	table {
		border-top: 1px solid #333;
		border-left: 1px solid #333;
		border-spacing: 0;
		background-color: #fff;
		width: 100%;
		.tab-tltle {
			td {
				font-weight: 700;
				font-size: 15px;
			}
		}
		.tab-tltle {
			font-weight: 700;
			font-size: 15px;
		}
		::v-deep .rat {
			.amap-logo {
				display: none !important;
			}
		}
	}
	table td {
		text-align: center;
		border-bottom: 1px solid #333;
		border-right: 1px solid #333;
		font-size: 13px;
		padding: 10px;
	}
}
</style>
