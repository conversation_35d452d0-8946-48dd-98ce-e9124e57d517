<template>
	<div>
		<el-dialog v-model="dialogVisible" :title="title" width="1000" @close="handleClose">
			<el-row :gutter="30">
				<el-col :span="6">
					<div class="search_item">
						<div>城市：</div>
						<el-cascader
							v-model="cityValue"
							style="width: 160px"
							placeholder="请选择城市"
							:options="$vuexStore.state.cityArray"
							@change="handleChange"
							:props="{ value: 'label' }"
						>
						</el-cascader>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="search_item">
						<div>资产类型：</div>
						<el-select v-model="buildingType" placeholder="选择资产类型" style="width: 140px">
							<el-option v-for="item in buildingTypes" :key="item.key" :label="item.name" :value="item.key" />
						</el-select>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="search_item">
						<div>关键词：</div>
						<el-input style="width: 150px" v-model="keyword" placeholder="请输入关键字"></el-input>
					</div>
				</el-col>
				<el-col :span="4">
					<el-button @click="reset">重置</el-button>
					<el-button type="primary" @click="search">查询</el-button>
				</el-col>
			</el-row>
			<!-- @selection-change="handleSelectionChange" @select-all="handleSelectAll"-->
			<el-table @select="handleSelect" :data="tableData" class="table_style" row-key="id" ref="multipleTableRef" stripe border>
				<!-- <el-table-column type="selection" width="50" :reserve-selection="true" align="center"> </el-table-column> -->
				<!-- 单选框列 -->
				<el-table-column label="选择" width="55" align="center">
					<template #default="{ row, $index }">
						<el-radio v-model="radioValue" :label="row.id" @change="handleRadioChange(row)">
							<i></i>
						</el-radio>
					</template>
				</el-table-column>
				<el-table-column v-for="(column, index) in tableColumns" :key="index" :label="column.label" :prop="column.prop" :width="column.width">
					<template #default="scope">{{
						column.prop === 'address'
							? scope.row['province'] + scope.row['city'] + scope.row['district'] + scope.row['street']
							: scope.row[column.prop]
					}}</template>
				</el-table-column>
			</el-table>
			<!-- 分页 -->
			<el-config-provider :locale="zhCn">
				<el-pagination
					@current-change="pageChange"
					:current-page="currentPage"
					small
					background
					layout="total, prev, pager, next"
					class="mt-4"
					:total="total"
				/>
			</el-config-provider>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="handleClose">取消</el-button>
					<el-button type="primary" @click="onConfirm()">确认</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>
<script setup>
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import { ElMessage } from 'element-plus';
import { getCreditRisk } from '@/api/risks.js';
import { getAllBusinessDistrict } from '@/api/syt.js';
import { nextTick } from 'vue';

const radioValue = ref(null);
const buildingTypes = [
	{
		name: '写字楼',
		key: '1',
	},
	{
		name: '零售',
		key: '2',
	},
	{
		name: '产业园区',
		key: '3',
	},
	{
		name: '仓储物流',
		key: '4',
	},
	{
		name: '酒店',
		key: '5',
	},
	{
		name: '长租公寓',
		key: '6',
	},
	{
		name: '医疗',
		key: '7',
	},
	{
		name: '综合市场',
		key: '8',
	},
];
const tableColumns = [
	{ label: '资产名称', prop: 'buildingName', width: '300' },
	{ label: '资产类型', prop: 'buildingType', width: '100' },
	{ label: '地址', prop: 'address' },
];
const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	title: {
		type: String,
		default: '选择资产',
	},
	maxSelectNum: {
		type: Number,
		default: 10,
	},
	selectData: {
		type: Array,
		default: () => [],
	},
	dialogType: {
		type: String,
		default: 'build', // build房源  business 商圈
	},
	searchTypes: {
		type: Array,
		default: () => ['position', 'rate', 'type', 'keyword'],
	},
});
const emit = defineEmits(['update:modelValue', 'confirm']);
const dialogVisible = ref(false);
watch(
	() => props.modelValue,
	(newVal) => {
		dialogVisible.value = newVal;
		if (newVal) {
			search();
		}
	}
);
watch(dialogVisible, (newVal) => {
	emit('update:modelValue', newVal);
});
const cityValue = ref([]);
const city = ref('');
const district = ref('');
const buildingType = ref('');
const keyword = ref('');
const tableData = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const multipleTableRef = ref();
const multipleSelection = ref(null);

function handleRadioChange(row) {
	console.log('🚀 ~ handleRadioChange ~ row:', row);
	multipleSelection.value = row;
}
function reset() {
	cityValue.value = [];
	city.value = '';
	district.value = '';
	buildingType.value = '';
	keyword.value = '';
	multipleTableRef.value.clearSelection();
	radioValue.value = null;
	search();
}
async function search() {
	const queryParams = {
		// degree,
		city: city.value,
		district: district.value,
		buildingType: buildingType.value,
		keyword: keyword.value,
		current: currentPage.value,
		size: pageSize.value,
	};
	if (props.dialogType == 'build') {
		await getCreditRisk(queryParams)
			.then((res) => {
				tableData.value = res.data.rows;
				total.value = res.data.total;
			})
			.catch((err) => {
				console.log(err, 'err');
			});
	} else {
		await getAllBusinessDistrict(queryParams)
			.then((res) => {
				tableData.value = res.data.rows;
				total.value = res.data.total;
			})
			.catch((err) => {
				console.log(err, 'err');
			});
	}
}
function handleChange(val) {
	city.value = val[0];
	district.value = val[1];
}
function handleClose() {
	cityValue.value = [];
	city.value = '';
	district.value = '';
	buildingType.value = '';
	keyword.value = '';
	multipleTableRef.value.clearSelection();
	radioValue.value = null;
	nextTick(() => {
		// 关闭对话框
		dialogVisible.value = false;
	});
}
function handleSelectionChange(selection) {
	if (selection.length > props.maxSelectNum) {
		// 超过最大限制时，保留前10条并取消多余的选中项
		const validSelection = selection.slice(0, props.maxSelectNum);
		// 遍历所有行，设置选中状态
		tableData.value.forEach((row) => {
			const isSelected = validSelection.includes(row);
			multipleTableRef.value.toggleRowSelection(row, isSelected);
		});
		// 更新选中数据
		multipleSelection.value = validSelection;
		// 提示用户
		ElMessage({
			message: `最多只能选择 ${props.maxSelectNum} 条数据！`,
			type: 'warning',
		});
	} else {
		multipleSelection.value = selection; // 更新选中数据
	}
}
function pageChange(page) {
	currentPage.value = page;
	search();
}
function onConfirm() {
	dialogVisible.value = false;
	emit('confirm', multipleSelection.value);
}
function handleSelect(selection, row) {
	console.log('🚀 ~ handleSelect ~ selection, row:', selection, row);
	if (selection.length > 1) {
		// 如果选中超过 1 行，保留最后一行并取消其他行的选中
		multipleTableRef.value.clearSelection(); // 清空所有选中项
		multipleTableRef.value.toggleRowSelection(row, true); // 只选中最后一行
		multipleSelection.value = row; // 更新当前选中的行数据
	} else {
		multipleSelection.value = row || null;
	}
}
function handleSelectAll(selection) {
	console.log('🚀 ~ handleSelectAll ~ selection:', selection);
	if (selection.length > props.maxSelectNum) {
		// 超过最大限制时，保留前10条并取消多余的选中项
		const validSelection = selection.slice(0, props.maxSelectNum);
		// 遍历所有行，设置选中状态
		tableData.value.forEach((row) => {
			const isSelected = validSelection.includes(row);
			multipleTableRef.value.toggleRowSelection(row, isSelected);
		});
		// 更新选中数据
		multipleSelection.value = validSelection;
		// 提示用户
		ElMessage({
			message: `最多只能选择 ${props.maxSelectNum} 条数据！`,
			type: 'warning',
		});
	} else {
		multipleSelection.value = selection; // 更新选中数据
	}
}
</script>

<style lang="less" scoped>
.search_item {
	display: flex;
	align-items: center;
}
.table_style {
	width: 100%;
	margin-top: 14px;
	:deep(.el-radio__label) {
		padding-left: 0;
	}
}
</style>
