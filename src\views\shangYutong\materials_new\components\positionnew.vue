<template>
	<div class="comparison_box">
		<div class="common_wrap">
			<div class="left_empty_wrap" v-if="tableDataLeft.length == 0">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择资产
				</arco-button>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0" class="left_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('left')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataLeft"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0 && tableDataRight.length == 0" class="right_empty_wrap">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择对比资产
				</arco-button>
			</div>
			<div v-if="tableDataRight && tableDataRight.length > 0" class="right_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('right')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataRight"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
		</div>
		<div class="chart_wrap">
			<div class="header_wrap">
				<span class="line"></span>
				<span class="title">位置分析</span>
			</div>
			<template v-if="sixRingDateList.length == 2">
				<div class="double_box_wrap">
					<div class="title1">
						<div class="title">维度分析</div>
					</div>
					<div class="double_wrap">
						<div class="content_wrap" v-for="item in sixRingDateList" :key="item.id">
							<rat
								class="rat"
								:containerIds="'contianers' + item.id + 0"
								:polygonArr="item.districtsList.businessCoordinateList"
								style="width: 100%; height: 436px; border-radius: 4px"
							></rat>
							<div class="map_tip">
								<div class="business_name">
									{{ item.districtsList?.businessDistrictName }}
								</div>
								<div class="type_wrap">
									<div
										class="type_item"
										:class="{ type_item_active: index == item.districtsList.infoActiveIndex }"
										v-for="(type, index) in item.districtsList?.info"
										@click="item.districtsList.infoActiveIndex = index"
									>
										{{ type.type }}
									</div>
								</div>

								<arco-typography-paragraph
									class="type_desc"
									:ellipsis="{
										rows: 3,
										showTooltip: true,
									}"
								>
									{{ item.districtsList.info?.[item.districtsList.infoActiveIndex]?.allName }}
								</arco-typography-paragraph>
							</div>
						</div>
					</div>
				</div>
				<div class="double_box_wrap">
					<div class="title1">
						<div class="title">商圈介绍</div>
					</div>
					<div class="double_wrap">
						<div class="content_wrap" v-for="(item, index) in sixRingDateList" :key="item.id">
							<arco-carousel
								indicator-type="line"
								indicator-position="bottom"
								:style="{
									width: '100%',
									height: '436px',
								}"
							>
								<arco-carousel-item v-for="(image, index) in item.districtsList.imgList">
									<arco-image
										height="436"
										width="100%"
										:src="image"
										:preview="false"
										:style="{
											borderRadius: '4px 4px 0 0',
										}"
										@click="previewImg(index, item.districtsList.imgList)"
									></arco-image>
								</arco-carousel-item>
							</arco-carousel>
							<div class="detail_wrap">
								<div class="row">
									<div class="col">
										<div class="label">所在商圈:</div>
										<div class="value">{{ item.districtsList?.businessEvaluateInfo.businessDistrictName }}</div>
									</div>
									<div class="col">
										<div class="label">所在位置:</div>
										<div class="value">{{ item.districtsList?.businessEvaluateInfo.location }}</div>
									</div>
									<div class="col">
										<div class="label">商圈面积:</div>
										<div class="value">{{ item.districtsList?.businessEvaluateInfo.area }}</div>
									</div>
								</div>
								<div class="row">
									<div class="col">
										<div class="label">功能分类:</div>
										<div class="value">{{ item.districtsList?.businessEvaluateInfo.functionCategory }}</div>
									</div>
									<div class="col">
										<div class="label">商圈定位:</div>
										<div class="value">{{ item.districtsList?.businessEvaluateInfo.positioning }}</div>
									</div>
								</div>
								<div class="val_item">
									<div class="label_row">主要项目:</div>
									<span class="value_row">
										{{ item.districtsList?.businessEvaluateInfo.mainProjects }}
									</span>
								</div>
								<div class="val_item" v-if="index == 0">
									<div class="label_row">商圈介绍:</div>
									<span
										@click="handlerCopy(item.districtsList?.businessEvaluateInfo.description)"
										class="value_row"
										@mouseenter="leftCopyShow = true"
										@mouseleave="leftCopyShow = false"
									>
										<span :class="{ value_hover: leftCopyShow }">
											{{ item.districtsList?.businessEvaluateInfo.description }}
										</span>
										<span v-if="leftCopyShow" class="copy_btn"> <icon-copy style="margin-right: 2px" />复制 </span>
									</span>
								</div>
								<div class="val_item" v-if="index == 1">
									<div class="label_row">商圈介绍:</div>
									<span
										@click="handlerCopy(item.districtsList?.businessEvaluateInfo.description)"
										class="value_row"
										@mouseenter="rightCopyShow = true"
										@mouseleave="rightCopyShow = false"
									>
										<span :class="{ value_hover: rightCopyShow }">
											{{ item.districtsList?.businessEvaluateInfo.description }}
										</span>
										<span v-if="rightCopyShow" class="copy_btn"> <icon-copy style="margin-right: 2px" />复制 </span>
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="double_box_wrap">
					<div class="title1">
						<div class="title">商圈评估表</div>
					</div>
					<div class="double_wrap">
						<div class="content_wrap" v-for="item in sixRingDateList" :key="item.id">
							<div class="table_wrap">
								<div class="table_row" v-for="item1 in labelInfo">
									<div class="label_wrap">
										{{ item1.label }}
									</div>
									<div class="value_wrap">
										{{ item.districtsList?.businessEvaluateInfo?.[item1.value] }}
									</div>
								</div>
								<div class="table_row_two" v-for="(itemRows, indexRows) in labelInfoRow" :key="indexRows">
									<div class="table_item" v-for="(itemRow, indexRow) in itemRows">
										<div class="label_">{{ itemRow.label }}</div>
										<div class="value_">{{ item.districtsList?.businessEvaluateInfo?.[itemRow.value] }}</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="box_wrap">
					<div class="left">
						<div class="single_wrap">
							<div class="title1">
								<div class="title">维度分析</div>
							</div>
							<div class="content_wrap" v-if="sixRingDateList.length == 1">
								<rat
									class="rat"
									:containerIds="'contianers' + sixRingDateList[0].id + 0"
									:polygonArr="sixRingDateList[0].districtsList.businessCoordinateList"
									style="width: 100%; height: 436px; border-radius: 4px"
								></rat>
								<div class="map_tip">
									<div class="business_name">
										{{ sixRingDateList[0].districtsList?.businessDistrictName }}
									</div>
									<div class="type_wrap">
										<div
											class="type_item"
											:class="{ type_item_active: index == sixRingDateList[0].districtsList.infoActiveIndex }"
											v-for="(type, index) in sixRingDateList[0].districtsList?.info"
											@click="sixRingDateList[0].districtsList.infoActiveIndex = index"
										>
											{{ type.type }}
										</div>
									</div>
									<arco-typography-paragraph
										class="type_desc"
										:ellipsis="{
											rows: 3,
											showTooltip: true,
										}"
									>
										{{ sixRingDateList[0].districtsList.info?.[sixRingDateList[0].districtsList.infoActiveIndex]?.allName }}
									</arco-typography-paragraph>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
						<div class="single_wrap">
							<div class="title1">
								<div class="title">商圈评估表</div>
							</div>
							<div class="content_wrap" v-if="sixRingDateList.length == 1">
								<div class="table_wrap">
									<div class="table_row" v-for="item in labelInfo">
										<div class="label_wrap">
											{{ item.label }}
										</div>
										<div class="value_wrap">
											{{ sixRingDateList[0].districtsList?.businessEvaluateInfo?.[item.value] }}
										</div>
									</div>
									<div class="table_row_two" v-for="(itemRows, indexRows) in labelInfoRow" :key="indexRows">
										<div class="table_item" v-for="(itemRow, indexRow) in itemRows">
											<div class="label_">{{ itemRow.label }}</div>
											<div class="value_">{{ sixRingDateList[0].districtsList?.businessEvaluateInfo?.[itemRow.value] }}</div>
										</div>
									</div>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
					<div class="right">
						<div class="single_wrap">
							<div class="title1">
								<div class="title">商圈介绍</div>
							</div>
							<div class="content_wrap" v-if="sixRingDateList.length == 1">
								<arco-carousel
									indicator-type="line"
									indicator-position="bottom"
									show-arrow="never"
									:style="{
										width: '100%',
										height: '436px',
									}"
								>
									<arco-carousel-item v-for="(image, index) in sixRingDateList[0].districtsList.imgList">
										<arco-image
											height="436"
											width="100%"
											:src="image"
											:preview="false"
											:style="{
												borderRadius: '4px 4px 0 0',
											}"
											@click="previewImg(index, sixRingDateList[0].districtsList.imgList)"
										></arco-image>
									</arco-carousel-item>
								</arco-carousel>
								<div class="detail_wrap">
									<div class="row">
										<div class="col">
											<div class="label">所在商圈:</div>
											<div class="value">{{ sixRingDateList[0].districtsList?.businessEvaluateInfo.businessDistrictName }}</div>
										</div>
										<div class="col">
											<div class="label">所在位置:</div>
											<div class="value">{{ sixRingDateList[0].districtsList?.businessEvaluateInfo.location }}</div>
										</div>
										<div class="col">
											<div class="label">商圈面积:</div>
											<div class="value">{{ sixRingDateList[0].districtsList?.businessEvaluateInfo.area }}</div>
										</div>
									</div>
									<div class="row">
										<div class="col">
											<div class="label">功能分类:</div>
											<div class="value">{{ sixRingDateList[0].districtsList?.businessEvaluateInfo.functionCategory }}</div>
										</div>
										<div class="col">
											<div class="label">商圈定位:</div>
											<div class="value">{{ sixRingDateList[0].districtsList?.businessEvaluateInfo.positioning }}</div>
										</div>
									</div>
									<div class="val_item">
										<div class="label_row">主要项目:</div>
										<span class="value_row">
											{{ sixRingDateList[0].districtsList?.businessEvaluateInfo.mainProjects }}
										</span>
									</div>
									<div class="val_item">
										<div class="label_row">商圈介绍:</div>
										<span
											@click="handlerCopy(sixRingDateList[0].districtsList?.businessEvaluateInfo.description)"
											class="value_row"
											@mouseenter="leftCopyShow = true"
											@mouseleave="leftCopyShow = false"
										>
											<span :class="{ value_hover: leftCopyShow }">
												{{ sixRingDateList[0].districtsList?.businessEvaluateInfo.description }}
											</span>
											<span v-if="leftCopyShow" class="copy_btn"> <icon-copy style="margin-right: 2px" />复制 </span>
										</span>
									</div>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
				</div>
			</template>
		</div>
	</div>
	<arco-image-preview-group v-model:visible="imgPreviewShow" v-model:current="imgPreviewCurrent" infinite :srcList="imgPreviewList" />
	<buildSelect key="all" v-model="dialogTableVisible" :selectedData="multipleSelection" :maxSelectNum="2" @confirm="handleBuildConfirm"></buildSelect>
	<!-- <buildSelect key="single_left" v-model="dialogSingleLeftVisible" :maxSelectNum="1" @confirm="handleBuildLeftConfirm"></buildSelect>
	<buildSelect key="single_right" v-model="dialogSingleRightVisible" :maxSelectNum="1" @confirm="handleBuildRightConfirm"></buildSelect> -->
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import { getBuildingLocation, getDictList } from '@/api/syt.js';
import getReport from '../../../../component/getReport/index.vue';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import descBg from '@/assets/images/shangYutong/buildInfo/desc_bg.png';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import people_icon from '@/assets/images/shangYutong/buildInfo/people_icon.png';
import vs from '@/assets/images/shangYutong/buildInfo/vs.png';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import buildSelect from '@/component/buildSelect/index.vue';
import echartPie from './echart/pie.vue';
import summary_bg from '@/assets/images/shangYutong/buildInfo/summary_bg.png';
import summary_icon from '@/assets/images/shangYutong/buildInfo/summary_icon.png';
import { useStore } from '../../../../store';
import rat from '@/RatMap.vue'; //地图
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const store = useStore();
const getReportRef = ref();
const loading = ref();
const dialogTableVisible = ref(false); //对话框显示
const dialogSingleLeftVisible = ref(false); //对话框显示
const dialogSingleRightVisible = ref(false); //对话框显示
const rate = ref([]);
const buildingTypes = ref([]);
const box_copyObj = ref('');
const box_copyObjTwo = ref('');
const multipleSelection = ref([]);
const imgPreviewShow = ref(false);
const imgPreviewCurrent = ref(0);
const imgPreviewList = ref([]);
// const multipleSelection = ref([
// 	{
// 		id: '1833701603456532482',
// 	},
// 	{ id: '1833706846806294530' },
// ]);
const leftData = ref({});
const rightData = ref({});
const tableDataLeft = ref([]);
const tableDataRight = ref([]);
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const tableColumns = [
	{
		title: '资产名称',
		dataIndex: 'buildingName',
		width: '150',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '资产类型',
		dataIndex: 'buildingType',
		width: '100',
	},
	{
		title: '地址',
		dataIndex: 'street',
		width: '300',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '建筑面积',
		dataIndex: 'buildingSize',
		width: '100',
	},
	{
		title: '维护情况',
		dataIndex: 'maintenance',
		width: '100',
	},
	{
		title: '单价',
		dataIndex: 'absoluteValue',
		width: '100',
	},
];
const leftCopyShow = ref(false);
const rightCopyShow = ref(false);
// 商圈评估表横向
const labelInfoRow = ref([
	[
		{
			label: '商业设施齐全程度',
			value: 'facilityLevel',
		},
		{
			label: '商业覆盖度',
			value: 'businessCover',
		},
	],
	[
		{
			label: '商业氛围',
			value: 'businessAtmo',
		},
		{
			label: '交通网络',
			value: 'trafficNetwork',
		},
	],
	[
		{
			label: '交通拥堵情况',
			value: 'trafficJam',
		},
		{
			label: '人流量',
			value: 'peopleTraffic',
		},
	],
	[
		{
			label: '消费人群特征',
			value: 'spendPeopleFeature',
		},
		{
			label: '配套设施',
			value: 'facilitySupport',
		},
	],
	[
		{
			label: '商圈管理',
			value: 'businessManage',
		},
		{
			label: '竟争品牌数量',
			value: 'brandCompeteNum',
		},
	],
	[
		{
			label: '竞争形式',
			value: 'businessCompete',
		},
		{
			label: '总分',
			value: 'totalScore',
		},
	],
]);
// 商圈评估表纵向
const labelInfo = ref([
	{
		label: '商圈亮点',
		value: 'businessHighlight',
	},
	{
		label: '商圈不足',
		value: 'businessDefect',
	},
	{
		label: '商圈评价',
		value: 'businessEvaluateDesc',
	},
]);

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
});

function previewImg(index, list) {
	imgPreviewShow.value = true;
	imgPreviewCurrent.value = index;
	imgPreviewList.value = list;
}
//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}
//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getBuildingLocation({ buildingIds: obj.ids }).then((res) => {
		sixRingDateList.value = [];
		tableDataLeft.value = [];
		tableDataRight.value = [];
		if (res.code === 200) {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			multipleSelection.value = obj.arr;
			sixRingDateList.value = res.data;
			handleUpdate(1);
			if (res.data && res.data.length == 1) {
				tableDataLeft.value.push(obj.arr[0]);
			} else if (res.data && res.data.length == 2) {
				tableDataLeft.value.push(obj.arr[0]);
				tableDataRight.value.push(obj.arr[1]);
			}
			loading.value.close();
		}
	});
}
const sixRingDateList = ref([]);
// const sixRingDateList = ref([
// 	//商圈数据
// 	{
// 		districtsList: {
// 			// //商圈数据
// 			businessDistrictName: '',
// 			range: '',
// 			activeUrl: [],
// 			imageActiveIndex: 0,
// 			businessEvaluateInfo: {},
// 			imgList: [],
// 			businessCoordinateList: [],
// 			info: [],
// 			infoActiveIndex: 0,
// 		},
// 		buildBusinessDistrictAvgList: [],
// 		buildBusinessDistrictList: [],
// 	},
// ]);
// 确定
const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		sixRingDateList.value = [];
		tableDataLeft.value = [];
		tableDataRight.value = [];
		emit('handleBuildingId', { ids: null, arr: [] });
		return;
	} else {
		let ids = multipleSelection.value.map((item) => item.id).join(',');
		getBuildingLocation({ buildingIds: ids }).then((res) => {
			sixRingDateList.value = [];
			tableDataLeft.value = [];
			tableDataRight.value = [];
			if (res.code === 200) {
				emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
				sixRingDateList.value = res.data;
				handleUpdate();
			}
			dialogTableVisible.value = false;
		});
	}
};
//组装数据
function handleUpdate(type) {
	sixRingDateList.value.forEach((element, index) => {
		if (!type) {
			multipleSelection.value.forEach((item) => {
				if (item.id === element.id && index === 0) {
					tableDataLeft.value.push(item);
				}
				if (item.id === element.id && index === 1) {
					tableDataRight.value.push(item);
				}
			});
		}
		let businessCoordinateLists = [];
		let imgUrlList = [];
		let imgUrl = element.locationAgg.buildingLocation.businessEvaluateInfo.businessDistrictPics.split(',');
		imgUrl.forEach((item) => {
			imgUrlList.push(`${store.imagePathPrefix}${item}`);
		});
		element.locationAgg.buildingLocation.businessCoordinateList.forEach((items) => {
			businessCoordinateLists.push([items.lng, items.lat]);
		});
		element.districtsList = {
			businessDistrictName: element.locationAgg.buildingLocation.businessEvaluateInfo?.businessDistrictName || '', // 资产名称
			range: element.locationAgg.buildingLocation.businessEvaluateInfo.businessDistrictInfo || '', // 商圈介绍
			businessEvaluateInfo: element.locationAgg.buildingLocation.businessEvaluateInfo, // 商圈评估表
			imgList: imgUrlList || [], // 商圈图片
			imageActiveIndex: 0, // 商圈图片下标
			activeUrl: [imgUrlList[0]] || [], // 商圈图片
			businessCoordinateList: businessCoordinateLists || [], // 商圈坐标
			info: element.locationAgg.info || [], // 商圈信息
			infoActiveIndex: 0, // 商圈信息下标
		};
	});
}
// 获取字典
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	save();
}
function handleBuildLeftConfirm(data) {
	multipleSelection.value[0] = data[0];
	save();
}
function handleBuildRightConfirm(data) {
	multipleSelection.value[1] = data[0];
	save();
}
function clear(type) {
	if (type == 'left') {
		tableDataLeft.value = [];
		multipleSelection.value.shift();
	} else {
		tableDataRight.value = [];
		multipleSelection.value.pop();
	}
	save();
}
// 复制
function handlerCopy(text) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(text)
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = text;
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}
</script>
<style lang="less" scoped>
.comparison_box {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.common_wrap {
		padding: 20px 16px;
		background-color: #fff;
		display: flex;
		gap: 16px;
		border-radius: 0px 4px 4px 4px;
		.left_empty_wrap,
		.left_content_wrap,
		.right_empty_wrap,
		.right_content_wrap {
			flex: 1;
		}
		.left_empty_wrap,
		.right_empty_wrap {
			border: 1px solid #e5e6eb;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-radius: 4px;
			padding: 12px 0;
			.icon {
				width: 64px;
				height: 64px;
			}
		}
		.title_wrap {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;
		}
	}
	.chart_wrap {
		flex: 1;
		border-radius: 4px;
		margin-top: 16px;
		padding: 20px 16px 16px 16px;
		background-color: #fff;
		.header_wrap {
			display: flex;
			align-items: center;
			margin-bottom: 16px;
			.line {
				width: 4px;
				height: 14px;
				background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
				border-radius: 4px;
			}
			.title {
				color: #1d2129;
				font-size: 20px;
				font-weight: 600;
				margin-left: 8px;
				margin-right: 20px;
				line-height: 28px;
			}
		}
		.double_box_wrap {
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			margin-bottom: 16px;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.double_wrap {
				display: flex;
				padding: 20px 16px;
				box-sizing: border-box;
				gap: 16px;
				.content_wrap {
					flex: 1;
					position: relative;
					:deep(.rat) {
						.amap-logo {
							display: none !important;
						}
						.amap-copyright {
							display: none !important;
						}
					}
					.map_tip {
            box-sizing: border-box;
            position: absolute;
            left: 16px;
            bottom: 16px;
            padding: 20px;
            margin: 0 auto;
            width: calc(100% - 32px);
            background: #ffffffcc;
            backdrop-filter: blur(20px);
            box-shadow: 0px 2px 8px 0px #0000001f;
            border-radius: 4px;
            border: 1px solid #ffffff;
						.business_name {
							font-size: 20px;
							font-weight: 600;
							line-height: 28px;
							color: #1d2129;
						}
						.type_wrap {
							margin-top: 12px;
							margin-bottom: 8px;
							display: flex;
							flex-wrap: wrap;
							gap: 8px;
							.type_item {
								height: 32px;
								padding: 0 12px;
								display: flex;
								align-items: center;
								justify-content: center;
								background: #f2f3f5;
								color: #86909c;
								border-radius: 100px;
								cursor: pointer;
							}
							.type_item_active {
								background: #e8f3ff;
								color: #1868f1;
							}
						}
						.type_desc {
							font-size: 14px;
							font-weight: 400;
							line-height: 22px;
							color: #4e5969;
							// display: -webkit-box;
							// -webkit-box-orient: vertical;
							// -webkit-line-clamp: 3; /* 限制显示的行数 */
							// overflow: hidden;
							// text-overflow: ellipsis;
						}
					}
					.detail_wrap {
						padding: 20px;
						box-sizing: border-box;
						border-radius: 0 0 4px 4px;
						background: #f7f8fa;
						font-size: 14px;
						line-height: 22px;
						display: flex;
						flex-direction: column;
						gap: 12px;
						.row {
							display: flex;
							gap: 48px;
							.col {
								width: 158px;
								display: flex;
								gap: 8px;
								.label {
									color: #1d2129;
									min-width: 60px;
								}
								.value {
									color: #4e5969;
								}
							}
						}
						.val_item {
							display: flex;
							gap: 8px;
							.label_row {
								color: #1d2129;
								min-width: 60px;
							}
							.value_row {
								color: #4e5969;
								flex: 1;
								cursor: pointer;
								.copy_btn {
									color: #1868f1;
									margin-left: 4px;
								}
								.value_hover {
									background-color: #e8f3ff;
								}
							}
						}
					}
					.table_wrap {
						border: 1px solid #e5e6eb;
						border-bottom: 0px;
						border-radius: 4px;
						.table_row {
							display: flex;
							height: 90px;
							.label_wrap {
								min-width: 192px;
								display: flex;
								align-items: center;
								border-right: 1px solid #e5e6eb;
								border-bottom: 1px solid #e5e6eb;
								padding: 0 16px;
								background: #f7f8fa;
								font-size: 14px;
								font-weight: 600;
								color: #1d2129;
							}
							.value_wrap {
								flex: 1;
								display: flex;
								align-items: center;
								padding: 0 16px;
								border-bottom: 1px solid #e5e6eb;
								font-size: 14px;
								font-weight: 400;
								color: #4e5969;
								line-height: 22px;
							}
						}
						.table_row_two {
							display: flex;
							height: 40px;
							.table_item {
								flex: 1;
								display: flex;
								.label_ {
									min-width: 192px;
									display: flex;
									align-items: center;
									padding: 0 16px;
									background: #f7f8fa;
									font-size: 14px;
									font-weight: 600;
									color: #1d2129;
									border-right: 1px solid #e5e6eb;
									border-bottom: 1px solid #e5e6eb;
								}
								.value_ {
									flex: 1;
									display: flex;
									align-items: center;
									padding: 0 16px;
									border-right: 1px solid #e5e6eb;
									border-bottom: 1px solid #e5e6eb;
									font-size: 14px;
									font-weight: 400;
									color: #1868f1;
								}
								.value_:nth-child(2n) {
									border-right: none;
								}
							}
						}
					}
				}
			}
		}
		.box_wrap {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			gap: 16px;
			.left,
			.right {
				width: calc(50% - 8px);
				display: flex;
				flex-direction: column;
				gap: 16px;
			}
			.single_wrap {
				box-sizing: border-box;
				min-height: 300px;
				border: 1px solid #e5e6eb;
				border-radius: 4px;

				display: flex;
				flex-direction: column;
				.title1 {
					box-sizing: border-box;
					padding: 0 20px;
					width: 100%;
					height: 48px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #f7f8fa;
					border-bottom: 1px solid #e5e6eb;
					.title {
						font-size: 16px;
						font-weight: 600;
						color: #1d2129;
					}
				}
				.content_wrap {
					flex: 1;
					padding: 20px 16px;
					box-sizing: border-box;
					position: relative;
					:deep(.rat) {
						.amap-logo {
							display: none !important;
						}
						.amap-copyright {
							display: none !important;
						}
					}
					.map_tip {
            box-sizing: border-box;
            position: absolute;
            left: 32px;
            bottom: 32px;
            padding: 20px;
            margin: 0 auto;
            width: calc(100% - 64px);
            background: #ffffffcc;
            backdrop-filter: blur(20px);
            box-shadow: 0px 2px 8px 0px #0000001f;
            border-radius: 4px;
            border: 1px solid #ffffff;
						.business_name {
							font-size: 20px;
							font-weight: 600;
							line-height: 28px;
							color: #1d2129;
						}
						.type_wrap {
							margin-top: 12px;
							margin-bottom: 8px;
							display: flex;
							flex-wrap: wrap;
							gap: 8px;
							.type_item {
								height: 32px;
								padding: 0 12px;
								display: flex;
								align-items: center;
								justify-content: center;
								background: #f2f3f5;
								color: #86909c;
								border-radius: 100px;
								cursor: pointer;
							}
							.type_item_active {
								background: #e8f3ff;
								color: #1868f1;
							}
						}
						.type_desc {
							font-size: 14px;
							font-weight: 400;
							line-height: 22px;
							color: #4e5969;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 3; /* 限制显示的行数 */
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
					.detail_wrap {
						padding: 20px;
						box-sizing: border-box;
						border-radius: 0 0 4px 4px;
						background: #f7f8fa;
						font-size: 14px;
						line-height: 22px;
						display: flex;
						flex-direction: column;
						gap: 12px;
						.row {
							display: flex;
							gap: 48px;
							.col {
								width: 158px;
								display: flex;
								gap: 8px;
								.label {
									color: #1d2129;
									min-width: 60px;
								}
								.value {
									color: #4e5969;
								}
							}
						}
						.val_item {
							display: flex;
							gap: 8px;
							.label_row {
								color: #1d2129;
								min-width: 60px;
							}
							.value_row {
								color: #4e5969;
								flex: 1;
								cursor: pointer;
								.copy_btn {
									color: #1868f1;
									margin-left: 4px;
								}
								.value_hover {
									background-color: #e8f3ff;
								}
							}
						}
					}
					.table_wrap {
						border: 1px solid #e5e6eb;
						border-bottom: 0px;
						border-radius: 4px;
						.table_row {
							display: flex;
							height: 90px;
							.label_wrap {
								min-width: 192px;
								display: flex;
								align-items: center;
								border-right: 1px solid #e5e6eb;
								border-bottom: 1px solid #e5e6eb;
								padding: 0 16px;
								background: #f7f8fa;
								font-size: 14px;
								font-weight: 600;
								color: #1d2129;
							}
							.value_wrap {
								flex: 1;
								display: flex;
								align-items: center;
								padding: 0 16px;
								border-bottom: 1px solid #e5e6eb;
								font-size: 14px;
								font-weight: 400;
								color: #4e5969;
								line-height: 22px;
							}
						}
						.table_row_two {
							display: flex;
							height: 40px;
							.table_item {
								flex: 1;
								display: flex;
								.label_ {
									min-width: 192px;
									display: flex;
									align-items: center;
									padding: 0 16px;
									background: #f7f8fa;
									font-size: 14px;
									font-weight: 600;
									color: #1d2129;
									border-right: 1px solid #e5e6eb;
									border-bottom: 1px solid #e5e6eb;
								}
								.value_ {
									flex: 1;
									display: flex;
									align-items: center;
									padding: 0 16px;
									border-right: 1px solid #e5e6eb;
									border-bottom: 1px solid #e5e6eb;
									font-size: 14px;
									font-weight: 400;
									color: #1868f1;
								}
								.value_:nth-child(2n) {
									border-right: none;
								}
							}
						}
					}
				}
				.empty_wrap {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: 400;
					color: #86909c;
					img {
						width: 80px;
						height: 80px;
					}
				}
			}
		}
	}

	.arco-btn-size-medium {
		border-radius: 4px;
	}
}
</style>
