<template>
	<div>
		<div class="tab_box">
			<!-- <div class="title">交易计算</div> -->
			<div class="tab" :class="activeName === route.name ? 'tabAct' : ''" v-for="route in routes" @click="handleTabClick(route)" :key="route.name">
				{{ route.name }}
			</div>
		</div>
		<component :is="componentNames" />
	</div>
</template>

<script setup>
import { ref } from 'vue';
import lease from './lease.vue';
import buy from './buy.vue';

const routes = [
	{
		name: '租赁',
		componentName: lease,
	},
	{
		name: '购买',
		componentName: buy,
	},
];
const componentNames = ref(lease);
const activeName = ref('购买');

const handleTabClick = (item) => {
	componentNames.value = item.componentName;
	activeName.value = item.name;
};
</script>

<style lang="scss" scoped>
.tab_box {
	width: 100%;
	height: 56px;
	box-sizing: border-box;
	font-size: 14px;
	font-weight: 600;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	background-color: rgba(255, 255, 255, 1);

	.title {
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		margin-right: 15px;
		color: #1a1a1a;
	}

	.tab {
		width: 60px;
		height: 56px;
		padding: 0 14px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		cursor: pointer;

		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		color: #666666;
	}

	.tabAct {
		width: 60px;
		height: 56px;
		font-size: 14px;
		font-weight: 700;
		line-height: 22px;
		color: #1a1a1a;

		&::after {
			content: '';
			width: 24px;
			height: 3px;
			position: absolute;
			bottom: 0;
			background-color: rgba(3, 93, 255, 1);
		}
	}
}
</style>
