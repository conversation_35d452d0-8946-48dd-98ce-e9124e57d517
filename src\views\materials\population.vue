<template>
	<div class="content">
		<div>
			<el-row :gutter="30">
				<el-col :span="6">
					<el-cascader
						style="width: 345px"
						:key="myCascader"
						placeholder="请选择城市"
						@change="handleChange"
						:options="$vuexStore.state.cityArray"
						:props="{ value: 'label' }"
					>
					</el-cascader>
				</el-col>
				<el-col :span="6">
					<el-select v-model="buildValue" placeholder="选择资产类型" style="width: 345px">
						<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" />
					</el-select>
				</el-col>
				<el-col :span="6" style="width: 345px">
					<el-input v-model="keyword" placeholder="请输入关键字"></el-input>
				</el-col>

				<el-col :span="6" style="display: flex; justify-content: flex-end">
					<el-button @click="search" type="primary">查询</el-button>
					<el-button @click="reset" type="primary">重置</el-button>
				</el-col>
			</el-row>
			<el-table :data="tableData" style="width: 80%" @selection-change="handleSelectionChange" stripe>
				<el-table-column type="selection" width="55" ref="multipleTableRef" />
				<el-table-column prop="name" label="名字" />
				<el-table-column prop="deType" label="资产类型" />
			</el-table>
			<div style="display: flex; justify-content: space-around; align-items: center">
				<el-pagination @current-change="pageChange" :current-page="currentPage" small background layout="prev, pager, next" :total="total" />
				<el-button style="margin-top: 20px" size="small" type="primary" @click="comparison">对比</el-button>
			</div>
		</div>
		<div v-if="leftData" style="margin-top: 15px">
			<el-row :gutter="20">
				<el-col :span="11">
					<el-text class="Title" tag="p">{{ leftData.building_name }}</el-text>
					<el-row :gutter="30">
						<el-col :span="5">
							<el-descriptions title="关键事实" column="1" direction="horizontal">
								<el-descriptions-item label="总人口">{{ leftData?.totalPopulation }}</el-descriptions-item>
								<el-descriptions-item label="年龄中位数">{{ leftData?.ageMid }}</el-descriptions-item>
								<el-descriptions-item label="家庭数">{{ leftData?.householdsNum }}</el-descriptions-item>
								<el-descriptions-item label="人均支配(万元)">{{ leftData?.disposableIncome }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="5">
							<el-descriptions title="教育" column="1">
								<el-descriptions-item label="高中以下">{{ leftData?.educationalLevel1 }}</el-descriptions-item>
								<el-descriptions-item label="高中">{{ leftData?.educationalLevel2 }}</el-descriptions-item>
								<el-descriptions-item label="专科">{{ leftData?.educationalLevel3 }}</el-descriptions-item>
								<el-descriptions-item label="学士/硕士/博士">{{ leftData?.educationalLevel4 }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="5">
							<el-descriptions title="就业情况" column="1">
								<el-descriptions-item label="白领">{{ leftData?.whiteCollar }}</el-descriptions-item>
								<el-descriptions-item label="蓝领">{{ leftData?.blueCollar }}</el-descriptions-item>
								<el-descriptions-item label="公共事业">{{ leftData?.publicUtilities }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="年龄情况" column="1">
								<el-descriptions-item label="0-14岁">{{ leftData?.ageRange1 }}</el-descriptions-item>
								<el-descriptions-item label="15-64岁">{{ leftData?.ageRange2 }}</el-descriptions-item>
								<el-descriptions-item label="65岁以上">{{ leftData?.ageRange3 }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
					</el-row>
				</el-col>
				<el-divider border-style="solid" direction="vertical" style="height: 200px; margin-right: 60px" />
				<el-col :span="11">
					<el-text class="Title" tag="p">{{ rightData.building_name }}</el-text>
					<el-row :gutter="20">
						<el-col :span="6">
							<el-descriptions title="关键事实" column="1" direction="horizontal">
								<el-descriptions-item label="总人口">{{ rightData?.totalPopulation }}</el-descriptions-item>
								<el-descriptions-item label="年龄中位数">{{ rightData?.ageMid }}</el-descriptions-item>
								<el-descriptions-item label="家庭数">{{ rightData?.householdsNum }}</el-descriptions-item>
								<el-descriptions-item label="人均支配(万元)">{{ rightData?.disposableIncome }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="教育" column="1">
								<el-descriptions-item label="高中以下">{{ rightData?.educationalLevel1 }}</el-descriptions-item>
								<el-descriptions-item label="高中">{{ rightData?.educationalLevel2 }}</el-descriptions-item>
								<el-descriptions-item label="专科">{{ rightData?.educationalLevel3 }}</el-descriptions-item>
								<el-descriptions-item label="学士/硕士/博士">{{ rightData?.educationalLevel4 }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="就业情况" column="1">
								<el-descriptions-item label="白领">{{ rightData?.whiteCollar }}</el-descriptions-item>
								<el-descriptions-item label="蓝领">{{ rightData?.blueCollar }}</el-descriptions-item>
								<el-descriptions-item label="公共事业">{{ rightData?.publicUtilities }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
						<el-col :span="6">
							<el-descriptions title="年龄情况" column="1">
								<el-descriptions-item label="0-14岁">{{ rightData?.ageRange1 }}</el-descriptions-item>
								<el-descriptions-item label="15-64岁">{{ rightData?.ageRange2 }}</el-descriptions-item>
								<el-descriptions-item label="65岁以上">{{ rightData?.ageRange3 }}</el-descriptions-item>
							</el-descriptions>
						</el-col>
					</el-row>
				</el-col>
			</el-row>
		</div>
	</div>
</template>
<script setup>
import { ref } from 'vue';
import http from '@/utils/http';
import { getSixRingList, getMapList } from '@/api/materials.js';
import { ElMessage } from 'element-plus';
import { getPopulationList } from '@/api/materials.js';
const city = ref('');
const county = ref('');
const myCascader = ref(0);
const rate = ['S', 'A+', 'A', 'B+', 'B', 'C'];
const buildingTypes = ['写字楼', '零售', '产业园区', '仓储物流', '酒店', '长租公寓', '医疗', '综合市场'];
const buildValue = ref('');
const keyword = ref('');
const tableData = ref([]);
const currentPage = ref(1);
const total = ref('');
const handleChange = (val) => {
	console.log(val, 'ss');
	city.value = val[0];
	county.value = val[1];
};
const reset = () => {
	myCascader.value++;
	city.value = '';
	county.value = '';
	buildValue.value = '';
	keyword.value = '';
	currentPage.value = 1;
};
const search = async () => {
	// const res = await http.get('/api/amber/getMap', {
	// 	params: { city: city.value, county: county.value, de_type: buildValue.value, keywords: keyword.value, currentPage: currentPage.value },
	// });
	// tableData.value = res.data.records;
	// total.value = res.total;
	// console.log(tableData.value, 'res');
	// if(buildValue.value == '零售'){
	// 	buildValue.value = '购物中心,百货大楼'
	// }
	// if(buildValue.value == '医疗'){
	// 	buildValue.value = '医疗康养'
	// }
	// if(buildValue.value == '长租公寓'){
	// 	buildValue.value = '公寓'
	// }
	const queryParams = {
		city: city.value,
		county: county.value,
		deType: buildValue.value,
		keywords: keyword.value,
		currentPage: currentPage.value,
		pageSize: currentPage.value,
		type: 0,
	};
	console.log(buildValue.value, 'buildingTypesValue1');
	console.log(queryParams.deType, 'queryParams.value1');
	if (buildValue.value == '零售') {
		queryParams.deType = '购物中心,百货大楼';
		console.log(1);
	}
	if (buildValue.value == '医疗') {
		queryParams.deType = '医疗康养';
	}
	if (buildValue.value == '长租公寓') {
		queryParams.deType = '公寓';
	}
	await getMapList(queryParams)
		.then((res) => {
			tableData.value = res.data.records;
			total.value = res.data.total;
			console.log(tableData.value, 'res');
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const multipleSelection = ref([]);
let leftData = ref();
let rightData = ref();
const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};

const comparison = async () => {
	console.log(multipleSelection.value, 'dsad');
	if (multipleSelection.value.length !== 2) {
		ElMessage.warning('只能选择两个！！！');
		return;
	}
	const idarray = [];
	multipleSelection.value.map((item) => {
		idarray.push(item.id);
	});
	console.log(idarray, 'idarray');
	const ids = idarray.join(',');
	await getPopulationList({ ids: ids })
		.then((res) => {
			console.log(res, 'res77');
			leftData.value = res.data[0];
			rightData.value = res.data[1];
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const pageChange = (val) => {
	currentPage.value = val;
	search();
};
</script>

<style lang="less" scoped>
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}
.content {
	margin: 20px 20px 0 20px;
	padding: 20px 40px 0 40px;
}

.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	text-align: center;
	margin-bottom: 15px;
}
</style>
