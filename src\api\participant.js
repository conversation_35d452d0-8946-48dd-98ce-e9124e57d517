// 参与者相关接口
import { fetch } from "./http";

export function getActorList(params) { //查经纪人
	return fetch({
		url: "/api/amber/getBrokerList",
		method: "get",
		params: params,
		loading: true,
	});
}

export function getPropertyList(params) { //查管理人，产权人
	return fetch({
		url: "/sm/v1/building/property-manage-list",
		method: "get",
		params: params,
		loading: true,
	});
}

export function getBrokerInfo(params) { //经纪人首页楼宇
	return fetch({
		url: "/api/amber/getBrokerBuildingByUserId",
		method: "get",
		params: params,
		loading: true,
	});
}

export function getBuildBook(params) { //经纪人首页楼宇
	return fetch({
		url: "/api/maps/building-book-list-all",
		method: "get",
		params: params,
		loading: true,
	});
}

export function companyList(params) { //公司列表查询
	return fetch({
		url: "/sm/v1/building/company-list",
		method: "get",
		params: params,
		loading: true,
	});
}
