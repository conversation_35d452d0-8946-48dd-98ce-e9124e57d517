import { fetch } from './http';

export function financialList(params) {
	//金融市场图表
	return fetch({
		url: '/sm/v1/financereport/list',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function financeDataList(params) {
	//金融市场数据
	return fetch({
		url: '/api/common/financial-data',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function getAbsListApi(params) {
	//ABS管理-分页列表查询
	return fetch({
		url: '/sm/v1/abs/abs-list',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function getSelector(params) {
	//abs-获取选择器数据
	return fetch({
		url: '/sm/v1/abs/abs-select',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function getReitsListApi(params) {
	//reits管理-分页列表查询
	return fetch({
		url: '/sm/v1/abs/reits-list',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function realEstateFinance() {
	//行业概况
	return fetch({
		url: '/api/syt/realEstateFinance/overview',
		method: 'get',
	});
}
