import { fetch } from './http';

export function getCouponList(params) {
	//所有权益
	return fetch({
		url: '/api/coupon/list',
		method: 'GET',
		params: params,
		loading: true,
	});
}

export function getCouponListByCity(params) {
	//所有权益
	return fetch({
		url: '/api/coupon/list-by-city',
		method: 'GET',
		params: params,
		loading: true,
	});
}

export function orderOreate(params) {
	//订单支付
	return fetch({
		url: '/api/order/create',
		method: 'POST',
		data: params,
		loading: true,
	});
}
export function orderStatus(orderId) {
	//查询订单状态
	return fetch({
		url: `/api/order/status/${orderId}`,
		method: 'GET',
		params: {},
		loading: false,
	});
}

export function addShoppingCart(params) {
	//添加购物车
	return fetch({
		url: '/api/shopping-cart/add',
		method: 'POST',
		data: params,
	});
}

export function getShoppingCart(params) {
	//购物车列表
	return fetch({
		url: '/api/shopping-cart/list',
		method: 'GET',
		params: params,
	});
}

export function getDiscount(params) {
	//生成优惠金额
	return fetch({
		url: '/api/order/discount',
		method: 'POST',
		data: params,
	});
}

export function editCount(data) {
	//编辑购物车数量
	return fetch({
		url: '/api/shopping-cart/edit-count',
		method: 'PUT',
		data: data,
	});
}

export function deleteShoppingCart(data) {
	//删除购物车
	return fetch({
		url: '/api/shopping-cart/delete',
		method: 'DELETE',
		data: data,
		loading: false,
	});
}

export function receiveCoupon(params) {
	//领取新人体验券
	return fetch({
		url: '/api/user-coupon/new-user',
		method: 'POST',
		data: params,
	});
}
