<template>
	<div class="comparison_box">
		<div class="common_wrap">
			<div class="left_empty_wrap" v-if="tableDataLeft.length == 0">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择资产
				</arco-button>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0" class="left_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('left')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataLeft"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0 && tableDataRight.length == 0" class="right_empty_wrap">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择对比资产
				</arco-button>
			</div>
			<div v-if="tableDataRight && tableDataRight.length > 0" class="right_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('right')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataRight"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
		</div>
		<div class="chart_wrap">
			<div class="header_wrap">
				<div class="left">
					<span class="line"></span>
					<span class="title">信用风险</span>
				</div>
				<div class="right_top" v-if="riskData?.length > 0">
					<div class="title_btn" @click="handleDownLoadRisk">下载风险评估报告<img src="@/assets/download.png" alt="" /></div>
				</div>
			</div>
			<div class="echars_main" v-if="tableDataLeft?.length > 0 && tableDataRight?.length > 0">
				<div class="box_" v-for="item in riskData" :key="item.id">
					<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
						<div class="title1">
							<div class="title">基本信息</div>
						</div>
						<div class="content_wrap" v-if="tableDataLeft.length > 0 || tableDataRight.length > 0">
							<div class="info_wrap">
								<arco-carousel
									indicator-type="never"
									v-if="riskData?.length > 0"
									:style="{
										width: '800',
										height: '240px',
									}"
								>
									<arco-carousel-item>
										<div class="slideshow">
											<div class="boxss">
												<div class="head-left-details">
													<img :src="`${proxyAddress}${item.creditRiskAsset.imgUrlList[0]}`" alt="" />
													<div class="head-right_details">
														<div class="right_details">
															<div class="top_tent">
																<div class="top_text">{{ item.creditRiskAsset.name }}</div>
																<div class="topt_text" :style="handlebuildingRateColor(item.creditRiskAsset.assetRatings)">
																	{{ item.creditRiskAsset.assetRatings }}级
																</div>
																<div class="topth_text">{{ item.creditRiskAsset.assetType }}</div>
															</div>
															<div class="center_tent">
																<div class="center_tent_left">
																	<el-icon><Location /></el-icon>
																	<div class="center_tent_left_text">{{ item.creditRiskAsset.address }}</div>
																</div>
															</div>
														</div>

														<div class="items_box_border">
															<div class="index_left_item">
																<div class="value">{{ item.creditRiskAsset.buildYear }}</div>
																<div class="title">建成年份</div>
																<div class="lingIndex"></div>
															</div>
															<div class="index_left_item">
																<div class="value" :style="handleKeyValue(item.creditRiskAsset.maintenance, '维护情况')">
																	{{ item.creditRiskAsset.maintenance }}
																</div>
																<div class="title">维护情况</div>
																<div class="lingIndex"></div>
															</div>
															<div class="index_left_item">
																<div class="value" :style="handleKeyValue(item.creditRiskAsset.regionalPotential, '区域潜力')">
																	{{ item.creditRiskAsset.regionalPotential }}
																</div>
																<div class="title">区域潜力</div>
															</div>
															<div class="index_left_item">
																<div class="value" :style="handleKeyValue(item.creditRiskAsset.businessDynamism, '商业活力')">
																	{{ item.creditRiskAsset.businessDynamism }}
																</div>
																<div class="title">商业活力</div>
																<div class="lingIndex"></div>
															</div>
															<div class="index_left_item">
																<div class="value">{{ item.creditRiskAsset.spendingPower }}</div>
																<div class="title">人均消费(元)</div>
																<div class="lingIndex"></div>
															</div>
															<div class="index_left_item">
																<div class="value">
																	{{
																		item.creditRiskAsset.presentValueOfAsset
																			? parseFloat(item.creditRiskAsset.presentValueOfAsset).toString().substring(0, 4)
																			: 0
																	}}
																</div>
																<div class="title">单价(亿)</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</arco-carousel-item>
								</arco-carousel>
							</div>
						</div>

						<!-- <div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div> -->
					</div>
				</div>
				<div class="box_" v-for="item in riskData" :key="item.id">
					<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
						<div class="title1">
							<div class="title">权益指数</div>
						</div>
						<div class="content_wrap">
							<div class="left_box">
								<div class="left_item">
									<div class="item_qy">
										<div class="item_qy_title">市场法价格</div>
										<div class="item_qy_value">{{ item?.creditRisk?.marketPriceIndex }}万元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">收益法价格</div>
										<div class="item_qy_value">{{ item?.creditRisk?.incomePriceIndex }}万元</div>
									</div>

									<div class="item_qy">
										<div class="item_qy_title">市场法比例</div>
										<div class="item_qy_value">70%</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">收益法比例</div>
										<div class="item_qy_value">30%</div>
									</div>

									<div class="item_qy">
										<div class="item_qy_title">评估值</div>
										<div>{{ item?.creditRisk?.presentValueOfAsset }}亿元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">折旧</div>
										<div>{{ item?.creditRisk?.depreciation }}万元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">EBITDA</div>
										<div>{{ item?.creditRisk?.ebitda }}万元</div>
									</div>
								</div>
								<div class="left_items">
									<div ref="echartsContainer" style="width: 100%; height: 100%"></div>
								</div>
							</div>
						</div>
						<!-- <div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div> -->
					</div>
				</div>

				<div class="box_" v-for="item in riskData" :key="item.id">
					<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
						<div class="title1">
							<div class="title">风险评估</div>
						</div>
						<div class="content_wrap">
							<div class="details_bottom_container">
								<div class="details-bottom-container">
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.salePrice }}</div>
											<div class="name">价格（万元/㎡）</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price.png" alt="" />
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.icr || 0 }}</div>
											<div class="name">利息覆盖倍数ICR</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price1.png" alt="" />
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.incomeGrowthRate || 0 }}%</div>
											<div class="name">收入/租金增长率</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price2.png" alt="" />
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.capitalizationRate || 0 }}%</div>
											<div class="name">资本化率</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price3.png" alt="" />
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.ltv || 0 }}%</div>
											<div class="name">抵押率（LTV）</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price4.png" alt="" />
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.defaultRate || 0 }}%</div>
											<div class="name">违约率（租户）</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price5.png" alt="" />
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.rentSaleRatio || 0 }}%</div>
											<div class="name">租售价格比</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price6.png" alt="" />
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value" style="font-weight: 500">
												{{ handleoperations(item.creditRisk?.operationalRisk) || 0 }}
												<img
													v-if="
														(0 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 3) ||
														(4.5 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 6) ||
														(8 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 9)
													"
													src="@/assets/arrowtopimg.png"
													class="arrowtopimg"
													alt=""
												/>
												<img
													v-if="
														(3 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 4.5) ||
														(6 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 8) ||
														(9 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 10)
													"
													src="@/assets/arrowtopimg1.png"
													class="arrowtopimg"
													alt=""
												/>
											</div>
											<div class="name">运营风险</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price7.png" alt="" />
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.presentValueOfAsset }}</div>
											<div class="name">资产现值（亿元）</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price8.png" alt="" />
										</div>
									</div>
									<div class="card-details-boxs tax-v2">
										<div class="card_details">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.transactionCost }}</div>
												<div class="name">交易成本（万元）</div>
											</div>
											<div class="line"></div>
										</div>
										<div class="details_box_fee">
											<div class="box_fee">
												<div class="box_fee_circle">
													<div class="box_fee_circle_img">
														<img src="@/assets/infoCircle.png" alt="" />
													</div>
													<div class="box_fee_circle_si">税(万元)</div>
													<div class="tax_feetax">
														{{ item.creditRisk?.tax }}
													</div>
												</div>

												<div class="box_fee_circleFx">
													<div class="box_fee_title">费(万元)</div>
													<div class="box_fee_value">
														{{ item.creditRisk?.fee }}
													</div>
												</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price9.png" alt="" />
											</div>
										</div>
									</div>
									<div class="card-details-box">
										<div class="details_box">
											<div class="value">{{ item.creditRisk?.investmentAmount }}</div>
											<div class="name">总投资额(亿元)</div>
										</div>
										<div class="details_boxImg">
											<img src="@/assets/images/risks/price10.png" alt="" />
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="echars_main" v-else style="align-items: flex-start !important">
				<div class="echars_main_left">
					<div class="box_ box_Con" style="margin-bottom: 16px !important" v-for="item in riskData" :key="item.id">
						<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
							<div class="title1">
								<div class="title">基本信息</div>
							</div>
							<div class="content_wrap" v-if="tableDataLeft.length > 0 || tableDataRight.length > 0">
								<div class="info_wrap">
									<arco-carousel
										indicator-type="never"
										v-if="riskData?.length > 0"
										:style="{
											width: '800',
											height: '240px',
										}"
									>
										<arco-carousel-item>
											<div class="slideshow">
												<div class="boxss">
													<div class="head-left-details">
														<img :src="`${proxyAddress}${item.creditRiskAsset.imgUrlList[0]}`" alt="" />
														<div class="head-right_details">
															<div class="right_details">
																<div class="top_tent">
																	<div class="top_text">{{ item.creditRiskAsset.name }}</div>
																	<div class="topt_text" :style="handlebuildingRateColor(item.creditRiskAsset.assetRatings)">
																		{{ item.creditRiskAsset.assetRatings }}级
																	</div>
																	<div class="topth_text">{{ item.creditRiskAsset.assetType }}</div>
																</div>
																<div class="center_tent">
																	<div class="center_tent_left">
																		<el-icon><Location /></el-icon>
																		<div class="center_tent_left_text">{{ item.creditRiskAsset.address }}</div>
																	</div>
																</div>
															</div>

															<div class="items_box_border">
																<div class="index_left_item">
																	<div class="value">{{ item.creditRiskAsset.buildYear }}</div>
																	<div class="title">建成年份</div>
																	<div class="lingIndex"></div>
																</div>
																<div class="index_left_item">
																	<div class="value" :style="handleKeyValue(item.creditRiskAsset.maintenance, '维护情况')">
																		{{ item.creditRiskAsset.maintenance }}
																	</div>
																	<div class="title">维护情况</div>
																	<div class="lingIndex"></div>
																</div>
																<div class="index_left_item">
																	<div class="value" :style="handleKeyValue(item.creditRiskAsset.regionalPotential, '区域潜力')">
																		{{ item.creditRiskAsset.regionalPotential }}
																	</div>
																	<div class="title">区域潜力</div>
																</div>
																<div class="index_left_item">
																	<div class="value" :style="handleKeyValue(item.creditRiskAsset.businessDynamism, '商业活力')">
																		{{ item.creditRiskAsset.businessDynamism }}
																	</div>
																	<div class="title">商业活力</div>
																	<div class="lingIndex"></div>
																</div>
																<div class="index_left_item">
																	<div class="value">{{ formattedMoney(item.creditRiskAsset.spendingPower) }}</div>
																	<div class="title">人均消费(元)</div>
																	<div class="lingIndex"></div>
																</div>
																<div class="index_left_item">
																	<div class="value">
																		{{
																			item.creditRiskAsset.presentValueOfAsset
																				? parseFloat(item.creditRiskAsset.presentValueOfAsset).toString().substring(0, 4)
																				: 0
																		}}
																	</div>
																	<div class="title">单价(亿)</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</arco-carousel-item>
									</arco-carousel>
								</div>
							</div>

							<!-- <div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div> -->
						</div>
					</div>
					<div class="box_ box_Con" v-for="item in riskData" :key="item.id">
						<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
							<div class="title1">
								<div class="title">风险评估</div>
							</div>
							<div class="content_wrap">
								<div class="details_bottom_container">
									<div class="details-bottom-container">
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.salePrice }}</div>
												<div class="name">价格（万元/㎡）</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price.png" alt="" />
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.icr || 0 }}</div>
												<div class="name">利息覆盖倍数ICR</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price1.png" alt="" />
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.incomeGrowthRate || 0 }}%</div>
												<div class="name">收入/租金增长率</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price2.png" alt="" />
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.capitalizationRate || 0 }}%</div>
												<div class="name">资本化率</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price3.png" alt="" />
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.ltv || 0 }}%</div>
												<div class="name">抵押率（LTV）</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price4.png" alt="" />
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.defaultRate || 0 }}%</div>
												<div class="name">违约率（租户）</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price5.png" alt="" />
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.rentSaleRatio || 0 }}%</div>
												<div class="name">租售价格比</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price6.png" alt="" />
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value" style="font-weight: 500">
													{{ handleoperations(item.creditRisk?.operationalRisk) || 0 }}
													<img
														v-if="
															(0 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 3) ||
															(4.5 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 6) ||
															(8 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 9)
														"
														src="@/assets/arrowtopimg.png"
														class="arrowtopimg"
														alt=""
													/>
													<img
														v-if="
															(3 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 4.5) ||
															(6 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 8) ||
															(9 < item.creditRisk?.operationalRisk && item.creditRisk?.operationalRisk <= 10)
														"
														src="@/assets/arrowtopimg1.png"
														class="arrowtopimg"
														alt=""
													/>
												</div>
												<div class="name">运营风险</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price7.png" alt="" />
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.presentValueOfAsset }}</div>
												<div class="name">资产现值（亿元）</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price8.png" alt="" />
											</div>
										</div>
										<div class="card-details-boxs tax-v2">
											<div class="card_details">
												<div class="details_box">
													<div class="value">{{ item.creditRisk?.transactionCost }}</div>
													<div class="name">交易成本（万元）</div>
												</div>
												<div class="line"></div>
											</div>
											<div class="details_box_fee">
												<div class="box_fee">
													<div class="box_fee_circle">
														<div class="box_fee_circle_img">
															<img src="@/assets/infoCircle.png" alt="" />
														</div>
														<div class="box_fee_circle_si">税(万元)</div>
														<div class="tax_feetax">
															{{ item.creditRisk?.tax }}
														</div>
													</div>

													<div class="box_fee_circleFx">
														<div class="box_fee_title">费(万元)</div>
														<div class="box_fee_value">
															{{ item.creditRisk?.fee }}
														</div>
													</div>
												</div>
												<div class="details_boxImg">
													<img src="@/assets/images/risks/price9.png" alt="" />
												</div>
											</div>
										</div>
										<div class="card-details-box">
											<div class="details_box">
												<div class="value">{{ item.creditRisk?.investmentAmount }}</div>
												<div class="name">总投资额(亿元)</div>
											</div>
											<div class="details_boxImg">
												<img src="@/assets/images/risks/price10.png" alt="" />
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="box_" v-for="item in riskData" :key="item.id">
					<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
						<div class="title1">
							<div class="title">权益指数</div>
						</div>
						<div class="content_wrap">
							<div class="left_box">
								<div class="left_item">
									<div class="item_qy">
										<div class="item_qy_title">市场法价格</div>
										<div class="item_qy_value">{{ item?.creditRisk?.marketPriceIndex }}万元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">收益法价格</div>
										<div class="item_qy_value">{{ item?.creditRisk?.incomePriceIndex }}万元</div>
									</div>

									<div class="item_qy">
										<div class="item_qy_title">市场法比例</div>
										<div class="item_qy_value">70%</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">收益法比例</div>
										<div class="item_qy_value">30%</div>
									</div>

									<div class="item_qy">
										<div class="item_qy_title">评估值</div>
										<div>{{ item?.creditRisk?.presentValueOfAsset ? item?.creditRisk?.presentValueOfAsset : '0.00' }}亿元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">折旧</div>
										<div>{{ item?.creditRisk?.depreciation }}万元</div>
									</div>
									<div class="item_qy">
										<div class="item_qy_title">EBITDA</div>
										<div>{{ item?.creditRisk?.ebitda }}万元</div>
									</div>
								</div>
								<div class="left_items">
									<div ref="echartsContainer" style="width: 100%; height: 100%"></div>
								</div>
							</div>
						</div>
						<!-- <div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div> -->
					</div>
				</div>
			</div>
		</div>
	</div>
	<buildSelect key="all" v-model="dialogTableVisible" :selectedData="multipleSelection" :maxSelectNum="2" @confirm="handleBuildConfirm"></buildSelect>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import * as echarts from 'echarts';
import axios from 'axios';
import { getComparativePopulation, getCreditRisk } from '@/api/syt.js';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import buildSelect from '@/component/buildSelect/index.vue';
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const riskData = ref([]);
// 权益指数图表
const myChart = ref(null);
// echarts绑定DOM
const echartsContainer = ref(null);
// 图表前缀
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const loading = ref();
const dialogTableVisible = ref(false); //对话框显示
const multipleSelection = ref([]);
const tableDataLeft = ref([]);
const tableDataRight = ref([]);
const tableColumns = [
	{
		title: '资产名称',
		dataIndex: 'buildingName',
		width: '150',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '资产类型',
		dataIndex: 'buildingType',
		width: '100',
	},
	{
		title: '地址',
		dataIndex: 'street',
		width: '300',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '建筑面积',
		dataIndex: 'buildingSize',
		width: '100',
	},
	{
		title: '维护情况',
		dataIndex: 'maintenance',
		width: '100',
	},
	{
		title: '单价',
		dataIndex: 'absoluteValue',
		width: '100',
	},
];
const activeData = ref({});
const proportionValue = ref(0.7);
const proportionValue2 = ref(0.3);
// 购买面积比例
const options = [
	{
		value: '1',
		label: '10%',
		price: 0.1,
	},
	{
		value: '2',
		label: '20%',
		price: 0.2,
	},
	{
		value: '3',
		label: '30%',
		price: 0.3,
	},
	{
		value: '4',
		label: '40%',
		price: 0.4,
	},
	{
		value: '5',
		label: '50%',
		price: 0.5,
	},
	{
		value: '6',
		label: '60%',
		price: 0.6,
	},
	{
		value: '7',
		label: '70%',
		price: 0.7,
	},
	{
		value: '8',
		label: '80%',
		price: 0.8,
	},
	{
		value: '9',
		label: '90%',
		price: 0.9,
	},
	{
		value: '10',
		label: '100%',
		price: 1,
	},
];

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
});

// 信用风险资产评级颜色
function handlebuildingRateColor(level) {
	if (level == 'S') {
		return {
			background: `linear-gradient(90deg, #9D71DA 0%, #722ED1 100%)`,
		};
	}
	if (level == 'A+') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}
	if (level == 'A') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}

	if (level == 'B+') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}
	if (level == 'B') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}

	if (level == 'C') {
		return {
			background: `linear-gradient(90deg, #FFA44D 0%, #FF7D00 100%)`,
		};
	}
}

function handleKeyValue(key, name) {
	if (name === '维护情况') {
		if (key == '无维护') {
			return {
				color: `#9FD4FD`,
			};
		} else if (key == '一般') {
			return {
				color: `#57A9FB`,
			};
		} else if (key == '良好') {
			return {
				color: `#3491FA`,
			};
		} else if (key == '优秀') {
			return {
				color: `#206CCF`,
			};
		} else if (key == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
	if (name === '区域潜力') {
		if (key == '上升') {
			return {
				color: `#89D178`,
			};
		} else if (key == '维持') {
			return {
				color: `#0FC6C2`,
			};
		} else if (key == '稳定') {
			return {
				color: `#3491FA`,
			};
		} else if (key == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
	if (name === '商业活力') {
		if (key == '初期') {
			return {
				color: `#89D178`,
			};
		} else if (key == '活跃') {
			return {
				color: `#FF9A2E`,
			};
		} else if (key == '繁荣') {
			return {
				color: `#F76560`,
			};
		} else if (key == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
}

const onChangeProportion = (val) => {
	proportionValue.value = val;
	proportionValue2.value = Number((1 - val).toFixed(1));
};
const onChangeProportion2 = (val) => {
	proportionValue2.value = val;
	proportionValue.value = Number((1 - val).toFixed(1));
};

//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getCreditRisk({
		buildingIds: obj.ids,
	})
		.then((res) => {
			if (res.code == 200) {
				riskData.value = res.data;
				emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
				if (res.data.length == 1) {
					tableDataLeft.value.push(obj.arr[0]);
				} else if (res.data.length == 2) {
					tableDataLeft.value.push(obj.arr[0]);
					tableDataRight.value.push(obj.arr?.[1]);
				}
				multipleSelection.value = obj.arr;
				loading.value.close();
				nextTick(() => {
					for (let i = 0; i < riskData.value.length; i++) {
						generateEcharts(i);
					}
				});
			}
		})

		.catch((err) => {
			console.log(err, 'err');
		});
}

// 下载风险评估报告
function handleDownLoadRisk() {
	riskData?.value?.forEach((element, index) => {
		let arr = [
			{
				name: element.creditRiskAsset.name,
				assetType: element.creditRiskAsset.assetType,
				address: element.creditRiskAsset.address,
				buildingArea: element.creditRiskAsset.buildingSize,
				buildYear: element.creditRiskAsset.buildYear,
				regionalPotential: element.creditRiskAsset.regionalPotential,
				businessDynamism: element.creditRiskAsset.businessDynamism,
				spendingPower: element.creditRiskAsset.spendingPower,
			},
		];
		let params = {
			marketPriceIndex: element?.creditRisk?.marketPriceIndex, // 市场法价格
			marketPriceRatio: proportionValue.value, // 市场法价格比
			incomePriceIndex: element?.creditRisk?.incomePriceIndex, //收益法价格
			incomePriceRatio: proportionValue2.value, // 收益法价格比
			assess: element?.creditRisk?.presentValueOfAsset * 10000, //评估值
			salePrice: element?.creditRisk?.salePrice * 10000, // 价格（万元/㎡）
			depreciation: element?.creditRisk?.depreciation, //折旧
			ebitda: element?.creditRisk?.ebitda, // EBITDA
			capitalizationRate: element?.creditRisk?.capitalizationRate, // 资本化率
			ltv: element?.creditRisk?.ltv, //抵押率
			defaultRate: element?.creditRisk?.defaultRate, //  违约率（租户）
			icr: element?.creditRisk?.icr, //利息覆盖倍数ICR
			incomeGrowthRate: element?.creditRisk?.incomeGrowthRate, //收入/租金增长率
			rentSaleRatio: element?.creditRisk?.rentSaleRatio, // 租售价格比
			operationalRisk: handleoperations(element?.creditRisk?.operationalRisk), //运营风险
			presentValueOfAsset: element?.creditRisk?.presentValueOfAsset, // 资产现值（亿元）
			transactionCost: element?.creditRisk?.transactionCost, //交易成本
			tax: element?.creditRisk?.tax, // 税金
			fee: element?.creditRisk?.fee, //费用
			total: element?.creditRisk?.investmentAmount, //总投资额
			asserts: arr, // 楼宇信息
		};
		let url = 'https://bbzhun.com/api/credit-risk/export-risk';
		handleExport(params, url, '风险评估', index); //风险评估报告导出
	});
}

// 导出
function handleExport(params, url, name, index) {
	axios({
		url: url,
		method: 'POST',
		responseType: 'blob',
		headers: {
			'Content-Type': 'application/json',
			Authorization: localStorage.getItem('token'),
		},
		data: params,
	})
		.then((response) => {
			const blob = new Blob([response.data], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			});
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `${name}-${index}.xlsx`;
			a.click();
			window.URL.revokeObjectURL(url);
			if (index == 0) {
				ElMessage.success('导出成功');
			}
		})
		.catch((error) => {
			ElMessage.warning(error);
		});
}

const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		tableDataLeft.value = [];
		tableDataRight.value = [];
		riskData.value = [];
		emit('handleBuildingId', { ids: null, arr: [] });
		return;
	}
	let ids = multipleSelection.value.map((item) => item.id).join(',');
	getCreditRisk({
		buildingIds: ids,
	})
		.then((res) => {
			if (res.code == 200) {
				riskData.value = res.data;
				// 重置所有数据
				tableDataLeft.value = [];
				tableDataRight.value = [];
				// 同步更新数据和表格
				if (multipleSelection.value.length === 1) {
					tableDataLeft.value = [multipleSelection.value[0]];
				} else if (multipleSelection.value.length === 2) {
					tableDataLeft.value = [multipleSelection.value[0]];
					tableDataRight.value = [multipleSelection.value[1]];
				}
				emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
				dialogTableVisible.value = false;
				nextTick(() => {
					for (let i = 0; i < riskData.value.length; i++) {
						generateEcharts(i);
					}
				});
			}
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};

// echarts生成
const generateEcharts = (index) => {
	myChart.value = echarts.init(echartsContainer.value[index]);
	myChart.value.setOption({
		color: '#249EFF',
		radar: {
			center: ['52%', '52%'],
			radius: 100,
			axisName: {
				formatter: '{value}',
				color: '#4E5969',
			},
			splitArea: {
				areaStyle: {
					color: ['#fff', '#fff', '#fff', '#fff', '#fff'],
				},
			},
			indicator: [{ name: '市场法价格' }, { name: '收益法价格' }, { name: '评估值' }, { name: '折旧' }, { name: 'EBITDA' }],
		},
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(244, 247, 252, .8)',
			borderColor: 'transparent',
			borderRadius: 4,
			formatter: (params) => {
				let arr = [
					{
						name: '市场法价格',
						value: '',
						unit: '万元',
					},
					{
						name: '收益法价格',
						value: '',
						unit: '万元',
					},
					{
						name: '评估值',
						value: '',
						unit: '亿元',
					},
					{
						name: '折旧',
						value: '',
						unit: '万元',
					},
					{
						name: 'EBITDA',
						value: '',
						unit: '万元',
					},
				];
				params.value.forEach((items, index) => {
					arr.forEach((element, indexs) => {
						if (index === indexs) {
							element.value = items;
						}
					});
				});
				console.log(arr, 'arrarrarrarr');
				let str = '';
				arr.forEach((item) => {
					str += `<div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
				<div style="width:8px;height:8px;background-color:#249EFF;margin-right: 8px;border-radius: 50%;">
				</div>
				<div style="width:100%;display:flex;justify-content: space-between;align-items: center;">
					<div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
					${item.name}
				</div>
				<div style="font-size: 14px; color: #1D2129; font-weight: 600">
					${item.value}${item.unit}
				</div>
				</div>
				</div>`;
				});
				return `
        <div style="display: flex;
            flex-direction: column;
            gap: 4px;">
            ${str}
        </div>
        `;
			},
		},
		radar: {
			indicator: [{ name: '市场法价格' }, { name: '收益法价格' }, { name: '评估值' }, { name: '折旧' }, { name: 'EBITDA' }],
		},
		series: [
			{
				type: 'radar',
				symbolSize: 6,
				backgroundColor: 'rgba(0,23,11,0.3)',
				tooltip: {
					trigger: 'item',
				},
				itemStyle: {
					borderWidth: 2,
					borderColor: '#d3ecff',
				},
				areaStyle: {
					color: '#a7d8ff',
				},
				data: [
					{
						value: [
							riskData.value?.[index].creditRisk.marketPriceIndex,
							riskData.value?.[index].creditRisk.incomePriceIndex,
							riskData.value?.[index].creditRisk.presentValueOfAsset,
							riskData.value?.[index].creditRisk.depreciation,
							riskData.value?.[index].creditRisk.ebitda,
						],
					},
				],
			},
		],
	});
};

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	save();
}

// 运营风险数值转化中文
function handleoperations(val) {
	if (0 < val && val <= 3) {
		return '低';
	} else if (3 < val && val <= 4.5) {
		return '低';
	} else if (4.5 < val && val <= 6) {
		return '中';
	} else if (6 < val && val <= 8) {
		return '中';
	} else if (8 < val && val <= 9) {
		return '高';
	} else if (9 < val && val <= 10) {
		return '高';
	}
}

function clear(type) {
	if (type == 'left') {
		tableDataLeft.value = [];
		multipleSelection.value.shift();
	} else {
		tableDataRight.value = [];
		multipleSelection.value.pop();
	}
	save();
}
</script>
<style lang="less" scoped>
.comparison_box {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.common_wrap {
		padding: 20px 16px;
		background-color: #fff;
		display: flex;
		gap: 16px;
		border-radius: 0px 4px 4px 4px;
		.left_empty_wrap,
		.left_content_wrap,
		.right_empty_wrap,
		.right_content_wrap {
			flex: 1;
		}
		.left_empty_wrap,
		.right_empty_wrap {
			border: 1px solid #e5e6eb;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 4px;
			padding: 13px 0 16px 0;
			.icon {
				width: 64px;
				height: 64px;
			}
		}
		.title_wrap {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;
		}
		.table_wrap {
		}
	}
	.chart_wrap {
		flex: 1;
		border-radius: 4px;
		margin-top: 16px;
		padding: 20px 16px 16px 16px;
		background-color: #fff;
		.header_wrap {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.left {
				display: flex;
				align-items: center;
				.line {
					width: 4px;
					height: 14px;
					background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
					border-radius: 4px;
				}
				.title {
					color: #1d2129;
					font-size: 20px;
					font-weight: 600;
					margin-left: 8px;
					margin-right: 20px;
					line-height: 28px;
				}
			}
			.right_top {
				height: 22px;
				background: #ffffff;
				border-radius: 6px;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				.title_btn {
					font-size: 14px;
					font-weight: 400;
					line-height: 22px;
					color: #1868f1;
					display: flex;
					align-items: center;
					z-index: 9;
					cursor: pointer;
					img {
						margin-left: 4px;
					}
				}
			}
		}
		.echars_main_left {
			width: calc(50% - 8px);
			border-radius: 4px;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			flex-direction: column;
		}
		.echars_main {
			margin-top: 16px;
			width: 100%;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;
			gap: 16px;
			.box_Con {
				width: 100% !important;
			}
			.box_ {
				width: calc(50% - 8px);
				border: 1px solid #e5e6eb;
				border-radius: 4px;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				flex-direction: column;
				.title1 {
					box-sizing: border-box;
					padding: 0 20px;
					width: 100%;
					height: 48px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #f7f8fa;
					border-bottom: 1px solid #e5e6eb;
					.title {
						font-size: 16px;
						font-weight: 600;
						color: #1d2129;
					}
				}
				.content_wrap {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: center;
					.info_wrap {
						width: 100%;
						height: 240px;
					}
				}

				.empty_wrap {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: 400;
					color: #86909c;
					img {
						width: 80px;
						height: 80px;
					}
				}
			}
		}
	}
}

.slideshow {
	height: 240px;
	padding: 20px 71px 20px 56px;
	box-sizing: border-box;
	.boxss {
		display: flex;
		align-items: center;
		justify-content: space-between;
		.head-left-details {
			display: flex;
			align-items: center;
			img {
				width: 298px;
				height: 200px;
				border-radius: 8px;
				object-fit: fill;
			}

			.head-right_details {
				width: 371px;
				height: 182px;
				margin-left: 4px;
				.right_details {
					padding: 0 20px;
					margin-bottom: 24px;
				}
				.top_tent {
					display: flex;
					height: 28px;
					align-items: center;
					gap: 8px;
					margin-bottom: 4px;
					.top_text {
						font-weight: 500;
						font-size: 18px;
						line-height: 28px;
						color: #1d2129;
					}
					.topt_text {
						background: linear-gradient(90deg, #77a9ff 0%, #1868f1 100%);
						height: 20px;
						border-radius: 2px;
						padding: 1px 8px;
						color: #ffffff;
						font-weight: 500;
						font-size: 12px;
						line-height: 20px;
						box-sizing: border-box;
					}
					.topth_text {
						box-sizing: border-box;
						background: #e8f3ff;
						height: 20px;
						border-radius: 2px;
						padding: 1px 8px;
						color: #1868f1;
						font-weight: 500;
						font-size: 12px;
						line-height: 20px;
					}
				}

				.center_tent {
					display: flex;
					height: 22px;
					align-items: center;
					gap: 24px;
					margin-bottom: 24px;
					.center_tent_left {
						display: flex;
						align-items: center;
					}
					.center_tent_left_text {
						font-weight: 400;
						font-size: 14px;
						margin-left: 4px;
						color: #4e5969;
					}
				}

				.items_box_border {
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					gap: 16px 41px;
					.index_left_item {
						width: 96px;
						height: 44px;
						position: relative;
						.lingIndex {
							width: 1px;
							height: 20px;
							background: #e5e6eb;
							position: absolute;
							right: -20.5px;
							bottom: 12px;
						}
						.value {
							font-weight: 600;
							font-size: 18px;
							line-height: 18px;
							text-align: center;
							color: #1d2129;
						}

						.title {
							margin-top: 4px;
							font-weight: 400;
							font-size: 14px;
							line-height: 22px;
							text-align: center;
							color: #4e5969;
						}
					}
				}
			}
		}
	}
}

.tag_box_details {
	height: 23px;
	line-height: 23px;
	padding: 12px 20px;
	font-weight: 500;
	font-size: 16px;
	color: #1d2129;
	background: #f7f8fa;
	border-bottom: 1px solid #e5e6eb;
}
.left_box {
	width: 100%;
	height: 416px;
	box-sizing: border-box;
	// background: rgb(255, 255, 255);
	padding: 20px 14px;
	display: flex;

	.left_item {
		width: 300px;
		background: #f9fbfe;
		border: 1px solid #e8f3ff;
		border-radius: 4px;
		padding: 40px 32px 40px 20px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 12px;
		.item_qy {
			display: flex;
			align-items: center;
			height: 32px;
			width: 100%;
		}
		.item_qy_title {
			width: 70px;
			font-weight: 400;
			font-size: 14px;
			line-height: 22px;
			color: #4e5969;
			text-align: right;
			margin-right: 16px;
		}

		.item_qy_value {
			font-weight: 500;
			font-size: 14px;
			line-height: 22px;
			color: #1d2129;
		}
	}

	.left_items {
		width: calc(70% - 10px);
	}
}

.details_bottom_container {
	width: calc(100% - 0px);
	box-sizing: border-box;
	background: #fff;
}
.details-bottom-container {
	width: calc(100% - 0px);
	// height: 264px;
	padding: 20px 16px;
	display: flex;
	flex-wrap: wrap;
	gap: 16px;
	box-sizing: border-box;
	// margin: 0 16px;
	// margin-bottom: 16px;
	.card-details-boxs {
		width: 502px;
		height: 104px;
		display: flex;
		align-items: center;
		// flex-direction: column;
		justify-content: space-between;
		padding: 20px 24px 20px 32px;
		box-sizing: border-box;
		border-radius: 4px;
		background: #f7f8fa;
		.card_details {
			display: flex;
			align-items: center;
			.details_box {
				margin-right: 30px;
				.value {
					font-weight: 600;
					font-size: 32px;
					line-height: 32px;
					color: #0f2860;
					margin-bottom: 4px;
				}

				.name {
					white-space: nowrap;
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
					color: #0f2860;
				}
			}
			.line {
				margin-right: 28px;
				width: 1px;
				height: 60px;
				background: #d8eaff;
			}
		}
		.details_box_fee {
			display: flex;
			width: calc(100% - 200px);
			align-items: center;
			justify-content: space-between;
			.box_fee {
				height: 64px;
				// width: 152px;
				.box_fee_circle {
					display: flex;
					height: 26px;
					margin-bottom: 12px;
					align-items: end;
					.box_fee_circle_img {
						width: 16px;
						height: 16px;
						margin-bottom: 2px;
						img {
							width: 100%;
							height: 100%;
						}
					}
					.box_fee_circle_si {
						margin-left: 4px;
						width: max-content;
						font-weight: 400;
						font-size: 14px;
						line-height: 22px;
						height: 22px;
						color: #0f2860;
					}
					.tax_feetax {
						margin-left: 12px;
						font-weight: 600;
						font-size: 26px;
						line-height: 26px;
						color: #0f2860;
					}
				}
				.box_fee_circleFx {
					display: flex;
					align-items: flex-end;
					height: 26px;
					.box_fee_title {
						width: 72px;
						height: 22px;
						line-height: 22px;
						text-align: right;
						margin-right: 12px;
						font-weight: 400;
						font-size: 14px;
						color: #0f2860;
					}
					.box_fee_value {
						font-weight: 600;
						font-size: 26px;
						line-height: 26px;
						color: #0f2860;
					}
				}
			}
			.details_boxImg {
				width: 48px;
				height: 48px;
				img {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
	.card-details-box {
		// width: calc((100% - (5* 16px)) / 6);
		width: 243px;
		// height: calc((100% - (2* 16px)) / 3);
		// width: 249px;
		height: 104px;
		display: flex;
		align-items: center;
		// flex-direction: column;
		justify-content: space-between;
		padding: 23px 24px 23px 32px;
		box-sizing: border-box;
		border-radius: 4px;
		background: #f7f8fa;
		.details_box {
			.value {
				font-weight: 600;
				font-size: 32px;
				line-height: 32px;
				color: #0f2860;
				margin-bottom: 4px;
			}

			.name {
				white-space: nowrap;
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: #0f2860;
			}
		}
		.details_boxImg {
			width: 48px;
			height: 48px;
			img {
				width: 100%;
				height: 100%;
			}
		}
	}

	.tax-v2 {
		display: flex;
		flex-direction: row;

		.tax_fee-left {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 40%;

			.num {
				height: 50%;
				font-size: 24px;
				font-weight: 700;
				color: rgba(24, 104, 241, 1);
				display: flex;
				align-items: flex-end;
			}

			.test {
				margin-top: 10px;
				margin-left: 7px;
				width: 112px;
				font-size: 14px;
				color: rgba(78, 89, 105, 1);
			}
		}

		.tax_fee {
			width: 60%;
			height: 72px;
			border-left: 1px solid rgba(231, 231, 231, 1);
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			font-size: 14px;
			color: rgba(78, 89, 105, 1);

			.tax,
			.fee {
				font-size: 20px;
				font-weight: 600;
				color: rgba(24, 104, 241, 1);
			}
		}
	}

	.proportion {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
}
</style>
