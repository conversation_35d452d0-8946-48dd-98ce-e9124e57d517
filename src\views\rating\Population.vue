<template>
	<div class="main">
		<div>
			<el-text tag="p" class="Title">
				客流统计>>
				<el-divider class="divider" />
			</el-text>
			<div style="height: 200px">
				<Hotmap v-if="showRatMap" />
			</div>
		</div>
		<div class="contonier">
			<el-row :gutter="20">
				<el-col :span="12">
					<el-text tag="p" class="Title">
						关键事实>>
						<el-divider class="divider" />
					</el-text>
					<div style="display: flex; justify-content: space-evenly; align-items: center;text-align: center;">
						<div class="box">
							<p class="prop">总人口</p>
							<p>{{ data.total_population }}</p>
						</div>
						<div class="box">
							<p class="prop">年龄中位数</p>
							<p>{{ data.age_mid }}</p>
						</div>
						<div class="box">
							<p class="prop">家庭数</p>
							<p>{{ data.households_num }}</p>
						</div>
						<div class="box">
							<p class="prop">人均支配(万元)</p>
							<p>{{ data.disposable_income }}</p>
						</div>
					</div>
				</el-col>
				<el-col :span="12">
					<el-text tag="p" class="Title">
						就业情况>>
						<el-divider class="divider" />
					</el-text>
					<div style="display: flex; width: 100%; align-items: center">
						<div v-for="employmentType in employmentTypes" :key="employmentType.key" style="width: 70%; display: flex" v-if="data.white_collar">
							<p>{{ employmentType.label }}：</p>
							<el-progress
								style="width: 70%"
								:percentage="data[employmentType.key].slice(0, -1)"
								:color="employmentType.color"
								:text-inside="true"
								:stroke-width="22"
							/>
						</div>
					</div>
					<div ref="echartsContainer" style="width: 100%; height: 200px"></div>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="12">
					<el-text tag="p" class="Title">
						年龄情况>>
						<el-divider class="divider" />
					</el-text>
					<div style="display: flex; width: 100%; align-items: center">
						<div v-for="employmentType in employmentAges" :key="employmentType.key" style="width: 70%; display: flex" v-if="data.age_range1">
							<p>{{ employmentType.label }}：</p>
							<el-progress
								style="width: 70%"
								:percentage="data[employmentType.key].slice(0, -1)"
								:color="employmentType.color"
								:text-inside="true"
								:stroke-width="22"
							/>
						</div>
					</div>
					<div ref="echartsContainerAge" style="width: 100%; height: 200px"></div>
				</el-col>
				<el-col :span="12">
					<el-text tag="p" class="Title">
						教育>>
						<el-divider class="divider" />
					</el-text>

					<div style="display: flex; width: 100%; align-items: center">
						<div
							v-for="employmentType in employmentSchools"
							:key="employmentType.key"
							style="width: 70%; display: flex"
							v-if="data.educational_level1"
						>
							<p>{{ employmentType.label }}：</p>
							<el-progress
								style="width: 45%"
								:percentage="data[employmentType.key].slice(0, -1)"
								:color="employmentType.color"
								:text-inside="true"
								:stroke-width="22"
							/>
						</div>
					</div>
					<div ref="echartsContainerSchool" style="width: 100%; height: 200px"></div>
				</el-col>
			</el-row>
		</div>
	</div>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import http from '@/utils/http';
import { useRoute } from 'vue-router';
import Hotmap from '../../Hotmap.vue';
import * as echarts from 'echarts';
const echartsContainer = ref(null);
const myChart = ref(null);
const echartsContainerAge = ref(null);
const myChartAge = ref(null);
const echartsContainerSchool = ref(null);
const myChartSchool = ref(null);
const route = useRoute();
const showRatMap = ref(true);
const data = ref([]);
const employmentTypes = [
	{ key: 'white_collar', label: '白领', color: '#67C23A' },
	{ key: 'blue_collar', label: '蓝领', color: '#e6a23c' },
	{ key: 'public_utilities', label: '公共事业', color: '#F56C6C' }, // 你可以在这里设置颜色
];
const employmentAges = [
	{ key: 'age_range1', label: '0-14岁', color: '#67C23A' },
	{ key: 'age_range2', label: '15-64岁', color: '#e6a23c' },
	{ key: 'age_range3', label: '65岁以上', color: '#F56C6C' }, // 你可以在这里设置颜色
];
const employmentSchools = [
	{ key: 'educational_level1', label: '高中以下', color: '#67C23A' },
	{ key: 'educational_level2', label: '高中', color: '#e6a23c' },
	{ key: 'educational_level3', label: '专科', color: '#F56C6C' }, // 你可以在这里设置颜色
	{ key: 'educational_level4', label: '学士/硕士/博士', color: '#e6a23c' },
];
onMounted(() => {
	http
		.get('/api/a_maps.do?act=ratingPassDetailsAction', {
			params: {
				maps_id: route.params.id,
				action: 'population',
			},
		})
		.then((res) => {
			data.value = res.list[0];
			const {
				white_collar,
				blue_collar,
				public_utilities,
				age_range1,
				age_range2,
				age_range3,
				educational_level1,
				educational_level2,
				educational_level3,
				educational_level4,
			} = res.list[0];

			// 处理百分比字符串，去除百分号
			const schoolingColl = educational_level1.replace('%', '');
			const schoolingColl2 = educational_level2.replace('%', '');
			const schoolingColl3 = educational_level3.replace('%', '');
			const schoolingColl4 = educational_level4.replace('%', '');
			const age_range1Coll = age_range1.replace('%', '');
			const age_range2Coll = age_range2.replace('%', '');
			const age_range3Coll = age_range3.replace('%', '');
			const processedWhiteCollar = white_collar.replace('%', '');
			const processedBlueCollar = blue_collar.replace('%', '');
			const processedPublicUtilities = public_utilities.replace('%', '');


			myChart.value = echarts.init(echartsContainer.value);
			myChartAge.value = echarts.init(echartsContainerAge.value);
			myChartSchool.value = echarts.init(echartsContainerSchool.value);

			window.addEventListener('resize', () => {
				myChart.value.resize();
				myChartAge.value.resize();
				myChartSchool.value.resize();
			});
			myChart.value.setOption({
				tooltip: {
					trigger: 'item',
				},
				legend: {
					top: '5%',
					left: 'center',
				},
				series: [
					{
						name: 'Access From',
						type: 'pie',
						radius: ['40%', '70%'],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: 'center',
						},
						emphasis: {
							label: {
								show: true,
								fontSize: 40,
								fontWeight: 'bold',
							},
						},
						labelLine: {
							show: false,
						},
						data: [
							{ value: processedWhiteCollar, name: '白领' },
							{ value: processedBlueCollar, name: '蓝领' },
							{ value: processedPublicUtilities, name: '公共事业' },
							{ value: (100 - processedPublicUtilities - processedBlueCollar - processedWhiteCollar).toFixed(2), name: '失业率' },
						],
					},
				],
			});
			myChartAge.value.setOption({
				tooltip: {
					trigger: 'item',
				},
				legend: {
					top: '5%',
					left: 'center',
				},
				series: [
					{
						name: 'Access From',
						type: 'pie',
						radius: ['40%', '70%'],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: 'center',
						},
						emphasis: {
							label: {
								show: true,
								fontSize: 40,
								fontWeight: 'bold',
							},
						},
						labelLine: {
							show: false,
						},
						data: [
							{ value: age_range1Coll, name: '0-14岁' },
							{ value: age_range2Coll, name: '14-65岁' },
							{ value: age_range3Coll, name: '65岁以上' },
						],
					},
				],
			});
			myChartSchool.value.setOption({
				tooltip: {
					trigger: 'item',
				},
				legend: {
					top: '5%',
					left: 'center',
				},
				series: [
					{
						name: 'Access From',
						type: 'pie',
						radius: ['40%', '70%'],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: 'center',
						},
						emphasis: {
							label: {
								show: true,
								fontSize: 40,
								fontWeight: 'bold',
							},
						},
						labelLine: {
							show: false,
						},
						data: [
							{ value: schoolingColl, name: '高中以下' },
							{ value: schoolingColl2, name: '高中' },
							{ value: schoolingColl3, name: '专科' },
							{ value: schoolingColl4, name: '学士/硕士/博士' },
						],
					},
				],
			});
		});

	showRatMap.value = true;
});

// ...

onUnmounted(() => {
	// debugger
	showRatMap.value = false;
});
</script>

<style lang="less" scoped>
.el-progress :deep(span) {
	color: #000;
}
.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
	margin-top: 36px;
}
.divider {
	flex-grow: 1;
	margin: 16px 0 16px 8px;
	border-color: #3483ce;
}
.main {
	background: white;
	border-radius: 9px;
	margin-bottom: 20px;
	margin-left: 20px;
	.prop {
		background-color: #dfe4ed;
	}
}
.demo-progress .el-progress--line {
	margin-bottom: 15px;
	width: 350px;
}
</style>
