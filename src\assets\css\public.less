//点击过渡效果
.active {
  transition: .3s all;
	cursor: pointer;
	&:active {
	  transform: scale(.95);
	}
}
.selected {
		color: rgba(23, 62, 136, 1);
		font-weight: bold;
  background-color: rgba(242, 243, 245, 1);
}
.el-loading-spinner .path {
  stroke: rgb(13, 161, 219);
}
//2行文本溢出隐藏
.overflowEllpsis_2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

//单行文本溢出隐藏
.overflowEllpsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.no-scroll {
    // 隐藏滚动条 chrome & safari
    &::-webkit-scrollbar {
        height: 0 !important;
    }
    // 隐藏滚动条 IE 10+
    -ms-overflow-style: none;
}
/* 	引入字体 */
@font-face {
  font-family: "youshebiaotihei";
  src: url('@/assets/font/优设标题黑.ttf') ;
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
.youshebiaotihei{
  font-family: "youshebiaotihei";
}
@font-face {
  font-family: "DDPRO";
  src: url('https://static.biaobiaozhun.com/mini-program/font/D-DIN-PRO-600-SemiBold.otf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.DDPRO {
	font-family: "DDPRO";
}
.AItalk{
  padding: 6px;
  .result{
   
    img{
      width: 40px;
      height: 40px;
    }
  }
  .answer{
    display: flex;
    align-items: start;
    .AIavatar{
      min-width: 40px;
      height: 40px;
      background-image: url('@/assets/AIAvatar.png');
      background-size: cover;
    }
    .leftNode{
      // display: inline-flex;
      max-width:600px;
      margin: 10px;
      padding: 10px;
      background-color: white;
      border: 1px solid #dddddd;
      border-radius: 0 15px 15px 15px;
    }
    
  }
  .rightNode{
    max-width: 600px;
    margin: 10px;
    padding: 10px;
    background: #95ec69;
    border-radius: 15px 0 15px 15px;
  }
 
  .question{
    display: flex;
    justify-content: flex-end;
    word-wrap:break-word;
    align-items: start;
    img{
      width: 40px;
      height: 40px;
    }
  }
}

@media screen and (max-width: 991px) {
  .AItalk{
    .answer{
      .leftNode{
        max-width:400px;
      }
    }
   
    .rightNode{
      max-width: 400px;
    }
  }
  
}
@media screen and (max-width: 768px) {
  .AItalk{
    .answer{
      .leftNode{
        max-width:210px;
      }
    }
   
    .rightNode{
      max-width:210px;
    }
  }

  
}

 .getReportdialog {
	.el-dialog__header{
		padding: 16px 14px;
		border-bottom: 1px solid #e7e7e7;
		.el-dialog__headerbtn {
			height: 56px !important;
		}
	}
}