<template>
	<div style="display: flex; justify-content: center">
		<el-table :data="data" ref="tableRefl" style="width: 100%" :lazy="true" :class="classname" empty-text="暂无数据">
			<el-table-column label="综合成本率" label-class-name="fixed_header" align="center" class="fixed-header">
				<el-table-column
					resizable
					v-for="column in tableColumns"
					:key="column.prop"
					:prop="column.prop"
					label-class-name="fixed_header"
					:label="column.label"
					:width="column.width"
					style="height: 100px"
					:formatter="formatterAmount"
				>
					<!-- <template #default="scope">
						<div>{{ column.prop === 'interestRate' ? scope.row[column.prop] * 100 + '%' : scope.row[column.prop] }}</div>
					</template> -->
				</el-table-column>
			</el-table-column>
		</el-table>
	</div>
</template>
<script setup>
import { defineProps } from 'vue';
const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
	classname: {
		type: String,
		default: [],
	},
});

let tableRefl = ref(null);

const { proxy } = getCurrentInstance();
nextTick(() => {
	proxy?.$dragTable(tableRefl);
});
const formatterAmount = (row, column, cellValue) => {
	if (column.label == '利率') {
		if (cellValue) {
			let cellv = cellValue * 100;
			return cellv.toFixed(2) + '%';
		}
	} else if (column.label == '规模(亿)' || column.label.includes('万')) {
		if (cellValue) {
			return cellValue.toFixed(2);
		}
	}
	return cellValue;
};
const baseTableColumns = [
	{ prop: 'stratification', label: '分层' },
	{ prop: 'level', label: '评级' },
	{ prop: 'scale', label: '规模(亿)', width: 120 },
	{ prop: 'interestRate', label: '利率' },
	{ prop: 'interest', label: '利息(万元)', width: 120 },
	{ prop: 'total', label: '合计(万元)', width: 120 },
];

const conditionalColumn = props.classname === 'have' ? { prop: 'guaranteeFee', label: '担保费(万元)', width: 120 } : undefined;

const tableColumns = [...baseTableColumns];

if (conditionalColumn) {
	tableColumns.splice(5, 0, conditionalColumn); // 在索引5的位置插入担保费列
}
const secured = [];
</script>
<style scoped lang="less">
::v-deep .fixed_header .cell {
	font-weight: 500;
	font-size: 14px;
	line-height: 22px;
	color: #1d2129;
}

::v-deep .fixed_header {
	height: 40px;
	background: #f7f8fa !important;
}

.el-table {
	--el-table-border-color: #e5e6eb;
	border-radius: 4px;
	.el-table__row {
		background: #fff !important;
	}
}
</style>
