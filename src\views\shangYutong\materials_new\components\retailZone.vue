<template>
	<div style="padding: 16px 10px 0px 0px; overflow: hidden">
		<div class="tab_box">
			<div class="tab" :class="activeName === route.name ? 'tabAct' : ''" v-for="route in routes" @click="handleTabClick(route)" :key="route.name">
				<img :src="activeName == route.name ? route.icon_active : route.icon" />
				{{ route.name }}
			</div>
		</div>
		<component :is="componentNames" />
	</div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import demographic from './demographic.vue';
import brandLibrary from './brandLibrary.vue';
import { useRoute } from 'vue-router';
import trendsAct from '@/assets/trendsAct.png';
import trends from '@/assets/trends.png';
import administratorActive from '@/assets/brandLibraryAct.png';
import administrator from '@/assets/brandLibrary.png';
const route = useRoute();

const routes = [
	{
		name: '人口趋势',
		componentName: demographic,
		icon: trends,
		icon_active: trendsAct,
	},
	{
		name: '品牌库',
		componentName: brandLibrary,
		icon: administrator,
		icon_active: administratorActive,
	},
];

const componentNames = ref(demographic);
const activeName = ref('人口趋势');

onMounted(() => {
	if (route.query.name) {
		//循环对比name
		for (let index = 0; index < routes.length; index++) {
			const element = routes[index];
			if (element.name == route.query.name) {
				componentNames.value = element.componentName;
				activeName.value = element.name;
				break;
			}
		}
	}
});

const handleTabClick = (item) => {
	componentNames.value = item.componentName;
	activeName.value = item.name;
};
</script>

<style lang="scss" scoped>
.tab_box {
	width: 100%;
	box-sizing: border-box;
	font-size: 14px;
	font-weight: 600;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	background-color: transparent;
	.title {
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		margin-right: 15px;
		color: #1a1a1a;
	}

	.tab {
		width: 216px;
		height: 48px;
		padding: 0 16px;
		display: flex;
		align-items: center;
		cursor: pointer;
		font-size: 16px;
		font-weight: 500;
		line-height: 24px;
		color: #4e5969;
		background-color: #f2f3f5;
		border-radius: 4px 4px 0 0;
		img {
			width: 16px;
			height: 16px;
			margin-right: 4px;
		}
	}

	.tabAct {
		font-size: 16px;
		color: #1868f1;
		background-color: #fff;
	}
}
</style>
