<template>
	<div class="content">
		<!-- <div class="title">人口对比</div> -->
		<div class="container_box">
			<div class="table_main">
				<div class="table_">
					<div class="top_boxFirst">
						<div class="tag_boxTitle">对比资产一</div>
						<div class="tag_boxCenter">
							<div class="tag_boxRight" v-if="tableDatao[0]" @click="clearDate(tableDatao[0], 1)">× 清空</div>
							<!-- <div class="tag_boxLeft" v-if="tableDatao.length > 0" @click="handleDownload(tableDatao[0].id, 'population')">
								<el-icon style="margin-right: 2px"><Download /></el-icon>半径人口报告
							</div>
							<div class="tag_boxLeft" v-if="tableDatao.length > 0" @click="handleDownload(tableDatao[0].id, 'populationthree')">
								<el-icon style="margin-right: 2px"><Download /></el-icon>客流量报告
							</div> -->
						</div>
					</div>
					<div class="table_1" v-if="tableDatao[0]">
						<el-table border :data="tableDatao" height="80px" style="width: 100%">
							<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
							<el-table-column prop="buildingType" label="资产类型" width="" />
							<el-table-column prop="street" label="地址" show-overflow-tooltip>
								<template #default="scope">
									{{
										scope.row?.city && scope.row?.district
											? scope.row?.city + scope.row?.district + scope.row?.street
											: scope.row?.buildingCity + scope.row?.buildingDistrict + scope.row?.buildingStreet
									}}
								</template>
							</el-table-column>
							<el-table-column prop="buildingSize" label="建筑面积" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
									</div>
								</template>
							</el-table-column>
							<el-table-column prop="maintenance" label="维护情况" width="" />
							<el-table-column prop="absoluteValue" label="单价" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="add active" @click="choose(1)" v-else>+ 选择对比资产</div>
					<!-- <div class="clear active" v-if="tableDatao.length > 0" @click="clearDate(1)">× 清空</div> -->
				</div>
				<div class="table_">
					<div class="top_box">
						<div class="top_boxFirst">
							<div class="tag_boxTitle">对比资产二</div>
							<div class="tag_boxCenter">
								<div class="tag_boxRight" v-if="tableDatat[0]" @click="clearDate(tableDatat[0], 2)">× 清空</div>
								<!-- <div class="tag_boxLeft" v-if="tableDatat.length > 0" @click="handleDownload(tableDatat[0].id, 'population')">
									<el-icon style="margin-right: 2px"><Download /></el-icon>半径人口报告
								</div>
								<div class="tag_boxLeft" v-if="tableDatat.length > 0" @click="handleDownload(tableDatat[0].id, 'populationthree')">
									<el-icon style="margin-right: 2px"><Download /></el-icon>客流量报告
								</div> -->
							</div>
						</div>
						<div class="table_1" v-if="tableDatat[0]">
							<el-table :data="tableDatat" border height="80px" style="width: 100%">
								<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
								<el-table-column prop="buildingType" label="资产类型" width="" />
								<el-table-column prop="street" label="地址" show-overflow-tooltip>
									<template #default="scope">
										{{
											scope.row?.city && scope.row?.district
												? scope.row?.city + scope.row?.district + scope.row?.street
												: scope.row?.buildingCity + scope.row?.buildingDistrict + scope.row?.buildingStreet
										}}
									</template>
								</el-table-column>
								<el-table-column prop="buildingSize" label="建筑面积" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="maintenance" label="维护情况" width="" />
								<el-table-column prop="absoluteValue" label="单价" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="add active" @click="choose(2)" v-else>+ 选择对比资产</div>
						<!-- <div class="clear active" v-if="tableDatat.length > 0" @click="clearDate(2)">× 清空</div> -->
					</div>
				</div>
			</div>

			<div class="flex_box">
				<div class="line_box">
					<div v-if="handlerLineBox(1).length > 0">
						{{ handlerLineBox(1) }}
					</div>
					<div class="box_copy" v-if="handlerLineBox(1).length > 0" @click="handlerCopy(1)">复制</div>
				</div>
				<div class="line_box">
					<div v-if="handlerLineBox(2).length > 0">
						{{ handlerLineBox(2) }}
					</div>
					<div class="box_copy" v-if="handlerLineBox(2).length > 0" @click="handlerCopy(2)">复制</div>
				</div>
			</div>

			<!-- 对比图 -->
			<div class="echars_box">
				<div class="tag_box">
					人口对比（半径一公里） <span v-if="tableDatao.length > 0">{{ leftData && leftData?.buildingName ? leftData.buildingName : '' }}</span
					><span v-if="tableDatat.length > 0">{{ rightData && rightData?.buildingName ? rightData.buildingName : '' }}</span>
				</div>
				<div class="echars_main">
					<div class="box_">
						<div class="title1">关键事实</div>
						<div class="box_min_box">
							<div class="box_min">
								<div class="title">总人口</div>
								<div class="tips_box" :style="`${!rightData || !Object.keys(rightData).length > 0 ? 'justify-content: center;' : ''}`">
									<span>{{ leftData ? leftData.totalPopulation : 0 }}</span>
									<template v-if="rightData && Object.keys(rightData).length > 0"> vs </template
									><span v-show="rightData && Object.keys(rightData).length > 0">{{ rightData ? rightData.totalPopulation : 0 }}</span>
								</div>
							</div>
							<div class="box_min">
								<div class="title">年龄中位数（岁）</div>
								<div class="tips_box" :style="`${!rightData || !Object.keys(rightData).length > 0 ? 'justify-content: center;' : ''}`">
									<span>{{ leftData ? leftData.ageMid : 0 }}</span>
									<template v-if="rightData && Object.keys(rightData).length > 0"> vs </template>
									<span v-show="rightData && Object.keys(rightData).length > 0">{{ rightData ? rightData.ageMid : 0 }}</span>
								</div>
							</div>
							<div class="box_min">
								<div class="title">家庭数（户）</div>
								<div class="tips_box" :style="`${!rightData || !Object.keys(rightData).length > 0 ? 'justify-content: center;' : ''}`">
									<span>{{ leftData ? leftData.householdsNum : 0 }}</span>
									<template v-if="rightData && Object.keys(rightData).length > 0"> vs </template>
									<span v-show="rightData && Object.keys(rightData).length > 0">{{ rightData ? rightData.householdsNum : 0 }}</span>
								</div>
							</div>
							<div class="box_min">
								<div class="title">人均支配（万元）</div>
								<div class="tips_box" :style="`${!rightData || !Object.keys(rightData).length > 0 ? 'justify-content: center;' : ''}`">
									<span>{{ leftData ? leftData.disposableIncome : 0 }}</span>
									<template v-if="rightData && Object.keys(rightData).length > 0"> vs </template
									><span v-show="rightData && Object.keys(rightData).length > 0">{{ rightData ? rightData.disposableIncome : 0 }}</span>
								</div>
							</div>
						</div>
						<div class="down_box">
							<span v-if="tableDatao.length > 0">{{ leftData && leftData?.buildingName ? leftData.buildingName : '' }}</span
							><span v-if="tableDatat.length > 0">{{ rightData && rightData?.buildingName ? rightData.buildingName : '' }}</span>
						</div>
					</div>
					<div class="box_">
						<div class="title1">教育</div>
						<div class="pie_box" :style="`${rightData ? '' : 'display: flex;justify-content: center;'}`">
							<div :style="`width: 50%`">
								<echartPie :pieData="jiaoyu_left"></echartPie>
							</div>
							<div :style="`width: 50%;${rightData ? '' : 'display: none'}`">
								<echartPie :pieData="jiaoyu_right"></echartPie>
							</div>
						</div>
						<div class="down_box" v-if="jiaoyu_left">
							<span v-if="jiaoyu_left.data.length > 0">{{ jiaoyu_left.data[0].name }}</span>
							<span v-if="jiaoyu_left.data.length > 0">{{ jiaoyu_left.data[1].name }}</span>
							<span v-if="jiaoyu_left.data.length > 0">{{ jiaoyu_left.data[2].name }}</span>
							<span v-if="jiaoyu_left.data.length > 0">{{ jiaoyu_left.data[3].name }}</span>
						</div>
					</div>
					<div class="box_">
						<div class="title1">就业情况</div>
						<div class="pie_box" :style="`${rightData ? '' : 'display: flex;justify-content: center;'}`">
							<div :style="`width: 50%;`"><echartPie :pieData="jiuye_left"></echartPie></div>
							<div :style="`width: 50%;${rightData ? '' : 'display: none'}`"><echartPie :pieData="jiuye_right"></echartPie></div>
						</div>
						<div class="down_box" v-if="jiuye_left">
							<span v-if="jiuye_left.data.length > 0">{{ jiuye_left.data[0].name }}</span>
							<span v-if="jiuye_left.data.length > 0">{{ jiuye_left.data[1].name }}</span>
							<span v-if="jiuye_left.data.length > 0">{{ jiuye_left.data[2].name }}</span>
						</div>
					</div>
					<div class="box_">
						<div class="title1">年龄情况</div>
						<div class="pie_box" :style="`${rightData ? '' : 'display: flex;justify-content: center;'}`">
							<div :style="`width: 50%`"><echartPie :pieData="nianling_left"></echartPie></div>
							<div :style="`width: 50%;${rightData ? '' : 'display: none'}`"><echartPie :pieData="nianling_right"></echartPie></div>
						</div>
						<div class="down_box" v-if="nianling_left">
							<span v-if="nianling_left.data.length > 0">{{ nianling_left.data[0].name }}</span>
							<span v-if="nianling_left.data.length > 0">{{ nianling_left.data[1].name }}</span>
							<span v-if="nianling_left.data.length > 0">{{ nianling_left.data[2].name }}</span>
						</div>
					</div>
				</div>
			</div>

			<div class="btn_box" v-if="handlerBtnBox().length > 0">
				{{ handlerBtnBox() }}
				<div class="box_copy" @click="handlerCopy(3)">复制</div>
			</div>
		</div>
	</div>
	<el-dialog v-model="dialogTableVisible" width="800" title="选择对比资产" :close-on-click-modal="false">
		<div class="title_box">
			<div class="tab" v-for="(item, index) in multipleSelection" :key="index">
				<div style="text-wrap: nowrap">对比资产</div>
				{{ $utils.chineseNumber(index) }}：
				<div :title="item.buildingName" style="text-wrap: nowrap; width: 116px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
					{{ item.buildingName }}
				</div>
				<div class="det" @click="clearDate(item, 0, index)">×</div>
			</div>
		</div>
		<div class="search_box">
			<div class="box_1">
				<div class="label">城市</div>
				<el-cascader
					placeholder="请选择城市"
					:options="$vuexStore.state.cityArray"
					v-model="selectedCity"
					@change="handleChange"
					:props="{ value: 'label' }"
				>
				</el-cascader>
			</div>
			<div class="box_1">
				<div class="label">资产评级</div>
				<el-select v-model="degree" placeholder="全部资产评级">
					<el-option v-for="(item, value) in rate" :key="value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">资产类型</div>
				<el-select v-model="buildValue" placeholder="全部资产">
					<el-option v-for="item in buildingTypes" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">关键词</div>
				<el-input v-model="keyword" placeholder="请输入关键字"></el-input>
			</div>
			<div class="box_2">
				<el-button type="primary" @click="searchs()">查询</el-button>
				<el-button type="primary" @click="reset()">重置</el-button>
			</div>
		</div>
		<div class="table_2">
			<el-table
				:data="tableData.data"
				style="width: 100%"
				height="308px"
				border
				ref="multipleTableRef"
				@selection-change="(selection) => handleSelectionChange(selection)"
				stripe
			>
				<el-table-column type="selection" width="55" />
				<el-table-column
					v-for="(column, index) in columns"
					:key="index"
					:label="column.label"
					:prop="column.dataName"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table>
		</div>
		<el-pagination
			@current-change="handleCurrentChange"
			:current-page="tableData.currentPage"
			small
			background
			layout="prev, pager, next"
			class="mt-4"
			:total="tableData.total"
		/>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogTableVisible = false">取消</el-button>
				<el-button type="primary" @click="save()"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>
<script setup>
import { onMounted, ref } from 'vue';
import getReport from '../../../../component/getReport/index.vue';
import { getBuildingListByMultiCondition, getComparativePopulation, getDictList } from '@/api/syt.js';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import echartPie from './echart/pie.vue';
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const getReportRef = ref();
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const loading = ref();
const city = ref('');
const county = ref('');
const rate = ref([]);
const buildingTypes = ref([]);
const buildValue = ref('');
const degree = ref('');
const keyword = ref('');
const currentPage = ref(1);
const selectedCity = ref([]);
const tableDatao = ref([]);
const tableDatat = ref([]);
const dialogTableVisible = ref(false); //对话框显示
const multipleTableRef = ref(null);

const box_copyObj = ref('');

const box_copyObjTwo = ref('');

const columns = ref([
	{
		label: '资产名称',
		dataName: 'buildingName',
	},
	{
		label: '资产类型',
		dataName: 'buildingType',
	},
]);
const tableData = ref({
	loading: false, // 控制加载状态
	data: [],
	total: 0, // 数据总数，用于分页
	currentPage: 1, // 当前页码
	layout: 'total, prev, pager, next', // 分页布局
	background: true, // 是否为分页按钮添加背景色
	small: true, // 是否使用小型分页样式
});
const jiaoyu_left = ref(null);
const jiaoyu_right = ref(null);
const jiuye_left = ref(null);
const jiuye_right = ref(null);
const nianling_left = ref(null);
const nianling_right = ref(null);

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
});

//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}

//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingPopulation',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getComparativePopulation({
		buildingIds: obj.ids,
	})
		.then((res) => {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			leftData.value = res.data[0];
			rightData.value = res.data[1];
			tableDatao.value.push(obj.arr[0]);
			tableDatat.value.push(obj.arr?.[1]);
			setData_pie(res.data[0], res.data?.[1] || []);
			multipleSelection.value = obj.arr;
			loading.value.close();
		})
		.catch((err) => {
			console.log(err, 'err');
		});
}
const handleChange = (val) => {
	city.value = val[0];
	county.value = val[1];
};
const reset = () => {
	city.value = '';
	degree.value = '';
	buildValue.value = '';
	selectedCity.value = [];
	county.value = '';
	keyword.value = '';
	tableData.value.currentPage = 1;
	search();
};

function searchs() {
	tableData.value.currentPage = 1;
	search();
}
const search = async () => {
	tableData.value.loading = true;
	const queryParams = {
		city: city.value,
		buildingRate: degree.value,
		district: county.value,
		buildingType: buildValue.value,
		keyword: keyword.value,
		currentPage: tableData.value.currentPage,
		pageSize: 10,
	};
	await getBuildingListByMultiCondition(queryParams)
		.then((res) => {
			tableData.value.loading = false;
			tableData.value.data = res.data.rows;
			nextTick(() => {
				tableData.value.data.map((v) => {
					multipleSelection.value.map((i) => {
						if (v.id == i.id) {
							multipleTableRef.value.toggleRowSelection(v, true);
						}
					});
				});
			});
			tableData.value.total = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const multipleSelection = ref([]);
let leftData = ref();
let rightData = ref({});
const handleSelectionChange = (val) => {
	setTimeout(() => {
		let mergedSet = new Set([...multipleSelection.value, ...val]);
		let mergedArray = Array.from(mergedSet);
		let uniqueById = Array.from(new Set(mergedArray.map((item) => item.id))).map((id) => {
			return mergedArray.find((item) => item.id === id);
		});
		// 当前页 tableData.value
		// 当前页选中 val
		// 当前页选中和之前选中的重复的去掉的 uniqueById
		tableData.value.data.map((item) => {
			uniqueById.map((uniqueItem, index) => {
				if (item.id == uniqueItem.id) {
					const foundInVal = val.some((v) => v.id === uniqueItem.id);
					if (!foundInVal) {
						uniqueById.splice(index, 1);
					}
				}
			});
		});
		multipleSelection.value = uniqueById;
	}, 100);
};
const handleCurrentChange = (val) => {
	tableData.value.currentPage = val;
	search();
};
const comparison = async () => {
	// if (multipleSelection.value.length !== 2) {
	// 	ElMessage.warning('只能选择两个！！！');
	// 	return;
	// }
	const idarray = [];
	multipleSelection.value.map((item) => {
		idarray.push(item.id);
	});
	const ids = idarray.join(',');
	await getComparativePopulation({
		buildingIds: ids,
	})
		.then((res) => {
			console.log(res, '-----');

			emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
			if (res.data.length > 1) {
				leftData.value = res.data[0];
				rightData.value = res.data[1];
				setData_pie(res.data[0], res.data[1]);
			}
			if (res.data.length == 1) {
				leftData.value = res.data[0];
				setData_pie(res.data[0], []);
			}
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const removePercentSigns = (str) => {
	return Number(str);
};
//插入数据
const setData_pie = (left, right) => {
	jiaoyu_left.value = {
		name: left.buildingName,
		data: [
			{
				value: removePercentSigns(left.educationNoHigh),
				name: '高中以下',
				itemStyle: { color: 'rgba(4, 80, 218, 1)' },
			},
			{
				value: removePercentSigns(left.educationHigh),
				name: '高中',
				itemStyle: { color: 'rgba(30, 170, 117, 1)' },
			},
			{
				value: removePercentSigns(left.educationJuniorCollege),
				name: '专科',
				itemStyle: { color: 'rgba(255, 114, 116, 1)' },
			},
			{
				value: removePercentSigns(left.educationBachelorMasterDoctor),
				name: '学士/硕士/博士',
				itemStyle: { color: 'rgba(250, 212, 24, 1)' },
			},
		],
	};
	jiaoyu_right.value = {
		name: right.buildingName,
		data: [
			{
				value: removePercentSigns(right.educationNoHigh),
				name: '高中以下',
				itemStyle: { color: 'rgba(4, 80, 218, 1)' },
			},
			{
				value: removePercentSigns(right.educationHigh),
				name: '高中',
				itemStyle: { color: 'rgba(30, 170, 117, 1)' },
			},
			{
				value: removePercentSigns(right.educationJuniorCollege),
				name: '专科',
				itemStyle: { color: 'rgba(255, 114, 116, 1)' },
			},
			{
				value: removePercentSigns(right.educationBachelorMasterDoctor),
				name: '学士/硕士/博士',
				itemStyle: { color: 'rgba(250, 212, 24, 1)' },
			},
		],
	};

	jiuye_left.value = {
		name: left.buildingName,
		data: [
			{
				value: removePercentSigns(left.whiteCollar),
				name: '白领',
				itemStyle: { color: 'rgba(4, 80, 218, 1)' },
			},
			{
				value: removePercentSigns(left.blueCollar),
				name: '蓝领',
				itemStyle: { color: 'rgba(30, 170, 117, 1)' },
			},
			{
				value: removePercentSigns(left.publicUtilities),
				name: '公共事业',
				itemStyle: { color: 'rgba(255, 114, 116, 1)' },
			},
		],
	};
	jiuye_right.value = {
		name: right.buildingName,
		data: [
			{
				value: removePercentSigns(right.whiteCollar),
				name: '白领',
				itemStyle: { color: 'rgba(4, 80, 218, 1)' },
			},
			{
				value: removePercentSigns(right.blueCollar),
				name: '蓝领',
				itemStyle: { color: 'rgba(30, 170, 117, 1)' },
			},
			{
				value: removePercentSigns(right.publicUtilities),
				name: '公共事业',
				itemStyle: { color: 'rgba(255, 114, 116, 1)' },
			},
		],
	};

	nianling_left.value = {
		name: left.buildingName,
		data: [
			{
				value: removePercentSigns(left.ageZeroFourteen),
				name: '0-14岁',
				itemStyle: { color: 'rgba(4, 80, 218, 1)' },
			},
			{
				value: removePercentSigns(left.ageFifteenSixtyfour),
				name: '15-64岁',
				itemStyle: { color: 'rgba(30, 170, 117, 1)' },
			},
			{
				value: removePercentSigns(left.ageSixtyfiveAbove),
				name: '65岁以上',
				itemStyle: { color: 'rgba(255, 114, 116, 1)' },
			},
		],
	};
	nianling_right.value = {
		name: right.buildingName,
		data: [
			{
				value: removePercentSigns(right.ageZeroFourteen),
				name: '0-14岁',
				itemStyle: { color: 'rgba(4, 80, 218, 1)' },
			},
			{
				value: removePercentSigns(right.ageFifteenSixtyfour),
				name: '15-64岁',
				itemStyle: { color: 'rgba(30, 170, 117, 1)' },
			},
			{
				value: removePercentSigns(right.ageSixtyfiveAbove),
				name: '65岁以上',
				itemStyle: { color: 'rgba(255, 114, 116, 1)' },
			},
		],
	};
};
//清空上一次选的
const toggleSelection = (rows, isSelect) => {
	if (rows) {
		rows.forEach((row) => {
			multipleTableRef.value.toggleRowSelection(row, undefined, isSelect);
		});
	} else {
		multipleTableRef.value.clearSelection();
	}
};
// 点击选择对比资产
const choose = (item) => {
	search();
	// multipleTableRef.clearSelection();
	// search();
	dialogTableVisible.value = true;
	// 清空上一次选的
	// if (multipleSelection.value.length > 0) {
	// 	toggleSelection(multipleSelection.value, true);
	// }
};

// 清空
function handelClear(row) {
	multipleSelection.value.forEach((item, indexs) => {
		if (item.id == row.id) {
			multipleSelection.value.splice(indexs, 1);
		}
	});
	save();
}

//清空资产选项
const clearDate = (row, type, index) => {
	if (type === 1) {
		tableDatao.value = [];
		handelClear(row);
	} else if (type === 2) {
		//资产二清空
		tableDatat.value = [];
		handelClear(row);
	} else {
		// 弹窗内资产清空
		multipleSelection.value.splice(index, 1);
	}

	// 更新表格选中状态
	if (tableData.value?.length > 0) {
		// 先清空所有选中
		toggleSelection();
		// 重新设置当前应该选中的项
		nextTick(() => {
			tableData.value.forEach((item) => {
				if (multipleSelection.value.some((selected) => selected.id === item.id)) {
					multipleTableRef.value.toggleRowSelection(item, true);
				}
			});
		});
	}

	// // 同步更新 emit
	// const ids = multipleSelection.value.map((item) => item.id).join(',');
	// console.log(ids, multipleSelection.value, 'multipleSelection.value');
	// emit('handleBuildingId', {
	// 	ids: ids,
	// 	arr: multipleSelection.value,
	// });
};

const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		ElMessage({
			message: multipleSelection.value.length == 0 ? '至少选一个商圈' : '最多选择两个商圈',
			type: 'error',
		});
		return;
	}

	let ids = multipleSelection.value.map((item) => item.id).join(',');
	getComparativePopulation({
		buildingIds: ids,
	})
		.then((res) => {
			// 重置所有数据
			tableDatao.value = [];
			tableDatat.value = [];
			leftData.value = null;
			rightData.value = null;

			// 同步更新数据和表格
			if (multipleSelection.value.length === 1) {
				leftData.value = res.data[0];
				tableDatao.value = [multipleSelection.value[0]];
				setData_pie(res.data[0], []);
			} else if (multipleSelection.value.length === 2) {
				leftData.value = res.data[0];
				rightData.value = res.data[1];
				tableDatao.value = [multipleSelection.value[0]];
				tableDatat.value = [multipleSelection.value[1]];
				setData_pie(res.data[0], res.data[1]);
			}

			emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
			dialogTableVisible.value = false;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();

function handlerCopy(type) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(type === 1 ? box_copyObj.value : type === 2 ? box_copyObjTwo.value : handlerBtnBox())
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = type === 1 ? box_copyObj.value : type === 2 ? box_copyObjTwo.value : handlerBtnBox();
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

function handlerBtnBox() {
	let obj = rightData.value;
	let obj1 = leftData.value;
	if (!obj || !obj1) return '';
	if (obj.totalPopulation > obj1.totalPopulation) {
		let name = null;
		let lastName = null;

		name = obj.buildingName + '半径一公里的';

		if (obj.totalPopulation > obj1.totalPopulation) {
			lastName = '总人口、';
		}
		if (obj.ageMid > obj1.ageMid) {
			lastName += '年龄中位数、';
		}
		if (obj.householdsNum > obj1.householdsNum) {
			lastName += '家庭数、';
		}
		if (obj.businessDynamism > obj1.businessDynamism) {
			lastName += '人均支配、';
		} else {
			if (obj.businessDynamism === obj1.businessDynamism) {
				lastName += '人均支配持平；';
			}
		}

		// 学士/硕士/博士
		let num1 = obj.educationBachelorMasterDoctor - obj1.educationBachelorMasterDoctor;
		// 高中以下
		let num2 = obj.educationNoHigh - obj1.educationNoHigh;
		// 高中
		let num3 = obj.educationHigh - obj1.educationHigh;
		// 专科
		let num4 = obj.educationJuniorCollege - obj1.educationJuniorCollege;
		const maxVal = Math.max(num1, num2, num3, num4);
		if (maxVal === num1) {
			lastName += '教育情况学士/硕士/博士占比，';
		}

		if (maxVal === num2) {
			lastName += '教育情况高中以下占比，';
		}

		if (maxVal === num3) {
			lastName += '教育情况高中占比，';
		}

		if (maxVal === num4) {
			lastName += '教育情况专科占比，';
		}

		// 白领
		let num6 = obj.whiteCollar - obj1.whiteCollar;
		// 蓝领
		let num7 = obj.blueCollar - obj1.blueCollar;
		// 公共事业
		let num8 = obj.publicUtilities - obj1.publicUtilities;
		const maxVal1 = Math.max(num6, num7, num8);
		if (maxVal1 === num6) {
			lastName += '就业情况白领占比，';
		}

		if (maxVal1 === num7) {
			lastName += '就业情况蓝领占比，';
		}

		if (maxVal1 === num8) {
			lastName += '就业情况公共事业占比，';
		}

		// 0-14岁
		let num11 = obj.ageZeroFourteen - obj1.ageZeroFourteen;
		// 15-64岁
		let num12 = obj.ageFifteenSixtyfour - obj1.ageFifteenSixtyfour;
		// 65岁以上
		let num13 = obj.ageSixtyfiveAbove - obj1.ageSixtyfiveAbove;
		const maxVal12 = Math.max(num11, num12, num13);
		if (maxVal12 === num11) {
			lastName += '年龄情况0-14岁人口占比';
		}

		if (maxVal12 === num12) {
			lastName += '年龄情况15-64岁人口占比';
		}

		if (maxVal12 === num13) {
			lastName += '年龄情况65岁以上人口占比';
		}

		if (lastName) {
			lastName += '高于' + obj1.buildingName + '。';
			return name + lastName;
		}
	} else {
		let name = null;
		let lastName = null;

		name = obj1.buildingName + '半径一公里的';

		if (obj1.totalPopulation > obj.totalPopulation) {
			lastName = '总人口、';
		}
		if (obj1.ageMid > obj.ageMid) {
			lastName += '年龄中位数、';
		}
		if (obj1.householdsNum > obj.householdsNum) {
			lastName += '家庭数、';
		}
		if (obj1.businessDynamism > obj.businessDynamism) {
			lastName += '人均支配、';
		} else {
			if (obj.businessDynamism === obj1.businessDynamism) {
				lastName += '人均支配持平；';
			}
		}
		// 学士/硕士/博士
		let num1 = obj1.educationBachelorMasterDoctor - obj.educationBachelorMasterDoctor;
		// 高中以下
		let num2 = obj1.educationNoHigh - obj.educationNoHigh;
		// 高中
		let num3 = obj1.educationHigh - obj.educationHigh;
		// 专科
		let num4 = obj1.educationJuniorCollege - obj.educationJuniorCollege;
		const maxVal = Math.max(num1, num2, num3, num4);
		if (maxVal === num1) {
			lastName += '教育情况学士/硕士/博士占比，';
		}

		if (maxVal === num2) {
			lastName += '教育情况高中以下占比，';
		}

		if (maxVal === num3) {
			lastName += '教育情况高中占比，';
		}

		if (maxVal === num4) {
			lastName += '教育情况专科占比，';
		}

		// 白领
		let num6 = obj1.whiteCollar - obj.whiteCollar;
		// 蓝领
		let num7 = obj1.blueCollar - obj.blueCollar;
		// 公共事业
		let num8 = obj1.publicUtilities - obj.publicUtilities;
		const maxVal1 = Math.max(num6, num7, num8);
		if (maxVal1 === num6) {
			lastName += '就业情况白领占比，';
		}

		if (maxVal1 === num7) {
			lastName += '就业情况蓝领占比，';
		}

		if (maxVal1 === num8) {
			lastName += '就业情况公共事业占比，';
		}

		// 0-14岁
		let num11 = obj1.ageZeroFourteen - obj.ageZeroFourteen;
		// 15-64岁
		let num12 = obj1.ageFifteenSixtyfour - obj.ageFifteenSixtyfour;
		// 65岁以上
		let num13 = obj1.ageSixtyfiveAbove - obj.ageSixtyfiveAbove;
		const maxVal12 = Math.max(num11, num12, num13);
		if (maxVal12 === num11) {
			lastName += '年龄情况0-14岁人口占比';
		}

		if (maxVal12 === num12) {
			lastName += '年龄情况15-64岁人口占比';
		}

		if (maxVal12 === num13) {
			lastName += '年龄情况65岁以上人口占比';
		}

		if (lastName) {
			lastName += '高于' + obj1.buildingName + '。';
			return name + lastName;
		}
	}
}

function handlerLineBox(type) {
	if (type === 1) {
		let obj = null;
		if (tableDatao.value.length > 0 && leftData?.value) {
			if (tableDatao.value[0]?.buildingName === leftData.value?.buildingName) {
				obj = leftData.value;
			} else {
				obj = rightData.value;
			}
		}
		if (!obj) return '';
		let name =
			obj.buildingName +
			'教育情况学士/硕士/博士占比' +
			formattedMoney(handleNumber(obj.educationBachelorMasterDoctor)) +
			'%，专科占比' +
			formattedMoney(handleNumber(obj.educationJuniorCollege)) +
			'%，高中占比' +
			formattedMoney(handleNumber(obj.educationHigh)) +
			'%；就业情况公共事业占比' +
			formattedMoney(handleNumber(obj.publicUtilities)) +
			'%，蓝领占比' +
			formattedMoney(handleNumber(obj.blueCollar)) +
			'%，白领占比' +
			formattedMoney(handleNumber(obj.whiteCollar)) +
			'%；年龄情况15-64岁占比' +
			formattedMoney(handleNumber(obj.ageFifteenSixtyfour)) +
			'%，0-14岁占比' +
			formattedMoney(handleNumber(obj.ageZeroFourteen)) +
			'%，65岁以上占比' +
			formattedMoney(handleNumber(obj.ageSixtyfiveAbove)) +
			'%。';
		if (name) {
			box_copyObj.value = name;
		}
		return name;
	} else {
		let obj1 = null;
		if (tableDatat.value.length > 0 && leftData?.value) {
			if (tableDatat.value[0]?.buildingName === leftData.value?.buildingName) {
				obj1 = leftData.value;
			} else {
				obj1 = rightData.value;
			}
		}
		if (!obj1) return '';
		let name =
			obj1.buildingName +
			'教育情况学士/硕士/博士占比' +
			formattedMoney(handleNumber(obj1.educationBachelorMasterDoctor)) +
			'%，专科占比' +
			formattedMoney(handleNumber(obj1.educationJuniorCollege)) +
			'%，高中占比' +
			formattedMoney(handleNumber(obj1.educationHigh)) +
			'%；就业情况公共事业占比' +
			formattedMoney(handleNumber(obj1.publicUtilities)) +
			'%，蓝领占比' +
			formattedMoney(handleNumber(obj1.blueCollar)) +
			'%，白领占比' +
			formattedMoney(handleNumber(obj1.whiteCollar)) +
			'%；年龄情况15-64岁占比' +
			formattedMoney(handleNumber(obj1.ageFifteenSixtyfour)) +
			'%，0-14岁占比' +
			formattedMoney(handleNumber(obj1.ageZeroFourteen)) +
			'%，65岁以上占比' +
			formattedMoney(handleNumber(obj1.ageSixtyfiveAbove)) +
			'%。';

		if (name) {
			box_copyObjTwo.value = name;
		}
		return name;
	}
}
</script>

<style lang="less" scoped>
.el-row {
	margin-bottom: 20px;
}

.el-row:last-child {
	margin-bottom: 0;
}

.el-col {
	border-radius: 4px;
}

.content {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;

		.table_main {
			width: 100%;
			height: 162px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.table_ {
				width: calc(50% - 8px);
				height: 162px;
				border-radius: 6px;
				background-color: rgba(255, 255, 255, 1);
				position: relative;

				.tag_box {
					width: auto;
					height: 16px;
					position: absolute;
					left: 0;
					top: 20px;
					font-size: 14px;
					font-weight: bold;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&::before {
						content: '';
						width: 4px;
						height: 16px;
						background-color: rgba(24, 104, 241, 1);
						margin-right: 10px;
					}
				}

				.table_1 {
					width: 96%;
					position: absolute;
					bottom: 10px;
					left: 2%;
					&::v-deep .el-table--fit {
						border-radius: 8px;
					}

					&::v-deep .el-table th {
						background-color: rgba(245, 245, 245, 1);
					}
				}

				.add {
					width: 96%;
					height: 90px;
					position: absolute;
					bottom: 10px;
					left: 2%;
					border-radius: 6px;
					border: 1px solid rgba(231, 231, 231, 1);
					display: flex;
					justify-content: center;
					align-items: center;
					color: rgba(3, 93, 255, 1);
					font-size: 14px;
					font-weight: bold;
				}

				.clear {
					width: auto;
					height: 20px;
					position: absolute;
					top: 15px;
					right: 15px;
					font-size: 14px;
					color: rgba(24, 104, 241, 1);
				}
			}
		}
		.echars_box {
			width: 100%;
			min-height: 522px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			border-radius: 6px;
			position: relative;
			.tag_box {
				width: auto;
				height: 16px;
				position: absolute;
				left: 0;
				top: 20px;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
				span {
					margin-top: 1.8px;
					margin-left: 10px;
					font-size: 12px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					&:first-child {
						&::after {
							content: '';
							width: 8px;
							height: 12px;
							margin-top: -2.82px;
							margin-left: 10px;
							background-color: rgba(4, 80, 218, 1);
						}
					}
					&:nth-child(2) {
						&::after {
							content: '';
							width: 8px;
							height: 12px;
							margin-top: -2px;
							margin-left: 10px;
							background-color: rgba(30, 170, 117, 1);
						}
					}
				}
			}
			.echars_main {
				width: 98%;
				height: 100%;
				margin: 0 auto;
				padding-top: 55px;
				padding-bottom: 15px;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex-wrap: wrap;
				.box_ {
					width: calc(50% - 8px);
					height: 442px;
					margin-bottom: 15px;
					border: 1px solid rgba(231, 231, 231, 1);
					border-radius: 6px;
					box-sizing: border-box;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-direction: column;
					.title1 {
						width: 100%;
						height: 44px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 12px;
						background-color: rgba(245, 245, 245, 1);
						border-bottom: 1px solid rgba(231, 231, 231, 1);
						margin-bottom: 20px;
					}
					.box_min_box {
						width: 90%;
						height: 288px;
						margin-top: 15px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						flex-wrap: wrap;
						.box_min {
							width: 49%;
							height: 132px;
							background-color: rgba(245, 245, 245, 1);
							border: 1px solid rgba(231, 231, 231, 1);
							box-sizing: border-box;
							border-radius: 6px;
							overflow: hidden;
							.title {
								width: 100%;
								height: 36px;
								background-color: rgba(255, 255, 255, 1);
								display: flex;
								justify-content: center;
								align-items: center;
								font-size: 12px;
							}
							.tips_box {
								width: 100%;
								height: 96px;
								display: flex;
								justify-content: space-between;
								align-items: center;
								padding: 0 10px;
								box-sizing: border-box;
								color: rgba(201, 205, 212, 1);
								font-weight: bold;
								span {
									font-size: 38px;
									&:first-child {
										color: rgba(4, 80, 218, 1);
									}
									&:last-child {
										color: rgba(30, 170, 117, 1);
									}
								}
							}
						}
					}
					.down_box {
						width: 100%;
						height: 24px;
						margin-top: 20px;
						display: flex;
						justify-content: center;
						align-items: center;
						span {
							margin-left: 10px;
							font-size: 12px;
							display: flex;
							justify-content: flex-start;
							align-items: center;
							&:first-child {
								&::after {
									content: '';
									width: 8px;
									height: 12px;
									margin-left: 10px;
									background-color: rgba(4, 80, 218, 1);
								}
							}
							&:nth-child(2) {
								&::after {
									content: '';
									width: 8px;
									height: 12px;
									margin-left: 10px;
									background-color: rgba(30, 170, 117, 1);
								}
							}
							&:nth-child(3) {
								&::after {
									content: '';
									width: 8px;
									height: 12px;
									margin-left: 10px;
									background-color: rgba(255, 114, 116, 1);
								}
							}
							&:nth-child(4) {
								&::after {
									content: '';
									width: 8px;
									height: 12px;
									margin-left: 10px;
									background-color: rgba(250, 212, 24, 1);
								}
							}
						}
					}
					.pie_box {
						width: 90%;
						height: 288px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						> :nth-child(n) {
							display: flex;
							justify-content: center;
						}
					}
				}
			}
		}
	}
}

.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	text-align: center;
	margin-bottom: 15px;
}

// 弹窗
.title_box {
	width: 100%;
	max-height: 100px;
	overflow: scroll;
	border-top: 1px solid rgba(231, 231, 231, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	box-sizing: border-box;
	display: flex;
	flex-wrap: wrap;
	.tab {
		width: 31%;
		height: 32px;
		margin: 8px 15px 8px 0;
		background-color: rgba(245, 246, 247, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
		position: relative;
		text-wrap: nowrap;
		.det {
			width: 10px;
			height: 10px;
			position: absolute;
			right: 10px;
			font-size: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: rgba(201, 205, 212, 1);
			cursor: pointer;
		}
	}
}
.search_box {
	width: 100%;
	height: auto;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
	.box_1 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		border: 1px solid rgba(231, 231, 231, 1);
		box-sizing: border-box;

		::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
			box-shadow: 0;
		}

		.label {
			width: 50%;
			height: 100%;
			font-size: 14px;
			color: rgba(134, 144, 156, 1);
			background-color: rgba(245, 246, 247, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.box_2 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		box-sizing: border-box;
	}
}
.table_2 {
	width: 100%;
	height: 308px;
	&::v-deep .el-table--fit {
		border-radius: 8px;
	}
	&::v-deep .el-table--small .el-table__cell {
		padding: 8px 0;
	}
	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}

.btn_box {
	width: calc(100% - 20px);
	display: flex;
	justify-content: flex-start;
	align-items: center;
	border-radius: 6px;
	background-color: #ffffff;
	margin-top: 10px;
	font-size: 14px;
	color: #555555;
	line-height: 20px;
	position: relative;
	padding: 10px 10px 15px 10px;
	.box_copy {
		position: absolute;
		right: 12px;
		bottom: 7px;
		cursor: pointer;
		color: #1868f1;
	}
}
.flex_box {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: space-between;
	margin-top: 10px;
	.line_box {
		font-size: 14px;
		line-height: 20px;
		padding: 10px;
		width: 47.8%;
		height: 100%;
		border-radius: 6px;
		background-color: #ffffff;
		position: relative;
		color: #555555;
		.box_copy {
			position: absolute;
			right: 12px;
			bottom: 4px;
			cursor: pointer;
			color: #1868f1;
		}
	}
}

.top_boxFirst {
	display: flex;
	justify-content: space-between;
	height: 56px;
	align-items: center;
	.tag_boxTitle {
		width: auto;
		height: 16px;
		font-size: 14px;
		font-weight: bold;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		&::before {
			content: '';
			width: 4px;
			height: 16px;
			background-color: #1868f1;
			margin-right: 10px;
		}
	}

	.tag_boxCenter {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		font-size: 14px;
		color: #1868f1;
		padding-right: 16px;
		> :nth-child(n) {
			cursor: pointer;
		}
		.tag_boxLeft {
			display: flex;
			align-items: center;
			margin: 0 0px 0 24px;
		}
	}
}
</style>
