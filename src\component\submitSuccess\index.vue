<template>
	<div class="receiveSuccess">
		<div class="box_receive">
			<div class="content">
				<img src="../../assets/submitSuccess.png" alt="" />
				<div class="content1">已提交</div>
				<div class="contentTwo">
					<div>我们已收到您的咨询信息，会尽快与您取得联系， 请留意电话和邮箱</div>
				</div>
			</div>

			<div class="box_Btn" @click="handleHomePage">回到首页（{{ time }}）</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter();

const time = ref(5); // 验证码秒数
const times = ref(null); // 定时器
onMounted(() => {
	handleTimer(); // 倒计时
});
//页面销毁
onUnmounted(() => {
	clearInterval(times.value);
});
// 回到首页
function handleHomePage() {
	// 跳转页面
	router.push({
		path: '/',
	});
}
// 定时器
function handleTimer(params) {
	times.value = setInterval(() => {
		time.value--;
		if (time.value == 0) {
			// 跳转页面
			handleHomePage();
			clearInterval(times.value);
			time.value = null;
		}
	}, 1000);
}
</script>
<style lang="scss" scoped>
.receiveSuccess {
	position: fixed;
	top: 0%;
	right: 0%;
	width: 100%;
	height: 100%;
	background: #fff;
	.box_receive {
		width: 600px;
		position: absolute;
		top: 50%;
		right: 50%;
		transform: translate(50%, -50%);
		display: flex;
		flex-direction: column;
		align-items: center;
		.content {
			width: 347px;
			height: 232px;
			display: flex;
			flex-direction: column;
			align-items: center;
			img {
				width: 80px;
				height: 80px;
			}
			.content1 {
				margin: 8px 0 16px 0;
				font-size: 21px;
				font-weight: 700;
				line-height: 32px;
				color: #1d2129;
			}
			.contentTwo {
				text-align: center;
				font-size: 15px;
				font-weight: 400;
				line-height: 32px;
				color: #4e5969;
			}
		}
		.box_Btn {
			margin-top: 103px;
			width: 306px;
			height: 48px;
			border-radius: 4px;
			line-height: 48px;
			text-align: center;
			background: #1868f1;
			color: #fff;
			cursor: pointer;
		}
	}
}
</style>
