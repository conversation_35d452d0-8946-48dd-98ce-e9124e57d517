<template>

    <div class="aboutBbz_box">
        <div class="titleBox">
          <h1>术木智能推出的商业地产行业楼宇分析和交易平台</h1>
          <p>致力于为客户提供高效、实用的商业地产信息及解决方案</p>
          <div class="btn">
            <button>开始使用</button>
            <div class="logo_box">
              <img src="@/assets/layout/logo.png" alt="">
              <span>标标准</span>
            </div>
          </div>
        </div>
        <div class="img_box">
          <img src="@/assets/images/home/<USER>" alt="">
          <p>
            术木智能科技（深圳）有限公司成立于2013年，是一家专注于地理信息系统(GIS)软件开发和数据运营的公司，拥有一支由资深工程师组成的技术团队，在行业内建立了良好的声誉。 2023年，公司开始涉足商业地产信息工具的设计和开发。作为一家具备广阔视野的科技公司，术木智能在多个城市设立了分支机构，包括北京、杭州和青岛。使公司能够更紧密地贴近客户，了解市场动态
          </p>
        </div>
        <div class="footer_box">
          <div class="title">更多关于术木智能</div>
          <div class="item_box">
             <div class="item">
              <p class="title">价值观</p>
              <p class="content">“创新、协作、高效、纯粹”</p>
            </div>
            <div class="item">
              <p class="title">人力资源</p>
              <p class="content">提供成体系的培训，为员工提供充满活力和创新的工作环境</p>
            </div>
            <div class="item">
              <p class="title">关于未来</p>
              <p class="content">做出最实用的信息工具，助力用户达成目标</p>
            </div>
          </div>
         
        </div>
    </div>

</template>

<script setup>
import { ref, reactive, toRefs, onMounted} from 'vue'

</script>
<style scoped lang="less">
  .aboutBbz_box {
    margin: 0 200px 200px;
	  height: auto;
  	min-height: 100vh;
    background-color: #fff;
    .titleBox {
        margin: 0 auto;
        padding-top: 70px;
      h1 {
        // width: 670px;
        color: rgb(0, 0, 0);
        font-family: 微软雅黑;
        font-size: 40px;
        font-weight: 700;
        letter-spacing: 0px;
        text-align: left;
        margin: 0 auto;
        // margin-top: 77px;
      }
      p {
        color: rgb(0, 0, 0);
        font-family: 微软雅黑;
        font-size: 16px;
        font-weight: 400;
        letter-spacing: 0px;
        text-align: left;
        margin-top: 35px;
      }
      .btn {
        display: flex;
        margin: 35px 0 10px;
        align-items: center;
        button {
          width: 122px;
          height: 46px;
          background: rgb(56, 96, 154);
          color: rgb(255, 255, 255);
          font-family: 微软雅黑;
          font-size: 16px;
          font-weight: 700;
          letter-spacing: 0px;
          text-align: center;
          border: none;
        }
        .logo_box {
          display: flex;
          margin-left: 34px;
          img {
            width: 34px;
            height: 34px;
          }
          span {
            color: rgb(0, 0, 0);
            font-family: 微软雅黑;
            font-size: 24px;
            font-weight: 400;
            letter-spacing: 0px;
            text-align: left;
          }
        }
      }
    }
    .img_box {
      // height: 487px;
      img {
        width: 100%;
      }
      p {
        margin: 0 auto;
        margin-top: 57px;
        color: rgb(0, 0, 0);
        font-family: 微软雅黑;
        font-size: 13px;
        font-weight: 400;
        letter-spacing: 0px;
        text-align: left;
      }
    }
    .footer_box {
      background-color: #fff;
      display: block;
      width: 100%;
      height: auto;
      
      margin-top: 120px;
      .title {
        color: rgb(0, 0, 0);
        font-family: 微软雅黑;
        font-size: 40px;
        font-weight: 700;
        letter-spacing: 0px;
        text-align: center;
      }
      .item_box {
        height: 100%;
        display: flex;
        margin: 0 auto;
        .item {
         width: 257px;
          margin-right: 50px;
        
        .title{
          color: rgb(0, 0, 0);
          font-family: 微软雅黑;
          font-size: 16px;
          font-weight: 700;
          // line-height: 21px;
          letter-spacing: 0px;
          text-align: left;
        }
        .content{
          font-size: 13px;
        }
      }
      }
     
    }
  }
  @media screen and (max-width: 991px) {
    .aboutBbz_box{
      margin: 0 100px 100px;
      .titleBox{
        h1{
          color: #000000;
          font-family: 微软雅黑;
          font-size: 35px;
          font-weight: 700;
          letter-spacing: 0px;
          text-align: left;
        }
      }
      .footer_box{
        .title{
          color: #000000;
          font-family: 微软雅黑;
          font-size: 35px;
          font-weight: 700;
          letter-spacing: 0px;
          text-align: center;
        }
      }
    }
  }
  @media screen and (max-width: 768px) {
    .item_box{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .aboutBbz_box{
      margin: 0 20px 60px;
      .titleBox{
        h1{
          color: #000000;
          font-family: 微软雅黑;
          font-size: 30px;
          font-weight: 700;
          letter-spacing: 0px;
          text-align: left;
        }
      }
      .footer_box{
        .title{
          color: #000000;
          font-family: 微软雅黑;
          font-size: 30px;
          font-weight: 700;
          letter-spacing: 0px;
          text-align: center;
        }
      }
    }
  }
</style>