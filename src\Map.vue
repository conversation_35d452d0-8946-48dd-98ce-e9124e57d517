<template>
	<div :id="containerId" style="height: 100%; width: 100%"></div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, toRefs, defineEmits, defineProps, onUpdated, onBeforeUpdate } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { ElMessage } from 'element-plus';
const emit = defineEmits(['handleMapClick', 'updateRadius']);

const props = defineProps({
	radius: Number,
});

const { radius } = toRefs(props);

const containerId = 'container';
let map = null;
let circle = null;
let circleEditor = null; // Move circleEditor definition to the outer scope
onMounted(() => {
	AMapLoader.load({
		key: 'adffeb71c6cc1f748cc57d3d1c8454c4',
		version: '2.0',
		plugins: ['AMap.CircleEditor'],
	})
		.then((AMap) => {
			map = new AMap.Map(containerId, {
				viewMode: '3D',
				zoom: 15,
				center: [120.369557, 36.094406],
			});

			// 监听地图点击事件
			map.on('click', handleMapClick);
		})
		.catch((e) => {
			console.log(e);
		});
});
onBeforeUpdate(() => {
	// console.log('radius1', radius.value,circle);
	// 更新圆的半径大小
	if (circle) {
		circle.setRadius(radius.value || 1000);
	}
});
onUpdated(() => {
	// console.log('radius2', radius.value,circle);
	// 更新圆的半径大小
	if (circle) {
		circle.setRadius(radius.value || 1000);
	}
});

onUnmounted(() => {
	if (map) {
		map.destroy();
	}

	if (circle) {
		circle.setMap(null);
	}
});

function handleMapClick(e) {
	const clickPosition = e.lnglat;
	const maxRadius = 1000; // 设置最大半径值
	const minRadius = 100; // 设置最小半径值
	const radiusValue = Math.min(Math.max(radius.value || 1000, minRadius), maxRadius); // 限制半径在[minRadius, maxRadius]范围内
	emit('clickChild', clickPosition.lng, clickPosition.lat);
	emit('searchMap');

	// 如果存在圆，先移除
	if (circle) {
		// 关闭圆编辑器
		circleEditor.close();
		// 移除上一个圆
		circle.setMap(null);
	}

	// 创建新的圆
	circle = new AMap.Circle({
		center: clickPosition, // 圆心位置为点击位置
		radius: radiusValue, // 半径（单位：米）
		strokeColor: '#67C23A',
		strokeOpacity: 1,
		strokeWeight: 3,
		fillColor: '#409EFF',
		fillOpacity: 0.35,
	});

	// 初始化圆编辑器
	circleEditor = new AMap.CircleEditor(map, circle);
	circleEditor.open();

	// 在编辑器调整半径时，手动检查并调整
	circleEditor.on('adjust', (e) => {
		const currentRadius = circle.getRadius();
		const newRadius = Math.min(Math.max(currentRadius, minRadius), maxRadius);
		console.log(currentRadius, newRadius, 'hh');
		emit('updateRadius', newRadius);
		emit('searchMap');
		if (currentRadius > 1000) {
			ElMessage.warning('半径不能超过1000');
			circleEditor.close();
			return;
		}
		if (currentRadius !== newRadius) {
			circle.setRadius(newRadius);
		}
	});

	// 将新的圆添加到地图
	circle.setMap(map);
}
</script>

<style lang="less" scoped></style>
