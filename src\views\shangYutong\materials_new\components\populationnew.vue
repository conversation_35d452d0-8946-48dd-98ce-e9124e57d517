<template>
	<div class="comparison_box">
		<div class="common_wrap">
			<div class="left_empty_wrap" v-if="tableDataLeft.length == 0">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择资产
				</arco-button>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0" class="left_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('left')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataLeft"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
				<div class="desc_wrap" v-if="handlerLineBox(1).length > 0">
					<img :src="descBg" class="bg" />
					<div class="desc">{{ handlerLineBox(1) }}</div>
					<div class="copy" @click="handlerCopy(1)">复制</div>
				</div>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0 && tableDataRight.length == 0" class="right_empty_wrap">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择对比资产
				</arco-button>
			</div>
			<div v-if="tableDataRight && tableDataRight.length > 0" class="right_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('right')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataRight"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
				<div class="desc_wrap" v-if="handlerLineBox(2).length > 0">
					<img :src="descBg" class="bg" />
					<div class="desc">{{ handlerLineBox(2) }}</div>
					<div class="copy" @click="handlerCopy(2)">复制</div>
				</div>
			</div>
		</div>
		<div class="chart_wrap">
			<div class="header_wrap">
				<span class="line"></span>
				<span class="title">人口分析(半径一公里)</span>
			</div>
			<div class="echars_main">
				<div class="box_">
					<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
						<div class="title1">
							<div class="title">关键事实</div>
						</div>
						<div class="content_wrap" v-if="tableDataLeft.length > 0 || tableDataRight.length > 0">
							<div class="key_wrap">
								<div class="key_left">
									<div class="header">
										<div class="text" :style="{ width: rightData ? '200px' : '420px' }">
											{{ leftData.buildingName }}
										</div>
									</div>
									<div class="val_item">
										<div class="val_wrap">
											<div class="val">{{ leftData ? leftData.totalPopulation : 0 }}</div>
											<div class="unit">人</div>
										</div>
										<div class="label">总人口</div>
									</div>
									<div class="val_item">
										<div class="val_wrap">
											<div class="val">{{ leftData ? leftData.ageMid : 0 }}</div>
											<div class="unit">岁</div>
										</div>
										<div class="label">年龄中位数</div>
									</div>
									<div class="val_item">
										<div class="val_wrap">
											<div class="val">{{ leftData ? leftData.householdsNum : 0 }}</div>
											<div class="unit">户</div>
										</div>
										<div class="label">家庭数</div>
									</div>
									<div class="val_item">
										<div class="val_wrap">
											<div class="val">{{ leftData ? leftData.disposableIncome : 0 }}</div>
											<div class="unit">万元</div>
										</div>
										<div class="label">人均收入</div>
									</div>
								</div>
								<div class="key_right" v-if="rightData">
									<img :src="vs" class="vs" />
									<div class="header">
										<div class="text" :style="{ width: rightData ? '200px' : '420px' }">
											{{ rightData.buildingName }}
										</div>
									</div>
									<div class="val_item">
										<div class="val_wrap">
											<div class="val">{{ rightData ? rightData.totalPopulation : 0 }}</div>
											<div class="unit">人</div>
										</div>
										<div class="label">总人口</div>
									</div>
									<div class="val_item">
										<div class="val_wrap">
											<div class="val">{{ rightData ? rightData.ageMid : 0 }}</div>
											<div class="unit">岁</div>
										</div>
										<div class="label">年龄中位数</div>
									</div>
									<div class="val_item">
										<div class="val_wrap">
											<div class="val">{{ rightData ? rightData.householdsNum : 0 }}</div>
											<div class="unit">户</div>
										</div>
										<div class="label">家庭数</div>
									</div>
									<div class="val_item">
										<div class="val_wrap">
											<div class="val">{{ rightData ? rightData.disposableIncome : 0 }}</div>
											<div class="unit">万元</div>
										</div>
										<div class="label">人均收入</div>
									</div>
								</div>
							</div>
						</div>

						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="box_">
					<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
						<div class="title1">
							<div class="title">教育情况</div>
							<div v-if="!(jiaoyu_left || jiaoyu_right)" class="download">
								<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
							</div>
							<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[0])">
								<icon-download class="download_icon" size="16" />
							</div>
						</div>
						<div class="pie_wrap" v-if="jiaoyu_left || jiaoyu_right">
							<div class="pie_box" :style="`${rightData ? '' : 'display: flex;justify-content: center;'}`">
								<div :style="`width: 50%`">
									<echartPie ref="jiaoyu1Ref" :pieData="jiaoyu_left"></echartPie>
								</div>
								<div :style="`width: 50%;${rightData ? '' : 'display: none'}`">
									<echartPie ref="jiaoyu2Ref" :pieData="jiaoyu_right"></echartPie>
								</div>
							</div>
							<div class="legend_wrap">
								<div class="legend_item" v-for="item in jyLegend">
									<div class="circle" :style="{ backgroundColor: item.color }"></div>
									<div class="name">{{ item.name }}</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="box_">
					<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
						<div class="title1">
							<div class="title">就业情况</div>
							<div v-if="!(jiuye_left || jiuye_right)" class="download">
								<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
							</div>
							<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[0])">
								<icon-download class="download_icon" size="16" />
							</div>
						</div>
						<div class="pie_wrap" v-if="jiuye_left || jiuye_right">
							<div class="pie_box" :style="`${rightData ? '' : 'display: flex;justify-content: center;'}`">
								<div :style="`width: 50%`">
									<echartPie ref="jiuye1Ref" :pieData="jiuye_left"></echartPie>
								</div>
								<div :style="`width: 50%;${rightData ? '' : 'display: none'}`">
									<echartPie ref="jiuye2Ref" :pieData="jiuye_right"></echartPie>
								</div>
							</div>
							<div class="legend_wrap">
								<div class="legend_item" v-for="item in jiuyeLegend">
									<div class="circle" :style="{ backgroundColor: item.color }"></div>
									<div class="name">{{ item.name }}</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="box_">
					<div style="width: 100%; height: 100%; display: flex; flex-direction: column">
						<div class="title1">
							<div class="title">年龄情况</div>
							<div v-if="!(nianling_left || nianling_right)" class="download">
								<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
							</div>
							<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[0])">
								<icon-download class="download_icon" size="16" />
							</div>
						</div>
						<div class="pie_wrap" v-if="nianling_left || nianling_right">
							<div class="pie_box" :style="`${rightData ? '' : 'display: flex;justify-content: center;'}`">
								<div :style="`width: 50%`">
									<echartPie ref="nianling1Ref" :pieData="nianling_left"></echartPie>
								</div>
								<div :style="`width: 50%;${rightData ? '' : 'display: none'}`">
									<echartPie ref="nianling2Ref" :pieData="nianling_right"></echartPie>
								</div>
							</div>
							<div class="legend_wrap">
								<div class="legend_item" v-for="item in ageLegend">
									<div class="circle" :style="{ backgroundColor: item.color }"></div>
									<div class="name">{{ item.name }}</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
			<div class="summary_wrap" v-if="leftData && Object.keys(leftData).length > 0 && rightData && Object.keys(rightData).length > 0">
				<img :src="summary_bg" class="bg" />
				<img :src="summary_icon" class="icon" />
				<div class="summary">
					{{ handlerBtnBox() }}
				</div>
				<div class="copy" @click="handlerCopy(3)">复制</div>
			</div>
		</div>
	</div>
	<buildSelect key="all" v-model="dialogTableVisible" :selectedData="multipleSelection" :maxSelectNum="2" @confirm="handleBuildConfirm"></buildSelect>
	<!-- <buildSelect key="single_left" v-model="dialogSingleLeftVisible" :maxSelectNum="1" @confirm="handleBuildLeftConfirm"></buildSelect>
	<buildSelect key="single_right" v-model="dialogSingleRightVisible" :maxSelectNum="1" @confirm="handleBuildRightConfirm"></buildSelect> -->
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import { getComparativePopulation, getDictList } from '@/api/syt.js';
import getReport from '../../../../component/getReport/index.vue';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import descBg from '@/assets/images/shangYutong/buildInfo/desc_bg.png';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import people_icon from '@/assets/images/shangYutong/buildInfo/people_icon.png';
import vs from '@/assets/images/shangYutong/buildInfo/vs.png';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import buildSelect from '@/component/buildSelect/index.vue';
import echartPie from './echart/pie.vue';
import summary_bg from '@/assets/images/shangYutong/buildInfo/summary_bg.png';
import summary_icon from '@/assets/images/shangYutong/buildInfo/summary_icon.png';
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const jiaoyu_left = ref(null);
const jiaoyu_right = ref(null);
const jiuye_left = ref(null);
const jiuye_right = ref(null);
const nianling_left = ref(null);
const nianling_right = ref(null);
const jiaoyu1Ref = ref();
const jiaoyu2Ref = ref();
const jiuye1Ref = ref();
const jiuye2Ref = ref();
const nianling1Ref = ref();
const nianling2Ref = ref();
const getReportRef = ref();
const loading = ref();
const dialogTableVisible = ref(false); //对话框显示
const dialogSingleLeftVisible = ref(false); //对话框显示
const dialogSingleRightVisible = ref(false); //对话框显示
const rate = ref([]);
const buildingTypes = ref([]);
const box_copyObj = ref('');
const box_copyObjTwo = ref('');
const multipleSelection = ref([]);
const leftData = ref({});
const rightData = ref({});
const tableDataLeft = ref([]);
const tableDataRight = ref([]);
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const tableColumns = [
	{
		title: '资产名称',
		dataIndex: 'buildingName',
		width: '150',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '资产类型',
		dataIndex: 'buildingType',
		width: '100',
	},
	{
		title: '地址',
		dataIndex: 'street',
		width: '300',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '建筑面积',
		dataIndex: 'buildingSize',
		width: '100',
	},
	{
		title: '维护情况',
		dataIndex: 'maintenance',
		width: '100',
	},
	{
		title: '单价',
		dataIndex: 'absoluteValue',
		width: '100',
	},
];
const jyLegend = [
	// color: ['#378EFF', '#3DD598', '#FFC542', '#FC5A5A', '#A6BEFE'],
	{
		color: '#378EFF',
		name: '高中以下',
	},
	{
		color: '#37B7FF',
		name: '高中',
	},
	{
		color: '#39DDE8',
		name: '专科',
	},
	{
		color: '#2FE2AC',
		name: '学士/硕士/博士',
	},
];
const jiuyeLegend = [
	{
		color: '#378EFF',
		name: '白领',
	},
	{
		color: '#37B7FF',
		name: '蓝领',
	},
	{
		color: '#39DDE8',
		name: '公共事业',
	},
];
const ageLegend = [
	{
		color: '#378EFF',
		name: '0-14岁',
	},
	{
		color: '#37B7FF',
		name: '15-64岁',
	},
	{
		color: '#39DDE8',
		name: '65岁以上',
	},
];

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
});
//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}
//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	console.log('🚀 ~ handleAssetsIds ~ obj:', obj);
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getComparativePopulation({
		buildingIds: obj.ids,
	})
		.then((res) => {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });

			console.log('🚀 ~ .then ~ tableDataLeft.value:', tableDataLeft.value);
			if (res.data.length == 1) {
				leftData.value = res.data[0];
				tableDataLeft.value.push(obj.arr[0]);
				setData_pie(res.data[0], []);
				nextTick(() => {
					jiaoyu1Ref.value.init();
					jiuye1Ref.value.init();
					nianling1Ref.value.init();
				});
			} else if (res.data.length == 2) {
				leftData.value = res.data[0];
				tableDataLeft.value.push(obj.arr[0]);
				rightData.value = res.data[1];
				tableDataRight.value.push(obj.arr?.[1]);
				setData_pie(res.data[0], res.data[1]);
				nextTick(() => {
					jiaoyu1Ref.value.init();
					jiuye1Ref.value.init();
					nianling1Ref.value.init();
					jiaoyu2Ref.value.init();
					jiuye2Ref.value.init();
					nianling2Ref.value.init();
				});
			}
			multipleSelection.value = obj.arr;
			loading.value.close();
		})

		.catch((err) => {
			console.log(err, 'err');
		});
}

// 复制
function handlerCopy(type) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(type === 1 ? box_copyObj.value : type === 2 ? box_copyObjTwo.value : handlerBtnBox())
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = type === 1 ? box_copyObj.value : type === 2 ? box_copyObjTwo.value : handlerBtnBox();
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

function handlerBtnBox() {
	const obj = rightData.value;
	const obj1 = leftData.value;

	if (!obj || !obj1) return '';
	const dimensions = [
		{ key: 'totalPopulation', label: '总人口' },
		{ key: 'ageMid', label: '年龄中位数' },
		{ key: 'householdsNum', label: '家庭数' },
		{ key: 'disposableIncome', label: '人均收入' },
	];
	let matchedLabels = [];
	const equalLabels = [];
	dimensions.forEach(({ key, label }) => {
		if (obj[key] > obj1[key]) {
			matchedLabels.push(label);
		}
		if (obj[key] == obj1[key]) {
			equalLabels.push(label);
		}
	});
	// 教育情况
	const educationDiffs = [
		{ key: 'educationBachelorMasterDoctor', label: '学士/硕士/博士占比' },
		{ key: 'educationNoHigh', label: '高中以下占比' },
		{ key: 'educationHigh', label: '高中占比' },
		{ key: 'educationJuniorCollege', label: '专科占比' },
	];
	const eduArrs = [];
	educationDiffs.forEach(({ key, label }) => {
		if (obj[key] > obj1[key]) {
			eduArrs.push(label);
		}
	});
	if (eduArrs.length > 0) {
		eduArrs[0] = `教育情况${eduArrs[0]}`;
	}
	const jobDiffs = [
		{ key: 'whiteCollar', label: '白领占比' },
		{ key: 'blueCollar', label: '蓝领占比' },
		{ key: 'publicUtilities', label: '公共事业占比' },
	];
	const jobArrs = [];
	jobDiffs.forEach(({ key, label }) => {
		if (obj[key] > obj1[key]) {
			jobArrs.push(label);
		}
	});
	if (jobArrs.length > 0) {
		jobArrs[0] = `就业情况${jobArrs[0]}`;
	}
	const ageDiffs = [
		{ key: 'ageZeroFourteen', label: '0-14岁人口占比' },
		{ key: 'ageFifteenSixtyfour', label: '15-64岁人口占比' },
		{ key: 'ageSixtyfiveAbove', label: '65岁以上人口占比' },
	];
	const ageArrs = [];
	ageDiffs.forEach(({ key, label }) => {
		if (obj[key] > obj1[key]) {
			ageArrs.push(label);
		}
	});
	if (ageArrs.length > 0) {
		ageArrs[0] = `年龄情况${ageArrs[0]}`;
	}
	matchedLabels = matchedLabels.concat(eduArrs, jobArrs, ageArrs);
	let first = '';
	let second = '';
	if (equalLabels.length > 0) {
		first = `${obj.buildingName}半径一公里的${equalLabels.join('、')}持平;`;
	}
	if (matchedLabels.length > 0) {
		if (equalLabels.length == 0) {
			second = `${obj.buildingName}半径一公里的${matchedLabels.join('、')}高于${obj1.buildingName}。`;
		} else {
			second = `${matchedLabels.join('、')}高于${obj1.buildingName}。`;
		}
	}
	return first + second;
}

// 比较
function handlerLineBox(type) {
	if (type === 1) {
		let obj = null;
		if (tableDataLeft.value.length > 0 && leftData?.value) {
			if (tableDataLeft.value[0]?.buildingName === leftData.value?.buildingName) {
				obj = leftData.value;
			} else {
				obj = rightData.value;
			}
		}
		if (!obj) return '';
		let name =
			obj.buildingName +
			'教育情况学士/硕士/博士占比' +
			formattedMoney(handleNumber(obj.educationBachelorMasterDoctor)) +
			'%，专科占比' +
			formattedMoney(handleNumber(obj.educationJuniorCollege)) +
			'%，高中占比' +
			formattedMoney(handleNumber(obj.educationHigh)) +
			'%；就业情况公共事业占比' +
			formattedMoney(handleNumber(obj.publicUtilities)) +
			'%，蓝领占比' +
			formattedMoney(handleNumber(obj.blueCollar)) +
			'%，白领占比' +
			formattedMoney(handleNumber(obj.whiteCollar)) +
			'%；年龄情况15-64岁占比' +
			formattedMoney(handleNumber(obj.ageFifteenSixtyfour)) +
			'%，0-14岁占比' +
			formattedMoney(handleNumber(obj.ageZeroFourteen)) +
			'%，65岁以上占比' +
			formattedMoney(handleNumber(obj.ageSixtyfiveAbove)) +
			'%。';
		if (name) {
			box_copyObj.value = name;
		}
		return name;
	} else {
		let obj1 = null;
		if (tableDataRight.value.length > 0 && leftData?.value) {
			if (tableDataRight.value[0]?.buildingName === leftData.value?.buildingName) {
				obj1 = leftData.value;
			} else {
				obj1 = rightData.value;
			}
		}
		if (!obj1) return '';
		let name =
			obj1.buildingName +
			'教育情况学士/硕士/博士占比' +
			formattedMoney(handleNumber(obj1.educationBachelorMasterDoctor)) +
			'%，专科占比' +
			formattedMoney(handleNumber(obj1.educationJuniorCollege)) +
			'%，高中占比' +
			formattedMoney(handleNumber(obj1.educationHigh)) +
			'%；就业情况公共事业占比' +
			formattedMoney(handleNumber(obj1.publicUtilities)) +
			'%，蓝领占比' +
			formattedMoney(handleNumber(obj1.blueCollar)) +
			'%，白领占比' +
			formattedMoney(handleNumber(obj1.whiteCollar)) +
			'%；年龄情况15-64岁占比' +
			formattedMoney(handleNumber(obj1.ageFifteenSixtyfour)) +
			'%，0-14岁占比' +
			formattedMoney(handleNumber(obj1.ageZeroFourteen)) +
			'%，65岁以上占比' +
			formattedMoney(handleNumber(obj1.ageSixtyfiveAbove)) +
			'%。';

		if (name) {
			box_copyObjTwo.value = name;
		}
		return name;
	}
}

const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		leftData.value = null;
		rightData.value = null;
		tableDataLeft.value = [];
		tableDataRight.value = [];
		jiaoyu_left.value = null;
		jiaoyu_right.value = null;
		jiuye_left.value = null;
		jiuye_right.value = null;
		nianling_left.value = null;
		nianling_right.value = null;
		emit('handleBuildingId', { ids: null, arr: [] });
		return;
	}
	let ids = multipleSelection.value.map((item) => item.id).join(',');
	getComparativePopulation({
		buildingIds: ids,
	})
		.then((res) => {
			// 重置所有数据
			tableDataLeft.value = [];
			tableDataRight.value = [];
			leftData.value = null;
			rightData.value = null;
			// 同步更新数据和表格
			if (multipleSelection.value.length === 1) {
				leftData.value = res.data[0];
				tableDataLeft.value = [multipleSelection.value[0]];
				setData_pie(res.data[0], []);
				nextTick(() => {
					jiaoyu1Ref.value.init();
					jiuye1Ref.value.init();
					nianling1Ref.value.init();
				});
			} else if (multipleSelection.value.length === 2) {
				leftData.value = res.data[0];
				rightData.value = res.data[1];
				tableDataLeft.value = [multipleSelection.value[0]];
				tableDataRight.value = [multipleSelection.value[1]];
				setData_pie(res.data[0], res.data[1]);
				nextTick(() => {
					jiaoyu1Ref.value.init();
					jiuye1Ref.value.init();
					nianling1Ref.value.init();
					jiaoyu2Ref.value.init();
					jiuye2Ref.value.init();
					nianling2Ref.value.init();
				});
			}
			emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
			dialogTableVisible.value = false;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const removePercentSigns = (str) => {
	return Number(str);
};
//插入数据
const setData_pie = (left, right) => {
	jiaoyu_left.value = {
		name: left.buildingName,
		data: [
			{
				value: removePercentSigns(left.educationNoHigh),
				name: '高中以下',
			},
			{
				value: removePercentSigns(left.educationHigh),
				name: '高中',
			},
			{
				value: removePercentSigns(left.educationJuniorCollege),
				name: '专科',
			},
			{
				value: removePercentSigns(left.educationBachelorMasterDoctor),
				name: '学士/硕士/博士',
			},
		],
	};
	jiaoyu_right.value = {
		name: right.buildingName,
		data: [
			{
				value: removePercentSigns(right.educationNoHigh),
				name: '高中以下',
			},
			{
				value: removePercentSigns(right.educationHigh),
				name: '高中',
			},
			{
				value: removePercentSigns(right.educationJuniorCollege),
				name: '专科',
			},
			{
				value: removePercentSigns(right.educationBachelorMasterDoctor),
				name: '学士/硕士/博士',
			},
		],
	};

	jiuye_left.value = {
		name: left.buildingName,
		data: [
			{
				value: removePercentSigns(left.whiteCollar),
				name: '白领',
			},
			{
				value: removePercentSigns(left.blueCollar),
				name: '蓝领',
			},
			{
				value: removePercentSigns(left.publicUtilities),
				name: '公共事业',
			},
		],
	};
	jiuye_right.value = {
		name: right.buildingName,
		data: [
			{
				value: removePercentSigns(right.whiteCollar),
				name: '白领',
			},
			{
				value: removePercentSigns(right.blueCollar),
				name: '蓝领',
			},
			{
				value: removePercentSigns(right.publicUtilities),
				name: '公共事业',
			},
		],
	};

	nianling_left.value = {
		name: left.buildingName,
		data: [
			{
				value: removePercentSigns(left.ageZeroFourteen),
				name: '0-14岁',
			},
			{
				value: removePercentSigns(left.ageFifteenSixtyfour),
				name: '15-64岁',
			},
			{
				value: removePercentSigns(left.ageSixtyfiveAbove),
				name: '65岁以上',
			},
		],
	};
	nianling_right.value = {
		name: right.buildingName,
		data: [
			{
				value: removePercentSigns(right.ageZeroFourteen),
				name: '0-14岁',
			},
			{
				value: removePercentSigns(right.ageFifteenSixtyfour),
				name: '15-64岁',
			},
			{
				value: removePercentSigns(right.ageSixtyfiveAbove),
				name: '65岁以上',
			},
		],
	};
};

// 获取字典
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	save();
}
function handleBuildLeftConfirm(data) {
	multipleSelection.value[0] = data[0];
	save();
}
function handleBuildRightConfirm(data) {
	multipleSelection.value[1] = data[0];
	save();
}
function clear(type) {
	if (type == 'left') {
		tableDataLeft.value = [];
		multipleSelection.value.shift();
	} else {
		tableDataRight.value = [];
		multipleSelection.value.pop();
	}
	save();
}
</script>
<style lang="less" scoped>
.comparison_box {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.common_wrap {
		padding: 20px 16px;
		background-color: #fff;
		display: flex;
		gap: 16px;
		border-radius: 0px 4px 4px 4px;
		.left_empty_wrap,
		.left_content_wrap,
		.right_empty_wrap,
		.right_content_wrap {
			flex: 1;
		}
		.left_empty_wrap,
		.right_empty_wrap {
			border: 1px solid #e5e6eb;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 4px;
			padding: 54px 0;
			.icon {
				width: 64px;
				height: 64px;
			}
		}
		.title_wrap {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;
		}
		.table_wrap {
		}
		.desc_wrap {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20px;
			border-radius: 4px;
			overflow: hidden;
			margin-top: 16px;
			.desc {
				max-width: 660px;
				z-index: 9;
				font-size: 14px;
				font-weight: 600;
				line-height: 22px;
				color: #1d2129;
			}
			.copy {
				z-index: 9;
				padding: 5px 16px;
				border: 1px solid #1868f1;
				color: #1868f1;
				background: #e8f3ff;
				border-radius: 4px;
				cursor: pointer;
			}
			.bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
		}
	}
	.chart_wrap {
		flex: 1;
		border-radius: 4px;
		margin-top: 16px;
		padding: 20px 16px 16px 16px;
		background-color: #fff;
		.header_wrap {
			display: flex;
			align-items: center;
			.line {
				width: 4px;
				height: 14px;
				background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
				border-radius: 4px;
			}
			.title {
				color: #1d2129;
				font-size: 20px;
				font-weight: 600;
				margin-left: 8px;
				margin-right: 20px;
				line-height: 28px;
			}
		}
		.echars_main {
			margin-top: 16px;
			width: 100%;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: wrap;
			gap: 16px;
			.box_ {
				width: calc(50% - 8px);
				height: 472px;
				border: 1px solid #e5e6eb;
				border-radius: 4px;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				flex-direction: column;
				.title1 {
					box-sizing: border-box;
					padding: 0 20px;
					width: 100%;
					height: 48px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #f7f8fa;
					border-bottom: 1px solid #e5e6eb;
					.title {
						font-size: 16px;
						font-weight: 600;
						color: #1d2129;
					}
					.download {
						color: #1868f1;
						.download_icon {
							font-size: 16px;
							cursor: pointer;
						}
					}
				}
				.content_wrap {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: center;
					.key_wrap {
						width: 560px;
						height: 344px;
						display: flex;
						gap: 8px;
						.key_left,
						.key_right {
							position: relative;
							flex: 1;
							.header {
								box-sizing: border-box;
								height: 48px;
								background: linear-gradient(180deg, #ccdeff 0%, #e7efff 100%);
								border-radius: 8px 8px 0 0;
								display: flex;
								align-items: center;
								justify-content: center;
								padding: 0 32px;
								font-size: 16px;
								font-weight: 600;
								line-height: 24px;
								color: #1d2129;
								.text {
									text-align: center;
									overflow: hidden; /* 超出部分隐藏 */
									white-space: nowrap; /* 单行文本不换行 */
									text-overflow: ellipsis; /* 超出部分显示省略号 */
								}
							}
							.val_item {
								margin-top: 2px;
								display: flex;
								flex-direction: column;
								align-items: center;
								justify-content: center;
								background: #f5f9ff;
								height: 72px;
								.val_wrap {
									display: flex;
									align-items: flex-end;
									color: #378eff;
									.val {
										font-size: 28px;
										font-weight: 600;
										line-height: 30px;
										margin-right: 4px;
									}
									.unit {
										font-size: 14px;
										font-weight: 400;
										line-height: 24px;
									}
								}
								.label {
									color: #86909c;
									font-size: 14px;
									font-weight: 400;
									line-height: 22px;
								}
							}
							.val_item:last-child {
								border-radius: 0 0 0 8px;
							}
						}
						.key_right {
							.vs {
								position: absolute;
								top: 16.5px;
								left: -33px;
								width: 66px;
								height: 66px;
							}
							.header {
								background: linear-gradient(180deg, #b7eeff 0%, #e5f9ff 100%);
							}
							.val_item {
								background: #f5fdff;
								.val_wrap {
									color: #00b89b;
								}
							}
							.val_item:last-child {
								border-radius: 0 0 8px 0;
							}
						}
					}
				}
				.pie_wrap {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					.pie_box {
						width: 100%;
						display: flex;
						justify-content: space-between;
						align-items: center;
						> :nth-child(n) {
							display: flex;
							justify-content: center;
						}
					}
					.legend_wrap {
						display: flex;
						align-items: center;
						justify-content: center;
						gap: 20px;
						.legend_item {
							display: flex;
							align-items: center;
							gap: 8px;
							.circle {
								width: 8px;
								height: 8px;
								border-radius: 50%;
							}
							.name {
								font-size: 14px;
								font-weight: 600;
								line-height: 22px;
								color: #4e5969;
							}
						}
					}
				}

				.empty_wrap {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: 400;
					color: #86909c;
					img {
						width: 80px;
						height: 80px;
					}
				}
			}
		}
		.summary_wrap {
			margin-top: 16px;
			position: relative;
			display: flex;
			align-items: center;
			height: 62px;
			padding: 0 20px;
			.bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
			.icon {
				width: 40px;
				height: 40px;
				z-index: 99;
				margin-right: 12px;
			}
			.summary {
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
				color: #1d2129;
				z-index: 99;
				padding-right: 60px;
			}
			.copy {
				z-index: 99;
				padding: 5px 16px;
				border: 1px solid #1868f1;
				color: #1868f1;
				background: #e8f3ff;
				border-radius: 4px;
				cursor: pointer;
				position: absolute;
				right: 20px;
			}
		}
	}

	.arco-btn-size-medium {
		border-radius: 4px;
	}
}
</style>
