<template>
	<div class="map_box">
		<el-drawer
			v-model="drawerShow"
			destroy-on-close
			direction="btt"
			:z-index="99"
			size="60%"
			modal-class="drawer_modal"
			class="customer_drawer"
			:with-header="false"
		>
			<div class="drawer_wrap">
				<div class="title">
					<div class="left">
						可视区域内共 <span class="num">{{ cardList.length }}</span> 个楼宇
					</div>
					<div class="right" @click="handleMarket">
						市场统计
						<el-icon><ArrowRight /></el-icon>
					</div>
				</div>
				<div class="list_wrap">
					<div class="list_item" v-for="(item, index) in cardList" :key="item.buildingId" @click="handleBuildOverview(item.buildingId)">
						<div class="img"><img :src="http_oa + item.mainImgUrl" /></div>
						<div class="detail">
							<div class="title">{{ item.buildingName }}</div>
							<div class="year">{{ item.buildYear }}年建成 · {{ item.street }}</div>
							<div class="tag_wrap">
								<div class="tag">维护{{ item.maintenance }}</div>
								<div class="tag tag_bg1">区域潜力-{{ item.regionalPotential }}</div>
								<div class="tag tag_bg2">商业活力-{{ item.businessDynamism }}</div>
							</div>
							<div class="price_wrap">
								<div class="price">
									<div class="num">{{ item.evaluation }}</div>
									<div class="unit">元/平</div>
								</div>
								<div class="desc">
									人均可支配收入<span class="money">{{ item.spendingPower }}</span
									>元
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</el-drawer>
		<div class="search">
			<div class="gaode">
				<!-- <div class="details" v-if="radioDraw && selectedRadius !== '' && selectedRadiusStr">
					<div class="titleContent">
						<el-dropdown v-if="radioDraw === 'circle'" :teleported="false" trigger="click" :hide-on-click="true">
							<div class="radius_box">
								<div class="radius_box_item">选择半径</div>
								<div class="radius_box_item_2">
									{{ selectedRadius ? selectedRadius + 'M' : '300M' }}<el-icon style="margin: -1.5px 0 0 2px"><CaretBottom /></el-icon>
								</div>
							</div>
							<template #dropdown>
								<el-dropdown-menu class="detailsDropdownMenu">
									<el-dropdown-item @click="handleButtonClick(300)" :style="{ color: selectedRadius === 300 ? '#1868F1!important' : '' }"
										>300M<el-icon :style="{ display: selectedRadius === 300 ? 'block' : 'none' }"><CircleCheckFilled /></el-icon
									></el-dropdown-item>
									<el-dropdown-item @click="handleButtonClick(500)" :style="{ color: selectedRadius === 500 ? '#1868F1!important' : '' }"
										>500M<el-icon :style="{ display: selectedRadius === 500 ? 'block' : 'none' }"><CircleCheckFilled /></el-icon
									></el-dropdown-item>
									<el-dropdown-item @click="handleButtonClick(1000)" :style="{ color: selectedRadius === 1000 ? '#1868F1!important' : '' }"
										>1000M<el-icon :style="{ display: selectedRadius === 1000 ? 'block' : 'none' }"><CircleCheckFilled /></el-icon
									></el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
						<div v-if="radioDraw === 'polygon'" class="titleLocation">请在地图上点击选点圈画范围</div>
					</div>
				</div> -->
				<div class="draw_circle_wrap" v-if="drawCircleShow">
					<div class="left_wrap">
						<div class="label">选择半径</div>
						<div class="tag_wrap">
							<div @click="handleButtonClick(300)" class="tag" :class="{ tag_active: selectedRadius === 300 }">300m</div>
							<div @click="handleButtonClick(500)" class="tag" :class="{ tag_active: selectedRadius === 500 }">500m</div>
							<div @click="handleButtonClick(1000)" class="tag" :class="{ tag_active: selectedRadius === 1000 }">1000m</div>
						</div>
					</div>
					<div class="right_wrap">
						<div class="quit" @click="handlequit">
							<IconCloseCircle size="20" />
							<view class="text">退出</view>
						</div>
						<div class="line"></div>
						<div class="redraw" @click="handleReDraw">
							<img :src="selectedRadius == '' ? drawCircle_disabled : drawCircle" class="img" style="width: 20px; height: 20px" />
							<view class="text" :style="{ color: selectedRadius == '' ? '#C9CDD4' : '#1D2129' }">重画</view>
						</div>
					</div>
				</div>
				<div class="search_wrap" v-if="!drawCircleShow">
					<mapFilter @confirm="searchConfirm" @reset="searchReset" @filterShow="filterShow"></mapFilter>
				</div>
				<div class="action_wrap" v-if="!drawCircleShow">
					<div class="position_wrap" @click="handlePositioning">
						<img :src="positioning" class="img" />
						定位
					</div>
					<div class="line"></div>

					<div class="draw_wrap" @click="handleDraw('circle')">
						<img :src="drawCircle" class="img" />
						画圈
					</div>
					<div class="line"></div>
					<div class="position_wrap" @click="handleList">
						<img :src="list" class="img" />
						列表
					</div>
				</div>
				<gaode
					@updateRadius="handleUpdateRadius"
					@handlePolygon="handlePolygon"
					@handleupDateCity="handleupDateCity"
					@clickChild="clickEven"
					@clickMarker="gomap"
					@buildOverview="handleBuildOverview"
					:radius="selectedRadius"
					:radioDraw="radioDraw"
					:markers="cardList"
					@searchMap="getCardListData"
					ref="aMap"
				></gaode>
			</div>
		</div>
		<div class="tabbar_wrap">
			<div class="tabbar_item">
				<div class="active_name_wrap">
					<img src="@/assets/images/h5/home.png" />
					首页
				</div>
			</div>
			<div class="tabbar_item" @click="handleMenu('workbench')">
				<div class="name_wrap">工作台</div>
			</div>
			<div class="tabbar_item" @click="handleMenu('my')">
				<div class="name_wrap">我的</div>
			</div>
		</div>
		<div class="mask_wrap" v-if="maskShow"></div>
	</div>
</template>

<script setup>
import arcoSelect from '@/component/arcoComponents/select/index.vue';
import { ref, onMounted, computed, reactive, nextTick } from 'vue';
import gaode from './MapContainer.vue';
import { getMarketStatistics, getDictList, getTransactionStatistics, getBuildingCardList, getAllBusinessDistrict } from '@/api/syt.js';
import { toRaw } from 'vue';
import { useStore } from '../../store';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { IconCloseCircle } from '@arco-design/web-vue/es/icon';
import mapFilter from './components/mapFilter.vue';
import positioning from '@/assets/images/h5/positioning.png';
import drawCircle from '@/assets/images/h5/drawCircle.png';
import drawCircle_disabled from '@/assets/images/h5/drawCircle_disabled.png';
import list from '@/assets/images/h5/list.png';
const activeName = ref('first');
const listShow = ref(true);
const selectedRadiusStr = ref(false);
const tableShow = ref(false);
const radioDraw = ref(); // 画圆还是画多边形
const store = useStore();
const { http_oa } = storeToRefs(store);
const aMap = ref(null);
const route = useRoute();
const tableColumns = [
	{
		prop: '',
		label: '序号',
		width: 60,
	},
	{
		prop: 'buildingType',
		label: '资产类型',
	},
	{
		prop: 'buildingNum',
		label: '数量',
	},
	{
		prop: 'coveredArea',
		label: '覆盖面积(万m²)',
	},
];
const lng = ref(null);
const lat = ref(null);
const real = ref([]);
const quStats = ref(false);
let lnglatObj = reactive({
	maxLatitude: '',
	minLatitude: '',
	maxLongitude: '',
	minLongitude: '',
});
const city = ref('');
const county = ref('');
// 选中城市
const cascaderDom = ref(null);
const cascaderModel = ref([]);
const checkedObj = ref([]);
const selectedRadius = ref(''); // 默认半径值
const rateValue = ref('');
const buildingTypesValue = ref('');
const securitization = ref('');
const inputValue = ref('');
const keyword = ref('');
const cardList = ref([]);
const copyOptions = ref([]);
const real1 = ref([]);
const rate = ref([]);
const buildingTypes = ref([]);
const businessDistrict = ref(''); //商圈
const businessDistrictList = ref([]); //商圈列表
const drawerShow = ref(false);
const deviceUuid = ref(null);

const propsCascader = ref({
	value: 'label',
	children: 'children',
});

// 初始化
onMounted(() => {
	console.log('mounteddddddd');
	// handleSearch();
	getDict();
	adjustBottomSafeArea();
	deviceUuid.value = route.query.deviceUuid;
	if (deviceUuid.value) {
		initSocket();
	}
});
watch(
	() => route.query.deviceUuid,
	(val) => {
		console.log('route.query.deviceUuid', val);
		// deviceUuid.value = val;
		// initSocket();
		window.location.reload();
	}
);
const socket = ref(null);
function initSocket() {
	if (socket.value) {
		socket.value.close();
	}
	// 创建 WebSocket 连接
	// const wsUrl = `ws://47.105.119.206:8081/api/ws/${deviceUuid.value}_h5/h5`; // 本地测试
	const wsUrl = `wss://www.bbzhun.com/api/ws/${deviceUuid.value}_h5/h5`; // 线上

	try {
		socket.value = new WebSocket(wsUrl);
		// 连接事件
		socket.value.addEventListener('open', (event) => {
			console.log('🚀 ~ socket.value.addEventListener ~ event:', event);
		});
		// 消息事件
		socket.value.addEventListener('message', (event) => {
			console.log('🚀 ~ socket.value.addEventListener ~ event:', event);
			const message = JSON.parse(event.data);
		});
		// 关闭事件
		socket.value.addEventListener('close', (event) => {
			console.log('🚀 ~ socket.value.addEventListener ~ event:', event);
			socket.value = null;
		});
		// 错误事件
		socket.value.addEventListener('error', (event) => {
			console.log('🚀 ~ socket.value.addEventListener ~ event:', event);
		});
		// 启动心跳检测
		startHeartbeat();
	} catch (error) {
		console.log('🚀 ~ initSocket ~ error:', error);
	}
}
// 心跳检测
let heartbeatInterval;
function startHeartbeat() {
	if (heartbeatInterval) {
		clearInterval(heartbeatInterval);
	}
	heartbeatInterval = setInterval(() => {
		if (socket.value && socket.value.readyState === WebSocket.OPEN) {
			const heartbeat = {
				type: 'heartbeat',
				timestamp: Date.now(),
			};
			socket.value.send(JSON.stringify(heartbeat));
		} else {
			clearInterval(heartbeatInterval);
		}
	}, 30000); // 30秒一次心跳
}

function adjustBottomSafeArea() {
	let bottomInset = 0;
	if (navigator) {
		const isIOS = /iPhone|iPad|iPod/.test(navigator?.userAgent) || false;
		if (isIOS) {
			bottomInset = parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--safe-area-bottom')) || 0;
		} else {
			// 安卓机型可能需要自定义逻辑（如检测虚拟导航栏高度）
			bottomInset = 34; // 默认适配主流安卓机型
		}
	} else {
		bottomInset = 34;
	}
	console.log('🚀 ~ adjustBottomSafeArea ~ bottomInset:', bottomInset);
	document.querySelector('.map_box').style.paddingBottom = `${bottomInset}px`;
}
function handleArcoSelect(val) {
	if (val) {
		getBuildingCardList({
			currentPage: 1,
			pageSize: 10000,
			city: city.value,
			district: county.value,
			buildingRate: rateValue.value, // 资产评级
			businessDistrict: businessDistrict.value || '', //商圈
			distribution: securitization.value, //证券化
			buildingType: buildingTypesValue.value, // 资产类型
			keyword: val,
		}).then((res) => {
			if (res.code === 200) {
				if (res.data?.rows?.length > 0) {
					copyOptions.value = setHightLight(res.data?.rows, val);
				} else {
					copyOptions.value = [];
				}
			}
		});
	} else {
		copyOptions.value = [];
	}
}

function handleChangeOption(val) {
	if (val.buildingName) {
		cardList.value = [val];
		copyOptions.value = [val];
		nextTick(() => {
			aMap.value.addMarker();
			aMap.value.handleSetCenter([cardList.value[0].lng, cardList.value[0].lat], 0, 1);
		});
	} else {
		inputValue.value = '';
		copyOptions.value = [];
		// searchBtn();
		handleClear('1', '1');
		// cardList.value = []
	}

	console.log(val, 'valvalvalvalval');
}

function setHightLight(arr, keyword) {
	if (arr) {
		let newArr = JSON.parse(JSON.stringify(arr));
		if (newArr && newArr.length > 0 && keyword) {
			newArr = newArr.filter((item) => {
				item.buildingTitle = item.buildingName;
				let reg = new RegExp(keyword, 'g');
				let replaceString = `<span style='color: #1868f1;font-weight: 500;'>${keyword.trim()}</span>`;
				if (item.buildingTitle.match(reg)) {
					item.buildingTitle = item.buildingTitle.replace(reg, replaceString);
					return item;
				}
			});
			return newArr;
		}
		// 空返回原数组
		if (!keyword) {
			return newArr;
		}
	}
}

// 收起展开
const handleTableShow = () => {
	tableShow.value = !tableShow.value;
	listShow.value = !tableShow.value;
};

// 获取商圈
function funcDistrict() {
	getAllBusinessDistrict({ city: city.value, district: county.value, current: 1, size: 100 }).then((res) => {
		if (res.code === 200) {
			businessDistrictList.value = res.data.rows;
		}
	});
}

//绑定大楼
const handleSearch = async () => {
	real.value = [];
	await getMarketStatistics(queryParams.value)
		.then((res) => {
			if (res.data?.length > 0) {
				let newList = toRaw(res.data);
				real.value = JSON.parse(JSON.stringify(newList));
			}
		})
		.catch((err) => {
			console.log('err', err);
			// 显示错误信息给用户
		});
};

const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = [
				{
					color: null,
					jsonObject: null,
					label: '资产类型不限',
					text: '资产类型不限',
					title: '资产类型不限',
					value: '',
				},
				...res.data,
			];
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = [
				{
					color: null,
					jsonObject: null,
					label: '资产评级不限',
					text: '资产评级不限',
					title: '资产评级不限',
					value: '',
				},
				...res.data,
			];
		})
		.catch((err) => {
			console.log(err);
		});
};

const drawCircleShow = ref(false);
// 绘制多边形
function handleDraw(val) {
	if (val === radioDraw.value) {
		handlequit();
		radioDraw.value = '';
		selectedRadius.value = '';
		return;
	}
	radioDraw.value = val;
	cardList.value = [];
	drawCircleShow.value = true;
	if (val === 'polygon') {
		selectedRadius.value = '';
		aMap.value.clearMarker();
		aMap.value.handleCircle();
	}
}
function handleReDraw() {
	aMap.value.clearMarker();
	aMap.value.handleCircle();
	cardList.value = [];
	selectedRadius.value = '';
}
function handleList() {
	drawerShow.value = true;
}

function handleReduce(type) {
	if (aMap.value) {
		aMap.value.setZoom(type);
	}
}

// 清除搜索条件
function handleClear(type, num) {
	// county.value = ''; // 市区
	if (!num) {
		selectedRadius.value = ''; // 半径
		rateValue.value = ''; // 评级
		buildingTypesValue.value = ''; // 类型
		securitization.value = ''; // 证券化
		inputValue.value = ''; // 关键字
		keyword.value = '';
		copyOptions.value = [];
	}
	handlequit(type);
}

// 搜索
const searchBtn = () => {
	radioDraw.value = '';
	cardList.value = [];
	selectedRadius.value = '';
	if (aMap.value) {
		aMap.value.handleCircle();
		if (checkedObj.value.length > 0) {
			// 城市更改后且第一次点击搜索
			aMap.value.handleSetCenter(quStats.value ? checkedObj.value : null, !quStats.value ? 1 : 0);
			quStats.value = false;
		} else {
			getCardListData(1);
		}
	}
};

// 获取定位城市
function handleupDateCity(address) {
	// cascaderModel.value = [address.city, address.district];
	cascaderModel.value = [address.city, address.district];

	city.value = address.city;
	county.value = address.district;
	funcDistrict();
}
// 更新半径
const handleUpdateRadius = (radius) => {
	selectedRadius.value = radius;
	setTimeout(() => {
		selectedRadiusStr.value = true;
	}, 500);
};

// 获取地图四个角经纬度
const clickEven = (lnglat) => {
	// selectedRadiusStr.value = false;
	if (selectedRadius.value) {
		lng.value = lnglat?.clickPosition?.lng;
		lat.value = lnglat?.clickPosition?.lat;
	} else {
		lng.value = '';
		lat.value = '';
	}
	lnglatObj.maxLatitude = lnglat?.bounds?.northEast?.lat; // 右上角纬度
	lnglatObj.minLatitude = lnglat?.bounds?.southWest?.lat; // 左下角经度
	lnglatObj.maxLongitude = lnglat?.bounds?.northEast?.lng; // 右上角经度
	lnglatObj.minLongitude = lnglat?.bounds?.southWest?.lng; // 左下角维度
	nextTick(() => {
		getCardListData(lnglat.type);
	});
};

// 切换城市
const handleChange = (val, selectedOptions) => {
	// 字符串转数组截取
	businessDistrict.value = '';
	let obj = {};
	cascaderDom.value.filteredLeafOptions.forEach((element) => {
		if (element.key === cascaderDom.value.activeKey) {
			obj = element;
		}
	});
	checkedObj.value = [obj?.raw?.lng, obj?.raw?.lat];

	// checkedObj.value = [cascaderDom.value.getCheckedNodes()?.[0]?.data?.lng, cascaderDom.value.getCheckedNodes()?.[0]?.data?.lat];
	if (val) {
		quStats.value = true;
		city.value = val[0];
		county.value = val[1];
		cascaderModel.value = [val[0], val[1]];
		funcDistrict();
		// 搜索
		searchBtn();
	} else {
		city.value = '';
		county.value = '';
		cascaderModel.value = [];
	}
};

function handleDistrictSearch(val) {
	if (val) {
		// searchBtn(val);
		radioDraw.value = '';
		cardList.value = [];
		selectedRadius.value = '';
		getCardListData(1, 1);
	} else {
		handleClear();
	}
}

//半径圆实现
const queryParams = computed(() => {
	return {
		currentPage: 1,
		pageSize: lnglatObj.minLongitude ? 10000 : 100,
		city: city.value,
		district: county.value,
		buildingRate: rateValue.value,
		businessDistrict: businessDistrict.value || '', //商圈
		distribution: securitization.value, //证券化
		buildingType: buildingTypesValue.value,
		keyword: keyword.value,
		// keyword: inputValue.value.buildingName,
		deal: deal.value,
		lng: lng.value,
		lat: lat.value,
		...lnglatObj,
		distance: selectedRadius.value, // 半径
	};
});
// 多边形
function handlePolygon(params) {
	cardList.value = params;
}
// 卡片信息
const getCardListData = (value, type) => {
	cardList.value = [];
	if (selectedRadius.value || (selectedRadius.value == '' && !value) || businessDistrict.value) {
		queryParams.value.maxLatitude = '';
		queryParams.value.minLatitude = '';
		queryParams.value.maxLongitude = '';
		queryParams.value.minLongitude = '';
	}

	if (!selectedRadius.value) {
		queryParams.value.lng = '';
		queryParams.value.lat = '';
	}

	// 市场统计
	handleSearch();
	getBuildingCardList(queryParams.value)
		.then((res) => {
			if (res.code === 200) {
				if (res.data?.rows?.length > 0) {
					cardList.value = res.data?.rows;
					console.log('🚀 ~ .then ~ cardList.value:', cardList.value);
					nextTick(() => {
						aMap.value.addMarker();
						if (type) {
							aMap.value.handleSetCenter([cardList.value[0].lng, cardList.value[0].lat], 0, 1);
						}
					});
				}
			} else {
				cardList.value = [];
				aMap.value.clearMarker();
			}
		})
		.catch((err) => {
			cardList.value = [];
			if (aMap.value) {
				aMap.value.clearMarker();
			}
			console.log('err', err);
			// 显示错误信息给用户
		});
};

//点击跳转地图
const gomap = (item) => {
	let params = {
		target: {
			data: item,
		},
	};
	if (aMap.value) {
		aMap.value.clickMarker(params, '1');
	}
};

// 退出
function handlequit(type) {
	if (aMap.value) {
		radioDraw.value = '';
		selectedRadius.value = '';
		aMap.value.handleCircle();
		drawCircleShow.value = false;
		if (type == '1' && businessDistrict.value) {
			getCardListData(1, 1);
		} else {
			aMap.value.handleSetCenter(null, 1);
		}
	}
}

// 点击半径
const handleButtonClick = (radius) => {
	selectedRadius.value = radius;
	handleUpdateRadius(radius);
	if (aMap.value) {
		aMap.value.creatCircle(null, radius);
	}
	getCardListData('');
};

// 文本展示
const truncateAddress = (address, maxLength = 15) => {
	if (address.length <= maxLength) {
		return address;
	} else {
		return address.slice(0, maxLength) + '...';
	}
};
const deal = ref('');
function searchConfirm(data) {
	maskShow.value = false;
	city.value = data.city;
	county.value = data.district;
	businessDistrict.value = data.businessName;
	inputValue.value = data.keyword;
	keyword.value = data.keyword;
	buildingTypesValue.value = data.buildingType;
	rateValue.value = data.buildingRate;
	securitization.value = data.distribution;
	deal.value = data.deal;
	if (data.longitude > 0 && data.latitude > 0) {
		aMap.value.handleSetCenter([data.longitude, data.latitude], 1);
	} else {
		aMap.value.handleSetCenter(null, 1);
	}
}
function searchReset() {
	maskShow.value = false;
	city.value = '';
	county.value = '';
	businessDistrict.value = '';
	inputValue.value = '';
	keyword.value = '';
	buildingTypesValue.value = '';
	rateValue.value = '';
	securitization.value = '';
	deal.value = '';
	aMap.value.handleSetCenter(null, 1);
}
const maskShow = ref(false);
function filterShow(show) {
	maskShow.value = show;
}
function handlePositioning() {
	aMap.value.getLoaction();
}
function handleMenu(type) {
	// window.postMessage('测试数据', 'https://bbzhun.com/#/marketMap');
	// window.parent.postMessage({ type: 'response', content: '来自H5的响应' }, '*');
	if (!socket.value || socket.value.readyState !== WebSocket.OPEN) {
		return;
	}
	const message = {
		type: 'text',
		fromUserId: `${deviceUuid.value}_h5`,
		fromClientType: 'h5',
		toUserId: deviceUuid.value,
		toClientType: null,
		content: {
			type,
		},
		timestamp: Date.now(),
	};
	socket.value.send(JSON.stringify(message));
}
function handleBuildOverview(id) {
	if (!socket.value || socket.value.readyState !== WebSocket.OPEN) {
		return;
	}
	const message = {
		type: 'text',
		fromUserId: `${deviceUuid.value}_h5`,
		fromClientType: 'h5',
		toUserId: deviceUuid.value,
		toClientType: null,
		content: {
			type: 'buildingOverview',
			id,
		},
		timestamp: Date.now(),
	};
	socket.value.send(JSON.stringify(message));
}
function handleMarket() {
	if (!socket.value || socket.value.readyState !== WebSocket.OPEN) {
		return;
	}
	const message = {
		type: 'text',
		fromUserId: `${deviceUuid.value}_h5`,
		fromClientType: 'h5',
		toUserId: deviceUuid.value,
		toClientType: null,
		content: {
			type: 'market',
			...lnglatObj,
		},
		timestamp: Date.now(),
	};
	socket.value.send(JSON.stringify(message));
}
</script>
<style lang="less">
.arco-cascader-option-active {
	color: #1868f1 !important;
}
.drawer_modal {
	background-color: transparent;
}
.customer_drawer {
	border-radius: 8px 8px 0 0;
	.el-drawer__body {
		padding: 16px 12px;
	}
}
</style>
<style lang="less" scoped>
::v-deep .city_box_cascader > :nth-child(1) {
	--el-input-border-color: #fff;
	--el-input-hover-border-color: #fff;
	--el-input-focus-border-color: #fff;
}

.map_box {
	position: relative;
	width: 100%;
	height: 100vh;
	.drawer_wrap {
		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.left {
				font-weight: 600;
				font-size: 15px;
				line-height: 22px;
				color: #1d2129;
				.num {
					color: #1868f1;
				}
			}
			.right {
				font-weight: 400;
				font-size: 13px;
				line-height: 20px;
				color: #4e5969;
				display: flex;
				align-items: center;
				gap: 2px;
			}
		}
		.list_wrap {
			.list_item {
				box-sizing: border-box;
				height: 120px;
				padding: 8px 0;
				border-bottom: 1px solid #e5e6eb;
				display: flex;
				gap: 12px;
				.img {
					min-width: 104px;
					max-width: 104px;
					height: 104px;
					overflow: hidden;
					border-radius: 8px;
					img {
						width: 100%;
						height: 100%;
					}
				}
				.detail {
					flex: 1;
					padding: 2.5px 0;
					.title {
						display: block;
						color: #1d2129;
						font-size: 14px;
						font-weight: 600;
						line-height: 22px;
						margin-bottom: 4px;
						white-space: nowrap; /* 强制文本不换行 */
						overflow: hidden; /* 隐藏溢出内容 */
						text-overflow: ellipsis; /* 溢出时显示省略号 */
						width: 244px;
					}
					.year {
						font-size: 12px;
						font-weight: 400;
						color: #4e5969;
						margin-bottom: 4px;
					}
					.tag_wrap {
						display: flex;
						gap: 4px;
						margin-bottom: 16px;
						.tag {
							padding: 0 4px;
							border-radius: 2px;
							background-color: #e8f3ff;
							color: #1868f1;
							font-size: 10px;
							font-weight: 400;
							line-height: 16px;
							display: flex;
							align-items: center;
							justify-content: center;
						}
						.tag_bg1 {
							background-color: #e7f8f8;
							color: #12b8b4;
						}
						.tag_bg2 {
							background-color: #fff3e8;
							color: #f77234;
						}
					}
					.price_wrap {
						display: flex;
						align-items: center;
						justify-content: space-between;
						.price {
							display: flex;
							color: #f53f3f;
							align-items: flex-end;
							.num {
								font-size: 18px;
								font-weight: 700;
								line-height: 18px;
								font-family: 'DDPRO';
							}
							.unit {
								font-size: 10px;
								font-weight: 500;
								line-height: 15px;
								margin-left: 2px;
							}
						}
						.desc {
							font-size: 10px;
							font-weight: 400;
							line-height: 16px;
							color: #4e5969;
						}
					}
				}
			}
		}
	}
	.mask_wrap {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		height: 100%;
		width: 100%;
		background-color: rgba(0, 0, 0, 0.6);
	}
}
.search {
	position: relative;
	width: 100%;
}
.tabbar_wrap {
	box-sizing: content-box;
	position: fixed;
	bottom: 0px;
	left: 0;
	width: 100%;
	height: 52px;
	display: flex;
	background-color: #fff;
	box-shadow: 0rpx -2px 8px 0px #00000014;
	border-radius: 16px 16px 0 0;
	z-index: 10;
	padding-bottom: env(safe-area-inset-bottom); /* 备用方案 */
	padding-bottom: constant(safe-area-inset-bottom); /* 备用方案 */
	.tabbar_item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		padding-top: 8px;
		padding-bottom: 8px;
		.name_wrap,
		.active_name_wrap {
			width: 72px;
			height: 34px;
			border-radius: 100px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 16px;
			font-weight: 600;
			color: #1d2129;
			line-height: 22px;
		}
		.active_name_wrap {
			font-size: 14px;
			color: #fff;
			background-color: #1868f1;
			img {
				width: 16px;
				height: 16px;
				margin-right: 2px;
			}
		}
	}
	.tabbar_item:first-child {
		margin-left: 8px;
	}
	.tabbar_item:last-child {
		margin-right: 8px;
	}
}

.details {
	z-index: 9;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(10px, -37px);
	width: 132px;
	height: 90px;
	// display: flex;
	// justify-content: space-between;
	// align-items: center;
	box-sizing: border-box;
	background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%);
	border-radius: 6px;
	font-size: 14px;
	.titleContent {
		// display: flex;
		// align-items: center;

		.radius_box {
			height: 66px;
			padding: 12px 8px;
			border-radius: 4px;
			background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%);
			.radius_box_item {
				width: 100%;
				height: 22px;
				font-weight: 500;
				font-size: 14px;
				line-height: 22px;
				color: #1d2129;
				margin-bottom: 4px;
			}
			.radius_box_item_2 {
				width: 92px;
				height: 22px;
				padding: 9px 12px;
				border-radius: 4px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #ffffff;
				box-shadow: 6px 0px 20px 0px #2257bc1a;
				font-weight: 500;
				font-size: 14px;
				color: #4e5969;
			}
		}
		img {
			width: 16px;
			height: 16px;
		}
		.titleLocation {
			margin: 0 10px;
			font-size: 14px;
			font-weight: 500;
			line-height: 22px;
			color: #fff;
		}

		.detailsDropdown {
			font-size: 14px;
			font-weight: 400;
			line-height: 22px;
			color: #fff;
			display: flex;
			align-items: center;
		}
	}
}

.gaode {
	width: 100%;
	height: 100vh;
	position: relative;
	.draw_circle_wrap {
		position: absolute;
		left: 12px;
		right: 12px;
		top: 88px;
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 8px;
		.left_wrap,
		.right_wrap {
			background-color: #fff;
			border-radius: 8px;
			height: 56px;
			box-sizing: border-box;
		}
		.left_wrap {
			flex: 2;
			padding: 9px 16px;
			.label {
				color: #86909c;
				font-size: 10px;
				font-weight: 400;
				line-height: 10px;
				margin-bottom: 4px;
			}
			.tag_wrap {
				display: flex;
				gap: 4px;
				.tag {
					color: #1d2129;
					font-size: 14px;
					font-weight: 400;
					line-height: 22px;
					padding: 1px 9px;
					box-sizing: border-box;
					border-radius: 4px;
				}
				.tag_active {
					background-color: #e8f3ff;
					font-weight: 600;
					color: #1868f1;
				}
			}
		}
		.right_wrap {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 16px;
			padding: 12px 16px;
			.quit,
			.redraw {
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				.text {
					font-size: 10px;
					font-weight: 400;
					line-height: 10px;
					color: #1d2129;
					margin-top: 2px;
				}
			}
			.line {
				height: 22px;
				width: 1px;
				background-color: #e5e6eb;
			}
		}
	}
	.search_wrap {
		background-color: #fff;
		position: absolute;
		left: 12px;
		right: 12px;
		top: 88px;
		z-index: 99;
		border-radius: 8px;
	}
	.action_wrap {
		padding: 8px 0;
		width: 40px;
		background-color: #fff;
		position: absolute;
		right: 12px;
		top: 194px;
		z-index: 1;
		border-radius: 8px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.position_wrap,
		.draw_wrap {
			display: flex;
			flex-direction: column;
			align-items: center;
			color: #1d2129;
			font-size: 10px;
			.img {
				width: 20px;
				height: 20px;
				margin-bottom: 2px;
			}
		}
		.line {
			width: 22px;
			border-bottom: 1px #e5e6eb solid;
			margin: 8px 0;
		}
	}
}

.detailsDropdownMenu {
	width: 100px;
	> :nth-child(n) {
		margin: 4px 8px;
		border-radius: 4px;
		padding: 7px 8px !important;
		font-size: 12px;
		font-weight: 500;
		display: flex;
		justify-content: space-between;
		color: #1d2129;
		.el-icon {
			display: none;
			fill: #1868f1;
		}
		&:hover {
			background: #f5f6f7 !important;
			color: #1d2129;
		}
	}
}
</style>
