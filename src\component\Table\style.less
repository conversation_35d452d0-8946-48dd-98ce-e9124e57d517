.system-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;

    * {
        user-select: text;
    }

    a {
        text-decoration: underline;
    }

    &-container {
        height: 100%;
        position: relative;

        .system-table-content {
            width: 100%;
            position: absolute;
            height: 100%;
            display: flex;
            flex-direction: column;

            .el-table {
                background-size: 100% 100%;
                display: flex;
                flex-direction: column;

                .el-table__header-wrapper {
                    width: 100%;
                }

                .el-table__body-wrapper {
                    width: 100%;

                    .el-table__row {
                        height: 45px;

                        td {
                            &:first-child {
                                border-left: none;
                            }
                        }
                    }

                    .el-table__inner-wrapper {
                        height: 100% !important;
                    }
                }

                th.el-table__cell {
                    font-size: 14px;


                }

                th.el-table-fixed-column--right,
                th.el-table-fixed-column--left,
                td.el-table-fixed-column--right,
                td.el-table-fixed-column--left,
                tr.hover-row,
                .el-table__body-wrapper {
                    // 表格字体统一调小
                    font-size: 14px;
                }
            }
        }
    }

    .pagination {
        height: 32px;

        .el-pagination {
            display: flex;
            justify-content: flex-end;
            padding: 0 20px;
        }
    }
}