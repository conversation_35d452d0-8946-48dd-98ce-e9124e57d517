<template>
  <h3>表格名称</h3>
    <el-table :data="tableData" style="width: 100%" :header-cell-style="{background:'#3170a7',color:'#ffffff '}">
      <el-table-column prop="date" label="Date" width="180" />
      <el-table-column prop="name" label="Name" width="180" />
      <el-table-column prop="address" label="Address" />
      <el-table-column prop="address" label="Address" />
    </el-table>
    <h3>表格名称</h3>
    <el-table :data="tableData" style="width: 100%;margin-top: 20px;" :header-cell-style="{background:'#3170a7',color:'#ffffff '}">
      <el-table-column prop="date" label="Date" style="width: 100%;text-align: center;"/>
      <!-- <el-table-column prop="name" label="Name" width="1000" /> -->
    </el-table>
  </template>
  
  <script setup>
  const tableData = [
    {
      date: '2016-05-03',
      name: '<PERSON>',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-02',
      name: '<PERSON>',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-04',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
    {
      date: '2016-05-01',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
    },
  ]
  </script>

  <style scoped>

  </style>
  