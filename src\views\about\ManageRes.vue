<template>
	<div class="container">
		<div class="top" style="text-align: center">
			<p>基本信息</p>
			<el-table :data="tableData" border style="width: 100%">
				<el-table-column v-for="item in tableColumns" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.width" />
			</el-table>
		</div>
		<el-row :gutter="20">
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<p>趋势信息</p>
			</el-col>
			<el-col :span="12"
				><div class="grid-content ep-bg-purple" />
				<p>关联物业</p>
				<el-card class="box-card" v-for="item in correLation" :key="item.id" @click="handle(item)">
					<el-image style="width: 150px; height: 100px" :src="`${proxyAddress}${'/upfile_jpg/'}${item.id}${'.jpg'}`" fit="scale-down" />
					<div style="margin-left: 10px">
						<el-text tag="p" style="font-size: 16px; margin-bottom: 8px">{{ item.name }}</el-text>
						<el-text tag="p">{{ item.address }}</el-text>
					</div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import http from '@/utils/http';
const tableData = ref([]);
const route = useRoute();
const correLation = ref([]);
const proxyAddress = process.env.NODE_ENV === 'development' ? 'http://*************:8080/' : 'http://**************:8081/';

const router = useRouter();
const tableColumns = [
	{
		prop: 'enterpriseName',
		label: '企业名称',
		width: '',
	},
	{
		prop: 'legalRepresentative',
		label: '法定代表人',
		width: '',
	},
	{
		prop: 'establishmentTime',
		label: '成立时间',
		width: '',
	},
	{
		prop: 'regCapital',
		label: '注册资本',
		width: '',
	},
	{ prop: 'officialWebsite', label: '官网', width: '' },
	{ prop: 'regAddress', label: '注册地址', width: '' },
	{ prop: 'realControl', label: '实控人', width: '' },
];
onMounted(async () => {
	const manage = route.query.id;
	const res = await http.get('/api/a_new_jiaoyi.do?act=getManageBaseInfo', { params: { manageOwner: manage, action: 'manage' } });
	tableData.value = res.baseInfo;
	correLation.value = res.mapList;
	console.log(tableData.value);
});
const handle = (row) => {
	console.log(row.id);
	router.push({
		path: `/main/property/${row.id}`,
	});
};
</script>

<style lang="less" scoped>
.box-card :deep(.el-card__body) {
	display: flex;
}
.box-card {
	height: 150px;
	margin-top: 10px;
	border-radius: 20px;
}
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 36px;
}
</style>
