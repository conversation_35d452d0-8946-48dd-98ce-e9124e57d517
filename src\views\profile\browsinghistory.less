.demo-tabs {
  .table-container {
    display: flex;
    flex-wrap: wrap;
    .details_box{
      display: flex;
      width: 363px;
      height: 82px;
      margin: 8px 0px 0 16px!important;
    }
    .details-box {
      // width: calc(50% - 40px);
      position: relative;
      display: flex;
      justify-content: space-between;
      width:  363px;
      height: 82px;
      // margin-bottom: 20px;
      margin: 8px 6px 32px 16px!important;
      &:hover{
        background:rgb(245, 246, 247) ;
      }
    }
    .center_line{
      position: absolute;
      top: 9px;
      right: 110px;
      width: 2px;
      height: 64px;
      background: linear-gradient(180deg, rgba(231, 231, 231, 0) 0%, #E7E7E7 50%, rgba(231, 231, 231, 0) 100%);
    }
      .operates{
        background-color:rgba(245, 246, 247, 1)!important;
      }
      .operate {
        width: 251px;
        height: 82px;
        // background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        border: 1px solid rgba(201, 205, 212, 1);
        border-right: 0px solid rgba(201, 205, 212, 1);
        display: flex;
        // align-items: center;
        // flex-direction: column;
        justify-content: space-between;
        .type {
          width: 15px;
          height: 82px;
          display: flex;
          padding-left: 4px;
          align-items: center;
          justify-items: center;
          border-top-left-radius: 6px;
          border-bottom-left-radius: 6px;
          color: #ffffff;
          // font-family: Alibaba PuHuiTi;
          font-size: 11px;
          font-weight: 1000;
        }

        .unused {
          background-color: rgba(201, 205, 212, 1);

        }

        .occupied {
          color: rgba(249, 194, 155, 1)!important;
          background-color: rgba(29, 33, 41, 1)!important;
        }

        .newUser {
          color: #FFFFFF!important;
          background: linear-gradient(90deg, #FF504C 0%, #FE8042 100%)!important;
        }

        .xse {
          background-color: rgba(24, 104, 241, 1);
        }

        .invalid{
          border-right:1px solid #E7E7E7;
          color: rgba(201, 205, 212, 1);
          background-color:rgba(245, 246, 247, 1);
        }
        // .button-data :hover{
        //   background:rgba(245, 246, 247) ;
        // }
        .button-data {
          width:232px;
          height: 74px;
          padding: 4px 16px;
          cursor: pointer;
          
          .button_top_contents>:nth-child(1){
           color: rgba(134, 144, 156, 1);
          }
          .invalid_content{
            color: rgba(29, 33, 41, 1)!important;
            font-size: 12px;
            height: 20px;
            line-height: 20px;
          }
          .button_content_twos{
            color: rgba(134, 144, 156, 1)!important;
          }
          .button_top_content{
            margin-top: 4px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: end;
          }
          .button_top_content>:nth-child(1){
            font-size: 16px;
            font-weight: 700;
          }
          .button_top_content>:nth-child(2){
            width: 36px;
            height: 20px;
            border-radius: 10px 2px;
            line-height: 20px;
            font-weight: 400;
            font-size: 12px;
            text-align: center;
            color: #ffffff;
            background-color: rgba(24, 104, 241, 1);
            
          }
          .button_content_two{
            height: 22px;
            font-weight: 700;
            line-height: 22px;
            font-size: 14px;
            color: rgba(78, 89, 105, 1);
            margin-bottom: 8px;
          }
          .button_contents{
            height: 20px;
            font-weight: 400;
            color: rgba(134, 144, 156, 1);
            line-height: 20px;
            font-size: 12px;
          }
          .button_contentts{
            color: rgba(24, 104, 241, 1);
          }
          // div {
          //   background-color: rgba(0, 121, 254, 0.24705882352941178);
          //   margin-bottom: 10px;
          //   color: #0079FE;
          //   width: 100px;
          //   height: 30px;
          //   border-radius: 18px;
          //   font-size: 14px;
          //   text-align: center;
          //   line-height: 30px;
          // }
          
        }
      }
      .right_content{
        width: 112px;
        height: 82px;
        background-color: rgba(245, 246, 247, 1);
        border-radius: 6px;
        border: 1px solid rgba(201, 205, 212, 1);
        border-left:0px solid rgba(201, 205, 212, 1); 
        line-height: 82px;
        color: rgba(134, 144, 156, 1);
        font-size: 12px;
        text-align: center;
        cursor: pointer;
  }
      .right-content {
        width: 95px;
        height: 66px;
        border-radius: 6px;
        border: 1px solid rgba(201, 205, 212, 1);
        border-left: 0px solid rgba(201, 205, 212, 1);
        padding: 8px ;
        .right_top_content{
          height: 28px;
          width: 96px;
          line-height: 28px;
          font-weight: 400;
          text-align: center;
          border-radius: 6px;
          color: rgba(134, 144, 156, 1);
          font-size: 12px;
          cursor: pointer;
          margin-bottom: 8px;
          &:hover{
            background: rgba(231, 231, 231, 1);
          }
        }

        .right_bottom_content{
          width: 96px;
          font-weight: 400;
          height: 28px;
          background: rgba(24, 104, 241, 1);
          border-radius: 6px;
          font-size: 12px;
          line-height: 28px;
          text-align: center;
          cursor: pointer;
        }
        .right_bottom_content_border{
          border: 1px solid rgba(24, 104, 241, 1)!important;
          background-color: #ffffff!important;
          color: rgba(24, 104, 241, 1)!important;
        }
        .right_bottom_content_noble{
          background-color: rgba(29, 33, 41, 1)!important;
          color: rgba(249, 194, 155, 1)!important;
        } 
        .right_bottom_content_nobleNewUser{
          background: linear-gradient(90deg, #FF504C 0%, #FE8042 100%)!important;
          color: #FFFFFF!important;
        } 
        
        .right_bottom_content_ordinary{
          background-color: rgba(24, 104, 241, 1)!important;
          color: rgba(255, 255, 255, 1)!important;
        }
      }
   
  }
}

.demo-tabs{
	margin-left: 16px;

  ::v-deep .el-tabs__active-bar {
    height: 3px!important;
    background-color: transparent !important;
    background-image: linear-gradient(
    	90deg, transparent 0, transparent 33%,
    	#4d72f6 0, #4d72f6 67%,
    	transparent 0, transparent
    );
}
  /* 下划线颜色 */
  ::v-deep .el-tabs__active-bar {
    background-color: rgba(3, 93, 255, 1);
    // width: 50%!important;
  }
  ::v-deep .el-tabs__item.is-active {
    color: rgba(29, 33, 41, 1);//选中
    opacity: 1;
    font-size: 14px;
    font-weight: 500;
  }
  ::v-deep .el-tabs__item {
    height: 70px;
    line-height: 70px;
    color:rgba(78, 89, 105, 1);
  }
  ::v-deep .el-tabs__nav{
    transform: translateX(16px)!important;
  }
}

.card_search_content{
	padding: 9px 16px 0px 16px;
	display: flex;
	justify-content: space-between;
}
.icon_sort{
	margin:1px 0 0 4px;
  img{
   width: 13px; 
  }
}
.icon_sortf{
	margin:1px  0px 0 4px;
  img{
    width: 13px; 
   }
}

.form_left>:nth-child(n){
	margin-right:20px ;
}
.form_right{
	font-size: 14px;
	display: flex;
  height: 24px;
	line-height: 27px;
	color: rgba(78, 89, 105, 1);
}

.form_rights{
	font-size: 14px;
	display: flex;
  height: 24px;
	line-height: 27px;
	color: rgba(78, 89, 105, 1);
}
  

.form_rights>:nth-child(1){
  width: 75px;
}

.form_rights>:nth-child(n){
  display: flex;
	cursor: pointer;
}

.form_right>:nth-child(1){
  width: 75px;
  margin-right: 58px;
}

.form_right>:nth-child(n){
  display: flex;
	cursor: pointer;
}

.hide_card_roll{
  width: 100%;
  margin-bottom: 16px;
  .hide_card_rollt{
    cursor: pointer;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgba(134, 144, 156, 1);
    font-size: 14px;
    font-weight: 400;
    margin-top: -6px;
    div{
      margin-right: 8px;
    }
  }
   

}
.custom_table_height{
  margin-top: -7px;
  max-width:calc(100% - 16px);
  border: none;
  ::v-deep .el-table__body{
    //-webkit-border-horizontal-spacing: 13px;  // 水平间距
    -webkit-border-vertical-spacing: 8px;  // 垂直间距
  } 
 
  --el-table-row-hover-bg-color:rgba(245, 248, 253, 1) !important;
  --el-table-border-color:none
 
 // 上面的线
 ::v-deep th.el-table__cell.is-leaf{
    border: none;
  }


}

.dialogHeader{
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  height: 56px;
  line-height: 56px;
  padding:0 16px;
  font-size: 16px;
  color: rgba(29, 33, 41, 1);
  border-bottom: 1px solid rgba(231, 231, 231, 1);
}
.dialogHeaders{
  border-bottom:none!important;
  font-weight: 700!important;

}
.dialogHeaderLeft{
  display: flex;
  align-items: center;
  color: #000000D9;
  font-size: 16px;
}
.dialogHeaderRight>:nth-child(1){
  cursor: pointer;
  margin:19px 0px 0 0;
}

::v-deep .el-dialog{
  padding: 0;
}

.container_contentDia{
  height: 394px;
  padding:0 16px;
  overflow-y: scroll;

  .contentDia{
    display: flex;
    align-items: center;
    .accountUser{
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 16px;
      font-size: 12px;
      font-weight: 500;
      line-height: 20px;
      color: #1868F1;
      margin-top: -1px;
      div{
        margin-top: 2px;
      }
    }
  }

}

.dialog_footer{
  height: 76px;
  width: 100%;
  border-top:1px solid rgba(231, 231, 231, 1) ;
}

.dialog_footers{
  border-top:none!important
}
.dialog_footer>:nth-child(1){
  height: 76px;
  display: flex;
  margin-right:16px ;
  align-items: center;
  justify-content: flex-end;
}

::-webkit-scrollbar{ /*滚动条整体样式*/
  width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
::-webkit-scrollbar-thumb{ /*滚动条里面小方块*/
  border-radius: 5px;
  background: #cfcbcb;
}
::-webkit-scrollbar-track{ /*滚动条里面轨道*/
  border-radius: 6px;
  background: #ededed;
}

::-webkit-scrollbar {  width: 5 !important;height: 0;}

.dialogRomove{
  height: 44px;
  padding: 0 24px;
}
.dialogRomove>:nth-child(n){
  height: 22px;
  line-height: 22px;
  color: #4E5969;
  font-size: 14px;
  font-weight: 500;
}

.table_container{
  align-content: flex-start;
  height: 558px;
  overflow-y: scroll;
}

.browsinghistory{
  background: #fff;
  height: 100%;
  border-radius: 6px;
}

.welfare>:nth-child(n){
  margin: 8px 6px 32px 16px !important;
}

.unavailable{
  width: 100%;
  text-align: center;
  line-height: 476px;
  font-size: 13px;
  color: #999;
}


.tag_boxTitle {
  width: auto;
  height: 30px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background-color: #1868f1;
    margin-right: 10px;
  }
}

.tag_box{
  height: calc(100vh - 226px);
  overflow: scroll;
}

.demo_tabs{
  --el-tabs-header-height:20px;
  border-radius: 12px;
  height: 688px;
}

.demo_tabs>:nth-child(1){
  background-color: unset;
    height: 40px;
}

::v-deep .demo_tabs {
  .el-tabs__nav{
    transform: translateX(0px) !important;
  }
  .el-tabs__item{
    background-color: unset!important;
    width: 120px;
    height: 40px;
    font-weight: 600;
  }
  .el-tabs__item.is-active{
    font-weight: 600;
    color: #1868f1 !important;
  }
  .el-tabs__nav>:nth-child(2){
    border-right:none
  }
}