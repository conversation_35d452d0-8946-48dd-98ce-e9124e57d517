<template>
	<div class="newUserVolume">
		<div class="mask"></div>
		<div class="content">
			<div class="titleBtn" @click="handleReceive">立即领取</div>
			<img class="imgf" src="../../assets/volumeNew.png" alt="" />
			<img class="imgt" @click="handleClose" src="../../assets/newUserVolumecolse.png" alt="" />
		</div>
	</div>
</template>

<script setup>
import { defineEmits } from 'vue';
import { vuexStore } from '../../store';

const emit = defineEmits(['handleClose', 'handleReceive']);
// 关闭
function handleClose() {
	// 关闭
	emit('handleClose');
	// 改变vuex内新用户体验弹窗状态
	vuexStore.commit('handleNewUserCouponDefaultShow', false); // 新用户体验弹窗
}
// 立即领取
function handleReceive() {
	emit('handleReceive');
}
</script>
<style lang="scss" scoped>
.newUserVolume {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
	z-index: 2024;
	display: flex;
	justify-content: center;
	align-items: center;
	.content {
		position: relative;
		z-index: 2024;
		display: flex;
		flex-direction: column;
		align-items: center;
		.titleBtn {
			position: absolute;
			right: 10px;
			width: 260px;
			height: 48px;
			top: 341px;
			left: 45px;
			background: #fff;
			border-radius: 8px;
			z-index: 2025;
			cursor: pointer;
			font-size: 18px;
			font-weight: 600;
			color: #1868f1;
			text-align: center;
			line-height: 48px;
		}
		.imgf {
			width: 350px;
			height: 430px;
			z-index: 2024;
		}
		.imgt {
			cursor: pointer;
			width: 32px;
			height: 32px;
			margin-top: 16px;
		}
	}

	.mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		z-index: 2023;
	}
}
</style>
