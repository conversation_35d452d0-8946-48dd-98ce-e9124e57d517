<template>
	<el-dialog
		v-model="props.dialogVisible"
		:title="dialogTitle"
		style="width: 700px; border-radius: 6px"
		:close-on-click-modal="false"
		align-center="center"
		:before-close="handleClose"
	>
		<div v-if="successType" class="purchase-container">
			<div class="purchase-box">
				<div class="name">适用城市</div>
				<div class="value">
					{{ props.coupon.cityName }}
				</div>
			</div>
			<div class="single-box">
				<div class="single-details">
					<div class="name-box">
						<div class="name">{{ props.coupon.name }}</div>
						<div class="equity">{{ props.coupon.itemsLength }}项权益</div>
					</div>
					<div class="money">
						<span class="mo">￥</span><span class="num">{{ props.coupon.price }}</span> /
						{{ props.coupon.period === 'MONTH' ? '月' : '年' }}
					</div>
				</div>
			</div>
			<div class="count-add" style="border-bottom: 1px solid rgba(231, 231, 231, 1)">
				<div class="purchase-box">
					<div class="name">购买数量</div>
					<div class="value"><el-input-number @change="purchaseChange" min="1" max="99" v-model="purchaseNum" :step="1" step-strictly /></div>
				</div>
				<div class="burning">
					<div class="burning-time">
						购买时长：<span>{{ purchaseNum * 1 }}个{{ props.coupon.period === 'MONTH' ? '月' : '年' }}次</span>
					</div>
					<div class="prompt">如果您已有开通权益，使用后则相应延长各项权益的有效期限</div>
				</div>
			</div>

			<div class="form_content">
				<div class="content_box_left">
					<el-skeleton-item variant="image" v-if="!paymentStatus" style="width: 100%; height: 100%; background: #fff" />
					<iframe
						:src="paymentURL"
						v-show="paymentStatus === 'ALI_PC' && checkbox"
						frameborder="no"
						border="0"
						marginwidth="0"
						marginheight="0"
						scrolling="no"
						width="200"
						height="200"
						style="overflow: hidden; transform: scale(0.6); margin: -20px 0 0 -40px; transform-origin: 100px 50px; /* 确保从左上角开始缩放 */"
					>
					</iframe>
					<canvas v-show="paymentStatus === 'WX_NATIVE' && checkbox > 0" ref="qrcodeCanvas" class="qrcode"></canvas>
				</div>
				<div class="content_box_right">
					<el-checkbox value="Agree" v-model="checkbox" name="type" class="check_boxAgreement"
						>我已阅读并同意<span class="blueSpan">《商宇通权益订阅服务协议 》</span>
					</el-checkbox>

					<div class="content_box_price">
						<div>扫码支付</div>
						<div>¥</div>
						<div>{{ priceAll || priceTotal.toFixed(2) }}</div>
					</div>

					<div class="content_box_bottom">
						<el-radio-group v-model="paymentStatus" @change="handlePriceChangeAll">
							<el-radio value="ALI_PC" size="large">支付宝</el-radio>
							<el-radio value="WX_NATIVE" size="large">微信</el-radio>
						</el-radio-group>
					</div>
				</div>
			</div>
		</div>

		<div v-if="!successType" class="prosperity">
			<div class="prosperity-box">
				<img src="@/assets/prosperity.png" alt="" />
				<div class="title">购买成功</div>
				<div class="prompt-content">
					<div>您已成功购买“{{ props.coupon.name }}{{ props.coupon.type !== 'SINGLE' ? '套餐权益卡券' : '单组权益卡券' }}</div>
					<div>
						总购买时长为{{ purchaseNum * 1 }}个{{ props.coupon.period === 'MONTH' ? '月' : '年' }}，使用后根据购买时长相应延长各项权益的有效期限
					</div>
					<div>您可以在“个人中心-权益中心-我的权益卡券”查看及使用</div>
				</div>

				<el-button type="primary" @click="RightsCentre">去权益中心使用</el-button>
			</div>

			<div class="prosperityCoucher" v-if="couponDetail.couponsType">
				<div class="titleDetails">购买卡券得优惠好礼（已发放至个人中心-权益中心-福利卡券）</div>
				<sm-coucher :itemCoupons="couponDetail" @handleWelfareAddCrad="handleWelfareAddCrad"></sm-coucher>
			</div>
		</div>
	</el-dialog>
	<buySuccess ref="buySuccessRef" />
</template>

<script setup>
import buySuccess from '../../component/buySuccess/index.vue';
import smCoucher from '../../component/smCoucher/index.vue';
import QRCode from 'qrcode';
import { getCouponDetail } from '@/api/equityTerm.js';
import { orderOreate, orderStatus, getDiscount } from '@/api/rights';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter();
const emit = defineEmits();
const props = defineProps({
	dialogVisible: {
		//弹框
		type: Boolean,
		default: false,
	},
	//单组权益
	coupon: {
		type: Object,
		default: () => {},
	},
});
// 创建订单后支付状态
const createSuccessType = ref(true);
//购买成功弹出
const buySuccessRef = ref(null);
const qrcodeCanvas = ref(); // 微信二维码
const dialogTitle = ref('购买单组权益卡券'); //弹框标题
const paymentStatus = ref(''); //支付方式 WX_NATIVE 微信 ALI_PC 支付宝
const paymentURL = ref(''); //支付二维码
const successType = ref(true); //成功类型
const checkbox = ref(false); // 同意协议
const priceAll = ref(0); //折扣后的商品总价
const priceTotal = ref(0); // 商品总价
const discounts = ref(0); // 折扣
const timerId = ref(null); // 订单轮询定时器
const purchaseNum = ref(1); //购买数量
const orderId = ref(''); // 订单ID
const couponDetail = ref({
	// couponsType: '1',
}); //优惠卷详情

// 清空数据
function handleClearData() {
	clearInterval(timerId.value);
	successType.value = true; //成功类型
	checkbox.value = false; //同意
	paymentStatus.value = ''; //支付状态
	priceTotal.value = 0; //总金额
	priceAll.value = 0; //应付金额
	purchaseNum.value = 1; //购买数量
	paymentURL.value = '';
}
// 关闭
function handleClose() {
	if (!createSuccessType.value) {
		buySuccessRef.value.show({
			paymentState: true,
			name: props.coupon.name,
			payType: paymentStatus.value,
			payableAmount: props.coupon.price,
		});
		createSuccessType.value = true;
	}
	handleClearData(); //清空数据
	emit('handleRightsClose');
}

// 权益中心
const RightsCentre = () => {
	handleClearData(); //清空数据
	emit('handleRightsClose');
	router.push({
		path: '/profile/browsingHistory',
	});
};

// 购买数量
const purchaseChange = (e) => {
	if (checkbox.value) {
		handlePriceChangeAll(); // 支付明细
	}
};
// 根据订单id获取优惠卷详情
const handleCouponDetail = (orderId) => {
	getCouponDetail({ outTradeNo: orderId }).then((res) => {
		if (res.code === 200 && res.data) {
			couponDetail.value = res.data;
			couponDetail.value.couponsType = '1';
		}
	});
};

// 跳转福利卡劵
function handleWelfareAddCrad() {
	handleClearData(); //清空数据
	emit('handleRightsClose');
	router.push({
		path: '/profile/browsingHistory',
		query: { type: 'third' },
	});
}

// 订单轮询获取支付状态
const pollOrderStatus = async (orderId) => {
	if (paymentStatus.value === '' || !checkbox.value) {
		return;
	}
	try {
		const response = await orderStatus(orderId);
		if (orderId === '') {
			return; // 提前返回，不执行后续代码
		}
		//PENDING("待支付"),        // 待支付
		// PAID("已支付"),          // 已支付
		// FAILED("支付失败"),      // 支付失败
		// CANCELLED("已取消"),     // 已取消
		// REFUNDED("已退款");      // 已退款
		if (response.code == 200) {
			// 根据返回的状态更新状态提示信息
			switch (response.data) {
				case 'PAID':
					ElMessage({
						message: `支付成功`,
						type: 'success',
					});
					clearInterval(timerId.value);
					createSuccessType.value = true;
					//根据订单id获取优惠卷详情
					handleCouponDetail(orderId);
					successType.value = false;
					break;
				case 'FAILED':
					ElMessage({
						message: `支付失败`,
						type: 'error',
					});
					clearInterval(timerId.value);
					break;
				case 'CANCELLED':
					ElMessage({
						message: `订单已取消`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				case 'REFUNDED':
					ElMessage({
						message: `已退款`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				default:
					break;
			}
		} else {
			clearInterval(timerId.value);
		}
	} catch (error) {
		clearInterval(timerId.value);
	}
};

//订单创建
function handleOrderCreate(params) {
	orderOreate(params).then((res) => {
		if (res.code == 200) {
			createSuccessType.value = false;
			let data = res.data;
			if (paymentStatus.value === 'ALI_PC') {
				// 支付宝
				paymentURL.value = data.url;
			} else {
				// 微信
				const qrCodeDiv = qrcodeCanvas.value;
				QRCode.toCanvas(qrCodeDiv, data.url, (error) => {
					if (error) console.error(error);
				});
			}

			if (data.outTradeNo) {
				orderId.value = data.outTradeNo;
				// 开始轮询
				timerId.value = setInterval(() => {
					pollOrderStatus(orderId.value);
				}, 1500);
			}
		}
	});
}

// 支付
function handlePayment() {
	//清楚定时器
	clearInterval(timerId.value);
	let arr = [
		{
			orderCount: 1, // 套餐数量
			businessType: 'COUPON_ORDER', // 订单类型
			couponId: props.coupon.id, // 套餐id
			quantity: purchaseNum.value, // 年月数量
			team: props.coupon.team, // 人数
			totalAmount: props.coupon.price * purchaseNum.value, // 总金额
		},
	];

	let params = {
		payType: paymentStatus.value, // 支付方式
		orderCount: 1, // 套餐数量
		totalAmount: props.coupon.price * purchaseNum.value, // 总金额
		discountAmount: Number(discounts.value), // 折扣
		payableAmount: Number(priceAll.value), // 应付金额
		orderDetails: arr, // 套餐
	};
	handleOrderCreate(params); // 订单创建
}

// 支付明细
function handlePriceChangeAll() {
	getDiscount({ totalAmount: Number(props.coupon.price.toFixed(2)) * purchaseNum.value }).then((res) => {
		if (res.code === 200) {
			discounts.value = Number(res.data.discountAmount.toFixed(2)); // 折扣
			priceAll.value = Number(res.data.payableAmount.toFixed(2)); // 应付金额
			handlePayment(); // 创建订单
		}
	});
}
</script>

<style lang="scss" scoped>
.purchase-container {
	.purchase-box {
		display: flex;
		height: 64px;
		align-items: center;
		background: #fff;
		.name {
			color: rgba(78, 89, 105, 1);
			font-size: 14px;
		}

		.value {
			margin-left: 20px;
		}
	}

	.count-add {
		display: flex;
		justify-content: space-between;
		align-items: center;
		// margin-top: 15px;
		padding: 15px 0;
		.burning {
			height: 42px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.burning-time {
				text-align: right;

				span {
					font-size: 14px;
					font-weight: 700;
					line-height: 22px;
					color: rgba(29, 33, 41, 1);
				}
			}
		}
	}
	.single-box {
		height: 80px;
		display: flex;
		border: 2px solid rgba(24, 104, 241, 1);
		border-radius: 8px;

		.single-details {
			height: 100%;
			width: calc(100% - 0px);
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 20px;

			.name-box {
				height: 60px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.name {
					font-size: 22px;
					font-weight: 700;
					color: rgba(29, 33, 41, 1);
				}

				.equity {
					font-size: 16px;
					font-weight: 700;
					color: rgba(78, 89, 105, 1);
				}
			}

			.money {
				font-size: 16px;
				font-weight: 700;
				color: rgba(29, 33, 41, 1);

				.mo {
					color: rgba(24, 104, 241, 1);
				}

				.num {
					font-size: 28px;
					font-weight: 500;
					color: rgba(24, 104, 241, 1);
				}
			}
		}
	}
}

.form_content {
	margin: 16px 0px 0px 0px;
	width: calc(100% - 0px);
	height: 152px;
	display: flex;
	justify-content: space-between;
	background: #f5f6f7;
	border-radius: 4px;
	.content_box_left {
		width: 120px;
		margin: 16px;
	}
	.content_box_right {
		margin: 16px 0;
		width: calc(100% - 152px);
		.check_boxAgreement {
			height: 22px;
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			margin-bottom: 34px;
		}
		.blueSpan {
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			color: rgba(24, 104, 241, 1);
		}

		.content_box_price {
			display: flex;
			height: 24px;
			margin-bottom: 10px;
			& > :nth-child(1) {
				font-size: 16px;
				font-weight: 700;
				line-height: 27px;
				color: #1d2129;
			}
			& > :nth-child(2) {
				font-size: 16px;
				font-weight: 700;
				line-height: 29px;
				color: #1868f1;
				margin: 0 8px;
			}
			& > :nth-child(3) {
				font-size: 28px;
				font-weight: 500;
				line-height: 24px;
				color: #1868f1;
				margin-bottom: -2px;
			}
		}

		.content_box_bottom {
			height: 22px;
			.el-radio-group {
				height: 24px;
				& > :nth-child(n) {
					height: 32px;
					margin-right: 10px;
				}
			}
		}
	}
}

.prosperity {
	width: 100%;

	.prosperity-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40px 40px 14px 40px;

		img {
			width: 50px;
			height: 50px;
		}

		.title {
			font-weight: 700;
			font-size: 20px;
			margin-top: 8px;
			margin-bottom: 16px;
			line-height: 32px;
			height: 32px;
			color: #1d2129;
		}

		.prompt-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			line-height: 22px;

			div {
				// margin: 5px 0;
				color: #4e5969;
				font-size: 14px;
			}
		}

		.el-button {
			margin-top: 18px;
			padding: 0 40px;
			width: 306px;
			height: 48px;
			background: #1868f1;
		}
	}
}

.qrcode {
	height: 136px !important;
	margin: -8px 0px 0 -8px;
	width: 136px !important;
}

.prosperityCoucher {
	width: 100%;
	height: 134px;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-top: 1px solid #e7e7e7;
	margin-bottom: 16px;
	.titleDetails {
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: center;
		color: #86909c;
		margin: 12px 0 10px 0;
	}
}
</style>
