<template>
	<div class="mian">
		<div class="oneTit">3.3客流量</div>
		<table class="MsoTableGrid" style="width: 100%">
			<tbody>
				<tr>
					<td valign="top" colspan="15" class style="text-align: left; position: relative">
						<div class="tab-tltle" style="margin-bottom: 5px">执行摘要</div>
						<div style="margin-bottom: 5px">{{ basic.buildingUniqueCode_dictText }},{{ basic.buildingDistrict }}, {{ basic.buildingCity }}</div>
						<div>时间：5，10，15分钟半径</div>
						<span style="position: absolute; bottom: 10px; right: 5px">术木智能</span>
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">人数统计</td>
					<td valign="top" colspan="4" v-for="stat in population.slice(1)" :key="stat.statisticalTime">{{ stat.statisticalTime }} 分钟</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">2010后</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.after2010 }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">2000后</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.after2000 }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">1990后</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.after1990 }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">1980后</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.after1980 }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">1970后</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.after1970 }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">1960后</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.after1960 }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">个人占比</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.individualProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">情侣占比</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.coupleProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">家庭占比</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.familyProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">多人占比</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">
						{{ (parseFloat(item.individualProportion) + parseFloat(item.coupleProportion) + parseFloat(item.familyProportion)).toFixed(2) }}%
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">男性占比</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.maleProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">女性占比</td>
					<td valign="top" colspan="4" v-for="(item, index) in population.slice(1)" :key="index">{{ item.femaleProportion }}%</td>
				</tr>
				<tr>
					<td colspan="14" style="text-align: left">
						数据说明：统计对象是已确定的建筑物。从客流统计结果，可以得出商业中心顾客的整体画像。客流密度和结构是商业中心定位的核心因素，也是不同商户考虑经营的重要原因。
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">不同时段人数</td>
					<td valign="top" colspan="4" v-for="stat in timenot.slice(1)" :key="stat.statisticalTime">{{ stat.statisticalTime }} 分钟</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">工作日上午</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.weekdayMorningProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">工作日中午</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.weekdayNoonProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">工作日下午</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.weekdayAfternoonProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">工作日晚上</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.weekdayNightProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">节假日上午</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.holidaysMorningProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">节假日中午</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.holidaysNoonProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">节假日下午</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.holidaysAfternoonProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">节假日晚上</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">{{ item.holidaysNightProportion }}%</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">一共</td>
					<td valign="top" colspan="4" v-for="(item, index) in timenot.slice(1)" :key="index">
						{{
							(
								parseFloat(item.weekdayMorningProportion) +
								parseFloat(item.weekdayNoonProportion) +
								parseFloat(item.weekdayAfternoonProportion) +
								parseFloat(item.weekdayNightProportion) +
								parseFloat(item.holidaysMorningProportion) +
								parseFloat(item.holidaysNoonProportion) +
								parseFloat(item.holidaysAfternoonProportion) +
								parseFloat(item.holidaysNightProportion)
							).toFixed(2)
						}}%
					</td>
				</tr>
				<tr>
					<td colspan="14" style="text-align: left">
						数据说明：商业中心时间的敏感性要超出我们的直观感觉，在不同时间段，客流有明显的变化，这种变化反映出顾客的购物习惯、地区的人口特性，这对管理一个商业中心意义重大，并且影像商户的展业决策。
					</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">外卖配送</td>
					<td valign="top" colspan="4" v-for="stat in waimai.slice(1)" :key="stat.statisticalTime">{{ stat.statisticalTime }} 分钟</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">工作日进出人次</td>
					<td valign="top" colspan="4" v-for="(item, index) in waimai.slice(1)" :key="index">{{ item.weekdayTakeoutNum }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">节假日进出人次</td>
					<td valign="top" colspan="4" v-for="(item, index) in waimai.slice(1)" :key="index">{{ item.holidaysTakeoutNum }}</td>
				</tr>
				<tr>
					<td colspan="14" style="text-align: left">数据说明：外卖密度可以反映地区和商圈人口的变化，页反映该商业中心的经营情况。</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">停车场进出车次</td>
					<td valign="top" colspan="4" v-for="stat in shop.slice(1)" :key="stat.statisticalTime">{{ stat.statisticalTime }} 分钟</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">工作日车次</td>
					<td valign="top" colspan="4" v-for="(item, index) in shop.slice(1)" :key="index">{{ item.weekdayTraffic }}</td>
				</tr>
				<tr>
					<td valign="center" colspan="2">节假日车次</td>
					<td valign="top" colspan="4" v-for="(item, index) in shop.slice(1)" :key="index">{{ item.holidaysTraffic }}</td>
				</tr>
				<tr>
					<td colspan="14" style="text-align: left">数据说明：对于停车场进出车次和车辆的洞察，可以直观地展示商业中心的繁荣程度和消费力。</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
import { perponOwn } from '@/api/baogao';
import { ref, onMounted } from 'vue';
const props = defineProps({
	id: {
		type: String,
		default: null,
		required: true,
	},
});
const basic = ref({});
const timenot = ref([]);
const waimai = ref([]);
const population = ref([]);
const shop = ref([]);
const getdata = async () => {
	const res = await perponOwn({ buildingId: props.id });
	if (res.code == 200) {
		basic.value = res.result;
		population.value = res.result.populationStatisticsList;
		timenot.value = res.result.differentTimeStatisticsList;
		waimai.value = res.result.takeawayDeliveryStatisticsList;
		shop.value = res.result.carInOutStatisticsList;
		population.value.unshift({
			after1960: '1960后',
			after1970: '1970后',
			after1980: '1980后',
			after1990: '1990后',
			after2000: '2000后',
			after2010: '2010后',
			coupleProportion: '情侣占比',
			familyProportion: '家庭占比',
			femaleProportion: '女占比',
			individualProportion: '个人占比',
			maleProportion: '男占比',
			statisticalTime: '时间',
		});
		timenot.value.unshift({
			statisticalTime: '不同时段人数',
		});
		waimai.value.unshift({
			statisticalTime: '外卖',
		});
		shop.value.unshift({
			statisticalTime: '停车场',
		});
	}
};
onMounted(() => {
	getdata();
});
</script>
<style lang="less" scoped>
.mian {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	.oneTit {
		margin-top: 30px;
		font-size: 24px;
		font-weight: 700;
	}
	table {
		border-top: 1px solid #333;
		border-left: 1px solid #333;
		border-spacing: 0;
		background-color: #fff;
		width: 100%;
		td {
			text-align: center;
			border-bottom: 1px solid #333;
			border-right: 1px solid #333;
			font-size: 13px;
			padding: 10px;
		}
		.tab-tltle {
			td {
				text-align: left;
				font-weight: 700;
				font-size: 15px;
			}
		}
		.tab-tltle {
			font-weight: 700;
			font-size: 15px;
		}
	}
}
</style>
