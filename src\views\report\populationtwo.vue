<template>
	<div>
		<div class="miantainer">
			<div class="top">
				<div class="lefttop">
					<div class="title">社区概况</div>
					<div class="gaikuang">
						<div class="basic" v-for="item in topleft" :key="item.text">
							<p class="topbas">{{ item.num }}</p>
							<p class="botbas">{{ item.text }}</p>
						</div>
					</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="../../assets/images/population/topone.png" fit="cover" />
					</div>
					<div class="toptex">{{ basic?.publicUtilities }}%</div>
					<div>公共事业</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="@/assets/images/population/toptwo.png" fit="cover" />
					</div>
					<div class="toptex">{{ basic?.blueCollar }}%</div>
					<div>蓝领</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="../../assets/images/population/topthr.png" />
					</div>
					<div class="toptex">{{ basic?.whiteCollar }}%</div>
					<div>白领</div>
				</div>
			</div>
			<div class="bottom">
				<div class="leftbottom">
					<div :style="{ width: '100%', height: '100%' }" id="zhutuone"></div>
					<div :style="{ width: '100%', height: '100%' }" id="zhututwo"></div>
					<div :style="{ width: '100%', height: '100%' }" id="zhututhr"></div>
				</div>
				<div class="center">
					<div :style="{ width: '100%', height: '100%' }" id="center"></div>
				</div>
				<div class="rightbottom">
					<div :style="{ width: '50%', height: '50%' }" id="bingone"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingtwo"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingthr"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingfou"></div>
				</div>
			</div>
		</div>
		<!-- <div class="container"></div> -->
		<div class="miantainer">
			<div class="top">
				<div class="lefttop">
					<div class="title">街道概况</div>
					<div class="gaikuang">
						<div class="basic" v-for="item in sectopleft" :key="item.text">
							<p class="topbas">{{ item.num }}</p>
							<p class="botbas">{{ item.text }}</p>
						</div>
					</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="../../assets/images/population/topone.png" fit="cover" />
					</div>
					<div class="toptex">{{ basicTwo?.publicUtilities }}%</div>
					<div>公共事业</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="@/assets/images/population/toptwo.png" fit="cover" />
					</div>
					<div class="toptex">{{ basicTwo?.blueCollar }}%</div>
					<div>蓝领</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="../../assets/images/population/topthr.png" />
					</div>
					<div class="toptex">{{ basicTwo?.whiteCollar }}%</div>
					<div>白领</div>
				</div>
			</div>
			<div class="bottom">
				<div class="leftbottom">
					<div :style="{ width: '100%', height: '100%' }" id="zhutuone1"></div>
					<div :style="{ width: '100%', height: '100%' }" id="zhututwo1"></div>
					<div :style="{ width: '100%', height: '100%' }" id="zhututhr1"></div>
				</div>
				<div class="center">
					<div :style="{ width: '100%', height: '100%' }" id="center1"></div>
				</div>
				<div class="rightbottom">
					<div :style="{ width: '50%', height: '50%' }" id="bingone1"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingtwo1"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingthr1"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingfou1"></div>
				</div>
			</div>
		</div>
		<!-- <div class="container"></div> -->
		<div class="miantainer" style="margin-bottom: 0px;">
			<div class="top">
				<div class="lefttop">
					<div class="title">周围其他街道概况</div>
					<div class="gaikuang">
						<div class="basic" v-for="item in thitopleft" :key="item.text">
							<p class="topbas">{{ item.num }}</p>
							<p class="botbas">{{ item.text }}</p>
						</div>
					</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="../../assets/images/population/topone.png" fit="cover" />
					</div>
					<div class="toptex">{{ basicThr?.publicUtilities }}%</div>
					<div>公共事业</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="@/assets/images/population/toptwo.png" fit="cover" />
					</div>
					<div class="toptex">{{ basicThr?.blueCollar }}%</div>
					<div>蓝领</div>
				</div>
				<div class="righttop">
					<div class="topimg">
						<img src="../../assets/images/population/topthr.png" />
					</div>
					<div class="toptex">{{ basicThr?.whiteCollar }}%</div>
					<div>白领</div>
				</div>
			</div>
			<div class="bottom">
				<div class="leftbottom">
					<div :style="{ width: '100%', height: '100%' }" id="zhutuone2"></div>
					<div :style="{ width: '100%', height: '100%' }" id="zhututwo2"></div>
					<div :style="{ width: '100%', height: '100%' }" id="zhututhr2"></div>
				</div>
				<div class="center">
					<div :style="{ width: '100%', height: '100%' }" id="center2"></div>
				</div>
				<div class="rightbottom">
					<div :style="{ width: '50%', height: '50%' }" id="bingone2"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingtwo2"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingthr2"></div>
					<div :style="{ width: '50%', height: '50%' }" id="bingfou2"></div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import * as echarts from 'echarts';
import { perponStreet } from '../../api/baogao';
import { useRoute } from 'vue-router';
const router = useRoute();
import { ref, onMounted, reactive, nextTick, computed } from 'vue';
import { formatter } from 'element-plus';

const basic = ref({});
const basicTwo = ref({});
const basicThr = ref({});

const zhutuone = ref(null);
const zhututwo = ref(null);
const zhututhr = ref(null);
const zhutucenter = ref(null);
const bingone = ref(null);
const bingtwo = ref(null);
const bingthr = ref(null);
const bingfou = ref(null);
const gongziChart = ref([]);
const fangwuChart = ref([]);
const jiatingChart = ref([]);
const leftChart = ref([]);
const rightChart = ref([]);
const bingoneChart = ref([]);
const bingtwoChart = ref([]);
const bingthrChart = ref([]);
const bingfouChart = ref([]);

const seczhutuone = ref(null);
const seczhututwo = ref(null);
const seczhututhr = ref(null);
const seczhutucenter = ref(null);
const secbingone = ref(null);
const secbingtwo = ref(null);
const secbingthr = ref(null);
const secbingfou = ref(null);
const secgongziChart = ref([]);
const secfangwuChart = ref([]);
const secjiatingChart = ref([]);
const secleftChart = ref([]);
const secrightChart = ref([]);
const secbingoneChart = ref([]);
const secbingtwoChart = ref([]);
const secbingthrChart = ref([]);
const secbingfouChart = ref([]);

const thizhutuone = ref(null);
const thizhututwo = ref(null);
const thizhututhr = ref(null);
const thizhutucenter = ref(null);
const thibingone = ref(null);
const thibingtwo = ref(null);
const thibingthr = ref(null);
const thibingfou = ref(null);
const thigongziChart = ref([]);
const thifangwuChart = ref([]);
const thijiatingChart = ref([]);
const thileftChart = ref([]);
const thirightChart = ref([]);
const thibingoneChart = ref([]);
const thibingtwoChart = ref([]);
const thibingthrChart = ref([]);
const thibingfouChart = ref([]);
const getdata = async () => {
	const res = await perponStreet({
		buildingId: router.query.id,
	});
	if (res.code === 200) {
		basic.value = res.result?.[0]?.populationBasic;
		basicTwo.value = res.result?.[1]?.populationBasic;
		basicThr.value = res.result?.[2]?.populationBasic;

		gongziChart.value = Object.values(res.result?.[0]?.mortgagePercentage);
		fangwuChart.value = Object.values(res.result?.[0]?.houseValuation);
		jiatingChart.value = Object.values(res.result?.[0]?.householdIncome);
		leftChart.value = Object.values(res.result?.[0]?.ageProfileLeft).map((value) => -value);
		rightChart.value = Object.values(res.result?.[0]?.ageProfileRight);
		bingoneChart.value = res.result?.[0]?.homeOwnershipStatusVo;
		bingtwoChart.value = res.result?.[0]?.buildingYear;
		bingthrChart.value = res.result?.[0]?.educationalLevel;
		bingfouChart.value = res.result?.[0]?.commutingTime;

		secgongziChart.value = Object.values(res.result?.[1]?.mortgagePercentage);
		secfangwuChart.value = Object.values(res.result?.[1]?.houseValuation);
		secjiatingChart.value = Object.values(res.result?.[1]?.householdIncome);
		secleftChart.value = Object.values(res.result?.[1]?.ageProfileLeft).map((value) => -value);
		secrightChart.value = Object.values(res.result?.[1]?.ageProfileRight);
		secbingoneChart.value = res.result?.[1]?.homeOwnershipStatusVo;
		secbingtwoChart.value = res.result?.[1]?.buildingYear;
		secbingthrChart.value = res.result?.[1]?.educationalLevel;
		secbingfouChart.value = res.result?.[1]?.commutingTime;

		thigongziChart.value = Object.values(res.result?.[2]?.mortgagePercentage);
		thifangwuChart.value = Object.values(res.result?.[2]?.houseValuation);
		thijiatingChart.value = Object.values(res.result?.[2]?.householdIncome);
		thileftChart.value = Object.values(res.result?.[2]?.ageProfileLeft).map((value) => -value);
		thirightChart.value = Object.values(res.result?.[2]?.ageProfileRight);
		thibingoneChart.value = res.result?.[2]?.homeOwnershipStatusVo;
		thibingtwoChart.value = res.result?.[2]?.buildingYear;
		thibingthrChart.value = res.result?.[2]?.educationalLevel;
		thibingfouChart.value = res.result?.[2]?.commutingTime;
		nextTick(() => {
			// 社区
			zhutuone.value = echarts.init(document.getElementById('zhutuone'));
			zhutuone.value.setOption({
				...option,
				series: [
					{
						data: gongziChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			zhututwo.value = echarts.init(document.getElementById('zhututwo'));

			zhututwo.value.setOption({
				title: {
					text: '房屋价值',
				},
				...optiontwo,
				series: [
					{
						data: fangwuChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			zhututhr.value = echarts.init(document.getElementById('zhututhr'));

			zhututhr.value.setOption({
				...optionthr,

				series: [
					{
						data: jiatingChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							color: '#ffffff',
							fontSize: 9,
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			zhutucenter.value = echarts.init(document.getElementById('center'));

			zhutucenter.value.setOption({
				...option1,

				series: [
					{
						name: '行政区',
						type: 'bar',
						stack: '总量',
						label: { show: true, formatter: (params) => params.value + '%', position: 'insideRight' },
						emphasis: { focus: 'series' },
						itemStyle: {
							color: '#4c668a', // 左边（其他街道）的颜色
						},
						data: leftChart.value,
					},
					{
						name: '社区',
						type: 'bar',
						stack: '总量',
						label: { show: true, formatter: (params) => params.value + '%', position: 'insideLeft' },
						emphasis: { focus: 'series' },
						data: rightChart.value,
					},
				],
			});
			// 同理，设置其他图表的选项

			bingone.value = echarts.init(document.getElementById('bingone'));
			bingone.value.setOption({
				title: {
					text: '居者有其屋',
					left: 'center',
					top: 20,
					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize: 16,
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: { show: true, position: 'inside',formatter:(params)=>params.value + '%',color: '#ffffff', }, // 隐藏标签
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: bingoneChart.value?.owner, name: '主人', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: bingoneChart.value?.lessor, name: '出租人', itemStyle: { color: '#89c2d9' } },
							{ value: bingoneChart.value?.vacant, name: '空缺的', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});

			bingtwo.value = echarts.init(document.getElementById('bingtwo'));
			bingtwo.value.setOption({
				title: {
					text: '住房：建造年份（省级）',
					left: 'center',
					top: 20,

					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize:16
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						fontSize: 6, // 设置字体大小
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							textStyle: {
								color: '#ffffff', // 字体颜色
							},
							formatter: (params) => params.value + '%'
						}, // 隐藏标签
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: bingtwoChart.value?.before1949, name: '<1949', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: bingtwoChart.value?.between19491959, name: '1949-59', itemStyle: { color: '#89c2d9' } },
							{ value: bingtwoChart.value?.between19601969, name: '1960-69', itemStyle: { color: '#d8e3e7' } },
							{ value: bingtwoChart.value?.between19701979, name: '1970-79', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: bingtwoChart.value?.between19801989, name: '1980-89', itemStyle: { color: '#89c2d9' } },
							{ value: bingtwoChart.value?.between19901999, name: '1990-99', itemStyle: { color: '#d8e3e7' } },
							{ value: bingtwoChart.value?.between20002009, name: '2000-09', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: bingtwoChart.value?.between20102014, name: '2010-14', itemStyle: { color: '#89c2d9' } },
							{ value: bingtwoChart.value?.after2015, name: '2015+', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});
			bingthr.value = echarts.init(document.getElementById('bingthr'));
			bingthr.value.setOption({
				title: {
					text: '教育程度',
					left: 'center',
					top: 20,

					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize:16
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							formatter: (params) => params.value + '%',
							textStyle: {
								color: '#ffffff', // 字体颜色
							},
						}, // 隐藏标签
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: bingthrChart.value?.noHighSchoolDiploma, name: '无高中文凭', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: bingthrChart.value?.highSchoolDiploma, name: '高中毕业', itemStyle: { color: '#89c2d9' } },
							{ value: bingthrChart.value?.collegeDiploma, name: '大专学历', itemStyle: { color: '#d8e3e7' } },
							{ value: bingthrChart.value?.bachelorMasterDoctorate, name: '学士、硕士、博士', itemStyle: { color: '#289ba9' } }, // 环形颜色
						],
					},
				],
			});
			bingfou.value = echarts.init(document.getElementById('bingfou'));
			bingfou.value.setOption({
				title: {
					text: '通勤时间：分钟',
					left: 'center',
					top: 20,
					textStyle: {
						color: '#ffffff',
						fontSize: 16,
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0, // 提高图例的底部距离
					textStyle: {
						color: '#ffffff',
						fontSize: 6,
					},
				},
				series: [
					{
						type: 'pie',
						radius: ['30%', '60%'],
						center: ['50%', '40%'], // 调整图表的中心位置
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							formatter: (params) => params.value + '%',
							textStyle: {
								color: '#ffffff', // 字体颜色
							},
						},
						labelLine: { show: false },
						data: [
							{ value: bingfouChart.value?.commuteTimeLt5, name: '<5', itemStyle: { color: '#4c9ca0' } },
							{ value: bingfouChart.value?.commuteTimeBetween59, name: '5-9', itemStyle: { color: '#89c2d9' } },
							{ value: bingfouChart.value?.commuteTimeBetween1014, name: '10-14', itemStyle: { color: '#d8e3e7' } },
							{ value: bingfouChart.value?.commuteTimeBetween1519, name: '15-19', itemStyle: { color: '#4c9ca0' } },
							{ value: bingfouChart.value?.commuteTimeBetween2024, name: '20-24', itemStyle: { color: '#89c2d9' } },
							{ value: bingfouChart.value?.commuteTimeBetween2529, name: '25-29', itemStyle: { color: '#d8e3e7' } },
							{ value: bingfouChart.value?.commuteTimeBetween3034, name: '30-34', itemStyle: { color: '#4c9ca0' } },
							{ value: bingfouChart.value?.commuteTimeBetween3539, name: '35-39', itemStyle: { color: '#89c2d9' } },
							{ value: bingfouChart.value?.commuteTimeBetween4044, name: '40-44', itemStyle: { color: '#d8e3e7' } },
							{ value: bingfouChart.value?.commuteTimeBetween4559, name: '45-59', itemStyle: { color: '#4c9ca0' } },
							{ value: bingfouChart.value?.commuteTimeBetween6089, name: '60-89', itemStyle: { color: '#89c2d9' } },
							{ value: bingfouChart.value?.commuteTimeGt90, name: '90+', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});
			//街道
			seczhutuone.value = echarts.init(document.getElementById('zhutuone1'));
			seczhutuone.value.setOption({
				...option,
				series: [
					{
						data: secgongziChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			seczhututwo.value = echarts.init(document.getElementById('zhututwo1'));

			seczhututwo.value.setOption({
				title: {
					text: '房屋价值',
				},
				...optiontwo,
				series: [
					{
						data: secfangwuChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			seczhututhr.value = echarts.init(document.getElementById('zhututhr1'));

			seczhututhr.value.setOption({
				...optionthr,

				series: [
					{
						data: secjiatingChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			seczhutucenter.value = echarts.init(document.getElementById('center1'));

			seczhutucenter.value.setOption({
				...option1,

				series: [
					{
						name: '行政区',
						type: 'bar',
						stack: '总量',
						label: { show: true, formatter: (params) => params.value + '%', position: 'insideRight' },
						emphasis: { focus: 'series' },
						itemStyle: {
							color: '#4c668a', // 左边（其他街道）的颜色
						},
						data: secleftChart.value,
					}, 
					{
						name: '街道',
						type: 'bar',
						stack: '总量',
						label: { show: true, formatter: (params) => params.value + '%', position: 'insideLeft' },
						emphasis: { focus: 'series' },
						data: secrightChart.value,
					},
				],
			});
			// 同理，设置其他图表的选项

			secbingone.value = echarts.init(document.getElementById('bingone1'));
			secbingone.value.setOption({
				title: {
					text: '居者有其屋',
					left: 'center',
					top: 20,
					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize:16
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						},
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: secbingoneChart.value?.owner, name: '主人', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: secbingoneChart.value?.lessor, name: '出租人', itemStyle: { color: '#89c2d9' } },
							{ value: secbingoneChart.value?.vacant, name: '空缺的', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});

			secbingtwo.value = echarts.init(document.getElementById('bingtwo1'));
			secbingtwo.value.setOption({
				title: {
					text: '住房：建造年份（省级）',
					left: 'center',
					top: 20,

					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize:16
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						fontSize: 6, // 设置字体大小
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						},
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: secbingtwoChart.value?.before1949, name: '<1949', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: secbingtwoChart.value?.between19491959, name: '1949-59', itemStyle: { color: '#89c2d9' } },
							{ value: secbingtwoChart.value?.between19601969, name: '1960-69', itemStyle: { color: '#d8e3e7' } },
							{ value: secbingtwoChart.value?.between19701979, name: '1970-79', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: secbingtwoChart.value?.between19801989, name: '1980-89', itemStyle: { color: '#89c2d9' } },
							{ value: secbingtwoChart.value?.between19901999, name: '1990-99', itemStyle: { color: '#d8e3e7' } },
							{ value: secbingtwoChart.value?.between20002009, name: '2000-09', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: secbingtwoChart.value?.between20102014, name: '2010-14', itemStyle: { color: '#89c2d9' } },
							{ value: secbingtwoChart.value?.after2015, name: '2015+', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});
			secbingthr.value = echarts.init(document.getElementById('bingthr1'));
			secbingthr.value.setOption({
				title: {
					text: '教育程度',
					left: 'center',
					top: 20,

					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize:16
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						},
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: secbingthrChart.value?.noHighSchoolDiploma, name: '无高中文凭', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: secbingthrChart.value?.highSchoolDiploma, name: '高中毕业', itemStyle: { color: '#89c2d9' } },
							{ value: secbingthrChart.value?.collegeDiploma, name: '大专学历', itemStyle: { color: '#d8e3e7' } },
							{ value: secbingthrChart.value?.bachelorMasterDoctorate, name: '学士、硕士、博士', itemStyle: { color: '#289ba9' } }, // 环形颜色
						],
					},
				],
			});
			secbingfou.value = echarts.init(document.getElementById('bingfou1'));
			secbingfou.value.setOption({
				title: {
					text: '通勤时间：分钟',
					left: 'center',
					top: 20,
					textStyle: {
						color: '#ffffff',
						fontSize: 16,
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0, // 提高图例的底部距离
					textStyle: {
						color: '#ffffff',
						fontSize: 6,
					},
				},
				series: [
					{
						type: 'pie',
						radius: ['30%', '60%'],
						center: ['50%', '40%'], // 调整图表的中心位置
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						},
						labelLine: { show: false },
						data: [
							{ value: secbingfouChart.value?.commuteTimeLt5, name: '<5', itemStyle: { color: '#4c9ca0' } },
							{ value: secbingfouChart.value?.commuteTimeBetween59, name: '5-9', itemStyle: { color: '#89c2d9' } },
							{ value: secbingfouChart.value?.commuteTimeBetween1014, name: '10-14', itemStyle: { color: '#d8e3e7' } },
							{ value: secbingfouChart.value?.commuteTimeBetween1519, name: '15-19', itemStyle: { color: '#4c9ca0' } },
							{ value: secbingfouChart.value?.commuteTimeBetween2024, name: '20-24', itemStyle: { color: '#89c2d9' } },
							{ value: secbingfouChart.value?.commuteTimeBetween2529, name: '25-29', itemStyle: { color: '#d8e3e7' } },
							{ value: secbingfouChart.value?.commuteTimeBetween3034, name: '30-34', itemStyle: { color: '#4c9ca0' } },
							{ value: secbingfouChart.value?.commuteTimeBetween3539, name: '35-39', itemStyle: { color: '#89c2d9' } },
							{ value: secbingfouChart.value?.commuteTimeBetween4044, name: '40-44', itemStyle: { color: '#d8e3e7' } },
							{ value: secbingfouChart.value?.commuteTimeBetween4559, name: '45-59', itemStyle: { color: '#4c9ca0' } },
							{ value: secbingfouChart.value?.commuteTimeBetween6089, name: '60-89', itemStyle: { color: '#89c2d9' } },
							{ value: secbingfouChart.value?.commuteTimeGt90, name: '90+', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});
			//周边街道
			thizhutuone.value = echarts.init(document.getElementById('zhutuone2'));
			thizhutuone.value.setOption({
				...option,
				series: [
					{
						data: thigongziChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			thizhututwo.value = echarts.init(document.getElementById('zhututwo2'));

			thizhututwo.value.setOption({
				title: {
					text: '房屋价值',
				},
				...optiontwo,
				series: [
					{
						data: thifangwuChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			thizhututhr.value = echarts.init(document.getElementById('zhututhr2'));

			thizhututhr.value.setOption({
				...optionthr,

				series: [
					{
						data: thijiatingChart.value, // 这里传入更新后的数据
						type: 'bar',
						label: {
							show: true,
							position: 'top',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						}
					},
				],
			});
			thizhutucenter.value = echarts.init(document.getElementById('center2'));

			thizhutucenter.value.setOption({
				...option1,

				series: [
					{
						name: '行政区',
						type: 'bar',
						stack: '总量',
						label: { show: true, formatter: (params) => params.value + '%', position: 'insideRight' },
						emphasis: { focus: 'series' },
						itemStyle: {
							color: '#4c668a', // 左边（其他街道）的颜色
						},
						data: thileftChart.value,
					},
					{
						name: '其他街道',
						type: 'bar',
						stack: '总量',
						label: { show: true, formatter: (params) => params.value + '%', position: 'insideLeft' },
						emphasis: { focus: 'series' },
						data: thirightChart.value,
					},
				],
			});
			// 同理，设置其他图表的选项

			thibingone.value = echarts.init(document.getElementById('bingone2'));
			thibingone.value.setOption({
				title: {
					text: '居者有其屋',
					left: 'center',
					top: 20,

					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize:16
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						},
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: thibingoneChart.value?.owner, name: '主人', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: thibingoneChart.value?.lessor, name: '出租人', itemStyle: { color: '#89c2d9' } },
							{ value: thibingoneChart.value?.vacant, name: '空缺的', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});

			thibingtwo.value = echarts.init(document.getElementById('bingtwo2'));
			thibingtwo.value.setOption({
				title: {
					text: '住房：建造年份（省级）',
					left: 'center',
					top: 20,
					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize:16
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						fontSize: 6, // 设置字体大小
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						},
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: thibingtwoChart.value?.before1949, name: '<1949', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: thibingtwoChart.value?.between19491959, name: '1949-59', itemStyle: { color: '#89c2d9' } },
							{ value: thibingtwoChart.value?.between19601969, name: '1960-69', itemStyle: { color: '#d8e3e7' } },
							{ value: thibingtwoChart.value?.between19701979, name: '1970-79', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: thibingtwoChart.value?.between19801989, name: '1980-89', itemStyle: { color: '#89c2d9' } },
							{ value: thibingtwoChart.value?.between19901999, name: '1990-99', itemStyle: { color: '#d8e3e7' } },
							{ value: thibingtwoChart.value?.between20002009, name: '2000-09', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: thibingtwoChart.value?.between20102014, name: '2010-14', itemStyle: { color: '#89c2d9' } },
							{ value: thibingtwoChart.value?.after2015, name: '2015+', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});
			thibingthr.value = echarts.init(document.getElementById('bingthr2'));
			thibingthr.value.setOption({
				title: {
					text: '教育程度',
					left: 'center',
					top: 20,

					textStyle: {
						color: '#ffffff', // 标题文字颜色为白色
						fontSize:16
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0,
					textStyle: {
						color: '#ffffff', // 图例文字颜色为白色
					},
				},
				series: [
					{
						type: 'pie', // 指定为饼图
						radius: ['30%', '60%'], // 内外半径，控制环形的大小
						center: ['50%', '40%'],
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						},
						labelLine: { show: false }, // 隐藏引导线
						data: [
							{ value: thibingthrChart.value?.noHighSchoolDiploma, name: '无高中文凭', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
							{ value: thibingthrChart.value?.highSchoolDiploma, name: '高中毕业', itemStyle: { color: '#89c2d9' } },
							{ value: thibingthrChart.value?.collegeDiploma, name: '大专学历', itemStyle: { color: '#d8e3e7' } },
							{ value: thibingthrChart.value?.bachelorMasterDoctorate, name: '学士、硕士、博士', itemStyle: { color: '#289ba9' } }, // 环形颜色
						],
					},
				],
			});
			thibingfou.value = echarts.init(document.getElementById('bingfou2'));
			thibingfou.value.setOption({
				title: {
					text: '通勤时间：分钟',
					left: 'center',
					top: 20,
					textStyle: {
						color: '#ffffff',
						fontSize: 16,
					},
				},
				tooltip: {
					trigger: 'item',
				},
				legend: {
					orient: 'horizontal',
					bottom: 0, // 提高图例的底部距离
					textStyle: {
						color: '#ffffff',
						fontSize: 6,
					},
				},
				series: [
					{
						type: 'pie',
						radius: ['30%', '60%'],
						center: ['50%', '40%'], // 调整图表的中心位置
						avoidLabelOverlap: false,
						label: {
							show: true,
							position: 'inside',
							fontSize: 9,
							color: '#ffffff',
							formatter: (params)=> params.value + '%'
						},
						labelLine: { show: false },
						data: [
							{ value: thibingfouChart.value?.commuteTimeLt5, name: '<5', itemStyle: { color: '#4c9ca0' } },
							{ value: thibingfouChart.value?.commuteTimeBetween59, name: '5-9', itemStyle: { color: '#89c2d9' } },
							{ value: thibingfouChart.value?.commuteTimeBetween1014, name: '10-14', itemStyle: { color: '#d8e3e7' } },
							{ value: thibingfouChart.value?.commuteTimeBetween1519, name: '15-19', itemStyle: { color: '#4c9ca0' } },
							{ value: thibingfouChart.value?.commuteTimeBetween2024, name: '20-24', itemStyle: { color: '#89c2d9' } },
							{ value: thibingfouChart.value?.commuteTimeBetween2529, name: '25-29', itemStyle: { color: '#d8e3e7' } },
							{ value: thibingfouChart.value?.commuteTimeBetween3034, name: '30-34', itemStyle: { color: '#4c9ca0' } },
							{ value: thibingfouChart.value?.commuteTimeBetween3539, name: '35-39', itemStyle: { color: '#89c2d9' } },
							{ value: thibingfouChart.value?.commuteTimeBetween4044, name: '40-44', itemStyle: { color: '#d8e3e7' } },
							{ value: thibingfouChart.value?.commuteTimeBetween4559, name: '45-59', itemStyle: { color: '#4c9ca0' } },
							{ value: thibingfouChart.value?.commuteTimeBetween6089, name: '60-89', itemStyle: { color: '#89c2d9' } },
							{ value: thibingfouChart.value?.commuteTimeGt90, name: '90+', itemStyle: { color: '#d8e3e7' } },
						],
					},
				],
			});
		});
		// alert(gongziChart.value);
	}
};

// ECharts 配置选项（可以根据你的需求修改）
const option = {
	title: {
		text: '抵押贷款占工资的百分比',
		left: 'center',
		textStyle: {
			color: '#ffffff', // 标题文字颜色为白色
		},
	},
	grid: {
		top: '20%', // 从上方留出10%的空间
		bottom: '10%', // 从下方留出10%的空间
		left: '3%',
		right: '4%',
		containLabel: true,
	},
	xAxis: {
		type: 'category',
		data: ['<10', '10-14', '15-19', '20-24', '25-29', '30-34', '35-39', '40-49', '50+'],
		axisLabel: {
			color: '#ffffff', // x轴文字颜色为白色
			fontSize: 8, // 设置字体大小为 10px
		},
	},
	yAxis: {
		type: 'value',
		axisLabel: {
			color: '#ffffff', // y轴文字颜色为白色
			formatter: function (value) {
				return value + '%'; // 将数值显示为百分比
			},
		},
	},
	series: [
		{
			data: [2, 1, 1, 1, 1, 1, 1, 1, 11, 1], // 数据为百分比形式
			type: 'bar',
			label: {
					show: true,
					position: 'top',
					color: 'red'
			}
		},
	],
};
const optiontwo = {
	title: {
		text: '房屋价值',
		left: 'center',
		textStyle: {
			color: '#ffffff', // 标题文字颜色为白色
		},
	},
	grid: {
		top: '20%', // 从上方留出10%的空间
		bottom: '10%', // 从下方留出10%的空间
		left: '3%',
		right: '4%',
		containLabel: true,
	},
	xAxis: {
		type: 'category',
		data: ['<$50,000', '$100,000', '$150,000', '$200,000', '$250,000', '$300,000', '$400,000', '$550,000', '$750,000', '$800,000', '$1,000,000'],
		axisLabel: {
			color: '#ffffff', // x轴文字颜色为白色
			fontSize: 6, // 设置字体大小为 10px
			rotate: 45,
		},
	},
	yAxis: {
		type: 'value',
		axisLabel: {
			color: '#ffffff', // y轴文字颜色为白色
			formatter: function (value) {
				return value + '%'; // 将数值显示为百分比
			},
		},
	},
	series: [
		{
			data: [2, 1, 1, 1, 1, 1, 1, 1, 11, 1], // 数据为百分比形式
			type: 'bar',
		},
	],
};
const optionthr = {
	title: {
		text: '家庭收入',
		left: 'center',
		textStyle: {
			color: '#ffffff', // 标题文字颜色为白色
		},
	},
	grid: {
		top: '20%', // 从上方留出10%的空间
		bottom: '10%', // 从下方留出10%的空间
		left: '3%',
		right: '4%',
		containLabel: true,
	},
	xAxis: {
		type: 'category',
		data: ['<$15,000', '$25,000', '$35,000', '$65,000', '$75,000', '$110,000', '$150,000', '$175,000', '$200,000+'],
		axisLabel: {
			color: '#ffffff', // x轴文字颜色为白色
			fontSize: 8, // 设置字体大小为 10px
			rotate: 45,
		},
	},
	yAxis: {
		type: 'value',
		axisLabel: {
			color: '#ffffff', // y轴文字颜色为白色
			formatter: function (value) {
				return value + '%'; // 将数值显示为百分比
			},
		},
	},
	series: [
		{
			data: [2, 1, 1, 1, 1, 1, 1, 1, 11, 1], // 数据为百分比形式
			type: 'bar',
		},
	],
};
const option1 = {
	title: {
		text: '年龄简介：5年递增',
		left: 'center',
		textStyle: {
			color: '#ffffff', // 标题文字颜色为白色
		},
	},
	tooltip: {
		trigger: 'axis',
		axisPointer: { type: 'shadow' },
	},
	legend: {
		bottom: -5,
		textStyle: {
			color: '#ffffff', // 图例文字颜色为白色
		},
	},
	grid: {
		left: '3%',
		right: '4%',
		bottom: '4%',
		containLabel: true,
	},
	xAxis: {
		type: 'value',
		axisLabel: {
			color: '#ffffff', // x轴文字颜色为白色
			formatter: function (value) {
				// .toFixed(2)
				return value + '%'; // 格式化成小数点后两位
			},
		},
		min: -12, // 最小值
		max: 12,
		interval: 4,
		splitLine: { show: false },
	},
	yAxis: {
		type: 'category',
		axisLabel: {
			color: '#ffffff', // y轴文字颜色为白色
		},
		data: ['0-4', '5-9', '10-14', '15-19', '20-24', '25-29', '30-34', '35-39', '40-44', '45-49', '50-54', '55-59', '60-64', '65-69', '70-74', '75-79', '80-84', '85+'],
	},
	series: [
		{
			name: '其他街道',
			type: 'bar',
			stack: '总量',
			label: { show: false },
			emphasis: { focus: 'series' },
			itemStyle: {
				color: '#4c668a', // 左边（其他街道）的颜色
			},
			data: [0.12, -0.08, -0.05, -0.09, -0.08, -0.06, -0.04, -0.03, -0.05, -0.06, -0.04, -0.03, -0.02, -0.01, -0.02],
		},
		{
			name: '黄岛区',
			type: 'bar',
			stack: '总量',
			label: { show: false },
			emphasis: { focus: 'series' },
			data: [0.12, 0.08, 0.05, 0.09, 0.08, 0.06, 0.04, 0.03, 0.05, 0.06, 0.04, 0.03, 0.02, 0.01, 0.02],
		},
	],
};

const option2 = {
	title: {
		text: '111',
		left: 'center',
		top: 20,

		textStyle: {
			color: '#ffffff', // 标题文字颜色为白色
		},
	},
	tooltip: {
		trigger: 'item',
	},
	legend: {
		orient: 'horizontal',
		bottom: 0,
		textStyle: {
			color: '#ffffff', // 图例文字颜色为白色
		},
		data: ['主人', '出租人', '空缺的'],
	},
	series: [
		{
			type: 'pie',
			radius: ['50%', '70%'], // 内外半径，控制环形的大小
			avoidLabelOverlap: false,
			label: { show: false, position: 'center' }, // 隐藏标签
			labelLine: { show: false }, // 隐藏引导线
			data: [
				{ value: 40, name: '主人', itemStyle: { color: '#4c9ca0' } }, // 环形颜色
				{ value: 30, name: '出租人', itemStyle: { color: '#89c2d9' } },
				{ value: 30, name: '空缺的', itemStyle: { color: '#d8e3e7' } },
			],
		},
	],
};

onMounted(() => {
	getdata();
});

const topleft = computed(() => [
	{ num: basic.value?.populationTotal || 'N/A', text: '人口总数' },
	{ num: basic.value?.populationGrowth || 'N/A', text: '人口增长' },
	{ num: basic.value?.avgHhSize || 'N/A', text: '平均HH大小' },
	{ num: basic.value?.diversityIndex || 'N/A', text: '多样性指数' },
	{ num: basic.value?.ageMid || 'N/A', text: '中位年龄' },
	{ num: basic.value?.householdIncomeMid || 'N/A', text: '家庭收入中位数' },
	{ num: basic.value?.houseMidValue || 'N/A', text: '房屋中值' },
	{ num: basic.value?.medianNetValue || 'N/A', text: '中值净值' },
	{ num: basic.value?.before18 || 'N/A', text: '18岁以下' },
	{ num: basic.value?.between1865 || 'N/A', text: '18-65岁' },
	{ num: basic.value?.after66 || 'N/A', text: '66岁+' },
]);
const sectopleft = computed(() => [
	{ num: basicTwo.value?.populationTotal || 'N/A', text: '人口总数' },
	{ num: basicTwo.value?.populationGrowth || 'N/A', text: '人口增长' },
	{ num: basicTwo.value?.avgHhSize || 'N/A', text: '平均HH大小' },
	{ num: basicTwo.value?.diversityIndex || 'N/A', text: '多样性指数' },
	{ num: basicTwo.value?.ageMid || 'N/A', text: '中位年龄' },
	{ num: basicTwo.value?.householdIncomeMid || 'N/A', text: '家庭收入中位数' },
	{ num: basicTwo.value?.houseMidValue || 'N/A', text: '房屋中值' },
	{ num: basicTwo.value?.medianNetValue || 'N/A', text: '中值净值' },
	{ num: basicTwo.value?.before18 || 'N/A', text: '18岁以下' },
	{ num: basicTwo.value?.between1865 || 'N/A', text: '18-65岁' },
	{ num: basicTwo.value?.after66 || 'N/A', text: '66岁+' },
]);
const thitopleft = computed(() => [
	{ num: basicThr.value?.populationTotal || 'N/A', text: '人口总数' },
	{ num: basicThr.value?.populationGrowth || 'N/A', text: '人口增长' },
	{ num: basicThr.value?.avgHhSize || 'N/A', text: '平均HH大小' },
	{ num: basicThr.value?.diversityIndex || 'N/A', text: '多样性指数' },
	{ num: basicThr.value?.ageMid || 'N/A', text: '中位年龄' },
	{ num: basicThr.value?.householdIncomeMid || 'N/A', text: '家庭收入中位数' },
	{ num: basicThr.value?.houseMidValue || 'N/A', text: '房屋中值' },
	{ num: basicThr.value?.medianNetValue || 'N/A', text: '中值净值' },
	{ num: basicThr.value?.before18 || 'N/A', text: '18岁以下' },
	{ num: basicThr.value?.between1865 || 'N/A', text: '18-65岁' },
	{ num: basicThr.value?.after66 || 'N/A', text: '66岁+' },
]);
</script>

<style lang="less" scoped>
.bottom {
	width: 100%;
	display: flex;
	.leftbottom {
		height: 185px;
		width: 33%;
	}
	.center {
		height: 555px;
		width: 30%;
	}
	.rightbottom {
		display: flex;
		flex-wrap: wrap;
		width: 37%;
		height: 555px;
	}
}

.gaikuang {
	display: flex;
	align-items: center;
}
.top {
	display: flex;
	justify-content: space-between;
}
.miantainer {
	// height: 650px;
	// width: 1150px;
	background-color: #2c437a;
	color: #ffffff;
	width: 1046px;
	height: 717px;
	padding: 10px;
	margin-bottom: 100px;
	box-sizing: border-box;
	.lefttop {
		.title {
			font-size: 48px;
		}
		.basic {
			text-align: center;
			padding-right: 20px;
			.topbas {
				font-size: 15px;
			}
			.botbas {
				font-size: 12px;
			}
		}
	}
	.righttop {
		text-align: center;
		font-size: 14px;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		margin-bottom: 12px;
		img {
			width: 60px;
			height: 60px;
		}
	}
}
.container {
	padding: 3px;
	font-family: serif;
	color: #ffffff;
	margin-bottom: 200px;
}
</style>
