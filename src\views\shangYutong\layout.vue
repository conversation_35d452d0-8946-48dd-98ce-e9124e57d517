<template>
	<div class="shangYutong_main">
		<div class="left_menus">
			<arco-menu :selected-keys="[data.activeMenu]" @menu-item-click="handleSelect" :auto-open="data.defaultlist" class="el_menu_custom">
				<menu-Item v-for="item in data.menus" :key="item.index" :item="item" :active-menu="data.activeMenu"></menu-Item>
			</arco-menu>
		</div>
		<div class="contionar_box" ref="contionar_box">
			<router-view v-slot="{ Component, route }">
				<keep-alive>
					<component :is="Component" :key="route.meta.usePathKey ? route.path : undefined" />
				</keep-alive>
			</router-view>
		</div>

		<el-dialog
			v-model="dialogVisible"
			append-to-body
			:width="524"
			:show-close="false"
			:close-on-click-modal="false"
			align-center
			class="trial_benefits_dialog"
		>
			<div class="container_dialog_content">
				<div class="container_dialog_content_top">
					<img src="@/assets/images/shangYutong/menu/newusermaskPack.png" alt="" />
				</div>
				<div class="container_dialog_content_bottom">
					<img src="@/assets/images/shangYutong/menu/daysFree.png" alt="" />
					<!-- 送您商宇通
					<div class="container_dialog_content_bottom_title">7天免费</div>
					试用权益 -->
				</div>
				<div class="container_dialog_content_title">
					<div class="container_dialog_content_s">
						<div class="context_wrap" v-for="item in data.context_wrap" :key="item.index">
							<div class="context_wrap_img">
								<img src="@/assets/images/shangYutong/menu/regardinghook.png" alt="" />
							</div>
							<div class="context_wrap_title">{{ item.title }}</div>
						</div>
					</div>
					<!-- <img src="@/assets/images/shangYutong/menu/andInterests.png" alt="" /> -->
				</div>

				<div class="container_dialog_content_bottom_button">
					<div class="container_dialog_content_bottom_button_button" @click="handleSendCoupon">立即试用</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup>
import { vuexStore } from '../../store';
import menuItem from './menuItem.vue';
import marketStatistics from '@/assets/images/shangYutong/menu/marketStatistics.png';
import marketStatisticsActive from '@/assets/images/shangYutong/menu/marketStatisticsAct.png';
import tradingmaterials from '@/assets/images/shangYutong/menu/tradingmaterials.png';
import tradingmaterialsActive from '@/assets/images/shangYutong/menu/tradingmaterialsAct.png';
import property from '@/assets/images/shangYutong/menu/property.png';
import propertyAct from '@/assets/images/shangYutong/menu/propertyAct.png';
import businessDaily from '@/assets/images/shangYutong/menu/businessDaily.png';
import businessDailyAct from '@/assets/images/shangYutong/menu/businessDailyAct.png';
import xiaoGe from '@/assets/images/shangYutong/menu/xiaoGe.png';
import xiaoGeAct from '@/assets/images/shangYutong/menu/xiaoGeAct.png';

import { ElMessage } from 'element-plus';
import { reactive, onMounted, watch, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getUserExtension, sendCoupon } from '@/api/layout.js';
const dialogVisible = ref(false);
const route = useRoute();
const router = useRouter();
const element = ref(); // 底部元素
const contionar_box = ref();
const data = reactive({
	context_wrap: [
		{
			title: '市场统计',
		},
		{
			title: '交易材料',
		},
		{
			title: '信用风险',
		},
	],
	activeMenu: '',
	defaultlist: ['/shangYutong', '/shangYutong/materials', '/shangYutong/financial'],
	menus: [
		{
			name: '市场统计',
			path: '/shangYutong/statistics',
			icon: marketStatistics,
			icon_active: marketStatisticsActive,
		},
		{
			name: '交易材料',
			path: '/shangYutong/materials',
			icon: tradingmaterials,
			icon_active: tradingmaterialsActive,
			children: [
				{
					name: '楼宇信息',
					path: '/shangYutong/materials/assetComparison',
				},

				{
					name: '商圈分析',
					path: '/shangYutong/materials/businessAnalysis',
				},
				{
					name: '城市数据',
					path: '/shangYutong/materials/cityData',
				},
				{
					name: '产权人/管理人',
					path: '/shangYutong/materials/participant',
				},
				{
					name: '零售专区',
					path: '/shangYutong/materials/demographic',
				},
				// {
				// 	name: '交易计算',
				// 	path: '/shangYutong/materials/calculate',
				// },
			],
		},

		{
			name: '地产金融',
			path: '/shangYutong/financial',
			icon: property,
			icon_active: propertyAct,
			children: [
				{
					name: 'REITs/ABS',
					path: '/shangYutong/financial/estateFinance',
				},
				{
					name: '风险测评',
					path: '/shangYutong/financial/risks',
				},
				// {
				// 	name: '商报auto',
				// 	path: '/shangYutong/financial/shangAuto',
				// },
				// {
				// 	name: '小葛AI',
				// 	path: 'https://smai.biaobiaozhun.com/chat/ZMSVkr7vL1ezsC9B',
				// },
			],
		},
		{
			name: '商报auto',
			path: '/shangYutong/shangAuto',
			icon: businessDaily,
			icon_active: businessDailyAct,
		},
		{
			name: '小葛AI',
			icon: xiaoGe,
			icon_active: xiaoGeAct,
			path: 'https://smai.biaobiaozhun.com/chat/ZMSVkr7vL1ezsC9B',
		},
	],
});
onMounted(() => {
	handleFooterBox(); //隐藏底部
	vuexStore.dispatch('handleDistrict'); // 获取区域
	handleSelect(route.path || data.activeMenu);
	window.scrollTo(0, 0);
	// 登录后执行方法
	setTimeout(() => {
		if (vuexStore.state.userInfo?.userName) {
			handleUserExtension();
		}
	}, 500);
});

//获取用户扩展信息
function handleUserExtension() {
	getUserExtension().then((res) => {
		if (res.code == 200) {
			if (res.data.sendSytNewUserCoupon === false) {
				dialogVisible.value = true;
			}
		}
	});
}

// 商宇通领取新人券
function handleSendCoupon() {
	sendCoupon().then((res) => {
		if (res.code == 200) {
			//提示
			ElMessage.success('领取成功');
			dialogVisible.value = false;
		}
	});
}

onBeforeUnmount(() => {
	if (element.value?.style) {
		element.value.style.display = '';
	}
});

watch(
	() => route.path,
	(to, from) => {
		vuexStore.dispatch('handleDistrict'); // 获取区域
		handleFooterBox(); //隐藏底部
		// 在路由变化时执行的操作
		handleSelect(route.path || to, route.query);
	}
);

//隐藏底部
function handleFooterBox() {
	setTimeout(() => {
		element.value = document.querySelector('.footer_box');
		if (element.value?.style) {
			element.value.style.display = 'none';
		}
	}, 100);
}

/**
 * @function handleSelect 点击菜单路由跳转
 * @param path 路径
 */
function handleSelect(path, query) {
	console.log(path, 'path');
	// 路由跳转
	// 判断路径是否为外部链接
	if (path.startsWith('https://')) {
		window.open(path);
		return;
	}
	// 路由跳转时,滚动到顶部
	router.push({ path: path, query: query }).then(() => {
		contionar_box.value.scrollTo({ top: 0 });
	});
	nextTick(() => {
		data.activeMenu = path;
	});
}
</script>

<style lang="less" scoped>
.el_menu_custom {
	::v-deep .arco-menu-selected {
		background-color: #e8f3ff; /* 自定义背景色 */
		color: #165dff; /* 自定义文字颜色 */
	}
	::v-deep .arco-menu-inline-header {
		background: #fff;
	}
}
.shangYutong_main {
	width: 100%;
	height: 100%;
	margin: 0 auto;
	box-sizing: border-box;
	position: relative;
	overflow: hidden;
	background-color: #f7f8fa;

	.left_menus {
		width: 240px;
		transition: all 0.3s;
		height: 100%;
		background-color: #fff;
		position: absolute;
	}

	.contionar_box {
		width: calc(100% - 256px);
		box-sizing: border-box;
		margin-left: 256px;
		height: calc(100vh - 56px);
		overflow: scroll;
		background-color: #f7f8fa;
	}
}

.container_dialog_content {
	background: url('@/assets/images/shangYutong/menu/newuserMaskgroup.png') no-repeat center center;
	background-size: 100% 100%;
	width: 524px;
	height: 348px;
	.container_dialog_content_top {
		width: 100%;
		height: 150px;
		display: flex;
		justify-content: center;
		align-items: center;
		img {
			margin-top: -102px;
			width: 391px;
			height: 250px;
		}
	}
	.container_dialog_content_bottom {
		width: 100%;
		height: 40px;
		margin: 12px 0 4px 0;
		img {
			width: 100%;
			height: 40px;
		}
		// width: 524px;
		// height: 40px;
		// gap: 4px;
		// display: flex;
		// justify-content: center;
		// align-items: center;
		// font-weight: 500;
		// font-size: 20px;
		// line-height: 40px;
		// color: #0f2860;
		// .container_dialog_content_bottom_title {
		// 	font-weight: 500;
		// 	font-size: 32px;
		// 	line-height: 32px;
		// 	color: #ff4e25;
		// }
	}

	.container_dialog_content_title {
		height: 22px;
		width: 100%;
		margin-bottom: 24px;
		display: flex;
		justify-content: center;
		align-items: center;
		.container_dialog_content_s {
			width: 270px;
			height: 22px;
			display: flex;
			gap: 24px;
			.context_wrap {
				width: 74px;
				height: 22px;
				display: flex;
				align-content: space-between;
				align-items: center;
				.context_wrap_img {
					height: 14px;
					width: 14px;
					margin: 4px 4px 4px 0;
					img {
						width: 100%;
						height: 100%;
					}
				}
				.context_wrap_title {
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
					color: #4e5969;
				}
			}
		}
	}
	.container_dialog_content_bottom_button {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.container_dialog_content_bottom_button_button {
			width: 300px;
			height: 48px;
			border-radius: 4px;
			background: #1868f1;
			color: #fff;
			font-weight: 500;
			font-size: 20px;
			cursor: pointer;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
</style>
