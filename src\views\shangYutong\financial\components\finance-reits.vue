<template>
	<div class="common-layout">
		<!-- <div class="tab_box">
			<div class="title">标准化产品</div>
			<div class="tab" @click="handleClick('ABS')">ABS</div>
			<div class="tab tabAct" @click="handleClick('REITs')">REITs</div>
		</div> -->
		<div class="container_box">
			<div class="search_main">
				<div class="search_box">
					<div class="form_box">
						<el-form :model="form" label-width="124px">
							<el-form-item label="关键词">
								<arco-input v-model="form.value" style="width: 240px" allow-clear placeholder="请输入产品、证券、机构关键词"></arco-input>
							</el-form-item>

							<el-form-item label="发行日期">
								<arco-range-picker v-model="form.data" style="width: 240px" />
							</el-form-item>

							<el-form-item label="产品状态">
								<!-- <span>基础资产</span> -->
								<el-checkbox-group fill="#fff" v-model="form.checkboxGroup0">
									<el-checkbox-button v-for="(product, index) in productList" :key="index" :label="product">
										{{ product }}
									</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>

							<el-form-item label="申报类型">
								<!-- <span>基础资产</span> -->
								<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup1">
									<el-checkbox-button v-for="(declare, index) in declareList" :key="index" :label="declare">
										{{ declare }}
									</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>

							<el-form-item label="资产业态">
								<!-- <span>基础资产</span> -->
								<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup2" border="false">
									<el-checkbox-button v-for="(subdivide, index) in assetssub" :key="index" :label="subdivide">
										{{ subdivide }}
									</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>

							<el-form-item label="交易场所">
								<!-- <span>基础资产</span> -->
								<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup3" s border="false">
									<el-checkbox-button v-for="(place, index) in placeList" :key="index" :label="place">
										{{ place }}
									</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>
							<!-- 确认取消按钮 -->
							<el-form-item>
								<el-button type="primary" @click="onSubmit" color="#1868F1">查询</el-button>
								<el-button @click="oncancel" color="#F2F3F5" style="color: #4e5969; margin-left: 8px">重置</el-button>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</div>
			<div class="table_box">
				<arco-table
					:scroll="{ y: 408, x: 2600 }"
					row-key="id"
					:data="ReitsList"
					ref="tableRef"
					:pagination="false"
					:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
				>
					<template #columns>
						<arco-table-column title="产品名称" data-index="name" ellipsis tooltip :width="212" :fixed="'left'">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.name) ? '-' : scope.record.name }}
							</template>
						</arco-table-column>
						<arco-table-column title="代码" data-index="code" ellipsis tooltip :width="104">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.code) ? '-' : scope.record.code }}
							</template>
						</arco-table-column>
						<arco-table-column title="最新收盘价(元)" data-index="closingPrice" ellipsis tooltip :width="130">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.closingPrice) ? '-' : formattedMoney(scope.record.closingPrice) }}
							</template>
						</arco-table-column>

						<arco-table-column title="最新收盘涨跌幅(%)" data-index="percentChange" ellipsis tooltip :width="155">
							<template #cell="scope">
								<div style="display: flex; align-items: center">
									{{ $utils.isEmpty(scope.record.percentChange) ? '-' : formattedMoney(scope.record.percentChange * 100) }}
									<img
										v-if="scope.record.percentChange * 100 && formattedMoney(scope.record.percentChange * 100) > 0"
										src="@/assets/arrowRise1.png"
										style="width: 16px; height: 16px; margin-left: 2px"
										alt=""
									/>
									<img
										v-if="scope.record.percentChange * 100 && formattedMoney(scope.record.percentChange * 100) < 0"
										src="@/assets/arrowRise.png"
										style="width: 16px; height: 16px; margin-left: 2px"
										alt=""
									/>
								</div>
							</template>
						</arco-table-column>
						<arco-table-column title="最新收盘日" data-index="tradeDate" :width="120" ellipsis tooltip>
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.tradeDate) ? '-' : scope.record.tradeDate }}
							</template>
						</arco-table-column>
						<arco-table-column title="资产业态" data-index="assetCategory" :width="150" ellipsis tooltip>
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.assetCategory) ? '-' : scope.record.assetCategory }}
							</template>
						</arco-table-column>
						<arco-table-column title="交易场所" data-index="exchange" :width="100" ellipsis tooltip>
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.exchange) ? '-' : scope.record.exchange }}
							</template>
						</arco-table-column>
						<arco-table-column title="总市值(亿元)" data-index="totalMarketValue" ellipsis tooltip :width="140">
							<template #cell="scope">
								{{
									$utils.isEmpty(scope.record.totalMarketValue)
										? '-'
										: formattedMoney(parseFloat((Number(scope.record.totalMarketValue) / 100000000).toFixed(3)))
								}}
							</template>
						</arco-table-column>
						<arco-table-column title="产品状态" data-index="status" :width="100" ellipsis tooltip>
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.status) ? '-' : scope.record.status }}
							</template>
						</arco-table-column>
						<arco-table-column title="申报类型" data-index="applicationType" :width="90" ellipsis tooltip>
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.applicationType) ? '-' : scope.record.applicationType }}
							</template>
						</arco-table-column>

						<arco-table-column title="最新募集份额(亿份)" data-index="raiseShare" ellipsis tooltip :width="160">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.raiseShare) ? '-' : formattedMoney(scope.record.raiseShare) }}
							</template>
						</arco-table-column>

						<arco-table-column title="最新发行价格(元)" data-index="issuePrice" ellipsis tooltip :width="140">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.issuePrice) ? '-' : formattedMoney(scope.record.issuePrice) }}
							</template>
						</arco-table-column>

						<arco-table-column title="最新发行规模(亿)" data-index="issueSize" ellipsis tooltip :width="140">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.issueSize) ? '-' : scope.record.issueSize }}
							</template>
						</arco-table-column>

						<arco-table-column title="成立日期" data-index="establishmentDate" :width="120" ellipsis tooltip>
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.establishmentDate) ? '-' : scope.record.establishmentDate }}
							</template>
						</arco-table-column>

						<arco-table-column title="原始权益人" data-index="originator" ellipsis tooltip :width="200">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.originator) ? '-' : scope.record.originator }}
							</template>
						</arco-table-column>

						<arco-table-column title="公募基金管理人" data-index="fundManager" ellipsis tooltip :width="180">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.fundManager) ? '-' : scope.record.fundManager }}
							</template>
						</arco-table-column>

						<arco-table-column title="基金经理" data-index="manager" ellipsis tooltip :width="180">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.manager) ? '-' : scope.record.manager }}
							</template>
						</arco-table-column>
						<arco-table-column title="投资专项计划规模占比" :fixed="'right'" data-index="proportionOfSpecialPlan" :width="180" ellipsis tooltip>
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.proportionOfSpecialPlan) ? '-' : scope.record.proportionOfSpecialPlan }}
							</template>
						</arco-table-column>
					</template>
				</arco-table>
				<div class="page_box">
					<arco-pagination
						:total="total"
						@change="handleCurrentChange"
						:current="currentPage"
						@page-size-change="pageSizeChange"
						:page-size="pageSize"
						size="medium"
						show-total
						show-page-size
					/>
				</div>

				<!-- <el-table
					:data="ReitsList"
					style="width: 100%; height: 500px"
					border
					header-row-class-name="tableHeader"
					ref="tableRef"
					:stripe="false"
					:lazy="true"
					size="min"
				>
					<el-table-column prop="name" label="产品名称" width="200">
						<template #default="scope">
							<el-tooltip :content="scope.row.name" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.name }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="code" label="代码" width="100" />
					<el-table-column prop="closingPrice" label="最新收盘价（元）" width="140">
						<template #default="scope">
							<span>{{ scope.row.closingPrice ? formattedMoney(scope.row.closingPrice) : '' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="percentChange" label="最新收盘涨跌幅（%）" width="165">
						<template #default="scope">
							<div style="display: flex; align-items: center">
								{{ scope.row.percentChange ? formattedMoney(scope.row.percentChange * 100) : '' }}
								<img
									v-if="scope.row.percentChange * 100 && formattedMoney(scope.row.percentChange * 100) > 0"
									src="@/assets/rise.png"
									style="width: 20px; height: 20px; margin-left: 8px"
									alt=""
								/>
								<img
									v-if="scope.row.percentChange * 100 && formattedMoney(scope.row.percentChange * 100) < 0"
									src="@/assets/decline.png"
									style="width: 20px; height: 20px; margin-left: 8px"
									alt=""
								/>
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="tradeDate" label="最新收盘日" width="120" />
					<el-table-column prop="assetCategory" label="资产业态" width="130" />
					<el-table-column prop="exchange" label="交易场所" width="100" />
					<el-table-column prop="totalMarketValue" label="总市值(元)" width="120">
						<template #default="scope">
							<span>{{ scope.row.totalMarketValue ? formattedMoney(scope.row.totalMarketValue) : '' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="status" label="产品状态" width="100" />
					<el-table-column prop="applicationType" label="申报类型" width="90" />
					<el-table-column prop="raiseShare" label="最新募集份额(亿份)" width="160">
						<template #default="scope">
							<span>{{ scope.row.raiseShare ? formattedMoney(scope.row.raiseShare) : '' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="issuePrice" label="最新发行价格(元)" width="140">
						<template #default="scope">
							<span>{{ scope.row.issuePrice ? formattedMoney(scope.row.issuePrice) : '' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="issueSize" label="最新发行规模(亿)" width="140">
						<template #default="scope">
							<span>{{ scope.row.issueSize ? formattedMoney(scope.row.issueSize) : '' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="establishmentDate" label="成立日期" width="120" />
					<el-table-column prop="originator" label="原始权益人" width="200">
						<template #default="scope">
							<el-tooltip :content="scope.row.originator" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.originator }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="fundManager" label="公募基金管理人" width="180">
						<template #default="scope">
							<el-tooltip :content="scope.row.fundManager" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.fundManager }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="manager" label="基金经理" width="180">
						<template #default="scope">
							<el-tooltip :content="scope.row.manager" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.manager }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="proportionOfSpecialPlan" label="投资专项计划规模占比" width="180" />
				</el-table> -->
				<!-- <div class="page_box">
					<div class="total_box">
						共<span>{{ total }}</span
						>项
					</div>
					<el-pagination
						@current-change="handleCurrentChange"
						:current-page="currentPage"
						layout="prev, pager, next,total, jumper"
						:page-size="10"
						class="mt-4"
						:total="total"
					/>
				</div> -->
			</div>
		</div>
	</div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { getFinanceReitsList } from '@/api/syt.js';
import { getReitsListApi } from '@/api/finance.js';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { formattedMoney } from 'UTILS';
const router = useRouter();
import { useStore } from '../../../../store/index.js';
const store = useStore();
// do not use same name with ref
//   表单
const form = reactive({
	value: '',
	data: '',
	checkboxGroup0: [],
	checkboxGroup1: [],
	checkboxGroup2: [],
	checkboxGroup3: [],
});
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

let tableRef = ref(null);

const { proxy } = getCurrentInstance();
nextTick(() => {
	proxy?.$dragTable(tableRef);
});

const handleCurrentChange = (val) => {
	currentPage.value = val;
	getReitsList();
};

const pageSizeChange = (val) => {
	pageSize.value = val;
	getReitsList();
};
const productList = ['全部', '已申报', '募集中', '已成立', '已清算', '已取消', '已受理', '已通过'];
const assetssub = ['保障性租赁住房', '仓储物流', '产业园区', '消费基础设施'];
const declareList = ['全部', '首发', '扩募'];
const placeList = ['全部', '上海证券交易所', '深圳证券交易所'];

const onSubmit = () => {
	console.log('submit!');
	getReitsList();
};
//   表格
const tableData = [
	{
		date: '2016-05-03',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-02',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-04',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-01',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
];
const handleClick = (tab) => {
	if (tab === 'ABS') {
		router.push({
			path: '/shangYutong/financial/standard1/abs',
			query: { path: '/shangYutong/financial/standard1/abs' },
		});
		sessionStorage.setItem('childMenuIndex3', 0);
		store.changeChildMenuIndex3(0);
	} else {
		router.push({
			path: '/shangYutong/financial/standard1/reits',
			query: { path: '/shangYutong/financial/standard1/reits' },
		});
		sessionStorage.setItem('childMenuIndex3', 1);
		store.changeChildMenuIndex3(1);
	}
};
// 点击取消
const oncancel = () => {
	console.log('cancel!');
	// 清空表单
	form.value = '';
	form.data = '';
	form.checkboxGroup0 = [];
	form.checkboxGroup1 = [];
	form.checkboxGroup2 = [];
	form.checkboxGroup3 = [];
	currentPage.value = 1;
	onSubmit();
};

// 发请求
const ReitsList = ref([]);
const getReitsList = async () => {
	const params = {
		keyword: form.value,
		issueDateStart: form.data?.[0],
		issueDateEnd: form.data?.[1],
		productStatus: form.checkboxGroup0.toString(),
		applicationType: form.checkboxGroup1.toString(), //申报类型
		assetCategory: form.checkboxGroup2.toString(), //资产业态
		exchange: form.checkboxGroup3.toString(), //资产业态
		currentPage: currentPage.value,
		pageSize: pageSize.value,
	};
	const res = await getFinanceReitsList(params)
		.then((res) => {
			ReitsList.value = res.data.rows;
			total.value = res.data.total;
			return res;
		})
		.catch((err) => {
			console.log(err, 12345);
			return err;
		});
	// console.log(res,2346);
};
getReitsList();
</script>

<style scoped lang="less">
.cell-content {
	display: inline-block;
	width: 100%; // 宽度设置为100%，以填满列宽
	overflow: hidden; // 隐藏超出部分
	text-overflow: ellipsis; // 超出部分显示省略号
	white-space: nowrap; // 防止文本换行
}
.common-layout {
	width: 100%;
	height: 100%;
	box-sizing: border-box;

	.tab_box {
		width: 100%;
		height: 56px;
		padding: 0 15px;
		box-sizing: border-box;
		font-size: 14px;
		font-weight: 600;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		background-color: rgba(255, 255, 255, 1);

		.title {
			margin-right: 15px;
		}

		.tab {
			width: 60px;
			height: 56px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			cursor: pointer;
		}

		.tabAct {
			width: 60px;
			height: 56px;

			&::after {
				content: '';
				width: 24px;
				height: 3px;
				position: absolute;
				bottom: 0;
				background-color: rgba(3, 93, 255, 1);
			}
		}
	}

	.container_box {
		width: 100%;
		height: 100%;
		// padding-top: 10px;
		box-sizing: border-box;

		.search_main {
			width: 100%;
			height: 100%;
			background-color: rgba(255, 255, 255, 1);
			box-sizing: border-box;
			position: relative;

			.btn_box {
				width: 100%;
				height: 22px;
				padding: 0 15px;
				box-sizing: border-box;
				margin-bottom: 10px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				cursor: pointer;
				font-size: 12px;
				color: rgba(134, 144, 156, 1);

				.title1 {
					width: auto;
					font-size: 14px;
					color: rgba(60, 60, 60, 1);
					margin-right: 10px;
				}

				span {
					display: inline-block;
					transform: rotate(-90deg);
					margin-left: 5px;
				}
			}

			.search_box {
				width: 100%;
				overflow: hidden;
				padding: 32px 16px 0px 16px;
				box-sizing: border-box;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				::v-deep .el-checkbox-group .is-checked .el-checkbox-button__inner {
					margin-left: -1px;
					padding-left: 16px;
					border: 1px solid #1868f1 !important;
					background: #fff !important;
					box-shadow: none !important;
					color: #1868f1 !important;
				}

				::v-deep .el-checkbox-group .is-focus .el-checkbox-button__inner {
					border: 1px solid #e5e6eb;
				}

				::v-deep .el-form-item__label {
					font-weight: 400;
					font-size: 14px;
					color: #4e5969;
				}
				::v-deep .arco-input-wrapper {
					background: none !important;
					border: 1px solid #e5e6eb;
					border-radius: 4px;
				}

				::v-deep .arco-picker {
					background: none !important;
					border: 1px solid #e5e6eb;
					border-radius: 4px;
				}
				::v-deep .arco-picker-focused {
					border: 1px solid #1868f1;
				}
				::v-deep .arco-picker-input > input {
					background: none !important;
				}

				::v-deep .arco-input-wrapper:hover {
					border: 1px solid #1868f1;
				}
				.form_box {
					width: 100%;

					.el-input__wrapper {
						flex: none;
						flex-grow: 0 !important;
					}

					.el-checkbox-button {
						// border:1px solid #;
					}

					.el-table__header-wrapper {
						background-color: #000 !important;
					}
				}

				.box_2 {
					width: 230px;
					height: 32px;
					margin: 10px 5px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					box-sizing: border-box;
				}
			}
		}

		.table_box {
			width: 100%;
			height: auto;
			padding: 15px;
			box-sizing: border-box;
			background-color: rgba(255, 255, 255, 1);
			position: relative;
			::v-deep .arco-table-container {
				border-radius: 4px !important;
				.arco-table-content {
					border-radius: 4px !important;
					tbody > :nth-last-child(1) > :nth-last-child(1) {
						border-bottom-right-radius: 4px !important;
					}
					tbody > :nth-last-child(1) > :nth-child(1) {
						border-bottom-left-radius: 4px !important;
					}
				}
			}
			.page_box {
				width: 100%;
				margin-top: 20px;
				height: 32px;
				display: flex;
				justify-content: end;
				align-items: center;

				.total_box {
					width: auto;
					height: 56px;
					display: flex;
					justify-content: center;
					align-items: center;

					span {
						color: rgba(3, 93, 255, 1);
					}
				}
			}

			.btn {
				position: absolute;
				top: 10px;
				right: 10px;
				z-index: 10;
				font-size: 14px;
				color: rgba(134, 144, 156, 1);

				span {
					display: inline-block;
					transform: rotate(-90deg);
					margin-left: 5px;
				}
			}

			&::v-deep .el-table--fit {
				border-radius: 8px;
			}

			&::v-deep .el-table th {
				background-color: rgba(245, 245, 245, 1);
			}
		}
	}
}

.el-input__wrapper {
	flex: none;
	flex-grow: 0 !important;
}

.el-checkbox-button {
	// border:1px solid #;
}

.el-table__header-wrapper {
	background-color: #000 !important;
}
</style>
