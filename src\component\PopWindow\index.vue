<template>
	<div
		class="popWindow pop-window-container ax-video-part-window-container"
		v-if="visible"
		ref="popWindowRef"
		:style="`z-index:200${zIndex < 2 ? 2 : 3}`"
	>
		<div
			class="container animate__animated animate__zoomIn"
			:style="`width:${popData && popData.width};height:${popData && popData.height};top:${popData && popData.top};left:${popData && popData.left}`"
		>
			<div class="content" :style="`padding-top:${popData && popData.paddingTop}`">
				<div class="ctrl">
					<div v-if="showClose" :class="`iconfont icon-guanbi`" title="关闭" @click="closePop">关闭</div>
				</div>
				<comp :popData="popData" :closePop="closePop" />
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
interface Props {
	id: string;
	zIndex: number;
	popData: any;
	closeCallBack?: any;
	destroy: () => void;
}
const props = defineProps<Props>();
const popWindowRef = ref(null);
// 显示弹窗
let visible = ref<boolean>(true);
// 判断是否展示关闭按钮
const showClose = computed(() => !props.popData?.hideClose);

// 关闭pop
const closePop = () => {
	visible.value = false;
	// 关闭的回调事件
	if (props.popData?.closeCallBack) {
		props.popData.closeCallBack();
	}
	// 在隐藏弹窗时主动销毁弹窗的父级元素，避免一个容器挂载多个实例
	const _dom = document.getElementById(props.id);
	if (_dom) {
		document.body.removeChild(_dom);
	}
	props.destroy();
};
// 切换全屏
provide('$closePop', closePop);
</script>
<style lang="less">
@import './style.less';
</style>
