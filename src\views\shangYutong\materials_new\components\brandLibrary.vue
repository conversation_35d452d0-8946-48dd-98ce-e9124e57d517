<template>
	<div class="brand-library">
		<arco-spin :loading="loadings" style="width: calc(100% - 0px)">
			<div v-show="libraryStatus">
				<!-- 筛选条件区域 -->
				<div class="filter-section">
					<div class="search-container">
						<arco-input-search
							style="max-width: 400px"
							v-model="filters.keyword"
							placeholder="请输入品牌名称找品牌"
							search-button
							@search="handleSearch"
						>
							<template #button-icon>
								<img src="@/assets/search.png" style="width: 16px; height: 16px; margin-top: 6px" alt="" />
							</template>
							<template #button-default> 搜索 </template>
						</arco-input-search>
					</div>
					<!-- 加盟方式 -->
					<div class="filter-item">
						<span class="filter-label">加盟方式</span>
						<arco-radio-group v-model="filters.joiningMode" type="button" @change="handleSearch">
							<arco-radio value="">不限</arco-radio>
							<arco-radio value="代理">代理</arco-radio>
							<arco-radio value="直营">直营</arco-radio>
							<arco-radio value="加盟">加盟</arco-radio>
						</arco-radio-group>
					</div>

					<!-- 拓展区域 -->
					<div class="filter-item">
						<span class="filter-label">拓展区域</span>
						<arco-radio-group v-model="filters.expansionArea" type="button" @change="handleSearch">
							<arco-radio value="">不限</arco-radio>
							<arco-radio value="201">华北区</arco-radio>
							<arco-radio value="202">东北区</arco-radio>
							<arco-radio value="203">华东区</arco-radio>
							<arco-radio value="204">华中区</arco-radio>
							<arco-radio value="205">华南区</arco-radio>
							<arco-radio value="206">西南区</arco-radio>
							<arco-radio value="207">西北区</arco-radio>
						</arco-radio-group>
					</div>

					<!-- 业态类别 -->
					<div class="filter-item">
						<span class="filter-label">业态类别</span>
						<arco-radio-group v-model="filters.businessType" type="button" @change="handleSearch">
							<arco-radio value="">不限</arco-radio>
							<arco-radio value="餐饮">餐饮</arco-radio>
							<arco-radio value="儿童亲子">儿童亲子</arco-radio>
							<arco-radio value="文体娱">文体娱</arco-radio>
							<arco-radio value="零售">零售</arco-radio>
							<arco-radio value="生活服务">生活服务</arco-radio>
							<arco-radio value="其他类型">其他类型</arco-radio>
						</arco-radio-group>
					</div>

					<!-- 面积要求 -->
					<div class="filter-item">
						<span class="filter-label">面积要求</span>
						<arco-radio-group v-model="filters.requiredArea" type="button" @change="handleSearch">
							<arco-radio value="">不限</arco-radio>
							<arco-radio value=",50">50平米以下</arco-radio>
							<arco-radio value="50,200">50-200平米</arco-radio>
							<arco-radio value="200,500">200-500平米</arco-radio>
							<arco-radio value="500,1000">500-1000平米</arco-radio>
							<arco-radio value="1000,">1000平米以上</arco-radio>
						</arco-radio-group>
					</div>

					<!-- 联系方式 -->
					<div class="filter-item">
						<span class="filter-label">联系方式</span>
						<arco-radio-group v-model="filters.hasContactor" type="button" @change="handleSearch">
							<arco-radio value="0">不限</arco-radio>
							<arco-radio value="1">有</arco-radio>
							<arco-radio value="2">无</arco-radio>
						</arco-radio-group>
					</div>

					<!-- 是否关注 -->
					<div class="filter-item">
						<span class="filter-label">是否关注</span>
						<arco-radio-group v-model="filters.isCollect" type="button" @change="handleSearch">
							<arco-radio value="0">不限</arco-radio>
							<arco-radio value="1">已关注</arco-radio>
							<arco-radio value="2">未关注</arco-radio>
						</arco-radio-group>
					</div>
				</div>

				<!-- 品牌列表 -->
				<div class="brand-list">
					<div class="brand-card" v-for="(item, index) in brandList" :key="index" @click="handleBrandDetail(item, index)">
						<div class="card_box">
							<div class="brand-logo">
								<img :src="item.brandLogo" alt="" />
							</div>
							<div class="brand-info">
								<div class="brand-name">{{ item.brandName }}</div>
								<div class="brand-area">{{ item.listIndustryType }} | {{ item.areaMin }} - {{ item.areaMax }}m²</div>
							</div>
						</div>
						<div class="tag_box">
							<div class="box_tag">
								<div class="brand-tag" v-for="(childItem, childeIndex) in item.tagNameList" :key="childeIndex">{{ childItem }}</div>
							</div>

							<div class="action-area" v-if="item.isCollect" @click.stop="handleBrandCollect(item)">
								<img class="actionImg" src="@/assets/attention.png" alt="" />
								<div class="actionText actionyText">{{ '已关注' }}</div>
							</div>

							<div class="action-area" v-else @click.stop="handleBrandCollect(item)">
								<img class="actionImg" src="@/assets/noattention.png" alt="" />
								<div class="actionText">{{ '关注' }}</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 分页 -->
				<div class="pagination">
					<arco-pagination
						:total="total"
						@change="handleCurrentChange"
						:current="pagination.currentPage"
						:page-size="pagination.pageSize"
						@page-size-change="pageSizeChange"
						:page-size-options="[15, 30, 60, 120]"
						show-page-size
						show-total
					/>
				</div>
			</div>
			<brandLibrarydetails
				v-if="!libraryStatus"
				:brandObj="brandObj"
				@handleReturnBtn="handleReturnBtn"
				@handleBrand="handleBrand"
			></brandLibrarydetails>
		</arco-spin>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import brandLibrarydetails from './brandLibrarydetails.vue';
import { getbrandList, getbrandCollect, getbrandDetail } from '@/api/syt.js';
import { ElMessage } from 'element-plus';
const libraryStatus = ref(true);

// 搜索关键词
const brandObj = ref();

const loadings = ref(false);

// 筛选条件
const filters = ref({
	keyword: '',
	joiningMode: '',
	expansionArea: '',
	businessType: '',
	requiredArea: '',
	hasContactor: '0',
	isCollect: '0',
});

// 分页数据
const pagination = ref({
	currentPage: 1,
	pageSize: 15,
});

const total = ref(0);

const brandCollectindex = ref();

// 品牌列表数据
const brandList = ref([]);

// 处理搜索
const handleSearch = (value) => {
	handleGetbrandList();
};

onMounted(() => {
	handleGetbrandList();
});

const handleGetbrandList = async () => {
	await getbrandList({
		...filters.value,
		...pagination.value,
	}).then((res) => {
		if (res.code === 200) {
			res.data.rows.forEach((element, index) => {
				if (element.tagName) {
					console.log(index, 'index');
					element.tagNameList = element.tagName.split(',');
				} else {
					element.tagNameList = [];
				}
			});
			brandList.value = res.data.rows;
			total.value = res.data.total;
		} else {
			brandList.value = [];
			total.value = 0;
		}
	});
};

const handleCurrentChange = (val) => {
	pagination.value.currentPage = val;
	handleGetbrandList();
};

const pageSizeChange = (val) => {
	pagination.value.pageSize = val;
	handleGetbrandList();
};

function handleBrandCollect(item) {
	item.isCollect = !item.isCollect;
	getbrandCollect({
		brandId: item.brandId,
		type: item.isCollect ? 1 : 0,
	}).then((res) => {
		// 提示
		if (res.code == 200) {
			ElMessage({
				message: item.isCollect ? '已关注' : '已取消关注',
				type: 'success',
			});
		}
	});
}

function handleBrandDetail(item, index) {
	loadings.value = true;
	brandCollectindex.value = index;
	getbrandDetail({
		brandId: item.brandId,
	}).then((res) => {
		// 提示
		if (res.code == 200) {
			libraryStatus.value = false;
			loadings.value = false;
			brandObj.value = res.data;
		}
	});
}

function handleReturnBtn() {
	libraryStatus.value = true;
}

function handleBrand() {
	handleGetbrandList();
	// brandList.value[brandCollectindex.value].isCollect = true;
}
</script>

<style scoped lang="scss">
.brand-library {
	background-color: #f5f7fa;
}

.search-container {
	margin-bottom: 20px;
	:deep(.arco-btn) {
		background: #1868f1;
	}
}

.filter-section {
	background-color: #fff;
	padding: 20px 10px 4px 16px;
	margin-right: 6px;
	border-radius: 0px 4px 4px 4px;
	margin-bottom: 16px;
}

.filter-item {
	margin-bottom: 16px;
	height: 22px;
	display: flex;
	align-items: center;
}

.filter-label {
	width: 56px;
	margin-right: 20px;
	font-size: 14px;
	line-height: 22px;
	color: #1d2129;
	font-weight: 600;
}

/* 自定义单选按钮组样式 */
:deep(.arco-radio-group-button) {
	border: none;
	background-color: transparent;
}

:deep(.arco-radio-button-content) {
	padding: 0px 0px 0px 20px !important;
}

:deep(.arco-radio-button) {
	border: none !important;
	background-color: transparent !important;
	margin-right: 0px;
	padding: 0;
}

:deep(.arco-radio-button input[type='radio']:checked + .arco-radio-button__inner) {
	background-color: transparent !important;
	color: #165dff !important;
	border-color: transparent !important;
	box-shadow: none !important;
}

:deep(.arco-radio-button input[type='radio'] + .arco-radio-button__inner) {
	background-color: transparent;
	border: none;
	color: #86909c;
}

:deep(.arco-radio-button:not(:first-child)::before) {
	display: none;
}

.brand-list {
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;
	gap: 16px;
}

.pagination {
	margin: 20px 6px 20px 0;
	display: flex;
	justify-content: flex-end;
}

.brand-card {
	cursor: pointer;
	width: 315.6px;
	background-color: #fff;
	position: relative;
	padding: 20px 16px;
	box-sizing: border-box;
	border-radius: 4px;
	height: fit-content;
	// height: 160px;
	.card_box {
		display: flex;
		height: 72px;
		margin-bottom: 24px;
		.brand-logo {
			width: 72px;
			height: 72px;
			border-radius: 4px;
		}
		.brand-logo img {
			width: 72px;
			height: 72px;
			border-radius: 4px;
			object-fit: contain;
		}
		.brand-info {
			margin-left: 12px;
			padding: 9px 0px;
			.brand-name {
				font-weight: 500;
				font-size: 16px;
				line-height: 24px;
				color: #1d2129;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.brand-area {
				color: #4e5969;
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				margin-top: 8px;
			}
		}
	}
	.tag_box {
		display: flex;
		justify-content: space-between;
		// height: 24px;
		.box_tag {
			display: flex;
			// height: 24px;
			flex-wrap: wrap;
			gap: 8px;
			max-width: calc(100% - 63px);
			.brand-tag {
				background: #e8f3ff;
				height: 24px;
				gap: 4px;
				padding: 1px 8px;
				border-radius: 2px;
				font-weight: 500;
				font-size: 14px;
				line-height: 22px;
				text-align: center;
				color: #1868f1;
			}
		}

		.action-area {
			width: 64px;
			height: 24px;
			display: flex;
			align-items: center;
			gap: 4px;
			cursor: pointer;
			.actionImg {
				width: 16px;
				height: 16px;
				object-fit: contain;
			}
			.actionText {
				font-weight: 400;
				font-size: 14px;
				line-height: 24px;
				color: #86909c;
			}
			.actionyText {
				color: #1d2129 !important;
			}
		}
	}
}
</style>
