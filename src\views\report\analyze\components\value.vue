<template>
	<div class="main">
		<div class="title">五、估值</div>
		<div class="sub_title_1">5.1估值报告</div>
		<div class="sub_title_2">5.1.1股价对象描述与分析</div>
		<div class="sub_title_3">*******物业邻近环境及建筑物</div>
		<p>估价对象坐落于{{ dataOne?.buildingAddress }}，地处{{ dataOne?.businessName }}，{{ dataOne?.businessIntroduce }}</p>
		<p>
			周边分布的学校有{{ kejiao }}；医院有青岛{{ yiliao }}；休闲娱乐场所有{{ xiuxian }}等；金融机构有{{
				jinrong
			}}、中国民生银行、中国光大银行等；餐饮品种丰富，区域内均设有中西式餐馆，公共服务配套设施完善。
		</p>
		<p class="center" style="font-weight: 700">估价对象周边环境现状照片</p>
		<div class="basic_pic" style="display: flex; justify-content: center; align-items: center; gap: 16px">
			<el-image :src="url + (dataTwo?.nearbyBuildingsImages?.[0]?.coverUrl || '')" style="width: 40%" fit="cover" />
			<el-image :src="url + (dataTwo?.nearbyBuildingsImages?.[1]?.coverUrl || '')" style="width: 40%" fit="cover" />
		</div>
		<div style="display: flex; justify-content: space-around">
			<p>{{ dataTwo?.nearbyBuildingsImages?.[0]?.name }}</p>
			<p>{{ dataTwo?.nearbyBuildingsImages?.[1]?.name }}</p>
		</div>
		<div class="sub_title_3">5.1.1.2物业之交通条件</div>
		<div>
			<p>估价对象交通非常便捷，地处{{ dataOne?.district }}的核心地段，公共交通发达。</p>
			<p>
				距离{{ dataTwo?.nearbyAirport?.name }}约{{ (dataTwo?.nearbyAirport?.distance / 1000)?.toFixed(1) }}公里，距{{
					dataTwo?.nearbyTrainStation?.name
				}}{{ (dataTwo?.nearbyTrainStation?.distance / 1000)?.toFixed(1) }}公里。
			</p>
			<p>周边毗邻{{ mainroad }}等多条交通主干道，自驾出行便利。</p>
			<p>
				周边公共交通设施便利临近{{ dataTwo?.nearbySubwayStation }}，邻近公交站点有{{ busname }}公交站。同行上述公交站点的公交线路有{{
					buslu
				}}交通便捷程度高。
			</p>
		</div>
		<div class="sub_title_3">5.1.1.3土地实物状况分析</div>
		<p>{{ dataOne?.evaluateObjects }}</p>
		<p>
			根据估价人员的实地查勘，估价对象所占用地块呈规则四边形，地势平坦。该地块市政基础设施齐全，宗地红线外已达到七通一平(通路、通电、通信、通上水、通下水、通燃气、通热力),宗地周边商业气氛成熟，有住宅、商业、办公等物业。
		</p>
		<!-- <div class="box">
			<div class="item"></div>
			<div class="item"><el-image :src="url" style="width: 100%" fit="cover" /></div>
			<div class="item"></div>
			<div class="item"><el-image :src="url" style="width: 100%" fit="cover" /></div>
			<div class="item">
				<el-image :src="url" style="width: 100%" fit="cover" />
			</div>
			<div class="item"><el-image :src="url" style="width: 100%" fit="cover" /></div>
			<div class="item"></div>
			<div class="item"><el-image :src="url" style="width: 100%" fit="cover" /></div>
			<div class="item"></div>
		</div> -->
		<p class="sub_title_3">5.1.1.4产权状况</p>
		<div>
			<p style="font-weight: 600">（1）中华人民共和国土地使用制度</p>
			<p>
				根据《中华人民共和国宪法》(2004年修订案)第十条，我国建立了土地使用权与土地所有权两权分离制度。自此，有偿取得的有限年期的土地使用权均可在中国转让、赠予、出租、抵押。市级地方政府可通过协议、招标或拍卖方式等三种方式将有限年期的土地使用权出让给国内及国外机构。一般情况下，土地使用权出让金将按一次性支付，土地使用者在支付全部土地使用权出让金后，可领取《国有土地使用证》。土地使用者同时需要支付其它配套公用设施费用、开发费及拆迁补偿费用予原居民。物业建成后，当地的房地产管理部门将颁发《房屋所有权证》或《房地产权证》,以证明估价对象的土地使用权及房屋所有权。
			</p>
			<p style="font-weight: 600">（2）国有土地性质</p>
			<p>根据术木智能收录的资产数据及其他资料，估价对象的土地用途为{{ dataOne?.evaluateBuildingType }}。</p>
		</div>
		<p class="sub_title_2">5.1.2估价对象租赁现状描述与分析</p>
		<p>
			估价对象是地下四层至地上十层(结构楼层)的商业裙楼，总建筑面积{{ dataOne?.evaluateDetails?.area }}平方米，商业可出租面积为{{
				dataThr?.leasableArea
			}}平方米，已出租面积为{{ dataThr?.leasedArea }}平方米，出租率约为{{ dataOne?.lettingRate }}%,已签约租户共计{{ dataOne?.tenantTotal }}个。
		</p>
		<p class="sub_title_3">5.1.2.1收入分析</p>
		<p class="bold">5.1.2.1.1租金分析</p>
		<p>
			估价对象是地下四层至地上十层(结构楼层)的商业裙楼，总建筑面积{{ dataOne?.evaluateDetails?.area }}平方米，商业可出租面积为{{
				dataThr?.leasableArea
			}}平方米，已出租面积为{{ dataThr?.leasedArea }}平方米，出租率约为{{ dataOne?.lettingRate }}%,已签约租户共计{{
				dataOne?.tenantTotal
			}}个。截止至价值时点{{ dataOne?.applicationTime }}，估价对象平均签约租金为{{ dataThr?.rent }}元/平方米/月(使用面积),当期年租金收入约{{
				dataThr?.currentAnnualRent
			}}元。根据租期长短，部分租户有1-13个月的免租期，部分租约在租期内有不同程度的递增。
		</p>
		<p>其详细租约情况见附件一《项目情况说明》。</p>
		<p class="bold">5.1.2.1.2物业管理收入</p>
		<p>
			根据术木智能调查,估价对象由{{ dataThr?.propertyCompany }}进行物业管理并向租户收取物业管理费，由于{{
				dataThr?.propertyCompany
			}}跟产权方为关联公司，故{{
				dataThr?.propertyCompany
			}}涉及本次估价对象的物业收入和成本应按照产权方的收入和成本考量。目前估价对象租户物业管理费为{{
				dataThr?.propertyFee
			}}元/平方米/月，2024年物业管理费拟收入约为{{ dataThr?.propertyFeePseudoRevenue }}元。
		</p>
		<p class="bold">5.1.2.1.3车位收入</p>
		<p>
			根据产权人提供的《项目情况说明》,估价对象可出租地下车位数量为{{
				dataThr?.parkingNum
			}}个，主要收入形式为对外包月出租或按小时收费临时停车。根据产权方提供的资料，{{ dataThr?.year }}年车位拟收入金额约为{{
				dataThr?.propertyFeePseudoRevenue
			}}万元。
		</p>
		<p class="sub_title_3">5.1.2.2租户结构分析</p>
		<p>
			根据业主提供的资料，本项目已出租面积为{{ dataThr?.leasedArea }}平方米，出租率约为{{
				dataOne?.lettingRate
			}}%。本项目租户涉及行业范围广泛，面积占比较大的行业有{{ fenxiname }},分别占比{{ fenxibilie }},租户涉及行业比例请详见下表：
		</p>
		<p class="center bold">本项目租户行业配比</p>
		<div class="matching">
			<div class="matching-box">
				<el-table :data="zuhutable" class="zuhutable table_header_bg" border>
					<el-table-column prop="commercialForm" label="业态" />
					<el-table-column prop="merchantPercentage" label="租户占比" />
				</el-table>
				<tenantMatching :pieData="zuhutable"></tenantMatching>
			</div>
		</div>
		<p class="sub_title_2">5.1.3青岛市优质商业物业市场</p>
		<p>
			青岛商业发展萌芽于二十世纪七十年代。二十世纪九十年代，传统百货陆续涌现。进入2005年，百货业态档次及时尚度均有较大提升。2008年，购物中心类型商业成为了商业发展主流，百货业出现了购物中心化并逐渐增强体验型商业业态。目前，青岛已形成以新百-东购商圈、唐岛湾商圈为核心商圈，怀特-万达商圈为新兴商圈，建华商圈等小型商圈为区域商圈的商业布局。其中新百-东购商圈、唐岛湾商圈及建华商圈均沿中山、裕华东西中心轴分布，中心轴附近集中了青岛大多数零售商业项目。由于具有国企背景，北人集团在青岛优质商业市场占有重要地位，但是，非本地开发商和新项目的开业已经造成对青岛市传统商业的冲击，并且在青岛商业市场中，购物中心逐步成为商业开发的主要趋势。
		</p>
		<div class="AdminSketch">
			<el-image :src="tradingArea" />
			<div class="center bold">{{ dataOne?.city }}主要商圈分布图</div>
		</div>
		<p class="bold">{{ dataThr?.businessIntroduceList?.[0]?.businessName }}</p>
		<p>{{ dataThr?.businessIntroduceList?.[0]?.supportingProjects }}</p>
		<p class="bold">{{ dataThr?.businessIntroduceList?.[1]?.businessName }}</p>
		<p>{{ dataThr?.businessIntroduceList?.[1]?.supportingProjects }}</p>
		<p class="bold">{{ dataOne?.businessName }}</p>
		<p>{{ dataOne?.businessIntroduce }}</p>

		<p class="sub_title_3">5.1.3.1{{ dataOne?.city + dataOne?.businessName }}商业市场分析</p>
		<p class="bold">5.1.3.1.1区域概述</p>
		<div style="height: 400px; width: 100%; margin-bottom: 20px">
			<baogaomap v-if="locadata.length > 0" :containerId="'map-container-2'" :zoom="13.5" :locadata="locadata" :lat="lat"> </baogaomap>
		</div>
		<p class="bold">区域发展历程</p>
		<p>{{ dataOne?.businessIntroduce }}</p>
		<p class="center bold">区域内主要商业情况</p>
		<div class="matching">
			<el-table :data="shangyetable" class="shangyetable table_header_bg" border>
				<el-table-column type="index" width="50" />
				<el-table-column prop="buildingName" :width="250" label="项目名称" />
				<el-table-column prop="buildingType" :width="250" label="业态" />
				<el-table-column prop="totalArea" :width="250" label="建筑面积（平方米）" />
				<el-table-column prop="openingDate" :width="250" label="开业时间" />
			</el-table>
		</div>
		<p style="text-align: center">资料来源：术木智能</p>
		<div class="sub_title_2">5.1.4价值时点</div>
		<p>{{ dataOne?.applicationTime }}(用户授权日期)</p>

		<p class="sub_title_2">5.1.5估价测算过程</p>
		<p class="sub_title_3">5.1.5.1估价测算过程之比较法</p>
		<p class="underline bold">商业部分</p>
		<p>可比实例选取原则：区域类似、用途一致、个别条件相近、交易类型相同、成交价格正常、交易时间与价值时点接近等原则。</p>
		<p>
			确定商业房地产市场价值时，我们采用比较法，由于估价对象为大型综合购物中心项目，青岛市目前类似大型购物中心交易情况较少，故首先选择两个类似项目散售案例的比较实例作为参照，经过充分考虑各物业的差异，作出修正后得出估价物业的市场价值。具体计算过程如下：
		</p>
		<p class="underline bold">选取可比实例</p>
		<p>经过市场调查与研究，我们最终确定了2个类似商业作为估价对象的可比实例。可比实例详情概述如下：</p>
		<p>
			可比实例选取原则：区域类似、用途一致、个别条件相近、交易类型相同、成交价格正常、交易时间与估值基准日接近等原则。确定商业房地产市场价值时，我们采用比较法，首先选择其中三个较为接近估价对象情况的比较实例作为参照，经过充分考虑各物业的差异，作出修正后得出评估物业的市场价值。具体计算过程如下：
		</p>
		<div style="width: 100% !important">
			<table class="MsoNormalTable">
				<thead>
					<tr>
						<td valign="center" colspan="3" style="width: 25%" class>因素</td>
						<td valign="center" style="width: 25%" class>待估物业</td>
						<td valign="center" style="width: 25%" class>可比实例一</td>
						<td valign="center" style="width: 25%" class>可比实例二</td>
					</tr>
				</thead>
				<tbody v-if="kebitable.length > 0">
					<tr>
						<td valign="center" colspan="3" rowspan="3" class>项目名称</td>
						<td v-for="(item, index) in kebitable" :key="index" valign="center" rowspan="3" class>
							{{ item.buildingName }}
						</td>
					</tr>
					<tr></tr>
					<tr></tr>
					<tr>
						<td valign="center" colspan="3" rowspan="6" class>物业照片</td>
						<td valign="center" rowspan="6" v-for="(item, index) in kebitable" :key="index" class>
							<img :src="url + item.mainImage" v-if="isImage(item.mainImage)" style="width: 100px; height: auto" />
						</td>
					</tr>
					<tr></tr>
					<tr></tr>
					<tr></tr>
					<tr></tr>
					<tr></tr>
					<tr>
						<td valign="center" colspan="3" class>案例来源</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.caseSource }}</td>
					</tr>
					<tr>
						<td valign="center" colspan="3" class>交易时间</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.tradingHour }}</td>
					</tr>
					<tr>
						<td valign="center" colspan="3" class>交易价格(元/平方米)</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.transactionPrice }}</td>
					</tr>
					<tr>
						<td valign="center" colspan="3" class>交易情况</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.tradingSituation }}</td>
					</tr>
					<tr>
						<td valign="center" rowspan="15" style="writing-mode: vertical-lr" class>房地产状况</td>
						<td valign="center" rowspan="6" class>区位状况</td>
						<td valign="center" class>商业繁华度</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.businessProsperity }}</td>
					</tr>
					<tr>
						<td valign="center" class>交通便捷度</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.trafficConvenience }}</td>
					</tr>
					<tr>
						<td valign="center" class>基础设施完善度</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.infrastructurePerfection }}</td>
					</tr>
					<tr>
						<td valign="center" class>白然及人文环境</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.humanisticEnvironment }}</td>
					</tr>
					<tr>
						<td valign="center" class>公共服务设施状况</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.serviceConditionFacilities }}</td>
					</tr>
					<tr>
						<td valign="center" class>楼层</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.buildingFloors }}</td>
					</tr>
					<tr>
						<td valign="center" rowspan="6" class>实体状况</td>
						<td valign="center" class>商业类型</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.buildingType }}</td>
					</tr>
					<tr>
						<td valign="center" class>建筑面积(平方米)</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.buildingArea }}</td>
					</tr>
					<tr>
						<td valign="center" class>进深比</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.depthRatio }}</td>
					</tr>
					<tr>
						<td valign="center" class>配套设施设备</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.supportingFacilities }}</td>
					</tr>
					<tr>
						<td valign="center" class>内部装修</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.interiorDecoration }}</td>
					</tr>
					<tr>
						<td valign="center" class>楼龄及保养</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.buildingAgeMaintenance }}</td>
					</tr>
					<tr>
						<td valign="center" rowspan="3" class>权益状况</td>
						<td valign="center" class>土地剩余年限</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.groundRemainingAge }}</td>
					</tr>
					<tr>
						<td valign="center" class>租约限制</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.leaseRestriction }}</td>
					</tr>
					<tr>
						<td valign="center" class>规划限制条件(如容积率)</td>
						<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.planningConstraint }}</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div style="height: 400px; width: 100%; margin: 20px 0">
			<baogaomap
				v-if="coordinates.length > 0"
				:containerId="'map-container-3'"
				:zoom="13.3"
				:locadata="locadata"
				:coordinates="coordinates"
				:lat="lat"
			>
			</baogaomap>
		</div>
		<div class="center bold">估价对象位置</div>

		<p>对上述案例，我们从区域因素和个别因素两大方面进行了相应的调整，主要有以下调整因素：</p>
		<p class="underline bold">交易时间</p>
		<p>
			虽然我们已经尽量选取与价值时点接近的交易案例，但房地产市场在交易时点和价值时点已不完全相同，根据期间房地产市场的变化趋势，已对时间因素作出了相应调整。
		</p>
		<p class="underline bold">交易情况</p>
		<p>各可比实例均为正常交易，估价对象交易情况指数设为1,则各可比实例的交易情况指数均为1。</p>
		<p class="underline bold">商业繁华度</p>
		<p>通过比较可比实例与估价对象周边的商业氛围等做相应调整。</p>
		<p class="underline bold">交通便捷度</p>
		<p>通过考察距周边交通主干道、轨道交通、机场、火车站的距离、公交线路的数量等来考察可比实例与估价对象的优劣，并做出相应调整。</p>
		<p class="underline bold">基础设施完善度</p>
		<p>通过比较可比实例与估价对象周边的市政基础设施完善程度做相应调整。</p>
		<p class="underline bold">自然及人文环境</p>
		<p>通过比较可比实例与估价对象周边的自然景观、人文景观、商务办公氛围等做相应调整。</p>
		<p class="underline bold">公共服务设施状况</p>
		<p>通过比较可比实例与估价对象周边的商务设施及生活设施等的完备程度做相应调整。</p>
		<p class="underline bold">楼层</p>
		<p>对于商业物业，通常情况下，向上楼层越高，价格逐渐递减。为此，需要根据可比实例楼层情况进行相应的修正。可比实例均为首层商业，故不作调整。</p>
		<p class="underline bold">临路状况</p>
		<p>通过比较可比实例与估价对象所临道路情况做相应调整。</p>
		<p class="underline bold">商业类型</p>
		<p>通过比较可比实例与估价对象所在楼宇的商业类型做相应调整。</p>
		<p class="underline bold">建筑面积</p>
		<p>根据估价对象的建筑面积对各可比实例的建筑面积进行相应的修正，以反映面积大小对单价的影响。</p>
		<p class="underline bold">进深比</p>
		<p>通过比较可比实例与估价对象的进深状况做相应调整。</p>
		<p class="underline bold">配套设施设备</p>
		<p>通过对房屋空调系统、电梯、高科技通讯系统、保安系统等设施的完备程度、档次等进行对比分析，进行系数调整以反映孰优孰劣。</p>
		<p class="underline bold">内部装修</p>
		<p>通过对物业外立面、大堂、电梯厅、公共区域、内部的装修标准进行调整。</p>
		<p class="underline bold">层高</p>
		<p>通过比较可比实例与估价对象的层高高低做相应调整。</p>
		<p class="underline bold">楼龄及保养</p>
		<p>通过对房屋的使用年限、新旧程度、维护状况的考察，进行相应调整。</p>
		<p class="underline bold">使用率</p>
		<p>通过比较可比实例与估价对象的可使用建筑面积占比情况进行相应调整。</p>
		<p class="underline bold">土地剩余年限</p>
		<p>通过比较可比实例与估价对象土地的剩余使用年限长短进行相应调整。</p>
		<p class="underline bold">规划限制条件</p>
		<p>通过比较可比实例与估价对象规划限制条件(如容积率等)进行相应调整。</p>
		<p>根据估价对象与可比实例上述因素具体情况，编制可比因素修正系数表，详见下表：</p>
		<div style="width: 100%">
			<p class="center bold">可比因素修正</p>
			<table class="revisetable" style="width: 100%">
				<thead>
					<tr>
						<td valign="top" style="width: 28%" colspan="3" class>因素</td>
						<td valign="top" style="width: 24%" class>待估物业</td>
						<td valign="top" style="width: 24%" class>可比实例一</td>
						<td valign="top" style="width: 24%" colspan="2" class>可比实例二</td>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td valign="top" colspan="3" class>交易价格(元/平方米)</td>
						<td valign="top" class></td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.tradingPrice }}</td>
					</tr>
					<tr>
						<td valign="top" colspan="3" class>交易时间</td>
						<td valign="top" class>1.00</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.tradingHours }}</td>
					</tr>
					<tr>
						<td valign="top" colspan="3" class>交易情况</td>
						<td valign="top" class>1.00</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.transactionSituation }}</td>
					</tr>
					<tr>
						<td valign="top" rowspan="12" style="writing-mode: vertical-lr" class>房地产状况</td>
						<td valign="top" rowspan="7" style="writing-mode: vertical-lr" class>区位情况</td>
						<td valign="top" class>商业繁华度</td>
						<td valign="top" class>1.0</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.businessProsperity }}</td>
					</tr>
					<tr>
						<td valign="top" class>交通便捷度</td>
						<td valign="top" class>1.0</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.peripheralProsperity }}</td>
					</tr>
					<tr>
						<td valign="top" class>基础设施完善度</td>
						<td valign="top" class>1.00</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.facilityConvenient }}</td>
					</tr>
					<tr>
						<td valign="top" class>自然及人文环境</td>
						<td valign="top" class>1.00</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.humanEnvironment }}</td>
					</tr>
					<tr>
						<td valign="top" class>公共服务设施状况</td>
						<td valign="top" class>1.00</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.conditionFacilities }}</td>
					</tr>
					<tr>
						<td valign="top" class>楼层</td>
						<td valign="top" class>1.00</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.floor }}</td>
					</tr>
					<tr>
						<td valign="top" class>临路状况</td>
						<td valign="top" class>1.0</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.roadConditions }}</td>
					</tr>
					<tr>
						<td valign="top" rowspan="4" style="writing-mode: vertical-lr" class>实体情况</td>
						<td valign="top" class>建筑面积</td>
						<td valign="top" class>1.0</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.buildingArea }}</td>
					</tr>
					<tr>
						<td valign="top" class>配套设施设备</td>
						<td valign="top" class>1.0</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.ancillaryFacilities }}</td>
					</tr>
					<tr>
						<td valign="top" class>内部装修</td>
						<td valign="top" class>1.00</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.interiorDecoration }}</td>
					</tr>
					<tr>
						<td valign="top" class>楼龄及保养</td>
						<td valign="top" class>1.0</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.buildingAge }}</td>
					</tr>
					<tr>
						<td valign="top" rowspan="1" style="writing-mode: vertical-lr" class>权益情况</td>
						<td valign="top" class>土地剩余年限</td>
						<td valign="top" class>1.00</td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.remainingYears }}</td>
					</tr>
					<tr>
						<td valign="top" colspan="3" class>修正因素合计</td>
						<td valign="top" class></td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.modifiersTotal }}</td>
					</tr>
					<tr>
						<td valign="top" colspan="3" class>权重</td>
						<td valign="top" class></td>
						<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.weight }}</td>
					</tr>
					<tr>
						<td valign="top" colspan="3" class>评估单价取算数平均数 (元/平方米)</td>
						<td valign="top" colspan="3" class>{{ dataFiv?.unitPriceAvg }}</td>
					</tr>
				</tbody>
			</table>
		</div>
		<p>
			由于可比实例一、可比实例二、与估价对象业态相似，区域因素类似，故取两个比较价值的算术平均数作为估价结果{{ dataFiv?.unitPriceAvg }}元/平方米。
		</p>
		<p>综上，比较法计算估价对象总价为人民币{{ dataFiv.evaluateObjectsTotalPrice }}元，单价为人民币{{ dataFiv.unitPrice }}元/平方米。</p>
		<p class="sub_title_3">5.1.5.2估价测算过程之现金流折现法</p>
		<p>
			现金流量折现法(DCF)是预计估价对象未来的正常净收益(净现金流量),选用适当的资本化率将其折现到价值时点后累加，以此估价为估价对象的客观合理价格或价值的方法。
		</p>
		<p class="bold">5.1.5.2.1出租计划</p>
		<p>
			估价对象为经营性物业，可出租面积为{{
				dataOne?.lettingRate
			}}%。首先，对于已出租部分房地产，租赁期限内的租金采用租赁合同中约定(租约限制)的租金(即实际租金),租赁期满后，假设该部分物业会按照当时的市场租金租赁，且每次租赁租期长度为十二个月。
		</p>
		<p class="bold underline">租约限制</p>
		<p>
			根据产权人提供的《房屋租赁合同》及《项目情况说明》,估价对象总建筑面积为{{ dataOne?.buildingArea }}平方米，商业可出租面积{{
				dataThr?.leasableArea
			}}平方米，已出租面积为{{ dataThr?.leasedArea }}平方米，出租率约为{{ dataOne?.lettingRate }}%,已签约租户共计{{
				dataOne?.tenantTotal
			}}个。截止至价值时点{{ dataOne?.applicationTime }}，估价对象平均签约租金为{{ dataThr?.rent }}元/平方米/月(使用面积),当期年租金收入约{{
				dataThr?.currentAnnualRent
			}}元。
		</p>
		<p>租赁期内采用租约租金计算租赁收入，租赁期外按照市场租金水平计算租赁收入，空置部分均按照市场租金水平计算租赁收入。</p>
		<p class="bold underline">有效出租面积</p>
		<p>
			根据租赁合同约定，估价对象总建筑面积为{{ dataThr?.buildingArea }}平方米，总可出租面积为{{ dataThr?.leasableArea }}平方米，已出租面积为{{
				dataThr?.leasedArea
			}}平方米，于价值时点出租率{{ dataOne?.lettingRate }}%。
		</p>
		<p class="bold underline">出租率</p>
		<p>
			经参考戴德梁行提供的估价对象之《市场调研报告》,并结合我们对该商圈商业市场供需情况的研究，我们认为该商圈周边商业氛围成熟，长远来说估价对象{{
				dataOne?.lettingRate
			}}%出租率属于合理水平。
		</p>
		<p class="bold underline">租金增长率</p>
		<p>
			估价对象由{{ dataSix?.currentYearCnDate }}至{{
				dataSix?.tenYearAfterCnDate
			}}的市场租金的预测年度增长率，乃以戴德梁行提供之《市场调研报告》为依据，具体增长幅度如下表。
		</p>
		<table class="revisetable" style="width: 100%">
			<thead>
				<tr>
					<td valign="top" style="width: 28%" colspan="3" class>年份</td>
					<td valign="top" style="width: 24%" class>非主力店增长率</td>
					<td valign="top" style="width: 24%" class>主力店增长率</td>
					<td valign="top" style="width: 24%" colspan="2" class>备注</td>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td valign="top" colspan="3" class>第一年</td>
					<td valign="top" class></td>
					<td valign="top" class></td>
					<td valign="top" class>价值时点租金水平</td>
				</tr>
				<tr>
					<td valign="top" colspan="3" class>第二年至第十年</td>
					<td valign="top" class>{{ dataSix?.notMainRentalGrowth }}</td>
					<td valign="top" class>{{ dataSix?.mainRentalGrowth }}</td>
					<td valign="top" class></td>
				</tr>
			</tbody>
		</table>
		<p>
			{{ dataSix?.tenYearAfterCnDate }}至收益期届满的长期年度增长率为{{
				dataSix?.mainRentalGrowth
			}},乃根据类似商业物业的发展经验及该区域商业的市场状况综合分析得出。
		</p>
		<p class="bold">5.1.5.2.2项目损益分析年总收入</p>
		<p class="underline">收入</p>
		<p>■商业年租金收入</p>
		<p>详见5.1.5.2.1出租计划分析</p>
		<p>■物业管理收入</p>
		<p>
			根据产权人介绍及提供的《物业管理合同》及2024年物业费收入统计，估价对象目前由产权方自行管理，其物业管理费属由产权方向租户收取，目前估价对象的租户物业管理费为{{
				dataThr?.propertyFee
			}}元/平方米/月，2024年物业管理费拟收入约为{{ dataThr?.propertyFeePseudoRevenue }}元。
		</p>
		<p>■运营成本</p>
		<p>指业主对商业项目进行管理及运营所需支出的费用，一般按年租金收入的一定比例计取，本次估价取8.0%。详见4.6.2.1出租计划分析</p>
		<p class="bold">5.1.5.2.3收益年期</p>
		<p>
			根据产权人提供的资料显示，该宗地的土地使用权终止日期为2049年1月10日，于价值时点{{ dataOne?.applicationTime }}，其土地剩余使用年期为{{
				dataSix?.groundRemainingAge
			}}年；估价对象于{{ dataSix?.buildingAgeMaintenance }}年建成，钢混结构，钢混结构非生产用房最高经济耐用年限为60年，至价值时点{{
				dataThr?.year
			}}年，已使用约{{ dataSix?.useAge }}年，估价对象剩余经济耐用年限为{{ dataSix?.durableAge }}年；根孰短原则，估价对象的收益年期为{{
				dataSix?.groundRemainingAge
			}}年。
		</p>
		<p class="bold">5.1.5.2.4贴现率的求取</p>
		<p>
			贴现率是用以转换日后应付或应收货币金额至现值之回报率，理论上反映资本之机会成本。估价人员通过对青岛市商业市场的物业投资者所需回报率的分析，再结合估价对象所处商圈的市场状况、租金收入及租户组合等情况确定其贴现率，其理论上与净报酬率的关系为贴现率等于净报酬率与稳定期增长率之和。
		</p>
		<p>
			采用市场提取法求取估价对象的净报酬率。市场提取法是利用与估价对象房地产具有类似收益特征的可比实例房地产的价格、净收益等资料，选用相应的报酬资本化法公式，反求出净报酬率的方法。净报酬率的求取过程见下表（备注：1、毛报酬率=年收益/房产价格=(①×12)/房地产价格
			2、净报酬率=毛报酬率×(1-②)）。
		</p>
		<div style="width: 100%">
			<table class="revisetable" style="width: 100%">
				<thead>
					<tr>
						<td valign="top" style="width: 33.33%" class>项目</td>
						<td valign="top" style="width: 33.33%" class>实例一</td>
						<td valign="top" style="width: 33.33%" class>实例二</td>
					</tr>
				</thead>
				<tbody>
					<tr v-for="(item, index) in tiexiantable" :key="index">
						<td valign="top" class>{{ item.factor }}</td>
						<td valign="top" class>{{ item.comparable1 }}</td>
						<td valign="top" class>{{ item.comparable2 }}</td>
					</tr>
					<tr>
						<td valign="top" rowspan="2" class>备注</td>
						<td valign="top" colspan="2" class>1、毛报酬率=年收益/房产价格=(①×12)/房地产价格</td>
					</tr>
					<tr>
						<td valign="top" colspan="2" class>2、净报酬率=毛报酬率×(1-②)</td>
					</tr>
				</tbody>
			</table>
		</div>
		<p>
			本次估价取各实例毛报酬率的简单算术平均数作为估计对象的毛报酬率，即
			(6.4%+6.5%)÷2=6.45%。综合考虑计算结果，本次估价采取最终毛报酬率为6.45%。故本次估价最终的净报酬率为6.45%×(1-30%)=4.52%
		</p>
		<p>估价对象稳定期所采用之增长率为3.0%,乃根据类似商业物业的发展经验及该区域商业的市场状况综合分析得出。</p>
		<p>则贴现率=净报酬率+稳定期增长率=4.52%+3.0%=7.52%</p>
		<p class="bold">5.1.5.2.5现金流量折现法计算结果</p>
		<p>
			通过现金流量折现法计算得出，「估价对象」于价值时点的房地产总价为{{ dataSix?.realEstateTotalPrice }}元，单价为{{ dataSix?.unitPrice }}元/平方米
		</p>
		<div class="sub_title_3">5.1.5.3估价结果确定</div>
		<p>
			我们采用了比较法和现金流量折现法对估价对象的房地产公开市场价格进行了测算，采用现金流量折现法首先确定出一连串定期现金流量，并就该一连串现金流量采用适当贴现率，以制订关于估价对象租金收入现值之指标，该方法用于衡量于假设投资年期内之租金及资本增长，让投资者或业主可对物业可能带来之长期回报作出估价。比较法主要是通过选取实际交易案例，进行各项因素比较后，所得出的价格水平，反映了该区域同类房地产的市场价格。
		</p>
		<p>
			根据估价经验，综合分析影响房地产价格的因素，并结合估价对象的具体情况，采用现金流量折现法作为本次房地产价值估价的基本方法，对其测算结果取70%的权重；并采用比较法作为本次房地产价值估价的辅助方法，对其采选结果取30%的权重，两种方法测算结果加权平均作为估价对象的最终估价结果。于{{
				dataOne?.applicationTime
			}}，估价对象之市场价值为人民币{{ dataSix?.evaluateDetails?.marketPrice }}元。
		</p>
		<p>明细如下：</p>
		<div style="width: 100%">
			<table class="revisetable" style="width: 100%">
				<thead>
					<tr>
						<td valign="top" style="width: 15%" class>用途</td>
						<td valign="top" style="width: 15%" class>建筑面积(平方米)</td>
						<td valign="top" style="width: 15%" class>比较法(元)(权重30%)</td>
						<td valign="top" style="width: 25%" class>现金流一折现法(元)(权重70%)</td>
						<td valign="top" style="width: 15%" class>市场价值(元)</td>
						<td valign="top" style="width: 15%" class>单价(元/平方米)</td>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td valign="top" class>商业</td>
						<td valign="top" class>{{ restable.area }}</td>
						<td valign="top" class>{{ restable.comparativeLaw }}</td>
						<td valign="top" class>{{ restable.discountedCashFlow }}</td>
						<td valign="top" class>{{ restable.marketPrice }}</td>
						<td valign="top" class>{{ restable.price }}</td>
					</tr>
				</tbody>
			</table>
		</div>
		<p class="sub_title_2">5.1.6估价结果确定</p>
		<p>
			中国{{ dataOne?.buildingAddress }}{{ dataOne?.buildingName }}地下四层至地上十层商业裙楼房地产，总建筑面积为{{
				dataOne?.evaluateDetails?.area
			}}平方米，于{{ dataOne?.applicationTime }}之房地产市场价值为人民币{{ dataSix?.realEstateTotalPrice }}元,楼面单价为{{
				dataSix?.unitPrice
			}}元/平方米。
		</p>
	</div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { valueOne, valueTwo, valueThr, valueFor, valueFiv, valueSix } from '@/api/baogao';
import baogaomap from '@/Mapbaogao.vue';
import tenantMatching from '../../components/tenantMatching.vue';
import tradingArea from '@/assets/images/tradingArea/Qingdao.png';
const props = defineProps({
	id: {
		type: String,
		default: null,
		required: true,
	},
});
const coordinates = ref([]);
const lat = ref('');
const locadata = ref([]);
const valuetable = ref([]);
const router = useRoute();
const dataOne = ref({});
const kebitable = ref([]);
const dataTwo = ref({});
const dataFor = ref({});
const dataFiv = ref({});
const dataSix = ref({});
const kejiao = ref();
const yiliao = ref();
const jinrong = ref();
const xiuxian = ref();
const buslu = ref('');
const busname = ref('');
const mainroad = ref('');
const dataThr = ref({});
const fenxiname = ref('');
const fenxibilie = ref('');
const zuhutable = ref([]);
const revisetable = ref([]);
const tiexiantable = ref([]);
const restable = ref({});
const shangyetable = ref([]);
const isImage = (value) => {
	return value && (value.endsWith('.jpg') || value.endsWith('.JPG') || value.endsWith('.jpeg') || value.endsWith('.png') || value.endsWith('.gif'));
};
async function getdata() {
	try {
		const [res, restwo, resthr, resfor, resfiv, ressix] = await Promise.all([
			valueOne({ buildingId: props.id }),
			valueTwo({ buildingId: props.id }),
			valueThr({ buildingId: props.id }),
			valueFor({ buildingId: props.id }),
			valueFiv({ buildingId: props.id }),
			valueSix({ buildingId: props.id }),
		]);

		if (res.code === 200) {
			dataOne.value = res.result;
			console.log('🚀 ~ getdata ~ dataOne.value:', dataOne.value);
			restable.value = res.result.evaluateDetails;
			if (res.result && res.result.evaluateDetails) {
				valuetable.value = [res.result.evaluateDetails];
				valuetable.value[0].yongtu = '商业';
				lat.value = res.result.buildingCoordinate;
				locadata.value = [];
				res.result.bussinessScopre.forEach((item) => {
					locadata.value.push([Number(item.lng), Number(item.lat)]);
				});
			} else {
				console.error('evaluateDetails 不存在或未定义');
			}
		}

		if (restwo.code === 200) {
			dataTwo.value = restwo.result;
			mainroad.value = restwo.result.nearbyMainRoad.map((item) => item).join(',');
			buslu.value = restwo.result.busStopsInfo.busNumber.map((item) => item).join(',');
			busname.value = restwo.result.busStopsInfo.busStopName.map((item) => item).join(',');
			restwo.result.buildingSurroundingSupporting.forEach((item) => {
				if (item.type == '科教文化服务') {
					kejiao.value = item.pois.map((item) => item.name).join(',');
				}
				if (item.type == '医疗保健服务') {
					yiliao.value = item.pois.map((item) => item.name).join(',');
				}
				if (item.type == '金融保险服务') {
					jinrong.value = item.pois.map((item) => item.name).join(',');
				}
				if (item.type == '体育休闲服务') {
					xiuxian.value = item.pois.map((item) => item.name).join(',');
				}
			});
		}

		if (resthr.code === 200) {
			dataThr.value = resthr.result;
			console.log('🚀 ~ getdata ~ dataThr.value:', dataThr.value);
			shangyetable.value = resthr.result.mainBusinessSituationList;
			if (resthr.result.tenantIndustryRatio) {
				fenxiname.value = resthr?.result?.tenantIndustryRatio?.map((item) => item.commercialForm).join(',');
			}
			fenxibilie.value = resthr.result.tenantIndustryRatio.map((item) => item.merchantPercentage).join('%,');
			zuhutable.value = resthr.result.tenantIndustryRatio;
		}

		if (resfor.code === 200) {
			dataFor.value = resfor.result;
			kebitable.value = resfor.result.comparableInstancesList;
			coordinates.value = resfor.result.coordinate;
		}

		if (resfiv.code === 200) {
			dataFiv.value = resfiv.result;
			revisetable.value = resfiv.result.comparableFactorCorrectionsList;
		}

		if (ressix.code === 200) {
			dataSix.value = ressix.result;
			tiexiantable.value = ressix.result.evaluateNetInterestRateList;
			tiexiantable.value.unshift({
				buildingName: '名称',
				tradingHour: '交易日期',
				rent: '租金价格(元/平方米/月)',
				realEstatePrice: '房地产价格(元/平方米)',
				grossProfitMargin: '毛报酬率',
				yearOperatingRate: '年运营费率',
				netInterestRate: '净报酬率',
			});
			const keys = Object.keys(tiexiantable.value[0]);
			tiexiantable.value = keys.map((key) => {
				return {
					factor: tiexiantable.value[0][key],
					comparable1: tiexiantable.value?.[1]?.[key],
					comparable2: tiexiantable.value?.[2]?.[key],
				};
			});
		}
	} catch (error) {
		console.error('请求失败:', error);
	}
}

getdata();
const url = ref('https://static.biaobiaozhun.com/');
</script>
<style lang="less" scoped>
.matching {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-content: center;
	flex-wrap: wrap;
	.matching-box {
		width: 720px;
	}
	.shangyetable {
		width: 1050px;
	}
}
.table_header_bg {
	:deep(.el-table__header-wrapper th) {
		background-color: rgb(62, 90, 140);
		color: #fff;
	}
}
.MsoNormalTable,
.revisetable {
	border-top: 1px solid #333;
	border-left: 1px solid #333;
	border-spacing: 0;
	background-color: #fff;
	width: 100%;
	thead {
		background-color: rgb(62, 90, 140);
		color: #fff;
		font-weight: 700;
	}
	td {
		border-bottom: 1px solid #333;
		border-right: 1px solid #333;
		font-size: 13px;
		padding: 5px;
		text-align: center;
	}
}
.AdminSketch {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-content: center;
	flex-wrap: wrap;
	.el-image {
		width: 1082px;
		height: 724px;
		margin-bottom: 10px;
	}
}
.tit_basic {
	padding-left: 20px;
}
.main {
	width: 100%;
	height: 100%;
	background-color: #fff;
	overflow: hidden;
	box-sizing: border-box;
}
.ident {
	text-indent: 2em;
}
.ident1 {
	text-indent: 3em;
}
.pl-2 {
	padding-left: 2em;
}
.pl-3 {
	padding-left: 3em;
}
.center {
	text-align: center;
}
.title {
	font-size: 24px;
	font-weight: 600;
	margin: 16px 0;
}
.sub_title_1 {
	font-size: 22px;
	font-weight: 600;
	margin: 16px 0;
}
.sub_title_2 {
	font-size: 20px;
	font-weight: 600;
	margin: 16px 0;
}
.sub_title_3 {
	font-size: 18px;
	font-weight: 600;
	margin: 16px 0;
}
.bold {
	font-weight: 600;
}
.underline {
	text-decoration: underline; /* 添加下划线 */
}
.box {
	display: grid;
	grid-template-columns: repeat(3, 1fr); /* 创建3列，每列宽度相等 */
	grid-template-rows: repeat(3, 1fr); /* 创建3行，每行高度相等 */
	.item {
		padding: 10px; /* 设置网格项的内边距 */
		text-align: center; /* 设置文本居中 */
		.el-image {
			width: 100%;
			height: 200px;
			// margin-bottom: 20px;
		}
	}
}
.top {
	width: 100%;
	height: 100%;
	background-color: #fff;
	padding: 20px 30px;
	box-sizing: border-box;
	.el-image {
		width: 100%;
		height: 100%;
	}
}
// @media screen and (max-width: 1280px) {
// 	.main {
// 		padding: 20px 200px;
// 	}
// 	.AdminSketch {
// 		width: 100%;
// 		display: flex;
// 		flex-direction: column;
// 		align-content: center;
// 		flex-wrap: wrap;
// 		.el-image {
// 			width: 100%;
// 			height: 300px;
// 			margin-bottom: 10px;
// 		}
// 		.shangyetable {
// 			width: 100%;
// 		}
// 	}
// 	.matching {
// 		width: 100%;
// 		display: flex;
// 		flex-direction: column;
// 		align-content: center;
// 		flex-wrap: wrap;
// 		.matching-box {
// 			width: 100%;
// 		}
// 	}
// }
// @media screen and (max-width: 1024px) {
// 	.main {
// 		padding: 20px 150px;
// 	}
// 	.AdminSketch {
// 		width: 100%;
// 		display: flex;
// 		flex-direction: column;
// 		align-content: center;
// 		flex-wrap: wrap;
// 		.el-image {
// 			width: 100%;
// 			height: 300px;
// 			margin-bottom: 10px;
// 		}
// 		.shangyetable {
// 			width: 100%;
// 		}
// 	}
// 	.matching {
// 		width: 100%;
// 		display: flex;
// 		flex-direction: column;
// 		align-content: center;
// 		flex-wrap: wrap;
// 		.matching-box {
// 			width: 100%;
// 		}
// 	}
// }
// @media screen and (max-width: 768px) {
// 	.main {
// 		padding: 20px 100px;
// 	}
// }
// @media screen and (max-width: 640px) {
// 	.main {
// 		padding: 20px 20px;
// 	}
// }
</style>
