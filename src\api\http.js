import axios from 'axios';
import { ElMessage, ElLoading } from 'element-plus';
import router from '../router';
import { vuexStore } from '../store';
axios.defaults.headers['Content-Type'] = 'application/json;charset=UTF-8';
axios.defaults.headers['Cache-Control'] = 'no-cache';
axios.defaults.headers['Pragma'] = 'no-cache';
axios.defaults.headers['X-Access-Token'] = '123';
let host = import.meta.env.VITE_BASE_URL_API;
import { emitter } from '../utils/eventBus';

var loading;
const http = axios.create({
	baseURL: host,
	timeout: 10000,
	withCredentials: true,
	crossDomain: true,
});

// 请求拦截器
http.interceptors.request.use(
	(config) => {
		if (config.url.indexOf('/sm') > -1) {
			config.baseURL = import.meta.env.VITE_BASE_URL_SM;
		}
		if (localStorage.getItem('token')) {
			config.headers.Authorization = localStorage.getItem('token');
		}
		if (config.loading) {
			loading = ElLoading.service({
				fullscreen: true,
				lock: true,
				// text: 'Loading',
				// spinner: 'el-icon-loading',
				background: 'rgba(255,255,255, 0.7)',
			});
		}
		loading && loading.close();
		return config;
	},
	(err) => {
		return Promise.reject(err);
	}
);

// 消息防抖
const messageDebounce = {
	lastMessage: '',
	lastTimestamp: 0,
	timeout: 3000, // 3 seconds
};

// 响应拦截器
http.interceptors.response.use(
	(response) => {
		if (response.data?.code === 401) {
			vuexStore.commit('handleNewUserCoupon', false); // 新用户专享优惠券
			router?.push('/login');
			//清空vuex数据
			vuexStore.dispatch('clearData');
		}
		if (response.data?.code === 403) {
			emitter.emit('updateMessage', response.data);
		}
		if (response.data.code !== 200 && response.data.code !== 403) {
			//接口返回错误消息防抖
			const errorMessage = response.data.msg;
			const now = Date.now();
			if (errorMessage === messageDebounce.lastMessage && now - messageDebounce.lastTimestamp < messageDebounce.timeout) {
				return response.data;
			}
			messageDebounce.lastMessage = errorMessage;
			messageDebounce.lastTimestamp = now;
			if (response.config.url !== '/sm/v1/sytinvoicerecord/queryByOrderId') {
				ElMessage.warning(errorMessage);
			}
			return response.data;
		} else {
			return response.data;
		}
	},
	(error) => {
		return Promise.reject(error);
	}
);
export const fetch = http;
