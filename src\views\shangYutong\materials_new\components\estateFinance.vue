<template>
	<div style="padding: 16px 16px 0px 0px;">
		<component :is="componentNames" />
	</div>
</template>

<script setup>
import { ref } from 'vue';
// import market1 from '../../financial/components/market.vue';
import standard1 from '../../financial/components/standard.vue';

const routes = [
	// {
	// 	name: '金融市场',
	// 	componentName: market1,
	// },
	{
		name: '标准化产品',
		componentName: standard1,
	},
];
const componentNames = ref(standard1);
const activeName = ref('标准化产品');

const handleTabClick = (item) => {
	componentNames.value = item.componentName;
	activeName.value = item.name;
};
</script>

<style lang="scss" scoped>
.tab_box {
	width: 100%;
	height: 56px;
	box-sizing: border-box;
	font-size: 14px;
	font-weight: 600;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	background-color: rgba(255, 255, 255, 1);

	.title {
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		margin-right: 15px;
		color: #1a1a1a;
		padding: 0 15px;
	}

	.tab {
		height: 56px;
		padding: 0 14px;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		cursor: pointer;

		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		color: #666666;
	}

	.tabAct {
		height: 56px;
		font-size: 14px;
		font-weight: 700;
		line-height: 22px;
		color: #1a1a1a;

		&::after {
			content: '';
			width: 24px;
			height: 3px;
			position: absolute;
			bottom: 0;
			background-color: rgba(3, 93, 255, 1);
		}
	}
}
</style>
