<template>
	<div class="body_box">
		<!-- <el-button  round @click="back(router.push('home'))" style="position: absolute;top: 20px;left: 20px;"> 返回首页</el-button> -->
		<div class="title_box">
			<h1>从强大的数据分析中获得见解</h1>
			<p class="title_p">数据涵盖了全部的商业地产信息，您可以立即访问相关数据</p>
		</div>
		<div class="main">
			<el-card class="card" v-for="item in hupoData" :key="item">
				<div class="card-content">
					<img style="width: 100px" src="../../assets/aa.jpg" />
					<p class="title">{{ item.name }}</p>
					<p class="description">{{ item.mk }}</p>
					<el-button type="primary" round @click="toDetail(item.name)">详细说说</el-button>
				</div>
			</el-card>
			<router-virw />
		</div>
	</div>
</template>

<script setup>
import { ref,onMounted } from 'vue';
import http from '@/utils/http'; 
import { useRouter } from 'vue-router';
const hupoData = ref([])
const router = useRouter();
const navigateTo = (to) => {
	router.push(to);

};
onMounted(() => {
	console.log(123);
	describe();
	// toDetail()
});
const describe = async() => {
	// console.log(1);
	const res = await http.get("gis/a_tg.do?act=getAmberInformationTitleInfo");
	console.log('琥珀',res);
	hupoData.value = res.list
}
const toDetail = (name) => {
    console.log(name);
	if(name == "市场分析"){
		router.push({path:'/market'})
	}else if(name == "交易材料"){
		router.push({path:'/trading'})
	}else if(name == "琥珀新闻"){
		router.push({path:'/news'})
	}else if(name == "地产金融"){
		router.push({path:'/finance'})
	}
}
</script>

<style scoped>
.body_box {
	width: 100%;
	height: 100%;
	/* background-color: #f5f5f5; */
}
.main {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	/* margin-top: 0px; */
}
.card :deep(.el-card__body) {
	display: flex;
}
.card {
	width: 640px;
	margin: 20px;
}
.card-content {
	padding: 20px;
}

.title {
	font-size: 18px;
	font-weight: bold;
}

.description {
	margin-top: 10px;
	color: #888;
}

.title_box {
	margin: 60px auto;
	width: 500px;
}
h1 {
	margin-bottom: 30px;
}
.title_p {
	color: #888;
	
}
</style>
