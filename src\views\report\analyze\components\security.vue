<template>
	<div class="security_container">
		<div class="title">六、证券化可行性分析</div>
		<div class="sub_title_1">6.1类REITs融资方案</div>
		<div class="sub_title_2">6.1.1项目背景介绍</div>
		<div class="sub_title_3">6.1.1.1产权人股份简介</div>
		<div style="padding: 20px 0 50px 0;">(用户填写)</div>
		<div class="sub_title_3">6.1.1.2产权人经营情况</div>
		<div style="padding: 20px 0 50px 0;">(用户填写)</div>
		<div class="sub_title_2">6.1.2发行利率估算</div>
		<div class="sub_title_3">6.1.2.1ABS定价分析</div>
		<img src="@/assets/images/shangauto/security6.1.2.1.png"></img>
		<div class="sub_title_3">6.1.2.2市场因素——利率环境</div>
		<el-image :src="security6122"></el-image>
		<div class="sub_title_3">6.1.2.3风险补偿水平——以三年期AAA级ABS为例</div>
		<el-image :src="security6123"></el-image>
		<p>从左图可以看出，3年期债项AAA级ABS产品的发行利率略低于同期发行的非公开公司债券。</p>
		<p>
			从右图来看，3年期主体AAA级PPN发行利率高于非公开公司债约20-30BP；二者发行利率均高于公开公司债，主要因为当前企业ABS仅能非公开发行，故发行利率含有流动性溢价，不过随着市场认可度以及理解力进一步提升，企业ABS与公募债券产品的利差已经在不断缩小。
		</p>
		<p class="bold">数据来源wind资讯</p>
		<div class="sub_title_3">6.1.2.4本次类REITs项目定价分析</div>
		<div class="security6124">
			<p style="position: absolute; bottom: 10%; left: 20%; width: 28%">
				3年期主体AAA级PPN发行利率高于非公开公司债20-30BP， 而3年期债项AAA级ABS产品的发行利率略低于同期发行的非公开公司债券。
			</p>
			<p style="position: absolute; top: 3%; left: 50%; width: 28%">
				综合以上分析，并对证券现金流的转付结构、加权平均期限等因素进行微调，初步估计本次ABS优先级三年期产品<span>发行利率区间为</span
				>{{ lilv.interestRateInterval }}。
			</p>
			<p style="position: absolute; bottom: 20%; right: 3%; width: 28%">
				根据利率期限相匹配的原则，预计本次ABS优先级资产支持证券的<span>发行利率为</span>优先A1类{{ lilv.interestRateAAA }}%,优先A2A3类{{
					lilv.interestRateAAPlus
				}}%-{{ lilv.interestRateAA }}%，该利率为根据目前市场情况及上述因素综合预期水平，最终实际发行利率取决于ABS债项评级及发行时市场环境。
			</p>
		</div>
		<div class="sub_title_2">6.1.3产品分层测算</div>
		<div class="sub_title_3">6.1.3.1拟入池标的资产现金流情况</div>
		<div style="display: flex; gap: 24px">
			<table class="base_table" style="height: 190px" border="1" cellpadding="8" cellspacing="0">
				<thead>
					<tr>
						<th rowspan="2"></th>
						<th colspan="4">资产业态</th>
					</tr>
					<tr>
						<th>面积</th>
						<th>租金</th>
						<th>管理费</th>
						<th>合计</th>
					</tr>
				</thead>
				<tbody>
					<tr style="text-align: center" v-for="(item, index) in fencen.rentalSaleFees" :key="index">
						<td>{{ feeTypeLabel(item.feeType) }}</td>
						<td>{{ item.area }}</td>
						<td>{{ item.rent }}</td>
						<td>{{ item.manageFee }}</td>
						<td>{{ item.totalFee }}</td>
					</tr>
				</tbody>
			</table>
			<div style="width: 40%">
				<img style="width: 100%; height: 190px" :src="url + basic?.buildingImgUrls?.split(',')[0]" alt="" />
			</div>
		</div>
		<div style="display: flex; gap: 24px">
			<div style="width: 60%">
				<table class="base_table" border="1" cellpadding="8" cellspacing="0">
					<thead>
						<tr>
							<th colspan="4">车位</th>
						</tr>
						<tr>
							<th>个数</th>
							<th>租金</th>
							<th>管理费</th>
							<th>合计</th>
						</tr>
					</thead>
					<tbody>
						<tr style="text-align: center">
							<td>{{ fencen.parkingNum }}</td>
							<td>{{ fencen.parkingFee }}</td>
							<td>{{ fencen.mangeFee }}</td>
							<td>{{ fencen.parkingTotalFee }}</td>
						</tr>
					</tbody>
				</table>
				<table class="base_table" border="1" cellpadding="8" cellspacing="0">
					<thead>
						<tr>
							<th colspan="4">广告费</th>
						</tr>
					</thead>
					<tbody>
						<tr style="text-align: center">
							<td colspan="4">{{ fencen.adFee }}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div style="width: 40%">
				<img style="width: 100%; height: 190px" :src="url + basic?.buildingImgUrls?.split(',')[1]" alt="" />
			</div>
		</div>
		<table class="base_table" border="1" cellpadding="8" cellspacing="0">
			<tbody>
				<tr>
					<th rowspan="2" style="width: 18%">{{ fencen.forecastYear }}年预测<br />年收入</th>
					<th>租金</th>
					<th>管理费</th>
					<th>其他</th>
					<th>合计</th>
				</tr>
				<tr style="text-align: center">
					<td>{{ fencen.forecastRent }}</td>
					<td>{{ fencen.forecastManageFee }}</td>
					<td>{{ fencen.forecastOtherFee }}</td>
					<td>{{ fencen.forecastTotalFee }}</td>
				</tr>
			</tbody>
		</table>
		<div class="sub_title_3">6.1.3.2{{ basic.buildingUniqueCode_dictText }}未来现金流预测</div>
		<div>
			<p>
				<span>{{ basic.buildingUniqueCode_dictText }}</span
				>未来现金流主要包括商铺、写字楼租金收入和物业管理费收入，地下停车场停车费收入，广告费收入。租金收入构成主要有两部分：一部分是直接向商户收取的租金，另一部分是以<span
					>{{ basic.buildingUniqueCode_dictText }}</span
				>名义对外销售进行销售分成收取的租金。
			</p>
			<p>
				预计{{ basic.buildingUniqueCode_dictText }}在 {{ fencen.futureForecastRemainingYears }}期间租金收入现金流入年增长率分别为{{
					fencen.futureForecastGrowthRate
				}}。
			</p>
			<p>
				预计<span> {{ fencen.futureForecastEstimatedYears }}</span
				>将实现现金流<span style="background-color: yellow"> {{ fencen.futureForecastFutureCashFlows }}</span
				>亿元。
			</p>
		</div>
		<div style="display: flex">
			<img style="width: 50%; height: 270px; padding: 10px" :src="url + basic?.buildingImgUrls?.split(',')[0]" alt="" />
			<img style="width: 50%; height: 270px; padding: 10px" :src="url + basic?.buildingImgUrls?.split(',')[1]" alt="" />
		</div>
		<table class="base_table" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th>年份</th>
					<th>资产租金收入</th>
					<th>资产管理费收入</th>
					<th>停车费收入</th>
					<th>广告费收入</th>
					<th>现金流入合计</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(item, index) in fencen.futureCashFlows" :key="index">
					<td>{{ item.year }}</td>
					<td>{{ item.rentIncome }}</td>
					<td>{{ item.manageFeeRevenue }}</td>
					<td>{{ item.parkingFeeRevenue }}</td>
					<td>{{ item.adFeeRevenue }}</td>
					<td>{{ item.totalCashInflows }}</td>
				</tr>
				<tr style="background-color: #fff">
					<td style="padding: 10px">合计</td>

					<td>{{ totals.rentIncome }}</td>
					<td>{{ totals.manageFeeRevenue }}</td>
					<td>{{ totals.parkingFeeRevenue }}</td>
					<td>{{ totals.adFeeRevenue }}</td>
					<td>{{ totals.totalCashInflows }}</td>
				</tr>
			</tbody>
		</table>
		<div class="sub_title_2">6.1.4交易结构安排</div>
		<div class="sub_title_3">6.1.4.1发行方案</div>
		<div style="display: flex; flex-wrap: wrap" class="cus_table">
			<p class="left">产品名称</p>
			<p class="right">
				<strong>{{ basic.buildingUniqueCode_dictText }}资产支持专项计划</strong>
			</p>
			<p class="left">发行总额</p>
			<p class="right">
				<span style="border: none">{{ arrangement?.issuanceScheme?.totalAmountIssued }}亿元</span>
			</p>
			<p class="left">证券分层</p>
			<div class="right" style="display: flex; justify-content: space-around">
				<span class="">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.stratified }} </span>
				<span class="">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.stratified }} </span>
			</div>
			<p class="left">规模</p>
			<div class="right" style="display: flex; justify-content: space-around">
				<span>{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.scale }} </span>
				<span>{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.scale }} </span>
			</div>
			<p class="left">期限</p>
			<div class="right" style="display: flex; justify-content: space-around">
				<span class="">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.term }} </span>
				<span class="">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.term }} </span>
			</div>
			<p class="left">预期评级</p>
			<p class="right" style="display: flex; justify-content: space-around">
				<span>{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.expectedRating }} </span
				><span>{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.expectedRating }} </span>
			</p>
			<p class="left">预期收益率</p>
			<div class="right" style="display: flex; justify-content: space-around">
				<span class="">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[0]?.expectedIncomeRate }} </span>
				<span class="">{{ arrangement?.issuanceScheme?.issuanceSchemeList?.[1]?.expectedIncomeRate }} </span>
			</div>
			<p class="left">信用增级方式</p>
			<p class="right" style="text-align: left">{{ arrangement?.issuanceScheme?.creditEnhancement }}</p>
			<p class="left">期限结构</p>
			<div class="right" style="text-align: left">
				{{ arrangement?.issuanceScheme?.termStructure }}
			</div>
			<p class="left">基础资产</p>
			<p class="right" style="text-align: left">{{ arrangement?.issuanceScheme?.underlyingAssets }}</p>
			<p class="left">募集资金用途</p>
			<p class="right" style="text-align: left">{{ arrangement?.issuanceScheme?.useProceeds }}</p>
			<p class="left">还本付息方式</p>
			<p class="right" style="text-align: left">{{ arrangement?.issuanceScheme?.interestPaymentMethod }}</p>
		</div>
		<div class="sub_title_3">6.1.4.2交易结构</div>
		<div style="display: flex">
			<div class="jiegou_left" style="width: 65%">
				<span class="bg17-abs-1">{{ basic.buildingUniqueCode_dictText }}抵押贷款资产支持专项计划 </span>
				<span class="bg17-abs-2">{{ backdata?.assets?.propertyOwner }}提供担保 </span>
			</div>
			<div style="width: 35%; margin-top: 20px">
				<div class="minjiacu" style="width: 40%; border: 1px dashed #18a3f2">
					<div class="vertical_level_center"><img style="width: 60%" src="@/assets/images/zhengquan/solid-arrow.jpg" alt="" />行为</div>
					<div class="vertical_level_center"><img style="width: 60%" src="@/assets/images/zhengquan/dashed-arrow.jpg" alt="" />资金</div>
				</div>
				<p style="font-size: 22px; border: 1px solid #168ea6; padding: 5px">资管计划单层模式结构介绍</p>
				<p>
					1.原始权益人发放借款给融资人形成债权，物业抵押/资产收益权质押给资产支持专项计划，原始权益人与专项资产管理计划签订《基础资产转让协议》，将资产收益权转让给专项资产管理计划，并提供相应的增信措施(权利维持费&差额补足承诺&回购)；
				</p>
				<p>2.计划管理人设立专项资产管理计划，与投资人签订协议，募集合格投资者资金，转让专项资产管理计划份额；</p>
				<p>3.中证登机构登记托管合格投资者份额，托营银行保管相应合同，到期分配收益。</p>
			</div>
		</div>
		<div class="sub_title_3">6.1.4.3税务处理</div>
		<p class="bold">环节一：资产原持有人以目标不动产出资新设特殊目的公司（SPV）</p>
		<table class="base_table" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th>征税主体</th>
					<th>税种及税率</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td class="sw_td_wrap">
						<div class="title">资产原持有人</div>
						<div class="desc">(以房屋权属作价投资，入股视同土地使用权转让、房屋买卖或者房屋赠与征税)</div>
					</td>
					<td>
						<p>【企业所得税】：25%</p>
						<p>
							【土地增值税】：税率30%-60%(根据规定，单位在改制重组时，对其将房屋权属转移、变更到被投资企业，暂不征土地增值税，但该条规定仅适用于资产原持有人及新设项目公司均为非房地产开发企业)
						</p>
						<p>【增值税】：一般纳税人增值税11%/征收率5%，小规模纳税人征收率5%【城建税、教育费附加】：增值税7%/3%/2%</p>
						<p>【印花税】：0.05%</p>
					</td>
				</tr>
				<tr>
					<td class="sw_td_wrap">
						<div class="title">SPV公司</div>
						<div class="desc">(项目公司)</div>
					</td>
					<td>
						<p>【印花税]】：0.05%</p>
						<p>
							【契税】：同一投资主体内部所属企业之间土地、房屋权属的划转，包括母公司与其全资子公司之间，同一公司所属全资子公司之间的土地及房屋权属的划转，免征契税
						</p>
					</td>
				</tr>
			</tbody>
		</table>
		<p class="bold">环节二：资产持有人将SPV股权转让给私募房地产投资基金</p>
		<table class="base_table" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th>征税主体</th>
					<th>税种及税率</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<th>资产原持有人</th>
					<td>【企业所得税】：25%(就SPV股权转让所得征收)【印花税】：0.05%</td>
				</tr>
				<tr>
					<th>私募房地产投资基金</th>
					<td>【印花税】：0.05%</td>
				</tr>
			</tbody>
		</table>
		<p class="bold">环节三：私募房地产投资基金份额证券化</p>
		<table class="base_table" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th>子环节</th>
					<th>是否设计增量税负</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>对REITs进行证券化的过程(认购人取得资产支持证券，REITs将基金份额转让至专项计划)</td>
					<td>不涉及增量税负，与一般ABS无异</td>
				</tr>
				<tr>
					<td>
						资产支持专项计划存续期间(项目公司SPV取得租金收益、向私募房地产投资基金分配股息、私募房地产投资基金向专项计划分红、专项计划向投资者分配收益)
					</td>
					<td>不涉及增量税负，与一般ABS无异</td>
				</tr>
			</tbody>
		</table>
		<p class="bold">环节四：退出环节，私募房地产投资基金处置项目公司股权</p>
		<table class="base_table" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th>征税主体</th>
					<th>税种及税率</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<th>私募房地产投资基金</th>
					<td>不涉及增量税负</td>
				</tr>
				<tr>
					<th>投资人</th>
					<td>投资收益缴纳增值税和所得税</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>
<script setup>
import { useRoute } from 'vue-router';
import { ref, computed } from 'vue';
import {
	securitybaisc,
	securityinterestRateEstimate,
	securitybaiscCalculation,
	securityVenueProcess,
	securityBackground,
	securityArrangement,
} from '@/api/baogao';
import security6122 from '@/assets/images/shangauto/security6.1.2.2.png';
import security6123 from '@/assets/images/shangauto/security6.1.2.3.png';

const props = defineProps({
	id: {
		type: String,
		default: null,
		required: true,
	},
});

const url = ref('https://static.biaobiaozhun.com/');
const basic = ref({});
const router = useRoute();
const lilv = ref({});
const fencen = ref({ futureCashFlows: [] });
const venue = ref({});
const backdata = ref({});
const arrangement = ref({});
const totals = computed(() => {
	let rentIncome = 0;
	let manageFeeRevenue = 0;
	let parkingFeeRevenue = 0;
	let adFeeRevenue = 0;
	let totalCashInflows = 0;
	if (fencen?.value?.futureCashFlows) {
		fencen.value.futureCashFlows.forEach((flow) => {
			rentIncome += Number(flow.rentIncome.replace(/,/g, '')) || 0;
			manageFeeRevenue += Number(flow.manageFeeRevenue.replace(/,/g, '')) || 0;
			parkingFeeRevenue += Number(flow.parkingFeeRevenue.replace(/,/g, '')) || 0;
			adFeeRevenue += Number(flow.adFeeRevenue.replace(/,/g, '')) || 0;
			totalCashInflows += Number(flow.totalCashInflows.replace(/,/g, '')) || 0;
		});
	}
	return {
		rentIncome: rentIncome.toLocaleString(),
		manageFeeRevenue: manageFeeRevenue.toLocaleString(),
		parkingFeeRevenue: parkingFeeRevenue.toLocaleString(),
		adFeeRevenue: adFeeRevenue.toLocaleString(),
		totalCashInflows: totalCashInflows.toLocaleString(),
	};
});
async function getbasic() {
	try {
		const [res, lilvres, fenceres, venueProcess, backres, arrangementres] = await Promise.all([
			securitybaisc({ buildingId: props.id }),
			securityinterestRateEstimate({ buildingId: props.id }),
			securitybaiscCalculation({ buildingId: props.id }),
			securityVenueProcess({ buildingId: props.id }),
			securityBackground({ buildingId: props.id }),
			securityArrangement({ buildingId: props.id }),
		]);

		if (res.code == 200) {
			basic.value = res.result;
		}

		if (lilvres.code == 200) {
			lilv.value = lilvres.result;
		}

		if (fenceres.code == 200) {
			fencen.value = fenceres.result;
		}

		if (venueProcess.code == 200) {
			venue.value = venueProcess.result;
		}

		if (backres.code == 200) {
			backdata.value = backres.result;
		}

		if (arrangementres.code == 200) {
			arrangement.value = arrangementres.result;
		}
	} catch (error) {
		console.error('请求失败:', error);
	}
}
const feeTypeLabel = (feeType) => {
	switch (feeType) {
		case '1':
			return '待租';
		case '2':
			return '已租';
		case '3':
			return '合计';
		default:
			return '';
	}
};
// 样式处理
const rowStyle = (index) => {
	if (index === 1) {
		return {
			backgroundColor: '#f5f5f5',
			color: '#00ffff', // 青色字体
		};
	}
	return {};
};
getbasic();
</script>
<style lang="less" scoped>
.security_container {
	.security6124 {
		width: 1440px;
		height: 600px;
		background-image: url('@/assets/images/shangauto/security6.1.2.4.png');
		background-size: contain;
		background-repeat: no-repeat;
		position: relative;
	}
	.minjiacu {
		margin-left: 60px;
		font-size: 24px;
		font-weight: 700;
		.vertical_level_center {
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.title {
		font-size: 24px;
		font-weight: 600;
		margin: 16px 0;
	}
	.sub_title_1 {
		font-size: 22px;
		font-weight: 600;
		margin: 16px 0;
	}
	.sub_title_2 {
		font-size: 20px;
		font-weight: 600;
		margin: 16px 0;
	}
	.sub_title_3 {
		font-size: 18px;
		font-weight: 600;
		margin: 16px 0;
	}
	.bold {
		font-weight: 600;
	}
	.underline {
		text-decoration: underline; /* 添加下划线 */
	}
	.base_table {
		width: 100%;
		/* 去掉重复边框 */
		border-collapse: collapse;
		margin-bottom: 16px;
	}
	.sw_td_wrap {
		text-align: center;
		.title {
			font-size: 16px;
			font-weight: 600;
			margin: 0;
			margin-bottom: 7px;
		}
		.desc {
			font-size: 14px;
		}
	}
	.cus_table {
		display: flex;
		flex-wrap: wrap;
		border: 1px solid #000;
		border-bottom: none;
		.left {
			box-sizing: border-box;
			text-align: center;
			width: 30%;
			font-weight: 700;
			line-height: 1.5;
			margin: 0;
			padding: 14px 7px;
			border-right: 1px solid #000;
			border-bottom: 1px solid #000;
		}

		.right {
			margin: 0;
			border-bottom: 1px solid #000;
			box-sizing: border-box;
			text-align: center;
			line-height: 1.5;
			width: 70%;
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 0 7px;
			span {
				flex: 1;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			span:first-child {
				border-right: 1px solid #000;
			}
		}
	}
	.jiegou_left {
		background-size: 100% 100%;
		background-image: url(@/assets/images/zhengquan/jiegou.jpg);
		position: relative;
		height: 523px;
		.bg17-abs-1 {
			position: absolute;
			top: 30%;
			left: 47%;
			width: 130px;
			font-size: 14px;
			font-weight: 700;
			color: #fff;
		}
		.bg17-abs-2 {
			position: absolute;
			top: 12%;
			right: 5%;
			width: 120px;
			font-size: 14px;
			font-weight: 700;
		}
	}
}
</style>
