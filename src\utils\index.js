import { ElNotification } from 'element-plus';
import html2canvas from 'html2canvas';
// NOTIF消息提示
const NOTIF = ElNotification;
export const $notify = (msg = '操作成功', type = 'success', offset = 50) => {
	NOTIF.closeAll();
	NOTIF({
		type: type,
		customClass: type,
		offset,
		message: msg,
	});
};
export function localGet(key) {
	const value = window.localStorage.getItem(key);
	try {
		return JSON.parse(window.localStorage.getItem(key));
	} catch (error) {
		return value;
	}
}

export function localSet(key, value) {
	window.localStorage.setItem(key, JSON.stringify(value));
}

export function localRemove(key) {
	window.localStorage.removeItem(key);
}

// 判断内容是否含有表情字符，现有数据库不支持。
export function hasEmoji(str = '') {
	const reg = /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g;
	return str.match(reg) && str.match(reg).length;
}

// 单张图片上传
export const uploadImgServer = 'http://backend-api-02.newbee.ltd/manage-api/v1/upload/file';
// 多张图片上传
export const uploadImgsServer = 'http://backend-api-02.newbee.ltd/manage-api/v1/upload/files';

export const pathMap = {
	login: '登录',
	introduce: '系统介绍',
	dashboard: '大盘数据',
	add: '添加商品',
	swiper: '轮播图配置',
	hot: '热销商品配置',
	new: '新品上线配置',
	recommend: '为你推荐配置',
	category: '分类管理',
	level2: '分类二级管理',
	level3: '分类三级管理',
	good: '商品管理',
	guest: '会员管理',
	order: '订单管理',
	order_detail: '订单详情',
	account: '修改账户',
};
export function handleNumber(value) {
	// debugger;
	if (!value) {
		return 0;
	}
	let val = parseFloat(value).toFixed(2);
	return Number(val);
}
//时间格式化
export const formatDate = (timestamp, format = 'yyyy-MM-dd hh:mm:ss') => {
	Date.prototype.format = function (format) {
		const o = {
			'M+': this.getMonth() + 1,
			'd+': this.getDate(),
			'h+': this.getHours(),
			'm+': this.getMinutes(),
			's+': this.getSeconds(),
			'q+': Math.floor((this.getMonth() + 3) / 3),
			S: this.getMilliseconds(),
		};
		if (/(y+)/.test(format)) {
			format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
		}
		for (const k in o) {
			if (new RegExp('(' + k + ')').test(format)) {
				format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
			}
		}
		return format;
	};
	if (isIE() && timestamp - 0 + '' == 'NaN') {
		return new Date(Date.parse(timestamp.replace(/-/g, '/'))).format(format);
	} else {
		return new Date(timestamp).format(format);
	}
};
// 动态生成uuid
export const getUuid = () => {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		const r = (Math.random() * 16) | 0,
			v = c == 'x' ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
};
// 文件下载
export const downloadFile = (response, fileName) => {
	const blob = new Blob([response.data]);
	const downloadElement = document.createElement('a');
	const href = window.URL.createObjectURL(blob);
	downloadElement.href = href;
	downloadElement.download = fileName ? fileName : decodeURIComponent(response.headers.filename);
	document.body.appendChild(downloadElement);
	downloadElement.click(); //点击下载
	document.body.removeChild(downloadElement);
	window.URL.revokeObjectURL(href);
};
/**
 * 格式化金额
 *
 * @param totalMoney 总金额
 * @param digit 保留的小数位数，默认为2
 * @returns 格式化后的金额字符串
 */
export const formattedMoney = (totalMoney, digit = 2) => {
	try {
		let factor = Math.pow(10, digit);
		let truncated = Math.floor(totalMoney * factor) / factor;
		let formattedMoney = truncated.toLocaleString('en-US', {
			style: 'decimal',
			maximumFractionDigits: digit, // 设置小数点后的最大位数
		});
		return formattedMoney;
	} catch (error) {
		console.error('发生错误:', error);
	}
};

/**
 * @function: requirex 静态资源处理
 * @param {*} imgPath
 * @returns
 */
export const require = (imgPath) => {
	try {
		const handlePath = imgPath.replace('@', '..');
		return new URL(handlePath, import.meta.url).href;
	} catch (error) {
		console.warn(error);
	}
};

// 下载Echarts图表
export const downloadEcharts = (myChart) => {
	downloadImage(myChart);
};

// 下载图片
async function downloadImage(myChart) {
	const canvas = await html2canvas(myChart, { scale: 1 });
	const dataURL = canvas.toDataURL('image/png');
	const link = document.createElement('a');
	link.href = dataURL;
	link.download = 'echarts.png';
	link.click();
}

//将数字索引转换为中文字符串
export const chineseNumbers = ['十', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
export const chineseNumber = (index) => {
	// 将数字索引转换为中文字符串
	let num = index + 1; // 因为索引是从0开始的，所以需要+1
	let chinese = '';
	while (num > 0) {
		const digit = num % 10;
		chinese = chineseNumbers[digit] + chinese;
		num = Math.floor(num / 10);
	}

	if (chinese.length > 1) {
		chinese = chinese.replace('一十', '十');
	}

	// 处理一十到一九的情况 将一转换为十
	if (chinese.length === 2 && chinese[0] === '一') {
		console.log(chinese[0], chinese[1], 'chinese');
		chinese = '十' + chinese[1];
	}

	return chinese || chineseNumbers[0]; // 处理0的情况
};

export const isEmpty = (value) => {
	// 检查 undefined 和 null
	if (value === undefined || value === null) {
		return true;
	}
	// 检查空字符串
	if (typeof value === 'string' && value.trim().length === 0) {
		return true;
	}
	// 检查空数组
	if (Array.isArray(value) && value.length === 0) {
		return true;
	}
	// 检查空对象
	if (typeof value === 'object' && Object.keys(value).length === 0) {
		return true;
	}

	return false;
};
