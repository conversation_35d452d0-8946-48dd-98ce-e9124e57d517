<template>
	<div>
		<div class="guanggao_img" v-for="(item, index) in AdvertLists" :key="index" @click="goUrl(item.id)">
			<div class="img">
				<div class="icon"></div>
				<img class="bg" :src="`${proxyAddress}${item.displayPic}`" />
			</div>
			<div class="shade">
				<div class="title">{{ item.title }}</div>
				<div class="address">{{ item.address }}</div>
			</div>
		</div>

		<div class="btn">
			<a @click="goDetails">更多标准空间</a>
		</div>
	</div>
</template>
<script setup>
import { Fragment, onMounted, ref } from 'vue';
import { AdvertList } from '../../../src/api/home.js';
const proxyAddress = ref('https://static.biaobiaozhun.com/');
import { useRouter } from 'vue-router';
const router = useRouter();
// 获取广告位详情
const AdvertLists = ref([]);
const goUrl = (url) => {
	console.log(url, 2323);
	router.push({
		path: '/advertising',
		query: {
			id: url,
		},
	});
};
const goDetails = () => {
	router.push({
		path: '/adsDetails',
	});
};
const getAdvertList = async () => {
	let params = {
		// articleType: articleType,
		pageNo: 1,
		pageSize: 10,
	};
	await AdvertList(params)
		.then((res) => {
			console.log('广告', res);
			AdvertLists.value = res.data.rows.splice(0, 4);
			console.log(AdvertLists.value, 88812);
		})
		.catch((err) => {
			console.log('请求失败！');
		});
};
onMounted(() => {
	getAdvertList(); //广告位
});
</script>
<style scoped lang="less">
.guanggao_img {
	width: 100%;
	height: auto;
	overflow: hidden;
	// height: 115px;
	margin-bottom: 10px;
	// height: auto;
	position: relative;
	.icon {
		width: 82px;
		height: 26px;
		background-image: url('../../assets/images/home/<USER>');
		background-size: 100% 100%;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 2;
	}
	.img {
		width: 220px;
		height: 160px;
		display: flex;
		justify-content: center;
		align-items: center;
		overflow: hidden;
		transition: transform 0.3s ease;
		img {
			width: 100%;
			height: 100%;
		}
	}
	.shade {
		width: 100%;
		margin-top: 15px;
		font-family: '微软雅黑';
		text-align: start;
		.title {
			width: 100%;
			font-size: 16px;
			color: rgba(29, 33, 41, 1);
			font-weight: bold;
		}
		.address {
			width: 100%;
			font-size: 12px;
			color: rgba(134, 144, 156, 1);
		}
	}

	// img:hover {
	// 	transform: scale(1.1);
	// 	cursor: pointer;
	// }
}

.btn {
	width: 100%;
	font-size: 14px;
	margin-top: 15px;
	height: 50px;
	text-align: left;

	a {
		cursor: pointer;
	}
}

@media screen and (max-width: 1024px) {
	.guanggao_img {
		width: 100%;
		padding: 0 15px;
		box-sizing: border-box;
		.img {
			width: 100%;
			height: auto;
		}
	}
}
</style>
