<template>
	<div class="cangchu_wrap">
		<template v-if="leftData && leftData.length > 1">
			<div class="double_box_wrap" v-for="(element, eindex) in leftData" :key="eindex">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">基本信息</div>
					</div>
					<div class="content_wrap" v-if="element.id">
						<div class="content_flex">
							<div class="content_c" v-for="(item, index) in list" :key="index">
								<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
									<div class="content_left">{{ childitem.name }}</div>
									<div class="content_right">{{ element[childitem.key] }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-else style="min-height: 280px">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</template>
		<template v-else>
			<div class="box_wrap" v-for="(element, eindex) in leftData" :key="eindex">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">基本信息</div>
						</div>
						<div class="content_wrap" v-if="element.id">
							<div class="content_flex">
								<div class="content_c" v-for="(item, index) in list" :key="index">
									<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
										<div class="content_left">{{ childitem.name }}</div>
										<div class="content_right">{{ element[childitem.key] }}{{ childitem.unit }}</div>
									</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-else style="min-height: 280px">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="right"></div>
			</div>
		</template>
	</div>
</template>
<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import { getBuildMarketInfo } from '@/api/syt.js';
import { defineExpose } from 'vue';
const props = defineProps({
	buildData: {
		type: Array,
		default: () => [],
	},
});
watch(
	() => props.buildData,
	(newVal) => {
		if (newVal.length > 0) {
		}
	},
	{
		deep: true,
	}
);

const leftData = ref([]);
const list = ref([
	{
		arr: [
			{ name: '经营状态', key: 'businessStatus' },
			{ name: '摊位面积', key: 'boothArea', unit: '㎡' },
		],
	},
	{
		arr: [
			{ name: '月租', key: 'monthRent' },
			{ name: '起租期', key: 'startingLeasePeriod' },
		],
	},
	{
		arr: [
			{ name: '押付', key: 'deposit' },
			{ name: '商铺性质', key: 'natureBusiness' },
		],
	},
	{
		arr: [
			{ name: '物业类型', key: 'propertyType' },
			{ name: '经营类型', key: 'businessType' },
		],
	},
	{
		arr: [
			{ name: '相关费用', key: 'relateFee' },
			{ name: '客流人群 ', key: 'customerFlow' },
		],
	},
	{
		arr: [{ name: '配套', key: 'facilities' }],
	},
]);

const getData = async () => {
	let ids = props.buildData.map((item) => item.id).join(',');
	const res = await getBuildMarketInfo({
		buildingIds: ids,
	});
	if (res.code === 200) {
		if ((res.data.length > 1 && props.buildData.length > 1) || (res.data.length === 1 && props.buildData.length === 1)) {
			leftData.value = res.data;
		} else {
			if (props.buildData.length > 1 && res.data.length === 1) {
				leftData.value = [...res.data, { id: null }];
			} else if (props.buildData.length === 1 && res.data.length === 0) {
				leftData.value = [{ id: null }];
			} else if (props.buildData.length > 1 && res.data.length === 0) {
				leftData.value = [{ id: null }, { id: null }];
			}
		}
	}
};

defineExpose({
	getData,
});
</script>
<style lang="less" scoped>
.cangchu_wrap {
	flex: 1;
	display: flex;
	gap: 16px;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
		.single_wrap {
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			//	.content_wrap {
			//	padding: 20px 16px;
			//	}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
	.double_box_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16px;
		.single_wrap {
			flex: 1;
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
}

.content_wrap {
	padding: 20px 16px;
	.content_flex {
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		border: 1px solid #e5e6eb;
		border-bottom: 0px;
		.content_c {
			width: 100%;
			display: flex;
		}
	}
	.features {
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.features_Item {
			width: 139px;
			height: 96px;
			padding: 21px 16px;
			border-radius: 4px;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: center;
			background: #f7f8fa;
			.item_img {
				width: 28px;
				height: 28px;
				margin-bottom: 4px;
			}
			.item_content {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 22px;
				.item_content_l {
					width: 16px;
					height: 16px;
					margin: 3px 4px 3px 0;
					.item_content_img {
						width: 16px;
						height: 16px;
					}
				}
				.item_content_text {
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
					text-align: center;
					color: #1d2129;
				}
			}
		}
	}
	.geographic {
		background: #f7f8fa;
		width: calc(100% - 32px);
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
		padding: 9px 16px;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #4e5969;
	}
	.content_ {
		display: flex;
		width: 50%;
		.content_left {
			box-sizing: border-box;
			padding: 9px 16px;
			background: #f7f8fa;
			border-right: 1px solid #e5e6eb;
			border-bottom: 1px solid #e5e6eb;
			font-weight: 500;
			font-size: 14px;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 120px;
			color: #1d2129;
		}
		.content_right {
			width: calc(100% - 120px);
			background: #ffffff;
			border-right: 1px solid #e5e6eb;
			border-bottom: 1px solid #e5e6eb;
			box-sizing: border-box;
			padding: 9px 16px;
			font-weight: 400;
			font-size: 14px;
			line-height: 21px;
			color: #4e5969;
		}
	}
	.content_flex > :nth-child(n) > :nth-child(2) > :nth-child(2) {
		border-right: 0px !important;
	}
	.content_flex > :nth-last-child(1) > :nth-last-child(1) {
		width: 100% !important;
	}
	.content_flex > :nth-last-child(1) > :nth-last-child(1) > :nth-child(2) {
		border-right: 0px !important;
	}
}
</style>
