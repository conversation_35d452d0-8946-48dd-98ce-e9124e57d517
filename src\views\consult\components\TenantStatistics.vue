<template>
	<div class="content">
		<div>
			<el-row :gutter="20">
				<el-col :span="4">
					<el-cascader
						:key="myCascader"
						placeholder="请选择城市"
						@change="handleChange"
						:options="$vuexStore.state.cityArray"
						:props="{ value: 'label' }"
					>
					</el-cascader>
				</el-col>
				<el-col :span="4">
					<el-select v-model="buildValue" placeholder="选择资产类型" style="width: 240px">
						<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" />
					</el-select>
				</el-col>
				<el-col :span="4">
					<el-input v-model="keyword" placeholder="请输入关键字"></el-input>
				</el-col>
				<el-col :span="5"></el-col>
				<el-col :span="4">
					<el-button @click="search" type="primary">查询</el-button>
					<el-button @click="reset" type="primary">重置</el-button>
				</el-col>
			</el-row>
			<el-table :data="tableData" style="width: 80%" @selection-change="handleSelectionChange" stripe>
				<el-table-column type="selection" width="55" ref="multipleTableRef" />
				<el-table-column prop="name" label="名字" />
				<el-table-column prop="de_type" label="资产类型" />
			</el-table>
			<div style="display: flex; justify-content: space-around; align-items: center">
				<el-pagination @current-change="pageChange" :current-page="currentPage" small background layout="prev, pager, next" :total="total" />
				<el-button style="margin-top: 20px" size="small" type="primary" @click="comparison">对比</el-button>
			</div>
		</div>
		<div v-if="leftName">
			<el-row :gutter="20" style="margin-top: 15px">
				<el-col :span="12">
					<el-text tag="p" class="TitleTwo">
						{{ leftName }}
					</el-text>
					<el-text tag="p" class="Title">
						租户普查>>
						<el-divider class="divider" />
					</el-text>
					<el-text style="line-height: 1.5" class="mx-1">
						{{ leftText }}
					</el-text>
					<el-text tag="p" style="padding: 16px 0 0 0" class="Title">
						租户构成>>
						<el-divider class="divider" />
					</el-text>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">信息技术</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in leftData?.IT" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'ITECommerceCount'">互联网电商</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'technologyInternetCount'">科技互联网</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'hardwareDevelopmentCount'">硬件研发</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'c3ProductCount'">3C电子产品</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'unknownITCount'">未知（其他）</el-text>
								<el-progress color="#f56c6c" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ leftData?.IT.total }}%</el-text>
					</div>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">金融</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in leftData?.finance" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'financeInsuranceCount'">保险经济</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'securityFundCount'">证券/期货</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'bankCount'">银行</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'nonTraditionalCount'">非传统金融</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherCount'">其他金融</el-text>

								<el-progress color="#67C23A" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ leftData?.finance.total }}%</el-text>
					</div>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">工业与商业服务</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in leftData?.businessServices" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'businessServicesCount'">商服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'consultingServiceCount'">咨询服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherConsultationsCount'">其他咨询</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'designServicesCount'">设计服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'integratedManagementCount'">综合管理</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'communicationServicesCount'">通讯服务</el-text>

								<el-progress color="#E6A23C" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ leftData?.businessServices.total }}%</el-text>
					</div>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">消费</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in leftData?.consumption" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'consumerGoodsManufactureCount'">消费品制造</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherConsumerServicesCount'">其他消费服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'educationServicesCount'">教育服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'retailAndTradeCount'">零售与贸易</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'nursingCount'">医美个人护理</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'hotelsCateringCount'">酒店与餐饮</el-text>

								<el-progress color="#E74C3C" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ leftData?.consumption.total }}%</el-text>
					</div>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">其他</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in leftData?.other" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'medicineCount'">医药生物</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'rawMaterialCount'">原材料</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'energyCount'">能源</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'realEstateCount'">房地产</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'publicUtilityCount'">公用事业</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'unknownCount'">其它</el-text>
								<el-progress color="#5B2C6F" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ leftData?.other.total }}%</el-text>
					</div>
				</el-col>
				<el-col :span="12">
					<el-text tag="p" class="TitleTwo">
						{{ rightName }}
					</el-text>
					<el-text tag="p" class="Title">
						租户普查>>
						<el-divider class="divider" />
					</el-text>
					<el-text style="line-height: 1.5" class="mx-1">
						{{ rightText }}
					</el-text>
					<el-text tag="p" style="padding: 16px 0 0 0" class="Title">
						租户构成>>
						<el-divider class="divider" />
					</el-text>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">信息技术</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in rightData?.IT" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'ITECommerceCount'">互联网电商</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'technologyInternetCount'">科技互联网</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'hardwareDevelopmentCount'">硬件研发</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'c3ProductCount'">3C电子产品</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'unknownITCount'">未知（其他）</el-text>
								<el-progress color="#f56c6c" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ rightData?.IT.total }}%</el-text>
					</div>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">金融</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in rightData?.finance" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'financeInsuranceCount'">保险经济</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'securityFundCount'">证券/期货</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'bankCount'">银行</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'nonTraditionalCount'">非传统金融</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherCount'">其他金融</el-text>

								<el-progress color="#67C23A" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ rightData?.finance.total }}%</el-text>
					</div>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">工业与商业服务</el-text>
						<div class="rightList">
							<div
								style="display: flex; padding-top: 8px"
								v-for="(item, index) in rightData?.businessServices"
								:key="item"
								v-show="index !== 'total'"
							>
								<el-text class="mx-1 title" tag="span" v-if="index === 'businessServicesCount'">商服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'consultingServiceCount'">咨询服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherConsultationsCount'">其他咨询</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'designServicesCount'">设计服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'integratedManagementCount'">综合管理</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'communicationServicesCount'">通讯服务</el-text>

								<el-progress color="#E6A23C" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ rightData?.businessServices.total }}%</el-text>
					</div>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">消费</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in rightData?.consumption" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'consumerGoodsManufactureCount'">消费品制造</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'otherConsumerServicesCount'">其他消费服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'educationServicesCount'">教育服务</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'retailAndTradeCount'">零售与贸易</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'nursingCount'">医美个人护理</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'hotelsCateringCount'">酒店与餐饮</el-text>

								<el-progress color="#E74C3C" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ rightData?.consumption.total }}%</el-text>
					</div>
					<div class="list">
						<el-text class="mx-1 ali" tag="p">其他</el-text>
						<div class="rightList">
							<div style="display: flex; padding-top: 8px" v-for="(item, index) in rightData?.other" :key="item" v-show="index !== 'total'">
								<el-text class="mx-1 title" tag="span" v-if="index === 'medicineCount'">医药生物</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'rawMaterialCount'">原材料</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'energyCount'">能源</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'realEstateCount'">房地产</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'publicUtilityCount'">公用事业</el-text>
								<el-text class="mx-1 title" tag="span" v-else-if="index === 'unknownCount'">其它</el-text>
								<el-progress color="#5B2C6F" text-inside style="width: 80%" :percentage="Number(item)" :stroke-width="15" striped-flow />
							</div>
						</div>
						<el-text class="mx-1 ali" tag="p"> {{ rightData?.other.total }}%</el-text>
					</div>
				</el-col>
			</el-row>
		</div>
	</div>
</template>
<script setup>
import { ref } from 'vue';
import http from '@/utils/http';
import { ElMessage } from 'element-plus';
const city = ref('');
const county = ref('');
const myCascader = ref(0);
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '保障房', '综合市场', '不限'];
const buildValue = ref('');
const keyword = ref('');
const tableData = ref([]);
const currentPage = ref(1);
const total = ref('');
const handleChange = (val) => {
	console.log(val, 'ss');
	city.value = val[0];
	county.value = val[1];
};
const reset = () => {
	myCascader.value++;
	city.value = '';
	county.value = '';
	buildValue.value = '';
	keyword.value = '';
	currentPage.value = 1;
};
const search = async () => {
	const res = await http.get('/api/a_maps.do?act=getMap', {
		params: { city: city.value, county: county.value, de_type: buildValue.value, keywords: keyword.value, currentPage: currentPage.value },
	});
	tableData.value = res.list;
	total.value = res.total;
	console.log(tableData.value, 'res');
};
const multipleSelection = ref([]);
let leftData = ref();
let rightData = ref();
const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};
const leftText = ref('');
const rightText = ref('');
const leftName = ref('');
const rightName = ref('');
const comparison = async () => {
	console.log(multipleSelection.value, 'dsad');
	if (multipleSelection.value.length !== 2) {
		ElMessage.warning('只能选择两个！！！');
		return;
	}
	const idarray = [];
	multipleSelection.value.map((item) => idarray.push(item.id));
	console.log(idarray, 'idarray');
	const res = await http.get('/api/a_maps.do?act=tenantContrast', {
		params: { ids: idarray.join(',') },
	});
	leftText.value = res.list[0].tenantCensusText;
	rightText.value = res.list[1].tenantCensusText;
	leftData.value = res.list[0].tenantComposition;
	rightData.value = res.list[1].tenantComposition;
	leftName.value = res.list[0].buildingName;
	rightName.value = res.list[1].buildingName;
};
const pageChange = (val) => {
	currentPage.value = val;
	search();
};
</script>

<style lang="less" scoped>
.ali {
	width: 20%;
}
.Title {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	display: flex;
	white-space: nowrap;
}
.list {
	display: flex;
	justify-content: space-between;
	padding-top: 15px;
	.rightList {
		width: 50%;
	}
}
.divider {
	flex-grow: 1;
	margin: 16px 0 16px 8px;
	border-color: #3483ce;
}
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}
.content {
	margin: 20px 0 0 20px;
	padding: 20px 0 0 20px;
}
.title {
	width: 20%;
}
.TitleTwo {
	font-size: 16px;
	font-weight: 700;
	color: #3483ce;
	text-align: center;
	margin-bottom: 15px;
}
</style>
