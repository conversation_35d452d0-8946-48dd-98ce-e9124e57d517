<template>
	<div class="pie_box" ref="echartsPie"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { onMounted, ref, watch } from 'vue';
const echartsPie = ref();
let myChart = null;
const props = defineProps({
	pieData: {
		type: Object,
		default: null,
	},
});
const chartOption = {
	tooltip: {
		trigger: 'item',
		formatter: function (params) {
			// params 是包含当前数据点信息的对象
			return params.name + ' : ' + params.data.merchantNum + ' (' + params.value + '%)';
		},
	},
	legend: {
		bottom: 10,
		left: 'center',
		// data: ['CityA', 'CityB', 'CityD', 'CityC'],
	},
	series: [
		{
			type: 'pie',
			radius: '65%',
			center: ['50%', '50%'],
			// selectedMode: 'single',
			data: [
				// { value: 25, name: '1' },
				// { value: 25, name: '2' },
				// { value: 25, name: '3' },
				// { value: 25, name: '4' },
			],
			label: {
				show: true, // 显示标签
				// formatter: '{b}: {c} ({d}%)', // 格式化标签内容
				// fontStyle: 'normal', // 字体风格，可选 'normal', 'italic', 'oblique'
				// fontWeight: 'bold', // 字体粗细，可选 'normal', 'bold', 'bolder', 'lighter', 100, 200, 300, ..., 900
				// fontFamily: 'sans-serif', // 字体族，可以是 'serif', 'sans-serif', 'monospace', 或者具体字体名称
				fontSize: 14, // 字体大小
				// 其他标签样式设置...
			},
			emphasis: {
				itemStyle: {
					shadowBlur: 10,
					shadowOffsetX: 0,
					shadowColor: 'rgba(0, 0, 0, 0.5)',
				},
			},
		},
	],
};
onMounted(() => {
	// 在组件挂载时初始化图表
	myChart = echarts.init(echartsPie.value);
	myChart.setOption(chartOption);
});
onBeforeUnmount(() => {
	// 在组件卸载时销毁图表
	if (myChart) {
		myChart.dispose();
	}
});
watch(
	() => props.pieData,
	(newData, oldData) => {
		console.log("🚀 ~ newData:", newData)
		if (newData && myChart) {
			chartOption.series[0].data = [];
			newData.map((item) => {
				chartOption.series[0].data.push({
					value: item.merchantPercentage,
					name: item.commercialForm,
					merchantNum: item.merchantNum,
				});
			});
			myChart.setOption(chartOption);
		}
		// chartOption.title.text = newData
	},
	{
		immediate: true,
	}
);
onMounted(() => {});
</script>

<style lang="less" scoped>
.pie_box {
	width: 100%;
	height: 380px;
	// padding: 50px;
	box-sizing: border-box;
}
</style>
