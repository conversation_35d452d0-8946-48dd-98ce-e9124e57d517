import { fetch } from './http';

export function tabPermissions(params) {
	//PC端tab访问权限验证
	return fetch({
		url: '/api/amber/pc/tabPermissions',
		method: 'get',
		params: params,
		loading: false,
	});
}

export function getPCGoodsList(params) {
	//PC端单一页面通过购买类型获取商品数据列表
	return fetch({
		url: `/api/amber/getPCGoodsList/${params}`,
		method: 'get',
		loading: false,
	});
}
export function getPCAllGoodsList() {
	//PC端商品数据列表
	return fetch({
		url: `/api/amber/getPCAllGoodsList`,
		method: 'get',
		loading: false,
	});
}
export function getPCAllComboGoodsList() {
	//PC端商品套餐数据列表
	return fetch({
		url: `/api/amber/getPCAllComboGoodsList`,
		method: 'get',
		loading: false,
	});
}
export function qrcodePlaceOrder(params) {
	//PC下单
	return fetch({
		url: `/api/amber/pc/qrcodePlaceOrder`,
		method: 'post',
		params: params,
		loading: true,
	});
}

export function getUserExtension(params) {
	//获取用户扩展信息
	return fetch({
		url: '/api/user/extension',
		method: 'get',
		params: params,
	});
}

export function sendCoupon(params) {
	// 商宇通领取新人券
	return fetch({
		url: '/api/user-coupon/send-coupon',
		method: 'post',
		params: params,
	});
}

export function sendSgtCoupon(params) {
	// 商估通领取新人券
	return fetch({
		url: '/api/sgt-user-coupon/send-coupon',
		method: 'post',
		params: params,
	});
}
