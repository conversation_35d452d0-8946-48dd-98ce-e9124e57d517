<template>
	<div class="mobile_shang_wrap">
		<div class="header_wrap">
			<img :src="mobile_bg" class="header_bg" />
			<div class="logo_wrap">
				<el-image :src="logo" class="logo"></el-image>
			</div>
			<div class="flag_wrap">
				<div class="title">投资分析 洞见未来</div>
				<div class="desc">60000+商业地产专业报告供您选择</div>
			</div>
			<div style="padding: 0 20px">
				<div class="prompt_wrap">
					<div class="desc">请用电脑端浏览器搜索“标标准”或打开下方链接体验完整功能</div>
					<div class="link_wrap">
						<img src="https://static.biaobiaozhun.com/mini-program/report/link.png" />
						<div class="link">https://bbzhun.com/#/shangAuto</div>
						<div class="copy" @click="copy">复制</div>
					</div>
				</div>
			</div>
		</div>
		<div class="reprot_wrap">
			<div class="report_desc">为您提供多种报告选择</div>
			<div class="single_wrap">
				<div class="top">
					<div class="img">
						<img src="https://static.biaobiaozhun.com/mini-program/report/single_bg.png" />
					</div>
					<div class="desc_wrap">
						<div class="title">单项报告</div>
						<div class="desc">为您提供多种单项报告类型选择</div>
						<div class="tag">累计下载1000+次</div>
						<!-- <div class="btn" @click="handleBuy">立即获取</div> -->
					</div>
				</div>
				<div class="bottom">
					<div class="type_wrap">
						<div class="type_item" v-for="item in reportList" @click="handleViewDemo(item)">
							<div class="tag">
								<img src="https://static.biaobiaozhun.com/mini-program/report/play.png" />
							</div>
							<div class="icon">
								<img :src="item.icon" />
							</div>
							<div class="title">{{ item.title }}</div>
						</div>
					</div>
				</div>
			</div>
			<div class="analyze_wrap">
				<div class="img">
					<img src="https://static.biaobiaozhun.com/mini-program/report/analyze_bg.png" />
				</div>
				<div class="desc_wrap">
					<div class="title">投资分析报告内容</div>
					<div class="desc">资产数据分析,投资风险评估</div>
					<div class="demo" @click="handleViewTzDemo">
						<img src="https://static.biaobiaozhun.com/mini-program/report/play-circle-fill.png" />
						演示文档
					</div>
				</div>
			</div>
			<div class="package_wrap">
				<div class="img">
					<img src="https://static.biaobiaozhun.com/mini-program/report/package_bg.png" />
				</div>
				<div class="desc_wrap">
					<div class="title">报告套餐内容</div>
					<div class="desc">投资分析报告+全套单项报告</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import logo from '@/assets/images/shangauto/logo_mobile.png';
import mobile_bg from '@/assets/images/shangauto/mobile_bg.png';
import { ElMessage } from 'element-plus';
import { reportList } from './data';
import { useRouter } from 'vue-router';
const router = useRouter();

function handleViewDemo(item) {
	router.push({
		path: '/mobileDemo',
		query: {
			reportType: item.reportType,
			title: item.title,
		},
	});
}
function handleViewTzDemo() {
	router.push({
		path: '/mobileDemo',
		query: {
			reportType: 'invest_analyze',
			reportTypeName: '投资分析报告',
		},
	});
}

function copy() {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText('https://bbzhun.com/#/shangAuto')
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = 'https://bbzhun.com/#/shangAuto';
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}
</script>
<style lang="less" scoped>
.mobile_shang_wrap {
	width: 100%;
    padding-bottom: 186px;
	.header_wrap {
		height: 320px;
		position: relative;
		.header_bg {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
		}
		.logo_wrap {
			position: relative;
			padding: 32px 0 8px 0;
			display: flex;
			justify-content: center;
			.logo {
				width: 48px;
				height: 48px;
			}
		}
		.flag_wrap {
			position: relative;
			.title {
				font-size: 26px;
				font-weight: 600;
				color: #1d2129;
				text-align: center;
				margin-bottom: 4px;
				line-height: 1.5;
			}
			.desc {
				font-size: 12px;
				text-align: center;
				color: #4e5969;
				font-weight: 400;
				line-height: 1.5;
			}
		}
		.prompt_wrap {
			position: relative;
			margin-top: 74px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			padding: 12px;
			border-radius: 8px;
			background-color: #fff;
			height: 81px;
			box-sizing: border-box;
			.desc {
				font-size: 10px;
				font-weight: 400;
				color: #4e5969;
			}
			.link_wrap {
				background: #f6f7f8;
				height: 36px;
				padding: 6px 8px;
				display: flex;
				align-items: center;
				border-radius: 6px;
				box-sizing: border-box;
				img {
					width: 16px;
					height: 16px;
				}
				.link {
					font-size: 12px;
					font-weight: 400;
					color: #1868f1;
					margin-left: 4px;
					flex: 1;
				}
				.copy {
					font-size: 12px;
					font-weight: 500;
					width: 48px;
					height: 24px;
					line-height: 24px;
					background: #2c6be9;
					color: #fff;
					border-radius: 4px;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}
	.reprot_wrap {
		padding: 0 12px;
		padding-bottom: 20px;
		display: flex;
		flex-direction: column;
		gap: 8px;
		background-color: #f4f7fc;
		.report_desc {
			margin-top: 48px;
			margin-bottom: 20px;
			font-size: 22px;
			font-weight: 500;
			line-height: 1.5;
			text-align: center;
		}
		.single_wrap,
		.analyze_wrap,
		.package_wrap {
			padding: 12px;
			background-color: #fff;
			border-radius: 8px;
			.img {
				width: 84px;
				height: 84px;
				img {
					width: 100%;
					height: 100%;
				}
			}
			.desc_wrap {
				.title {
					font-size: 16px;
					font-weight: 500;
					line-height: 22px;
					color: #1d2129;
					margin-top: 4px;
					margin-bottom: 2px;
				}
				.desc {
					font-size: 12px;
					font-weight: 400;
					line-height: 18px;
					color: #4e5969;
					margin-bottom: 3px;
				}
				.tag {
					background: #fff7e8;
					padding: 2px 4px;
					font-size: 10px;
					font-weight: 400;
					color: #ff7d00;
					border-radius: 2px;
					width: 90px;
					text-align: center;
				}
				.demo {
					color: #1868f1;
					font-size: 12px;
					font-weight: 400;
					margin-top: 15px;
					display: flex;
					align-items: center;
					line-height: 1.5;
					img {
						width: 14px;
						height: 14px;
						margin-right: 2px;
					}
				}
				.btn {
					position: absolute;
					right: 0;
					bottom: 0;
					background: #1868f1;
					border-radius: 4px;
					width: 72px;
					height: 28px;
					color: #fff;
					font-size: 14px;
					font-weight: 500;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
		.single_wrap {
			display: flex;
			flex-direction: column;
			gap: 12px;
			.top {
				position: relative;
				display: flex;
				gap: 12px;
			}
			.bottom {
				.type_wrap {
					display: grid;
					grid-template-columns: repeat(4, 1fr); /* 四列，等宽 */
					grid-template-rows: repeat(2, 1fr); /* 两行，等高 */
					gap: 8px;
					.type_item {
						position: relative;
						border: 1px solid #e5e6eb;
						width: 100%;
						height: 76px;
						border-radius: 8px;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						.tag {
							position: absolute;
							right: 0;
							top: 0;
							width: 18px;
							height: 18px;
							img {
								width: 100%;
								height: 100%;
							}
						}
						.icon {
							width: 28px;
							height: 28px;
							img {
								width: 100%;
								height: 100%;
							}
						}
						.title {
							margin-top: 4px;
							font-size: 12px;
							font-weight: 400;
							color: #1d2129;
						}
					}
				}
			}
		}
		.analyze_wrap,
		.package_wrap {
			position: relative;
			display: flex;
			gap: 12px;
			.desc_wrap {
				.btn {
					right: 12px;
					bottom: 12px;
				}
			}
		}
	}
}
</style>
