<template>
	<div class="subscription">
		<!-- 背景图 -->
		<div class="background">
			<div class="text">
				<p class="title">查看标标准的实际应用</p>
				<p class="manifesto">“创新、协作、高效、纯粹”</p>
			</div>
		</div>
		<!-- 订阅内容 -->
		<div class="content">
			<div class="left_box">
				<div class="title">体验{{menu_name}}</div>
				<div class="content_text">
					{{content}}
				</div>
			</div>
			<div class="right_box" v-if="data.obj">
				<div class="price_box1">
					<div class="label1">{{data.obj.goodsDataList[0].typeName}}</div>
					<div class="value_box">
						<div class="value2">￥{{data.obj.goodsDataList[0].goodsDataList[0].currentPrice}}<span>/每月</span></div>
					</div>
				</div>
				<div class="price_box1">
					<div class="label1">{{data.obj.comboPackGoodsDataList[0].typeName}}</div>
					<div class="value_box">
						<div class="value2" v-for="(item,index) in data.obj.comboPackGoodsDataList[0].goodsDataList" :key="index"><span>1折</span>￥{{item.currentPrice}}<span>/{{item.dataName}}</span></div>
					</div>
				</div>
			</div>
			<div class="btn active" @click="jumpDetail">立刻订阅</div>
		</div>
	</div>
</template>

<script setup>
	import {
		ref,
		reactive,
		toRefs,
		onMounted
	} from 'vue'
	import { getPCGoodsList } from '../../api/layout.js'
	const code = ref('707008')
	const money = ref(98)
	const content = ref('')
	const menu_name = ref('')
	// 接收方
	import {
		useRoute,
		useRouter
	} from 'vue-router'
	const route = useRoute()
	const router = useRouter()
	console.log(route.query.id, 7888);
	const describe = ref([{
			name: '市场统计',
			describe: '您需要了解的有当前状况和未来前景的所有信息基于我们对商业地产市场全类型资产的分析和掌握，设计出的具有完整数据和强大分析能力的版块。您可以在其中精确掌握资产数量、楼宇概况以及发展前景。使用的商业地产分析工具，您可以了解市场，并轻松掌握近期趋势、关键指标和未来前景。',
			money: 98,
			value:'707008'
		},
		{
			name: '交易统计',
			describe: '展示租售信息和账务分析标标准有完整的交易提供，依托自研及交易平台，借助全面的租售信息系统，能够帮您获取最一线的交易情况，助力您保持清晰的信息脉络。',
			money: 98,
			value:''
		},
		{
			name: '交易材料',
			describe: '使用强大的分析工具以自己的方式探索商业地产借助标标准强大的底层资产分析，提供全面的数据，涵盖推动商业地产的行业、市场、交易和交易撮合者。您可以有效评估资产、了解市场情况。使用电子表格分析买卖租赁数据，并进行建模是一项繁琐并困难的手动任务，交易材料是一个强大的工具，可以自动计算新的资产数据和财务数据。',
			money: 198.,
			value:'707010'
		},
		{
			name: '参与者',
			describe: '无论是个人还是机构，都会保持对资产的敏感和自我的经营。借助标标准平台，实现行业从业人员的有效交互是标标准一直以来持有的观点。',
			money: 98,
			value:'707011'
		},
		{
			name: '地产金融',
			describe: '中国商业地产金融化需要您参加。以全新的眼光看待地产金融。标标准提供有关私募基金和公募基金的详细信息，并整合了行业领先的资产、交易和数据。通过将RETIs、地产基金和基础资产联系起来，我们的新工作方式为专业人士提供了另一种识别和评估新机会的方法。',
			money: 98,
			value:'707012'
		},
		{
			name: '信用风险',
			describe: '新颖的资产配置分析模型，让您获得确定的结果。',
			money: 198,
			value:'707013'
		}
	])
	const data = reactive({
		obj:null,
	})
	onMounted(() => {
		const id = route.query.id
		console.log(id);
		describe.value.forEach(item => {
			if (item.value === id) {
				console.log(item)
				code.value = item.value
				menu_name.value = item.name
				content.value = item.describe
				money.value = item.money
			}
		})
		getGoods_list(id)
		console.log(code.value, 6888);
	})
	//获取商品列表
	const getGoods_list = async (id) => {
		await getPCGoodsList(id).then(res => {
			console.log(res)
			data.obj = res.data
		}).catch(err => {
			console.log('请求失败！')
		})
	}
	const jumpDetail = () => {
		router.push({
			path:`/subscription_detail`,
			query:{
				id:data.obj.goodsDataList[0].goodsDataList[0].id
			}
		})
	}
	// 
</script>

<style lang="less" scoped>
	.subscription {
		width: 100%;
		height: 100%;

		.background {
			width: 100%;
			height: 417px;
			background: url('@/assets/images/home/<USER>') no-repeat;
			background-size: 100% 100%;
			position: relative;

			.text {
				width: 500px;
				height: 171px;
				background: rgb(255, 255, 255);
				opacity: 0.78;
				text-align: center;
				position: absolute;
				left: 203px;
				top: 152px;

				.title {
					color: rgb(0, 0, 0);
					font-family: 微软雅黑;
					font-size: 22px;
					font-weight: 400;
					line-height: 33px;
					letter-spacing: 0px;
					text-align: center;
					border-bottom: 3px solid rgb(71, 121, 184);
					width: 220px;
					margin-left: 40px;
					padding-bottom: 10px;
					// margin-bottom: 10px;
				}

				.manifesto {
					color: rgb(0, 0, 0);
					font-family: 微软雅黑;
					font-size: 35px;
					font-weight: 700;
					line-height: 46px;
					letter-spacing: 0px;
					text-align: center;
					margin-top: 0;
				}
			}
		}

		.content {
			width: 100%;
			height: 100%;
			margin-top: 40px;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: flex-start;

			.left_box {
				width: 50%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-direction: column;

				.title {
					width: 62%;
					font-size: 18px;
					margin: 30px 0;
					font-weight: bold;
				}

				.content_text {
					width: 62%;
					font-size: 14px;
					line-height: 20px;
				}
			}

			.right_box {
				width: 30%;

				.price_box1 {
					width: 100%;
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					margin: 30px 0;

					.label1 {
						font-size: 16px;
						font-weight: bold;
					}

					.value_box {
						width: 50%;

						.value2 {
							width: 100%;
							text-align: end;
							box-sizing: border-box;
							margin: 15px 0;
							font-size: 30px;
							font-weight: bold;

							span {
								font-size: 14px;
							}
						}

					}
				}
			}

			.btn {
				width: 120px;
				height: 48px;
				margin-top: 60px;
				background-color: #38609A;
				display: flex;
				justify-content: center;
				align-items: center;
				color: rgba(255, 255, 255, 1);
				font-size: 14px;
				font-weight: bold;
				cursor: pointer;
			}
		}
	}
</style>