<template>
	<el-dialog
		v-model="props.dialogVisible"
		:title="dialogTitle"
		class="getReportdialog"
		style="width: 700px; border-radius: 6px; padding: 0px"
		:close-on-click-modal="false"
		align-center="center"
		:before-close="handleClose"
	>
		<div class="purchase-container">
			<div class="purchase_box">
				<div class="name">请填写邮箱地址</div>
				<el-input v-model="emailAddress" style="width: 310px" placeholder="请填写邮箱地址（支付后报告会发送至该邮箱）" />
			</div>
			<div class="single-box">
				<div class="left-back">
					<img src="@/assets/valuationReport.png" alt="" />
				</div>
				<div class="single-details">
					<div class="name-box" @click="handleReportDemo()">
						<div class="name">{{ couponDetail.reportTypeName }}</div>
						<div class="equity"><img src="@/assets/viewReport.png" alt="" />点击查看报告Demo</div>
					</div>
					<div class="money">
						<span class="mo">￥</span><span class="num">{{ couponDetail.price }}</span>
					</div>
				</div>
			</div>
			<div class="count-add">
				<el-checkbox value="Agree" v-model="checkbox" name="type" class="check_boxAgreement"
					>我已阅读并同意<span class="blueSpan">《商宇通权益订阅服务协议 》</span>
				</el-checkbox>
			</div>

			<div class="form_content">
				<div class="content_box_left">
					<el-skeleton-item variant="image" v-if="!paymentStatus" style="width: 100%; height: 100%; background: #fff" />
					<iframe
						:src="paymentURL"
						v-show="paymentStatus === 'ALI_PC' && checkbox"
						frameborder="no"
						border="0"
						marginwidth="0"
						marginheight="0"
						scrolling="no"
						width="200"
						height="200"
						style="overflow: hidden; transform: scale(0.6); margin: -20px 0 0 -40px; transform-origin: 100px 50px; /* 确保从左上角开始缩放 */"
					>
					</iframe>
					<canvas v-show="paymentStatus === 'WX_NATIVE' && checkbox > 0" ref="qrcodeCanvas" class="qrcode"></canvas>
				</div>
				<div class="content_box_right">
					<div class="content_box_price">
						<div>扫码支付</div>
						<div>¥</div>
						<div>{{ priceAll || priceTotal.toFixed(2) }}</div>
					</div>

					<div class="content_box_bottom">
						<el-radio-group v-model="paymentStatus" @change="handlePriceChangeAll">
							<el-radio value="ALI_PC" size="large">支付宝</el-radio>
							<el-radio value="WX_NATIVE" size="large">微信</el-radio>
						</el-radio-group>
					</div>
				</div>
			</div>
		</div>
	</el-dialog>

	<reportDemo
		ref="reportDemoDom"
		:reportType="couponDetail.reportType"
		:reportTypeName="couponDetail.reportTypeName"
		@close="handleReportClose"
	></reportDemo>
</template>

<script setup>
import QRCode from 'qrcode';
import { getReport } from '@/api/equityTerm.js';
import { orderOreate, orderStatus, getDiscount } from '@/api/rights';
import { ElMessage, ElMessageBox } from 'element-plus';
import { defineExpose } from 'vue';
import reportDemo from '../reportDemo/index.vue';
const emit = defineEmits();
const props = defineProps({
	dialogVisible: {
		//弹框
		type: Boolean,
		default: false,
	},
	// 建筑id
	buildingId: {
		type: String,
		default: '',
	},
});
const reportDemoDom = ref(); // 报告demo元素
const qrcodeCanvas = ref(); // 微信二维码
const dialogTitle = ref('获取完整报告'); //弹框标题
const paymentStatus = ref(''); //支付方式 WX_NATIVE 微信 ALI_PC 支付宝
const paymentURL = ref(''); //支付二维码
const checkbox = ref(false); // 同意协议
const priceAll = ref(0); //折扣后的商品总价
const priceTotal = ref(0); // 商品总价
const discounts = ref(0); // 折扣
const timerId = ref(null); // 订单轮询定时器
const orderId = ref(''); // 订单ID
const couponDetail = ref({}); //报告详情
const emailAddress = ref(''); // 邮箱地址

// 查看当前报告demo
function handleReportDemo() {
	reportDemoDom.value.generateReport();
}
// 获取报告
function hanldeGetReport(params) {
	getReport({ reportType: params }).then((res) => {
		if (res.code === 200) {
			couponDetail.value = res.data;
		}
	});
}
// 清空数据
function handleClearData() {
	clearInterval(timerId.value);
	checkbox.value = false; //同意
	emailAddress.value = '';
	paymentStatus.value = ''; //支付状态
	priceTotal.value = 0; //总金额
	priceAll.value = 0; //应付金额
	discounts.value = 0; //折扣
	paymentURL.value = ''; //支付二维码
}
// 关闭报告
function handleReportClose() {
	reportDemoDom.value.dialogVisible = false;
}

// 关闭
function handleClose() {
	handleClearData(); //清空数据
	emit('handleRightsClose');
}

// 订单轮询获取支付状态
const pollOrderStatus = async (orderId) => {
	if (paymentStatus.value === '' || !checkbox.value) {
		return;
	}
	try {
		const response = await orderStatus(orderId);
		if (orderId === '') {
			return; // 提前返回，不执行后续代码
		}
		//PENDING("待支付"),        // 待支付
		// PAID("已支付"),          // 已支付
		// FAILED("支付失败"),      // 支付失败
		// CANCELLED("已取消"),     // 已取消
		// REFUNDED("已退款");      // 已退款
		if (response.code == 200) {
			// 根据返回的状态更新状态提示信息
			switch (response.data) {
				case 'PAID':
					ElMessage({
						message: `支付成功`,
						type: 'success',
					});
					clearInterval(timerId.value);
					// 关闭弹框
					handleClose();
					break;
				case 'FAILED':
					ElMessage({
						message: `支付失败`,
						type: 'error',
					});
					clearInterval(timerId.value);
					// 关闭弹框
					handleClose();
					break;
				case 'CANCELLED':
					ElMessage({
						message: `订单已取消`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				case 'REFUNDED':
					ElMessage({
						message: `已退款`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				default:
					break;
			}
		} else {
			clearInterval(timerId.value);
		}
	} catch (error) {
		clearInterval(timerId.value);
	}
};

//订单创建
function handleOrderCreate(params) {
	orderOreate(params).then((res) => {
		if (res.code == 200) {
			let data = res.data;
			if (paymentStatus.value === 'ALI_PC') {
				// 支付宝
				paymentURL.value = data.url;
			} else {
				// 微信
				const qrCodeDiv = qrcodeCanvas.value;
				QRCode.toCanvas(qrCodeDiv, data.url, (error) => {
					if (error) console.error(error);
				});
			}

			if (data.outTradeNo) {
				orderId.value = data.outTradeNo;
				// 开始轮询
				timerId.value = setInterval(() => {
					pollOrderStatus(orderId.value);
				}, 1500);
			}
		} else {
			//清空支付方式
			paymentStatus.value = '';
			//清空邮箱
			emailAddress.value = '';
		}
	});
}

// 支付
function handlePayment() {
	//清楚定时器
	clearInterval(timerId.value);
	let arr = [
		{
			orderCount: 1,
			businessType: 'REPORT_ORDER', // 这是购买报告的类型
			buildingId: props.buildingId, // 建筑id
			reportType: couponDetail.value.reportType, // 报告类型
			email: emailAddress.value, // 邮箱
			totalAmount: couponDetail.value.price, // 总金额
		},
	];

	let params = {
		payType: paymentStatus.value, // 支付方式
		orderCount: 1, // 套餐数量
		totalAmount: couponDetail.value.price, // 总金额
		discountAmount: Number(discounts.value), // 折扣
		payableAmount: Number(priceAll.value), // 应付金额
		orderDetails: arr, // 套餐
	};
	handleOrderCreate(params); // 订单创建
}

// 支付明细
function handlePriceChangeAll() {
	if (!checkbox.value) {
		paymentStatus.value = '';
		// ElMessage({
		//   message: '请勾选同意',
		//   type: 'warning',
		// });
		ElMessageBox({
			title: '提示',
			message: '请勾选同意',
			type: 'warning',
			showCancelButton: true,
			confirmButtonText: '确定',
			cancelButtonText: '取消',
		})
			.then(() => {
				checkbox.value = true;
			})
			.catch(() => {
				checkbox.value = false;
			});
		return;
	}

	// 判断邮箱是否为空
	if (emailAddress.value === '') {
		ElMessage({
			message: '请输入邮箱',
			type: 'warning',
		});
		paymentStatus.value = '';
		return;
	}
	getDiscount({ totalAmount: Number(couponDetail.value.price.toFixed(2)) }).then((res) => {
		if (res.code === 200) {
			discounts.value = Number(res.data.discountAmount.toFixed(2)); // 折扣
			priceAll.value = Number(res.data.payableAmount.toFixed(2)); // 应付金额
			handlePayment(); // 创建订单
		}
	});
}
defineExpose({
	hanldeGetReport,
});
</script>

<style lang="scss" scoped>
.purchase-container {
	padding: 0 32px;
	.purchase_box {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 64px;
		.name {
			color: #4e5969;
			font-size: 14px;
			font-weight: 400;
			line-height: 22px;
		}
	}

	.count-add {
		display: flex;
		justify-content: space-between;
		align-items: center;
		// margin-top: 15px;
		height: 72px;
	}
	.single-box {
		height: 80px;
		margin: 16px 0;
		display: flex;
		border: 2px solid rgba(24, 104, 241, 1);
		border-radius: 8px;
		.left-back {
			width: 100px;
			height: 100%;
			background: rgba(24, 104, 241, 1);
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
		.single-details {
			height: 100%;
			width: calc(100% - 0px);
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 20px;

			.name-box {
				height: 60px;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.name {
					font-size: 22px;
					font-weight: 700;
					color: rgba(74, 77, 84);
				}

				.equity {
					cursor: pointer;
					font-size: 14px;
					font-weight: 500;
					display: flex;
					color: #4e5969;
					align-items: center;
					img {
						margin-right: 4px;
					}
				}
			}

			.money {
				font-size: 16px;
				font-weight: 700;
				color: rgba(29, 33, 41, 1);

				.mo {
					color: rgba(24, 104, 241, 1);
				}

				.num {
					font-size: 28px;
					font-weight: 700;
					color: rgba(24, 104, 241, 1);
				}
			}
		}
	}
}

.form_content {
	width: calc(100% - 0px);
	height: 128px;
	display: flex;
	justify-content: space-between;
	border-radius: 4px;
	.content_box_left {
		width: 120px;
		height: 120px;
		margin: 0 16px 0 0;
	}
	.content_box_right {
		margin: 16px 0;
		width: calc(100% - 136px);
		display: flex;
		flex-direction: column;
		justify-content: center;
		.check_boxAgreement {
			height: 22px;
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			margin-bottom: 34px;
		}
		.blueSpan {
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			color: rgba(24, 104, 241, 1);
		}

		.content_box_price {
			display: flex;
			height: 24px;
			margin-bottom: 10px;
			& > :nth-child(1) {
				font-size: 16px;
				font-weight: 700;
				line-height: 27px;
				color: #1d2129;
			}
			& > :nth-child(2) {
				font-size: 16px;
				font-weight: 700;
				line-height: 29px;
				color: #1868f1;
				margin: 0 8px;
			}
			& > :nth-child(3) {
				font-size: 28px;
				font-weight: 500;
				line-height: 24px;
				color: #1868f1;
				margin-bottom: -2px;
			}
		}

		.content_box_bottom {
			.el-radio-group {
				& > :nth-child(n) {
					height: 32px;
					margin-right: 10px;
				}
			}
		}
	}
}

.prosperity {
	width: 100%;

	.prosperity-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40px 40px 14px 40px;

		img {
			width: 50px;
			height: 50px;
		}

		.title {
			font-weight: 700;
			font-size: 20px;
			margin-top: 8px;
			margin-bottom: 16px;
			line-height: 32px;
			height: 32px;
			color: #1d2129;
		}

		.prompt-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			line-height: 22px;

			div {
				// margin: 5px 0;
				color: #4e5969;
				font-size: 14px;
			}
		}

		.el-button {
			margin-top: 18px;
			padding: 0 40px;
			width: 306px;
			height: 48px;
			background: #1868f1;
		}
	}
}

.qrcode {
	height: 136px !important;
	margin: -8px 0px 0 -8px;
	width: 136px !important;
}

.prosperityCoucher {
	width: 100%;
	height: 134px;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-top: 1px solid #e7e7e7;
	margin-bottom: 16px;
	.titleDetails {
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: center;
		color: #86909c;
		margin: 12px 0 10px 0;
	}
}
</style>
