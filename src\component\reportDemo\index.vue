<template>
	<div class="previewReport" v-if="dialogVisible">
		<div class="previewReportClose" @click="$emit('close')">
			<el-icon><Close /></el-icon>
		</div>
		<div class="previewReportContainer">
			<!-- 估值报告 -->
			<div ref="preview" class="preview" v-if="reportType === 'value'">
				<div v-for="(image, index) in valueList" :key="index">
					<img :src="image" alt="" />
				</div>
			</div>
			<!-- 半径人口报告 -->
			<div ref="preview" class="preview" v-if="reportType === 'population'">
				<div v-for="(image, index) in radiusList" :key="index">
					<img :src="image" alt="" />
				</div>
			</div>
			<!-- 客流量报告 -->
			<div ref="preview" class="preview" v-if="reportType === 'populationthree'">
				<div v-for="(image, index) in populationthree_demoList" :key="index">
					<img :src="image" alt="" />
				</div>
			</div>
			<!-- 租户 -->
			<div ref="preview" class="preview" v-if="reportType === 'business'">
				<div v-for="(image, index) in tenantList" :key="index">
					<img :src="image" alt="" />
				</div>
			</div>

			<!-- 市调报告 -->
			<div ref="preview" class="preview" v-if="reportType === 'location'">
				<div v-for="(image, index) in marketResearchList" :key="index">
					<img :src="image" alt="" />
				</div>
			</div>
			<!-- 社区人口报告 -->

			<div ref="preview" class="preview" v-if="reportType === 'populationtwo'">
				<div v-for="(image, index) in pdemoList" :key="index">
					<img :src="image" alt="" />
				</div>
			</div>

			<!-- 证券化报告 -->

			<div ref="preview" class="preview" v-if="reportType === 'security'">
				<div v-for="(image, index) in valuationSList" :key="index">
					<img :src="image" alt="" />
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { ref, defineExpose, nextTick } from 'vue';
import { ElLoading } from 'element-plus';
import html2pdf from 'html2pdf.js';
const preview = ref();
const props = defineProps({
	// 报告类型
	reportType: {
		type: String,
		default: '',
	},
	// 报告名称
	reportTypeName: {
		type: String,
		default: '',
	},
});
// 是否显示
const dialogVisible = ref(false);

// 社区人口
import communityPopulation1 from '../../assets/images/shangYutong/communityPopulation/populationtwodemo_page-0001.jpg';
import communityPopulation2 from '../../assets/images/shangYutong/communityPopulation/populationtwodemo_page-0002.jpg';
import communityPopulation3 from '../../assets/images/shangYutong/communityPopulation/populationtwodemo_page-0003.jpg';
const pdemoList = ref([communityPopulation1, communityPopulation2, communityPopulation3]);
// 市调
import marketResearch_01 from '../../assets/images/shangYutong/marketResearch/location_demo_01.jpg';
import marketResearch_02 from '../../assets/images/shangYutong/marketResearch/location_demo_02.jpg';
import marketResearch_03 from '../../assets/images/shangYutong/marketResearch/location_demo_03.jpg';
import marketResearch_04 from '../../assets/images/shangYutong/marketResearch/location_demo_04.jpg';
import marketResearch_05 from '../../assets/images/shangYutong/marketResearch/location_demo_05.jpg';
import marketResearch_06 from '../../assets/images/shangYutong/marketResearch/location_demo_06.jpg';
import marketResearch_07 from '../../assets/images/shangYutong/marketResearch/location_demo_07.jpg';
import marketResearch_08 from '../../assets/images/shangYutong/marketResearch/location_demo_08.jpg';
import marketResearch_09 from '../../assets/images/shangYutong/marketResearch/location_demo_09.jpg';
const marketResearchList = ref([
	marketResearch_01,
	marketResearch_02,
	marketResearch_03,
	marketResearch_04,
	marketResearch_05,
	marketResearch_06,
	marketResearch_07,
	marketResearch_08,
	marketResearch_09,
]);
// 租户
import tenant1 from '../../assets/images/shangYutong/tenant/office_demo01.png';
import tenant2 from '../../assets/images/shangYutong/tenant/office_demo02.png';
import tenant3 from '../../assets/images/shangYutong/tenant/office_demo03.png';
import tenant4 from '../../assets/images/shangYutong/tenant/office_demo04.png';
import tenant5 from '../../assets/images/shangYutong/tenant/office_demo05.png';
import tenant6 from '../../assets/images/shangYutong/tenant/office_demo06.png';
import tenant7 from '../../assets/images/shangYutong/tenant/office_demo07.png';
import tenant8 from '../../assets/images/shangYutong/tenant/office_demo08.png';
import tenant9 from '../../assets/images/shangYutong/tenant/office_demo09.png';
import tenant10 from '../../assets/images/shangYutong/tenant/office_demo10.png';

const tenantList = ref([tenant1, tenant2, tenant3, tenant4, tenant5, tenant6, tenant7, tenant8, tenant9, tenant10]);
// 半径人口
import radius1 from '../../assets/images/shangYutong/radius/population_demo_01.png';
import radius2 from '../../assets/images/shangYutong/radius/population_demo_02.png';
import radius3 from '../../assets/images/shangYutong/radius/population_demo_03.png';
const radiusList = ref([radius1, radius2, radius3]);
// 客流量
import populationthree_demo from '../../assets/images/shangYutong/passengerFlow/populationthree_demo.png';
const populationthree_demoList = ref([populationthree_demo]);

// 价值报告
import value_page1 from '../../assets/images/shangYutong/value/value_page-0001.jpg';
import value_page2 from '../../assets/images/shangYutong/value/value_page-0002.jpg';
import value_page3 from '../../assets/images/shangYutong/value/value_page-0003.jpg';
import value_page4 from '../../assets/images/shangYutong/value/value_page-0004.jpg';
import value_page5 from '../../assets/images/shangYutong/value/value_page-0005.jpg';
import value_page6 from '../../assets/images/shangYutong/value/value_page-0006.jpg';
import value_page7 from '../../assets/images/shangYutong/value/value_page-0007.jpg';
import value_page8 from '../../assets/images/shangYutong/value/value_page-0008.jpg';
import value_page9 from '../../assets/images/shangYutong/value/value_page-0009.jpg';
import value_page10 from '../../assets/images/shangYutong/value/value_page-0010.jpg';
import value_page11 from '../../assets/images/shangYutong/value/value_page-0011.jpg';
import value_page12 from '../../assets/images/shangYutong/value/value_page-0012.jpg';
import value_page13 from '../../assets/images/shangYutong/value/value_page-0013.jpg';
import value_page14 from '../../assets/images/shangYutong/value/value_page-0014.jpg';
import value_page15 from '../../assets/images/shangYutong/value/value_page-0015.jpg';
import value_page16 from '../../assets/images/shangYutong/value/value_page-0016.jpg';
import value_page17 from '../../assets/images/shangYutong/value/value_page-0017.jpg';
import value_page18 from '../../assets/images/shangYutong/value/value_page-0018.jpg';
import value_page19 from '../../assets/images/shangYutong/value/value_page-0019.jpg';
import value_page20 from '../../assets/images/shangYutong/value/value_page-0020.jpg';
import value_page21 from '../../assets/images/shangYutong/value/value_page-0021.jpg';
import value_page22 from '../../assets/images/shangYutong/value/value_page-0022.jpg';
import value_page23 from '../../assets/images/shangYutong/value/value_page-0023.jpg';
import value_page24 from '../../assets/images/shangYutong/value/value_page-0024.jpg';
import value_page25 from '../../assets/images/shangYutong/value/value_page-0025.jpg';
import value_page26 from '../../assets/images/shangYutong/value/value_page-0026.jpg';
import value_page27 from '../../assets/images/shangYutong/value/value_page-0027.jpg';
import value_page28 from '../../assets/images/shangYutong/value/value_page-0028.jpg';
import value_page29 from '../../assets/images/shangYutong/value/value_page-0029.jpg';
import value_page30 from '../../assets/images/shangYutong/value/value_page-0030.jpg';
import value_page31 from '../../assets/images/shangYutong/value/value_page-0031.jpg';
import value_page32 from '../../assets/images/shangYutong/value/value_page-0032.jpg';
import value_page33 from '../../assets/images/shangYutong/value/value_page-0033.jpg';
import value_page34 from '../../assets/images/shangYutong/value/value_page-0034.jpg';
import value_page35 from '../../assets/images/shangYutong/value/value_page-0035.jpg';

const valueList = ref([
	value_page1,
	value_page2,
	value_page3,
	value_page4,
	value_page5,
	value_page6,
	value_page7,
	value_page8,
	value_page9,
	value_page10,
	value_page11,
	value_page12,
	value_page13,
	value_page14,
	value_page15,
	value_page16,
	value_page17,
	value_page18,
	value_page19,
	value_page20,
	value_page21,
	value_page22,
	value_page23,
	value_page24,
	value_page25,
	value_page26,
	value_page27,
	value_page28,
	value_page29,
	value_page30,
	value_page31,
	value_page32,
	value_page33,
	value_page34,
	value_page35,
]);
// 证券化
import valuationS1 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0001.jpg';
import valuationS2 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0002.jpg';
import valuationS3 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0003.jpg';
import valuationS4 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0004.jpg';
import valuationS5 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0005.jpg';
import valuationS6 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0006.jpg';
import valuationS7 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0007.jpg';
import valuationS8 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0008.jpg';
import valuationS9 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0009.jpg';
import valuationS10 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0010.jpg';
import valuationS11 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0011.jpg';
import valuationS12 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0012.jpg';
import valuationS13 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0013.jpg';
import valuationS14 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0014.jpg';
import valuationS15 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0015.jpg';
import valuationS16 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0016.jpg';
import valuationS17 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0017.jpg';
import valuationS18 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0018.jpg';
import valuationS19 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0019.jpg';
import valuationS20 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0020.jpg';
import valuationS21 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0021.jpg';
import valuationS22 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0022.jpg';
import valuationS23 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0023.jpg';
import valuationS24 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0024.jpg';
import valuationS25 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0025.jpg';
import valuationS26 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0026.jpg';
import valuationS27 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0027.jpg';
import valuationS28 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0028.jpg';
import valuationS29 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0029.jpg';
import valuationS30 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0030.jpg';
import valuationS31 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0031.jpg';
import valuationS32 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0032.jpg';
import valuationS33 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0033.jpg';
import valuationS34 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0034.jpg';
import valuationS35 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0035.jpg';
import valuationS36 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0036.jpg';
import valuationS37 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0037.jpg';
import valuationS38 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0038.jpg';
import valuationS39 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0039.jpg';
import valuationS40 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0040.jpg';
import valuationS41 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0041.jpg';
import valuationS42 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0042.jpg';
import valuationS43 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0043.jpg';
import valuationS44 from '../../assets/images/shangYutong/valuationSecuritization/security-demo_page-0044.jpg';

const valuationSList = ref([
	valuationS1,
	valuationS2,
	valuationS3,
	valuationS4,
	valuationS5,
	valuationS6,
	valuationS7,
	valuationS8,
	valuationS9,
	valuationS10,
	valuationS11,
	valuationS12,
	valuationS13,
	valuationS14,
	valuationS15,
	valuationS16,
	valuationS17,
	valuationS18,
	valuationS19,
	valuationS20,
	valuationS21,
	valuationS22,
	valuationS23,
	valuationS24,
	valuationS25,
	valuationS26,
	valuationS27,
	valuationS28,
	valuationS29,
	valuationS30,
	valuationS31,
	valuationS32,
	valuationS33,
	valuationS34,
	valuationS35,
	valuationS36,
	valuationS37,
	valuationS38,
	valuationS39,
	valuationS40,
	valuationS41,
	valuationS42,
	valuationS43,
	valuationS44,
]);

const generateReport = async () => {
	nextTick(() => {
		dialogVisible.value = true;
	});
};

defineExpose({ generateReport, dialogVisible });
</script>
<style lang="scss" scoped>
.preview {
	img {
		width: 100%;
		object-fit: cover;
	}
}

.previewReport {
	position: fixed;
	top: 0px;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: auto;
	z-index: 999999;
	background: #fff;
	.previewReportClose {
		position: fixed;
		top: 10px;
		right: 10px;
		cursor: pointer;
		i {
			font-size: 35px;
		}
	}

	.previewReportContainer {
		width: 80%;
		padding: 20px 0;
		margin: 0 auto;
		animation: fadeIn 1.5s;
		@keyframes fadeIn {
			0% {
				opacity: 0;
			}
			100% {
				opacity: 1;
			}
		}
	}
}
</style>
