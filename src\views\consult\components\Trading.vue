<template>
	<div>
		<div class="title_box">
			<a class="AAA" style="margin: 15px">交易材料</a>
		</div>
		<div class="tab">
			<el-tabs type="border-card" v-model="activeName" @tab-click="handleTabClick">
				<el-tab-pane v-for="route in routes" :label="route.name" :name="route.name" :key="route.name"> </el-tab-pane>
			</el-tabs>
		</div>
		<div>
			<Transition><router-view /></Transition>
		</div>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter();
const routes = [
	{
		path: '/trading/trading-calculation',
		name: '交易计算',
	},
	{
		path: '/trading/value-comparison',
		name: '价值对比',
	},

	{
		path: '/trading/octagonal-arena',
		name: ' 六角擂台',
	},
	{
		path: '/trading/tenant-population',
		name: '人口对比',
	},
	{
		path: '/trading/broker-search',
		name: '查经纪人',
	},

	{
		path: '/trading/tenant-statistics',
		name: '租户比对',
	},
	{
		path: '/trading/property-owner',
		name: '查产权人',
	},
	{
		path: '/trading/management-personnel',
		name: '查管理人',
	},
	{
		path: '/trading/floor-plan',
		name: '户型图',
	},
	{
		path: '/trading/transaction-history',
		name: '交易历史',
	},
];
const activeName = ref('交易计算');
const handleTabClick = (activeName) => {
	const selectedRoute = routes.find((route) => route.name === activeName.props.name);
	console.log(selectedRoute, 'name');
	if (selectedRoute) {
		console.log('Tab clicked:', activeName);
		router.replace(selectedRoute.path);
	}
};
</script>

<style lang="scss" scoped>
.v-enter-active,
.v-leave-active {
	transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
	opacity: 0;
}
.el-tabs--border-card > :deep(.el-tabs__content) {
	padding: 0px;
}
.el-tabs--border-card :deep(.el-tabs__item) {
	font-size: 16px;
}
.title_box {
	display: flex;
	margin-top: 2px;
	margin-left: 19px;
	.AAA {
		text-decoration: none;
		color: #000;
		font-size: 24px;
		font-weight: bold;
	}
	.el-link {
		text-decoration: none;
		font-size: 16px;
		border: #000 1px solid;
		border-radius: 15px;
		margin: 15px;
		padding: 10px;
	}
}
</style>
