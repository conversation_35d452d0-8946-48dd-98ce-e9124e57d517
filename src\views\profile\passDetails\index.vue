<template>
	<div class="rights-details-container">
		<div class="container_top">
			<el-icon @click="goBack" class="container_top_elion"><ArrowLeftBold /></el-icon>
			<div @click="goBack" class="container_top_title">权益卡券详情</div>
			<div class="container_top_line"></div>
		</div>
		<div style="height: calc(100% - 100px)">
			<div class="contaner_content_">
				<div
					class="details-box"
					:class="`${
						data.objCrad.cardStatus === '1' || data.objCrad.cardStatus === '3'
							? 'type_2'
							: data.objCrad.cardStatus == '2'
							? 'type_1'
							: data.objCrad.cardStatus == 'newUser'
							? 'newUser'
							: 'status_3'
					}`"
				>
					<div class="operate">
						<div class="status"></div>
						<div class="button-data">
							<div class="button_top_content">
								<div>{{ data.objCrad.name }}</div>
							</div>
							<div class="button_content_two">{{ data.objCrad.periodName }}</div>
						</div>
					</div>
					<div class="center_line"></div>
					<div class="right-content">
						<div class="status_name">{{ data.objCrad.statusName }}</div>
					</div>
				</div>
			</div>

			<div class="t-box">
				<div
					class="Rights-box"
					v-for="(item, index) in RightsData"
					:style="{ display: data.objCrad.type === 'SINGLE' && index === 2 ? 'none' : '' }"
					:key="index"
				>
					<div class="name">{{ item.name }}</div>
					<div class="value">{{ item.value }}</div>
				</div>
			</div>
			<div class="contain-rights">
				<div class="name">包含权益：</div>
				<div class="value">
					{{ data.objCrad.description }}
				</div>
			</div>
		</div>
	</div>
</template>
<script setup>
import { reactive, onMounted } from 'vue';
const emit = defineEmits(['handleComponentClose']);
const props = defineProps({
	// 当前卡卷对象
	params: {
		type: Object,
		default: {},
	},
});

let data = reactive({
	objCrad: {},
});

const RightsData = reactive([
	{
		name: '卡券类型：',
		typeName: 'name',
		value: '',
	},
	{
		name: '适用用户',
		typeName: 'Suitable',
		value: '当前登录用户',
	},
	{
		name: '购买时间：',
		typeName: 'buyTime',
		value: '',
	},
	{
		name: '购买时长：',
		typeName: 'periodName',
		value: '',
	},

	{
		name: '卡券状态：',
		typeName: 'statusName',
		value: '',
	},
	{
		name: '使用时间：',
		typeName: 'useTime',
		value: '',
	},
	{
		name: '剩余时长：',
		typeName: 'expirationTime',
		value: '',
	},
]);

onMounted(() => {
	if (!props.params) return;
	// 处理数据
	handleProcessing();
});

// 处理数据
function handleProcessing() {
	data.objCrad = props.params;
	RightsData.forEach((item) => {
		if (data.objCrad[item.typeName]) {
			item.value = data.objCrad[item.typeName];
		}
	});
}

const goBack = () => {
	emit('handleComponentClose');
};
</script>

<style lang="less" scoped>
@import url('./style.less');
</style>
