<template>
	<div class="content">
		<!-- <div class="title">租户对比</div> -->
		<div class="container_box">
			<div class="table_main">
				<div class="table_">
					<div class="top_boxFirst">
						<div class="tag_boxTitle">对比资产一</div>
						<div class="tag_boxCenter">
							<div class="tag_boxRight" v-if="tableDatao[0]" @click="clearDate(tableDatao[0], 1)">× 清空</div>
							<!-- <div class="tag_boxLeft" v-if="tableDatao.length > 0" @click="handleDownload(tableDatao[0].id, 'business')">
								<el-icon style="margin-right: 2px"><Download /></el-icon>招商报告
							</div> -->
						</div>
					</div>
					<div class="table_1" v-if="tableDatao[0]">
						<el-table border :data="tableDatao" height="80px" style="width: 100%">
							<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
							<el-table-column prop="buildingType" label="资产类型" width="" />
							<el-table-column prop="street" label="地址" show-overflow-tooltip>
								<template #default="scope">
									{{
										scope.row?.city && scope.row?.district
											? scope.row?.city + scope.row?.district + scope.row?.street
											: scope.row?.buildingCity + scope.row?.buildingDistrict + scope.row?.buildingStreet
									}}
								</template>
							</el-table-column>
							<el-table-column prop="buildingSize" label="建筑面积" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.buildingSize ? formattedMoney(scope.row?.buildingSize, 2) + '㎡' : '' }}
									</div>
								</template>
							</el-table-column>
							<el-table-column prop="maintenance" label="维护情况" width="" />
							<el-table-column prop="absoluteValue" label="单价" width="">
								<template #default="scope">
									<div style="text-align: center; width: max-content">
										{{ scope.row?.absoluteValue ? formattedMoney(scope.row?.absoluteValue, 2) + '元' : '' }}
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
					<div class="add active" @click="choose(1)" v-else>+ 选择对比资产</div>
					<!-- <div class="clear active" v-if="tableDatao.length > 0" @click="clearDate(1)">× 清空</div> -->
				</div>
				<div class="table_">
					<div class="top_box">
						<div class="top_boxFirst">
							<div class="tag_boxTitle">对比资产二</div>
							<div class="tag_boxCenter">
								<div class="tag_boxRight" v-if="tableDatat[0]" @click="clearDate(tableDatat[0], 2)">× 清空</div>
								<!-- <div class="tag_boxLeft" v-if="tableDatat.length > 0" @click="handleDownload(tableDatat[0].id, 'business')">
									<el-icon style="margin-right: 2px"><Download /></el-icon>招商报告
								</div> -->
							</div>
						</div>
						<div class="table_1" v-if="tableDatat[0]">
							<el-table :data="tableDatat" border height="80px" style="width: 100%">
								<el-table-column prop="buildingName" label="资产名称" width="" show-overflow-tooltip />
								<el-table-column prop="buildingType" label="资产类型" width="" />
								<el-table-column prop="street" label="地址" show-overflow-tooltip>
									<template #default="scope">
										{{
											scope.row?.city && scope.row?.district
												? scope.row?.city + scope.row?.district + scope.row?.street
												: scope.row?.buildingCity + scope.row?.buildingDistrict + scope.row?.buildingStreet
										}}
									</template>
								</el-table-column>
								<el-table-column prop="buildingSize" label="建筑面积" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.buildingSize ? formattedMoney(scope.row.buildingSize, 2) + '㎡' : '' }}
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="maintenance" label="维护情况" width="" />
								<el-table-column prop="absoluteValue" label="单价" width="">
									<template #default="scope">
										<div style="text-align: center; width: max-content">
											{{ scope.row?.absoluteValue ? formattedMoney(scope.row.absoluteValue, 2) + '元' : '' }}
										</div>
									</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="add active" @click="choose(2)" v-else>+ 选择对比资产</div>
						<!-- <div class="clear active" v-if="tableDatat.length > 0" @click="clearDate(2)">× 清空</div> -->
					</div>
				</div>
			</div>
			<!-- 对比图 -->
			<div class="echars_box">
				<div class="tag_box">
					租户对比
					<!-- <span v-if="tableDatao.length > 0">{{ tableDatao[0].buildingName }}</span -->
					<!-- ><span v-if="tableDatat.length > 0">{{ tableDatat[0].buildingName }}</span> -->
				</div>
				<div class="echars_main">
					<div
						class="box_"
						v-if="tableDatao[0]"
						:style="{ width: tableDatao[0] && tableDatat[0] ? 'calc(50% - 8px)' : tableDatao[0] ? '100%' : 'calc(50% - 8px)' }"
					>
						<div class="title1">{{ tableDatao[0]?.buildingName }}</div>
						<div class="tips">
							{{ leftText?.text1 }}
							{{ leftText?.text2 }}
							{{ leftText?.text3 }}
							{{ leftText?.text4 }}
						</div>
						<div class="chartBox">
							<div class="title2">租户构成占比</div>
							<div class="pie_box">
								<echartPie :pieData="left_pie_data"></echartPie>
								<div class="down_box" v-if="left_pie_data">
									<span v-if="left_pie_data.data.length > 0">{{ left_pie_data.data[0].name }}</span>
									<span v-if="left_pie_data.data.length > 0">{{ left_pie_data.data[1].name }}</span>
									<span v-if="left_pie_data.data.length > 0">{{ left_pie_data.data[2].name }}</span>
									<span v-if="left_pie_data.data.length > 0">{{ left_pie_data.data[3].name }}</span>
									<span v-if="left_pie_data.data.length > 0">{{ left_pie_data.data[4].name }}</span>
								</div>
							</div>
						</div>
						<div class="tips_box" v-if="left_pie_data">
							<div class="title2">租户构成详细信息</div>
							<div class="detail">
								<div class="title3">{{ left_pie_data.data[0].name }}：{{ left_pie_data.data[0].value }}%</div>
								<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituInfoTech" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'telecomBroadcast'">电信广播</div>
									<div class="lable_" v-else-if="index === 'internet'">互联网</div>
									<div class="lable_" v-else-if="index === 'softInfoTech'">软件和信息技术</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(53, 125, 255, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
							<div class="detail">
								<div class="title3">{{ left_pie_data.data[1].name }}：{{ left_pie_data.data[1].value }}%</div>
								<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituFinance" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'monetaryFinance'">货币金融</div>
									<div class="lable_" v-else-if="index === 'capitalMarket'">资本市场</div>
									<div class="lable_" v-else-if="index === 'insurance'">保险业</div>
									<div class="lable_" v-else-if="index === 'otherFinance'">其他金融</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(30, 170, 117, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
							<div class="detail">
								<div class="title3">{{ left_pie_data.data[2].name }}：{{ left_pie_data.data[2].value }}%</div>
								<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituLeaseBusiness" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'lease'">租赁</div>
									<div class="lable_" v-else-if="index === 'businessServices'">商务服务</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(255, 114, 116, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
							<div class="detail">
								<div class="title3">{{ left_pie_data.data[3].name }}：{{ left_pie_data.data[3].value }}%</div>
								<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituWholesaleRetail" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'wholesale'">批发</div>
									<div class="lable_" v-else-if="index === 'retail'">零售</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(250, 212, 24, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
							<div class="detail">
								<div class="title3">{{ left_pie_data.data[3].name }}：{{ left_pie_data.data[3].value }}%</div>
								<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituOther" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'realEstate'">房地产</div>
									<div class="lable_" v-else-if="index === 'fabricate'">制造</div>
									<div class="lable_" v-else-if="index === 'transportation'">交通运输</div>
									<div class="lable_" v-else-if="index === 'scienceResearch'">科学研究</div>
									<div class="lable_" v-else-if="index === 'other'">其他</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(251, 157, 108, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
						</div>
					</div>
					<div
						class="box_"
						v-if="tableDatat[0]"
						:style="{ width: tableDatat[0] && tableDatao[0] ? 'calc(50% - 8px)' : tableDatat[0] ? '100%' : 'calc(50% - 8px)' }"
					>
						<div class="title1">{{ tableDatat[0]?.buildingName }}</div>
						<div class="tips">
							{{ rightText?.text1 }}
							{{ rightText?.text2 }}
							{{ rightText?.text3 }}
							{{ rightText?.text4 }}
						</div>
						<div class="chartBox">
							<div class="title2">租户构成占比</div>
							<div class="pie_box">
								<echartPie :pieData="right_pie_data"></echartPie>
								<div class="down_box" v-if="right_pie_data">
									<span v-if="right_pie_data.data.length > 0">{{ right_pie_data.data[0].name }}</span>
									<span v-if="right_pie_data.data.length > 0">{{ right_pie_data.data[1].name }}</span>
									<span v-if="right_pie_data.data.length > 0">{{ right_pie_data.data[2].name }}</span>
									<span v-if="right_pie_data.data.length > 0">{{ right_pie_data.data[3].name }}</span>
									<span v-if="right_pie_data.data.length > 0">{{ right_pie_data.data[4].name }}</span>
								</div>
							</div>
						</div>
						<div class="tips_box" v-if="right_pie_data">
							<div class="title2">租户构成详细信息</div>

							<div class="detail">
								<div class="title3">{{ right_pie_data.data[0].name }}：{{ right_pie_data.data[0].value }}%</div>
								<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituInfoTech" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'telecomBroadcast'">电信广播</div>
									<div class="lable_" v-else-if="index === 'internet'">互联网</div>
									<div class="lable_" v-else-if="index === 'softInfoTech'">软件和信息技术</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(53, 125, 255, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
							<div class="detail">
								<div class="title3">{{ right_pie_data.data[1].name }}：{{ right_pie_data.data[1].value }}%</div>
								<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituFinance" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'monetaryFinance'">货币金融</div>
									<div class="lable_" v-else-if="index === 'capitalMarket'">资本市场</div>
									<div class="lable_" v-else-if="index === 'insurance'">保险业</div>
									<div class="lable_" v-else-if="index === 'otherFinance'">其他金融</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(30, 170, 117, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
							<div class="detail">
								<div class="title3">{{ right_pie_data.data[2].name }}：{{ right_pie_data.data[2].value }}%</div>
								<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituLeaseBusiness" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'lease'">租赁</div>
									<div class="lable_" v-else-if="index === 'businessServices'">商务服务</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(255, 114, 116, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
							<div class="detail">
								<div class="title3">{{ right_pie_data.data[3].name }}：{{ right_pie_data.data[3].value }}%</div>
								<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituWholesaleRetail" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'wholesale'">批发</div>
									<div class="lable_" v-else-if="index === 'retail'">零售</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(250, 212, 24, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
							<div class="detail">
								<div class="title3">{{ right_pie_data.data[3].name }}：{{ right_pie_data.data[3].value }}%</div>
								<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituOther" :key="item" v-show="index !== 'total'">
									<div class="lable_" v-if="index === 'realEstate'">房地产</div>
									<div class="lable_" v-else-if="index === 'fabricate'">制造</div>
									<div class="lable_" v-else-if="index === 'transportation'">交通运输</div>
									<div class="lable_" v-else-if="index === 'scienceResearch'">科学研究</div>
									<div class="lable_" v-else-if="index === 'other'">其他</div>
									<div class="progress">
										<div class="line" :style="{ width: (item ? item : 0) + '%', backgroundColor: 'rgba(251, 157, 108, 1)' }"></div>
									</div>
									<div class="num">{{ Number(item) }}%</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="btn_box" v-if="handlerBtnBox().length > 0">
				{{ handlerBtnBox() }}
				<div class="box_copy" @click="handlerCopy()">复制</div>
			</div>
		</div>
	</div>
	<el-dialog v-model="dialogTableVisible" width="800" title="选择对比资产" :close-on-click-modal="false">
		<div class="title_box">
			<div class="tab" v-for="(item, index) in multipleSelection" :key="index">
				<div style="text-wrap: nowrap">对比资产</div>
				{{ $utils.chineseNumber(index) }}：
				<div :title="item.buildingName" style="text-wrap: nowrap; overflow: hidden; width: 116px; text-overflow: ellipsis; white-space: nowrap">
					{{ item.buildingName }}
				</div>
				<div class="det" @click="clearDate(item, 0, index)">×</div>
			</div>
		</div>
		<div class="search_box">
			<div class="box_1">
				<div class="label">城市</div>
				<el-cascader
					placeholder="请选择城市"
					v-model="selectedCity"
					:options="$vuexStore.state.cityArray"
					@change="handleChange"
					:props="{ value: 'label' }"
				>
				</el-cascader>
			</div>
			<div class="box_1">
				<div class="label">资产评级</div>
				<el-select v-model="degree" placeholder="全部资产评级">
					<el-option v-for="(item, value) in rate" :key="value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">资产类型</div>
				<el-select v-model="buildValue" placeholder="资产类型">
					<el-option v-for="item in buildingTypes" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
			</div>
			<div class="box_1">
				<div class="label">关键词</div>
				<el-input v-model="keyword" placeholder="请输入关键字"></el-input>
			</div>
			<div class="box_2">
				<el-button type="primary" @click="searchs()">查询</el-button>
				<el-button type="primary" @click="reset()">重置</el-button>
			</div>
		</div>
		<div class="table_2">
			<el-table :data="tableData" style="width: 100%" height="308px" border ref="multipleTableRef" @selection-change="handleSelectionChange" stripe>
				<el-table-column type="selection" width="55" />
				<el-table-column
					v-for="(column, index) in tableColumns"
					:key="index"
					:label="column.label"
					:prop="column.prop"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table>
		</div>

		<el-pagination
			@current-change="handleCurrentChange"
			:current-page="currentPage"
			small
			background
			layout="prev, pager, next"
			class="mt-4"
			:total="total"
		/>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogTableVisible = false">取消</el-button>
				<el-button type="primary" @click="save()"> 确定 </el-button>
			</span>
		</template>
	</el-dialog>
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>
<script setup>
import { nextTick, onMounted, ref } from 'vue';
import getReport from '../../../../component/getReport/index.vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { getBuildingListByMultiCondition, getComparativeTenant, getDictList } from '@/api/syt.js';
import echartPie from './echart/pie.vue';
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const city = ref('');
const county = ref('');
const getReportRef = ref();
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const totalCount = ref(null);
const totalCountTwo = ref(null);
const rate = ref([]);
const selectedCity = ref([]);
const buildingTypes = ref([
	{
		value: '1',
		text: '写字楼',
		label: '写字楼',
		title: '写字楼',
	},
	// {
	// 	value: '2',
	// 	text: '零售',
	// 	label: '零售',
	// 	title: '零售',
	// },
	{
		value: '3',
		text: '产业园区',
		label: '产业园区',
		title: '产业园区',
	},
]);
const buildValue = ref('');
const buildValueDefault = ref('1,3');
const keyword = ref('');
const tableData = ref([]);
const currentPage = ref(1);
const total = ref('');
const loading = ref();
const tableData2 = ref([]);
const tableDatao = ref([]);
const tableDatat = ref([]);
const degree = ref('');
const dialogTableVisible = ref(false); //对话框显示
const multipleTableRef = ref(null);
const tableColumns = [
	{
		label: '资产名称',
		prop: 'buildingName',
	},
	{
		label: '资产类型',
		prop: 'buildingType',
	},
];
let left_pie_data = ref(null);
let right_pie_data = ref(null);

onMounted(() => {
	if (props.assetsIds) {
		// 判断资产类型
		if (props.assetsIds && props.assetsIds?.arr?.length > 0) {
			let state = true;
			props.assetsIds.arr.forEach((element) => {
				if (element.buildingType !== '写字楼' && element.buildingType !== '产业园区') {
					state = false;
				}
			});
			if (!state) {
				return;
			}
			handleOpenFullScreen(); //加载
			handleAssetsIds(props.assetsIds);
		}
	}
});

//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingPopulation',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getComparativeTenant({
		buildingIds: obj.ids,
	})
		.then((res) => {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			tableDatao.value.push(obj.arr[0]);
			tableDatat.value.push(obj.arr[1]);
			
			if (tableDatao.value[0].id == res.data[0].id) {
				setTimeout(() => {
					leftText.value = res.data[0].tenantCensusText;
					rightText.value = res.data[1]?.tenantCensusText;
					multipleSelection.value = obj.arr;
					leftData.value = res.data[0];
					rightData.value = res.data[1];
					totalCount.value = res.data[0].totalCount;
					totalCountTwo.value = res.data[1]?.totalCount;
					setData_pie(res.data[0], res.data?.[1]);
					loading.value.close();
				}, 500);
			} else {
				setTimeout(() => {
					leftText.value = res.data[1]?.tenantCensusText;
					rightText.value = res.data[0].tenantCensusText;
					leftData.value = res.data?.[1];
					rightData.value = res.data[0];
					totalCount.value = res.data[1]?.totalCount;
					totalCountTwo.value = res.data[0].totalCount;

					setData_pie(res.data?.[1], res.data[0]);
					loading.value.close();
				}, 500);
			}
		})
		.catch((err) => {
			console.log(err, 'err');
		});
}

//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}
const handleChange = (val) => {
	city.value = val[0];
	county.value = val[1];
};
const reset = () => {
	city.value = '';
	degree.value = '';
	buildValue.value = '';
	selectedCity.value = [];
	county.value = '';
	keyword.value = '';
	currentPage.value = 1;
	search();
};

function searchs() {
	currentPage.value = 1;
	search();
}

const search = async () => {
	const queryParams = {
		city: city.value,
		buildingRate: degree.value,
		district: county.value,
		buildingType: buildValue.value || buildValueDefault.value,
		keyword: keyword.value,
		currentPage: currentPage.value,
		// year: 2024,
		pageSize: 10,
	};
	await getBuildingListByMultiCondition(queryParams)
		.then((res) => {
			tableData.value = res.data.rows;
			nextTick(() => {
				tableData.value.map((v) => {
					multipleSelection.value.map((i) => {
						if (v.id == i.id) {
							multipleTableRef.value.toggleRowSelection(v, true);
						}
					});
				});
			});
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const handleCurrentChange = (val) => {
	currentPage.value = val;
	search();
};
const multipleSelection = ref([]);
let leftData = ref();
let rightData = ref();
const handleSelectionChange = (val) => {
	setTimeout(() => {
		let mergedSet = new Set([...multipleSelection.value, ...val]);
		let mergedArray = Array.from(mergedSet);
		let uniqueById = Array.from(new Set(mergedArray.map((item) => item.id))).map((id) => {
			return mergedArray.find((item) => item.id === id);
		});
		// 当前页 tableData.value
		// 当前页选中 val
		// 当前页选中和之前选中的重复的去掉的 uniqueById
		tableData.value.map((item) => {
			uniqueById.map((uniqueItem, index) => {
				if (item.id == uniqueItem.id) {
					const foundInVal = val.some((v) => v.id === uniqueItem.id);
					if (!foundInVal) {
						uniqueById.splice(index, 1);
					}
				}
			});
		});
		multipleSelection.value = uniqueById;
	}, 100);
};
const leftText = ref('');
const rightText = ref('');
const leftName = ref('');
const rightName = ref('');
const comparison = async () => {
	left_pie_data.value = null;
	right_pie_data.value = null;
	const idarray = [];
	multipleSelection.value.map((item) => idarray.push(item.id));
	const ids = idarray.join(',');
	await getComparativeTenant({
		buildingIds: ids,
	})
		.then((res) => {
			emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
			if (tableDatao.value[0].id == res.data[0].id) {
				nextTick(() => {
					leftText.value = res.data[0].tenantCensusText;
					leftData.value = res.data[0];
					totalCount.value = res.data[0].totalCount;
					rightText.value = res.data?.[1]?.tenantCensusText;
					rightData.value = res.data?.[1];
					totalCountTwo.value = res.data?.[1]?.totalCount;
					setData_pie(res.data[0], res.data?.[1]);
				});
			} else {
				nextTick(() => {
					rightText.value = res.data[0].tenantCensusText;
					rightData.value = res.data[0];
					totalCountTwo.value = res.data[0].totalCount;
					leftText.value = res.data?.[1]?.tenantCensusText;
					leftData.value = res.data?.[1];
					totalCount.value = res.data?.[1]?.totalCount;
					setData_pie(res.data?.[1], res.data?.[0]);
				});
			}
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
//插入数据
const setData_pie = (left, right) => {
	if (left) {
		left_pie_data.value = {
			name: left.buildingName,
			data: [
				{
					value: left.tenantConstituInfoTech.total,
					name: '招商写字楼租户构成信息技术占比',
					itemStyle: {
						color: 'rgba(4, 80, 218, 1)',
					},
				},
				{
					value: left.tenantConstituFinance.total,
					name: '	招商写字楼租户构成金融占比',
					itemStyle: {
						color: 'rgba(30, 170, 117, 1)',
					},
				},
				{
					value: left.tenantConstituLeaseBusiness.total,
					name: '招商写字楼租户构成租赁和商务服务占比',
					itemStyle: {
						color: 'rgba(255, 114, 116, 1)',
					},
				},
				{
					value: left.tenantConstituWholesaleRetail.total,
					name: '招商写字楼租户构成批发和零售占比',
					itemStyle: {
						color: 'rgba(250, 212, 24, 1)',
					},
				},
				{
					value: left.tenantConstituOther.total,
					name: '招商写字楼租户构成批发和零售占比',
					itemStyle: {
						color: 'rgba(251, 157, 108, 1)',
					},
				},
			],
		};
	}

	if (right) {
		right_pie_data.value = {
			name: right.buildingName,
			data: [
				{
					value: right.tenantConstituInfoTech.total,
					name: '招商写字楼租户构成信息技术占比',
					itemStyle: {
						color: 'rgba(4, 80, 218, 1)',
					},
				},
				{
					value: right.tenantConstituFinance.total,
					name: '招商写字楼租户构成金融占比',
					itemStyle: {
						color: 'rgba(30, 170, 117, 1)',
					},
				},
				{
					value: right.tenantConstituLeaseBusiness.total,
					name: '招商写字楼租户构成租赁和商务服务占比',
					itemStyle: {
						color: 'rgba(255, 114, 116, 1)',
					},
				},
				{
					value: right.tenantConstituWholesaleRetail.total,
					name: '	招商写字楼租户构成批发和零售占比',
					itemStyle: {
						color: 'rgba(250, 212, 24, 1)',
					},
				},
				{
					value: right.tenantConstituOther.total,
					name: '招商写字楼租户构成批发和零售占比',
					itemStyle: {
						color: 'rgba(251, 157, 108, 1)',
					},
				},
			],
		};
	}

	dialogTableVisible.value = false;
};
const pageChange = (val) => {
	currentPage.value = val;
	search();
};
const toggleSelection = (rows, isSelect) => {
	if (rows) {
		rows.forEach((row) => {
			multipleTableRef.value.toggleRowSelection(row, undefined, isSelect);
		});
	} else {
		multipleTableRef.value.clearSelection();
	}
};
// 点击选择对比资产
const choose = (item) => {
	// multipleTableRef.clearSelection();
	search();
	dialogTableVisible.value = true;
	// 清空上一次选的
	// if (multipleSelection.value.length > 0) {
	// 	toggleSelection(multipleSelection.value, true);
	// }
};

// 清空
function handelClear(row) {
	multipleSelection.value.forEach((item, indexs) => {
		if (item.id == row.id) {
			multipleSelection.value.splice(indexs, 1);
		}
	});
	save();
}

//清空资产选项
const clearDate = (row, type, index) => {
	if (type === 1) {
		//资产一清空
		tableDatao.value = [];
		handelClear(row);
	} else if (type === 2) {
		//资产二清空
		tableDatat.value = [];
		handelClear(row);
	} else {
		// 弹窗内资产清空
		multipleSelection.value.splice(index, 1);
	}
	if (tableData.value?.length > 0) {
		// 删除table选中的数据后，清空table选中的数据
		tableData.value.forEach((item) => {
			if (item.id == row.id) {
				toggleSelection([row]);
			}
		});
	}
};
const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		ElMessage({
			message: multipleSelection.value.length == 0 ? '至少选一个资产' : '最多选择两个资产',
			type: 'error',
		});
	} else {
		// 两个数组 一个数组里面放一个
		let list1 = [];
		let list2 = [];
		list1.push(multipleSelection.value[0]);
		list2.push(multipleSelection.value[1]);
		tableDatao.value = list1;
		tableDatat.value = list2;

		comparison();
	}
};
const getDict = async () => {
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();

function handlerCopy() {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(handlerBtnBox())
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = handlerBtnBox();
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

function handlerBtnBox() {
	let obj = rightData.value;
	let obj1 = leftData.value;
	if (!obj || !obj1) return '';
	if (!tableDatat.value[0] || !tableDatao.value[0]) return '';

	if (totalCountTwo.value > totalCount.value) {
		let name = null;
		let lastName = '';
		let lastNames = '租户构成一级行业';

		name =
			tableDatat.value[0].buildingName +
			'的累计租户数量较' +
			tableDatao.value[0].buildingName +
			'多' +
			(totalCountTwo.value - totalCount.value) +
			'户，';

		if (obj.tenantConstituInfoTech.total > obj1.tenantConstituInfoTech.total) {
			lastName += '招商写字楼租户构成信息技术占比，';
		}

		if (obj.tenantConstituFinance.total > obj1.tenantConstituFinance.total) {
			lastName += '招商写字楼租户构成金融占比，';
		}

		if (obj.tenantConstituLeaseBusiness.total > obj1.tenantConstituLeaseBusiness.total) {
			lastName += '招商写字楼租户构成租赁和商务服务占比，';
		}

		if (obj.tenantConstituWholesaleRetail.total > obj1.tenantConstituWholesaleRetail.total) {
			lastName += '招商写字楼租户构成批发和零售占比，';
		}

		if (obj.tenantConstituOther.total > obj1.tenantConstituOther.total) {
			lastName += '招商写字楼租户构成批发和零售占比';
		}

		if (lastName) {
			lastName += '高于' + tableDatat.value[0].buildingName + '，租户构成分布更为均衡。';
			return name + lastNames + lastName;
		} else {
			lastName = '占比高于' + tableDatat.value[0].buildingName + '，租户构成分布更为均衡。';
			return name + lastNames + lastName;
		}
	} else {
		if (totalCountTwo.value === totalCount.value) {
			let name = null;
			let lastName = '';
			let lastNames = '租户构成一级行业';
			name = tableDatao.value[0].buildingName + '的累计租户数量与' + tableDatat.value[0].buildingName + '持平。';

			if (obj1.tenantConstituInfoTech.total > obj.tenantConstituInfoTech.total) {
				lastName += '招商写字楼租户构成信息技术占比，';
			}

			if (obj1.tenantConstituFinance.total > obj.tenantConstituFinance.total) {
				lastName += '招商写字楼租户构成金融占比，';
			}

			if (obj1.tenantConstituLeaseBusiness.total > obj.tenantConstituLeaseBusiness.total) {
				lastName += '招商写字楼租户构成租赁和商务服务占比，';
			}

			if (obj1.tenantConstituWholesaleRetail.total > obj.tenantConstituWholesaleRetail.total) {
				lastName += '招商写字楼租户构成批发和零售占比，';
			}

			if (obj1.tenantConstituOther.total > obj.tenantConstituOther.total) {
				lastName += '招商写字楼租户构成批发和零售占比';
			}

			if (lastName) {
				lastName += '高于' + tableDatat.value[0].buildingName + '，租户构成分布更为均衡。';
				return name + lastNames + lastName;
			} else {
				lastName = '占比高于' + tableDatat.value[0].buildingName + '，租户构成分布更为均衡。';
				return name + lastNames + lastName;
			}
		} else {
			let name = null;
			let lastName = '';
			let lastNames = '租户构成一级行业';

			name =
				tableDatao.value[0].buildingName +
				'的累计租户数量较' +
				tableDatat.value[0].buildingName +
				'多' +
				(totalCount.value - totalCountTwo.value) +
				'户，';

			if (obj1.tenantConstituInfoTech.total > obj.tenantConstituInfoTech.total) {
				lastName += '招商写字楼租户构成信息技术占比，';
			}

			if (obj1.tenantConstituFinance.total > obj.tenantConstituFinance.total) {
				lastName += '招商写字楼租户构成金融占比，';
			}

			if (obj1.tenantConstituLeaseBusiness.total > obj.tenantConstituLeaseBusiness.total) {
				lastName += '招商写字楼租户构成租赁和商务服务占比，';
			}

			if (obj1.tenantConstituWholesaleRetail.total > obj.tenantConstituWholesaleRetail.total) {
				lastName += '招商写字楼租户构成批发和零售占比，';
			}

			if (obj1.tenantConstituOther.total > obj.tenantConstituOther.total) {
				lastName += '招商写字楼租户构成批发和零售占比';
			}

			if (lastName) {
				lastName += '高于' + tableDatat.value[0].buildingName + '，租户构成分布更为均衡。';
				return name + lastNames + lastName;
			} else {
				lastName = '占比高于' + tableDatat.value[0].buildingName + '，租户构成分布更为均衡。';
				return name + lastNames + lastName;
			}
		}
	}
}
</script>

<style lang="less" scoped>
.content {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;

		.table_main {
			width: 100%;
			height: 162px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.table_ {
				width: calc(50% - 8px);
				height: 162px;
				border-radius: 6px;
				background-color: rgba(255, 255, 255, 1);
				position: relative;

				.tag_box {
					width: auto;
					height: 16px;
					position: absolute;
					left: 0;
					top: 20px;
					font-size: 14px;
					font-weight: bold;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&::before {
						content: '';
						width: 4px;
						height: 16px;
						background-color: rgba(24, 104, 241, 1);
						margin-right: 10px;
					}
				}

				.table_1 {
					width: 96%;
					position: absolute;
					bottom: 10px;
					left: 2%;

					&::v-deep .el-table--fit {
						border-radius: 8px;
					}

					&::v-deep .el-table th {
						background-color: rgba(245, 245, 245, 1);
					}
				}

				.add {
					width: 96%;
					height: 90px;
					position: absolute;
					bottom: 10px;
					left: 2%;
					border-radius: 6px;
					border: 1px solid rgba(231, 231, 231, 1);
					display: flex;
					justify-content: center;
					align-items: center;
					color: rgba(3, 93, 255, 1);
					font-size: 14px;
					font-weight: bold;
				}

				.clear {
					width: auto;
					height: 20px;
					position: absolute;
					top: 15px;
					right: 15px;
					font-size: 14px;
					color: rgba(24, 104, 241, 1);
				}
			}
		}

		.echars_box {
			width: 100%;
			min-height: 673px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			border-radius: 6px;
			position: relative;

			.tag_box {
				width: auto;
				height: 16px;
				position: absolute;
				left: 0;
				top: 20px;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;

				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}

				span {
					margin-top: 1.8px;
					margin-left: 10px;
					font-size: 12px;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					&:first-child {
						&::after {
							content: '';
							width: 8px;
							height: 12px;
							margin-top: -2.82px;
							margin-left: 10px;
							background-color: rgba(4, 80, 218, 1);
						}
					}

					&:last-child {
						&::after {
							content: '';
							width: 8px;
							margin-top: -2px;
							height: 12px;
							margin-left: 10px;
							background-color: rgba(30, 170, 117, 1);
						}
					}
				}
			}

			.echars_main {
				width: 98%;
				height: 100%;
				margin: 0 auto;
				padding-top: 55px;
				padding-bottom: 15px;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;

				.box_ {
					width: calc(50% - 8px);
					min-height: 442px;
					margin-bottom: 15px;
					border: 1px solid rgba(231, 231, 231, 1);
					border-radius: 6px;
					box-sizing: border-box;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-direction: column;

					.title1 {
						width: 100%;
						height: 44px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 12px;
						background-color: rgba(245, 245, 245, 1);
						border-bottom: 1px solid rgba(231, 231, 231, 1);
						margin-bottom: 20px;
					}

					.tips {
						width: 90%;
						line-height: 20px;
						margin: 0 auto;
						font-size: 12px;
						color: rgba(29, 33, 41, 1);
					}

					.chartBox {
						width: 90%;
						height: 330px;
						margin-top: 30px;
						margin-bottom: 15px;
						border: 1px solid rgba(231, 231, 231, 1);
						border-radius: 6px;
						overflow: hidden;

						.title2 {
							width: 100%;
							height: 44px;
							border-bottom: 1px solid rgba(231, 231, 231, 1);
							display: flex;
							justify-content: center;
							align-items: center;
							font-size: 12px;
							color: rgba(78, 89, 105, 1);
						}

						.pie_box {
							width: 100%;
							height: 280px;
							display: flex;
							justify-content: space-between;
							align-items: center;
							> :nth-child(n) {
								width: 50% !important;
							}
							.down_box {
								width: 40%;
								height: 24px;
								margin-top: 20px;
								display: flex;
								justify-content: center;
								align-items: flex-start;
								flex-direction: column;

								span {
									margin-left: 10px;
									font-size: 12px;
									margin: 5px;
									font-weight: bold;
									display: flex;
									justify-content: flex-start;
									align-items: center;

									&:first-child {
										&::after {
											content: '';
											width: 8px;
											height: 12px;
											margin-left: 10px;
											background-color: rgba(4, 80, 218, 1);
										}
									}

									&:nth-child(2) {
										&::after {
											content: '';
											width: 8px;
											height: 12px;
											margin-left: 10px;
											background-color: rgba(30, 170, 117, 1);
										}
									}

									&:nth-child(3) {
										&::after {
											content: '';
											width: 8px;
											height: 12px;
											margin-left: 10px;
											background-color: rgba(255, 114, 116, 1);
										}
									}

									&:nth-child(4) {
										&::after {
											content: '';
											width: 8px;
											height: 12px;
											margin-left: 10px;
											background-color: rgba(250, 212, 24, 1);
										}
									}

									&:nth-child(5) {
										&::after {
											content: '';
											width: 8px;
											height: 12px;
											margin-left: 10px;
											background-color: rgba(251, 157, 108, 1);
										}
									}
								}
							}
						}
					}

					.tips_box {
						width: 90%;
						min-height: 330px;
						margin-top: 30px;
						margin-bottom: 15px;
						border: 1px solid rgba(231, 231, 231, 1);
						box-sizing: border-box;
						border-radius: 6px;
						overflow: hidden;

						.title2 {
							width: 100%;
							height: 44px;
							border-bottom: 1px solid rgba(231, 231, 231, 1);
							display: flex;
							justify-content: center;
							align-items: center;
							font-size: 12px;
							color: rgba(78, 89, 105, 1);
							margin-bottom: 20px;
						}

						.detail {
							width: 90%;
							height: auto;
							margin: 0 auto;
							margin-bottom: 20px;
							border-radius: 6px;
							overflow: hidden;
							border: 1px solid rgba(231, 231, 231, 1);

							.title3 {
								width: 100%;
								height: 56px;
								background-color: rgba(245, 245, 245, 1);
								border-bottom: 1px solid rgba(231, 231, 231, 1);
								padding: 0 15px;
								box-sizing: border-box;
								font-size: 14px;
								font-weight: bold;
								color: rgba(78, 89, 105, 1);
								display: flex;
								justify-content: flex-start;
								align-items: center;
							}
							.progress_box {
								width: 100%;
								height: 24px;
								display: flex;
								justify-content: flex-start;
								align-items: center;
								padding: 0 20px;
								box-sizing: border-box;
								margin: 20px 0;
								font-size: 12px;
								.lable_ {
									width: 80px;
									height: 24px;
									box-sizing: border-box;
									display: flex;
									justify-content: flex-start;
									align-items: center;
								}
								.progress {
									//width: 240px;
									width: 100%;
									height: 24px;
									margin: 0 20px;
									border: 1px solid rgba(231, 231, 231, 1);
									border-radius: 4px;
									padding: 3px;
									box-sizing: border-box;
									display: flex;
									justify-content: flex-start;
									align-items: center;
									.line {
										height: 100%;
										border-radius: 4px;
										box-sizing: border-box;
									}
								}
								.num {
									width: 40px;
									text-align: left;
								}
							}
						}
					}
				}
			}
		}
	}
}

// 弹窗
.title_box {
	width: 100%;
	max-height: 100px;
	overflow: scroll;
	border-top: 1px solid rgba(231, 231, 231, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	box-sizing: border-box;
	display: flex;
	flex-wrap: wrap;

	.tab {
		width: 31%;
		height: 32px;
		margin: 8px 15px 8px 0;
		background-color: rgba(245, 246, 247, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
		position: relative;
		text-wrap: nowrap;

		.det {
			width: 10px;
			height: 10px;
			position: absolute;
			right: 10px;
			font-size: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			color: rgba(201, 205, 212, 1);
			cursor: pointer;
		}
	}
}

.search_box {
	width: 100%;
	height: auto;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;

	.box_1 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		border: 1px solid rgba(231, 231, 231, 1);
		box-sizing: border-box;

		::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
			box-shadow: 0;
		}

		.label {
			width: 50%;
			height: 100%;
			font-size: 14px;
			color: rgba(134, 144, 156, 1);
			background-color: rgba(245, 246, 247, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.box_2 {
		width: 230px;
		height: 32px;
		margin: 10px 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		border-radius: 4px;
		box-sizing: border-box;
	}
}

.table_2 {
	width: 100%;
	height: 308px;

	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}

.btn_box {
	width: calc(100% - 20px);
	display: flex;
	justify-content: flex-start;
	align-items: center;
	border-radius: 6px;
	background-color: #ffffff;
	margin-top: 10px;
	font-size: 14px;
	color: #555555;
	line-height: 20px;
	position: relative;
	padding: 10px 10px 15px 10px;
	.box_copy {
		position: absolute;
		right: 12px;
		bottom: 7px;
		cursor: pointer;
		color: #1868f1;
	}
}

.top_boxFirst {
	display: flex;
	justify-content: space-between;
	height: 56px;
	align-items: center;
	.tag_boxTitle {
		width: auto;
		height: 16px;
		font-size: 14px;
		font-weight: bold;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		&::before {
			content: '';
			width: 4px;
			height: 16px;
			background-color: #1868f1;
			margin-right: 10px;
		}
	}

	.tag_boxCenter {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		font-size: 14px;
		color: #1868f1;
		padding-right: 16px;
		> :nth-child(n) {
			cursor: pointer;
		}
		.tag_boxLeft {
			display: flex;
			align-items: center;
			margin: 0 0px 0 24px;
		}
	}
}
</style>
