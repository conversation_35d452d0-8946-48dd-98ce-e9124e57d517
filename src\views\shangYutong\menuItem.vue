<template>
	<div>
		<arco-sub-menu v-if="props.item.children" :key="props.item.path">
			<template #icon
				><img :src="props.activeMenu.includes(props.item.path) ? props.item.icon_active : props.item.icon" alt="" style="width: 18px; height: 18px" />
			</template>
			<template #title>
				<span class="item_name">{{ props.item.name }}</span>
			</template>
			<menu-Item v-for="child in props.item.children" :key="child.path" :item="child" :active-menu="props.activeMenu" />
		</arco-sub-menu>
		<arco-menu-item v-if="!props?.item?.children" :key="props.item.path">
			<template #icon
				><img
					v-if="props.item.icon || props.item.icon_active"
					:src="props.item.path === props.activeMenu ? props.item.icon_active : props.item.icon"
					alt=""
					style="width: 18px; height: 18px"
			/></template>
			<span class="item_name">{{ props.item.name }}</span>
		</arco-menu-item>
	</div>
</template>

<script setup>
const props = defineProps({
	item: {
		type: Object,
		required: true,
	},
	activeMenu: {
		type: String,
		required: true,
	},
});

function handleActiveMenu(props) {
	console.log(props, 'props.activeMenu1111111');
	// 字符串在activeMenu中
	if (props.activeMenu.includes(props.item.pathParent)) {
		return '1111111';
	}
}
</script>

<style scoped lang="scss"></style>
