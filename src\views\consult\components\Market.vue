<template>
	<div style="padding: 0 20px">
		<div class="title_box">
			<p>市场分析</p>
		</div>

		<div class="search">
			<el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleTabClick">
				<el-tab-pane label="市场统计" name="rating">
					<a-row style="display: flex; margin-bottom: 10px">
						<a-col :span="16" style="display: flex; justify-content: center">
							<el-cascader
								placeholder="请选择城市"
								size="large"
								:options="$vuexStore.state.cityArray"
								@change="handleChange"
								:props="{ value: 'label' }"
							>
							</el-cascader>

							<el-select style="margin-left: 20px; width: 150px" v-model="rateValue" placeholder="全部资产评级" size="large">
								<el-option v-for="(item, value) in rate" :key="item" :label="item" :value="item" />
							</el-select>
							<el-select style="margin-left: 20px; width: 150px" v-model="buildingTypesValue" placeholder="全部资产" size="large">
								<el-option v-for="(item, index) in buildingTypes" :key="item" :label="item" :value="item" />
							</el-select>
							<el-input v-model="inputValue" placeholder="关键字搜索" style="height: 38px; width: 160px; margin-left: 20px" />
							<el-button round @click="handleSearch">搜索</el-button>
						</a-col>
						<a-col :span="8" style="display: flex">
							<div>
								<el-button round @click="handleButtonClick(300)" :class="{ 'active-button': selectedRadius === 300 }">300m</el-button>
								<el-button round @click="handleButtonClick(500)" :class="{ 'active-button': selectedRadius === 500 }">500m</el-button>
								<el-button round @click="handleButtonClick(1000)" :class="{ 'active-button': selectedRadius === 1000 }">1000m</el-button>
							</div>
						</a-col>
					</a-row>
					<!-- <el-scrollbar class="real" height="400px">
						<el-card v-for="item in real" :key="item.ll" shadow="hover" style="margin-bottom: 10px; height: 150px">
							<el-row>
								<el-col :span="6">
									<img
										:src="`${proxyAddress}${'/upfile_jpg/'}${item.id}${'.jpg'}`"
										alt="Image"
										style="width: 100%; height: 150px; object-fit: cover"
									/>
								</el-col>
								<el-col :span="18">
									<div>
										<p>{{ item.name }}</p>
										<p>{{ item.address }}</p>
									</div>
								</el-col>
							</el-row>
						</el-card>
					</el-scrollbar> -->
					<div class="gaode">
						<gaode @updateRadius="handleUpdateRadius" @clickChild="clickEven" :radius="selectedRadius" @searchMap="handleSearch"></gaode>
					</div>
					<div class="table">
						<p>大楼信息</p>
						<el-table :data="real" style="width: 100%" :stripe="true" height="400px" :lazy="true">
							<el-table-column v-for="column in tableColumns" :key="column.prop" :prop="column.prop" :label="column.label" :width="column.width">
							</el-table-column>
						</el-table>
					</div>
				</el-tab-pane>
				<el-tab-pane label="交易统计" name="deal">
					<div class="search">
						<a-row style="display: flex; margin-bottom: 10px">
							<a-col :span="16" style="display: flex; justify-content: center">
								<el-cascader
									placeholder="请选择城市"
									size="large"
									:options="$vuexStore.state.cityArray"
									@change="handleChange"
									:props="{ value: 'label' }"
								>
								</el-cascader>

								<el-select style="margin-left: 20px; width: 150px" v-model="rateValue" placeholder="全部资产评级" size="large">
									<el-option v-for="(item, value) in rate" :key="item" :label="item" :value="item" />
								</el-select>
								<el-select style="margin-left: 20px; width: 150px" v-model="buildingTypesValue" placeholder="全部资产" size="large">
									<el-option v-for="(item, index) in buildingTypes" :key="item" :label="item" :value="item" />
								</el-select>
								<!-- <el-input v-model="inputValue" placeholder="关键字搜索" style="height: 38px; width: 160px; margin-left: 20px" /> -->
								<el-button round @click="handleSearchReal">搜索</el-button>
							</a-col>
							<a-col :span="8" style="display: flex">
								<div>
									<el-button round @click="handleButtonClick(300)" :class="{ 'active-button': selectedRadius === 300 }">300m</el-button>
									<el-button round @click="handleButtonClick(500)" :class="{ 'active-button': selectedRadius === 500 }">500m</el-button>
									<el-button round @click="handleButtonClick(1000)" :class="{ 'active-button': selectedRadius === 1000 }">1000m</el-button>
								</div>
							</a-col>
						</a-row>
					</div>
					<el-scrollbar class="real" height="400px">
						<el-card v-for="item in real" :key="item.ll" shadow="hover" style="margin-bottom: 20px; height: 150px">
							<el-row>
								<!-- <el-col :span="6">
								<img :src="`${proxyAddress}${'/upfile_jpg/'}${item.id}${'.jpg'}`" alt="Image" style="width: 100%; height: 150px; object-fit: cover" />
							</el-col> -->
								<el-col :span="18">
									<div>
										<p>{{ item.anj_name }}</p>
										<p>{{ item.anj_body }}</p>
									</div>
								</el-col>
							</el-row>
						</el-card>
					</el-scrollbar>
					<div class="gaode">
						<rat @clickChildReal="clickEvenReal" :radius="selectedRadius" @searchMapReal="handleSearchReal"></rat>
					</div>
					<div class="table">
						<p>大楼信息</p>
						<div style="display: flex; justify-content: space-between; align-items: center">
							<el-table :data="real" style="width: 45%" :stripe="true" height="300px" :lazy="true">
								<!-- <el-table-column v-for="column in tableColumns" :key="column.prop" :prop="column.prop" :label="column.label" :width="column.width">
							<template v-if="column.slot" #default="{ row }">
								<el-button @click="router.push(`/main/property/${row.id}`)">{{ column.slotText || '操作' }}</el-button>
							</template>
						</el-table-column> -->
								<el-table-column
									v-for="column in tableColumnsReal"
									:key="column.prop"
									:prop="column.prop"
									:label="column.label"
									:width="column.width"
								>
								</el-table-column>
							</el-table>
							<el-table :data="real" style="width: 45%" :stripe="true" height="300px" :lazy="true">
								<el-table-column
									v-for="column in tableColumnsReal"
									:key="column.prop"
									:prop="column.prop"
									:label="column.label"
									:width="column.width"
								>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</el-tab-pane>
			</el-tabs>
			<!-- <a-row style="display: flex; margin-bottom: 10px">
				<a-col :span="16" style="display: flex; justify-content: center">
					<el-cascader
						style="margin-left: 5px"
						placeholder="请选择城市"
						size="large"
						:options="pcaTextArr"
						@change="handleChange"
						filterable
						:props="{ value: 'label' }"
						v-model="selectedOptions"
					>
					</el-cascader>
					<el-select style="margin-left: 20px; width: 150px" v-model="rateValue" placeholder="全部资产评级" size="large">
						<el-option v-for="(item, value) in rate" :key="item" :label="item" :value="item" />
					</el-select>
					<el-select style="margin-left: 20px; width: 150px" v-model="buildingTypesValue" placeholder="全部资产" size="large">
						<el-option v-for="(item, index) in buildingTypes" :key="item" :label="item" :value="item" />
					</el-select>
					<el-input v-model="inputValue" placeholder="关键字搜索" style="height: 38px; width: 160px; margin-left: 20px" />
					<el-button round @click="handleSearch">搜索</el-button>
				</a-col>
				<a-col :span="8" style="display: flex">
					<div>
						<el-button round @click="handleButtonClick(300)" :class="{ 'active-button': selectedRadius === 300 }">300m</el-button>
						<el-button round @click="handleButtonClick(1000)" :class="{ 'active-button': selectedRadius === 1000 }">1000m</el-button>
						<el-button round @click="handleButtonClick(3000)" :class="{ 'active-button': selectedRadius === 3000 }">3000m</el-button>
					</div>
					<el-button
						@click="handleTransactionType('出租')"
						:class="{ 'active-button': transactionTypeValue === '出租' }"
						v-model="transactionTypeValue"
						>出租</el-button
					>
					<el-button
						@click="handleTransactionType('出售')"
						:class="{ 'active-button': transactionTypeValue === '出售' }"
						v-model="transactionTypeValue"
						>出售</el-button
					>
					<el-button
						@click="handleTransactionType('证券交易')"
						:class="{ 'active-button': transactionTypeValue === '证券交易' }"
						v-model="transactionTypeValue"
						>证券化</el-button
					>
				</a-col>
			</a-row> -->
		</div>
		<!-- <el-scrollbar class="real" height="400px">
			<el-card
				@click="router.push(`/main/property/${item.id}`)"
				v-for="item in real"
				:key="item.ll"
				shadow="hover"
				style="margin-bottom: 10px; height: 150px"
			>
				<el-row>
					<el-col :span="6">
						<img :src="`${proxyAddress}${'.jpg'}`" alt="Image" style="width: 100%; height: 150px; object-fit: cover" />
					</el-col>
					<el-col :span="18">
						<div>
							<p>{{ item.name }}</p>
							<p>{{ item.address }}</p>
						</div>
					</el-col>
				</el-row>
			</el-card>
		</el-scrollbar> -->
	</div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import gaode from '../../../MapContainer.vue';
import rat from '../../../RatMap.vue';
import http from '@/utils/http';
import { useRouter } from 'vue-router';
const router = useRouter();
// import { pcaTextArr } from 'element-china-area-data';
const transactionTypeValue = ref('');
//资产类型搜索
const handleTransactionType = (type) => {
	transactionTypeValue.value = type;
};
const tableColumns = [
	{ prop: 'am_deType', label: '资产类型' },
	{ prop: 'am_degree', label: '数量' },
	{ prop: 'am_typex', label: '覆盖面积(m²)' },
	{ prop: 'am_jjrName', label: '空置率' },
	{ prop: 'am_name', label: '每平米租金' },
	{ prop: 'am_deType', label: '租金增长率' },
	{ prop: 'am_degree', label: '每平米售价' },
	{ prop: 'am_typex', label: '售价增长率' },
	{ prop: 'am_jjrName', label: '近12月销售额' },
	{ prop: 'am_jjrName', label: '近12月去化率' },
];
const tableColumnsReal = [
	{ prop: 'ant_type', label: '序号' },
	{ prop: 'ant_type', label: '名称' },
	{ prop: 'ant_pk', label: '资产类型' },
	{ prop: 'ant_mj', label: '评级' },
	{ prop: 'ant_sq', label: '出租' },
	{ prop: 'ant_ppls', label: '出售' },
	{ prop: 'ant_mmls', label: '证券化' },
	{ prop: 'ant_momey', label: '经纪人' },
];

const lnglat = ref(''); //经纬度 chang
const proxyAddress = process.env.NODE_ENV === 'development' ? 'http://*************:8080/' : 'http://**************:8081/';
const mapradius = ref('');
const handleUpdateRadius = (radius) => {
	mapradius.value = radius;
};
const clickEven = (clicklng, clicklat) => {
	lnglat.value = clicklng + ',' + clicklat;
};
const clickEvenReal = (clicklng, clicklat) => {
	lnglat.value = clicklng + ',' + clicklat;
};

const city = ref('');
const county = ref('');
const handleChange = (val) => {
	city.value = val[0];
	county.value = val[1];
};
const rateValue = ref('');
const buildingTypesValue = ref('');
const inputValue = ref('');
//半径圆实现
const selectedRadius = ref(300); // 默认半径值
const queryParams = computed(() => {
	return {
		city: city.value,
		county: county.value,
		degree: rateValue.value,
		de_type: buildingTypesValue.value,
		keywords: inputValue.value,
		loasts: lnglat.value,
		distance: mapradius.value || selectedRadius.value,
		// transactionType: transactionTypeValue.value,
	};
});
onMounted(() => {
	handleSearch();
});
//绑定大楼
const real = ref([]);
const handleSearch = async () => {
	try {
		const response = await http.get('/api/a_maps.do?act=listMAPTransaction', {
			params: queryParams.value,
		});

		// 假设 res 是 response.data 的属性

		real.value = response.list;
	} catch (error) {
		console.error('Error while fetching data:', error);
		// 这里可以添加适当的错误处理逻辑
	}
};
const handleSearchReal = async () => {
	try {
		const response = await http.get('/api/maps/listMAPTransaction', {
			params: Object.assign({}, queryParams.value, { transactionType: transactionTypeValue.value }),
		});

		// 假设 res 是 response.data 的属性

		real.value = response.list;
	} catch (error) {
		console.error('Error while fetching data:', error);
		// 这里可以添加适当的错误处理逻辑
	}
};
const activeName = ref('rating');
const selectedOptions = ref([]);
const rate = ['A+', 'A', 'B+', 'B', 'B-', 'C'];
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '数据中心', '保障房', '农贸市场'];

const handleButtonClick = (radius) => {
	mapradius.value = radius;
	selectedRadius.value = radius;
};
const handleTabClick = () => {
	// 在这里清空 real 数组
	real.value = [];
	city.value = '';
	rateValue.value = '';
	buildingTypesValue.value = '';
	inputValue.value = '';
	lnglat.value = '';
	if (activeName.value === 'deal') {
		handleSearch();
	} else if (activeName.value === 'rating') {
		handleSearchReal();
	}
};
</script>

<style lang="less" scoped>
.title_box {
	p {
		font-size: 20px;
		margin-top: 8px;
		font-weight: 600;
		margin-bottom: 10px;
	}
}
.table {
	width: 100%;
	height: 100%;
	text-align: center;
}
.search {
	button {
		margin-left: 30px;
	}
}
.real {
	width: 500px;
	position: absolute;
	z-index: 999;
	right: 0;
	bottom: -50px;
}
.scrollbar-demo-item {
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
	border-radius: 4px;
	background: var(--el-color-primary-light-9);
}

.gaode {
	height: 400px;
}
.demo-tabs > .el-tabs__content {
	padding: 20px;
	color: #6b778c;
	font-size: 32px;
	font-weight: 600;
}
.demo-tabs {
	padding: 0 20px;
}
.active-button {
	background-color: #409eff; // 蓝色背景
	color: white; // 文字颜色
}
</style>
