<template>
	<el-dialog
		v-model="props.dialogVisible"
		:close-on-click-modal="false"
		:show-close="false"
		align-center="center"
		:before-close="handleClose"
		style="width: 700px"
	>
		<template #header>
			<div class="dialogHeader">
				<div class="dialogHeaderLeft">
					<div>开票详情</div>
				</div>

				<div class="dialogHeaderRight">
					<el-icon><CloseBold @click="handleClose" /></el-icon>
				</div>
			</div>
		</template>
		<div class="form_content">
			<div class="content_bottom">
				<div class="content_">
					<div v-for="(item, index) in listDetails" :key="index" class="content_f">
						<div class="content_name">{{ item.key }}</div>
						<div class="content_value" :class="item.status ? 'content_valueActive' : ''">
							{{ handleValue(item.value) }}
						</div>
					</div>
				</div>
				<div class="content_f">
					<div class="content_name">接收邮箱</div>
					<div class="content_value">
						{{ objActive.receiveEmail }}
					</div>
				</div>
			</div>
		</div>
		<template #footer>
			<div class="dialog_footer">
				<div>
					<el-button @click="handleResend" v-if="objActive.invoiceState === '1'"> 重新发送 </el-button>
				</div>
			</div>
		</template>
	</el-dialog>
	<resends
		:objActive="objActive"
		@handleResendClose="handleResendClose"
		@handleEmailDialogClose="handleEmailDialogClose"
		:resendDialogVisible="resendDialogVisible"
	></resends>
	<IssuedSuccess
		@handleRevertClose="handleRevertClose"
		@handleIssuedClose="handleIssuedClose"
		:IssuedDialogVisible="IssuedDialogVisible"
	></IssuedSuccess>
</template>

<script setup>
import resends from './resend.vue';
import IssuedSuccess from './IssuedSuccess.vue';
import { getInvoiceRecord } from '@/api/equityTerm.js';
import { ref } from 'vue';

const emit = defineEmits();
const props = defineProps({
	dialogVisible: {
		type: Boolean,
		default: false,
	},
	activeObj: {
		type: Object,
		default: () => {},
	},
	objActives: {
		type: Object,
		default: () => {},
	},
});
const resendDialogVisible = ref(false);

const IssuedDialogVisible = ref(false);
const listDetails = ref([
	{
		key: '开票状态',
		value: 'invoiceState',
		status: true,
	},
	{
		key: '发票类型',
		value: 'invoiceType',
	},
	{
		key: '抬头类型',
		value: 'headerType',
	},
	{
		key: '发票抬头',
		value: 'headerName',
	},
	{
		key: '税号',
		value: 'taxNum',
	},
	{
		key: '发票内容',
		value: 'invoiceContent',
	},
	{
		key: '发票金额',
		value: 'invoiceMoney',
	},
	{
		key: '开户银行',
		value: 'openBank',
	},
	{
		key: '银行账号',
		value: 'bankAccount',
	},
	{
		key: '注册地址',
		value: 'address',
	},
	{
		key: '注册电话',
		value: 'registerPhone',
	},
	{
		key: '备注说明',
		value: 'invoiceDesc',
	},
	{
		key: '申请时间',
		value: 'createTime',
	},
	{
		key: '开票时间',
		value: 'invoiceTime',
	},
]);

const objActive = ref({});
watch(
	() => props.dialogVisible,
	(newVal, oldVal) => {
		if (newVal) {
			objActive.value = props.objActives;
		}
	}
);
// 获取发票记录
function handleGetInvoiceRecord() {
	getInvoiceRecord({ orderId: props.activeObj.outTradeNo }).then((res) => {
		if (res.code === 200) {
			objActive.value = res.result;
		}
	});
}

function handleValue(value) {
	// 抬头类型
	if (value === 'headerType') {
		if (objActive.value[value] === '1') {
			return '企业单位';
		} else if (objActive.value[value] === '2') {
			return '个人/非企业单位';
		}
		// 发票类型
	} else if (value === 'invoiceType') {
		if (objActive.value[value] === '1') {
			return '电子-普通发票';
		} else if (objActive.value[value] === '2') {
			return '电子-专用发票';
		}
		// 开票状态
	} else if (value === 'invoiceState') {
		if (objActive.value[value] === '1') {
			return '已开票';
		} else if (objActive.value[value] === '2') {
			return '已开票';
		} else {
			return '未开票';
		}
	} else {
		return objActive.value[value];
	}
}
// 关闭对话框
function handleClose() {
	emit('handleDialogClose');
}
// 重新发送
function handleResend() {
	resendDialogVisible.value = true;
}
// 关闭邮箱对话框
function handleEmailDialogClose() {
	resendDialogVisible.value = false;
}
// 发送成功
function handleResendClose() {
	IssuedDialogVisible.value = true;
}
// 关闭对话框
function handleIssuedClose() {
	IssuedDialogVisible.value = false;
	// emit('handleDialogClose');
}
// 返回
function handleRevertClose() {
	IssuedDialogVisible.value = false;
	emit('handleDialogClose', true);
	handleEmailDialogClose();
}
</script>

<style lang="scss" scoped>
.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	color: rgba(29, 33, 41, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	.el-icon {
		cursor: pointer;
	}
}

.form_content {
	padding: 0 16px;
	.content_bottom {
		height: 416px;
		.content_ {
			display: flex;
			flex-wrap: wrap;
			border-bottom: 1px solid #e7e7e7;
		}
		.content_f {
			display: flex;
			width: calc(50% - 32px);
			margin: 8px 0 12px 0;
			padding: 0 16px;
			div {
				font-size: 12px;
				font-weight: 400;
				line-height: 20px;
			}
			.content_name {
				color: #1d2129;
				margin-right: 40px;
				width: 48px;
				// 不换行
				white-space: nowrap;
			}

			.content_value {
				width: 210px;
				color: #4e5969;
				// overflow: hidden;
				// text-overflow: ellipsis;
				// white-space: nowrap;
			}
			.content_valueActive {
				color: #1868f1;
			}
		}
	}
}

.dialog_footer {
	height: 54px;
	width: calc(100% - 16px);
	border-top: 1px solid rgba(231, 231, 231, 1);
	display: flex;
	align-items: center;
	justify-content: end;
	padding-right: 16px;
}
</style>
