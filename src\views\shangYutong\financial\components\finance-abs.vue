<template>
	<div class="common-layout">
		<!-- <div class="tab_box">
			<div class="title">标准化产品</div>
			<div class="tab tabAct" @click="handleClick('ABS')">ABS</div>
			<div class="tab" @click="handleClick('REITs')">REITs</div>
		</div> -->
		<div class="container_box">
			<div class="search_main">
				<div class="search_box">
					<div class="form_box">
						<el-form :model="form" ref="ruleform" label-width="124px">
							<div class="flex_containerFrom">
								<el-form-item label="关键词搜索" prop="valuee">
									<!-- <span>关键词搜索</span> -->
									<arco-input v-model="form.valuee" style="width: 240px" allow-clear placeholder="请输入产品、证券、机构关键词"></arco-input>
								</el-form-item>

								<el-form-item label="发行日期" prop="data">
									<arco-range-picker v-model="form.data" style="width: 240px" />
								</el-form-item>
							</div>

							<el-form-item label="当前状态" prop="checkboxGroup3">
								<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup3">
									<el-checkbox-button v-for="state in stateList" :key="state" :label="state">
										{{ state }}
									</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>

							<el-form-item label="基础资产" prop="checkboxGroup1">
								<!-- <span>基础资产</span> -->
								<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup1">
									<el-checkbox-button v-for="Basicassets in assets" :key="Basicassets" :label="Basicassets">
										{{ Basicassets }}
									</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>

							<el-form-item label="资产细分" prop="checkboxGroup2">
								<!-- <span>基础资产</span> -->
								<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup2" border="false" @change="oncheckzcxf">
									<el-checkbox-button v-for="subdivide in assetssub" :key="subdivide" :label="subdivide">
										{{ subdivide }}
									</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>
							<el-form-item label="监管机构" prop="checkboxGroup5">
								<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup5" border="false">
									<el-checkbox-button v-for="supervise in superviseList" :key="supervise" :label="supervise">
										{{ supervise }}
									</el-checkbox-button>
								</el-checkbox-group>
							</el-form-item>
							<div class="flex_containerFrom">
								<el-form-item label="发起/原始权益人">
									<arco-select v-model="value1" class="m-2" filterable style="width: 240px" placeholder="请选择发行/原始权益人">
										<arco-option
											v-for="item in options1"
											:style="{ color: item.value === value1 ? '#1868F1' : '' }"
											:key="item.value"
											:label="item.key"
											:value="item.value"
										/>
									</arco-select>
								</el-form-item>
								<el-form-item label="发行/管理人">
									<arco-select v-model="value2" class="m-2" filterable style="width: 240px" placeholder="请选择发行/管理人">
										<arco-option
											v-for="item in options2"
											:style="{ color: item.value === value2 ? '#1868F1' : '' }"
											:key="item.value"
											:label="item.key"
											:value="item.value"
										/>
									</arco-select>
								</el-form-item>
								<el-form-item label="参与承销商">
									<arco-select v-model="value3" class="m-2" filterable style="width: 240px" placeholder="请选择参与承销商">
										<arco-option
											v-for="item in options3"
											:key="item.value"
											:label="item.value"
											:style="{ color: item.value === value3 ? '#1868F1' : '' }"
											:value="item.value"
										/>
									</arco-select>
								</el-form-item>
							</div>
							<!-- 选择器 -->

							<!-- 确认取消按钮 -->
							<el-form-item>
								<el-button type="primary" @click="onSubmit" color="#1868F1">查询</el-button>
								<el-button @click="oncancel" color="#F2F3F5" style="color: #4e5969; margin-left: 8px">重置</el-button>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</div>
			<div class="table_box">
				<arco-table
					row-key="id"
					:scroll="{ y: 408, x: 1611 }"
					:data="AbsList"
					ref="tableRef"
					:pagination="false"
					:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
				>
					<template #columns>
						<arco-table-column title="产品简称" data-index="shortName" ellipsis tooltip :width="160" :fixed="'left'">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.shortName) ? '-' : scope.record.shortName }}
							</template>
						</arco-table-column>
						<arco-table-column title="产品名称" data-index="fullName" ellipsis tooltip :width="180">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.fullName) ? '-' : scope.record.fullName }}
							</template>
						</arco-table-column>
						<arco-table-column title="发起/原始收益人" data-index="originator" ellipsis tooltip :width="180">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.originator) ? '-' : scope.record.originator }}
							</template>
						</arco-table-column>

						<arco-table-column title="发行规模(元)" data-index="totalOffering" ellipsis tooltip :width="120">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.totalOffering) ? '-' : formattedMoney(scope.record.totalOffering) }}
							</template>
						</arco-table-column>

						<arco-table-column title="方式" data-index="issueType" ellipsis tooltip :width="120">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.issueType) ? '-' : scope.record.issueType }}
							</template>
						</arco-table-column>
						<arco-table-column title="监管机构" data-index="regulator" ellipsis tooltip :width="120">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.regulator) ? '-' : scope.record.regulator }}
							</template>
						</arco-table-column>
						<arco-table-column title="循环池" data-index="isCyclePurchase" ellipsis tooltip :width="120">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.isCyclePurchase) ? '-' : scope.record.isCyclePurchase }}
							</template>
						</arco-table-column>
						<arco-table-column title="交易场所" data-index="exchange" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.exchange) ? '-' : scope.record.exchange }}
							</template>
						</arco-table-column>
						<arco-table-column title="市场分类" data-index="marketType" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.marketType) ? '-' : scope.record.marketType }}
							</template>
						</arco-table-column>
						<arco-table-column title="产品分类" data-index="dealType" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.dealType) ? '-' : scope.record.dealType }}
							</template>
						</arco-table-column>
						<arco-table-column title="产品细分" data-index="assetSubCategory" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.assetSubCategory) ? '-' : scope.record.assetSubCategory }}
							</template>
						</arco-table-column>
						<arco-table-column title="状态" data-index="currentStatus" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.currentStatus) ? '-' : scope.record.currentStatus }}
							</template>
						</arco-table-column>
						<arco-table-column title="年份" data-index="year" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.year) ? '-' : scope.record.year }}
							</template>
						</arco-table-column>
						<arco-table-column title="起息日" data-index="closingDate" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.closingDate) ? '-' : scope.record.closingDate }}
							</template>
						</arco-table-column>
						<arco-table-column title="起息日" data-index="closingDate" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.closingDate) ? '-' : scope.record.closingDate }}
							</template>
						</arco-table-column>
						<arco-table-column title="法定到期日" data-index="legalMaturityDate" ellipsis tooltip :width="150">
							<template #cell="scope">
								{{ $utils.isEmpty(scope.record.legalMaturityDate) ? '-' : scope.record.legalMaturityDate }}
							</template>
						</arco-table-column>
						<arco-table-column title="主承销商" data-index="lederUnderWriter" ellipsis tooltip :width="180">
							<template #cell="scope">
								{{
									scope.record.lederUnderWriter === 'None' ? '-' : $utils.isEmpty(scope.record.lederUnderWriter) ? '-' : scope.record.lederUnderWriter
								}}
							</template>
						</arco-table-column>
						<arco-table-column title="发行/管理人" data-index="issuer" ellipsis tooltip :width="180">
							<template #cell="scope">
								{{ scope.record.issuer === 'None' ? '-' : $utils.isEmpty(scope.record.issuer) ? '-' : scope.record.issuer }}
							</template>
						</arco-table-column>
						<arco-table-column title="评级机构" :fixed="'right'" data-index="ratingAgency" ellipsis tooltip :width="180">
							<template #cell="scope">
								{{ scope.record.ratingAgency === 'None' ? '-' : $utils.isEmpty(scope.record.ratingAgency) ? '-' : scope.record.ratingAgency }}
							</template>
						</arco-table-column>
					</template>
				</arco-table>

				<div class="page_box">
					<arco-pagination
						:total="total"
						@change="handleCurrentChange"
						:current="currentPage"
						@page-size-change="pageSizeChange"
						:page-size="pageSize"
						size="medium"
						show-total
						show-page-size
					/>
				</div>
				<!-- 表格 -->
				<!-- <el-table
					:data="AbsList"
					style="width: 100%; height: 500px"
					border
					header-row-class-name="tableHeader"
					:stripe="false"
					ref="tableRef"
					:lazy="true"
					size="min"
				>
					<el-table-column prop="shortName" width="100" label="产品简称">
						<template #default="scope">
							<el-tooltip :content="scope.row.shortName" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.shortName }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="fullName" width="180" label="产品名称">
						<template #default="scope">
							<el-tooltip :content="scope.row.fullName" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.fullName }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="fullName" width="180" label="发起/原始收益人">
						<template #default="scope">
							<el-tooltip :content="scope.row.fullName" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.fullName }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="totalOffering" width="120" label="发行规模(元)">
						<template #default="scope">
							<span>{{ formattedMoney(scope.row.totalOffering) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="issueType" label="方式" />
					<el-table-column prop="regulator" width="120" label="监管机构" />
					<el-table-column prop="isCyclePurchase" label="循环池" />
					<el-table-column prop="exchange" width="150" label="交易场所" />
					<el-table-column prop="marketType" width="150" label="市场分类" />
					<el-table-column prop="dealType" width="150" label="产品分类" />
					<el-table-column prop="assetSubCategory" width="150" label="产品细分" />
					<el-table-column prop="currentStatus" label="状态" />
					<el-table-column prop="year" label="年份" />
					<el-table-column prop="closingDate" width="150" label="起息日" />
					<el-table-column prop="legalMaturityDate" width="150" label="法定到期日" />
					<el-table-column prop="lederUnderWriter" width="180" label="主承销商">
						<template #default="scope">
							<el-tooltip :content="scope.row.lederUnderWriter" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.lederUnderWriter === 'None' ? '' : scope.row.lederUnderWriter }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="issuer" width="180" label="发行/管理人">
						<template #default="scope">
							<el-tooltip :content="scope.row.issuer" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.issuer }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
					<el-table-column prop="ratingAgency" width="180" label="评级机构">
						<template #default="scope">
							<el-tooltip :content="scope.row.ratingAgency" placement="top" effect="dark">
								<span class="cell-content">{{ scope.row.ratingAgency === 'None' ? '' : scope.row.ratingAgency }}</span>
							</el-tooltip>
						</template>
					</el-table-column>
				</el-table> -->
				<!-- <div class="page_box">
					<div class="total_box">
						共<span>{{ total }}</span
						>项
					</div>
					<el-pagination
						@current-change="handleCurrentChange"
						:current-page="currentPage"
						layout="prev, pager, next,total, jumper"
						:page-size="10"
						class="mt-4"
						:total="total"
					/>
				</div> -->
			</div>
		</div>
	</div>
</template>

<script setup>
import { getFinanceAbsList, getAbsCompanySelectList } from '@/api/syt.js';
import { reactive, ref } from 'vue';
import http from '@/utils/http';
import { formattedMoney } from 'UTILS';
import { getAbsListApi, getSelector } from '@/api/finance.js';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
const router = useRouter();
import { useStore } from '../../../../store/index.js';
const store = useStore();
const activeName = ref('first');
let ruleform = ref();
const form = reactive({
	valuee: '',
	data: '',
	checkboxGroup0: [],
	checkboxGroup1: [],
	checkboxGroup2: [],
	checkboxGroup3: [],
	checkboxGroup4: [],
	checkboxGroup5: [],
});
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
let tableRef = ref(null);

const { proxy } = getCurrentInstance();
nextTick(() => {
	proxy?.$dragTable(tableRef);
});

const handleCurrentChange = (val) => {
	currentPage.value = val;
	getAbsList();
};

const pageSizeChange = (val) => {
	pageSize.value = val;
	getAbsList();
};

const options1 = ref([]);
const options2 = ref([]);
const options3 = ref([]);
const value1 = ref('');
const value2 = ref('');
const value3 = ref('');
const assets = ['类Reits', 'CMBS/CMBN', '保障房'];
const assetssub = ['全部', '办公物业', '零售物业', '混合', '酒店', '物流仓储', '混合类', '公寓', '基础设施'];
const onSubmit = () => {
	console.log('submit!');
	getAbsList();
};
const yearList = ['2024', '2023', '2022', '2021', '2020', '2019', '2018', '2017', '2016', '2015', '2014'];
const stateList = ['全部', '存续期', '已清算', '停售', '发行期'];
const labelList = ['不限', '房地产', '评级下调', '展期', '绿色', '汽车', '消费金融', '区块链', '知识产权', '公募REITs'];
const superviseList = ['全部', '证监会', '银保监', '交易商协会', '其他监管机构'];
const handleClick = (tab) => {
	if (tab === 'ABS') {
		router.push({
			path: '/shangYutong/financial/standard1/abs',
			query: { path: '/shangYutong/financial/standard1/abs' },
		});
		sessionStorage.setItem('childMenuIndex3', 0);
		store.changeChildMenuIndex3(0);
	} else {
		router.push({
			path: '/shangYutong/financial/standard1/reits',
			query: { path: '/shangYutong/financial/standard1/reits' },
		});
		sessionStorage.setItem('childMenuIndex3', 1);
		store.changeChildMenuIndex3(1);
	}
};
// 点击取消
const oncancel = () => {
	ruleform.value.resetFields();
	value1.value = null;
	value2.value = null;
	value3.value = null;
	currentPage.value = 1;
	// console.log('cancel!');
	// // 清空表单
	// form.valuee = '';
	// form.data = '';
	// form.checkboxGroup0 = [];
	// form.checkboxGroup1 = [];
	// form.checkboxGroup2 = [];
	// form.checkboxGroup3 = [];
	// form.checkboxGroup4 = [];
	// form.checkboxGroup5 = [];
	onSubmit();
};
const oncheckzcxf = () => {
	console.log(form.checkboxGroup2, 332);
};
// 发请求
const AbsList = ref([]);
const getAbsList = async () => {
	console.log('getAbsList', form, 223);
	//
	const params = {
		keyword: form.valuee,
		issueDateStart: form.data?.[0],
		issueDateEnd: form.data?.[1],
		basicAssets: form.checkboxGroup1.toString(),
		assetsDetail: form.checkboxGroup2.toString(),
		regulatoryauthority: form.checkboxGroup5.toString(),
		productStatus: form.checkboxGroup3.toString(),
		originator: value1.value, //发起/原始权益人
		issuer: value2.value, //发行/管理人
		lederUnderWriter: value3.value, //参与承销商
		currentPage: currentPage.value,
		pageSize: pageSize.value,
	};
	const res = await getFinanceAbsList(params)
		.then((res) => {
			// console.log(res.result.records, );
			AbsList.value = res.data.rows;
			total.value = res.data.total;
			return res;
		})
		.catch((err) => {
			console.log(err, 12345);
			return err;
		});
	// console.log(res,2346);
};
getAbsList();
// 获取选择器数据
const getSelectorData = async (type) => {
	const params = {
		selectType: type,
		keyword: '',
	};
	const res = await getAbsCompanySelectList(params)
		.then((res) => {
			// console.log(res.result, 1244 );
			if (type === 1) {
				options1.value = res.data.rows;
			} else if (type === 2) {
				options2.value = res.data.rows;
			} else if (type === 3) {
				options3.value = res.data.rows;
			}

			return res;
		})
		.catch((err) => {
			// console.log(err,12345);
			return err;
		});
};
getSelectorData(1); //发起/原始权益人
getSelectorData(2); //发起/原始权益人
getSelectorData(3); //发起/原始权益人
</script>

<style scoped lang="less">
.cell-content {
	display: inline-block; // 确保元素是内联块级元素
	width: 100%; // 宽度设置为100%，以填满列宽
	overflow: hidden; // 隐藏超出部分
	text-overflow: ellipsis; // 超出部分显示省略号
	white-space: nowrap; // 防止文本换行
}
.common-layout {
	width: 100%;
	height: 100%;
	box-sizing: border-box;

	.tab_box {
		width: 100%;
		height: 56px;
		padding: 0 15px;
		box-sizing: border-box;
		font-size: 14px;
		font-weight: 600;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		background-color: rgba(255, 255, 255, 1);

		.title {
			margin-right: 15px;
		}

		.tab {
			width: 60px;
			height: 56px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			cursor: pointer;
		}

		.tabAct {
			width: 60px;
			height: 56px;

			&::after {
				content: '';
				width: 24px;
				height: 3px;
				position: absolute;
				bottom: 0;
				background-color: rgba(3, 93, 255, 1);
			}
		}
	}
	.container_box {
		width: 100%;
		height: 100%;
		// padding-top: 10px;
		box-sizing: border-box;
		.search_main {
			width: 100%;
			height: 100%;
			background-color: rgba(255, 255, 255, 1);
			// margin-top: 15px;
			box-sizing: border-box;
			position: relative;
			.btn_box {
				width: 100%;
				height: 22px;
				padding: 0 15px;
				box-sizing: border-box;
				margin-bottom: 10px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				cursor: pointer;
				font-size: 12px;
				color: rgba(134, 144, 156, 1);
				.title1 {
					width: auto;
					font-size: 14px;
					color: rgba(60, 60, 60, 1);
					margin-right: 10px;
				}

				span {
					display: inline-block;
					transform: rotate(-90deg);
					margin-left: 5px;
				}
			}
			.search_box {
				width: 100%;
				overflow: hidden;
				padding: 32px 16px 0px 16px;
				box-sizing: border-box;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;
				::v-deep .el-checkbox-group .is-checked .el-checkbox-button__inner {
					margin-left: -1px;
					padding-left: 16px;
					border: 1px solid #1868f1 !important;
					background: #fff !important;
					box-shadow: none !important;
					color: #1868f1 !important;
				}

				::v-deep .el-checkbox-group .is-focus .el-checkbox-button__inner {
					border: 1px solid #e5e6eb;
				}

				::v-deep .el-form-item__label {
					font-weight: 400;
					font-size: 14px;
					color: #4e5969;
				}
				::v-deep .arco-input-wrapper {
					background: none !important;
					border: 1px solid #e5e6eb;
					border-radius: 4px;
				}

				::v-deep .arco-picker {
					background: none !important;
					border: 1px solid #e5e6eb;
					border-radius: 4px;
				}
				::v-deep .arco-picker-focused {
					border: 1px solid #1868f1;
				}
				::v-deep .arco-picker-input > input {
					background: none !important;
				}

				::v-deep .arco-input-wrapper:hover {
					border: 1px solid #1868f1;
				}

				.form_box {
					width: 100%;
					.flex_containerFrom {
						display: flex;
						align-content: flex-start;
					}
					.el-input__wrapper {
						flex: none;
						flex-grow: 0 !important;
					}
					::v-deep .arco-select-view-focus {
						border: 1px solid #409eff !important;
					}
					::v-deep .arco-select-view-single {
						border: 1px solid #e5e6eb;
						background-color: #fff;
						height: 32px;
						border-radius: 4px;
					}
				}
				.box_2 {
					width: 230px;
					height: 32px;
					margin: 10px 5px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					box-sizing: border-box;
				}
			}
		}
		.table_box {
			width: 100%;
			height: auto;
			padding: 15px;
			box-sizing: border-box;
			background-color: rgba(255, 255, 255, 1);
			position: relative;
			::v-deep .arco-table-container {
				border-radius: 4px !important;
				.arco-table-body {
					border-radius: 4px !important;
				}
				.arco-table-content {
					border-radius: 4px !important;
					tbody > :nth-last-child(1) > :nth-last-child(1) {
						border-bottom-right-radius: 4px !important;
					}
					tbody > :nth-last-child(1) > :nth-child(1) {
						border-bottom-left-radius: 4px !important;
					}
				}
			}
			.page_box {
				width: 100%;
				margin-top: 20px;
				height: 32px;
				display: flex;
				justify-content: end;
				align-items: center;

				.total_box {
					width: auto;
					height: 56px;
					display: flex;
					justify-content: center;
					align-items: center;
					span {
						color: rgba(3, 93, 255, 1);
					}
				}
			}

			.btn {
				position: absolute;
				top: 10px;
				right: 10px;
				z-index: 10;
				font-size: 14px;
				color: rgba(134, 144, 156, 1);
				span {
					display: inline-block;
					transform: rotate(-90deg);
					margin-left: 5px;
				}
			}

			&::v-deep .el-table--fit {
				border-radius: 8px;
			}

			&::v-deep .el-table th {
				background-color: rgba(245, 245, 245, 1);
			}
		}
	}
}
</style>
