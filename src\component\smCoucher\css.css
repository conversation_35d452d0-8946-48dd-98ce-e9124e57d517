.table-container {
  display: flex;
  flex-wrap: wrap;
  /* 1：发放劵 */
  .details-box {
    position: relative;
    display: flex;
    justify-content: space-between;
    width:  487px;
    height: 82px;
    &:hover{
      background:rgb(245, 246, 247) ;
    }
    .center_line{
      position: absolute;
      top: 9px;
      right: 110px;
      width: 2px;
      height: 64px;
      background: linear-gradient(180deg, rgba(231, 231, 231, 0) 0%, #E7E7E7 50%, rgba(231, 231, 231, 0) 100%);
    }
    .operate {
      width: 375px;
      height: 80px;
      border-radius: 6px;
      border: 1px solid #FE8042;
      border-right: 0px solid rgba(201, 205, 212, 1);
      display: flex;
      justify-content: space-between;
      .status{
        width: 62px;
        /* border-radius: 6px; */
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        line-height: 76px;
        font-weight: 400;
        text-align: center;
        background: linear-gradient(90deg, #FF504C 0%, #FE8042 100%);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
          width: 40px;
          height: 40px;
        }
      }
   
      .button-data {
        width:319px;
        height: 72px;
        padding: 4px 16px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        /* justify-content: center; */
        justify-content: flex-start;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;

        .button_top_content{
          width: 100%;
          margin-top: 12px;
          /* height: 24px; */
          line-height: 24px;
          display: flex;
          justify-content: space-between;
          /* align-items: end; */
          align-items: flex-start;
          color: #1D2129;
        }
        .button_top_content>:nth-child(1){
          font-size: 16px;
          font-weight: 700;
        }
        .button_top_content>:nth-child(2){
          /* width: 60px; */
          padding: 0 10px;
          height: 21px;
          border-radius: 10px 2px;
          line-height: 21px;
          font-weight: 400;
          font-size: 12px;
          text-align: center;
          color: #ffffff;
          background: linear-gradient(90deg, #FF504C 0%, #FE8042 100%);
          
        }
        .button_content_two{
          /* height: 22px; */
          font-weight: 400;
          /* line-height: 22px; */
          font-size: 12px;
          color: #4E5969;
          margin-top: 12px;
        }
      }
    }

    .right-content {
      position: relative;
      /* width: 95px; */
      height: 64px;
      border-radius: 6px;
      border: 1px solid #FF514C;
      border-left: 0px solid rgba(201, 205, 212, 1);
      padding: 8px ;
      .right_top_content{
        height: 28px;
        width: 93px;
        line-height: 28px;
        font-weight: 400;
        text-align: center;
        border-radius: 6px;
        color: #86909C;
        font-size: 12px;
        cursor: pointer;
        margin-bottom: 8px;
        &:hover{
          background: rgba(231, 231, 231, 1);
        }
      }

      .right_bottom_content{
        width: 93px;
        font-weight: 400;
        height: 28px;
        background: #fff;
        border-radius: 6px;
        font-size: 12px;
        color: #FF514C;
        border: 1px solid #FF514C66;
        line-height: 28px;
        text-align: center; 
        cursor: pointer;
      }

    }

  }
 
  /* 2：未使用劵  5：详情未使用劵 */
  .details_box_notUsed{
    width: 363px!important;
    .status{
      width: 19px!important;
      font-size: 11px!important;
      line-height: 14px!important;
      font-weight: bold!important;
      color: #fff;
    }
    .center_line{
      right: 110px!important;
      z-index: 99;
    }
    .button-data{
      width: 200px! important;
    }
    .button_content_two{
                height: 22px;
                line-height: 22px;
      color: #86909C! important;
    }

    .right_top_content{
      width: 93px! important;
    }
    .right_bottom_content{
      width: 93px! important;
      color: #fff! important;
      background: linear-gradient(90deg, #FF504C 0%, #FE8042 100%)! important;
    }
    .notUsedRight{
      height: 66px;
      width: 95px;
      line-height: 66px;
      font-size: 14px;
      font-weight: 700;
      text-align: center;
      border-radius: 6px;
      color: #1D2129;
      cursor: pointer;
      margin-bottom: 8px;
    }
  }
/* 3：已使用劵 4：已过期劵 */
  .usedAlready{
    width: 363px!important;
    &:hover{
      background:none ;
    }
    .operate {
      border: 1px solid #C9CDD4! important;
      border-right: 0px solid rgba(201, 205, 212, 1)! important;
    }
    .button-data{
      width: 200px! important;
    }
    .status{
      background: #F5F6F7! important;
      width: 19px!important;
      font-size: 11px!important;
      line-height: 14px!important;
      font-weight: bold!important;
      color: #C9CDD4! important;
      border-right: 1px solid #E7E7E7;
      border-top-left-radius: 6px! important;
      border-bottom-left-radius: 6px! important;
    }
    .center_line{
      right: 110px !important;
      z-index: 99;
    }

    .button-data{
      background: #F5F6F7! important;
    }
    .button_content_two{
      height: 22px;
      line-height: 22px;
      color: #86909C! important;
    }
    .right-content {
      border: 1px solid #C9CDD4! important;
      background: #F5F6F7! important;
      border-left: 0px solid #F5F6F7! important;
    }
    .notUsedRight{
      height: 66px;
      width: 95px;
      line-height: 66px;
      font-size: 14px;
      font-weight: 700;
      text-align: center;
      border-radius: 6px;
      color: #86909C;
      cursor: pointer;
      margin-bottom: 8px;
    }
  }
}