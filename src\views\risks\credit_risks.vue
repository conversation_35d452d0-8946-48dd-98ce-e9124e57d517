<template>
	<div>
		<div class="tab_box">
			<div
				class="tab"
				:class="activeName === route.name ? 'tabAct' : ''"
				v-for="(route, index) in routes"
				@click="handleTabClick(route, index)"
				:key="route.name"
			>
				<img :src="activeName == route.name ? route.icon_active : route.icon" />
				{{ route.name }}
			</div>
		</div>
		<div class="constructions" v-show="activeName == '新建楼宇进行风险测评'">
			<el-form :model="form" label-width="120px" label-position="top" ref="formRef" :rules="rules">
				<div class="container_build">
					<el-form-item label="建筑名称" prop="buildingName">
						<el-input v-model="form.buildingName" style="width: 290px" maxlength="20" placeholder="请输入建筑名称"></el-input>
					</el-form-item>

					<el-form-item label="资产类型" prop="buildingType">
						<el-select v-model="form.buildingType" placeholder="请选择资产类型" style="width: 290px">
							<el-option v-for="type in assetTypes" :key="type.key" :label="type.name" :value="type.name"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="建筑面积(m²)" prop="buildingArea">
						<el-input
							v-model="form.buildingArea"
							style="width: 290px"
							@input="handleBuildingInput($event, 'buildingArea')"
							placeholder="请输入建筑面积"
						></el-input>
					</el-form-item>

					<el-form-item label="建成年份" prop="buildYear">
						<el-date-picker
							format="YYYY"
							v-model="form.buildYear"
							style="width: 290px"
							type="year"
							value-format="YYYY"
							placeholder="请选择建成年份"
						></el-date-picker>
					</el-form-item>

					<el-form-item label="城市" prop="address">
						<el-cascader
							v-model="form.address"
							style="width: 290px"
							placeholder="请选择城市"
							:options="$vuexStore.state.cityArray"
							@change="handleChange"
							:props="{ value: 'label' }"
						></el-cascader>
					</el-form-item>

					<el-form-item label="详细地址" prop="street">
						<el-input v-model="form.street" maxlength="50" style="width: 290px" placeholder="请输入详细地址"></el-input>
					</el-form-item>

					<el-form-item label="维护情况" prop="maintenanceStatus">
						<el-select v-model="form.maintenanceStatus" placeholder="请选择维护情况" style="width: 290px">
							<el-option label="优秀" value="优秀"></el-option>
							<el-option label="良好" value="良好"></el-option>
							<el-option label="一般" value="一般"></el-option>
							<el-option label="无维护" value="无维护"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item label="完整年度物业费用(元)" prop="propertyCost">
						<el-input
							style="width: 290px"
							v-model="form.propertyCost"
							@input="handleBuildingInput($event, 'propertyCost')"
							placeholder="请输入完整年度物业费用"
						></el-input>
					</el-form-item>

					<el-form-item label="完整年度收入总和(元)" prop="totalIncome">
						<el-tooltip :content="tooltipObj.totalIncomeText" :disabled="handleIsEmpty()" placement="top-start">
							<el-input
								v-model="form.totalIncome"
								style="width: 290px"
								@input="handleBuildingInput($event, 'totalIncome')"
								placeholder="请输入完整年度收入总和"
							></el-input>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="租金(元/m²/年)" prop="rentCost">
						<el-tooltip :disabled="handleIsEmptySquare('rentCost')" :content="tooltipObj.rentCostText" placement="top-start">
							<el-input
								v-model="form.rentCost"
								@input="handleBuildingInput($event, 'rentCost')"
								style="width: 290px"
								placeholder="请输入租金"
							></el-input>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="售价(元/m²)" prop="sellingPrice">
						<el-tooltip :disabled="handleIsEmptySquare('sellingPrice')" :content="tooltipObj.sellingPriceText" placement="top-start">
							<el-input
								v-model="form.sellingPrice"
								style="width: 290px"
								@input="handleBuildingInput($event, 'sellingPrice')"
								placeholder="请输入售价"
							></el-input>
						</el-tooltip>
					</el-form-item>

					<el-form-item label="毛报酬率" prop="grossRate" class="fromContainer">
						<el-tooltip content="建议输入0＜x＜39%的数值" placement="top-start">
							<el-input
								v-model="form.grossRate"
								style="width: 290px"
								@input="handleBuildingInput($event, 'grossRate')"
								placeholder="请输入毛报酬率"
							></el-input>
						</el-tooltip>

						<div class="bfcontainer">%</div>
					</el-form-item>

					<el-form-item label="物业费用增加" prop="propertyRatio" class="fromContainer">
						<el-input
							v-model="form.propertyRatio"
							@input="handleInput($event, 'propertyRatio')"
							style="width: 290px"
							placeholder="请输入物业费用增加"
						>
						</el-input>
						<div class="bfcontainer">%</div>
					</el-form-item>

					<el-form-item label="收入/租金增长率" prop="rentalRate" class="fromContainer">
						<el-input
							v-model="form.rentalRate"
							style="width: 290px"
							@input="handleInput($event, 'rentalRate')"
							placeholder="请输入收入/租金增长率"
						></el-input>
						<div class="bfcontainer">%</div>
					</el-form-item>
				</div>
			</el-form>
			<div class="btn_wrap">
				<el-button type="primary" @click="submitForm" color="#1868F1">确定</el-button>
				<el-button @click="resetForm" color="#F2F3F5" style="color: #4e5969">取消</el-button>
			</div>
		</div>
		<keep-alive>
			<component :is="componentNames" ref="riskDom1" :key="activeName" :riskType="activeName == '新建楼宇进行风险测评' ? '1' : ''" />
		</keep-alive>
	</div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import constructionAct from '@/assets/images/risks/constructionAct.png';
import construction from '@/assets/images/risks/construction.png';
import selectBuildingAct from '@/assets/images/risks/selectBuildingAct.png';
import selectBuilding from '@/assets/images/risks/selectBuilding.png';
import { getCityData } from '@/api/syt.js';

import riskIndex from './index.vue';
import riskIndexs from './index.vue';
const riskDomIndex = ref();
const riskDom1 = ref();
const tooltipObj = ref({
	totalIncomeText: '',
	rentCostText: '',
	sellingPriceText: '',
	grossRateText: '',
});

const routes = [
	{
		name: '新建楼宇进行风险测评',
		icon: construction,
		componentName: riskIndex,
		icon_active: constructionAct,
	},
	{
		name: '选择楼宇进行风险测评',
		icon: selectBuilding,
		componentName: riskIndexs,
		icon_active: selectBuildingAct,
	},
];

const componentNames = ref(riskIndex);

const activeName = ref('新建楼宇进行风险测评');

const form = ref({
	buildingName: '',
	buildingArea: '',
	address: [],
	city: '',
	district: '',
	maintenanceStatus: '',
	propertyCost: '',
	sellingPrice: '',
	propertyRatio: '',
	buildingType: '',
	buildYear: '',
	street: '',
	totalIncome: '',
	rentCost: '',
	grossRate: '',
	rentalRate: '',
});
const assetTypes = ref([
	{
		name: '写字楼',
		key: '1',
	},
	{
		name: '零售',
		key: '2',
	},
	{
		name: '产业园区',
		key: '3',
	},
	{
		name: '仓储物流',
		key: '4',
	},
	{
		name: '酒店',
		key: '5',
	},
	{
		name: '长租公寓',
		key: '6',
	},
	{
		name: '医疗',
		key: '7',
	},
	{
		name: '综合市场',
		key: '8',
	},
]);
const formRef = ref();
const rules = ref({
	buildingName: [{ required: true, message: '请输入建筑名称', trigger: 'blur' }],
	buildingArea: [{ required: true, message: '请输入建筑面积', trigger: 'blur' }],
	address: [{ required: true, message: '请选择城市', trigger: 'change' }],
	maintenanceStatus: [{ required: true, message: '请选择维护情况', trigger: 'change' }],
	propertyCost: [{ required: true, message: '请输入完整年度物业费用', trigger: 'blur' }],
	sellingPrice: [{ required: true, message: '请输入售价', trigger: 'blur' }],
	propertyRatio: [{ required: true, message: '请输入物业费用增加', trigger: 'blur' }],
	buildingType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
	buildYear: [{ required: true, message: '请选择建成年份', trigger: 'change' }],
	// street: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
	totalIncome: [{ required: true, message: '请输入完整年度收入总和', trigger: 'blur' }],
	rentCost: [{ required: true, message: '请输入租金', trigger: 'blur' }],
	grossRate: [{ required: true, message: '请输入毛报酬率', trigger: 'blur' }],
	rentalRate: [{ required: true, message: '请输入收入/租金增长率', trigger: 'blur' }],
});

const handleChange = (val) => {
	form.value.city = val[0];
	form.value.district = val[1];
};

const handleInput = (val, key) => {
	// 判断小数点后位数超出两位后，只保留两位
	const valArr = val.split('.');
	if (valArr[1]?.length > 2) {
		form.value[key] = valArr[0] + '.' + valArr[1].slice(0, 2);
	} else {
		form.value[key] = val;
	}
};

const handleBuildingInput = (val, key) => {
	// 输入限制大于0的数字，小数点后两位
	const reg = /^[0-9]+(\.[0-9]{1,2})?$/;
	if (!reg.test(val)) {
		form.value[key] = val.replace(/[^\d.]/g, '');
		// 判断小数点后位数超出两位后，只保留两位
		const valArr = val.split('.');
		if (valArr[1]?.length > 2) {
			form.value[key] = valArr[0] + '.' + valArr[1].slice(0, 2);
		}
	} else {
		form.value[key] = val;
	}
};

// 控制是否展示输入建议
const handleIsEmptySquare = (val) => {
	// rentCost 租金
	// sellingPrice 售价
	if (val === 'rentCost' || val === 'sellingPrice') {
		if (form.value.city && form.value.district) {
			getCityData({ city: form.value.city, district: form.value.district }).then((res) => {
				if (res.code == 200) {
					let arr = []; // 存放每平米租金
					let arrs = []; // 存放每平米售价
					res.data.forEach((element) => {
						arr.push(element.perSquareRent);
						arrs.push(element.perSquareSale);
					});
					// 每平米租金最大值最小值
					const rentMax = Math.max(...arr);
					const rentMin = Math.min(...arr);
					// 每平米租金最大值最小值
					const saleMax = Math.max(...arrs);
					const saleMin = Math.min(...arrs);
					let zMax = (rentMax * 365).toFixed(2);
					let yMin = (rentMin * 365).toFixed(2);
					let nMax = (saleMax * 10000).toFixed(2);
					let mMax = (saleMin * 10000).toFixed(2);

					if (val === 'rentCost') {
						tooltipObj.value.rentCostText = `建议输入${yMin}＜x＜${zMax}的数值`;
					} else {
						tooltipObj.value.sellingPriceText = `建议输入${mMax}＜x＜${nMax}的数值`;
					}
					return false;
				}
			});
		} else {
			return true;
		}
	}
};

// 控制是否展示输入建议
const handleIsEmpty = (val) => {
	// 建筑面积
	if (form.value.buildingArea && form.value.propertyCost) {
		let come = ((Number(form.value.buildingArea) * 20 + Number(form.value.propertyCost)) / 0.9328).toFixed(2);
		tooltipObj.value.totalIncomeText = `建议输入大于${come}的数值`;
		return false;
	} else {
		return true;
	}
};

const submitForm = () => {
	console.log(riskDom1.value, 'riskDom1');
	let obj = JSON.parse(JSON.stringify(form.value));
	formRef.value.validate((valid) => {
		if (valid) {
			riskDom1.value.handleUpdateClose({
				visible: false,
				form: obj,
			});
		}
	});
};
const resetForm = () => {
	formRef.value.resetFields();
};

const handleTabClick = (item, index) => {
	riskDomIndex.value = index;
	componentNames.value = item.componentName;
	activeName.value = item.name;
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
::v-deep .el-form-item {
	margin-bottom: 16px;
}
.fromContainer {
	position: relative;
}
.bfcontainer {
	position: absolute;
	right: 13px;
	top: 0px;
	color: #86909c;
}

.tab_box {
	margin-top: 16px;
	width: 100%;
	box-sizing: border-box;
	font-size: 14px;
	font-weight: 600;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	background-color: transparent;

	.title {
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		margin-right: 15px;
		color: #1a1a1a;
	}

	.tab {
		width: 216px;
		height: 48px;
		padding: 0 16px;
		display: flex;
		align-items: center;
		cursor: pointer;
		font-size: 16px;
		font-weight: 500;
		line-height: 24px;
		color: #4e5969;
		background-color: #f2f3f5;
		border-radius: 4px 4px 0 0;
		img {
			width: 16px;
			height: 16px;
			margin-top: -3px;
			margin-right: 4px;
		}
	}

	.tabAct {
		font-size: 16px;
		color: #1868f1;
		background-color: #fff;
	}
}
.constructions {
	width: calc(100% - 16px);
	padding: 20px 16px;
	// height: 306px;
	box-sizing: border-box;
	background: #fff;
	margin-bottom: 16px;
}

.container_build {
	display: flex;
	flex-wrap: wrap;
	// align-items: flex-start;
	// flex-direction: row;
	gap: 0 40px;
	// .content_1 {
	// 	width: 243px;
	// 	height: 374px;
	// }
}
.btn_wrap {
	display: flex;
	justify-content: start;
}
</style>
