<template>
	<div class="main-container">
		<!-- 顶部导航 -->
		<header class="header">
			<div class="logo">
				<img src="../../assets/newLogo.png" style="width: 38px; height: 38px" alt="术木智能商业地产价值分析平台" />
				<div class="logo-text">术木智能商业地产价值分析平台</div>
			</div>

			<!-- 登录按钮 -->
			<div class="login_btn">
				<el-badge :value="$vuexStore.state.shoppingCart" class="item" v-if="isLogin">
					<el-icon size="22" style="cursor: pointer; color: #fff" @click="handlePushCart"><ShoppingCart /></el-icon>
				</el-badge>
				<div class="login-btn" @click="onLogin" v-if="!isLogin">登录</div>

				<el-popover placement="bottom" :width="130" trigger="hover" v-show="!isLogin" ref="popoverRef">
					<template #reference>
						<div class="head" v-show="isLogin">
							<img v-if="!imageUrl" class="headImg" src="@/assets/rectangles.png" alt="" />
							<img v-if="imageUrl" class="headImg" :src="imageUrl" alt="" />
						</div>
					</template>
					<div class="popover_box">
						<div class="userName">{{ $vuexStore.state.userInfo.userName || '用户名' }}</div>
						<div class="personal" @click="onMy">个人中心</div>
						<div class="exitlogon" v-if="isLogin" @click="onQuit">退出登录</div>
					</div>
				</el-popover>
			</div>
		</header>

		<!-- 主要内容区 -->
		<div class="content">
			<!-- 中心标题 -->
			<div class="center-title">
				<div>
					<img src="../../assets/images/home/<USER>/discoveringValue.png" alt="" />
				</div>
				<div class="phone-box">
					<div class="phone-box-item">
						<img src="../../assets/images/home/<USER>/phone.png" alt="" />
					</div>
					<div class="phone">************</div>
				</div>
				<div class="scroll-down">
					<i class="el-icon-arrow-down"></i>
				</div>
			</div>

			<!-- 导航按钮组 -->
			<div class="nav-buttons">
				<button
					class="nav-btn"
					v-for="(item, index) in navList"
					:key="item.name"
					v-show="!isMobile || item.type === 'mobile'"
					@mouseenter="handleMouseEnter(index)"
					@mouseleave="handleMouseLeave(index)"
					@click="handleOpenUrl(item)"
				>
					<img src="../../assets/images/home/<USER>/arrow.png" v-if="hoverIndex === index" class="arrow" alt="" />
					<img class="nav-img" :src="item.img" alt="标标准" />
					<span>{{ item.name }}</span>
				</button>
			</div>
		</div>
		<siderBar class="siderBar"></siderBar>
	</div>
</template>
<script setup>
import siderBar from '../../component/siderBar/index.vue';
import shangyutong from '../../assets/images/home/<USER>/shangYutong.png';
import standard from '../../assets/images/home/<USER>/standard.png';
import shangqiuTong from '../../assets/images/home/<USER>/shangqiuTong.png';
import dailyAuto from '../../assets/images/home/<USER>/dailyAuto.png';
import xiaogeAI from '../../assets/images/home/<USER>/xiaogeAI.png';
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useStore, vuexStore } from '../../store/index.js';
const store = useStore();
const isLogin = ref(true);
const router = useRouter();
const imageUrl = ref();
const navList = ref([
	{
		img: standard,
		name: '标标准',
		type: 'mobile',
		path: '/biaobiaozhun',
	},
	{
		img: shangyutong,
		name: '商宇通',
		type: 'mobile',
		path: '/IntroductionPage',
		isMobilePath: '/mobileEndSyt',
	},
	{
		img: shangqiuTong,
		name: '商估通',
		type: 'mobile',
		path: '/introduce',
		isMobilePath: '/introduceMobile',
	},
	// {
	// 	img: dailyAuto,
	// 	name: '商报auto',
	// 	type: 'mobile',
	// 	path: '/shangAuto',
	// 	isMobilePath: '/mobileShangAuto',
	// },
	// {
	// 	img: xiaogeAI,
	// 	name: '小葛AI',
	// 	type: 'mobile',
	// 	href: 'https://smai.biaobiaozhun.com/chat/ZMSVkr7vL1ezsC9B',
	// },
]);
// hover按钮
const hoverIndex = ref();
//是否是移动端
const isMobile = ref(false);
// 监听
watch(
	() => vuexStore.state.userInfo,
	(newVal, oldVal) => {
		if (vuexStore.state.userInfo.headImage) {
			let randomInt = Math.floor(Math.random() * 100) + 1;
			imageUrl.value = store.imagePathPrefix + vuexStore.state.userInfo.headImage + '?v=' + randomInt;
		} else {
			imageUrl.value = null;
		}
	}
);

// 挂载
onMounted(() => {
	handleIsMobile(); //判断是否是移动端
	handleLogin();
	if (vuexStore.state.userInfo.headImage) {
		let randomInt = Math.floor(Math.random() * 100) + 1;
		imageUrl.value = store.imagePathPrefix + vuexStore.state.userInfo.headImage + '?v=' + randomInt;
	} else {
		imageUrl.value = null;
	}
});

//监听屏幕
window.addEventListener('resize', () => {
	const screenWidth = window.innerWidth;
	if (screenWidth < 768) {
		isMobile.value = true;
	} else {
		isMobile.value = false;
	}
});

//判断是否是移动端
const handleIsMobile = () => {
	const innerWidth = window.innerWidth;
	if (innerWidth < 768) {
		isMobile.value = true;
	} else {
		isMobile.value = false;
	}
};
// 登录
function handleLogin() {
	let token = window.localStorage.getItem('token');
	if (token == null) {
		isLogin.value = false;
	}
}
// 个人中心
const onMy = () => {
	router.push({
		path: '/profile/mymessage',
	});
};

// 购物车跳转
function handlePushCart(params) {
	router.push({
		path: '/shoppingCart',
	});
}

// 退出登录
const onQuit = () => {
	onExit();
	router.push({
		path: '/login',
		query: '1',
		replace: true,
	});
};
// 退出登录
const onExit = () => {
	localStorage.removeItem('token');
	// 清空vuex中用户数据
	vuexStore.dispatch('clearData');
	vuexStore.commit('handleNewUserCoupon', false); // 新用户专享优惠券
	isLogin.value = false;
};
// 鼠标移入
const handleMouseEnter = (index) => {
	hoverIndex.value = index;
};

const handleMouseLeave = (index) => {
	hoverIndex.value = null;
};

// 打开链接
const handleOpenUrl = (item) => {
	if (item.isMobilePath && isMobile.value) {
		router.push(item.isMobilePath);
	} else if (item.path) {
		router.push(item.path);
		vuexStore.dispatch('handleDistrict'); // 获取区域
	} else {
		if (item.href) {
			window.open(item.href, '_blank');
		}
	}
};
// 登录
const onLogin = () => {
	router.push({
		path: '/login',
	});
};
</script>
<style scoped lang="scss">
.main-container {
	min-height: 100vh;
	background-image: url('../../assets/images/home/<USER>/page.png');
	background-size: cover;
	background-position: center;
	position: relative;
	display: flex;
	flex-direction: column;

	.header {
		height: 70px;
		padding: 0px 23px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.logo {
			display: flex;
			align-items: center;
			gap: 10px;
			color: #fff;
			.logo-text {
				font-weight: 600;
				font-size: 20px;
				color: #ffffff;
				line-height: 28px;
				text-align: left;
				font-style: normal;
			}
		}
		.login-btn {
			width: 60px;
			height: 28px;
			background: rgba(255, 255, 255, 0.98);
			border-radius: 14px;
			border: 1px solid rgba(255, 255, 255, 0.8);
			backdrop-filter: blur(8px);
			cursor: pointer;
			font-weight: 400;
			font-size: 14px;
			color: #333333;
			line-height: 28px;
			text-align: center;
		}

		.login_btn {
			font-size: 12px;
			width: auto;
			height: 60px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			::v-deep .el-badge {
				margin-right: 24px;
				line-height: 0px;
				margin-top: 2px;
				.el-badge__content--danger {
					background-color: rgb(24, 104, 241) !important;
				}
			}
			.phone {
				margin-right: 30px;
				display: flex;
				align-items: center;
				line-height: 22px;
				font-size: 14px;
				font-weight: 500;

				img {
					width: 18px;
					height: 18px;
					margin-right: 8px;
				}
			}
			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				border: 1px solid gray;
				box-sizing: border-box;
				margin: 0 5px;
				border-radius: 4px;
				font-size: 1rem;
			}

			.btn1 {
				background-color: #38609a;
				color: rgba(255, 255, 255, 1);
			}

			.head {
				width: 33px !important;
				height: 33px;
				border-radius: 50%;
				margin-left: 20px;

				img {
					cursor: pointer;
					width: 33px;
					height: 33px;
					border-radius: 50%;
				}
			}
		}
	}
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: start;
		padding: 130px 60px 40px 60px;
		.center-title {
			text-align: center;
			color: #fff;
			margin-bottom: 44px;
			.phone-box {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 24px;
				margin-top: 22px;
				.phone-box-item {
					width: 24px;
					margin-right: 6px;
					height: 24px;
					line-height: 28px;
				}

				.phone {
					font-weight: 400;
					font-size: 16px;
					color: #333333;
					line-height: 22px;
				}
			}
		}

		.nav-buttons {
			display: flex;
			gap: 52px;
			flex-wrap: wrap;
			justify-content: center;
			.nav-btn:hover {
				transform: translateY(-3px);
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
			}
			.nav-btn {
				position: relative;
				width: 200px;
				height: 64px;
				background: rgba(255, 255, 255, 0.66);
				box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.08);
				border-radius: 32px;
				border: 1px solid #ffffff;
				backdrop-filter: blur(15px);
				display: flex;
				align-items: center;
				transition: all 0.3s;
				cursor: pointer;
				.arrow {
					position: absolute;
					left: 50%;
					top: -35px;
					transform: translateX(-50%);
				}
				.nav-img {
					width: 40px;
					height: 40px;
					margin-left: 28px;
				}
				span {
					margin-left: 16px;
					font-weight: 500;
					font-size: 18px;
					color: #333333;
					line-height: 25px;
				}
			}
		}
	}
}

.popover_box {
	display: flex;
	flex-direction: column;
	border-radius: 15px;
	.userName {
		font-size: 16px;
		font-weight: 500;
		line-height: 24px;
		color: #1d2129;
		padding: 8px 16px;
		border-bottom: 1px solid #e7e7e7;
	}
	.personal {
		cursor: pointer;
		margin-top: 8px;
		height: 20px;
		padding: 8px 16px;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: left;
		color: #4e5969;
		&:hover {
			background: #f5f6f7;
			border-radius: 4px;
			color: #1d2129;
		}
	}

	.exitlogon {
		cursor: pointer;
		margin-top: 8px;
		height: 20px;
		padding: 8px 16px;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: left;
		color: #86909c;
		&:hover {
			background: #f5f6f7;
			border-radius: 4px;
			color: #ec655f;
		}
	}
	.headImg {
		cursor: pointer;
		width: 49px;
		height: 49px;
		border-radius: 50%;
		margin: 0 auto;
		cursor: pointer;
	}
}

.siderBar {
	top: 14rem !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.main-container {
		min-height: calc(100vh - 52px);
	}
	.header {
		display: none !important;
	}
	.content {
		padding: 80px 0 0px 0 !important;
		.nav-buttons {
			flex-direction: column;
			gap: 20px !important;
			.nav-btn {
				backdrop-filter: blur(0px) !important;
			}
		}
	}
}
</style>
