<template>
	<div>
				<div class="search">
					<a-row style="display: flex; margin-bottom: 10px">
						<a-col :span="16" style="display: flex; justify-content: center">
							<el-cascader
								style="margin-left: 5px"
								placeholder="请选择城市"
								size="large"
								:options="provinceAndCityData"
								@change="handleChange"
								filterable
								:props="{ value: 'label' }"
								v-model="selectedOptions"
							>
							</el-cascader>
							<el-select style="margin-left: 20px; width: 150px" v-model="buildingTypesValue" placeholder="全部资产" size="large">
								<el-option v-for="(item, index) in buildingTypes" :key="item" :label="item" :value="item" />
							</el-select>
							<el-input v-model="inputValue" placeholder="关键字搜索" style="height: 38px; width: 160px; margin-left: 20px" />
							<el-button round @click="handleSearch">搜索</el-button>
						</a-col>
						<a-col :span="8" style="display: flex">
							<div>
								<el-button round @click="handleButtonClick(300)" :class="{ 'active-button': selectedRadius === 300 }">300m</el-button>
								<el-button round @click="handleButtonClick(1000)" :class="{ 'active-button': selectedRadius === 1000 }">1000m</el-button>
								<el-button round @click="handleButtonClick(3000)" :class="{ 'active-button': selectedRadius === 3000 }">3000m</el-button>
							</div>
							</a-col
						>
					</a-row>
				</div>
				<el-scrollbar class="real" height="400px">
					<el-card v-for="item in real" :key="item.ll" shadow="hover" style="margin-bottom: 10px; height: 150px">
						<el-row>
							<el-col :span="6">
								<img :src="`${proxyAddress}${'/upfile_jpg/'}${item.id}${'.jpg'}`" alt="Image" style="width: 100%; height: 150px; object-fit: cover" />
							</el-col>
							<el-col :span="18">
								<div>
									<p>{{ item.name }}</p>
									<p>{{ item.address }}</p>
								</div>
							</el-col>
						</el-row>
					</el-card>
				</el-scrollbar>
				<div class="gaode">
					<gaode @clickChild="clickEven" :radius="selectedRadius" @searchMap="handleSearch"></gaode>
				</div>
					<!-- 新闻列表 -->
				<div class="table">
					<h5>新闻列表</h5>
				<el-table 
				:data="newsLists" 
				:header-cell-style="{background:'#3170a7',color:'#ffffff '}">

					<el-table-column label="新闻标题" width="320" prop="title" class="title_table">
					</el-table-column>

					<el-table-column label="发布时间" width="220" prop="addedtime"></el-table-column>

					<el-table-column label="操作" width="220">
						<template #default>
							<el-button link type="primary" size="small" @click="handleClick">查看详情</el-button>
						</template>
					</el-table-column>
				</el-table>
				</div>
	</div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import gaode from '../../../MapContainer.vue';
import rat from '../../../RatMap.vue';
import http from '@/utils/http';

import { provinceAndCityData } from 'element-china-area-data';
// 新增的交易类型按钮值
const transactionTypeValue = ref('出租'); // 交易类型的数据
const transactionTypes = ['出租', '出售', '证券交易'];
//资产类型搜索
const handleTransactionType = (type) => {
	transactionTypeValue.value = type;
};

const viewDetails = (row) => {
	// 在这里定义 viewDetails 的具体逻辑
	console.log('View details for:', row);
	// 这里可以添加你的其他逻辑
};
const lnglat = ref(''); //经纬度 chang
const proxyAddress = window.location.origin;
const clickEven = (clicklng, clicklat) => {
	lnglat.value = clicklng + ',' + clicklat;
};
const clickEvenReal = (clicklng, clicklat) => {
	lnglat.value = clicklng + ',' + clicklat;
};
const province = ref('');
const city = ref('');
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const rateValue = ref('');
const buildingTypesValue = ref('');
const inputValue = ref('');
//半径圆实现
const selectedRadius = ref(300); // 默认半径值
const queryParams = computed(() => {
	return {
		province: province.value,
		city: city.value,
		degree: rateValue.value,
		de_type: buildingTypesValue.value,
		keywords: inputValue.value,
		loasts: lnglat.value,
		distance: selectedRadius.value,
	};
});
onMounted(() => {
	console.log(proxyAddress, 'ff');
	handleSearch();
});
//绑定大楼
const real = ref([]);
const handleSearch = async () => {
	try {
		const response = await http.get('/api/api.do?act=listMAP', {
			params: queryParams.value,
		});

		// 假设 res 是 response.data 的属性

		real.value = response.list;
		console.log(response.list, real.value, 'success');
	} catch (error) {
		console.error('Error while fetching data:', error);
		// 这里可以添加适当的错误处理逻辑
	}
};
const handleSearchReal = async () => {
	// debugger;
	try {
		const response = await http.get('/api/api.do?act=listMAPTranaction', {
			params: Object.assign({}, queryParams.value, { transactionType: transactionTypeValue.value }),
		});

		// 假设 res 是 response.data 的属性

		real.value = response.list;
		console.log(response.list, real.value, 'success');
	} catch (error) {
		console.error('Error while fetching data:', error);
		// 这里可以添加适当的错误处理逻辑
	}
};
const activeName = ref('rating');
const selectedOptions = ref([]);
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '数据中心', '保障房', '农贸市场'];

const handleButtonClick = (radius) => {
	selectedRadius.value = radius;
};
const handleTabClick = () => {
	// 在这里清空 real 数组
	real.value = [];
	province.value = '';
	city.value = '';
	rateValue.value = '';
	buildingTypesValue.value = '';
	inputValue.value = '';
	lnglat.value = '';
	if (activeName.value === 'deal') {
		handleSearch();
	} else if (activeName.value === 'rating') {
		handleSearchReal();
	}
};
// 获取新闻数据
let newsLists = ref([]);
const getNewsList = async () => {
	// 调用接口获取新闻列表数据
	const { list } = await http.get('api/api.do?act=listNewsNew1');
	// 处理，
	newsLists.value = JSON.parse(JSON.stringify(list));
	console.log('列表',newsLists.value);
};
getNewsList();
// console.log('列表1',newsList);
// 点击查看详情
const handleClick = () => {
	console.log('click');
};
</script>

<style lang="less" scoped>
.table {
	width: 100%;
	height: 100%;
	// text-align: center;
	margin-top: 20px;
}
.search {
	button {
		margin-left: 30px;
	}
}
.real {
	width: 500px;
	position: absolute;
	z-index: 999;
	right: 0;
	bottom: -50px;
}
.scrollbar-demo-item {
	display: flex;
	align-items: center;
	justify-content: center;
	text-align: center;
	border-radius: 4px;
	background: var(--el-color-primary-light-9);
}
.title {
	font-size: 26px;
	font-weight: bold;
	margin: 20px;
}
.gaode {
	height: 400px;
}
.demo-tabs > .el-tabs__content {
	padding: 32px;
	color: #6b778c;
	font-size: 32px;
	font-weight: 600;
}
.active-button {
	background-color: #409eff; // 蓝色背景
	color: white; // 文字颜色
}
</style>