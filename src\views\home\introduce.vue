<template>
	<div>
		<div class="download-container">
			<div class="logo">
				<div class="logo-image">
					<img src="@/assets/images/home/<USER>" alt="商估通" />
				</div>
			</div>

			<div class="content">
				<div class="title">楼宇测评软件</div>
				<div class="downloadSubtitle">动态现金流预测、估值与融资分析、楼宇效用分析</div>

				<div class="download-btn">
					<el-button
						type="primary"
						style="width: 220px; height: 62px; font-size: 24px; box-shadow: 0px 8px 20px 0px #0a23581a"
						@click="handleDownload"
					>
						免费试用
					</el-button>
					<el-button
						type="primary"
						style="width: 220px; height: 62px; font-size: 24px; background: #fff; color: #1868f1; box-shadow: 0px 8px 20px 0px #0a23581a"
						@click="handleCustomSystem"
					>
						定制系统
					</el-button>
					<div class="trial-tip">
						<div class="trial">移动端协作</div>
						<div class="trial_line"></div>
						<div class="trial-tip-text">支持私有化部署</div>
						<div class="trial_line"></div>
						<div class="trial-tip-textt">参数自定义</div>
						<div class="trial_lines"></div>
						<div class="trial-tip-texts">数据私密安全</div>
					</div>
				</div>
			</div>

			<!-- <div class="platform-btns">
				<div class="platform-btn" @click="downloadWindows">
					<div>
						<img src="@/assets/images/home/<USER>" alt="" />
					</div>
					Windows客户端
				</div>
				<div class="platform-btn" @click="downloadMac">
					<div>
						<img src="@/assets/images/home/<USER>" alt="" />
					</div>
					macOS客户端
				</div>
			</div> -->
		</div>

		<div class="introduce-container">
			<div class="introduceTitle">商估通可以帮您完成</div>
			<div class="tabs-wrapper">
				<el-tabs v-model="activeTab" class="custom-tabs">
					<el-tab-pane label="管理资产组合" name="manage">
						<div class="tab-desc" style="margin: 30px 0 -8px 0">支持创建不同的资产组合，帮助您根据不同需求分析复杂的资产运营数据</div>
						<div class="content-box">
							<div class="left-image">
								<img src="@/assets/images/home/<USER>" alt="资产管理" style="margin-bottom: 11px" />
							</div>
						</div>
					</el-tab-pane>
					<el-tab-pane label="自定义创建资产" name="custom">
						<div class="tab-desc">支持为资产组合自定义添加楼宇资产，帮助您维护个人专属资产数据</div>
						<div class="content-box">
							<div class="left-image">
								<img src="@/assets/images/home/<USER>" style="margin-bottom: 32px" alt="资产管理" />
							</div>
						</div>
					</el-tab-pane>
					<el-tab-pane label="动态分析运营数据" name="analysis">
						<div class="tab-desc">支持动态多参数运营资产数据，帮助您快速评估资产数据，做出更佳决策</div>
						<div class="content-box">
							<div class="left-image">
								<img src="@/assets/images/home/<USER>" style="margin-bottom: 32px" alt="资产管理" />
							</div>
						</div>
					</el-tab-pane>
					<el-tab-pane label="生成报告" name="report">
						<div class="tab-desc">
							实时生成现金流预测报告、实时估值报告、标准化产品融资报告
						</div>
						<div class="content-box">
							<div class="left-image">
								<img src="@/assets/images/home/<USER>" style="margin-bottom: 32px" alt="报告生成" />
							</div>
						</div>
					</el-tab-pane>
				</el-tabs>
			</div>
		</div>

		<div class="compare-container">
			<div class="compareTitle">商估通对 Argus 的优势</div>
			<div class="desc">
				<div>假设你在管理一个大型商业地产项目，其中包含多个租户，每个租户的租赁协议都具有不同的租金增长模式。</div>
				<div>
					例如，某些租户的租金可能在每五年以固定百分比增长，而其他租户可能有基于CPI（消费者价格指数）的租金调整，或者根据租户的销售额比例进行浮动租金计算。
				</div>
			</div>

			<div class="compare-box">
				<div class="box_bg">
					<div class="box1"></div>
					<div class="box2"></div>
					<div class="box3"></div>
				</div>
				<div class="header_wrap">
					<div class="header1">对比维度</div>
					<div class="header2">Argus</div>
					<div class="header3">商估通</div>
				</div>
				<div class="content_wrap">
					<div
						class="content_item"
						v-for="(item, index) in compareData"
						:key="index"
						@mouseenter="compareActiveIndex = index"
						@mouseleave="compareActiveIndex = -1"
					>
						<div class="content1">{{ item.title }}</div>
						<div class="content2">{{ item.argus }}</div>
						<div class="content3">{{ item.syt }}</div>
						<div class="content_border_wrap" v-if="compareActiveIndex === index">
							<div class="content_border"></div>
						</div>
					</div>
				</div>

				<div style="height: 10px"></div>
			</div>
		</div>

		<div class="feature-container">
			<div class="featureTitle">让资产评估工作变得简单</div>

			<div class="feature-box">
				<div
					class="feature-item"
					v-for="(item, index) in features"
					:key="index"
					:class="{ active: activeIndex === index }"
					@mouseenter="activeIndex = index"
					@mouseleave="activeIndex = -1"
				>
					<div class="feature-title" :class="{ 'text-primary': activeIndex === index }">
						{{ item.title }}
					</div>
					<div class="feature-desc">{{ item.desc }}</div>
					<div class="feature-icon">
						<img :src="activeIndex === index ? item.icon : item.hoverIcon" :alt="item.title" />
					</div>
				</div>
			</div>
		</div>

		<div class="comments-container">
			<!-- <div class="commentsTitle">商估通用户出于这些原因与我们一直保持合作</div> -->

			<!-- <div class="comments-box"> -->
			<!-- </div> -->
		</div>

		<siderBar class="siderBar"></siderBar>
		<through ref="throughRef"></through>
		<customSystem ref="customSystemRef" @close="handleClose"></customSystem>
		<submittedPop ref="submittedPopRef" />
	</div>
</template>

<script setup>
import siderBar from '../../component/siderBar/index.vue';
import submittedPop from './submittedPop.vue';
import through from './through.vue';
import customSystem from './customSystem.vue';
import { ref } from 'vue';
import guessing11 from '@/assets/images/home/<USER>';
import guessing12 from '@/assets/images/home/<USER>';
import guessing13 from '@/assets/images/home/<USER>';
import guessing14 from '@/assets/images/home/<USER>';
import guessing15 from '@/assets/images/home/<USER>';
import guessing16 from '@/assets/images/home/<USER>';
const throughRef = ref(null);
const customSystemRef = ref(null);
const submittedPopRef = ref(null);
const compareActiveIndex = ref(-1);
const compareData = ref([
	{
		title: '核心定位',
		argus: '国际专业级资产估值与投资分析工具',
		syt: '企业级楼宇测评工具',
		// syt: '企业级全周期资产运营管理平台（Argus国内优化版）',
	},
	{
		title: '技术架构',
		argus: '客户端-服务器架构，支持云端协作（需额外配置）',
		syt: '微服务架构，支持混合云部署，动态负载均衡，API开放生态',
	},
	{
		title: '核心功能',
		argus: '复杂现金流建模、敏感性分析、投资组合优化、自动化估值报告',
		syt: '动态现金流分析、估值与证券化分析、租赁与财务集成、楼宇效用分析',
		// syt: '动态多参数分析、楼宇资产全生命周期管理、租赁与财务集成、移动端操作',
	},
	{
		title: '算法引擎',
		argus: '专业级估值算法（DCF、IRR等），支持蒙特卡洛模拟、敏感性分析',
		syt: '机器学习驱动的动态预测模型（结合市场数据、政策变量）',
	},
	{
		title: '数据处理能力',
		argus: '支持大规模数据建模，内置专业算法和财务函数',
		syt: '支持海量数据动态分析，提供本土化财务模型与参数库',
	},
	{
		title: '协作与权限',
		argus: '支持多用户角色权限分配，审计追踪功能',
		syt: '企业级权限分级管理，支持实时协作与审批流程，移动端同步',
	},
	{
		title: '扩展性与集成',
		argus: '提供API接口，支持与财务、CRM等系统对接',
		syt: '开放API接口，深度集成国内主流企业系统（如OA、财务平台），支持定制化开发',
	},
	{
		title: '成本模型',
		argus: '软件许可费¥50万+/年 + 维护费15%，培训成本¥10万+/团队',
		syt: '订阅制4.8万元/年，含数据和技术服务包',
		// syt: '订阅制¥2.8万/年起，包含本土服务包',
	},
	{
		title: '成本与资源',
		argus: '高昂的许可费用+年度维护成本，需专业培训团队',
		syt: '订阅制模式，成本低于Argus，提供本地化培训与技术支持',
	},
	{
		title: '典型场景',
		argus: '大型商业地产估值、跨国投资组合分析',
		syt: '中大型企业楼宇资产运营、国内商业地产全周期管理、多项目并行决策',
	},
	{
		title: '技术优势',
		argus: '专业级估值引擎，支持蒙特卡洛模拟等高级分析',
		syt: '结合机器学习优化预测模型，支持AI辅助决策',
	},
	{
		title: '用户体验',
		argus: '功能复杂，需长期培训，专业术语门槛高',
		syt: '国内用户习惯优化，支持中文界面与本土化操作逻辑，移动端适配性强',
	},
	{
		title: '劣势',
		argus: '高昂成本、本地化支持有限、学习曲线陡峭，难度较高',
		syt: '生态成熟度较Argus低、部分高级功能需定制开发',
	},
	{
		title: '独特价值',
		argus: '国际认可的专业估值标准，适合跨国投资与复杂财务分析',
		syt: '国产替代首选方案，全生命周期管理+移动端支持，性价比优于Argus',
	},
]);
// 下载
const handleDownload = () => {
	throughRef.value.show();
};

// 定制系统
const handleCustomSystem = () => {
	customSystemRef.value.show();
};

const downloadWindows = () => {
	const link = document.createElement('a');
	link.href = 'https://static.biaobiaozhun.com/sgt-software/%E5%95%86%E4%BC%B0%E9%80%9A-win.zip'; // replace with your app's URL
	link.download = '商估通-win';
	link.click();
	// Windows下载逻辑
};

const downloadMac = () => {
	const a = document.createElement('a');
	a.href = 'https://static.biaobiaozhun.com/sgt-software/%E5%95%86%E4%BC%B0%E9%80%9A-mac.zip';
	a.download = '商估通-mac';
	a.click();
	// Mac下载逻辑
};

const activeTab = ref('manage');

const excelItems = [
	{
		title: '模拟所有租户的租金增长模式：',
		desc: '这些模式可能包括固定的百分比增长、基于市场租金的增长、或者基于某些经济指标如CPI的增长。',
	},
	{
		title: '自动化租金收入预测：',
		desc: '系统需要自动生成未来每一年的租金收入，并根据每个租户的不同租金条款进行调整。',
	},
	{
		title: '整合不同租赁条款并进行敏感性分析：',
		desc: '你希望能够轻松调整假设参数（如市场租金增长率或CPI增长率），并立即查看对整个地产项目现金流的影响。',
	},
];

const sgtItems = [
	{
		title: '复杂租赁条款的建模：',
		desc: '商估通有专门的功能来处理复杂的租赁条款和租金调整机制。它可以自动应用这些规则并生成精确的现金流预测，而这些在Excel中会需要大量的自定义公式和手动工作，且极易出错。',
	},
	{
		title: '自动化与效率：',
		desc: '商估通可以快速处理大量的租户数据和复杂的租赁条款，生成标准化的报告和预测。Excel虽然也可以实现这些，但会非常耗时，尤其当涉及到大量的租户和复杂的租金结构时，管理和维护这些模型将变得极其困难。',
	},
	{
		title: '敏感性分析：',
		desc: '商估通能够快速进行敏感性分析，允许你调整不同的假设条件（如CPI增长、市场租金变化）并即时查看对整体项目财务表现的影响。这在Excel中可能需要重复调整多个工作表和公式，效率低下且容易出错。',
	},
];

const activeIndex = ref(-1);

const features = [
	{
		title: '专业数据动态分析',
		desc: '基于平台强大的分析能力，帮助您高效管理丰富的资产数据和资产组合，让您能更快选、灵活地进行决策，做出更加合理的选择。',
		icon: guessing11,
		hoverIcon: guessing12,
	},
	{
		title: '功能强大逻辑连贯',
		desc: '为您清晰划分逻辑处理模块及分析顺序，按序按需提供数据后，即可自动测算收益、估值等专业结论。',
		icon: guessing13,
		hoverIcon: guessing14,
	},
	{
		title: '交互友好操作清晰',
		desc: '采用PC客户端的设计模式，配合客户明确用户操作引导，帮助您快速理解系统功能，清晰决策获取所需信息。',
		icon: guessing15,
		hoverIcon: guessing16,
	},
];

const handleClose = () => {
	submittedPopRef.value.show();
};
</script>

<style scoped lang="less">
.introduce-container {
	padding-top: 60px;
	background: #fff;
	.introduceTitle {
		text-align: center;
		font-size: 36px;
		margin-bottom: 60px;
		color: #1d2129;
		font-weight: 500;
	}

	.tabs-wrapper {
		:deep(.el-tabs__header) {
			display: flex;
			justify-content: center;
			width: 80%;
			margin: auto;
			border-bottom: 1px solid #e8e8f1;
		}

		:deep(.el-tabs__nav-wrap) {
			&::after {
				display: none;
			}
		}

		:deep(.el-tabs__item) {
			font-size: 26px;
			padding: 0 40px;
			color: #666;

			&.is-active {
				color: #1868f1;
			}
		}

		:deep(.el-tabs__active-bar) {
			background-color: #1868f1;
		}
	}

	.tab-desc {
		display: flex;
		align-items: center;
		justify-content: center;
		color: #23366e;
		margin: 31px 0 -5px 0;
		text-align: center;
		font-size: 18px;

		.tab-desc-report {
			font-weight: 600;
			padding: 10px 20px;
			background-color: #d8e6ff;
			border-radius: 6px;
			margin: 0 6px;
			opacity: 0.7;
		}
	}

	.content-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 30px;
		.left-image {
			margin: auto;
			img {
				height: 514px;
				object-fit: contain;
			}
		}
		img {
			width: 100%;
		}
	}
}

.compare-container {
	padding: 60px 0;
	margin: 0 auto;
	background: #f8fafd;

	.compareTitle {
		text-align: center;
		font-size: 36px;
		margin-bottom: 60px;
		color: #1d2129;
		font-weight: 500;
	}

	.desc {
		width: 62%;
		margin: auto;
		text-align: left;
		color: #000;
		margin-bottom: 40px;
		font-size: 16px;
		font-weight: 400;
		line-height: 22.4px;
	}

	.compare-box {
		position: relative;
		.box_bg {
			z-index: 1;
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			gap: 16px;

			.box1 {
				border-radius: 12px;
				width: 124px;
				background: linear-gradient(180deg, #f2f4f7 0%, #ffffff 49.52%, #ffffff 100%);
			}
			.box2 {
				border-radius: 12px;
				width: 456px;
				background: linear-gradient(180deg, #e9f3fe 0%, #ffffff 49.52%, #ffffff 100%);
			}
			.box3 {
				border-radius: 12px;
				width: 588px;
				background: linear-gradient(180deg, #e9ebfe 0%, #ffffff 50%, #ffffff 100%);
			}
		}

		.header_wrap {
			position: relative;
			z-index: 9;
			height: 64px;
			display: flex;
			justify-content: center;
			gap: 16px;
			margin-bottom: 10px;
			.header1 {
				width: 124px;
				background: linear-gradient(284.14deg, #dfe4ea 4.47%, #ecf1f7 88.62%);
				border: 1px solid #dee5ec;

				color: #0f2860;
			}
			.header2 {
				width: 456px;
				background: linear-gradient(95.82deg, #eff5fe 5.7%, #cfe5fc 73.5%);
				border: 1px solid #b7daff;
				color: #1868f1;
			}
			.header3 {
				width: 588px;
				background: linear-gradient(95.82deg, #eae7fb 5.7%, #cacdfb 73.5%);
				border: 1px solid #cdcffa;
				color: #3141c5;
			}
			.header1,
			.header2,
			.header3 {
				display: flex;
				align-items: center;
				justify-content: center;
				box-sizing: border-box;
				border-radius: 12px;
				font-size: 20px;
				font-weight: 500;
			}
		}
		.content_wrap {
			position: relative;
			z-index: 9;
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			.content_item {
				position: relative;
				z-index: 9;
				height: 40px;
				display: flex;
				justify-content: center;
				gap: 16px;
				box-sizing: border-box;
				cursor: pointer;
				width: 1200px;
				.content1 {
					width: 124px;
					justify-content: center;
					color: #0f2860;
				}
				.content2 {
					width: 456px;
					text-align: left;
					padding-left: 20px;
					color: #5e6a8e;
				}
				.content3 {
					width: 588px;
					text-align: left;
					padding-left: 20px;
					color: #0f2860;
				}
				.content1,
				.content2,
				.content3 {
					display: flex;
					align-items: center;
					box-sizing: border-box;
					font-size: 14px;
					font-weight: 400;
					line-height: 22px;
				}
				.content_border_wrap {
					z-index: 999;
					width: 100%;
					position: absolute;
					left: 0;
					top: 0;
					display: flex;
					justify-content: center;
					.content_border {
						width: 1200px;
						height: 40px;
						border: 2px solid #b0d4ff;
						box-sizing: border-box;
					}
				}
			}
		}
	}
}

.feature-container {
	padding: 60px 40px;
	background: #fff;

	.featureTitle {
		font-size: 36px;
		font-weight: 500;
		text-align: center;
		color: #1d2129;
		margin-bottom: 40px;
	}

	.feature-box {
		display: flex;
		justify-content: space-between;
		gap: 35px;
		max-width: 1200px;
		margin: 0 auto;

		.feature-item {
			flex: 1;
			background: #f7faff;
			border-radius: 12px;
			padding: 0px 32px;
			position: relative;
			min-height: 475px;
			transition: all 0.3s ease;
			cursor: pointer;

			&:hover {
				transform: translateY(-2px);
				box-shadow: 0 8px 24px rgba(24, 104, 241, 0.1);
			}

			&.active {
				background: linear-gradient(180deg, #e8f3ff 0%, #f7fbff 100%);

				.feature-title {
					color: #0a42f1;
				}
				.feature-desc {
					color: #1d2129;
				}

				.feature-icon {
					transform: scale(1.05);
				}
			}

			.feature-title {
				height: 100px;
				color: #1d2129;
				margin-bottom: 25px;
				transition: color 0.3s ease;
				font-size: 24px;
				font-weight: 500;
				line-height: 100px;
				border-bottom: 1px solid #e8e8f1;
			}

			.feature-desc {
				font-size: 16px;
				font-weight: 400;
				line-height: 24px;
				font-size: 14px;
				color: #8b94ac;
			}

			.feature-icon {
				position: absolute;
				right: 0;
				bottom: 0;
				transition: transform 0.3s ease;

				img {
					width: 100%;
					height: 100%;
					object-fit: contain;
				}
			}
		}
	}
}

.text-primary {
	color: #1868f1 !important;
}

.comments-container {
	padding: 0px 0px 100px 0;
	background: #fff;
	.commentsTitle {
		text-align: center;
		color: #1d2129;
		font-size: 36px;
		font-weight: 500;
		line-height: 170px;
	}
}

.download-container {
	height: calc(100vh - 60px);
	// background: linear-gradient(180deg, #edf4ff 0%, #f7faff 100%);
	background: #e1eefd;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;

	.logo {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 100%;
		height: 166px;
		background: linear-gradient(180deg, #a1c6ff 0%, rgba(52, 121, 233, 0) 100%);
		.logo-image {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: end;
			img {
				width: 56px;
				z-index: 99;
				object-fit: contain;
			}
		}
	}

	.content {
		background-image: url('../../assets/images/home/<USER>');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		width: 75%;
		height: calc(100vh - 289px);
		text-align: center;
		z-index: 2;

		.title {
			color: #1d2129;
			margin: 207px auto 8px auto;
			font-size: 60px;
			font-weight: 600;
			line-height: 84px;
		}

		.downloadSubtitle {
			color: #23366e;
			margin-bottom: 66px;
			font-size: 20px;
			font-weight: 400;
			line-height: 28px;
		}

		.download-btn {
			margin-bottom: 40px;

			.el-button {
				padding: 12px 40px;
				font-size: 16px;
				border-radius: 12px;
				background: #1868f1;
				border: none;

				&:hover {
					background: #4288f3;
				}
			}

			.trial-tip {
				margin-top: 12px;
				height: 22px;
				display: flex;
				align-items: center;
				justify-content: center;
				.trial_line {
					width: 2px;
					height: 16px;
					background: #266efa;
					margin: -2px 8px 0px 8px;
				}
				.trial_lines {
					width: 2px;
					height: 16px;
					background: #1654f5;
					margin: -2px 8px 0px 8px;
				}
				.trial-tip-text {
					font-size: 16px;
					font-weight: 600;
					line-height: 22px;
					color: #2165f9;
				}
				.trial-tip-textt {
					font-size: 16px;
					font-weight: 600;
					line-height: 22px;
					color: #0f55eb;
				}
				.trial-tip-texts {
					font-size: 16px;
					font-weight: 600;
					line-height: 22px;
					color: #0a40ef;
				}
			}
			.trial {
				font-size: 16px;
				font-weight: 600;
				line-height: 22px;
				color: #2e7bfe;
			}
		}
	}

	.platform-btns {
		display: flex;
		justify-content: center;
		margin-top: 78px;
		gap: 32px;

		.platform-btn {
			display: flex;
			align-items: center;
			padding: 14.8px 20px;
			background: #fff;
			border-radius: 12px;
			cursor: pointer;
			transition: all 0.3s;

			img {
				margin-right: 8px;
			}

			&:hover {
				background: #f5f7fa;
			}
		}
	}
}

.siderBar {
	top: 14rem !important;
}
</style>
