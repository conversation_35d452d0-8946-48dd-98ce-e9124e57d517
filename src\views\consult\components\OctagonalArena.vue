<template>
	<div class="content">
		<el-dialog v-model="dialogTableVisible" title="对比资产">
			<el-row :gutter="20">
				<el-col :span="4">
					<el-cascader placeholder="请选择城市" size="large" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }">
					</el-cascader>
				</el-col>
				<el-col :span="4">
					<el-select style="margin-left: 20px" v-model="rateValue" placeholder="全部资产评级" size="large">
						<el-option v-for="(item, value) in rate" :key="item" :label="item" :value="item" />
					</el-select>
				</el-col>
				<el-col :span="4">
					<el-select style="margin-left: 20px" v-model="buildingTypesValue" placeholder="全部资产" size="large">
						<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" /> </el-select
				></el-col>
				<el-col :span="4"> <el-input v-model="essential" placeholder="请输入关键字" size="large"></el-input></el-col>
				<el-col :span="6">
					<el-button type="primary" @click="Compared()">查询</el-button>
					<el-button type="primary">重置</el-button></el-col
				>
			</el-row>
			<el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange" stripe>
				<el-table-column type="selection" width="55" ref="multipleTableRef" />
				<el-table-column
					v-for="(column, index) in tableColumns"
					:key="index"
					:label="column.label"
					:prop="column.prop"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table>
			<el-pagination
				@current-change="handleCurrentChange"
				:current-page="currentPage"
				small
				background
				layout="prev, pager, next"
				class="mt-4"
				:total="tableData.length"
			/>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogTableVisible = false">取消</el-button>
					<el-button type="primary" @click="save()"> 确定 </el-button>
				</span>
			</template>
		</el-dialog>

		<el-button type="primary" @click="choose()">选择对比资产</el-button>
		<div class="main">
			<!-- <el-table :data="tableDataLeft" style="width: 600px" stripe>
				<el-table-column
					v-for="(column, index) in tableColumnsValue"
					:key="index"
					:label="column.label"
					:prop="column.prop"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table> -->
			<el-descriptions class="margin-top" :column="1" :size="size" :style="blockMargin" border>
				<el-descriptions-item label="物业名称">{{ tableDataLeft.property_name }}</el-descriptions-item>
				<el-descriptions-item label="建筑面积">{{ tableDataLeft.construction_area }}</el-descriptions-item>
				<el-descriptions-item label="业态">{{ tableDataLeft.parkng_lot }}</el-descriptions-item>
				<el-descriptions-item label="地址">{{ tableDataLeft.address }}</el-descriptions-item>
				<el-descriptions-item label="停车场">{{ tableDataLeft.construction_area }}</el-descriptions-item>
				<el-descriptions-item label="总层数（标准层高）">{{ tableDataLeft.floor }}</el-descriptions-item>
				<el-descriptions-item label="车位费">{{ tableDataLeft.parking_fee }}</el-descriptions-item>
				<el-descriptions-item label="得房率">{{ tableDataLeft.room_probability }}</el-descriptions-item>
			</el-descriptions>
			<div ref="echartsContainer" style="width: 400px; height: 400px; padding: 0 80px"></div>
			<!-- <el-table :data="tableDataRight" style="width: 600px" stripe>
				<el-table-column
					v-for="(column, index) in tableColumnsValue"
					:key="index"
					:label="column.label"
					:prop="column.prop"
					:width="column.width"
					:show-overflow-tooltip="column.showOverflowTooltip"
				/>
			</el-table> -->
			<el-descriptions class="margin-top" :column="1" :size="size" :style="blockMargin" border>
				<el-descriptions-item label="物业名称">{{ tableDataRight.property_name }}</el-descriptions-item>
				<el-descriptions-item label="建筑面积">{{ tableDataRight.construction_area }}</el-descriptions-item>
				<el-descriptions-item label="业态">{{ tableDataRight.parkng_lot }}</el-descriptions-item>
				<el-descriptions-item label="地址">{{ tableDataRight.address }}</el-descriptions-item>
				<el-descriptions-item label="停车场">{{ tableDataRight.construction_area }}</el-descriptions-item>
				<el-descriptions-item label="总层数（标准层高）">{{ tableDataRight.floor }}</el-descriptions-item>
				<el-descriptions-item label="车位费">{{ tableDataRight.parking_fee }}</el-descriptions-item>
				<el-descriptions-item label="得房率">{{ tableDataRight.room_probability }}</el-descriptions-item>
			</el-descriptions>
		</div>
	</div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { provinceAndCityData } from 'element-china-area-data';
import http from '@/utils/http';
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
const echartsContainer = ref(null);
const myChart = ref(null);
const xAxis = ref([]);
const yAxis = ref([]);
const title = ref([]);
const multipleTableRef = ref(null);
const tableDataLeft = ref([]);
const tableDataRight = ref([]);
onMounted(() => {
	// 初始化 ECharts 实例
	myChart.value = echarts.init(echartsContainer.value);
	window.addEventListener('resize', () => {
		myChart.value.resize(); // 窗口发生改变就更新echarts
	});
	// 设置图表选项
	myChart.value.setOption({
		title: {
			text: '维度分析',
			left: 'center',
		},
		legend: {
			data: ['Allocated Budget', 'Actual Spending'],
			top: 30,
		},
		radar: {
			indicator: [{ name: 'AA' }, { name: 'AA' }, { name: 'AA' }, { name: 'AA' }, { name: 'AA' }, { name: 'AA' }],
		},
		series: [
			{
				type: 'radar',
				data: [
					{
						value: [8, 8, 8],
						name: 'Allocated Budget',
					},
					{
						value: [6, 18, 6],
						name: 'Actual Spending',
					},
				],
			},
		],
	});
});
const save = () => {
	// 定义一个数组用于存储 keyw 值
	const keywArray = [];

	if (multipleSelection.value.length > 2) {
		ElMessage.error('最多选择2个维度');
		return;
	}
	// 清空数组
	tableDataLeft.value = [];
	tableDataRight.value = [];

	// 获取 keyw 值并存储到 keywArray
	multipleSelection.value.forEach((item, index) => {
		if (item.keyw) {
			keywArray.push(item.keyw);
			// 将选中的对象添加到数组
			if (index === 0) {
				tableDataLeft.value.push(item);
			} else if (index === 1) {
				tableDataRight.value.push(item);
			}
		}
	});
	xAxis.value = multipleSelection.value.map((item) => {
		return item.line1 ? item.line1.split(',') : [];
	});

	yAxis.value = multipleSelection.value.map((item) => {
		return item.line2 ? item.line2.split(',') : [];
	});

	updateChart(keywArray);

	dialogTableVisible.value = false;
};
const choose = () => {
	dialogTableVisible.value = true;
};
const updateChart = (keywArray) => {
	const indicator = xAxis.value[0].map((name) => ({ name: name }));
	console.log(indicator, 'indicator');
	myChart.value.setOption({
		title: {
			text: '维度分析',
			left: 'center',
		},
		legend: {
			data: keywArray,
			top: 30,
		},
		radar: {
			indicator: indicator,
		},
		series: [
			{
				type: 'radar',
				data: [
					{
						value: yAxis.value[0],
						name: keywArray[0],
					},
					{
						value: yAxis.value[1],
						name: keywArray[1],
					},
				],
			},
		],
	});
};
const multipleSelection = ref([]);
const tableData = ref([]);
const handleSelectionChange = (val) => {
	console.log(val, 'handleSelectionChange');
	multipleSelection.value = val;
};

const tableColumns = [
	{ label: '关键字', prop: 'keyw', width: '300' },
	{ label: '资产类型', prop: 'type', width: '300' },
];
const tableColumnsValue = [
	{ label: '物业名称', prop: 'property_name' },
	{ label: '建筑面积', prop: 'construction_area' },
	{ label: '业态', prop: 'parkng_lot' },
	{ label: '地址', prop: 'address' },
	{ label: '停车场', prop: 'parkng_lot' },
	{ label: '总层数（标准层高）', prop: 'floor' },
	{ label: '车位费', prop: 'parking_fee' },
	{ label: '得房率', prop: 'room_probability' },
];
const currentPage = ref(1);

const handleCurrentChange = (val) => {
	currentPage.value = val;
};
const queryParams = computed(() => {
	return {
		city: city.value,
		degree: rateValue.value,
		province: province.value,
		de_type: buildingTypesValue.value,
		search: essential.value,
		currentPage: currentPage.value,
	};
});
const Compared = async () => {
	try {
		// 发送请求
		const response = await http.get('/api/a_new_cal_bajiao.do?act=getBajiaoList', {
			params: queryParams.value, // 注意这里
		});

		tableData.value = response;
		console.log(tableData.value, 'tableData');
	} catch (error) {
		console.error('请求失败', error);
	}
};

const dialogTableVisible = ref(false);
const selectedOptions = ref([]);
const province = ref('');
const city = ref('');
const essential = ref('');
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
};
const rateValue = ref('');
const rate = ['A+', 'A', 'B+', 'B', 'B-', 'C'];
const buildingTypes = ['购物中心', '写字楼', '百货大楼', '产业园区', '仓储物流', '酒店', '公寓', '医疗康养', '数据中心', '保障房', '农贸市场'];
const buildingTypesValue = ref('');
</script>

<style lang="less" scoped>
.content {
	margin: 20px 0 0 20px;
	padding: 20px 0 0 20px;
}
.main {
	display: flex;
	justify-content: space-around;
	align-items: center;
}
</style>
