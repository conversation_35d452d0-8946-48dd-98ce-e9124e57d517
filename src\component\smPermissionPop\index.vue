<template>
	<el-dialog
		v-model="dialogObj.dialogVisible"
		:close-on-click-modal="false"
		:show-close="false"
		align-center="center"
		class="smPermissionPopdialog"
		:before-close="handleClose"
		style="width: 700px; padding: 0"
	>
		<div class="form_content">
			<div class="dialogHeaderRight">
				<el-icon><CloseBold @click="handleClose" /></el-icon>
			</div>
			<div class="content">
				<div class="c_title">{{ dialogObj.dialogTitle }}</div>
				<div class="c_titles">开通后您将获得以下功能权益</div>
				<div class="c_content">
					<div v-for="(item, index) in dialogObj.dialogList" :key="index">
						<div class="c_contentd">
							<div class="circular"></div>
							<div class="c_text">{{ item }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="contentk"></div>
		<template #footer>
			<div class="dialog_footer">
				<div>
					<el-button v-if="dialogObj.coupon" type="primary" color="#1868F1" @click="handleOpenRights"> 开通当前权益 </el-button>
					<el-button plain @click="handleDetermine"> 前往卡劵市场 </el-button>
				</div>
			</div>
		</template>
	</el-dialog>
	<OpenRights :dialogVisible="dialogVisibleRights" :coupon="dialogObj.coupon" @handleRightsClose="handleRightsClose"></OpenRights>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import OpenRights from '../OpenRights/index.vue';
import { useRouter } from 'vue-router';
import { emitter } from '../../utils/eventBus';
const router = useRouter();
const emit = defineEmits();
const dialogVisibleRights = ref(false);
const dialogObj = reactive({
	dialogVisible: false, //弹框展示状态
	dialogTitle: '', //弹框标题
	dialogList: [], //权益
	coupon: {}, //当前权益对象
});
emitter.on('updateMessage', (data) => {
	dialogObj.dialogVisible = true;
	dialogObj.dialogTitle = data.msg;
	dialogObj.dialogList = data.data.includes;
	dialogObj.coupon = data.data.coupon; //当前权益对象
});

onMounted(() => {});

function handleRightsClose() {
	dialogVisibleRights.value = false;
	handleClose();
}
// 关闭对话框
function handleClose() {
	dialogObj.dialogVisible = false;
}

// 确定
function handleDetermine() {
	router.push({
		path: '/rights',
	});
	handleClose();
}

// 开通权益
function handleOpenRights() {
	dialogVisibleRights.value = true;
}
</script>
<style lang="scss" scoped>
@import url('./css.css');
</style>
