<template>
	<el-dialog
		v-model="props.IssuedDialogVisible"
		:close-on-click-modal="false"
		:show-close="false"
		align-center="center"
		:before-close="handleClose"
		style="width: 560px"
	>
		<template #header>
			<div class="dialogHeader">
				<div class="dialogHeaderLeft">
					<div>开具发票</div>
				</div>

				<div class="dialogHeaderRight">
					<el-icon><CloseBold @click="handleClose" /></el-icon>
				</div>
			</div>
		</template>
		<div class="form_content">
			<div class="content_bottom">
				<img src="../../../assets/Issuedsuccess.png" style="width: 80px; height: 80px" alt="" />
				<div class="content_name">提交成功</div>
				<div class="content_text">您的发票开局申请已提交，我们会尽快处理，请耐心等待 开具完成后会发送至您预留的邮箱，请注意查收~</div>
				<el-button @click="handleRevertClose" style="height: 48px; width: 163px"> 返回订单列表 </el-button>
			</div>
		</div>
	</el-dialog>
</template>

<script setup>
const emit = defineEmits();
const props = defineProps({
	IssuedDialogVisible: {
		type: Boolean,
		default: false,
	},
});

// 关闭对话框
function handleClose() {
	// emit('handleIssuedClose');
	handleRevertClose();
}
// 成功
function handleRevertClose() {
	emit('handleRevertClose');
}
</script>

<style lang="scss" scoped>
.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	color: rgba(29, 33, 41, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	.el-icon {
		cursor: pointer;
	}
}

.form_content {
	padding: 0 16px;
	display: flex;
	flex-direction: column;
	align-items: center;
	.content_bottom {
		height: 407px;
		width: 354px;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 80px;
		.content_name {
			font-size: 22px;
			font-weight: 700;
			line-height: 32px;
			color: #4e5969;
			margin: 8px 0 16px 0;
		}
		.content_text {
			text-align: center;
			font-size: 14px;
			font-weight: 500;
			line-height: 22px;
			color: #4e5969;
			margin-bottom: 60px;
		}
	}
}
</style>
