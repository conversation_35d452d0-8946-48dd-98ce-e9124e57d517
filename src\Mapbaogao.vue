<template>
	<div :id="containerId"></div>
</template>
<script setup>
import { onMounted, onUnmounted } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
const props = defineProps({
	locadata: Array,
	lat: String,
	containerId: {
		type: String,
		default: 'map-container-0',
	},
	zoom: {
		type: Number,
		default: 15,
	},
	coordinates: {
		type: Array,
		default: [],
	},
});

let map = null;

onMounted(() => {
	if (!props.locadata || !Array.isArray(props.locadata) || props.locadata.length === 0) {
		console.error('locadata is empty or invalid');
		return;
	}

	AMapLoader.load({
		key: '364076015b42375ffa7ee8ea61c9ae6b',
		version: '2.0',
		plugins: ['AMap.Scale', 'AMap.Polygon', 'AMap.CircleEditor'],
	})
		.then((AMap) => {
			// let lat = props.lat.split(',')[0];
			// let lng = props.lat.split(',')[1];
			console.log(props.lat.split(','));
			map = new AMap.Map(props.containerId, {
				viewMode: '3D',
				zoom: props.zoom,
				center: props.lat.split(','), // Initialize map center
			});

			if (props.coordinates.length > 0) {
				props.coordinates.forEach((item) => {
					const content = `<div class="marker_buildingName" style="	border-radius: 10%;">
					${item.buildingName}
									</div>`;
					const marker = new AMap.Marker({
						position: item.coordinate.split(',').map(Number),
						label: {
							// 添加名称标签
							content: content, // 使用传入的楼宇名称
							offset: new AMap.Pixel(10, 0), // 标签相对于标记的位置偏移
						},
					});
					map.add(marker);
				});
			} else {
				const polygon = new AMap.Polygon({
					path: props.locadata,
					strokeColor: '#4779b8',
					strokeWeight: 6,
					strokeOpacity: 0.2,
					fillOpacity: 0.4,
					fillColor: '#1791fc',
					zIndex: 50,
				});
				const marker = new AMap.Marker({
					position: props.lat.split(','),
					title: 'Marker',
				});
				map.add(marker);
				map.add(polygon);
			}
		})
		.catch((e) => {
			console.error('Failed to load AMap:', e);
		});
});
onUnmounted(() => {
	map?.destroy();
});
</script>

<style>
[id^='map-container'] {
	width: 100%;
	height: 400px;
}
.amap-marker-label {
	border-radius: 10%;
	background-color: #ffffff59;
	border-radius: 10px;
	color: #00f;
	padding: 7px;
}
</style>
