<template>
	<div :id="containerId" style="height: 180px; width: 100%"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import AMapLoader from '@amap/amap-jsapi-loader';

const containerId = 'mian';
let mapInstance = null; // 将mapInstance声明在onMounted之外
let heatMap = null; // 声明heatmap对象

onMounted(() => {
	try {
		// 加载 AMap JavaScript API
		AMapLoader.load({
			key: 'adffeb71c6cc1f748cc57d3d1c8454c4',
			version: '2.0',
			plugins: ['AMap.HeatMap'],
		}).then((AMap) => {
			// 创建地图
			mapInstance = new AMap.Map(containerId, {
				viewMode: '2D',
				zoom: 15,
				center: [120.369557, 36.094406],
			});

			// 创建热力图层
			heatMap = new AMap.HeatMap(mapInstance, {
				data:
					{
						lng: 116.191031,
						lat: 39.988585,
						count: 10,
					} || heatmapData,
				radius: 100,
				opacity: [0, 0.8],
			});

			// 当路由参数更改时更新热力图数据
			mapInstance.on('complete', function () {
				heatMap.setDataSet({
					data: heatmapData,
					max: 100,
				});
			});
		});
	} catch (error) {
		console.error(error);
	}
});

onUnmounted(() => {
	// 销毁地图和热力图
	if (mapInstance) {
		mapInstance.destroy();
	}
	if (heatMap) {
		heatMap.setDataSet({ data: [] }); // 清空热力图数据
	}
});
</script>

<style lang="less" scoped>
#container {
	height: 180px; /* 根据需要调整高度 */
}
</style>
