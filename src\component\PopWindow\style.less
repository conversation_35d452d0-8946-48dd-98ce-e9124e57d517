.popWindow {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2005;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;

    .container {
        width: 85%;
        height: 85%;
        max-width: 100%;
        max-height: 100%;
        background: #fff;
        position: absolute;

        .ctrl {
            position: absolute;
            right: 0;
            top: 0;
            display: flex;
            flex-direction: row;
            z-index: 2001;

            >div {
                cursor: pointer;
                font-size: 13px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 28px;
                height: 28px;
                margin: 20px 20px;
                z-index: 201;
            }
        }

        .close {
            display: block !important;

            >i {
                font-size: 13px;
                vertical-align: middle;
            }
        }

        >.content {
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 200;
            display: flex;
            flex-direction: column;
            flex: 1;
            justify-content: center;
            align-items: center;
            overflow: hidden;

            .el-loading-mask {
                top: 1px;
                bottom: 1px;
            }
        }
    }
}

.big {
    width: 100% !important;
    height: 100% !important;
    max-width: inherit !important;
    max-height: inherit !important;
}

.el-select-dropdown {
    z-index: 2003;
}

.el-picker-panel {
    z-index: 2004 !important;
}