<template>
	<div class="home_main">
		<div class="left_1">
			<!-- 新闻 -->
			<div class="news_mian">
				<div class="banner" @click="onNewsdetail(data.list4[0].id)" v-if="data.list4.length > 0">
					<img :src="data.list4[0].displayImg ? data.list4[0].displayImg : img" alt="" />
				</div>
				<div class="banner" v-else>
					<img :src="img" alt="" />
				</div>
				<div class="news_title active" v-if="data.list4.length > 0" @click="onNewsdetail(data.list4[0].id)">
					{{ data.list4[0].articleTitle }}
				</div>
				<div class="news_title" v-else>标题不见了</div>
				<div class="zuozhe" v-if="data.list4.length > 0">
					<p style="color: #000">作者：</p>
					{{ data.list4[0].articleAuthor }}
				</div>
				<div class="zuozhe" v-else>
					<p style="color: #000">作者：</p>
					无
				</div>
				<div class="tips" v-if="data.list4.length > 0">{{ data.list4[0].articleType_dictText }}</div>
				<div class="tips" v-else>暂无内容</div>
			</div>
			<!-- 洞察 -->
			<div class="dongcha_main">
				<div class="title_box">
					<div class="title1">
						<div class="title">洞察</div>
						<div class="select_box">
							<el-select v-model="data.value" placeholder="请选择" style="width: 150px" @change="cityChange">
								<el-option v-for="item in data.options" :key="item.areaValue" :label="item.areaKey" :value="item.areaKey"> </el-option>
							</el-select>
						</div>
					</div>
					<div class="more" @click="onViewMore(1)">查看更多</div>
				</div>
				<div class="dongcha_list">
					<div
						@click="onNewsdetail(item.id)"
						class="li active"
						v-show="data.list1.length > 0 && index < 5"
						v-for="(item, index) in data.list1"
						:key="index"
					>
						<div class="textBox">
							<p class="titleText overflowEllpsis">{{ item.articleTitle }}</p>
							<p class="digestText overflowEllpsis">{{ item.summary }}</p>
							<p class="createTime overflowEllpsis">{{ item.createTime }}</p>
						</div>
						<img class="img" :src="item.displayImg" alt="" />
					</div>
					<div class="li overflowEllpsis noData" v-if="data.list1.length <= 0">暂无数据</div>
				</div>
			</div>
		</div>
		<div class="center_1">
			<!-- 解析 -->
			<div class="jiexi_main">
				<div class="title_box">
					<div class="title_list">
						<div class="title title_act">数据</div>
					</div>

					<div class="more" @click="onViewMore(2)">查看更多</div>
				</div>
				<div class="shuju_list">
					<div @click="onNewsdetail(item.id)" class="li active" v-show="data.list2.length > 0" v-for="(item, index) in data.list2" :key="index">
						<div class="textBox">
							<div class="titleText overflowEllpsis">{{ item.articleTitle }}</div>
						</div>
					</div>
					<div class="li noData" v-if="data.list2.length <= 0">暂无数据</div>
				</div>
			</div>

			<div class="jiexi_main">
				<div class="title_box">
					<div class="title_list">
						<div class="title title_act">解析</div>
					</div>

					<div class="more" @click="onViewMore(0)">查看更多</div>
				</div>
				<div class="jiexi_list">
					<div @click="onNewsdetail(item.id)" class="li active" v-show="data.list0.length > 0" v-for="(item, index) in data.list0" :key="index">
						<div class="textBox">
							<div class="titleText overflowEllpsis">{{ item.articleTitle }}</div>
						</div>
					</div>

					<div class="li noData" v-if="data.list0.length <= 0">暂无数据</div>
				</div>
			</div>
		</div>
		<div class="right_box">
			<div class="guanggao_main">
				<ads></ads>
			</div>
		</div>
		<siderBar></siderBar>
	</div>
</template>

<script setup>
import siderBar from '../../component/siderBar/index.vue';
import { reactive, onMounted } from 'vue';
import { ArticleList, ArticleDetail, getArea } from '../../../src/api/home.js';
import ads from './ads.vue';
import img from '@/assets/images/home/<USER>';
import { useRouter } from 'vue-router';
const router = useRouter();

const data = reactive({
	tabIndex: 2, //2：要闻，0：解析
	value: '',
	list0: [],
	tianxieshow: false,
	list1: [],
	list2: [],
	// list3: [],
	list4: [],
});
onMounted(() => {
	window.scrollTo(0, 0);
	getNewsDetail('1');
	getNewsList(0); //解析
	getNewsList(1); //洞察
	getNewsList(2); //数据
	getNewsList(4); //推荐文章
});

//获取新闻列表
const getNewsList = async (articleType) => {
	let params = {
		articleType: articleType,
		pageNo: 1,
		pageSize: articleType === 0 || articleType === 2 ? 6 : 15,
		area: data.value,
	};
	await ArticleList(params)
		.then((res) => {
			console.log('新闻', res);
			if (articleType === 0) {
				data.list0 = res.data.rows;
			}
			if (articleType === 1) {
				data.list1 = res.data.rows;
			}
			if (articleType === 2) {
				data.list2 = res.data.rows;
			}
			if (articleType === 4) {
				data.list4 = res.data.rows;
			}
		})
		.catch((err) => {
			console.log('请求失败！');
		});
};

//获取新闻详情信息
const getNewsDetail = async (id) => {
	let params = {
		articleId: id,
	};
	await ArticleDetail(params)
		.then((res) => {
			console.log('新闻', res);
		})
		.catch((err) => {
			console.log('请求失败');
		});
};

// 跳转到文章详情
const onNewsdetail = (id) => {
	console.log(id, 2323);
	router.push({
		path: '/newsdetail',
		query: {
			id: id,
			area: data.value,
		},
	});
};

const cityChange = (e) => {
	console.log(e, 12341);
	data.value = e;
	getNewsList(1);
};
const getAreaList = async () => {
	await getArea()
		.then((res) => {
			data.options = res.data;
		})
		.catch((err) => {
			console.log('请求失败！');
		});
};
// 查看更多
const onViewMore = (articleType) => {
	router.push({
		path: '/viewMore',
		query: {
			articleType: articleType,
			area: data.value,
		},
	});
};
getAreaList();
</script>

<style scoped lang="less">
.home_main {
	width: 100%;
	max-width: 1440px;
	margin: 0 auto;
	height: 100%;
	min-height: 100vh;
	// background-color: #fff;
	padding-top: 15px;
	box-sizing: border-box;
	display: flex;
	justify-content: space-between;
	margin: 0 auto;
	position: relative;
	::v-deep .el-dialog {
		border-radius: 8px;
		padding: 0 !important;
		overflow: hidden;
		.el-dialog__header {
			padding-bottom: 0 !important;
		}
	}
	.formBox {
		position: fixed;
		width: 300px;
		height: 340px;
		padding: 20px;
		box-sizing: border-box;
		top: 45%;
		right: 300px;
		background-color: #fff;
		box-shadow: 5px 5px 10px 0 rgba(0, 0, 0, 0.1);
	}
	.popAi {
		position: fixed;
		top: 62%;
		right: 16px;
		width: 46px;
		height: 70px;
		padding: 20px 4px 20px 4px;
		border-radius: 40px;
		background: url(../../assets/popaiImg.png) no-repeat;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		border: 1px solid #1868f1;
		box-shadow: 0px 6px 32px 0px #2626261a;

		font-size: 20px;
		font-weight: 700;
		line-height: 24px;
		color: #ffffff;
		box-shadow: 0px 1px 4px 0px #1452e366;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		div {
			width: 21px;
			height: 72px;
		}
	}
	.popAi1 {
		z-index: 99;
		position: fixed;
		top: 60%;
		right: 16px;
		width: 46px;
		height: 130px;
		padding: 20px 4px 20px 4px;
		border-radius: 40px;
		background-color: #fff;
		// background: url(../../assets/popaiImg.png) no-repeat;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		border: 1px solid #1868f1;
		box-shadow: 0px 6px 32px 0px #2626261a;
		font-size: 20px;
		font-weight: 700;
		line-height: 24px;
		color: #000;
		box-shadow: 0px 1px 4px 0px #1452e366;
		align-items: center;
		cursor: pointer;
		.fuchuang {
			width: 46px;
			height: 50px;
			line-height: 15px;
			font-size: 12px;
			color: #000;
			// margin-left: 15px;
			padding: 0 11px;
			box-sizing: border-box;
			margin-top: 13px;
		}
		.fuchuang_icon {
			font-size: 25px;
		}
		.fuchuang:hover {
			color: #1868f1;
			background-color: #f5f6f7;
		}
	}
	.left_1 {
		width: calc((100% - 220px) / 2 - 1%);
		height: auto;
		margin-bottom: 20px;

		.news_mian {
			width: 100%;
			height: auto;
			padding: 15px 15px 30px 15px;
			box-sizing: border-box;
			background-color: rgba(255, 255, 255, 1);

			.banner {
				width: 100%;
				max-height: 420px;
				overflow: hidden;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-bottom: 20px;

				img {
					width: 100%;
					height: auto;
					transition: transform 0.3s ease;
				}

				img:hover {
					cursor: pointer;
					transform: scale(1.1);
				}
			}

			.news_title {
				font-size: 1rem;
				font-weight: bold;
			}

			.zuozhe {
				font-weight: bold;
				font-size: 0.8rem;
				color: rgb(34, 96, 172);
				display: flex;
				align-items: center;
			}

			.tips {
				font-size: 0.8rem;
				// margin: 10px 0;
			}
		}

		.dongcha_main {
			width: 100%;
			height: auto;
			margin-top: 15px;
			padding: 15px 15px 30px 15px;
			box-sizing: border-box;
			background-color: rgba(255, 255, 255, 1);

			.title_box {
				width: 100%;
				height: 52px;
				border-bottom: 1px solid rgba(231, 231, 231, 1);
				display: flex;
				justify-content: space-between;
				align-items: center;

				.title1 {
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.title {
						width: 50px;
						height: 52px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 20px;
						margin-right: 15px;
						font-weight: bold;
						position: relative;

						&::after {
							content: '';
							width: 40px;
							height: 3px;
							background-color: rgba(24, 104, 241, 1);
							position: absolute;
							bottom: 0;
						}
					}

					.select_box {
					}
				}

				.more {
					color: rgba(134, 144, 156, 1);
					cursor: pointer;
					font-size: 12px;
				}
			}

			.dongcha_list {
				width: 100%;
				height: auto;
				margin-top: 20px;

				.li {
					width: 100%;
					display: flex;
					// color: #;
					justify-content: space-between;
					align-items: center;
					// width: 100%;
					// height: 90px;
					// padding: 5px 10px;
					border-bottom: 1px solid rgba(0, 0, 0, 0.1);
					padding: 5px 0;
					box-sizing: border-box;

					.textBox {
						width: calc(100% - 120px);
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.titleText {
							// height: 35px;
							width: 100%;
							font-size: 0.9rem;
							font-weight: bold;
							padding: 0;
							margin: 0;
							margin-top: 10px;
						}

						.digestText {
							// height: 35px;
							font-size: 12px;
							// font-weight: bold;
							padding: 0;
							margin: 0;
							line-height: 20px;
							width: 100%;
							max-width: 400px;
							white-space: pre-wrap;
							margin-top: 5px;
						}

						.createTime {
							// height: 35px;
							font-size: 0.8rem;
							// font-weight: bold;
							padding: 0;
							margin: 0;
							line-height: 20px;
							width: 100%;
							max-width: 400px;
							white-space: pre-wrap;
							margin-top: 5px;
							color: #797979;
						}
					}

					.img {
						width: 120px;
						height: 80px;
					}
				}
			}
		}
	}

	.center_1 {
		width: calc((100% - 220px) / 2 - 1%);
		height: auto;
		margin-bottom: 20px;

		.jiexi_main {
			width: 100%;
			//min-height: 50vh;
			height: auto;
			padding: 11px 15px 0px 15px;
			margin-bottom: 15px;
			box-sizing: border-box;
			background-color: #ffffff;
			position: relative;

			.title_box {
				width: 100%;
				height: 52px;
				border-bottom: 1px solid rgba(231, 231, 231, 1);
				display: flex;
				justify-content: space-between;
				align-items: center;

				.title_list {
					width: 50%;
					height: 52px;
					display: flex;
					justify-content: flex-start;
					align-items: center;

					.title {
						width: 50px;
						height: 52px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 20px;
						margin-right: 15px;
						font-weight: bold;
						position: relative;
						cursor: pointer;
					}

					.title_act {
						width: 50px;
						height: 52px;

						&::after {
							content: '';
							width: 40px;
							height: 3px;
							background-color: rgba(24, 104, 241, 1);
							position: absolute;
							bottom: 0;
						}
					}
				}

				.more {
					cursor: pointer;
					font-size: 12px;
				}
			}

			.jiexi_list {
				width: 100%;
				height: auto;
				// margin-top: 10px;

				.li {
					width: 100%;
					// max-width: 554px;
					display: flex;
					// color: #;
					justify-content: space-between;
					align-items: center;
					// width: 100%;
					// height: 90px;
					// padding: 5px 10px;
					border-bottom: 1px solid rgba(0, 0, 0, 0.1);
					box-sizing: border-box;
					padding: 5px 0;

					.textBox {
						width: 100%;
						// width: calc(100% - 120px);
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.titleText {
							width: 100%;
							height: 48px;
							line-height: 48px;
							font-size: 20px;
							// display: flex;
							// justify-content: flex-start;
							// align-items: center;
							font-weight: bold;
							padding: 5px 0;
							margin: 0;
							// margin-top: 10px;
							&::before {
								content: '';
								width: 5px;
								height: 5px;
								margin-right: 15px;
								background-color: rgba(217, 217, 217, 1);
							}
						}

						.digestText {
							width: 100%;
							// height: 35px;
							font-size: 12px;
							// font-weight: bold;
							padding: 0;
							margin: 0;
							line-height: 20px;
							max-width: 400px;
							white-space: pre-wrap;
							margin-top: 5px;
						}

						.createTime {
							// height: 35px;
							font-size: 0.8rem;
							// font-weight: bold;
							padding: 0;
							margin: 0;
							width: 100%;
							line-height: 20px;
							max-width: 400px;
							white-space: pre-wrap;
							margin-top: 5px;
							color: #797979;
						}
					}

					.img {
						width: 120px;
						height: 80px;
					}
				}
			}
			.jiexi_list > :nth-last-child(1) {
				border-bottom: none;
			}
			.shuju_list {
				width: 100%;
				height: auto;

				.li {
					width: 100%;
					// max-width: 554px;
					display: flex;
					// color: #;
					justify-content: space-between;
					align-items: center;
					// width: 100%;
					// height: 90px;
					// padding: 5px 10px;
					border-bottom: 1px solid rgba(0, 0, 0, 0.1);
					box-sizing: border-box;
					padding: 5px 0;

					.textBox {
						width: 100%;
						// width: calc(100% - 120px);
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.titleText {
							width: 100%;
							height: 48px;
							line-height: 48px;
							// display: flex;
							// justify-content: flex-start;
							// align-items: center;
							font-size: 20px;
							font-weight: bold;
							padding: 5px 0;
							margin: 0;
							// margin-top: 10px;
							&::before {
								content: '';
								width: 5px;
								height: 5px;
								margin-right: 15px;
								background-color: rgba(217, 217, 217, 1);
							}
						}

						.digestText {
							// height: 35px;
							font-size: 12px;
							// font-weight: bold;
							padding: 0;
							margin: 0;
							line-height: 20px;
							width: 100%;
							max-width: 400px;
							white-space: pre-wrap;
							margin-top: 5px;
						}

						.createTime {
							// height: 35px;
							font-size: 0.8rem;
							// font-weight: bold;
							padding: 0;
							margin: 0;
							line-height: 20px;
							width: 100%;
							max-width: 400px;
							white-space: pre-wrap;
							margin-top: 5px;
							color: #797979;
						}
					}

					.img {
						width: 120px;
						height: 80px;
					}
				}
			}

			.shuju_list > :nth-last-child(1) {
				border-bottom: none;
			}
		}
	}

	.right_box {
		width: 220px;
		height: 100%;
		min-height: 100vh;
		// margin: 0 auto;
		box-sizing: border-box;

		.guanggao_main {
			width: 100%;
			margin: 0 auto;
			// height: 115px;
		}
	}
}

@media screen and (max-width: 1024px) {
	.home_main {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		.left_1,
		.center_1 {
			width: 100%;
			min-width: 375px;
			padding: 0 15px;
			box-sizing: border-box;
			.jiexi_list {
				.titleText {
					width: 100%;
				}
			}
			.shuju_list {
				.titleText {
					width: 100%;
				}
			}
		}
		.right_box {
			width: 100%;
			min-width: 375px;
			padding: 0 15px;
			box-sizing: border-box;
		}
	}
}

.dialogTopContent {
	width: calc(100% - 32px);
	height: 56px;
	background: linear-gradient(90deg, #1868f1 0%, #327eff 100%);
	border-bottom: 1px solid #e7e7e7;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16px;
	color: #fff;
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	font-size: 16px;
	font-weight: 700;
	text-align: center;
}
.dialogTopContent > :nth-child(2) {
	font-weight: 600;
	font-size: large;
	cursor: pointer;
}

.popDiv {
	height: 524px;
	::v-deep .AItalk {
		padding: 10px 16px 0px 16px;
		height: calc(100% - 72px);
	}
}

.popDiv > :nth-child(2) {
	height: 100%;
}

@media only screen and (max-width: 600px) {
	.popAi {
		width: 22px !important;
		height: 35px !important;
		right: 0px !important;
		font-size: 12px !important;
		line-height: 18px !important;
		top: 63% !important;
		div {
			height: 50px !important;
			text-align: center;
		}
	}
	.popAi1 {
		width: 22px !important;
		height: 100px !important;
		top: 64% !important;
		right: 0px !important;
		font-size: 12px !important;
		line-height: 18px !important;
		padding-top: 2px !important;
		.fuchuang {
			width: 22px !important;
			height: 40px !important;
			line-height: 15px !important;
			font-size: 12px !important;
			// margin-left: 15px;
			padding: 4px !important;
			margin-top: 10px !important;
		}
		.fuchuang_icon {
			font-size: 12px !important;
		}
	}
	.formBox {
		width: 240px !important;
		right: 240px !important;
	}
}
</style>
