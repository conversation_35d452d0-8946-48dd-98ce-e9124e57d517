<template>
	<div class="jiudian_wrap">
		<template v-if="allData && allData.length > 1">
			<div class="double_box_wrap" v-for="(item, index) in allData" style="margin-bottom: 16px">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">酒店评价</div>
					</div>
					<div class="content_wrap">
						<div class="common_bg">
							<div class="title">
								顾客满意度评分
								<div class="points">{{ item.evaluateScore }}</div>
							</div>
							<div class="progress_box">
								<div class="progress_item">
									<div class="label_">环境和清洁度</div>
									<div class="progress">
										<div
											class="line"
											:style="{
												width:
													(isNaN(item.evaluateEnvironmentCleanliness) ? 0 : Number(((item.evaluateEnvironmentCleanliness / 5) * 100).toFixed(2))) +
													'%',
												backgroundColor: '#378EFF',
											}"
										></div>
									</div>
									<div class="num" style="color: #1868f1">
										{{ isNaN(item.evaluateEnvironmentCleanliness) ? 0 : item.evaluateEnvironmentCleanliness }}
									</div>
								</div>
								<div class="progress_item">
									<div class="label_">客房舒适度</div>
									<div class="progress">
										<div
											class="line"
											:style="{
												width: (isNaN(item.evaluateRoomComfort) ? 0 : Number(((item.evaluateRoomComfort / 5) * 100).toFixed(2))) + '%',
												backgroundColor: '#378EFF',
											}"
										></div>
									</div>
									<div class="num" style="color: #1868f1">
										{{ isNaN(item.evaluateRoomComfort) ? 0 : item.evaluateRoomComfort }}
									</div>
								</div>
								<div class="progress_item">
									<div class="label_">服务</div>
									<div class="progress">
										<div
											class="line"
											:style="{
												width: (isNaN(item.evaluateServer) ? 0 : Number(((item.evaluateServer / 5) * 100).toFixed(2))) + '%',
												backgroundColor: '#378EFF',
											}"
										></div>
									</div>
									<div class="num" style="color: #1868f1">
										{{ isNaN(item.evaluateServer) ? 0 : item.evaluateServer }}
									</div>
								</div>
								<div class="progress_item">
									<div class="label_">性价比</div>
									<div class="progress">
										<div
											class="line"
											:style="{
												width: (isNaN(item.evaluateCostEffectiveness) ? 0 : Number(((item.evaluateCostEffectiveness / 5) * 100).toFixed(2))) + '%',
												backgroundColor: '#378EFF',
											}"
										></div>
									</div>
									<div class="num" style="color: #1868f1">
										{{ isNaN(item.evaluateCostEffectiveness) ? 0 : item.evaluateCostEffectiveness }}
									</div>
								</div>
							</div>
						</div>
						<div class="common_bg">
							<div class="title">消费活跃度</div>
							<div class="content">
								{{ item.consumptionActivity }}
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">内部设施评价</div>
					</div>
					<div class="content_wrap">
						<div class="common_bg">
							<div class="progress_box">
								<div class="progress_item" :key="index">
									<div class="label_" style="width: 28px">卫生</div>
									<div class="progress">
										<div
											class="line"
											:style="{
												width: (isNaN(item.internalEvaluationHealth) ? 0 : Number(((item.internalEvaluationHealth / 5) * 100).toFixed(2))) + '%',
												backgroundColor: '#37B7FF',
											}"
										></div>
									</div>
									<div class="num" style="color: #37b7ff">
										{{ isNaN(item.internalEvaluationHealth) ? 0 : item.internalEvaluationHealth }}
									</div>
								</div>
								<div class="progress_item" :key="index">
									<div class="label_" style="width: 28px">环境</div>
									<div class="progress">
										<div
											class="line"
											:style="{
												width:
													(isNaN(item.internalEvaluationEnvironment) ? 0 : Number(((item.internalEvaluationEnvironment / 5) * 100).toFixed(2))) + '%',
												backgroundColor: '#37B7FF',
											}"
										></div>
									</div>
									<div class="num" style="color: #37b7ff">
										{{ isNaN(item.internalEvaluationEnvironment) ? 0 : item.internalEvaluationEnvironment }}
									</div>
								</div>
								<div class="progress_item" :key="index">
									<div class="label_" style="width: 28px">设施</div>
									<div class="progress">
										<div
											class="line"
											:style="{
												width: (isNaN(item.internalEvaluationFacility) ? 0 : Number(((item.internalEvaluationFacility / 5) * 100).toFixed(2))) + '%',
												backgroundColor: '#37B7FF',
											}"
										></div>
									</div>
									<div class="num" style="color: #37b7ff">
										{{ isNaN(item.internalEvaluationFacility) ? 0 : item.internalEvaluationFacility }}
									</div>
								</div>
								<div class="progress_item" :key="index">
									<div class="label_" style="width: 28px">服务</div>
									<div class="progress">
										<div
											class="line"
											:style="{
												width: (isNaN(item.internalEvaluationServer) ? 0 : Number(((item.internalEvaluationServer / 5) * 100).toFixed(2))) + '%',
												backgroundColor: '#37B7FF',
											}"
										></div>
									</div>
									<div class="num" style="color: #37b7ff">
										{{ isNaN(item.internalEvaluationServer) ? 0 : item.internalEvaluationServer }}
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">配套设施</div>
					</div>
					<div class="content_wrap" style="gap: 2px">
						<div class="service_item">
							<div class="label">前台服务</div>
							<div class="detail_wrap">
								{{ item.supportingFacilitiesFrontDesk }}
							</div>
						</div>
						<div class="service_item">
							<div class="label">餐饮服务</div>
							<div class="detail_wrap">
								{{ item.supportingFacilitiesFood }}
							</div>
						</div>
						<div class="service_item">
							<div class="label">休闲娱乐</div>
							<div class="detail_wrap">
								{{ item.supportingFacilitiesLeisure }}
							</div>
						</div>
						<div class="service_item">
							<div class="label">会议设施</div>
							<div class="detail_wrap">
								{{ item.supportingFacilitiesMeeting }}
							</div>
						</div>
						<div class="service_item">
							<div class="label">公共区域设施</div>
							<div class="detail_wrap">
								{{ item.supportingFacilitiesCommon }}
							</div>
						</div>
						<div class="service_item">
							<div class="label">支持酒店设施</div>
							<div class="detail_wrap">
								{{ item.supportingFacilitiesSupport }}
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">会议租赁</div>
					</div>
					<div class="content_wrap">
						<div class="table_wrap">
							<arco-table
								:columns="columns1"
								:data="item.merchantHotelRentalVoList"
								:pagination="false"
								:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
							>
							</arco-table>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">客房信息</div>
					</div>
					<div class="content_wrap">
						<div class="table_wrap">
							<arco-table
								:columns="columns2"
								:data="item.merchantHotelRoomInfoVoList"
								:pagination="false"
								:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
							>
							</arco-table>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">酒店政策</div>
					</div>
					<div class="content_wrap">
						<div class="common_bg">
							<div class="common_item">
								<div class="label">入离时间</div>
								<div class="desc_wrap">
									<div class="desc">{{ item.policyInOutTime }}</div>
								</div>
							</div>
							<div class="common_item">
								<div class="label">宠物</div>
								<div class="desc_wrap">
									<div class="desc">{{ item.policyPet }}</div>
								</div>
							</div>
							<div class="common_item">
								<div class="label">早餐</div>
								<div class="desc_wrap">
									<div class="desc">{{ item.policyBreakfast }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="empty_wrap" v-if="false">
						<img :src="empty" />
						<div>暂无数据</div>
					</div>
				</div>
			</div>
		</template>
		<template v-else>
			<div class="box_wrap" v-if="!$utils.isEmpty(leftData)">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">酒店评价</div>
						</div>
						<div class="content_wrap">
							<div class="common_bg">
								<div class="title">
									顾客满意度评分
									<div class="points">{{ leftData.evaluateScore }}</div>
								</div>
								<div class="progress_box">
									<div class="progress_item">
										<div class="label_">环境和清洁度</div>
										<div class="progress">
											<div
												class="line"
												:style="{
													width:
														(isNaN(leftData.evaluateEnvironmentCleanliness)
															? 0
															: Number(((leftData.evaluateEnvironmentCleanliness / 5) * 100).toFixed(2))) + '%',
													backgroundColor: '#378EFF',
												}"
											></div>
										</div>
										<div class="num" style="color: #1868f1">
											{{ isNaN(leftData.evaluateEnvironmentCleanliness) ? 0 : leftData.evaluateEnvironmentCleanliness }}
										</div>
									</div>
									<div class="progress_item">
										<div class="label_">客房舒适度</div>
										<div class="progress">
											<div
												class="line"
												:style="{
													width: (isNaN(leftData.evaluateRoomComfort) ? 0 : Number(((leftData.evaluateRoomComfort / 5) * 100).toFixed(2))) + '%',
													backgroundColor: '#378EFF',
												}"
											></div>
										</div>
										<div class="num" style="color: #1868f1">
											{{ isNaN(leftData.evaluateRoomComfort) ? 0 : leftData.evaluateRoomComfort }}
										</div>
									</div>
									<div class="progress_item">
										<div class="label_">服务</div>
										<div class="progress">
											<div
												class="line"
												:style="{
													width: (isNaN(leftData.evaluateServer) ? 0 : Number(((leftData.evaluateServer / 5) * 100).toFixed(2))) + '%',
													backgroundColor: '#378EFF',
												}"
											></div>
										</div>
										<div class="num" style="color: #1868f1">
											{{ isNaN(leftData.evaluateServer) ? 0 : leftData.evaluateServer }}
										</div>
									</div>
									<div class="progress_item">
										<div class="label_">性价比</div>
										<div class="progress">
											<div
												class="line"
												:style="{
													width:
														(isNaN(leftData.evaluateCostEffectiveness) ? 0 : Number(((leftData.evaluateCostEffectiveness / 5) * 100).toFixed(2))) +
														'%',
													backgroundColor: '#378EFF',
												}"
											></div>
										</div>
										<div class="num" style="color: #1868f1">
											{{ isNaN(leftData.evaluateCostEffectiveness) ? 0 : leftData.evaluateCostEffectiveness }}
										</div>
									</div>
								</div>
							</div>
							<div class="common_bg">
								<div class="title">消费活跃度</div>
								<div class="content">
									{{ leftData.consumptionActivity }}
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">会议租赁</div>
						</div>
						<div class="content_wrap">
							<div class="table_wrap">
								<arco-table
									:columns="columns1"
									:data="leftData.merchantHotelRentalVoList"
									:pagination="false"
									:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
								>
								</arco-table>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">酒店政策</div>
						</div>
						<div class="content_wrap">
							<div class="common_bg">
								<div class="common_item">
									<div class="label">入离时间</div>
									<div class="desc_wrap">
										<div class="desc">{{ leftData.policyInOutTime }}</div>
									</div>
								</div>
								<div class="common_item">
									<div class="label">宠物</div>
									<div class="desc_wrap">
										<div class="desc">{{ leftData.policyPet }}</div>
									</div>
								</div>
								<div class="common_item">
									<div class="label">早餐</div>
									<div class="desc_wrap">
										<div class="desc">{{ leftData.policyBreakfast }}</div>
									</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="right">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">内部设施评价</div>
						</div>
						<div class="content_wrap">
							<div class="common_bg">
								<div class="progress_box">
									<div class="progress_item" :key="index">
										<div class="label_" style="width: 28px">卫生</div>
										<div class="progress">
											<div
												class="line"
												:style="{
													width:
														(isNaN(leftData.internalEvaluationHealth) ? 0 : Number(((leftData.internalEvaluationHealth / 5) * 100).toFixed(2))) + '%',
													backgroundColor: '#37B7FF',
												}"
											></div>
										</div>
										<div class="num" style="color: #37b7ff">
											{{ isNaN(leftData.internalEvaluationHealth) ? 0 : leftData.internalEvaluationHealth }}
										</div>
									</div>
									<div class="progress_item" :key="index">
										<div class="label_" style="width: 28px">环境</div>
										<div class="progress">
											<div
												class="line"
												:style="{
													width:
														(isNaN(leftData.internalEvaluationEnvironment)
															? 0
															: Number(((leftData.internalEvaluationEnvironment / 5) * 100).toFixed(2))) + '%',
													backgroundColor: '#37B7FF',
												}"
											></div>
										</div>
										<div class="num" style="color: #37b7ff">
											{{ isNaN(leftData.internalEvaluationEnvironment) ? 0 : leftData.internalEvaluationEnvironment }}
										</div>
									</div>
									<div class="progress_item" :key="index">
										<div class="label_" style="width: 28px">设施</div>
										<div class="progress">
											<div
												class="line"
												:style="{
													width:
														(isNaN(leftData.internalEvaluationFacility) ? 0 : Number(((leftData.internalEvaluationFacility / 5) * 100).toFixed(2))) +
														'%',
													backgroundColor: '#37B7FF',
												}"
											></div>
										</div>
										<div class="num" style="color: #37b7ff">
											{{ isNaN(leftData.internalEvaluationFacility) ? 0 : leftData.internalEvaluationFacility }}
										</div>
									</div>
									<div class="progress_item" :key="index">
										<div class="label_" style="width: 28px">服务</div>
										<div class="progress">
											<div
												class="line"
												:style="{
													width:
														(isNaN(leftData.internalEvaluationServer) ? 0 : Number(((leftData.internalEvaluationServer / 5) * 100).toFixed(2))) + '%',
													backgroundColor: '#37B7FF',
												}"
											></div>
										</div>
										<div class="num" style="color: #37b7ff">
											{{ isNaN(leftData.internalEvaluationServer) ? 0 : leftData.internalEvaluationServer }}
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">配套设施</div>
						</div>
						<div class="content_wrap" style="gap: 2px">
							<div class="service_item">
								<div class="label">前台服务</div>
								<div class="detail_wrap">
									{{ leftData.supportingFacilitiesFrontDesk }}
								</div>
							</div>
							<div class="service_item">
								<div class="label">餐饮服务</div>
								<div class="detail_wrap">
									{{ leftData.supportingFacilitiesFood }}
								</div>
							</div>
							<div class="service_item">
								<div class="label">休闲娱乐</div>
								<div class="detail_wrap">
									{{ leftData.supportingFacilitiesLeisure }}
								</div>
							</div>
							<div class="service_item">
								<div class="label">会议设施</div>
								<div class="detail_wrap">
									{{ leftData.supportingFacilitiesMeeting }}
								</div>
							</div>
							<div class="service_item">
								<div class="label">公共区域设施</div>
								<div class="detail_wrap">
									{{ leftData.supportingFacilitiesCommon }}
								</div>
							</div>
							<div class="service_item">
								<div class="label">支持酒店设施</div>
								<div class="detail_wrap">
									{{ leftData.supportingFacilitiesSupport }}
								</div>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">客房信息</div>
						</div>
						<div class="content_wrap">
							<div class="table_wrap">
								<arco-table
									:columns="columns2"
									:data="leftData.merchantHotelRoomInfoVoList"
									:pagination="false"
									:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
								>
								</arco-table>
							</div>
						</div>
						<div class="empty_wrap" v-if="false">
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import { getBuildJiuDianInfo } from '@/api/syt.js';
import { defineExpose } from 'vue';

const props = defineProps({
	buildData: {
		type: Array,
		default: () => [],
	},
});
watch(
	() => props.buildData,
	(newVal) => {
		if (newVal.length > 0) {
			console.log('props', newVal);
		}
	},
	{
		deep: true,
	}
);

const leftData = ref({});
const rightData = ref({});
const allData = ref([]);

const columns1 = [
	{
		title: '会议厅名称',
		dataIndex: 'conferenceHallName',
	},
	{
		title: '面积(㎡)',
		dataIndex: 'area',
	},
	{
		title: '最多可容纳(人)',
		dataIndex: 'maximumNumberPeople',
	},
	{
		title: '市场参考价',
		dataIndex: 'marketReferencePrice',
	},
];
const columns2 = [
	{
		title: '客房类型',
		dataIndex: 'roomType',
	},
	{
		title: '面积(㎡)',
		dataIndex: 'area',
	},
	{
		title: '数量(个)',
		dataIndex: 'quantity',
	},
	{
		title: '早餐',
		dataIndex: 'breakfast',
	},
	{
		title: '宽带',
		dataIndex: 'broadband',
	},
	{
		title: '参考价格',
		dataIndex: 'referencePrice',
	},
];

async function getData() {
	const res = await getBuildJiuDianInfo({
		buildingIds: props.buildData.map((item) => item.id).join(),
	});
	allData.value = res.data;
	leftData.value = res.data[0];
	console.log('🚀 ~ getData ~ res:', res);
}
defineExpose({
	getData,
});
</script>
<style lang="less" scoped>
.jiudian_wrap {
	flex: 1;
	display: flex;
	gap: 16px;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
		.single_wrap {
			box-sizing: border-box;
			// min-height: 300px;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.content_wrap {
				padding: 20px 16px;
				display: flex;
				flex-direction: column;
				gap: 8px;
				.common_bg {
					background-color: #f7f8fa;
					padding: 16px 20px;
					border-radius: 4px;
					.title {
						display: flex;
						align-items: flex-end;
						gap: 8px;
						color: #1d2129;
						font-size: 14px;
						font-weight: 600;
						line-height: 22px;
						margin-bottom: 12px;
						.points {
							color: #1868f1;
							font-size: 24px;
							font-weight: 600;
							line-height: 24px;
						}
					}
					.content {
						font-size: 14px;
						font-weight: 400;
						color: #4e5969;
						line-height: 22px;
					}
					.progress_box {
						display: flex;
						flex-direction: column;
						gap: 12px;
						.progress_item {
							height: 22px;
							display: flex;
							align-items: center;
							gap: 16px;
							.label_ {
								width: 84px;
								text-align: right;
								font-size: 14px;
								font-weight: 400;
								color: #4e5969;
								line-height: 22px;
							}
							.progress {
								flex: 1;
								border: 1px solid #e5e6eb;
								border-radius: 6px;
								height: 12px;
								overflow: hidden;
								.line {
									height: 100%;
								}
							}
							.num {
								width: 22px;
								font-size: 14px;
								font-weight: 600;
								color: #4e5969;
								line-height: 22px;
								display: flex;
								align-items: center;
								justify-content: center;
							}
						}
					}
					.common_item {
						display: flex;
						flex-direction: column;
						gap: 4px;
						font-size: 14px;
						font-weight: 400;
						color: #4e5969;
						line-height: 22px;
						margin-bottom: 16px;
						.label {
							font-size: 14px;
							font-weight: 600;
							color: #1d2129;
							line-height: 22px;
						}
						.desc_wrap {
							display: flex;
							gap: 27px;
							.desc {
							}
						}
					}
					.common_item:last-child {
						margin-bottom: 0;
					}
				}
				.table_wrap {
					width: 100%;
				}
				.service_item {
					display: flex;
					gap: 2px;
					.label {
						width: 116px;
						display: flex;
						align-items: center;
						justify-content: center;
						background: #e8f3ff;
						color: #1868f1;
						font-size: 14px;
						font-weight: 500;
						line-height: 22px;
						height: 100%;
						border-radius: 4px 0 0 4px;
						padding: 9px 16px;
						box-sizing: border-box;
					}
					.detail_wrap {
						flex: 1;
						display: flex;
						flex-direction: column;
						gap: 2px;
						color: #4e5969;
						font-weight: 400;
						font-size: 14px;
						line-height: 22px;
						padding: 9px 16px;
						background: #f7f8fa;
						border-radius: 0 4px 4px 0;
					}
				}
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
	.double_box_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16px;
		.single_wrap {
			flex: 1;
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.content_wrap {
				padding: 20px 16px;
				display: flex;
				flex-direction: column;
				gap: 8px;
				.common_bg {
					background-color: #f7f8fa;
					padding: 16px 20px;
					border-radius: 4px;
					.title {
						display: flex;
						align-items: flex-end;
						gap: 8px;
						color: #1d2129;
						font-size: 14px;
						font-weight: 600;
						line-height: 22px;
						margin-bottom: 12px;
						.points {
							color: #1868f1;
							font-size: 24px;
							font-weight: 600;
							line-height: 24px;
						}
					}
					.content {
						font-size: 14px;
						font-weight: 400;
						color: #4e5969;
						line-height: 22px;
					}
					.progress_box {
						display: flex;
						flex-direction: column;
						gap: 12px;
						.progress_item {
							height: 22px;
							display: flex;
							align-items: center;
							gap: 16px;
							.label_ {
								width: 84px;
								text-align: right;
								font-size: 14px;
								font-weight: 400;
								color: #4e5969;
								line-height: 22px;
							}
							.progress {
								flex: 1;
								border: 1px solid #e5e6eb;
								border-radius: 6px;
								height: 12px;
								overflow: hidden;
								.line {
									height: 100%;
								}
							}
							.num {
								width: 22px;
								font-size: 14px;
								font-weight: 600;
								color: #4e5969;
								line-height: 22px;
								display: flex;
								align-items: center;
								justify-content: center;
							}
						}
					}
					.common_item {
						display: flex;
						flex-direction: column;
						gap: 4px;
						font-size: 14px;
						font-weight: 400;
						color: #4e5969;
						line-height: 22px;
						margin-bottom: 16px;
						.label {
							font-size: 14px;
							font-weight: 600;
							color: #1d2129;
							line-height: 22px;
						}
						.desc_wrap {
							display: flex;
							gap: 27px;
							.desc {
							}
						}
					}
					.common_item:last-child {
						margin-bottom: 0;
					}
				}
				.table_wrap {
					width: 100%;
				}
				.service_item {
					display: flex;
					gap: 2px;
					.label {
						width: 116px;
						display: flex;
						align-items: center;
						justify-content: center;
						background: #e8f3ff;
						color: #1868f1;
						font-size: 14px;
						font-weight: 500;
						line-height: 22px;
						height: 100%;
						border-radius: 4px 0 0 4px;
						padding: 9px 16px;
						box-sizing: border-box;
					}
					.detail_wrap {
						flex: 1;
						display: flex;
						flex-direction: column;
						gap: 2px;
						color: #4e5969;
						font-weight: 400;
						font-size: 14px;
						line-height: 22px;
						padding: 9px 16px;
						background: #f7f8fa;
						border-radius: 0 4px 4px 0;
					}
				}
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
}
</style>
