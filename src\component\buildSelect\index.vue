<template>
	<div>
		<myModal :width="794" :visible="dialogVisible" @cancel="dialogVisible = false" unmount-on-close mask-closable>
			<template #title>
				<div class="modal_title">{{ title }}</div>
			</template>
			<div class="search_wrap">
				<arco-row :gutter="[24, 12]" style="margin-bottom: 12px; display: flex; justify-content: space-between">
					<arco-col :span="8" class="search_item" v-if="searchTypes.includes('position')">
						<div class="label">城市</div>
						<div class="select">
							<arco-cascader
								allow-clear
								path-mode
								placeholder="请选择城市"
								:options="$vuexStore.state.cityArray"
								:disabled="buildingTypelt === '2' && multipleSelection.length == 1"
								v-model="cityValue"
								@change="handleChange"
								:fieldNames="{ label: 'label', value: 'label', children: 'children' }"
							>
							</arco-cascader>
						</div>
					</arco-col>
					<arco-col :span="8" class="search_item" v-if="searchTypes.includes('rate')">
						<div class="label">资产评级</div>
						<div class="select">
							<arco-select v-model="rateValue" placeholder="全部资产评级" allow-clear>
								<arco-option
									v-for="(item, value) in rate"
									:key="value"
									:label="item.label"
									:value="item.value"
									:style="{ color: item.value == rateValue ? '#1868F1' : '#1D2129' }"
								/>
							</arco-select>
						</div>
					</arco-col>
					<arco-col :span="8" class="search_item" v-if="searchTypes.includes('type')">
						<div class="label">资产类型</div>
						<div class="select">
							<arco-select v-model="buildingType" placeholder="全部资产评级" allow-clear>
								<arco-option
									v-for="(item, value) in buildingTypes"
									:key="value"
									:label="item.label"
									:value="item.value"
									:style="{ color: item.value == buildingType ? '#1868F1' : '#1D2129' }"
								/>
							</arco-select>
						</div>
					</arco-col>
					<arco-col :span="8" class="search_item" v-if="searchTypes.includes('keyword')">
						<div class="label">关键词</div>
						<div class="select">
							<arco-input v-model="keyword" placeholder="请输入关键字" style="background-color: none"></arco-input>
						</div>
					</arco-col>

					<arco-col :span="8" class="search_item" v-if="searchTypes.includes('dealHistory')">
						<div class="label">交易历史</div>
						<div class="select">
							<arco-select v-model="deal" placeholder="不限" allow-clear>
								<arco-option
									v-for="(item, value) in dealHistoryList"
									:key="value"
									:label="item.label"
									:value="item.value"
									:style="{ color: item.value == deal ? '#1868F1' : '#1D2129' }"
								/>
							</arco-select>
						</div>
					</arco-col>

					<arco-col :span="8" style="text-align: right">
						<arco-button type="primary" @click="handleSearch" style="margin-right: 8px">查询</arco-button>
						<arco-button @click="reset">重置</arco-button>
					</arco-col>
				</arco-row>
			</div>
			<div class="tag_wrap" v-if="multipleSelection && multipleSelection.length > 0">
				<arco-tag
					style="color: #1868f1"
					color="#E8F3FF"
					size="large"
					v-for="item in multipleSelection"
					:key="item.id"
					closable
					@close="handleRemove(item)"
				>
					{{ item[props.tagLabelKey] }}
				</arco-tag>
			</div>
			<div class="table_wrap">
				<arco-table
					ref="buidlTableRef"
					row-key="id"
					:columns="tableColumns"
					:data="tableData"
					:row-selection="rowSelection"
					:pagination="pagination"
					:selected-keys="selectedKeys"
					@page-change="pageSizeChange"
					@select="selectChange"
					:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
				/>
			</div>
			<div class="btn_wrap">
				<arco-button @click="cancel" style="margin-right: 8px">取消</arco-button>
				<arco-button type="primary" @click="confirm">确定</arco-button>
			</div>
		</myModal>
	</div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { getBuildingListByMultiCondition, getAllBusinessDistrict, getComparativeValue, getDictList } from '@/api/syt.js';
import { nextTick, ref } from 'vue';
import {
	getCreditRisk, // 获取资产信息
} from '@/api/risks.js';
import myModal from '@/component/arcoComponents/modal/index.vue';

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	title: {
		type: String,
		default: '选择资产',
	},
	maxSelectNum: {
		type: Number,
		default: 10,
	},
	selectData: {
		type: Array,
		default: () => [],
	},
	showCheckedAll: {
		type: Boolean,
		default: true,
	},
	dialogType: {
		type: String,
		default: 'build', // build房源  business 商圈 house户型图
	},
	searchTypes: {
		type: Array,
		default: () => ['position', 'rate', 'type', 'keyword', 'dealHistory'],
	},
	selectedData: {
		type: Array,
		default: () => [],
	},
	tableColumns: {
		type: Array,
		default: () => {
			return [
				{
					title: '资产名称',
					dataIndex: 'buildingName',
					width: '200',
					ellipsis: true,
					tooltip: true,
				},
				{
					title: '所在城市',
					dataIndex: 'city',
          width: '120',
				},
				{
					title: '资产类型',
					dataIndex: 'buildingType',
          width: '100',
				},
				{
					title: '地址',
					dataIndex: 'street',
					// width: '300',
					ellipsis: true,
					tooltip: true,
				},
			];
		},
	},
	tagLabelKey: {
		type: String,
		default: 'buildingName',
	},
	// 资产类型
	buildingTypelt: {
		type: String,
		default: '',
	},
	title: {
		type: String,
		default: '选择资产',
	},
	isTenant: {
		type: Boolean,
		default: false,
	},
});
const emit = defineEmits(['update:modelValue', 'confirm']);
const dialogVisible = ref(false);
const buidlTableRef = ref();
watch(
	() => props.modelValue,
	(newVal) => {
		dialogVisible.value = newVal;
		selectedKeys.value = props.selectedData.map((item) => item.id);
		multipleSelection.value = props.selectedData;
		if (props.buildingTypelt && multipleSelection.value.length === 1) {
			search();
		}
	}
);
watch(dialogVisible, (newVal) => {
	emit('update:modelValue', newVal);
});

const dealHistoryList = ref([
	{ label: '不限', value: 1 },
	{ label: '有', value: 2 },
	{ label: '无', value: 3 },
]);
const deal = ref(1);
const cityValue = ref([]);
const city = ref('');
const district = ref('');
const buildingType = ref('');
const rateValue = ref('');
const keyword = ref('');
const tableData = ref([]);
const multipleSelection = ref([]);
const selectedKeys = ref([]);
const rowSelection = ref({
	type: 'checkbox',
	showCheckedAll: props.showCheckedAll,
	onlyCurrent: false,
});
const pagination = ref({ pageSize: 8, current: 1, total: 0 });

function pageSizeChange(e) {
	pagination.value.current = e;
	search();
}
function selectChange(rowKeys, rowKey, record) {
	if (rowKeys.length <= props.maxSelectNum) {
		if (multipleSelection.value.length == 1 && props.isTenant) {
			if (multipleSelection.value[0].buildingType !== record.buildingType) {
				ElMessage({
					message: `请选择同类型资产进行对比！`,
					type: 'warning',
				});
				return;
			}
		}
		if (rowKeys.includes(rowKey)) {
			multipleSelection.value.push(record);
		}

		multipleSelection.value = multipleSelection.value.filter((item) => rowKeys.includes(item.id));
		selectedKeys.value = rowKeys;
		if (multipleSelection.value.length === 1 && props.buildingTypelt) {
			pagination.value.current = 1;
			search();
		}
		nextTick(() => {
			buidlTableRef.value.$forceUpdate();
		});
	} else {
		// 提示用户
		ElMessage({
			message: `最多只能选择 ${props.maxSelectNum} 条数据！`,
			type: 'warning',
		});
		return;
	}
}
function reset() {
	cityValue.value = [];
	city.value = '';
	district.value = '';
	buildingType.value = '';
	keyword.value = '';
	deal.value = 1;
	rateValue.value = '';
	pagination.value.current = 1;
	search();
}
async function search() {
	let queryParams = {};
	if (props.dialogType == 'build') {
		let param = {};
		if (props.buildingTypelt && multipleSelection.value.length === 1) {
			param = {
				radius: true,
				lng: multipleSelection.value[0].lng,
				lat: multipleSelection.value[0].lat,
			};
		}
		queryParams = {
			...param,
			city: city.value,
			district: district.value,
			buildingType: props.buildingTypelt || buildingType.value,
			keyword: keyword.value,
			deal: deal.value == 1 ? '' : deal.value == 2 ? true : false,
			currentPage: pagination.value.current,
			pageSize: pagination.value.pageSize,
			buildingRate: rateValue.value,
		};
	} else if (props.dialogType == 'business' || props.dialogType == 'building') {
		queryParams = {
			city: city.value,
			district: district.value,
			buildingType: buildingType.value,
			keyword: keyword.value,
			deal: deal.value == 1 ? '' : deal.value == 2 ? true : false,
			current: pagination.value.current,
			size: pagination.value.pageSize,
			buildingRate: rateValue.value,
		};
	} else if (props.dialogType == 'house') {
		queryParams = {
			city: city.value,
			district: district.value,
			buildingType: buildingType.value,
			keyword: keyword.value,
			deal: deal.value == 1 ? '' : deal.value == 2 ? true : false,
			currentPage: pagination.value.current,
			pageSize: pagination.value.pageSize,
			buildingRate: rateValue.value,
			book: true,
		};
	}
	if (props.dialogType == 'build' || props.dialogType == 'house') {
		await getBuildingListByMultiCondition(queryParams)
			.then((res) => {
				tableData.value = res.data.rows;
				pagination.value.total = res.data.total;
			})
			.catch((err) => {
				console.log(err, 'err');
			});
	} else if (props.dialogType == 'business') {
		await getAllBusinessDistrict(queryParams)
			.then((res) => {
				tableData.value = res.data.rows;
				pagination.value.total = res.data.total;
			})
			.catch((err) => {
				console.log(err, 'err');
			});
	} else if (props.dialogType == 'building') {
		await getCreditRisk(queryParams)
			.then((res) => {
				tableData.value = res.data.rows;
				pagination.value.total = res.data.total;
			})
			.catch((err) => {
				console.log(err, 'err');
			});
	}
}
function handleSearch() {
	pagination.value.current = 1;
	search();
}
function handleChange(val) {
	city.value = val[0];
	district.value = val[1];
}
function handleRemove(tag) {
	multipleSelection.value = multipleSelection.value.filter((item) => item.id !== tag.id);
	selectedKeys.value = selectedKeys.value.filter((item) => item !== tag.id);
	if (props.buildingTypelt && multipleSelection.value.length === 0) {
		search();
	}
}
const rate = ref([]);
const buildingTypes = ref([]);
// 获取字典
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			// if (props.buildingTypeFilter) {
			// 	buildingTypes.value = res.data.filter((item) => {
			// 		return item.text == '写字楼' || item.text == '产业园区';
			// 	});
			// } else {
			// 	buildingTypes.value = res.data;
			// }
			buildingTypes.value = res.data;
			console.log('🚀 ~ .then ~ buildingTypes.value:', buildingTypes.value);
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();
search();
function cancel() {
	cityValue.value = [];
	city.value = '';
	district.value = '';
	buildingType.value = '';
	keyword.value = '';
	deal.value = 1;
	rateValue.value = '';
	nextTick(() => {
		// 关闭对话框
		dialogVisible.value = false;
	});
}
function confirm() {
	emit('confirm', multipleSelection.value);
	dialogVisible.value = false;
}
</script>
<style lang="less">
.arco-cascader-option-active {
	color: #1868f1 !important;
}
.arco-select-view-single,
.arco-input-wrapper {
	background: none !important;
	border: 1px solid #e5e6eb !important;
	border-radius: 4px;
}
.arco-select-view-single:hover,
.arco-input-wrapper:hover {
	border: 1px solid #1868f1 !important;
}
</style>
<style lang="less" scoped>
.modal_title {
	text-align: left;
	width: 100%;
	font-size: 20px;
	font-weight: 500;
	color: #1d2129;
}
.search_wrap {
	margin-bottom: 20px;
	.search_item {
		display: flex;
		.label {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			padding-right: 16px;
		}
		.select {
			width: 160px;
		}
	}
}
.tag_wrap {
	margin-bottom: 12px;
	display: flex;
	gap: 12px;
}
.table_wrap {
}
.btn_wrap {
	margin-top: 24px;
	display: flex;
	justify-content: flex-end;
}
</style>
