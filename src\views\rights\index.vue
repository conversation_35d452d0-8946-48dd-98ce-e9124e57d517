<template>
	<div class="rights-container">
		<div class="rights-top" :style="{ backgroundSize: tabPosition == 'setMeal' ? '100% 9.8%!important' : '100% 19.8%!important' }">
			<div class="title">购买套餐权益卡券 解锁商宇通全部VIP功能</div>

			<div class="tab-container">
				<el-radio-group v-model="tabPosition" style="margin-bottom: 76px">
					<el-radio-button value="setMeal">套餐权益</el-radio-button>
					<el-radio-button value="monoblocUnits">单组权益</el-radio-button>
					<!-- <el-radio-button value="newUserVip" v-if="!$vuexStore.state.newUserCoupon">新用户专享</el-radio-button> -->
				</el-radio-group>

				<div class="tab_line">
					<div class="select-city" ref="elDropdownEnd">
						<div class="city_name">选择适用城市</div>
						<div class="name" @click="handleDownBold">{{ '全部' }}</div>
						<el-popover placement="bottom" :visible="visible" :width="424" trigger="click">
							<template #reference>
								<el-icon style="cursor: pointer; padding-left: 16px; font-size: larger" @click="handleDownBold" v-if="visible"
									><ArrowUpBold
								/></el-icon>
								<el-icon style="cursor: pointer; padding-left: 16px; font-size: larger" @click="handleDownBold" v-else><ArrowDownBold /></el-icon>
							</template>
							<div class="city_list">
								<div class="header_list">
									<div class="header_list_name">选择适用城市</div>
									<div class="header_list_btn">
										<div class="all" @click="handleCheckAll">全选</div>
										<div class="all" @click="handleEmpty">清空</div>
										<div class="header_close">
											<el-icon @click="handleSure(false)"><CloseBold /></el-icon>
										</div>
									</div>
								</div>
								<div class="cityListF">
									<div :class="item.checked ? 'active_item cityList_item' : 'cityList_item'" v-for="(item, index) in cityList" :key="index">
										<el-checkbox v-model="item.checked" @change="handleCheck()" />
										<div class="cityList_item_name">{{ item.cityName }}</div>
									</div>
								</div>
								<div class="bottom_btn">
									<div @click="handleSure(true)">确定</div>
								</div>
							</div>
						</el-popover>
					</div>

					<div class="tab_Rright" v-if="tabPosition == 'setMeal'">
						<div class="name">选择团队人数</div>
						<div class="purchase-box">
							<el-input-number min="1" max="20" @change="peopleSelected" v-model="selectedItemActive" :step="1" step-strictly />
						</div>
					</div>
				</div>

				<div class="details-container">
					<div class="set-meal-container" v-if="tabPosition == 'setMeal'">
						<div class="set-meal">
							<div class="set_meal_box" :class="`${item.type}`" v-for="(item, index) in cityListAgg" :key="index" style="margin-bottom: 20px">
								<div class="back-top back_top">{{ item.type == 'PACKAGE_PREMIUM' ? '团队或企业使用一步到位更划算' : '' }}</div>
								<div class="set_container">
									{{ item.name }}
								</div>
								<div class="permission">解锁全部城市</div>
								<div class="set_button" v-if="item.type == 'PACKAGE_BASE'">
									<div class="set_Yuan">￥</div>
									<div class="set_number">{{ item.month }}</div>
								</div>
								<div class="set_button" v-if="item.type !== 'PACKAGE_BASE'">
									<div class="set_Yuan">￥</div>
									<div class="set_number">{{ item.month * selectedItemActive }} / 月</div>
									<el-divider direction="vertical" />
									<div class="month_year">
										<div class="set_Yuan">￥</div>
										<div class="set_number">{{ item.year * selectedItemActive }} / 年</div>
									</div>
								</div>
							</div>
						</div>

						<div class="set-meal" v-for="(items, indexs) in groupedData.PACKAGE" :key="indexs">
							<div :class="`set-meal-box ${item.specs}`" v-for="(item, index) in items" :key="index" style="margin-bottom: 20px">
								<div class="back-top">{{ item.specs == 'PACKAGE_PREMIUM' ? '强烈推荐团队或企业使用' : '' }}</div>
								<div class="set-container">
									<div class="set-title-box">
										<div class="title-box-container">
											<div class="set-title">
												{{ item.name }}
												<div class="set-city">{{ item.cityName }}</div>
											</div>
											<div class="permission">{{ item.description }}</div>
										</div>
									</div>
									<div class="permission-details">
										<div class="contain-rights">{{ `包含${item.specs == 'PACKAGE_BASE' ? '免费' : ''}权益` }}</div>
										<template v-if="item.specs == 'PACKAGE_BASE'">
											<div class="content-name" v-for="(ite, inde) in item.items.slice(0, 2)" :key="inde">{{ ite }}</div>
										</template>
										<template v-else>
											<div class="content-name" v-for="(ite, inde) in item.items" :key="inde">{{ ite }}</div>
										</template>

										<div class="view-rights" @click="contrastJump" v-if="item.specs == 'PACKAGE_BASE'">
											查看权益对比 <img src="@/assets/Union.png" alt="" />
										</div>
										<div class="view-rights" @click="contrastJump" v-else>共{{ item.Rights }}尊享内容 <img src="@/assets/Union.png" alt="" /></div>
									</div>
								</div>
								<div class="set-button" v-if="item.specs == 'PACKAGE_BASE'">
									<div class="buy-button">免费使用</div>
								</div>
								<div class="set-button" v-if="item.specs !== 'PACKAGE_BASE'">
									<div class="month_year">
										<div class="shoppingCart" @click="handleShoppingCart(item, 'MONTH')"></div>
										<div class="buy-button month" ref="shoppingMonth" @click="mealPay('meal', 'month', item, items)">
											{{ item.prices.MONTH * selectedItemActive }} / 月
										</div>
									</div>
									<div class="month_year">
										<div class="shoppingCart shoppingCart1" @click="handleShoppingCart(item, 'YEAR')"></div>
										<div class="buy-button year" ref="shoppingMonth1" @click="mealPay('meal', 'year', item, items)">
											{{ item.prices.YEAR * selectedItemActive }} / 年
										</div>
									</div>
								</div>
							</div>
						</div>
						<div v-if="groupedData.PACKAGE" class="contrast-list" style="width: 1196px" id="section1">
							<div class="contrast-top">
								<div class="contrast-title">套餐权益对比</div>
								<div class="card-roll" @click="viewCard">查看我的权益卡卷 ></div>
							</div>
							<div class="contrast-container">
								<div class="table-header">
									<div></div>
									<div>基础版</div>
									<div>标准版</div>
									<div>臻享版</div>
								</div>
								<!-- {{ transformSpecsListToTabData(groupedData.PACKAGE.BEIJING) }} -->
								<template v-for="(item, index) in tabData" :key="index">
									<div class="table-title">{{ item.title }}</div>
									<div class="table-box" v-for="(ite, inde) in item.data" :key="inde">
										<div class="name">{{ ite.name }}</div>
										<div>
											<img v-if="!ite.basics" src="@/assets/Rectangle.png" alt="" /> <img v-if="ite.basics" src="@/assets/Pigeon.png" alt="" />
										</div>
										<div>
											<img v-if="!ite.standard" src="@/assets/Rectangle.png" alt="" /><img v-if="ite.standard" src="@/assets/Pigeon.png" alt="" />
										</div>
										<div>
											<img v-if="!ite.perfectest" src="@/assets/Rectangle.png" alt="" /><img v-if="ite.perfectest" src="@/assets/Pigeon.png" alt="" />
										</div>
									</div>
								</template>
							</div>
						</div>
					</div>
					<div class="monobloc-units" v-if="tabPosition == 'monoblocUnits'">
						<template v-for="(items, indexs) in groupedData.SINGLE" :key="indexs">
							<div :class="`monobloc-units-box monobloc-type-${item.type}`" v-for="(item, index) in items" :key="index">
								<div class="back-top"></div>
								<div class="set-container">
									<div class="set-title-box">
										<div class="title-box-container">
											<div class="set-title">
												{{ item.name }}
												<div class="set-city">{{ item.cityName }}</div>
											</div>
											<div class="permission">单组权益</div>
										</div>
									</div>
									<div class="permission-details">
										<div class="contain-rights">包含{{ item.itemsLength }}项权益</div>
										<div style="display: flex; flex-wrap: wrap">
											<div class="content-name" v-for="(ite, inde) in item.items" :key="inde">
												{{ ite }}
											</div>
										</div>
										<!-- <template v-if="item.name == '信用风险'">
											<div class="contain-rights">订阅后包含以下三种服务订阅权限</div>
											<div style="display: flex; flex-wrap: wrap">
												<div class="content-name" v-for="(ite, inde) in ['现金流', '证券化', 'Pre-Reits基金']" :key="inde">{{ ite }}</div>
											</div>
										</template> -->
									</div>
								</div>
								<div class="set-button set_button1">
									<div class="shoppingCart" @click="handleShoppingCart(item, 'MONTH', '1')"></div>
									<div class="buy-button" @click="monoblocPay('monobloc', item)">{{ item.price }} / 月</div>
								</div>
							</div>
						</template>
					</div>

					<div class="user_vip" v-if="tabPosition == 'newUserVip'">
						<template v-for="(item, index) in vipData" :key="index">
							<div class="user_vip_box" @click="handleVip(item)" :style="{ opacity: city !== '' ? '1' : '0.4' }">
								<div class="back-top">新用户专享券</div>
								<div class="set-container">
									<div class="set-title">{{ item.description }}</div>
									<div class="set-city">{{ item.cityName.replace('市', '') }} · {{ item.team }}人 · 一个月</div>
									<div class="permission-details">
										<div class="contain-rights">
											包含<span> {{ item.itemsLength }} </span>{{ ' ' }} 项权益
										</div>
										<div class="content-box">
											<div class="content-name" v-for="(ite, inde) in item.items" :key="inde">
												<div class="content-icon"><img src="@/assets/rectangleIcon.png" alt="" /></div>
												{{ ite }}
											</div>
										</div>
									</div>
								</div>
							</div>
						</template>

						<div class="user_vip_footer">
							<div class="btn" @click="handleClickClaim(city)">{{ city !== '' ? '一键领取' : '请先选择所需城市' }}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<el-dialog v-model="dialogVisible" :title="dialogTitle" :close-on-click-modal="false" :before-close="handleClose">
			<div v-if="successType" class="purchase-container">
				<div class="purchase-box">
					<div class="name">适用城市</div>
					<div class="value">
						{{ selectedData.cityName }}
					</div>
				</div>
				<template v-if="dialogType == 'meal'">
					<div class="purchase-box">
						<div class="name">选择团队人数</div>
						<div class="people-box">
							<el-input-number @change="peopleSelected" min="1" max="20" v-model="selectedItemActive" :step="1" step-strictly />
						</div>
					</div>
					<div class="category-container">
						<div
							:class="`monobloc-units-box ${item.selected} type_${item.specs}`"
							v-for="(item, index) in categoryData"
							:key="index"
							@click="categorySelected(item)"
						>
							<div class="back-top">
								<div class="selected-true" v-if="item.selected">
									<img :src="`${item.specs == 'PACKAGE_STANDARD' ? Frame1 : Frame2}`" alt="" />已选择
								</div>
							</div>
							<div class="set-container">
								<div class="set-title-box">
									<div class="title-box-container">
										<div class="set-title">{{ item.name }}·1{{ item.units }}</div>
										<div class="permission">{{ item.Rights }}</div>
									</div>
									<div class="set-city">{{ item.cityName }}</div>
								</div>
								<div class="money">
									<div class="num">
										<span class="mo">￥</span>{{ item.money }}<span class="units"> / {{ item.units }}</span>
									</div>
									<div class="prompt" v-if="item.units == '年'">每月仅需<span class="mo">￥</span>{{ item.prices.MONTH }}</div>
								</div>
							</div>
							<div class="recommend" v-if="item.specs == 'PACKAGE_PREMIUM' && item.units == '月'">推荐</div>
						</div>
					</div>
				</template>
				<template v-if="dialogType == 'monobloc'">
					<div class="single-box">
						<div class="left-back">
							<div class="single-tltle">单项权益</div>
							<div class="city">{{ selectedData.cityName }}</div>
						</div>
						<div class="single-details">
							<div class="name-box">
								<div class="name">{{ selectedData.description }}</div>
								<div class="equity">{{ selectedData.itemsLength }}项权益</div>
							</div>
							<div class="money">
								<span class="mo">￥</span><span class="num">{{ selectedData.price }}</span> / 月
							</div>
						</div>
					</div>
				</template>
				<div class="count-add" style="border-bottom: 1px solid rgba(231, 231, 231, 1)">
					<div class="purchase-box">
						<div class="name">购买数量</div>
						<div class="value"><el-input-number @change="purchaseChange" min="1" max="99" v-model="purchaseNum" :step="1" step-strictly /></div>
					</div>
					<div class="burning">
						<div class="burning-time" v-if="dialogType == 'meal'">
							购买时长：<span>{{ selectedData.units == '年' ? purchaseNum * 12 : purchaseNum * 1 }}个月次</span>
						</div>
						<div class="burning-time" v-if="dialogType == 'monobloc'">
							购买时长：<span>{{ purchaseNum * 1 }}个月次</span>
						</div>
						<div class="prompt">如果您已有开通权益，使用后则相应延长各项权益的有效期限</div>
					</div>
				</div>
				<div class="tcp-box">
					<el-checkbox-group v-model="activities" @change="activitieschange">
						<el-checkbox value="Agree" name="type" style="font-size: 14px; font-weight: 700; line-height: 22px"
							>我已阅读并同意<span
								class="tcp"
								@click="showShangYutong = true"
								style="font-size: 14px; font-weight: 700; line-height: 22px; color: rgba(24, 104, 241, 1)"
								>《商宇通权益订阅服务协议 》</span
							>
						</el-checkbox>
					</el-checkbox-group>
					<!-- <el-button v-if="!activities.length > 0" style="background: rgba(24, 104, 241, 1); color: rgba(255, 255, 255, 1)" @click="Agree"
						>我已阅读并同意左侧协议</el-button
					> -->
				</div>
				<div class="qr-code-box">
					<el-skeleton-item variant="image" v-if="!paymentStatus || activities.length === 0" style="width: 148px; height: 148px; margin: 0 26px" />
					<iframe
						:src="paymentURL"
						v-show="paymentStatus === 'ALI_PC' && activities.length > 0"
						frameborder="no"
						border="0"
						marginwidth="0"
						marginheight="0"
						scrolling="no"
						width="200"
						height="200"
						style="overflow: hidden; transform: scale(0.6); transform-origin: 100px 50px; /* 确保从左上角开始缩放 */"
					>
					</iframe>
					<canvas v-show="paymentStatus === 'WX_NATIVE' && activities.length > 0" ref="qrcodeCanvas" class="qrcode"></canvas>
					<!-- <div v-show="paymentStatus === 'WX_NATIVE'" ref="qrCode"></div> -->
					<div class="payment-box">
						<div class="payment">
							扫码支付<span class="num"><span class="mo">￥</span>{{ priceAll }}</span>
						</div>
						<div class="manner">
							<el-radio-group v-model="paymentStatus" @change="handlePayment">
								<el-radio value="ALI_PC" size="large">支付宝</el-radio>
								<el-radio value="WX_NATIVE" size="large">微信</el-radio>
							</el-radio-group>
							<!-- 支付宝 -->
						</div>
					</div>
				</div>
			</div>

			<div v-if="!successType" class="prosperity">
				<div class="prosperity-box">
					<img src="@/assets/prosperity.png" alt="" />
					<div class="title">购买成功</div>
					<div class="prompt-content">
						<div>
							您已成功购买“{{ selectedData.cityName }}-【{{ selectedData.name }}】-{{ selectedItemActive || 1 }}人”{{
								dialogType == 'meal' ? '套餐权益卡券' : '单组权益卡券'
							}}
						</div>
						<div>
							总购买时长为{{
								dialogType == 'meal' ? (selectedData.units == '年' ? purchaseNum * 12 : purchaseNum * 1) : purchaseNum * 1
							}}个月，使用后根据购买时长相应延长各项权益的有效期限
						</div>
						<div>您可以在“个人中心-权益中心-我的权益卡券”查看及使用</div>
					</div>

					<el-button type="primary" @click="RightsCentre">去权益中心使用</el-button>
				</div>

				<div class="prosperityCoucher" v-if="couponDetail.couponsType">
					<div class="titleDetails">购买卡券得优惠好礼（已发放至个人中心-权益中心-福利卡券）</div>
					<sm-coucher :itemCoupons="couponDetail" @handleWelfareAddCrad="handleWelfareAddCrad"></sm-coucher>
				</div>
			</div>
		</el-dialog>

		<div class="receiveSuccess" v-if="receiveSuccess">
			<div class="box_receive">
				<div class="content">
					<img src="../../assets/cardCoupons.png" alt="" />
					<div class="content1">领取成功</div>
					<div class="contentTwo">
						<div>您已成功领取“【青岛】新用户专享权益体验券”</div>
						<div>其中每张券的体验时长均为1个月，使用后根据购买时长相应延长各项权益的有效期限</div>
						<div>您可以在“个人中心-权益中心-我的权益卡券”查看及使用</div>
					</div>
				</div>

				<div class="box_Btn" @click="handleCenterUse">去权益中心使用（{{ time }}）</div>
			</div>
		</div>
	</div>

	<el-dialog v-model="showShangYutong" fullscreen>
		<shangYutong @handleReturn="handleReturn"></shangYutong>
	</el-dialog>
	<buySuccess ref="buySuccessRef" />
</template>

<script setup>
import buySuccess from '../../component/buySuccess/index.vue';
import shangYutong from '../equityServices/shangYutong.vue';
import smCoucher from '../../component/smCoucher/index.vue';
import { ref, onMounted, reactive, watch } from 'vue';
import { vuexStore } from '@/store';
import QRCode from 'qrcode';
import { ElMessage } from 'element-plus';
import Frame1 from '@/assets/Frame1.png';
import Frame2 from '@/assets/Frame2.png';
import { getCouponList, getCouponListByCity, orderOreate, orderStatus, addShoppingCart, getDiscount, receiveCoupon } from '@/api/rights';
import { getCommonCity, getCouponDetail } from '@/api/equityTerm.js';
import { useRoute, useRouter } from 'vue-router';
import { Check } from '@element-plus/icons-vue';
import CityData from '../shangYutong/materials_new/components/cityData.vue';
// 创建订单后支付状态
const createSuccessType = ref(true);
//购买成功弹出
const buySuccessRef = ref(null);
const route = useRoute();
const receiveSuccess = ref(false); // 领取成功弹出
const showShangYutong = ref(false);
const cityCheckAll = ref(false);
const time = ref(5); // 验证码秒数
const times = ref(null); // 定时器
const router = useRouter();
const tabPosition = ref('setMeal');
const qrcodeCanvas = ref();
const selectedItemActive = ref(1); // 人数
const priceAll = ref(0); //折扣后的商品总价
const priceTotal = ref(0); // 商品总价
const discounts = ref(0); // 折扣
const dialogVisible = ref(false);
const cityListAgg = ref([]);
const couponDetail = ref({
	// couponsType: '1',
	// name: '146元打车券礼包',
	// desc: '包含1张￥11券、1张￥12券、1张￥13券、2张￥10券、2张￥10券',
	// useLimit: '无门槛',
}); //优惠卷详情
const cityString = ref('');
const activities = ref([]);
const paymentStatus = ref('');
const dialogTitle = ref('购买套餐权益卡卷');
const dialogType = ref('meal');
const elDropdownEnd = ref();
const paymentURL = ref('');
const vipData = ref([]);
let timerId = ref(null); // 订单轮询定时器
const tabData = ref([
	{
		title: '市场统计',
		data: [
			{ name: '资产地图', basics: true, standard: true, perfectest: true },
			{ name: '金融市场', basics: true, standard: true, perfectest: true },
			{ name: '标准化产品', basics: true, standard: true, perfectest: true },
		],
	},
	{
		title: '交易材料',
		data: [
			{ name: '交易计算', basics: false, standard: true, perfectest: true },
			{ name: '楼宇信息', basics: false, standard: true, perfectest: true },
			{ name: '参与者', basics: false, standard: true, perfectest: true },
			{ name: '户型图', basics: false, standard: true, perfectest: true },
		],
	},
	{
		title: '信用风险',
		data: [
			{ name: '信用风险', basics: false, standard: false, perfectest: true },
			{ name: '现金流', basics: false, standard: false, perfectest: true },
			{ name: '证券化', basics: false, standard: false, perfectest: true },
			{ name: 'Pre-Reits基金', basics: false, standard: false, perfectest: true },
		],
	},
]);
const groupedData = ref({
	PACKAGE: {},
	SINGLE: {},
});
const successType = ref(true);
const categoryData = ref([
	{ name: '标准版', type: '1', Rights: '14项权益', city: '北京', money: '22296', units: '年', prompt: '800', selected: true },
	{ name: '臻享版', type: '2', Rights: '14项权益', city: '北京', money: '22296', units: '年', prompt: '800', selected: false },
	{ name: '标准版', type: '1', Rights: '14项权益', city: '北京', money: '800', units: '月', prompt: '800', selected: false },
	{ name: '臻享版', type: '2', Rights: '14项权益', city: '北京', money: '800', units: '月', prompt: '800', selected: false },
]);

const visible = ref(false);

const orderId = ref(''); // 订单ID
const cityList = ref([]);
const city = ref('');
const selectedData = ref({
	name: '标准版·1年',
	type: '1',
	Rights: '14项权益',
	city: '北京',
	money: '22296',
	units: '年',
	prompt: '800',
	selected: true,
});

const purchaseNum = ref(1);

onMounted(() => {
	if (route.query.type) {
		router.replace({ path: '/rights', query: {} });
		tabPosition.value = route.query.type;
	}
	window.scrollTo(0, 0);
	handleCity();
	getData();
});
function handleDownBold() {
	visible.value = !visible.value;
}

function handleCheck() {
	// 数组如果全部选中,cityCheckAll为true
	cityCheckAll.value = cityList.value.every((item) => item.checked);
	console.log(cityCheckAll.value);
}
// 确定
function handleSure(type) {
	city.value = '';
	cityString.value = '';
	let cityNameArr = [];
	let cityItem = [];
	if (!type) {
		visible.value = false;
		return;
	}
	// 获取选中的城市,并逗号拼接
	cityList.value.forEach((item) => {
		if (item.checked) {
			cityNameArr.push(item.cityName);
			cityItem.push(item.city);
		}
	});
	// cityItem数组变成字符串并逗号分隔

	city.value = cityNameArr.join(',') === '' ? '' : cityNameArr.join(',');
	cityString.value = cityItem.join(',') === '' ? '' : cityItem.join(',');
	if (cityCheckAll.value) {
		cityString.value = '';
	}
	getData();
	visible.value = false;
}
// 全选
function handleCheckAll() {
	cityList.value.forEach((item) => {
		item.checked = true;
	});
	cityCheckAll.value = true;
}
// 清空
function handleEmpty() {
	cityCheckAll.value = false;
	cityList.value.forEach((item) => {
		item.checked = false;
	});
}

// 跳转权益中心
function handleCenterUse() {
	clearInterval(times.value);
	router.push({
		path: '/profile/browsingHistory',
	});
}
// 领取新用户专享权益体验券
function handleNewUserVip() {
	receiveCoupon({ couponId: vipData.value[0].id }).then((res) => {
		if (res.code === 200) {
			ElMessage({
				type: 'success',
				message: '领取成功',
			});
			vuexStore.commit('handleNewUserCoupon', true);
			handleCenterVip();
		}
	});
}

// 跳转权益体验券
function handleCenterVip() {
	receiveSuccess.value = true;
	times.value = setInterval(() => {
		time.value--;
		if (time.value == 0) {
			// 跳转页面
			router.push({
				path: '/profile/browsingHistory',
			});
			clearInterval(times.value);
			time.value = null;
		}
	}, 1000);
}
// 一键领取
function handleClickClaim(type) {
	if (vipData.value.length == 0) {
		ElMessage({
			type: 'warning',
			message: '暂无可领取的权益体验券',
		});
		return;
	}
	if (type !== '') {
		// 领取新用户专享权益体验券
		handleNewUserVip();
	} else {
		//页面上滑定位至【选择适用城市】位置
		window.scrollTo({ top: elDropdownEnd.value.offsetTop, behavior: 'smooth' });
	}
}

// 获取购物车
function handlegetShoppingCart() {
	vuexStore.dispatch('handleGetShoppingCart'); // 获取购物车
}
/**
 * @function  handleShoppingCart // 加入购物车
 * @param params // 套餐
 * @param type // 套餐类型
 * @param typeName // 单组权益
 */
function handleShoppingCart(params, type, typeName) {
	let param = {
		orderCount: '1', // 订单数量
		businessType: 'COUPON_ORDER', // 订单类型
		couponId: typeName ? params.id : params.ides[type], // 套餐id
		quantity: '1', // 套餐数量
		team: params.team,
		totalAmount: typeName ? params.price : params.prices[type],
	};
	addShoppingCart({ ...param }).then((res) => {
		if (res.code === 200) {
			handlegetShoppingCart(); //获取购物车列表
			ElMessage({
				message: `加入购物车成功`,
				type: 'success',
			});
		}
	});
}
/**
 * @function handleCity 获取城市
 */
function handleCity() {
	getCommonCity().then((res) => {
		if (res?.code === 200) {
			res.data.forEach((element) => {
				element.checked = false;
			});
			cityList.value = res.data;
		}
	});
}
const getData = async () => {
	groupedData.value = { PACKAGE: {}, SINGLE: {} };
	vipData.value = [];
	await getCouponListByCity({ city: cityString.value, team: '1' })
		.then((res) => {
			if (res.code == 200) {
				res.data.agg.forEach((element) => {
					if (element.name == '基础版') {
						element.type = 'PACKAGE_BASE';
					}

					if (element.name == '标准版') {
						element.type = 'PACKAGE_STANDARD';
					}

					if (element.name == '臻享版') {
						element.type = 'PACKAGE_PREMIUM';
					}
				});
				cityListAgg.value = res.data.agg;
				let data = res.data.coupons;
				// 对数据进行分组，分为套餐(PACKAGE)和单组(SINGLE)
				data.forEach((item) => {
					if (item.type === 'PACKAGE') {
						if (!groupedData.value.PACKAGE[item.city]) {
							groupedData.value.PACKAGE[item.city] = [];
						}
						groupedData.value.PACKAGE[item.city].push(item);
					} else if (item.type === 'SINGLE') {
						if (!groupedData.value.SINGLE[item.city]) {
							groupedData.value.SINGLE[item.city] = [];
						}
						groupedData.value.SINGLE[item.city].push(item);
					} else if (item.type === 'NEW_USER') {
						vipData.value.push(item);
					}
				});
				Object.keys(groupedData.value.PACKAGE).forEach((city) => {
					groupedData.value.PACKAGE[city] = processCityPackage(groupedData.value.PACKAGE[city]);
				});
			}
		})
		.catch((err) => {
			console.log(err);
		});
};
// 创建一个通用的处理函数，用于去重和排序
const processCityPackage = (cityPackages) => {
	return cityPackages
		.reduce((accumulator, current) => {
			let existingObject = accumulator.find((obj) => obj.name === current.name);

			if (!existingObject) {
				// 如果没有找到具有相同名称的对象，则创建一个新对象
				existingObject = {
					...current, // 复制当前对象的所有属性
					prices: {}, // 初始化prices对象来存储不同周期的价格
					ides: [], // 初始化数组来存储不同周期的权益id
				};
				// 从新对象中删除price属性，因为价格将存储在prices对象中
				delete existingObject.price;
				delete existingObject.id;
				accumulator.push(existingObject);
			}

			// 在prices对象中以period为键存储价格，并保留其他属性
			existingObject.prices[current.period] = current.price;
			existingObject.ides[current.period] = current.id;

			// 如果当前对象的其它属性有变化，可以在这里更新
			// 例如：existingObject.someOtherProperty = current.someOtherProperty;

			return accumulator;
		}, [])
		.sort((a, b) => {
			const priorityMap = {
				基础版: 1,
				标准版: 2,
				臻享版: 3,
			};
			const getPriority = (name) => {
				return priorityMap[Object.keys(priorityMap).find((key) => name.includes(key))] || Infinity;
			};
			const aPriority = getPriority(a.name);
			const bPriority = getPriority(b.name);
			return aPriority - bPriority;
		});
};
const monoblocPay = (Type, row) => {
	console.log(row);
	purchaseNum.value = 1;
	activities.value = [];
	selectedData.value = row;
	dialogType.value = Type;
	dialogVisible.value = true;
	successType.value = true;
	dialogTitle.value = '购买单组权益卡卷';
};
const mealPay = (Type, date, row, items) => {
	purchaseNum.value = 1;
	activities.value = [];
	items.map((item) => {
		if (item.name == '标准版') {
			categoryData.value[0] = { ...categoryData.value[0], money: item.prices.YEAR, Rights: item.itemsLength + '项权益', ...item };
			categoryData.value[2] = { ...categoryData.value[2], money: item.prices.MONTH, Rights: item.itemsLength + '项权益', ...item };
		} else if (item.name == '臻享版') {
			categoryData.value[1] = { ...categoryData.value[1], money: item.prices.YEAR, Rights: item.itemsLength + '项权益', ...item };
			categoryData.value[3] = { ...categoryData.value[3], money: item.prices.MONTH, Rights: item.itemsLength + '项权益', ...item };
		}
	});
	row.name;
	dialogType.value = Type;
	dialogVisible.value = true;
	successType.value = true;
	dialogTitle.value = '购买套餐权益卡卷';
	categoryData.value.forEach((item) => {
		item.selected = false;
	});
	if (date == 'month') {
		if (row.name == '臻享版') {
			categoryData.value[3].selected = true;
		} else if (row.name == '标准版') {
			categoryData.value[2].selected = true;
		}
		console.log(row);
	} else if (date == 'year') {
		if (row.name == '臻享版') {
			categoryData.value[1].selected = true;
		} else if (row.name == '标准版') {
			categoryData.value[0].selected = true;
		}

		console.log(row);
	}
	categoryData.value.forEach((item) => {
		if (item.selected == true) {
			selectedData.value = item;
		}
	});
};

const peopleSelected = (selectedItem) => {
	selectedItemActive.value = selectedItem;
	if (activities.value.length > 0) {
		handlePriceChangeAll(); // 支付明细
	}
};
const categorySelected = (selectedItem) => {
	clearInterval(timerId.value);
	priceAll.value = 0; // 应付金额
	paymentStatus.value = '';
	// 遍历peopleData数组，将每个对象的selected属性设置为false
	categoryData.value.forEach((item) => {
		item.selected = false;
	});
	// 将被选中的对象的selected属性设置为true
	selectedItem.selected = true;
	selectedData.value = selectedItem;
	activities.value = [];
};
// 权益中心
const RightsCentre = () => {
	router.push({
		path: '/profile/browsingHistory',
	});
};
// 跳转福利卡劵
function handleWelfareAddCrad() {
	router.push({
		path: '/profile/browsingHistory',
		query: { type: 'third' },
	});
}
const viewCard = () => {
	router.push({
		path: '/profile/browsingHistory',
	});
};
const handleClose = () => {
	console.log(selectedData.value, 'console.log(selectedData.value);');
	if (!createSuccessType.value) {
		buySuccessRef.value.show({
			paymentState: true,
			name: selectedData.value.description,
			payType: paymentStatus.value,
			payableAmount: Number(priceTotal.value.toFixed(2)),
		});
		createSuccessType.value = true;
	}
	// 关闭对话框
	dialogVisible.value = false;
	priceAll.value = 0;
	clearInterval(timerId.value);
};
// 同意左侧
// 	successType.value = false;
// dialogVisible.value = false;
// document.documentElement.scrollTop = 0;
// document.body.scrollTop = 0;

function handlePayment() {
	//清楚定时器
	clearInterval(timerId.value);
	handlePriceChangeAll(); // 支付明细
}

const Agree = () => {
	console.log(selectedData.value);
	let params = {
		payType: paymentStatus.value,
		orderCount: '1',
		totalAmount: Number(priceTotal.value.toFixed(2)),
		discountAmount: Number(discounts.value),
		payableAmount: Number(priceAll.value),
		orderDetails: [
			{
				orderCount: 1,
				businessType: 'COUPON_ORDER',
				couponId:
					dialogType.value == 'monobloc'
						? selectedData.value.id
						: selectedData.value.units == '月'
						? selectedData.value.ides.MONTH
						: selectedData.value.ides.YEAR,
				quantity: purchaseNum.value,
				team: selectedItemActive.value || 1,
				outTradeNo: '',
				totalAmount:
					dialogType.value == 'monobloc'
						? selectedData.value.price * purchaseNum.value
						: selectedData.value.money * purchaseNum.value * selectedItemActive.value,
			},
		],
	};

	orderOreate(params)
		.then((res) => {
			if (res.code == 200) {
				createSuccessType.value = false;
				let data = res.data;
				if (paymentStatus.value === 'ALI_PC') {
					paymentURL.value = data.url;
				} else {
					const qrCodeDiv = qrcodeCanvas.value;
					QRCode.toCanvas(qrCodeDiv, data.url, (error) => {
						if (error) console.error(error);
					});
				}

				if (data.outTradeNo) {
					orderId.value = data.outTradeNo;
					// 开始轮询
					timerId.value = setInterval(() => {
						pollOrderStatus(orderId.value);
					}, 1500);
				}
			}
		})
		.catch((err) => {
			console.log(err);
		});
};
const contrastJump = () => {
	document.documentElement.scrollTop = document.getElementById('section1').offsetTop - 200;
	document.body.scrollTop = document.getElementById('section1').offsetTop - 200;
};
const purchaseChange = (e) => {
	if (activities.value.length > 0) {
		handlePriceChangeAll(); // 支付明细
	}
};

// 支付明细
function handlePriceChangeAll() {
	priceTotal.value = 0;
	let yearOrMonthPrice = 0;
	if (selectedData.value.units == '年') {
		yearOrMonthPrice = selectedData.value.prices.YEAR;
	} else if (selectedData.value.units == '月') {
		yearOrMonthPrice = selectedData.value.prices.MONTH;
	} else {
		yearOrMonthPrice = selectedData.value.price;
	}
	priceTotal.value =
		dialogType.value == 'monobloc' ? yearOrMonthPrice * purchaseNum.value : yearOrMonthPrice * purchaseNum.value * selectedItemActive.value;
	getDiscount({ totalAmount: Number(priceTotal.value.toFixed(2)) }).then((res) => {
		if (res.code === 200) {
			discounts.value = Number(res.data.discountAmount.toFixed(2)); // 折扣
			priceAll.value = Number(res.data.payableAmount.toFixed(2)); // 应付金额
			if (paymentStatus.value === '' || activities.value.length !== 1) {
				return;
			}
			Agree();
		}
	});
}

//根据订单id获取优惠卷详情
const handleCouponDetail = (orderId) => {
	getCouponDetail({ outTradeNo: orderId }).then((res) => {
		if (res.code === 200 && res.data) {
			couponDetail.value = res.data;
			couponDetail.value.couponsType = '1';
		}
	});
};

const pollOrderStatus = async (orderId) => {
	try {
		const response = await orderStatus(orderId);
		if (orderId === '') {
			return; // 提前返回，不执行后续代码
		}
		//PENDING("待支付"),      // 待支付
		// PAID("已支付"),         // 已支付
		// FAILED("支付失败"),       // 支付失败
		// CANCELLED("已取消"),    // 已取消
		// REFUNDED("已退款");      // 已退款
		if (response.code == 200) {
			// 根据返回的状态更新状态提示信息
			switch (response.data) {
				case 'PAID':
					ElMessage({
						message: `支付成功`,
						type: 'success',
					});
					clearInterval(timerId.value);
					createSuccessType.value = true;
					//根据订单id获取优惠卷详情
					handleCouponDetail(orderId);
					successType.value = false;
					break;
				case 'FAILED':
					ElMessage({
						message: `支付失败`,
						type: 'error',
					});
					clearInterval(timerId.value);
					break;
				case 'CANCELLED':
					ElMessage({
						message: `订单已取消`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				case 'REFUNDED':
					ElMessage({
						message: `已退款`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				default:
					break;
			}
		} else {
			clearInterval(timerId.value);
		}
	} catch (error) {
		clearInterval(timerId.value);
	}
};
const activitieschange = (e) => {
	if (e.length > 0) {
		handlePriceChangeAll();
	}
};

function handleReturn() {
	// 关闭对话框
	showShangYutong.value = false;
}
</script>

<style lang="less" scoped>
@import url('./style.less');
</style>
<style lang="scss">
.el-dropdown__popper,
.el-picker__popper,
.el-select__popper {
	.el-popper__arrow {
		left: 50% !important;
		transform: translateX(-50%);
	}
}
</style>
