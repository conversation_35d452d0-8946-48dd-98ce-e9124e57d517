<template>
	<el-dialog
		v-model="props.dialogVisible"
		:close-on-click-modal="false"
		:show-close="false"
		align-center="center"
		:before-close="handleClose"
		style="width: 448px; border-radius: 6px"
	>
		<div class="form_content">
			<div class="dialogHeaderRight">
				<div class="dialogHeaderLeft">使用平台赠券</div>
				<el-icon><CloseBold @click="handleClose" /></el-icon>
			</div>
			<div class="content">
				完成下方手机号验证后，优惠券将发放至该手机号绑定的【{{ props.welfareCouponsAddCrad.brandName }}打车】平台账号中，登录{{
					props.welfareCouponsAddCrad.brandName
				}}打车即可查看及使用。
			</div>

			<div class="contentk">
				<div class="form_right">
					<div class="form_right_img">
						<img src="@/assets/prompts.png" alt="" />
					</div>
					<div class="form_right_content">请确保该手机号已在【{{ props.welfareCouponsAddCrad.brandName }}打车】完成注册，否则可能会发放失败</div>
				</div>

				<div class="content_banner">
					<el-input
						v-model="informationObj.tel"
						style="width: 100%"
						size="large"
						:placeholder="`请输入已注册【${props.welfareCouponsAddCrad.brandName}打车】的手机号`"
					/>
					<el-input v-model="informationObj.code" style="width: 100%" size="large" placeholder="请输入验证码">
						<template #suffix>
							<div class="verificationCode" @click="onGetCode()" v-if="isSend">发送验证码</div>
							<div class="getCode" v-else style="margin-right: 8px">重新发送{{ time }}s</div>
						</template>
					</el-input>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="dialog_footer">
				<div>
					<el-button type="primary" @click="handleDetermine"> 确定 </el-button>
				</div>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { onMounted, defineProps } from 'vue';
import { ElMessage } from 'element-plus';
import { getCodes } from 'REQUEST_API';
const emit = defineEmits();
const props = defineProps({
	// 展示状态
	dialogVisible: {
		type: Boolean,
		default: true,
	},
	welfareCouponsAddCrad: {
		typer: Object,
		default: () => {},
	},
});
// 变更验证码秒数
const time = ref(60);
// 变更验证码
const isSend = ref(true);
let informationObj = reactive({
	tel: null, //手机号 tel
	code: null, //密码 code
});

onMounted(() => {
	// console.log(props.welfareCouponsAddCrad.brandName, 'props.welfareCouponsAddCrad.brandName');
});

// 关闭对话框
function handleClose(type, informationObj) {
	emit('handleWelfareClose', { informationObj: informationObj, type: type });
}

// 确定
function handleDetermine() {
	// 验证码校验
	if (!informationObj.code) {
		return ElMessage({
			message: '请输入验证码',
			type: 'warning',
		});
	}
	handleClose(1, informationObj);
}

const onGetCode = async () => {
	const tel = informationObj.tel;

	if (!tel) {
		return ElMessage({
			message: '请输入手机号',
			type: 'warning',
		});
	}
	// 校验手机号
	const reg = /^1[3-9]\d{9}$/;
	if (!reg.test(tel)) {
		return ElMessage({
			message: '请输入正确的手机号',
			type: 'warning',
		});
	}
	handleCodes(tel);
};

// 发送验证码
async function handleCodes(phone) {
	await getCodes({ phone: phone })
		.then((res) => {
			console.log(res);
			// 发送成功提示
			ElMessage({
				message: '验证码发送成功,请注意查收~',
				type: 'success',
			});

			isSend.value = false;
			const timer = setInterval(() => {
				time.value--;
				if (time.value == 0) {
					clearInterval(timer);
					isSend.value = true;
					time.value = 60;
				}
			}, 1000);
		})
		.catch((err) => {
			console.log('err', err);
			// 显示错误信息给用户
		});
}
</script>
<style lang="scss" scoped>
@import url('./css.scss');
</style>
