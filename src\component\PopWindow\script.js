import PopWindow from './index.vue';
// import { component } from 'vue';
import store from '@/store';
export const openPop = (popComponent, externalData, closeCallBack) => {
	let comp = '';
	const index = document.getElementsByClassName('popWindow').length;
	const _id = `pop-window${index}`;
	const app = createApp(PopWindow, {
		zIndex: index,
		popData: externalData,
		closeCallBack,
		id: _id,
		destroy: () => {
			app.unmount();
		},
	});
	comp = defineAsyncComponent(() => import(/* @vite-ignore */ `/${popComponent}.vue`));

	app.use(store).component('comp', comp);

	const _dom = document.createElement('div');
	_dom.id = _id;
	document.body.appendChild(_dom);
	app.provide('$openPop', openPop);
	app.mount(`#${_id}`);
};
