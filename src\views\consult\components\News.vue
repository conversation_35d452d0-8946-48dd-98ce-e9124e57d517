<template>
<div class="title_box">
    <el-button  round @click="back(router.push('consult'))" style="margin: 15px;">返回</el-button>
    <a style="margin: 15px;">琥珀新闻</a>
</div>
<div class="new_table">
    <el-tabs type="border-card">
    <el-tab-pane label="新闻">
        <news-map></news-map>
    </el-tab-pane>
    <el-tab-pane label="访谈">
        <news-information></news-information>
    </el-tab-pane>
    <el-tab-pane label="深度分析">
        <new-interview></new-interview>
    </el-tab-pane>
    
  </el-tabs>
</div>
</template>

<script setup>
import newsMap from './news-map.vue'
import newsInformation from './news-information.vue'
import newInterview from './news-interview.vue'
import {ref} from 'vue'
import {useRouter} from 'vue-router'
const router = useRouter()

</script>

<style lang="less" scoped>
.title_box {
    display: flex;
    margin-top: 2px;
    margin-left: 19px;
    .title_pic{
        width: 35px;
        float: left;
        margin-right: 10px;
        position: relative;
        top: 0px;
        visibility: hidden;
    }
    a ,
    span
    {
        text-decoration: none;
        color: #000;
        font-size: 24px;
        font-weight: bold;
    }
}
</style>