<template>
	<div class="common-layout">
		<el-container>
			<el-header height="20%">
				<el-row v-if="data">
					<el-col style="display: flex; justify-content: space-between; align-items: center" :span="5">
						<div style="margin-left: 18px">
							<el-text style="font-size: 16px; font-weight: bold; color: #000; margin-top: 10px" tag="p">{{ data.am_name }}</el-text>
							<el-text style="font-size: 14px; margin-top: 20px; color: #000" tag="p">{{ data.am_address }}</el-text>
						</div>
						<el-divider direction="vertical" style="height: 80%" />
					</el-col>

					<el-col :span="12" style="display: flex; justify-content: space-around; align-items: center">
						<div v-for="(item, index) in info" :key="item" style="text-align: center">
							<p>{{ Object.entries(item)[0][1] }}</p>
							<p>{{ Object.entries(item)[0][0] }}</p>
						</div>
					</el-col>
					<el-col :span="2"></el-col>
					<el-col :span="2" style="display: flex; justify-content: center">
						<div style="display: flex; flex-direction: column-reverse">
							<el-text
								type="primary"
								style="
									font-size: 32px;
									border-radius: 50%;
									overflow: hidden;
									width: 42px;
									height: 40px;
									background-color: #409eff;
									color: #fff;
									text-align: center;
									line-height: 40px;
								"
								tag="p"
								>{{ data.am_degree }}</el-text
							>
							<el-text type="warning" tag="p" style="margin-bottom: 6px">{{ data.am_typex }}</el-text>
						</div>
					</el-col>
						<el-button size="" icon="plus" style="margin: 25px 0 0 120px" @click.stop=""></el-button>
					<el-col :span="2"></el-col>
				</el-row>
			</el-header>
			<el-main>
				<el-tabs type="border-card" @tab-click="changeTab">
					<el-tab-pane v-for="(label, index) in tabLabels" :key="label.path" :label="label.name"> </el-tab-pane>
				</el-tabs>
				<Transition> <router-view /></Transition>
			</el-main>
		</el-container>
	</div>
</template>

<script setup>
import http from '@/utils/http';
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const data = ref(null);
const route = useRoute();
const info = ref(null);
const router = useRouter();
const tabLabels = [
	{
		path: 'property',
		name: '概况',
	},
	{
		path: 'manage',
		name: '管理',
	},
	{
		path: 'position',
		name: '位置',
	},
	{
		path: 'population',
		name: '人口',
	},
	{
		path: 'canvass',
		name: '招商',
	},
	{
		path: 'cost',
		name: '估值',
	},
	{
		path: 'banking',
		name: '证券化',
	},
	{
		path: 'broker',
		name: '咨询',
	},
];
const changeTab = (tab) => {
	const selectedTab = tabLabels.find((item) => item.name === tab.props.label);
	if (selectedTab) {
		router.replace({
			name: selectedTab.path,
			params: {
				id: route.params.id,
			},
		});
	}
};

onMounted(() => {
	http
		.get('/api/a_maps.do?act=ratingPassDetailsAction', {
			params: {
				maps_id: route.params.id,
				action: 'property',
			},
		})
		.then((res) => {
			if (res && res.list && res.list[0]) {
				data.value = res.list[0];
				info.value = data.value.am_topmk.map((item) => {
					const [key, value] = item.split('=');
					return { [key]: value };
				});
			}
		});
});
</script>

<style lang="less" scoped>
.el-tabs:deep(.el-tabs__item) {
	font-size: 16px;
	color: #000;
}
.el-button + .el-button {
	margin-left: 0px;
}
.v-enter-active,
.v-leave-active {
	transition: opacity 0.5s ease;
}

.el-tabs--border-card > :deep(.el-tabs__content) {
	padding: 0px;
}
.v-enter-from,
.v-leave-to {
	opacity: 0;
}
.common-layout {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	overflow: hidden;
}
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 36px;
}
</style>
