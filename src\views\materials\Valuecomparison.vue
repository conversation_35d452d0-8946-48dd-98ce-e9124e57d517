<template>
  <div>
    <div ref="echartsContainer" id="main" style="width: 500px; height: 500px; padding: 0 80px"></div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted,watch} from 'vue'
import * as echarts from 'echarts';
const myChart = ref(null);
const echartsContainer = ref(null);
const props = defineProps({
    valueDateList: {
        type: Array,
        default: 300
    }
});
const legend1 = ref('数据1')
const legend2 = ref('数据2')
console.log(props.valueDateList,"valueDateList");
onMounted(() => {
    myChart.value = echarts.init(echartsContainer.value);
		//props发生改变就更新
    
    // 设置图表选项
	        myChart.value.setOption({
                tooltip: {
					trigger: 'axis'
				},
				legend: {
					data: [{name: legend1.value},{name: legend2.value}],
					// 点击图例，可以隐藏或显示对应的数据
				},
				xAxis: {
					type: 'category',
					data: ['1月', '2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
				},
				yAxis: [
					{
						type: 'value',
                       	min:0.6,
                        max:1.4,
                        interval:0.05,

						name: '相对价值'
						// 其他轴的配置...
					},
				],
				series: [
					{
						name: '数据1',
						type: 'line',
						yAxisIndex: 0, // 使用左侧纵轴
						data: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1, 0.2]
					},
				]
    })
});

const dealPrice1 = ref([])
const dealPrice2 = ref([])
const echartsData = () => {
    legend1.value = props.valueDateList[0].name
    legend2.value = props.valueDateList[1].name
    console.log( legend1.value ,42134, legend2.value);
    const yAxis = props.valueDateList[0].comparableValue
    const yAxis2 = props.valueDateList[1].comparableValue
    for (let i = 0; i < 12; i++) {
        dealPrice1.value.push(yAxis)
        dealPrice2.value.push(yAxis2)
        console.log(dealPrice1.value, 41234);
    }
	if (props.valueDateList.length > 0) {
        updateEcharts(dealPrice1)
    }
}
const updateEcharts = (dealPrice1) => {
    console.log(props.valueDateList.length,77812);
    console.log( legend1.value,1513331233);
    if(props.valueDateList.length > 0) {
        console.log(props.valueDateList[0],"props.valueDateList12");
        
        myChart.value.setOption({
                tooltip: {
					trigger: 'axis'
				},
				legend: {
				  data: [{
                    name: legend1.value,
                    },
                    {
                     name: legend2.value,
                  }]

    		    },
				xAxis: {
					type: 'category',
					data: ['1月', '2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'],
				},
				yAxis: [
					{
						type: 'value',
                       	min:0.6,
                        max:1.4,
                        interval:0.05,

						name: '相对价值'
						// 其他轴的配置...
					},
				],
				series: [
					{
						name: legend1.value,
						type: 'line',
						symbol:'none',
						data: dealPrice1.value
					},
					{
						name: legend2.value,
						type: 'line',
						symbol:'none',
						data: dealPrice2.value
					}
				]
        })
    }
}

watch(()=>props.valueDateList, (newVal, oldVal) => {
    echartsData()

}, { deep: true });
</script>
<style scoped lang="less">
</style>