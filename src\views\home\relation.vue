<template>
	<div class="relation_main">
		<div class="box_two">
			<div class="formBox1">
				<div class="banner_box" ref="banner_box">
					<div class="title">联系我们</div>
					<div class="tips">请填写您的联系方式及需要咨询的详细问题，工作人员会尽快联系您</div>
				</div>
				<el-form :model="form" label-width="auto" label-position="top">
					<el-form-item label="如何称呼您（必填）">
						<el-input v-model="form.name" placeholder="请输入您的名字" />
					</el-form-item>
					<el-form-item label="联系电话（必填）">
						<el-input v-model="form.phone" placeholder="请输入您的电话" />
					</el-form-item>
					<el-form-item label="邮箱">
						<el-input v-model="form.email" placeholder="请输入您的邮箱" />
					</el-form-item>
					<el-form-item label="公司名称">
						<el-input v-model="form.company" placeholder="请输入公司名称" />
					</el-form-item>
					<el-form-item label="从事行业">
						<el-input v-model="form.industry" placeholder="请输入您的从事行业" />
					</el-form-item>

					<el-form-item label="咨询问题">
						<el-input
							v-model="form.description"
							:resize="false"
							class="textareaInput"
							:rows="5"
							type="textarea"
							placeholder="请输入您的想了解的问题"
						/>
					</el-form-item>
					<div>
						<el-button type="primary" style="width: 132px" @click="onSubmit" color="#1868F1">提交</el-button>
					</div>
				</el-form>
			</div>
			<div class="tab_card">
				<div class="service_box_list" v-for="(item, index) in data.serviceList" :key="index">
					<h4 class="service_h4">{{ item.title }}</h4>
					<h5 class="service_h5" @click="copyText(item.tips1)">
						<el-icon style="margin-right: 10px; vertical-align: middle"><PhoneFilled /></el-icon>{{ item.tips1 }}
					</h5>
					<h5 class="service_h5" @click="copyText(item.tips2)">
						<el-icon style="margin-right: 10px; vertical-align: middle"><Message /></el-icon>{{ item.tips2 }}
					</h5>
					<h5 class="service_h5" @click="copyText(item.tips3)">
						<el-icon style="margin-right: 10px; vertical-align: middle"><LocationFilled /></el-icon>{{ item.tips3 }}
					</h5>
					<p class="service_p">{{ item.tips4 }}</p>
					<img :src="item.icon" alt="" class="erji" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { tabPermissions } from '../../api/layout.js';
import { Addcontact } from '../../api/home.js';
const router = useRouter();
import { reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useStore } from '../../store';
import erji from '../../assets/images/home/<USER>';
import shangwu from '../../assets/images/home/<USER>';
const form = reactive({
	name: '',
	industry: '',
	description: '', //咨询问题
	company: '',
	phone: '',
	email: '',
});
const num = ref(0);
const banner_box = ref();
const islogin = ref(true);
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const data = reactive({
	menuIndex1: 0,
	menus1: ['标标准', '联系我们'],
	menuIndex2: 0,
	serviceList: [
		{
			title: '客户服务',
			tips1: '************',
			tips2: '<EMAIL>',
			tips3: '青岛市黄岛区瑞源名嘉国际1栋1212室',
			tips4: '工作时间：北京时间AM 8:00-PM 5:30',
			icon: erji,
		},
		{
			title: '商务服务',
			tips1: '************',
			tips2: '<EMAIL>',
			tips3: '青岛市黄岛区瑞源名嘉国际1栋1212室',
			tips4: '工作时间：北京时间AM 8:00-PM 5:30',
			icon: shangwu,
		},
	],
});

onMounted(() => {
	window.scrollTo(0, 0);
});

const onSubmit = async () => {
	console.log(form, '表单');
	let params = {
		name: form.name,
		industry: form.industry,
		description: form.description,
		company: form.company,
		phone: form.phone,
		email: form.email,
	};
	// 校验表单都不为空
	if (!params.name || !params.phone) {
		ElMessage.warning('必填项不可为空');
		return;
	}
	await Addcontact(params)
		.then((res) => {
			console.log(res);
			if (res.code === 200) {
				router.push('/submitSuccess');
				form.name = '';
				form.industry = '';
				form.description = '';
				form.company = '';
				form.phone = '';
				form.email = '';
			}
		})
		.catch((err) => {
			console.log('请求失败！');
		});
};
const copyText = (item) => {
	console.log(item);
	if (navigator.clipboard && window.isSecureContext) {
		navigator.clipboard
			.writeText(item)
			.then(() => {
				alert('复制成功');
			})
			.catch(() => {
				alert('复制失败');
			});
	} else {
		// 创建text area
		const textArea = document.createElement('textarea');
		textArea.value = item;
		// 使text area不在viewport，同时设置不可见
		document.body.appendChild(textArea);
		textArea.focus();
		textArea.select();
		return new Promise((resolve, reject) => {
			// 执行复制命令并移除文本框
			document.execCommand('copy') ? resolve() : reject(new Error('出错了'));
			textArea.remove();
		}).then(
			() => {
				alert('复制成功');
			},
			() => {
				alert('复制失败');
			}
		);
	}
	// 		navigator.clipboard.writeText(item.value).then(
	//     () => alert('复制成功'),
	//     () => alert('复制失败')
	//   );
};
</script>
<style scoped lang="less">
::v-deep .el-form .el-form-item__label {
	color: #1d2129;
	font-weight: 500;
}
.textareaInput {
	resize: none;
}
.relation_main {
	width: 100%;
	height: 100%;
	min-height: 100vh;
	// background-color: rgba(245, 245, 245, 1);
	background: url('../../assets/shoppingCart.png') no-repeat;
	background-size: initial;
	padding-top: 70px;
	.banner_box {
		width: 100%;
		.title {
			font-weight: 700;
			line-height: 100px;

			font-size: 40px;
			// font-weight: 600;
			color: rgba(29, 33, 41, 1);
		}

		.tips {
			font-size: 20px;
			font-weight: 500;
			color: #1d2129;
			margin-bottom: 20px;
		}
	}
	.box_two {
		display: flex;
		justify-content: center;
		height: 700px;
		.formBox1 {
			width: 30%;
			margin: 0 5% 0 15%;
		}
		.tab_card {
			margin-top: 170px;
			width: 40%;
			// margin: 0 auto;
			height: 468px;
			display: flex;
			justify-content: space-between;
			position: relative;
			flex-direction: column;
			box-sizing: border-box;
			.erji {
				width: 32px;
				height: 32px;
				position: absolute;
				top: 30px;
				right: 40px;
			}
			.service_box_list {
				width: 462px;
				height: 300px;
				position: relative;
				animation: fadeIn 1s ease-in-out forwards;
				cursor: pointer;
				border-radius: 20px;
				background: #fff;
				margin-bottom: 30px;
				.service_h4 {
					color: #4e5969;
					margin-top: 30px;
					margin-left: 30px;
					font-weight: 600;
					font-size: 24px;
					margin-bottom: 25px;
					animation: fadeIn 1s ease-in-out forwards;
				}
				.service_h5 {
					color: #4e5969;
					margin-left: 30px;
					font-weight: 600;
					margin-top: 0px;
					font-size: 18px;
					line-height: 30px;
					box-sizing: border-box;
					vertical-align: middle;
					animation: fadeIn 1s ease-in-out forwards;
					margin-bottom: 20px;
				}
				.service_h5_1 {
					color: #4e5969;
					margin-left: 30px;
					font-weight: 600;
					margin-top: 0px;
					font-size: 18px;
					line-height: 30px;
					box-sizing: border-box;
					vertical-align: middle;
					animation: fadeIn 1s ease-in-out forwards;
					margin-bottom: 20px;
				}
				.service_h5:hover {
					color: #1868f1;
				}
				.service_p {
					color: #86909c;
					margin-left: 30px;
					margin-top: 40px;
					font-size: 16px;
				}
				.service_btn {
					width: 259px;
					height: 52px;
					color: #4e5969;
					background: #f5f6f7;
					border-radius: 26px;
					border: 1px solid #4e5969;
					margin-top: 105px;
					margin-left: 30px;
					text-align: center;
					font-size: 20px;
					padding-top: 13px;
					box-sizing: border-box;
				}
			}
		}
	}
}

@media screen and (max-width: 1024px) {
	.relation_main {
		width: 100%;
		height: 100%;
		min-height: 100vh;
		padding-bottom: 146px;
		background-color: rgba(245, 245, 245, 1);
		.service_box_list {
			width: 100% !important;
		}
		.box_two {
			display: block;
			height: auto;
			.formBox1 {
				width: 90%;
				margin: 0 5%;
			}
			.tab_card {
				margin-top: 0px;
				width: 100%;
				height: 100%;
				padding: 15px;
				box-sizing: border-box;
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				.service_box_list {
					padding: 0 30px;
					box-sizing: border-box;
					.service_h4 {
						margin-left: 0;
					}
					.service_h5 {
						margin-left: 0;
					}
					.service_p {
						margin-left: 0;
					}
				}
			}
		}
		.banner_box {
			width: 100%;
			height: 100%;
			box-sizing: border-box;
		}
	}
}
</style>
