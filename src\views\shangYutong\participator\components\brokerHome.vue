<template>
	<div class="content_box">
		<div class="title">
			<div class="goback" @click="handleGoBack()">
				<span
					><el-icon> <ArrowLeft /> </el-icon
				></span>
				返回
			</div>
			<div class="card_body">
				<div class="avatar">
					<img src="@/assets/Default.png" alt="" v-if="borkerInfo.avatar == '' || borkerInfo.avatar == '暂无'" />
					<img :src="`${proxyAddress}${borkerInfo.avatar}`" alt="" v-else />
				</div>
				<div class="tips_box">
					<div class="nickName">{{ borkerInfo.borkerName }}</div>
					<div class="company_name">{{ borkerInfo.companyName }}</div>
					<div class="company_name" style="font-weight: 900">{{ borkerInfo.phone }}</div>
				</div>
			</div>
		</div>
		<div class="container_box">
			<div class="buildList">
				<div class="title1">主管楼宇</div>
				<div class="build_box">
					<div
						class="item active"
						:class="activeIndex === index ? 'item_act' : ''"
						v-for="(item, index) in buildNameList"
						:key="index"
						@click="onActive(item, index)"
					>
						{{ item.buildingName }}<span v-if="activeIndex === index"></span>
					</div>
				</div>
			</div>
			<!-- 楼宇信息 -->
			<div class="center_main">
				<div class="tab_box1">
					<div
						class="tab1 active"
						:class="typyIndex === index ? 'tab1_act' : ''"
						v-for="(item, index) in tab_list"
						:key="index"
						@click="changetab(item, index)"
					>
						{{ item }}
					</div>
				</div>
				<!-- 列表 -->
				<div class="live_box">
					<div class="build_item" v-for="(item, index) in list" :key="index">
						<div class="tab_box">
							<div class="tab_item" :class="selectIndex == 0 ? 'activeed' : ''" @click="onSelect(0)">视频</div>
							<div class="tab_item" :class="selectIndex == 1 ? 'activeed' : ''" @click="onSelect(1)">图片</div>
						</div>
						<div class="swapper_box" v-if="selectIndex == 1">
							<el-carousel height="220px" :autoplay="false">
								<el-carousel-item v-for="(pic, index) in item.imgs" :key="index">
									<img :src="`${proxyAddress}${pic}`" alt="" />
								</el-carousel-item>
							</el-carousel>
						</div>
						<div class="redio_box" v-if="selectIndex == 0">
							<video
								:src="`${proxyAddress_video}${item.video}`"
								:controls="true"
								:autoplay="false"
								:loop="true"
								:volume="0"
								width="100%"
								height="100%"
							/>
						</div>

						<div class="property">
							<div class="area">
								<span>面积：</span>
								<span>{{ item.area }}m²</span>
							</div>
							<div class="rent">
								<span>租金：</span>
								<span>{{ item.rent }}元/m²/天</span>
							</div>
							<div class="state">租赁</div>
						</div>
					</div>
				</div>
				<div class="pageNum_box">
					<div class="num1">
						共<span>{{ total }}</span
						>项
					</div>
					<el-pagination
						v-if="total != ''"
						current-page="currentPage"
						page-size="pageSize"
						:page-sizes="[5, 10, 20, 30]"
						@size-change="pageSizeChange"
						@current-change="handlePageChange"
						layout="->,prev, pager, next, jumper, sizes,total"
						:total="total"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getBrokerInfo, getBuildBook } from '@/api/participant.js';
import { getBrokerBuildingList, getBuildingBookList } from '@/api/syt.js';

const route = useRoute();
const router = useRouter();
import { defineComponent } from 'vue';
// import videojs from "video.js";
//   import 'video.js/dist/video-js.css'

console.log(route.query, 7812);
const typyIndex = ref(0);
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(0);
const handlePageChange = (page) => {
	console.log(page, 76);
	currentPage.value = page;
	getBrokerInfos();
};
const pageSizeChange = (val) => {
	console.log(val, 'val');
	pageSize.value = val;
	getBrokerInfos(val);
};
const tab_list = ref(['全部', '租赁', '购买']);
const borkerInfo = ref({});
const buildNameList = ref([]);
const buildList = ref([]);
const list = ref([]);
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const proxyAddress_video = ref('https://static.biaobiaozhun.com/'); //视频前缀
const activeIndex = ref(0); //选中的tabs
const imas = ref('');
const imgArr = ref([]);
// 获取经纪人信息
const getBrokerInfos = async () => {
	borkerInfo.value = route.query;
	const queryParams = {
		currentPage: currentPage.value,
		userId: route.query.userId,
	};
	console.log(queryParams, 'queryParams');
	await getBrokerBuildingList(queryParams)
		.then((res) => {
			console.log(res, 'ss12');
			buildNameList.value = res.data.rows;
			console.log(buildNameList.value[0], 'buildNameList');
			total.value = res.data.total;

			//   默认选中第一项
			onActive(buildNameList.value[0], 0);
			// total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
// getBrokerInfos();
onMounted(() => {
	getBrokerInfos();
});

function handleGoBack() {
	router.push({
		path: '/shangYutong/participator/components/participant',
		query: {
			name: '顾问',
		},
	});
}

//切换类型
const changetab = (item, index) => {
	typyIndex.value = index;
	if (index === 1) {
		let list1 = [];
		list.value = [];
		buildList.value.forEach((v, i) => {
			if (v.dealType == 2) {
				list1.push(v);
			}
		});
		list.value = list1;
	} else if (index === 2) {
		let list1 = [];
		list.value = [];
		buildList.value.forEach((v, i) => {
			if (v.dealType != 2) {
				list1.push(v);
			}
		});

		list.value = list1;
	} else {
		list.value = buildList.value;
	}
};
// 切换tabs
const onActive = async (item, index) => {
	activeIndex.value = index;
	typyIndex.value = 0;
	selectIndex.value = 0;
	console.log(item, index, 4321);
	const queryParams = {
		currentPage: currentPage.value,
		userId: route.query.userId,
		buildingInfoId: item.buildingInfoId,
		bookType: item.buildingFlag,
		dealType: '',
	};
	// 获取楼书
	await getBuildingBookList(queryParams)
		.then((res) => {
			buildList.value = res.data.rows.map((item) => {
				return {
					...item,
					imgs: item.imgs.split(','),
				};
			});
			list.value = res.data.rows.map((item) => {
				return {
					...item,
					imgs: item.imgs.split(','),
				};
			});
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};

const selectIndex = ref(0);

const onSelect = (tab) => {
	console.log(tab, 73487);
	selectIndex.value = tab;
};
</script>

<style scoped lang="less">
.content_box {
	width: 100%;
	height: calc(100vh - 78px);
	// min-height: 100vh;
	overflow: hidden;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;

		.goback {
			width: 63px;
			height: 44px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			cursor: pointer;

			span {
				width: 14px;
				height: 14px;
				display: flex;
				justify-content: center;
				align-items: center;
				color: rgba(24, 104, 241, 1);
				font-weight: bold;
				margin-right: 5px;
			}
		}

		.card_body {
			width: 24%;
			height: 44px;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			border-radius: 6px;
			padding: 0 15px;
			box-sizing: border-box;
			position: relative;

			&::before {
				content: '';
				width: 1px;
				height: 15px;
				background-color: rgba(231, 231, 231, 1);
				position: absolute;
				left: 0;
			}

			.avatar {
				width: 40px;
				height: 40px;
				border-radius: 8px;
				overflow: hidden;
				margin-right: 10px;

				img {
					width: 100%;
					height: 100%;
				}
			}

			.tips_box {
				width: 200px;
				height: 44px;
				display: flex;
				justify-content: center;
				align-items: flex-start;
				flex-direction: column;

				.nickName {
					font-weight: bold;
				}

				.company_name {
					font-size: 12px;
				}
			}
		}
	}

	.container_box {
		width: 100%;
		height: calc(100% - 63px);
		// min-height: 100vh;
		padding-top: 10px;
		box-sizing: border-box;
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.buildList {
			width: 200px;
			height: calc(100% - 12px);
			border-radius: 6px;
			padding: 0 10px;
			box-sizing: border-box;
			position: absolute;
			top: 10px;
			background-color: rgba(255, 255, 255, 1);
			box-shadow: 2px 5px 5px rgba(0, 0, 0, 0.1);

			.title1 {
				width: 100%;
				height: 54px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				border-bottom: 1px solid rgba(231, 231, 231, 1);
			}

			.build_box {
				width: 100%;
				height: calc(100% - 90px);
				overflow: scroll;
				margin-top: 15px;

				.item {
					width: 100%;
					height: 46px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					position: relative;
					border-radius: 6px;
					padding: 0 30px 0 15px;
					box-sizing: border-box;

					span {
						width: 16px;
						height: 16px;
						background-image: url('../../../../assets/images/shangYutong/check_icon.png');
						background-size: 100% 100%;
						position: absolute;
						right: 15px;
					}
				}

				.item_act {
					background-color: rgba(230, 239, 255, 1);
					color: rgba(24, 104, 241, 1);
				}
			}
		}

		.center_main {
			width: calc(100% - 220px);
			height: calc(100% - 0px);
			// min-height: 100vh;
			background-color: rgba(255, 255, 255, 1);
			border-radius: 6px;
			margin-left: 210px;
			position: relative;

			.tab_box1 {
				width: 100%;
				height: 54px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				padding: 0 20px;
				border-bottom: 1px solid rgba(231, 231, 231, 1);
				box-sizing: border-box;

				.tab1 {
					width: 60px;
					height: 54px;
					display: flex;
					justify-content: center;
					align-items: center;
					color: rgba(102, 102, 102, 1);
					position: relative;
				}

				.tab1_act {
					color: rgba(26, 26, 26, 1);

					&::after {
						content: '';
						width: 24px;
						height: 3px;
						background-color: rgba(3, 93, 255, 1);
						position: absolute;
						bottom: 0;
					}
				}
			}

			.live_box {
				width: 100%;
				height: calc(100% - 137px);
				overflow: scroll;
				display: flex;
				justify-content: flex-start;
				align-items: flex-start;
				flex-wrap: wrap;
				padding: 20px;
				box-sizing: border-box;

				.build_item {
					width: 48%;
					height: 262px;
					margin: 5px 5px 40px 5px;
					position: relative;
					border-radius: 6px;
					overflow: hidden;

					.tab_box {
						width: 120px;
						height: 20px;
						display: flex;
						justify-content: center;
						align-items: center;
						border-radius: 15px;
						background: rgba(0, 0, 0, 0.45);
						color: #fff;
						font-size: 12px;
						// text-align: center;
						position: absolute;
						top: 15px;
						left: 50%-20px;
						z-index: 99;

						.tab_item {
							width: 50%;
							height: 100%;
							display: flex;
							justify-content: center;
							align-items: center;
						}
					}

					.activeed {
						border-radius: 15px;
						width: 40%;
						color: rgba(255, 255, 255, 1);
						background: rgba(24, 104, 241, 1);
					}

					.redio_box {
						width: 100%;
						height: 220px;
						border-radius: 6px;
					}

					.swapper_box {
						width: 100%;
						height: 220px;
						border-radius: 6px;
						overflow: hidden;
					}

					.property {
						width: 100%;
						height: 42px;
						background-color: rgba(245, 246, 247, 1);
						color: rgb(0, 0, 0);
						// font-family: youshebiaotihei;
						font-size: 12px;
						text-align: left;
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 0 20px;
						box-sizing: border-box;

						.area {
							display: flex;
							// margin-right: 100px;
						}

						.state {
							width: 44px;
							height: 22px;
							border-radius: 2px;
							background: rgba(24, 104, 241, 1);
							color: rgb(255, 255, 255);
							font-family: 微软雅黑;
							font-size: 14px;
							font-weight: 700;
							display: flex;
							justify-content: center;
							align-items: center;
						}
					}
				}
			}

			.pageNum_box {
				width: 100%;
				height: 48px;
				position: absolute;
				bottom: 20px;
				padding: 20px;
				border-top: 1px solid rgba(231, 231, 231, 1);
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.num1 {
					width: auto;
					margin-top: 15px;
					display: inline-block;
					height: auto;
					span {
						color: rgba(3, 93, 255, 1);
					}
				}
			}
		}
	}
}
</style>
