<template>
	<el-dialog
		v-model="dialogVisible"
		:width="400"
		:close-on-click-modal="false"
		:show-close="true"
		align-center
		class="confirm_dialog"
		custom-class="confirm-dialog"
	>
		<div class="content">
			<div class="title">确定要使用这张权益卡券吗？</div>
			<div class="user-info">开通权益用户：{{ $vuexStore.state.userInfo.userName || '用户名' }}</div>
			<div class="notice">确认使用后权益卡券生效，开始消耗购买时长</div>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button class="confirmBtn" @click="handleCancel">我再想想</el-button>
				<el-button class="confirmbuyBtn" type="primary" @click="handleConfirm">确定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref } from 'vue';
const emit = defineEmits(['handleConfirm']);
const dialogVisible = ref(false);

// 父组件传递过来的方法 显示
const show = () => {
	dialogVisible.value = true;
};

// 父组件传递过来的方法 隐藏
const hide = () => {
	dialogVisible.value = false;
};

const handleCancel = () => {
	dialogVisible.value = false;
};

const handleConfirm = () => {
	// 处理确认逻辑
	dialogVisible.value = false;
	emit('handleConfirm');
};

// 暴露方法给父组件
defineExpose({ show, hide });
</script>

<style lang="scss">
.confirm_dialog {
	height: 189px;
	border-radius: 8px;
	padding: 16px !important;

	.el-dialog__header {
		margin: 0;
		padding: 0;
		.el-dialog__title {
			font-size: 16px;
			font-weight: 500;
		}
	}

	.el-dialog__body {
		padding: 8px 0px;
	}
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		.title {
			font-size: 20px;
			font-weight: 500;
			line-height: 28px;
			color: #1d2129;
			margin-bottom: 16px;
		}
		.user-info {
			font-size: 14px;
			font-weight: 400;
			line-height: 19.6px;
			color: #1d2129;
			margin-bottom: 8px;
		}
		.notice {
			font-size: 12px;
			font-weight: 400;
			line-height: 16.8px;
			color: #7281ab;
			margin-bottom: 16px;
		}
	}
	.el-dialog__footer {
		padding: 0 20px 24px;

		.dialog-footer {
			display: flex;
			justify-content: center;
			gap: 24px;

			.el-button {
				width: 84px;
				height: 36px;
				margin: 0;
				padding: 0;
				border-radius: 40px;
				font-size: 14px;
				font-weight: 400;
			}
			.confirmBtn {
				color: #bbc1d3;
			}
			.confirmbuyBtn {
				border: 1px solid #0a42f1;
				background: #0a42f1;
			}
		}
	}
}
</style>
