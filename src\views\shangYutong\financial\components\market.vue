<template>
	<div class="content_box">
		<!-- <div class="title">金融市场</div> -->
		<div class="container_box">
			<div class="data_box">
				<div class="data_item">
					<div class="top items">
						<span class="ratio"
							>{{ formattedMoney(finDataList.shanghaiCompositeIndex, 2) }}
							<img v-if="finDataList.shanghaiCompositeIndexTrend > 0" src="@/assets/rise.png" style="width: 20px; height: 20px" alt="" />
							<img v-else src="@/assets/decline.png" style="width: 20px; height: 20px" alt="" />
						</span>
						<span class="datas">上证指数</span>
					</div>
					<!-- <div class="bottom items">
		            <span  class="datas">2947.69</span>
		            <span class="ratio">+19.60</span>
		        </div> -->
				</div>
				<div class="data_item">
					<div class="top items">
						<span class="ratio"
							>{{ formattedMoney(finDataList.nasdaq, 2) }}
							<img v-if="finDataList.nasdaqTrend > 0" src="@/assets/rise.png" style="width: 20px; height: 20px" alt="" />
							<img v-else src="@/assets/decline.png" style="width: 20px; height: 20px" alt="" />
						</span>
						<span class="datas">纳斯达克</span>
					</div>
					<!-- <div class="bottom items">
		            <span  class="datas">2947.69</span>
		            <span class="ratio">+19.60</span>
		        </div> -->
				</div>
				<div class="data_item">
					<div class="top items">
						<span class="ratio"
							>{{ formattedMoney(finDataList.usDollarIndex, 2) }}
							<img v-if="finDataList.usDollarIndexTrend > 0" src="@/assets/rise.png" style="width: 20px; height: 20px" alt="" />
							<img v-else src="@/assets/decline.png" style="width: 20px; height: 20px" alt="" />
						</span>
						<span class="datas">美元指数</span>
					</div>
					<!-- <div class="bottom items">
		            <span  class="datas">2947.69</span>
		            <span class="ratio">+19.60</span>
		        </div> -->
				</div>
				<div class="data_item">
					<div class="top items">
						<span class="ratio"
							>{{ formattedMoney(finDataList.hangSengIndex, 2) }}
							<img v-if="finDataList.hangSengIndexTrend > 0" src="@/assets/rise.png" style="width: 20px; height: 20px" alt="" />
							<img v-else src="@/assets/decline.png" style="width: 20px; height: 20px" alt="" />
						</span>
						<span class="datas">恒生指数</span>
					</div>
					<!-- <div class="bottom items">
		            <span  class="datas">2947.69</span>
		            <span class="ratio">+19.60</span>
		        </div> -->
				</div>
				<div class="data_item">
					<div class="top items">
						<span class="ratio"
							>{{ formattedMoney(finDataList.onshoreRmb, 4) }}
							<img v-if="finDataList.onshoreRmbTrend > 0" src="@/assets/rise.png" style="width: 20px; height: 20px" alt="" />
							<img v-else src="@/assets/decline.png" style="width: 20px; height: 20px" alt="" />
						</span>
						<span class="datas">在岸人民币</span>
					</div>
					<!-- <div class="bottom items">
		            <span  class="datas">2947.69</span>
		            <span class="ratio">+19.60</span>
		        </div> -->
				</div>
				<div class="data_item">
					<div class="top items">
						<span class="ratio"
							>{{ formattedMoney(finDataList.offshoreRmb, 4) }}
							<img v-if="finDataList.offshoreRmbTrend > 0" src="@/assets/rise.png" style="width: 20px; height: 20px" alt="" />
							<img v-else src="@/assets/decline.png" style="width: 20px; height: 20px" alt="" />
						</span>
						<span class="datas">离岸人民币</span>
					</div>
					<!-- <div class="bottom items">
		            <span  class="datas">2947.69</span>
		            <span class="ratio">+19.60</span>
		        </div> -->
				</div>
			</div>
			<!-- 折线图 -->
			<div class="echars_box">
				<div class="search_box">
					<div class="box_1">
						<div class="label">城市</div>
						<el-cascader placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }">
						</el-cascader>
					</div>
					<div class="box_1">
						<div class="label">资产类型</div>
						<el-select v-model="buildingTypesValue" placeholder="全部资产">
							<el-option v-for="item in buildingTypes" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</div>
					<div class="box_2">
						<el-button type="primary" @click="onSubmit()">查询</el-button>
					</div>
				</div>
				<!-- 折线图 -->
				<div class="echars_main">
					<div class="box_">
						<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
							<el-icon><Download /></el-icon>
						</div>
						<div style="width: 100%">
							<div class="title1">每平米市场租金/租金要价(可选择资产类型和地区)</div>
							<div id="myChart1" class="charts"></div>
						</div>
					</div>
					<div class="box_">
						<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
							<el-icon><Download /></el-icon>
						</div>
						<div style="width: 100%">
							<div class="title1">市场租金增长(可选择资产类型和地区)</div>
							<div id="myChart2" class="charts"></div>
						</div>
					</div>
					<div class="box_">
						<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
							<el-icon><Download /></el-icon>
						</div>
						<div style="width: 100%">
							<div class="title1">每平米市场售价/买入要价(可选择资产类型和地区)</div>
							<div id="myChart3" class="charts"></div>
						</div>
					</div>
					<div class="box_">
						<div class="download" @click="$utils.downloadEcharts($event.target.closest('.box_').children[1])">
							<el-icon><Download /></el-icon>
						</div>
						<div style="width: 100%">
							<div class="title1">市场售价增长(可选择资产类型和地区)</div>
							<div id="myChart4" class="charts"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
// 引入echarts
import * as echarts from 'echarts';
import { onBeforeMount, onMounted, reactive, ref, toRaw } from 'vue';
import { formattedMoney } from 'UTILS';
import { financialList, financeDataList } from '@/api/finance.js';
import { getFinanceIndex, getFinanceReportList, getDictList } from '@/api/syt.js';
const rate = ['S', 'A+', 'A', 'B+', 'B', 'C'];
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const buildingTypes = ref([]);
const data = ref([]);
const province = ref('');
const provinceList = ref([]);
const city = ref('');
const activeed = ref(0);
const counselor = ref('');
const buildingTypesValue = ref('');
const onChangeCity = (val, index) => {
	provinceList.value = val.children;
	city.value = val.label;
	console.log(province.value, 'province.value', city.value, 'city.value');
	console.log(val, 'val23567', index);
	activeed.value = index;
};
const handleChange = (val) => {
	console.log(val, 'ss');
	city.value = val[0];
	province.value = val[1];
};

// 默认选
const onChangeCounty = (val, index) => {
	console.log(val, 'val23567', index);
	activeed.value = index;
	province.value = val.label;
};
const activeIndex = ref('');
const onChangeProperty = (val, index) => {
	console.log(val, 'val23567', index);
	activeIndex.value = val;
	buildingTypesValue.value = val;
	console.log(buildingTypesValue.value, 'buildingTypesValue1');
};
onChangeProperty(buildingTypes.value[0], 0);
const onSubmit = () => {
	console.log(1243);
	const queryParams = {
		city: city.value,
		district: province.value,
		buildingType: buildingTypesValue.value,
		reportType: 0,
		year: new Date().getFullYear(),
	};
	for (let index = 0; index < 4; index++) {
		getData(queryParams, index);
	}
	console.log(queryParams, 'queryParams1243');
};
const params = ref({
	reportType: 0,
});
// getData( );
import http from '@/utils/http';
//   onBeforeMount(()=>{
//     getData()
// })

// onMounted(() => { // 需要获取到element,所以是onMounted的Hook
//    getData()
// });
// 获取数据
const echartsList = ref([]);
const data1 = ref([]);
const data2 = ref([]);
const data3 = ref([]);
const data4 = ref([]);
const newarr = ref([]);
let echarts1Max = ref(null);
let echarts2Max = ref(null);
let echarts3Max = ref(null);
let echarts4Max = ref(null);
let echarts1Min = ref(null);
let echarts2Min = ref(null);
let echarts3Min = ref(null);
let echarts4Min = ref(null);

let echartsTwelve1 = reactive([]);
let echartsTwelve2 = ref(null);
let echartsTwelve3 = ref(null);
let echartsTwelve4 = ref(null);
let myChart1;
let myChart2;
let myChart3;
let myChart4;

const getData = async (queryParams, reportType) => {
	try {
		// 发送请求
		const response = await getFinanceReportList({ ...queryParams, reportType: reportType });
		if (reportType == 0) {
			newarr.value = response.data.map((item) => item.financeValue);
			echartsTwelve1 = response.data.map((item) => item.twelveMonth);
			echarts1Max.value = newarr.value.reduce((a, b) => Math.max(a, b));
			echarts1Min.value = newarr.value.reduce((a, b) => Math.min(a, b));
		} else if (reportType == 1) {
			data2.value = response.data.map((item) => (item.financeValue * 100).toFixed(2));
			echartsTwelve2 = response.data.map((item) => item.twelveMonth);

			echarts2Max.value = data2.value.reduce((a, b) => Math.max(a, b));
			echarts2Min.value = data2.value.reduce((a, b) => Math.min(a, b));
		} else if (reportType == 2) {
			data3.value = response.data.map((item) => item.financeValue);
			echartsTwelve3 = response.data.map((item) => item.twelveMonth);
			echarts3Max.value = data3.value.reduce((a, b) => Math.max(a, b));
			echarts3Min.value = data3.value.reduce((a, b) => Math.min(a, b));
			console.log(data3.value, '[]');
		} else if (reportType == 3) {
			data4.value = response.data.map((item) => (item.financeValue * 100).toFixed(2));
			echartsTwelve4 = response.data.map((item) => item.twelveMonth);
			echarts4Max.value = data4.value.reduce((a, b) => Math.max(a, b));
			echarts4Min.value = data4.value.reduce((a, b) => Math.min(a, b));
			console.log(data4.value, '[]');
		}
		myChart1 = echarts.init(document.getElementById('myChart1'));
		myChart2 = echarts.init(document.getElementById('myChart2'));
		myChart3 = echarts.init(document.getElementById('myChart3'));
		myChart4 = echarts.init(document.getElementById('myChart4'));
		myChart1.resize({
			width: 600,
			height: 400,
		});
		myChart1.setOption({
			title: {
				text: '',
				left: 'center', // 文字说明的位置，默认为居中
				bottom: 10, // 距离底部的距离，单位为像素
				// backgroundColor: '#999',
				width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度

				textStyle: {
					// 文字样式
					color: '#000',
					fontSize: 18,
				},
			},
			grid: {
				// top: '12%',
				// bottom: '10%',
				// left: '6%',
				// right: '6%',
				// containLabel: true,
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: echartsTwelve1,
				axisLabel: {
					interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
				},
			},
			yAxis: {
				name: '元/㎡/天',
				nameTextStyle: {
					padding: [0, 40, 8, 0],
				},
				offset: 10,
				type: 'value',
				min: echarts1Min.value * 0.8,
				max: echarts1Max.value * 1.25,
				axisLabel: {
					formatter: function (value, index) {
						return value.toFixed(2);
					},
				},
			},

			series: [
				{
					data: newarr.value,
					type: 'line',
					itemStyle: { normal: { label: { show: true } } },
					// areaStyle: {},
				},
			],
		});
		// 2

		myChart2.resize({
			width: 600,
			height: 400,
		});
		myChart2.setOption({
			title: {
				text: '',
				left: 'center', // 文字说明的位置，默认为居中
				bottom: 10, // 距离底部的距离，单位为像素
				// backgroundColor: '#999',
				width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度

				textStyle: {
					// 文字样式
					color: '#000',
					fontSize: 18,
				},
			},
			grid: {
				// top: '12%',
				// bottom: '10%',
				// left: '6%',
				// right: '6%',
				// containLabel: true,
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: echartsTwelve2,
				axisLabel: {
					interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
				},
			},
			yAxis: {
				name: '%',
				nameTextStyle: {
					padding: [0, 40, 8, 0],
				},
				offset: 10,
				type: 'value',
				// min: echarts2Min.value * 0.8,
				// max: echarts2Max.value * 1.25,
				axisLabel: {
					formatter: function (value, index) {
						return value.toFixed(2);
					},
				},
			},
			series: [
				{
					data: data2.value,
					type: 'line',
					itemStyle: { normal: { label: { show: true } } },
				},
			],
		});
		myChart3.resize({
			width: 600,
			height: 400,
		});
		// 3
		myChart3.setOption({
			title: {
				text: '',
				left: 'center', // 文字说明的位置，默认为居中
				bottom: 10, // 距离底部的距离，单位为像素
				// backgroundColor: '#999',
				width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度

				textStyle: {
					// 文字样式
					color: '#000',
					fontSize: 18,
				},
			},
			grid: {
				// top: '12%',
				// bottom: '10%',
				// left: '6%',
				// right: '6%',
				// containLabel: true,
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: echartsTwelve3,
				axisLabel: {
					interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
				},
			},
			yAxis: {
				name: '万/㎡',
				nameTextStyle: {
					padding: [0, 40, 8, 0],
				},
				offset: 10,
				type: 'value',
				min: echarts3Min.value * 0.8,
				max: echarts3Max.value * 1.25,
				axisLabel: {
					formatter: function (value, index) {
						return value.toFixed(2);
					},
				},
			},
			series: [
				{
					data: data3.value,
					type: 'line',
					itemStyle: { normal: { label: { show: true } } },
					// areaStyle: {},
				},
			],
		});
		// 4
		myChart4.resize({
			width: 600,
			height: 400,
		});
		myChart4.setOption({
			title: {
				text: '',
				left: 'center', // 文字说明的位置，默认为居中
				bottom: 10, // 距离底部的距离，单位为像素
				// backgroundColor: '#999',
				width: '100%', // 标题的宽度，设置为100%可以使标题占满整个宽度
				textStyle: {
					// 文字样式
					color: '#000',
					fontSize: 18,
				},
			},
			grid: {
				// top: '12%',
				// bottom: '10%',
				// left: '4%',
				// right: '7%',
				// containLabel: true,
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: echartsTwelve4,
				axisLabel: {
					interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
				},
			},
			yAxis: {
				name: '%',
				nameTextStyle: {
					padding: [0, 40, 8, 0],
				},
				offset: 10,
				type: 'value',
				// min: echarts4Min.value,
				// max: echarts4Max.value * 1.25,
				axisLabel: {
					formatter: function (value, index) {
						return value.toFixed(2);
					},
				},
			},
			series: [
				{
					data: data4.value,
					type: 'line',
					itemStyle: { normal: { label: { show: true } } },
					// areaStyle: {},
				},
			],
		});
	} catch (error) {
		console.error('请求失败', error);
	}
};
// onMounted(() => {
//     getData()
// }
// )
const finDataList = ref([]);
const getfinData = async () => {
	await getFinanceIndex()
		.then((res) => {
			finDataList.value = res.data;
			console.log(res, 'shuju111');
		})
		.catch((err) => {
			console.log(err);
		});
};
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
			console.log(res, 'shuju111');
		})
		.catch((err) => {
			console.log(err);
		});
};
getfinData();
getDict();
</script>

<style scoped lang="less">
.content_box {
	width: 100%;
	height: 100%;
	min-height: 100vh;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;

		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		color: #1a1a1a;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;

		.data_box {
			width: 100%;
			height: 104px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.data_item {
				width: 16%;
				height: 104px;
				border-radius: 6px;
				background: rgb(250, 250, 250);
				box-sizing: border-box;
				display: flex;
				justify-content: center;
				align-items: center;
				box-shadow: 1px 1px 15px rgba(0, 0, 0, 0.05);

				.items {
					width: 100%;

					.ratio {
						width: 100%;
						text-align: center;
						display: block;
						color: rgba(24, 104, 241, 1);
						font-size: 26px;
						font-weight: 600;
						margin-bottom: 15px;
					}

					.datas {
						width: 100%;
						text-align: center;
						display: block;
						font-size: 12px;
					}
				}
			}
		}
		.echars_box {
			width: 100%;
			min-height: 522px;
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			padding: 15px 0;
			box-sizing: border-box;
			border-radius: 6px;
			position: relative;
			.search_box {
				width: 100%;
				overflow: hidden;
				padding: 0 15px;
				box-sizing: border-box;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.box_1 {
					width: 330px;
					height: 32px;
					margin: 10px 5px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					border: 1px solid rgba(231, 231, 231, 1);
					box-sizing: border-box;

					::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
						box-shadow: 0;
					}

					.label {
						width: 50%;
						height: 100%;
						font-size: 14px;
						color: rgba(134, 144, 156, 1);
						background-color: rgba(245, 246, 247, 1);
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
				.box_2 {
					width: 230px;
					height: 32px;
					margin: 10px 5px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					box-sizing: border-box;
				}
			}
			.echars_main {
				width: 98%;
				height: 100%;
				margin: 0 auto;
				padding-bottom: 15px;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex-wrap: wrap;
				.box_ {
					width: 49.5%;
					height: 442px;
					margin-bottom: 15px;
					border: 1px solid rgba(231, 231, 231, 1);
					border-radius: 6px;
					box-sizing: border-box;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					flex-direction: column;
					position: relative;
					.download {
						position: absolute;
						top: 10px;
						right: 15px;
						cursor: pointer;
						color: #333333;
						font-size: 20px;
						font-weight: 600;
						&:hover {
							color: #1868f1;
						}
					}
					.title1 {
						width: 100%;
						height: 44px;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 12px;
						background-color: rgba(245, 245, 245, 1);
						border-bottom: 1px solid rgba(231, 231, 231, 1);
					}
				}
			}
		}
	}
}

.echarts_box {
	width: 100%;
	display: grid;
	grid-template-columns: repeat(2, 1fr); // 定义三列的布局。
	grid-gap: 50px; // 根据实际需求调整。
}

.charts {
	height: calc(100% - 44px);
	> :nth-child(1) {
		width: 100% !important;
		> :nth-child(1) {
			width: 100% !important;
		}
	}
}

.search {
	width: 100%;
	height: 285px;
	background: rgb(250, 250, 250);
	padding: 30px 0;
	position: relative;

	button {
		width: 85px;
		height: 36px;
		border-radius: 2px;
		background: rgb(56, 96, 154);
		color: rgb(255, 255, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 21px;
		letter-spacing: 0px;
		// text-align: center;
		position: absolute;
		right: 20px;
		border: none;
	}

	.active {
		box-sizing: border-box;
		border: 1px solid rgb(64, 158, 255);
		border-radius: 2px;

		background: rgb(241, 248, 255);
		color: rgb(64, 158, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 23px;
		letter-spacing: 0px;
		text-align: left;
	}

	.area_box {
		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}

		.city {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin: 15px 0;
			margin-bottom: 15px;

			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}

		.county {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin: 15px 0;

			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}

	.property_box {
		margin-top: 15px;

		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}

		.property {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;

			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}

	.type_box {
		margin-top: 15px;

		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}

		.type {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;

			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}
}
</style>
