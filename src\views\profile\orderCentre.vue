<template>
	<div class="orderCenter">
		<el-form :model="form" class="card_search_content">
			<el-form-item>
				<el-select style="width: 130px" v-model="form.status" placeholder="全部状态" clearable @change="handleSearch">
					<el-option label="待付款" value="PENDING" />
					<el-option label="已完成" value="PAID" />
					<el-option label="已取消" value="CANCELLED" />
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-dropdown trigger="click">
					<span class="el-dropdown-link" style="cursor: pointer">
						{{ data.selectedValue }}<el-icon><CaretBottom /></el-icon>
					</span>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item @click="handleItemClick(1, '筛选创建时间')">筛选创建时间</el-dropdown-item>
							<el-dropdown-item @click="handleItemClick(1, '筛选付款时间')">筛选付款时间</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>

				<div :style="{ display: data.selectedValue === '筛选创建时间' ? 'block' : 'none' }">
					<el-date-picker
						v-model="data.createDaterange"
						type="datetimerange"
						range-separator="-"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						value-format="YYYY-MM-DD HH:mm:ss"
						format="YYYY-MM-DD HH:mm:ss"
						@change="handleDaterange($event, 1)"
						:default-time="defaultTime"
						style="width: 336px; flex: none; margin-left: 18px"
					/>
				</div>
				<div :style="{ display: data.selectedValue === '筛选付款时间' ? 'block' : 'none' }">
					<el-date-picker
						v-model="data.payDaterange"
						type="datetimerange"
						range-separator="-"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						value-format="YYYY-MM-DD HH:mm:ss"
						format="YYYY-MM-DD HH:mm:ss"
						@change="handleDaterange($event, 2)"
						:default-time="defaultTime"
						style="width: 336px; flex: none; margin-left: 18px"
					/>
				</div>
			</el-form-item>

			<el-form-item>
				<el-dropdown trigger="click">
					<span class="el-dropdown-link" style="cursor: pointer">
						{{ data.searchValue }}<el-icon><CaretBottom /></el-icon>
					</span>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item @click="handleItemClick(2, '搜索订单编号')">搜索订单编号</el-dropdown-item>
							<el-dropdown-item @click="handleItemClick(2, '搜索商品名称')">搜索商品名称</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>

				<el-input
					v-show="data.searchValue === '搜索订单编号'"
					v-model="form.outTradeNo"
					style="max-width: 160px; margin-left: 18px"
					clearable
					placeholder="搜索"
					@input="handleInput"
					class="input_select"
				>
					<template #append
						><el-icon><Search @click="handleSearch" /></el-icon>
					</template>
				</el-input>

				<el-input
					v-show="data.searchValue !== '搜索订单编号'"
					v-model="form.name"
					@input="handleInput"
					style="max-width: 160px; margin-left: 18px"
					placeholder="搜索"
					clearable
					class="input_select"
				>
					<template #append
						><el-icon><Search @click="handleSearch" /></el-icon>
					</template>
				</el-input>
			</el-form-item>
		</el-form>
		<div class="container_banner">
			<el-table :data="data.tableData" border stripe height="620" header-row-class-name="tableHeader">
				<el-table-column type="index" label="序号" width="60" />
				<el-table-column property="outTradeNo" label="订单编号" min-width="90" show-overflow-tooltip />
				<el-table-column property="name" label="商品名称" width="300" show-overflow-tooltip />
				<el-table-column property="orderType" label="商品类型" width="150" show-overflow-tooltip />
				<el-table-column property="payableAmount" label="应付金额" width="120" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row.payableAmount + '元' }}
					</template>
				</el-table-column>
				<el-table-column property="statusName" label="订单状态" width="120" show-overflow-tooltip />
				<!-- <el-table-column property="createdTime" label="创建时间" width="160" show-overflow-tooltip /> -->
				<!-- <el-table-column property="paymentTime" label="付款时间" width="160" show-overflow-tooltip /> -->
				<el-table-column fixed="right" label="操作" width="170" align="center">
					<template #default="scope">
						<el-button type="text" size="small" v-if="scope.row.status === 'PENDING'" @click.prevent="handlePay(scope.row)">继续付款</el-button>
						<!-- <el-button type="text" size="small" @click.prevent="hanldeRowInvoicing(scope.row)">开具发票</el-button> -->
						<el-button type="text" style="margin-left: 0 !important" size="small" @click.prevent="hanldeDetails(scope.row)">订单详情</el-button>
					</template>
				</el-table-column>
			</el-table>

			<el-config-provider :locale="customPagination">
				<el-pagination
					layout="total, prev, pager, next,jumper"
					:pager-count="3"
					background
					v-model:current-page="data.page.current"
					v-model:page-size="data.page.size"
					:total="total"
					@change="handlepageChange"
					style="justify-content: right"
				>
				</el-pagination>
			</el-config-provider>
		</div>

		<el-dialog
			v-model="dialogVisible"
			:close-on-click-modal="false"
			:show-close="false"
			align-center="center"
			:before-close="handleClose"
			style="width: 560px"
		>
			<template #header>
				<div class="dialogHeader">
					<div class="dialogHeaderLeft">
						<div>订单详情</div>
					</div>

					<div class="dialogHeaderRight">
						<el-icon><CloseBold @click="handleClose" /></el-icon>
					</div>
				</div>
			</template>
			<div class="form_content">
				<div class="content">
					<div class="title">商品信息</div>
					<div class="content_fc" :style="{ overflow: data.selectList.length > 4 ? 'scroll' : 'hidden' }">
						<div class="content_banner" v-for="(items, indexs) in data.selectList" :key="indexs">
							<div class="left_Img">
								<img src="../../assets/img_ins.png" alt="" />
							</div>
							<div class="right_content">
								<div class="top_content">
									<div>{{ items.description }}</div>
									<div>
										<span>￥</span>
										{{ items.totalAmount }}
									</div>
								</div>
								<div class="bottom_c">
									<div class="title_c">
										<span>权益套餐</span>
										<div class="bottom_cs" v-for="(item, index) in data.list" :key="index">
											<div class="line"></div>
											<div class="name_c">
												{{ item.key
												}}{{
													items[item.value]
														? item.value === 'quantity'
															? items.period === 'YEAR'
																? items[item.value] + '年'
																: items[item.value] + '月'
															: items[item.value]
														: ''
												}}
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="content_bottom">
					<div class="title">订单信息</div>

					<div v-for="(item, index) in data.listDetails" :key="index" class="content_f">
						<div class="content_name">{{ item.key }}</div>
						<div class="content_value" :class="item.status ? 'content_valueActive' : ''">
							{{ item.company ? '￥' : '' }}{{ item.company ? data.selectRow[item.value] || 0 : data.selectRow[item.value] }}
						</div>
					</div>
				</div>
			</div>
			<div class="contentk"></div>
			<template #footer>
				<div class="dialog_footer">
					<div>
						<el-button v-if="continueObj.status === 'PENDING'" @click.prevent="handlePay(continueObj)">继续付款</el-button>
						<el-button @click="hanldeInvoicing" v-if="objActives.id && data.selectRow.status === 'PAID'">开票详情</el-button>
						<el-button @click="hanldeRowInvoicing" v-if="!objActives?.id && data.selectRow.status === 'PAID'">开具发票</el-button>

						<!-- <el-button type="primary" color="#1868F1" @click="handleDetermine"> 立即开通 </el-button> -->
					</div>
				</div>
			</template>
		</el-dialog>
		<detailst
			:dialogVisible="IndialogVisible"
			:objActives="objActives"
			:activeObj="data.selectList[0]"
			@handleDialogClose="handleDialogClose"
		></detailst>
		<invoiceRow
			:activeObj="data.selectRow"
			@handleInvoice="handleInvoice"
			:dialogInvoiceRowVisible="dialogInvoiceRowVisible"
			@handleInvoiceClose="handleInvoiceClose"
		></invoiceRow>
		<manageInvoices
			:activeObj="data.selectRow"
			:dialogInvoiceRowVisible="dialogManageVisible"
			@handleInvoiceClose="handleManageClose"
		></manageInvoices>
		<addHeaders
			:activeObj="data.selectRow"
			:headerEdit="headerEdit"
			:headerEditId="headerEditId"
			:dialogInvoiceRowVisible="dialogaddVisible"
			@handleInvoiceClose="handleAddClose"
		></addHeaders>

		<el-dialog
			v-model="dialogVisibleds"
			:close-on-click-modal="false"
			:destroy-on-close="true"
			:show-close="false"
			align-center="center"
			:before-close="handleCloseds"
			style="width: 800px"
		>
			<template #header>
				<div class="dialogHeader">
					<div class="dialogHeaderLeft">
						<div>继续付款</div>
					</div>

					<div class="dialogHeaderRight">
						<el-icon><CloseBold @click="handleCloseds" /></el-icon>
					</div>
				</div>
			</template>

			<el-dialog v-model="showShangYutong" fullscreen>
				<shangYutong @handleReturn="handleReturn"></shangYutong>
			</el-dialog>
			<div v-if="successType" class="form_contents">
				<div class="content_box_left">
					<el-skeleton-item variant="image" v-if="!paymentStatus" style="width: 100%; height: 100%; background: #fff" />
					<iframe
						:src="paymentURL"
						v-show="paymentStatus === 'ALI_PC' && checkbox"
						frameborder="no"
						border="0"
						marginwidth="0"
						marginheight="0"
						scrolling="no"
						width="200"
						height="200"
						style="overflow: hidden; transform: scale(0.6); margin: -20px 0 0 -40px; transform-origin: 100px 50px; /* 确保从左上角开始缩放 */"
					>
					</iframe>
					<canvas v-show="paymentStatus === 'WX_NATIVE' && checkbox > 0" ref="qrcodeCanvas" class="qrcode"></canvas>
				</div>
				<div class="content_box_right">
					<el-checkbox value="Agree" v-model="checkbox" name="type" class="check_boxAgreement"
						>我已阅读并同意<span class="blueSpan" @click="showShangYutong = true">《商宇通权益订阅服务协议 》</span>
					</el-checkbox>

					<div class="content_box_price">
						<div>扫码支付</div>
						<div>¥</div>
						<div>{{ continueObj.payableAmount }}</div>
					</div>

					<div class="content_box_bottom">
						<el-radio-group v-model="paymentStatus" @change="handlePayment">
							<el-radio value="ALI_PC" size="large">支付宝</el-radio>
							<el-radio value="WX_NATIVE" size="large">微信</el-radio>
						</el-radio-group>
					</div>
				</div>
			</div>

			<div v-if="!successType" class="prosperity">
				<div class="prosperity-box">
					<img src="@/assets/prosperity.png" alt="" />
					<div class="title">购买成功</div>
					<div class="prompt-content">
						<div>您可以在“个人中心-权益中心-我的权益卡券”查看及使用</div>
					</div>
					<el-button type="primary" @click="RightsCentre">去权益中心使用</el-button>
				</div>
				<div class="prosperityCoucher" v-if="couponDetail.couponsType">
					<div class="titleDetails">购买卡券得优惠好礼（已发放至个人中心-权益中心-福利卡券）</div>
					<sm-coucher :itemCoupons="couponDetail" @handleWelfareAddCrad="handleWelfareAddCrad"></sm-coucher>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup>
import detailst from './Invoice/details.vue';
import invoiceRow from './Invoice/invoiceRow.vue';
import manageInvoices from './Invoice/manageInvoices.vue';
import addHeaders from './Invoice/addHeader.vue';
import shangYutong from '../equityServices/shangYutong.vue';
import { ref, onMounted, reactive } from 'vue';
import { getOrderPage, getOrderDetail, getInvoiceRecord, continuePay, getCouponDetail } from '@/api/equityTerm.js';
import { orderStatus } from '@/api/rights';
import { ElMessage } from 'element-plus';
import QRCode from 'qrcode';
import smCoucher from '../../component/smCoucher/index.vue';
import { useRouter } from 'vue-router';
const router = useRouter(); // 路由
const IndialogVisible = ref(false);
const dialogInvoiceRowVisible = ref(false);
const dialogaddVisible = ref(false);
const dialogManageVisible = ref(false);
const defaultTime = [new Date(), new Date()];
const headerEdit = ref(null);
const headerEditId = ref(null);
const continueObj = ref({}); // 继续付款
const dialogVisibleds = ref(false); // 弹窗
const showShangYutong = ref(false); // 订阅服务
const paymentStatus = ref(''); // 支付方式
const paymentURL = ref(''); // 支付链接
const successType = ref(true); // 成功类型
const checkbox = ref(false); // 同意协议
const qrcodeCanvas = ref(); // 微信二维码
const couponDetail = ref({}); // 优惠卷详情
let timerId = ref(null); // 订单轮询定时器
const orderId = ref(''); // 订单号
const form = reactive({
	status: null, // 订单状态
	outTradeNo: null,
	name: null,
	paymentTimeBegin: null,
	paymentTimeEnd: null,
	createdTimeBegin: null,
	createdTimeEnd: null,
});

let total = ref(0);

let data = reactive({
	selectRow: {},
	selectList: [],
	tableData: [],
	list: [
		{
			key: '城市：',
			value: 'cityName',
		},
		{
			key: '团队人数：',
			value: 'team',
		},
		{
			key: '时长：',
			value: 'quantity',
		},
		{
			key: '单价：',
			value: 'price',
		},
		{
			key: '商品数量：',
			value: 'orderCount',
		},
	],

	listDetails: [
		{
			key: '订单编号',
			value: 'outTradeNo',
		},
		{
			key: '创建时间',
			value: 'createdTime',
		},
		{
			key: '付款时间',
			value: 'paymentTime',
		},
		{
			key: '商品总价',
			value: 'totalAmount',
			company: '1',
		},
		{
			key: '折扣优惠',
			value: 'discountAmount',
			company: '1',
		},
		{
			key: '实付金额',
			value: 'payableAmount',
			company: '1',
			status: '1',
		},
	],
	createDaterange: [], // 创建时间
	payDaterange: [], // 创建时间
	selectedValue: '筛选创建时间', // 搜索时间展示状态
	searchValue: '搜索订单编号', // 搜索订单、商品名称展示状态
	page: {
		current: 1,
		size: 10,
	},
});

let customPagination = reactive({
	el: {
		pagination: {
			pagesize: '页',
			total: `共 ${total.value} 条`,
			goto: '跳至',
			pageClassifier: '',
		},
	},
});
const dialogVisible = ref(false);

const objActives = ref({});

onMounted(() => {
	// 获取订单
	handleUsersOrder();
});

function handleReturn() {
	// 关闭对话框
	showShangYutong.value = false;
}

// 权益中心
const RightsCentre = () => {
	clearInterval(timerId.value);
	router.push({
		path: '/profile/browsingHistory',
	});
};

// 跳转福利卡劵
function handleWelfareAddCrad() {
	router.push({
		path: '/profile/browsingHistory',
		query: { type: 'third' },
	});
}

// 支付
function handlePayment() {
	handlecontinuePay();
}
// 继续支付
function handlecontinuePay() {
	continuePay({ outTradeNo: continueObj.value.outTradeNo, payType: paymentStatus.value }).then((res) => {
		if (res.code === 200) {
			let data = res.data;
			if (paymentStatus.value === 'ALI_PC') {
				// 支付宝
				paymentURL.value = data.url;
			} else {
				// 微信
				const qrCodeDiv = qrcodeCanvas.value;
				QRCode.toCanvas(qrCodeDiv, data.url, (error) => {
					if (error) console.error(error);
				});
			}

			if (data.outTradeNo) {
				orderId.value = data.outTradeNo;
				// 开始轮询
				timerId.value = setInterval(() => {
					pollOrderStatus(orderId.value);
				}, 1500);
			}
		}
	});
}

// 订单轮询获取支付状态
const pollOrderStatus = async (orderId) => {
	if (paymentStatus.value === '' || !checkbox.value) {
		return;
	}
	try {
		const response = await orderStatus(orderId);
		if (orderId === '') {
			return; // 提前返回，不执行后续代码
		}
		//PENDING("待支付"),        // 待支付
		// PAID("已支付"),          // 已支付
		// FAILED("支付失败"),      // 支付失败
		// CANCELLED("已取消"),     // 已取消
		// REFUNDED("已退款");      // 已退款
		if (response.code == 200) {
			// 根据返回的状态更新状态提示信息
			switch (response.data) {
				case 'PAID':
					ElMessage({
						message: `支付成功`,
						type: 'success',
					});
					clearInterval(timerId.value);
					//根据订单id获取优惠卷详情
					handleCouponDetail(orderId);
					successType.value = false;
					break;
				case 'FAILED':
					ElMessage({
						message: `支付失败`,
						type: 'error',
					});
					clearInterval(timerId.value);
					break;
				case 'CANCELLED':
					ElMessage({
						message: `订单已取消`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				case 'REFUNDED':
					ElMessage({
						message: `已退款`,
						type: 'warning',
					});
					clearInterval(timerId.value);
					break;
				default:
					break;
			}
		} else {
			clearInterval(timerId.value);
		}
	} catch (error) {
		clearInterval(timerId.value);
	}
};

//根据订单id获取优惠卷详情
const handleCouponDetail = (orderId) => {
	getCouponDetail({ outTradeNo: orderId }).then((res) => {
		if (res.code === 200 && res.data) {
			couponDetail.value = res.data;
			couponDetail.value.couponsType = '1';
		}
	});
};

// 关闭对话框
const handleCloseds = () => {
	dialogVisible.value = false;
	paymentStatus.value = ''; // 支付状态清楚
	// 清楚checkBox
	checkbox.value = false;
	dialogVisibleds.value = false;
	//清楚定时器
	clearInterval(timerId.value);
};

function hanldeInvoicing() {
	IndialogVisible.value = true;
}

// 获取发票记录
function handleGetInvoiceRecord() {
	getInvoiceRecord({ orderId: data.selectList[0].outTradeNo }).then((res) => {
		if (res.code === 200) {
			objActives.value = res.result;
		}
	});
}

function handleDialogClose(param) {
	IndialogVisible.value = false;
	if (param) {
		dialogVisible.value = false;
	}
}
// 搜索条件
function handleItemClick(type, value) {
	if (type === 1) {
		data.selectedValue = value;
		if (value === '筛选创建时间') {
			data.payDaterange = [];
			form.paymentTimeBegin = null;
			form.paymentTimeEnd = null;
		} else {
			data.createDaterange = [];
			form.createdTimeBegin = null;
			form.createdTimeEnd = null;
		}
	} else {
		data.searchValue = value;
		if (value === '搜索商品名称') {
			form.outTradeNo = '';
		} else {
			form.name = '';
		}
	}
	handleUsersOrder();
}

// 获取时间
function handleDaterange(data, type) {
	if (type === 1) {
		form.createdTimeBegin = data?.[0] || '';
		form.createdTimeEnd = data?.[1] || '';

		form.paymentTimeBegin = null;
		form.paymentTimeEnd = null;
	} else {
		form.paymentTimeBegin = data?.[0] || '';
		form.paymentTimeEnd = data?.[1] || '';

		form.createdTimeBegin = null;
		form.createdTimeEnd = null;
	}
	// 列表
	handleUsersOrder();
}

/**
 * @function handleInput 输入框清空时执行方法
 * @param e 输入框value
 */
function handleInput(e) {
	if (!e) {
		handleUsersOrder();
	}
}

// 关闭对话框
function handleClose() {
	objActives.value = {};
	dialogVisible.value = false;
}
// 继续支付
function handlePay(row) {
	continueObj.value = row;
	dialogVisibleds.value = true;
	console.log(row, 'row');
}

/**
 * @function hanldeDetails 订单详情
 * @param row
 */
function hanldeDetails(row) {
	continueObj.value = row;
	getOrderDetail(row).then((res) => {
		if (res.code === 200) {
			dialogVisible.value = true;
			data.selectList = res.data;
			data.selectRow = row;
			handleGetInvoiceRecord();
			res.data.forEach((element) => {
				element['paymentTime'] = row.paymentTime;
			});
		}
	});
}
// 开具发票
function hanldeRowInvoicing(row) {
	// data.selectRow = row;
	dialogInvoiceRowVisible.value = true;
}

// 关闭对话框
function handleInvoiceClose() {
	dialogInvoiceRowVisible.value = false;
}
// 添加抬头
function handleAddClose() {
	dialogaddVisible.value = false;
	dialogManageVisible.value = true;
	headerEditId.value = '';
}

// 管理抬头关闭
function handleManageClose(param) {
	console.log(param, 'param');
	if (param.type) {
		headerEdit.value = param.type === 1 ? '新增' : '编辑';
		headerEditId.value = param.id;
		dialogaddVisible.value = true;
	}
	dialogManageVisible.value = false;
}
// 管理抬头
function handleInvoice() {
	dialogManageVisible.value = true;
}
/**
 * @function handleUsersOrder 获取订单
 */
function handleUsersOrder() {
	getOrderPage({ ...form, ...data.page }).then((res) => {
		if (res.code === 200) {
			data.tableData = res.data.rows;
			total.value = res.data.total;
			customPagination.el.pagination.total = `共 ${total.value} 条`;
		}
	});
}

// current-page 或 page-size 更改时触发
function handlepageChange(currentPage, pageSize) {
	data.page.current = currentPage;
	handleUsersOrder();
}

// 搜索区监听
function handleSearch() {
	handleUsersOrder();
}
</script>

<style lang="scss" scoped>
.orderCenter {
	width: 100%;
	height: calc(100vh - 120px);
	border-radius: 6px;
	background: #ffffff;
}

.card_search_content {
	display: flex;
	padding: 24px 32px 6px 32px;
}

.card_search_content > :nth-child(n) {
	margin-right: 20px;
}

::v-deep .input_select .el-input-group__append {
	background: #fff;
	padding: 0 9px;
}

.container_banner {
	margin: 0 32px;
	width: calc(100% - 64px);
	height: 100%;
	margin-bottom: 10px;
	text-align: center;

	&::v-deep .el-table--fit {
		border-radius: 8px;
	}

	&::v-deep .el-table th {
		background-color: rgba(245, 245, 245, 1);
	}
}

::v-deep .el-dialog {
	padding: 0;
}

.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	color: rgba(29, 33, 41, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	.el-icon {
		cursor: pointer;
	}
}

.form_content {
	padding: 0 16px;
	.content {
		//height: 120px;
		.title {
			//styleName: 商宇通/文字14B;
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			height: 22px;
			margin-bottom: 10px;
		}
		.content_fc {
			max-height: 292px;
			overflow: scroll;
		}
	}

	.content_banner {
		width: calc(100% - 24px);
		height: 64px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		background: #f5f6f7;
		border-radius: 6px;
		padding: 0 12px;
		margin-bottom: 10px;
		.left_Img {
			margin-right: 10px;
			img {
				width: 44px;
				height: 44px;
			}
		}
		.right_content {
			height: 44px;
			width: 100%;
			.top_content {
				height: 22px;
				width: 100%;
				display: flex;
				justify-content: space-between;
				//styleName: 商宇通/文字14M;
				font-size: 14px;
				font-weight: 500;
				line-height: 22px;
				text-align: center;
				color: #1d2129;
				margin-bottom: 4px;
				span {
					font-size: 10px;
					font-weight: 400;
					line-height: 18px;
				}
			}

			.bottom_c {
				height: 18px;
				.title_c {
					display: flex;
					span {
						font-size: 11px;
						font-weight: 400;
						line-height: 18px;
					}
					.bottom_cs {
						display: flex;
						align-items: center;
						.line {
							width: 1px;
							height: 12px;
							background: #e7e7e7;
							margin: 0 8px;
						}
						.name_c {
							font-size: 11px;
							font-weight: 400;
							line-height: 18px;
							color: #4e5969;
						}
					}
				}
			}
		}
	}

	.content_bottom {
		height: 262px;
		.title {
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			height: 22px;
			margin-bottom: 10px;
		}

		.content_f {
			display: flex;
			justify-content: flex-start;
			height: 20px;
			margin-top: 16px;
			div {
				font-size: 12px;
				font-weight: 400;
				line-height: 20px;
			}
			.content_name {
				color: #1d2129;
				margin-right: 80px;
			}

			.content_value {
				color: #4e5969;
			}
			.content_valueActive {
				font-size: 16px;
				font-weight: 500;
				color: #1868f1;
			}
		}
	}
}

.dialog_footer {
	height: 54px;
	width: calc(100% - 16px);
	border-top: 1px solid rgba(231, 231, 231, 1);
	display: flex;
	align-items: center;
	justify-content: end;
	padding-right: 16px;
}

.form_contents {
	margin: -2px 32px 16px 32px;
	width: calc(100% - 64px);
	height: 152px;
	display: flex;
	justify-content: space-between;
	background: #f5f6f7;
	border-radius: 4px;
	.content_box_left {
		width: 120px;
		margin: 16px;
	}
	.content_box_right {
		margin: 16px 0;
		width: calc(100% - 152px);
		.check_boxAgreement {
			height: 22px;
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			margin-bottom: 34px;
		}
		.blueSpan {
			font-size: 14px;
			font-weight: 700;
			line-height: 22px;
			color: rgba(24, 104, 241, 1);
		}

		.content_box_price {
			display: flex;
			height: 24px;
			margin-bottom: 10px;
			& > :nth-child(1) {
				font-size: 16px;
				font-weight: 700;
				line-height: 27px;
				color: #1d2129;
			}
			& > :nth-child(2) {
				font-size: 16px;
				font-weight: 700;
				line-height: 29px;
				color: #1868f1;
				margin: 0 8px;
			}
			& > :nth-child(3) {
				font-size: 28px;
				font-weight: 500;
				line-height: 24px;
				color: #1868f1;
				margin-bottom: -2px;
			}
		}

		.content_box_bottom {
			height: 22px;
			.el-radio-group {
				height: 24px;
				& > :nth-child(n) {
					height: 32px;
					margin-right: 10px;
				}
			}
		}
	}
}

.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	border-bottom: 1px solid #e7e7e7;
}
.dialogHeaders {
	border-bottom: none !important;
	font-weight: 700 !important;
}
.dialogHeaderLeft {
	display: flex;
	align-items: center;
	color: #1d2129;
	font-size: 16px;
	font-weight: 700;
}
.dialogHeaderRight > :nth-child(1) {
	cursor: pointer;
	margin: 19px 0px 0 0;
}

.prosperity {
	width: 100%;

	.prosperity-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40px 40px 14px 40px;

		img {
			width: 50px;
			height: 50px;
		}

		.title {
			font-weight: 700;
			font-size: 20px;
			margin-top: 8px;
			margin-bottom: 16px;
			line-height: 32px;
			height: 32px;
			color: #1d2129;
		}

		.prompt-content {
			height: 36px;
			display: flex;
			flex-direction: column;
			align-items: center;
			line-height: 22px;

			div {
				// margin: 5px 0;
				color: #4e5969;
				font-size: 14px;
			}
		}

		.el-button {
			margin-top: 18px;
			padding: 0 40px;
			width: 306px;
			height: 48px;
			background: #1868f1;
		}
	}
}

.qrcode {
	height: 136px !important;
	margin: -8px 0px 0 -8px;
	width: 136px !important;
}

::v-deep .el-checkbox {
	--el-checkbox-checked-bg-color: #1868f1;
	.el-checkbox__input.is-checked .el-checkbox__inner {
		border-color: #1868f1;
	}
	.el-checkbox__input.is-indeterminate .el-checkbox__inner {
		border-color: #1868f1;
	}
}

.prosperityCoucher {
	width: 100%;
	height: 134px;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-top: 1px solid #e7e7e7;
	margin-bottom: 16px;
	.titleDetails {
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: center;
		color: #86909c;
		margin: 12px 0 10px 0;
	}
}
</style>
