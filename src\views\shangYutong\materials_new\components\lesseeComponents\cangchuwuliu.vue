<template>
	<div class="cangchu_wrap">
		<template v-if="allData && allData.length > 1">
			<div class="double_box_wrap" v-for="(item, index) in allData">
				<div class="single_wrap">
					<div class="title1">
						<div class="title">基础设施</div>
					</div>
					<div class="content_wrap">
						<div class="features">
							<div class="features_Item">
								<img v-if="item.platform == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/yuetai.png" />
								<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/yuetai_close.png" />
								<div class="item_content">
									<div class="item_content_l">
										<img v-if="item.platform == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
										<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
									</div>
									<div class="item_content_text" :style="{ color: item.platform == 1 ? '#1D2129' : '#86909C' }">月台</div>
								</div>
							</div>
							<div class="features_Item">
								<img v-if="item.goodsLift == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/huoti.png" />
								<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/huoti_close.png" />
								<div class="item_content">
									<div class="item_content_l">
										<img v-if="item.goodsLift == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
										<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
									</div>
									<div class="item_content_text" :style="{ color: item.goodsLift == 1 ? '#1D2129' : '#86909C' }">货梯</div>
								</div>
							</div>
							<div class="features_Item">
								<img v-if="item.ramp == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/podao.png" />
								<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/podao_close.png" />
								<div class="item_content">
									<div class="item_content_l">
										<img v-if="item.ramp == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
										<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
									</div>
									<div class="item_content_text" :style="{ color: item.ramp == 1 ? '#1D2129' : '#86909C' }">坡道</div>
								</div>
							</div>
							<div class="features_Item">
								<img v-if="item.shelter == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/yupeng.png" />
								<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/yupeng_close.png" />
								<div class="item_content">
									<div class="item_content_l">
										<img v-if="item.shelter == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
										<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
									</div>
									<div class="item_content_text" :style="{ color: item.shelter == 1 ? '#1D2129' : '#86909C' }">雨棚</div>
								</div>
							</div>
							<div class="features_Item">
								<img v-if="item.rainmaker == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/penlin.png" />
								<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/penlin_close.png" />
								<div class="item_content">
									<div class="item_content_l">
										<img v-if="item.rainmaker == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
										<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
									</div>
									<div class="item_content_text" :style="{ color: item.rainmaker == 1 ? '#1D2129' : '#86909C' }">喷淋设备</div>
								</div>
							</div>
							<div class="features_Item">
								<img v-if="item.spareMotor == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/dianji.png" />
								<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/dianji_close.png" />
								<div class="item_content">
									<div class="item_content_l">
										<img v-if="item.spareMotor == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
										<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
									</div>
									<div class="item_content_text" :style="{ color: item.spareMotor == 1 ? '#1D2129' : '#86909C' }">备用电机</div>
								</div>
							</div>
							<div class="features_Item">
								<img v-if="item.dehumidifier == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/chushi.png" />
								<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/chushi_close.png" />
								<div class="item_content">
									<div class="item_content_l">
										<img v-if="item.dehumidifier == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
										<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
									</div>
									<div class="item_content_text" :style="{ color: item.dehumidifier == 1 ? '#1D2129' : '#86909C' }">除湿装备</div>
								</div>
							</div>
							<!-- <div class="features_Item">
								<img v-if="item.fireFightingDevice == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/xiaofang.png" />
								<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/xiaofang_close.png" />
								<div class="item_content">
									<div class="item_content_l">
										<img v-if="item.fireFightingDevice == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
										<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
									</div>
									<div class="item_content_text" :style="{ color: item.fireFightingDevice == 1 ? '#1D2129' : '#86909C' }">消防设施</div>
								</div>
							</div> -->
						</div>
					</div>
					<!-- <div class="empty_wrap">
						<img :src="empty" />
						<div>暂无数据</div>
					</div> -->
				</div>
				<div class="single_wrap">
					<div class="title1">
						<div class="title">仓储详情</div>
					</div>
					<div class="content_wrap" v-if="index == 0">
						<div class="content_flex">
							<div class="content_c" v-for="(item, index1) in leftList" :key="index1">
								<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
									<div class="content_left">{{ childitem.name }}</div>
									<div class="content_right">{{ childitem.value }}</div>
								</div>
							</div>
						</div>
					</div>
					<div class="content_wrap" v-if="index == 1">
						<div class="content_flex">
							<div class="content_c" v-for="(item, index1) in rightList" :key="index1">
								<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
									<div class="content_left">{{ childitem.name }}</div>
									<div class="content_right">{{ childitem.value }}</div>
								</div>
							</div>
						</div>
					</div>
					<!-- <div class="empty_wrap">
						<img :src="empty" />
						<div>暂无数据</div>
					</div> -->
				</div>
			</div>
		</template>
		<template v-else>
			<div class="box_wrap" v-if="!$utils.isEmpty(leftData)">
				<div class="left">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">基础设施</div>
						</div>
						<div class="content_wrap">
							<div class="features">
								<div class="features_Item">
									<img v-if="leftData.platform == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/yuetai.png" />
									<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/yuetai_close.png" />
									<div class="item_content">
										<div class="item_content_l">
											<img v-if="leftData.platform == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
											<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
										</div>
										<div class="item_content_text" :style="{ color: leftData.platform == 1 ? '#1D2129' : '#86909C' }">月台</div>
									</div>
								</div>
								<div class="features_Item">
									<img v-if="leftData.goodsLift == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/huoti.png" />
									<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/huoti_close.png" />
									<div class="item_content">
										<div class="item_content_l">
											<img v-if="leftData.goodsLift == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
											<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
										</div>
										<div class="item_content_text" :style="{ color: leftData.goodsLift == 1 ? '#1D2129' : '#86909C' }">货梯</div>
									</div>
								</div>
								<div class="features_Item">
									<img v-if="leftData.ramp == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/podao.png" />
									<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/podao_close.png" />
									<div class="item_content">
										<div class="item_content_l">
											<img v-if="leftData.ramp == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
											<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
										</div>
										<div class="item_content_text" :style="{ color: leftData.ramp == 1 ? '#1D2129' : '#86909C' }">坡道</div>
									</div>
								</div>
								<div class="features_Item">
									<img v-if="leftData.shelter == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/yupeng.png" />
									<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/yupeng_close.png" />
									<div class="item_content">
										<div class="item_content_l">
											<img v-if="leftData.shelter == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
											<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
										</div>
										<div class="item_content_text" :style="{ color: leftData.shelter == 1 ? '#1D2129' : '#86909C' }">雨棚</div>
									</div>
								</div>
								<div class="features_Item">
									<img v-if="leftData.rainmaker == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/penlin.png" />
									<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/penlin_close.png" />
									<div class="item_content">
										<div class="item_content_l">
											<img v-if="leftData.rainmaker == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
											<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
										</div>
										<div class="item_content_text" :style="{ color: leftData.rainmaker == 1 ? '#1D2129' : '#86909C' }">喷淋设备</div>
									</div>
								</div>
								<div class="features_Item">
									<img v-if="leftData.spareMotor == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/dianji.png" />
									<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/dianji_close.png" />
									<div class="item_content">
										<div class="item_content_l">
											<img v-if="leftData.spareMotor == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
											<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
										</div>
										<div class="item_content_text" :style="{ color: leftData.spareMotor == 1 ? '#1D2129' : '#86909C' }">备用电机</div>
									</div>
								</div>
								<div class="features_Item">
									<img v-if="leftData.dehumidifier == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/chushi.png" />
									<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/chushi_close.png" />
									<div class="item_content">
										<div class="item_content_l">
											<img v-if="leftData.dehumidifier == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
											<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
										</div>
										<div class="item_content_text" :style="{ color: leftData.dehumidifier == 1 ? '#1D2129' : '#86909C' }">除湿装备</div>
									</div>
								</div>
								<!-- <div class="features_Item">
									<img v-if="leftData.fireFightingDevice == 1" class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/xiaofang.png" />
									<img v-else class="item_img" src="@/assets/images/shangYutong/buildInfo/lessee/xiaofang_close.png" />
									<div class="item_content">
										<div class="item_content_l">
											<img v-if="leftData.fireFightingDevice == 1" class="item_content_img" src="@/assets/featuresCheck.png" />
											<img v-else class="item_content_img" src="@/assets/featuresClose.png" />
										</div>
										<div class="item_content_text" :style="{ color: leftData.fireFightingDevice == 1 ? '#1D2129' : '#86909C' }">消防设施</div>
									</div>
								</div> -->
							</div>
						</div>
						<!-- <div class="empty_wrap">
							<img :src="empty" />
							<div>暂无数据</div>
						</div> -->
					</div>
				</div>
				<div class="right">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">仓储详情</div>
						</div>
						<div class="content_wrap">
							<div class="content_flex">
								<div class="content_c" v-for="(item, index) in leftList" :key="index">
									<div class="content_" v-for="(childitem, childindex) in item.arr" :key="childindex">
										<div class="content_left">{{ childitem.name }}</div>
										<div class="content_right">{{ childitem.value }}</div>
									</div>
								</div>
							</div>
						</div>
						<!-- <div class="empty_wrap">
							<img :src="empty" />
							<div>暂无数据</div>
						</div> -->
					</div>
				</div>
			</div>
		</template>
	</div>
</template>
<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import { getBuildCangchuInfo } from '@/api/syt.js';
import { defineExpose } from 'vue';
import { all } from 'axios';

const props = defineProps({
	buildData: {
		type: Array,
		default: () => [],
	},
});
watch(
	() => props.buildData,
	(newVal) => {
		if (newVal.length > 0) {
			console.log('props', newVal);
		}
	},
	{
		deep: true,
	}
);

const leftData = ref({});
const rightData = ref({});
const allData = ref({});
const leftList = ref([]);
const rightList = ref([]);
async function getData() {
	const res = await getBuildCangchuInfo({
		buildingIds: props.buildData.map((item) => item.id).join(),
	});
	allData.value = res.data;

	if (res.data.length == 1) {
		leftData.value = res.data[0];
		leftList.value = [
			{
				arr: [
					{ name: '可租面积', value: leftData.value.rentArea },
					{ name: '起租面积', value: leftData.value.minRentArea },
				],
			},
			{
				arr: [
					{ name: '租赁价格', value: leftData.value.rental },
					{ name: '仓库服务', value: leftData.value.storageService },
				],
			},
			{
				arr: [
					{ name: '可分割', value: leftData.value.divisible == 1 ? '是' : '否' },
					{ name: '带重型货架', value: leftData.value.hasHeavyShelf == 1 ? '是' : '否' },
				],
			},
			{
				arr: [
					{ name: '可租时间', value: leftData.value.rentTime },
					{ name: '日处理订单量', value: leftData.value.dailyOrderCount },
				],
			},
			{
				arr: [
					{ name: '配送渠道', value: leftData.value.deliveryChannel },
					{ name: '服务类型 ', value: leftData.value.serviceType },
				],
			},
			{
				arr: [
					{ name: '配送范围', value: leftData.value.deliveryCoverage },
					{ name: '经营类型 ', value: leftData.value.manageType },
				],
			},
			{
				arr: [
					{ name: '入仓时间', value: leftData.value.entryTime },
					{ name: '起租时长 ', value: leftData.value.minRentDay },
				],
			},
			{
				arr: [
					{ name: '发货时间', value: leftData.value.deliveryTime },
					{ name: '发货速率 ', value: leftData.value.deliveryRate },
				],
			},
			{
				arr: [
					{ name: '标签', value: leftData.value.tags },
					{ name: '支持品类 ', value: leftData.value.supportCategories },
				],
			},
			{
				arr: [
					{ name: '消防设施', value: leftData.value.fireFightingDevice },
					{ name: '', value: '' },
				],
			},
		];
	} else if (res.data.length == 2) {
		leftData.value = res.data[0];
		rightData.value = res.data[1];
		leftList.value = [
			{
				arr: [
					{ name: '可租面积', value: leftData.value.rentArea },
					{ name: '起租面积', value: leftData.value.minRentArea },
				],
			},
			{
				arr: [
					{ name: '租赁价格', value: leftData.value.rental },
					{ name: '仓库服务', value: leftData.value.storageService },
				],
			},
			{
				arr: [
					{ name: '可分割', value: leftData.value.divisible == 1 ? '是' : '否' },
					{ name: '带重型货架', value: leftData.value.hasHeavyShelf == 1 ? '是' : '否' },
				],
			},
			{
				arr: [
					{ name: '可租时间', value: leftData.value.rentTime },
					{ name: '日处理订单量', value: leftData.value.dailyOrderCount },
				],
			},
			{
				arr: [
					{ name: '配送渠道', value: leftData.value.deliveryChannel },
					{ name: '服务类型 ', value: leftData.value.serviceType },
				],
			},
			{
				arr: [
					{ name: '配送范围', value: leftData.value.deliveryCoverage },
					{ name: '经营类型 ', value: leftData.value.manageType },
				],
			},
			{
				arr: [
					{ name: '入仓时间', value: leftData.value.entryTime },
					{ name: '起租时长 ', value: leftData.value.minRentDay },
				],
			},
			{
				arr: [
					{ name: '发货时间', value: leftData.value.deliveryTime },
					{ name: '发货速率 ', value: leftData.value.deliveryRate },
				],
			},
			{
				arr: [
					{ name: '标签', value: leftData.value.tags },
					{ name: '支持品类 ', value: leftData.value.supportCategories },
				],
			},
			{
				arr: [
					{ name: '消防设施', value: leftData.value.fireFightingDevice },
					{ name: '', value: '' },
				],
			},
		];
		rightList.value = [
			{
				arr: [
					{ name: '可租面积', value: rightData.value.rentArea },
					{ name: '起租面积', value: rightData.value.minRentArea },
				],
			},
			{
				arr: [
					{ name: '租赁价格', value: rightData.value.rental },
					{ name: '仓库服务', value: rightData.value.storageService },
				],
			},
			{
				arr: [
					{ name: '可分割', value: rightData.value.divisible == 1 ? '是' : '否' },
					{ name: '带重型货架', value: rightData.value.hasHeavyShelf == 1 ? '是' : '否' },
				],
			},
			{
				arr: [
					{ name: '可租时间', value: rightData.value.rentTime },
					{ name: '日处理订单量', value: rightData.value.dailyOrderCount },
				],
			},
			{
				arr: [
					{ name: '配送渠道', value: rightData.value.deliveryChannel },
					{ name: '服务类型 ', value: rightData.value.serviceType },
				],
			},
			{
				arr: [
					{ name: '配送范围', value: rightData.value.deliveryCoverage },
					{ name: '经营类型 ', value: rightData.value.manageType },
				],
			},
			{
				arr: [
					{ name: '入仓时间', value: rightData.value.entryTime },
					{ name: '起租时长 ', value: rightData.value.minRentDay },
				],
			},
			{
				arr: [
					{ name: '发货时间', value: rightData.value.deliveryTime },
					{ name: '发货速率 ', value: rightData.value.deliveryRate },
				],
			},
			{
				arr: [
					{ name: '标签', value: rightData.value.tags },
					{ name: '支持品类 ', value: rightData.value.supportCategories },
				],
			},
			{
				arr: [
					{ name: '消防设施', value: rightData.value.fireFightingDevice },
					{ name: '', value: '' },
				],
			},
		];
	}
}
defineExpose({
	getData,
});
</script>
<style lang="less" scoped>
.cangchu_wrap {
	flex: 1;
	display: flex;
	gap: 16px;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
		.single_wrap {
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			//	.content_wrap {
			//	padding: 20px 16px;
			//	}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
	.double_box_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16px;
		.single_wrap {
			flex: 1;
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			display: flex;
			flex-direction: column;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.empty_wrap {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				font-weight: 400;
				color: #86909c;
				img {
					width: 80px;
					height: 80px;
				}
			}
		}
	}
}

.content_wrap {
	padding: 20px 16px;
	.content_flex {
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		border: 1px solid #e5e6eb;
		border-bottom: 0px;
		.content_c {
			width: 100%;
			display: flex;
		}
	}
	.features {
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.features_Item {
			width: 139px;
			height: 96px;
			padding: 21px 16px;
			border-radius: 4px;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: center;
			background: #f7f8fa;
			.item_img {
				width: 28px;
				height: 28px;
				margin-bottom: 4px;
			}
			.item_content {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 22px;
				.item_content_l {
					width: 16px;
					height: 16px;
					margin: 3px 4px 3px 0;
					.item_content_img {
						width: 16px;
						height: 16px;
					}
				}
				.item_content_text {
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
					text-align: center;
					color: #1d2129;
				}
			}
		}
	}
	.geographic {
		background: #f7f8fa;
		width: calc(100% - 32px);
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
		padding: 9px 16px;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: #4e5969;
	}
	.content_ {
		display: flex;
		width: 50%;
		.content_left {
			box-sizing: border-box;
			padding: 9px 16px;
			background: #f7f8fa;
			border-right: 1px solid #e5e6eb;
			border-bottom: 1px solid #e5e6eb;
			font-weight: 500;
			font-size: 14px;
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 120px;
			// margin: 0 2px 2px 0;
			color: #1d2129;
		}
		.content_right {
			width: calc(100% - 120px);
			background: #ffffff;
			border-right: 1px solid #e5e6eb;
			border-bottom: 1px solid #e5e6eb;
			box-sizing: border-box;
			padding: 9px 16px;
			font-weight: 400;
			font-size: 14px;
			line-height: 21px;
			color: #4e5969;
		}
	}
	.content_flex > :nth-child(n) > :nth-child(2) > :nth-child(2) {
		border-right: 0px !important;
	}
	.content_flex > :nth-last-child(1) > :nth-last-child(1) > :nth-child(2) {
		border-right: 0px !important;
	}
}
</style>
