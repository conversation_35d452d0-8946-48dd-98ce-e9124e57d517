<template>
	<div class="comparison_box">
		<div class="common_wrap">
			<div class="left_empty_wrap" v-if="tableDataLeft.length == 0">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择资产
				</arco-button>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0" class="left_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('left')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataLeft"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0 && tableDataRight.length == 0" class="right_empty_wrap">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择对比资产
				</arco-button>
			</div>
			<div v-if="tableDataRight && tableDataRight.length > 0" class="right_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('right')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="id"
						:columns="tableColumns"
						:data="tableDataRight"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
		</div>
		<div class="chart_wrap">
			<div class="header_wrap">
				<span class="line"></span>
				<span class="title">估值与证券化</span>
			</div>
			<template v-if="sixRingDateList.length == 2">
				<div class="double_box_wrap">
					<div class="title1">
						<div class="title">估值结果</div>
					</div>
					<div class="double_wrap">
						<div class="content_wrap" v-for="item in sixRingDateList">
							<div class="top_wrap">
								<div class="box1">
									<div class="val_wrap">
										<div class="val">{{ item?.agg?.appraise?.evaluateValue }}</div>
										<div class="unit">亿元</div>
										<img :src="up_icon" class="top" />
									</div>
									<div class="label">资产评估值</div>
									<img :src="ping" class="bg" />
								</div>
								<div class="box2">
									<div class="val_wrap">
										<div class="val">{{ item?.agg?.appraise?.comparableValueRank }}</div>
										<div class="unit">/ {{ item?.agg?.appraise?.comparableValueRankTotal }}</div>
										<img :src="up_icon" class="top" />
									</div>
									<div class="label">额度排行</div>
									<img :src="pai" class="bg" />
								</div>
								<div class="desc_wrap">
									{{ handleValuationRanking(item) }}
								</div>
							</div>
							<div class="bottom_wrap">
								<div class="row">
									<div class="col1">评估对象</div>
									<div class="col2">
										{{ item?.agg?.appraise?.evaluateObjects }}
									</div>
								</div>
								<div class="row">
									<div class="col1">收益法</div>
									<div class="col3">{{ $utils.handleNumber(item?.agg?.appraise?.incomeApproach) }}亿元</div>
									<div class="col2">
										{{ handleApproach(item) }}
									</div>
								</div>
								<div class="row">
									<div class="col1">市场法</div>
									<div class="col3">{{ $utils.handleNumber(item?.agg?.appraise?.marketApproach) }}亿元</div>
									<div class="col2">
										{{ handleMarketApproach(item) }}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="double_box_wrap">
					<div class="title1">
						<div class="title">证券化</div>
					</div>
					<div class="double_wrap">
						<div class="content_wrap" v-for="item in sixRingDateList">
							<div class="top_wrap">
								<div class="box3">
									<div class="val_wrap">
										<div class="val">{{ item?.agg?.securitization?.comparableValueRank }}</div>
										<div class="unit">/ {{ item?.agg?.securitization?.comparableValueRankTotal }}</div>
									</div>
									<div class="label">额度排行</div>
									<img :src="zheng" class="bg" />
								</div>
								<div class="desc_wrap">
									{{ handleSecuritization(item) }}
								</div>
							</div>
							<div class="bottom_wrap2">
								<div class="desc">
									{{ handleTitleMontage(item) }}
								</div>
								<div class="table_wrap">
									<arco-table
										row-key="id"
										:columns="tableColumns2"
										:data="item.securitiesProgramList"
										:pagination="false"
										:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
									>
										<template #columns>
											<arco-table-column title="分层" data-index="name" ellipsis tooltip></arco-table-column>
											<arco-table-column title="评级" data-index="rating" ellipsis tooltip>
												<template #cell="{ record }">
													{{ $utils.isEmpty(record.rating) ? '-' : record.rating }}
												</template>
											</arco-table-column>
											<arco-table-column title="规模(亿)" data-index="scale" ellipsis tooltip>
												<template #cell="{ record }">
													<div style="text-align: center; width: max-content">
														{{
															$utils.isEmpty(record.scale)
																? '-'
																: record.name === '发行规模'
																? formattedMoney(record.scale, 2) + '亿'
																: record.name === '综合成本率'
																? formattedMoney(record.scale, 2) + '%'
																: formattedMoney(record.scale, 2)
														}}
													</div>
												</template>
											</arco-table-column>
											<arco-table-column title="利率" data-index="interestRate" ellipsis tooltip>
												<template #cell="{ record }">
													<div style="text-align: center; width: max-content">
														{{ record.interestRate ? `${record.interestRate}%` : '-' }}
													</div>
												</template>
											</arco-table-column>
										</template>
									</arco-table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="box_wrap">
					<div class="left">
						<div class="single_wrap">
							<div class="title1">
								<div class="title">估值结果</div>
							</div>
							<div class="content_wrap" v-if="sixRingDateList[0]">
								<div class="top_wrap">
									<div class="box1">
										<div class="val_wrap">
											<div class="val">{{ sixRingDateList[0]?.agg?.appraise?.evaluateValue }}</div>
											<div class="unit">亿元</div>
											<img :src="up_icon" class="top" />
										</div>
										<div class="label">资产评估值</div>
										<img :src="ping" class="bg" />
									</div>
									<div class="box2">
										<div class="val_wrap">
											<div class="val">{{ sixRingDateList[0]?.agg?.appraise?.comparableValueRank }}</div>
											<div class="unit">/ {{ sixRingDateList[0]?.agg?.appraise?.comparableValueRankTotal }}</div>
											<img :src="up_icon" class="top" />
										</div>
										<div class="label">额度排行</div>
										<img :src="pai" class="bg" />
									</div>
									<div class="desc_wrap">
										{{ handleValuationRanking(sixRingDateList[0]) }}
									</div>
								</div>
								<div class="bottom_wrap">
									<div class="row">
										<div class="col1">评估对象</div>
										<div class="col2">
											{{ sixRingDateList[0]?.agg?.appraise?.evaluateObjects }}
										</div>
									</div>
									<div class="row">
										<div class="col1">收益法</div>
										<div class="col3">{{ $utils.handleNumber(sixRingDateList[0]?.agg?.appraise?.incomeApproach) }}亿元</div>
										<div class="col2">
											{{ handleApproach(sixRingDateList[0]) }}
										</div>
									</div>
									<div class="row">
										<div class="col1">市场法</div>
										<div class="col3">{{ $utils.handleNumber(sixRingDateList[0]?.agg?.appraise?.marketApproach) }}亿元</div>
										<div class="col2">
											{{ handleMarketApproach(sixRingDateList[0]) }}
										</div>
									</div>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
					<div class="right">
						<div class="single_wrap">
							<div class="title1">
								<div class="title">证券化</div>
							</div>
							<div class="content_wrap" v-if="sixRingDateList[0]">
								<div class="top_wrap">
									<div class="box3">
										<div class="val_wrap">
											<div class="val">{{ sixRingDateList[0]?.agg?.securitization?.comparableValueRank }}</div>
											<div class="unit">/ {{ sixRingDateList[0]?.agg?.securitization?.comparableValueRankTotal }}</div>
										</div>
										<div class="label">额度排行</div>
										<img :src="zheng" class="bg" />
									</div>
									<div class="desc_wrap">
										{{ handleSecuritization(sixRingDateList[0]) }}
									</div>
								</div>
								<div class="bottom_wrap2">
									<div class="desc">
										{{ handleTitleMontage(sixRingDateList[0]) }}
									</div>
									<div class="table_wrap">
										<arco-table
											row-key="id"
											:columns="tableColumns2"
											:data="sixRingDateList[0].securitiesProgramList"
											:pagination="false"
											:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
										>
											<template #columns>
												<arco-table-column title="分层" data-index="name" ellipsis tooltip></arco-table-column>
												<arco-table-column title="评级" data-index="rating" ellipsis tooltip>
													<template #cell="{ record }">
														{{ $utils.isEmpty(record.rating) ? '-' : record.rating }}
													</template>
												</arco-table-column>
												<arco-table-column title="规模(亿)" data-index="scale" ellipsis tooltip>
													<template #cell="{ record }">
														<div style="text-align: center; width: max-content">
															{{
																$utils.isEmpty(record.scale)
																	? '-'
																	: record.name === '发行规模'
																	? formattedMoney(record.scale, 2) + '亿'
																	: record.name === '综合成本率'
																	? formattedMoney(record.scale, 2) + '%'
																	: formattedMoney(record.scale, 2)
															}}
														</div>
													</template>
												</arco-table-column>
												<arco-table-column title="利率" data-index="interestRate" ellipsis tooltip>
													<template #cell="{ record }">
														<div style="text-align: center; width: max-content">
															{{ record.interestRate ? `${record.interestRate}%` : '-' }}
														</div>
													</template>
												</arco-table-column>
											</template>
										</arco-table>
									</div>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
				</div>
			</template>
		</div>
	</div>
	<buildSelect key="all" v-model="dialogTableVisible" :selectedData="multipleSelection" :maxSelectNum="2" @confirm="handleBuildConfirm"></buildSelect>
	<!-- <buildSelect key="single_left" v-model="dialogSingleLeftVisible" :maxSelectNum="1" @confirm="handleBuildLeftConfirm"></buildSelect>
	<buildSelect key="single_right" v-model="dialogSingleRightVisible" :maxSelectNum="1" @confirm="handleBuildRightConfirm"></buildSelect> -->
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import { getBuildingAppraisedValue, getDictList } from '@/api/syt.js';
import getReport from '../../../../component/getReport/index.vue';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import descBg from '@/assets/images/shangYutong/buildInfo/desc_bg.png';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import people_icon from '@/assets/images/shangYutong/buildInfo/people_icon.png';
import vs from '@/assets/images/shangYutong/buildInfo/vs.png';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import buildSelect from '@/component/buildSelect/index.vue';
import echartPie from './echart/pie.vue';
import summary_bg from '@/assets/images/shangYutong/buildInfo/summary_bg.png';
import summary_icon from '@/assets/images/shangYutong/buildInfo/summary_icon.png';
import up_icon from '@/assets/images/shangYutong/buildInfo/up_icon.png';
import ping from '@/assets/images/shangYutong/buildInfo/ping.png';
import pai from '@/assets/images/shangYutong/buildInfo/pai.png';
import zheng from '@/assets/images/shangYutong/buildInfo/zheng.png';
import { useStore } from '../../../../store';
import rat from '@/RatMap.vue'; //地图
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});
const store = useStore();
const getReportRef = ref();
const loading = ref();
const dialogTableVisible = ref(false); //对话框显示
const dialogSingleLeftVisible = ref(false); //对话框显示
const dialogSingleRightVisible = ref(false); //对话框显示
const rate = ref([]);
const buildingTypes = ref([]);
const box_copyObj = ref('');
const box_copyObjTwo = ref('');
const multipleSelection = ref([]);
// const multipleSelection = ref([
// 	{
// 		id: '1833701603456532482',
// 	},
// 	{ id: '1833706846806294530' },
// ]);
const leftData = ref({});
const rightData = ref({});
const tableDataLeft = ref([]);
const tableDataRight = ref([]);
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const tableColumns2 = [
	{
		title: '分层',
		dataIndex: 'name',
	},
	{
		title: '评级',
		dataIndex: 'rating',
	},
	{
		title: '规模(亿)',
		dataIndex: 'scale',
	},
	{
		title: '利率',
		dataIndex: 'interestRate',
	},
];
const tableColumns = [
	{
		title: '资产名称',
		dataIndex: 'buildingName',
		width: '150',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '资产类型',
		dataIndex: 'buildingType',
		width: '100',
	},
	{
		title: '地址',
		dataIndex: 'street',
		width: '300',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '建筑面积',
		dataIndex: 'buildingSize',
		width: '100',
	},
	{
		title: '维护情况',
		dataIndex: 'maintenance',
		width: '100',
	},
	{
		title: '单价',
		dataIndex: 'absoluteValue',
		width: '100',
	},
];
const leftCopyShow = ref(false);
const rightCopyShow = ref(false);

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
	}
	// save();
});
//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}
//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};

//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	getBuildingAppraisedValue({ buildingIds: obj.ids }).then((res) => {
		sixRingDateList.value = [];
		tableDataLeft.value = [];
		tableDataRight.value = [];
		if (res.code === 200) {
			emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
			sixRingDateList.value = res.data;
			multipleSelection.value = obj.arr;
			handleUpdate(1);
			if (res.data && res.data.length == 1) {
				tableDataLeft.value.push(obj.arr[0]);
			} else if (res.data && res.data.length == 2) {
				tableDataLeft.value.push(obj.arr[0]);
				tableDataRight.value.push(obj.arr?.[1]);
			}
			sixRingDateList.value.forEach((element, index) => {
				if (index == 0) element.businessDistrictName = obj.arr[0].buildingName;
				if (index == 1) element.businessDistrictName = obj.arr?.[1]?.buildingName;
			});

			loading.value.close();
		}
	});
}
const sixRingDateList = ref([]);
// const sixRingDateList = ref([
// 	//商圈数据
// 	{
// 		districtsList: {
// 			// //商圈数据
// 			businessDistrictName: '',
// 			range: '',
// 			activeUrl: [],
// 			imageActiveIndex: 0,
// 			businessEvaluateInfo: {},
// 			imgList: [],
// 			businessCoordinateList: [],
// 			info: [],
// 			infoActiveIndex: 0,
// 		},
// 		buildBusinessDistrictAvgList: [],
// 		buildBusinessDistrictList: [],
// 	},
// ]);
// 确定
//组装数据
function handleUpdate(type) {
	sixRingDateList.value.forEach((element, index) => {
		if (!type) {
			multipleSelection.value.forEach((item) => {
				if (item.id === element.id && index === 0) {
					element.businessDistrictName = item.buildingName;
					tableDataLeft.value.push(item);
				}
				if (item.id === element.id && index === 1) {
					element.businessDistrictName = item.buildingName;
					tableDataRight.value.push(item);
				}
			});
		}
		// 拼接证券化表格数据
		let arr = [
			...element.agg.securitization.securitiesProgramList,
			{
				name: '发行规模',
			},
			{
				name: '综合成本率',
			},
		];

		arr.forEach((childItems, childIndex) => {
			if (childIndex == 0) {
				childItems['name'] = '优先级A1';
			} else if (childIndex == 1) {
				childItems['name'] = '优先级A2';
			} else if (childIndex == 2) {
				childItems['name'] = '优先级A3';
			} else if (childIndex == 3) {
				childItems['scale'] = element.agg?.securitization?.securitiesFinancialParam?.issueScale || 0;
				childItems['name'] = '发行规模';
			} else if (childIndex == 4) {
				childItems['scale'] = element.agg?.securitization?.securitiesFinancialParam?.combinedRatio || 0;
				childItems['name'] = '综合成本率';
			}
		});
		element.securitiesProgramList = arr;
	});
	console.log('🚀 ~ sixRingDateList.value.forEach ~ sixRingDateList.value:', sixRingDateList.value);
}
// 确定
const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		sixRingDateList.value = [];
		tableDataLeft.value = [];
		tableDataRight.value = [];
		emit('handleBuildingId', { ids: null, arr: [] });
		return;
	} else {
		let ids = multipleSelection.value.map((item) => item.id).join(',');
		getBuildingAppraisedValue({ buildingIds: ids }).then((res) => {
			sixRingDateList.value = [];
			tableDataLeft.value = [];
			tableDataRight.value = [];
			if (res.code === 200) {
				emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
				sixRingDateList.value = res.data;
				handleUpdate();
			}
			dialogTableVisible.value = false;
		});
	}
};
// 获取字典
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	save();
}
function handleBuildLeftConfirm(data) {
	multipleSelection.value[0] = data[0];
	save();
}
function handleBuildRightConfirm(data) {
	multipleSelection.value[1] = data[0];
	save();
}
function clear(type) {
	if (type == 'left') {
		tableDataLeft.value = [];
		multipleSelection.value.shift();
	} else {
		tableDataRight.value = [];
		multipleSelection.value.pop();
	}
	save();
}
// 复制
function handlerCopy(text) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(text)
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = text;
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

//市场法内容拼接
function handleMarketApproach(item) {
	if (!item?.agg?.appraise?.grossReturn) {
		return '';
	}
	return `以${item?.agg?.appraise?.comparableInstance}作为市场法的参照物。 对交易时间、交易情况、房地产状况、区位状况、商业繁华度、基础设施完善度、自然及人文环境、公共服务设施状况、楼层、临路状况、实体状况、商业类型、进深比、配套基础设施、内部装修、层高、楼龄及保养、权益状况土地剩余年限、租约限制、规划限制条件（容积率）等因素进行修正，各给与${item?.agg?.appraise?.weight}%权重。最终${item.businessDistrictName}的市场法估价结果为${item?.agg?.appraise?.marketApproachEvaluateResult}元/平方米。`;
}

//收益法内容拼接
function handleApproach(item) {
	if (!item?.agg?.appraise?.grossReturn) {
		return '';
	}
	return `本次估价取各实例毛报酬率的简单算术平均数作为估计对象的毛报酬率，即${item?.agg?.appraise?.grossReturn || 0}%，本次估价采取最终毛报酬率为${
		item?.agg?.appraise?.grossReturn || 0
	}%，故本次估价最终的净报酬率为${item?.agg?.appraise?.netInterestRate || 0}%。 按照评估基准日${item?.agg?.appraise?.createTime}中国十年期国债收益率${
		item?.agg?.appraise?.decadeCountriesEarnings
	}%为无风险报酬率，对应折现率为${item?.agg?.appraise?.discountRate || 0}%。通过现金流量折现法计算得出，收益法估价结果为${
		item?.agg?.appraise?.incomeApproachEvaluateResult || 0
	}元/平方米`;
}

// 估值排名详情内容拼接
function handleValuationRanking(item) {
	console.log('🚀 ~ handleValuationRanking ~ item:', item);
	if (!item?.agg?.appraise?.createTime) {
		return '';
	}
	return `于${item.agg.appraise.createTime}，选取可比实例为${item?.agg?.appraise?.comparableInstance}。术木智能基于出租率为80%的情形，评估资产价值。${
		item.businessDistrictName
	}估值结果为${item?.agg?.appraise?.evaluateResult || 0}元/平方米，在同资产类型中排名${item.agg?.appraise?.comparableValueRank || 0}。`;
}

//证券化详情内容拼接
function handleSecuritization(item) {
	if (!item?.agg?.securitization?.evaluateValue && !item?.agg?.securitization?.ebidta) {
		return '';
	}

	let name = `根据《${item.businessDistrictName}单价评估报告》，${item.businessDistrictName}的评估值为${item?.agg?.securitization?.evaluateValue}亿元。根据术木智能EBITDA系数模型，结合${item.businessDistrictName}的面积、运维情况，${item.businessDistrictName}EBITDA系数为${item?.agg?.securitization?.ebidta}。`;
	return name;
}

// 术木智能金融项目风险评估模型内容拼接
function handleTitleMontage(item) {
	if (!item?.businessDistrictName) {
		return '';
	}
	return `根据术木智能金融项目风险评估模型，${item?.businessDistrictName}权益型金融产品参数如下：`;
}
</script>
<style lang="less" scoped>
.comparison_box {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.common_wrap {
		padding: 20px 16px;
		background-color: #fff;
		display: flex;
		gap: 16px;
		border-radius: 0px 4px 4px 4px;
		.left_empty_wrap,
		.left_content_wrap,
		.right_empty_wrap,
		.right_content_wrap {
			flex: 1;
		}
		.left_empty_wrap,
		.right_empty_wrap {
			border: 1px solid #e5e6eb;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-radius: 4px;
			padding: 12px 0;
			.icon {
				width: 64px;
				height: 64px;
			}
		}
		.title_wrap {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;
		}
	}
	.chart_wrap {
		flex: 1;
		border-radius: 4px;
		margin-top: 16px;
		padding: 20px 16px 16px 16px;
		background-color: #fff;
		.header_wrap {
			display: flex;
			align-items: center;
			margin-bottom: 16px;
			.line {
				width: 4px;
				height: 14px;
				background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
				border-radius: 4px;
			}
			.title {
				color: #1d2129;
				font-size: 20px;
				font-weight: 600;
				margin-left: 8px;
				margin-right: 20px;
				line-height: 28px;
			}
		}
		.double_box_wrap {
			box-sizing: border-box;
			border: 1px solid #e5e6eb;
			border-radius: 4px;
			margin-bottom: 16px;
			.title1 {
				box-sizing: border-box;
				padding: 0 20px;
				width: 100%;
				height: 48px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f8fa;
				border-bottom: 1px solid #e5e6eb;
				.title {
					font-size: 16px;
					font-weight: 600;
					color: #1d2129;
				}
			}
			.double_wrap {
				display: flex;
				padding: 20px 16px;
				box-sizing: border-box;
				gap: 16px;
				.content_wrap {
					.top_wrap {
						display: flex;
						gap: 8px;
						.box1,
						.box2,
						.box3 {
							box-sizing: border-box;
							min-width: 180px;
							min-height: 120px;
							padding-left: 20px;
							display: flex;
							flex-direction: column;
							justify-content: center;
							position: relative;
							border-radius: 4px;
							gap: 2px;
							.val_wrap {
								display: flex;
								align-items: flex-end;
								gap: 4px;
								color: #0f2860;
								z-index: 99;
								.val {
									font-size: 32px;
									font-weight: 600;
									line-height: 32px;
								}
								.unit {
									font-size: 14px;
									font-weight: 600;
									line-height: 22px;
								}
								.top {
									width: 16px;
									height: 16px;
									margin-bottom: 4px;
								}
							}
							.label {
								font-size: 14px;
								font-weight: 400;
								line-height: 22px;
								color: #0f2860;
							}
							.bg {
								width: 60px;
								height: 64px;
								position: absolute;
								right: 0;
								bottom: 0;
							}
						}
						.box1 {
							background: linear-gradient(180deg, #e2e5ff 0%, #f5f7ff 100%);
						}
						.box2 {
							background: linear-gradient(180deg, #daf6ff 0%, #f2fcff 100%);
						}
						.box3 {
							background: linear-gradient(180deg, #dcecff 0%, #f2f9ff 100%);
						}
						.desc_wrap {
							background: #f7f8fa;
							padding: 16px 20px;
							line-height: 22px;
							font-size: 14px;
							font-weight: 400;
							color: #4e5969;
							min-height: 120px;
							box-sizing: border-box;
						}
					}
					.bottom_wrap {
						margin-top: 16px;
						border: 1px solid #e5e6eb;
						border-bottom: 0px;
						border-radius: 4px;
						.row {
							display: flex;
							.col1 {
								background: #f7f8fa;
								min-width: 120px;
								padding-left: 16px;
								padding-top: 16px;
								padding-bottom: 16px;
								border-right: 1px solid #e5e6eb;
								border-bottom: 1px solid #e5e6eb;
								display: flex;
								align-items: center;
								font-size: 14px;
								font-weight: 600;
								line-height: 22px;
								color: #1d2129;
							}
							.col2 {
								display: flex;
								align-items: center;
								color: #4e5969;
								padding: 12px 16px;
								font-size: 14px;
								line-height: 22px;
								border-bottom: 1px solid #e5e6eb;
								flex: 1;
							}
							.col3 {
								box-sizing: border-box;
								color: #1868f1;
								padding: 0 16px;
								font-size: 14px;
								font-weight: 600;
								line-height: 22px;
								display: flex;
								align-items: center;
								justify-content: center;
								border-right: 1px solid #e5e6eb;
								border-bottom: 1px solid #e5e6eb;
								min-width: 100px;
							}
						}
					}
					.bottom_wrap2 {
						margin-top: 16px;
						border-radius: 4px;
						.desc {
							font-size: 14px;
							font-weight: 600;
							color: #1d2129;
							margin-top: 16px;
							margin-bottom: 8px;
						}
					}
				}
			}
		}
		.box_wrap {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			gap: 16px;
			.left,
			.right {
				width: calc(50% - 8px);
				display: flex;
				flex-direction: column;
				gap: 16px;
			}
			.single_wrap {
				box-sizing: border-box;
				min-height: 300px;
				border: 1px solid #e5e6eb;
				border-radius: 4px;
				display: flex;
				flex-direction: column;
				.title1 {
					box-sizing: border-box;
					padding: 0 20px;
					width: 100%;
					height: 48px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #f7f8fa;
					border-bottom: 1px solid #e5e6eb;
					.title {
						font-size: 16px;
						font-weight: 600;
						color: #1d2129;
					}
				}
				.content_wrap {
					padding: 20px 16px;
					.top_wrap {
						display: flex;
						gap: 8px;
						.box1,
						.box2,
						.box3 {
							min-width: 180px;
							height: 120px;
							padding-left: 20px;
							display: flex;
							flex-direction: column;
							justify-content: center;
							position: relative;
							border-radius: 4px;
							gap: 2px;
							.val_wrap {
								display: flex;
								align-items: flex-end;
								gap: 4px;
								color: #0f2860;
								z-index: 99;
								.val {
									font-size: 32px;
									font-weight: 600;
									line-height: 32px;
								}
								.unit {
									font-size: 14px;
									font-weight: 600;
									line-height: 22px;
								}
								.top {
									width: 16px;
									height: 16px;
									margin-bottom: 4px;
								}
							}
							.label {
								font-size: 14px;
								font-weight: 400;
								line-height: 22px;
								color: #0f2860;
							}
							.bg {
								width: 60px;
								height: 64px;
								position: absolute;
								right: 0;
								bottom: 0;
							}
						}
						.box1 {
							background: linear-gradient(180deg, #e2e5ff 0%, #f5f7ff 100%);
						}
						.box2 {
							background: linear-gradient(180deg, #daf6ff 0%, #f2fcff 100%);
						}
						.box3 {
							background: linear-gradient(180deg, #dcecff 0%, #f2f9ff 100%);
						}
						.desc_wrap {
							background: #f7f8fa;
							padding: 16px 20px;
							line-height: 22px;
							font-size: 14px;
							font-weight: 400;
							color: #4e5969;
						}
					}
					.bottom_wrap {
						margin-top: 16px;
						border: 1px solid #e5e6eb;
						border-bottom: 0px;
						border-radius: 4px;
						.row {
							display: flex;
							.col1 {
								background: #f7f8fa;
								min-width: 120px;
								padding-left: 16px;
								padding-top: 16px;
								padding-bottom: 16px;
								border-right: 1px solid #e5e6eb;
								border-bottom: 1px solid #e5e6eb;
								display: flex;
								align-items: center;
								font-size: 14px;
								font-weight: 600;
								line-height: 22px;
								color: #1d2129;
							}
							.col2 {
								display: flex;
								align-items: center;
								color: #4e5969;
								padding: 12px 16px;
								font-size: 14px;
								line-height: 22px;
								border-bottom: 1px solid #e5e6eb;
								flex: 1;
							}
							.col3 {
								box-sizing: border-box;
								color: #1868f1;
								padding: 0 16px;
								font-size: 14px;
								font-weight: 600;
								line-height: 22px;
								display: flex;
								align-items: center;
								justify-content: center;
								border-right: 1px solid #e5e6eb;
								border-bottom: 1px solid #e5e6eb;
								min-width: 100px;
							}
						}
					}
					.bottom_wrap2 {
						margin-top: 16px;
						border-radius: 4px;
						.desc {
							font-size: 14px;
							font-weight: 600;
							color: #1d2129;
							margin-top: 16px;
							margin-bottom: 8px;
						}
					}
				}
				.empty_wrap {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: 400;
					color: #86909c;
					img {
						width: 80px;
						height: 80px;
					}
				}
			}
		}
	}

	.arco-btn-size-medium {
		border-radius: 4px;
	}
}
</style>
