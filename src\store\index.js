import { defineStore } from 'pinia';
import { nextTick } from 'vue';
import { getDistrict } from '@/api/home.js'; // 获取城市
import { getUserInfo } from '../api/login.js'; // 获取用户信息
import { getShoppingCart } from '@/api/rights';

import { createStore } from 'vuex';
import { ElMessage } from 'element-plus';
import createPersistedState from 'vuex-persistedstate';
export const vuexStore = createStore({
	state: {
		//城市
		cityArray: [],
		//用户信息
		userInfo: {},
		//购物车数量
		shoppingCart: 0,
		// 新用户专享劵
		newUserCoupon: false,
		//新用户专享劵存在的情况下默认弹出展示一次
		newUserCouponDefaultShow: true,
	},
	mutations: {
		// 获取城市
		handleGetCity(state, data) {
			state.cityArray = data;
		},
		// 获取用户信息
		handleUpdata(state, data) {
			state.userInfo = data;
		},
		//获取购物车数量
		handleShoppingCart(state, data) {
			state.shoppingCart = data;
		},
		// 更新新用户专享劵展示状态
		handleNewUserCoupon(state, data) {
			state.newUserCoupon = data;
		},
		// 更新新用户专享劵展示状态
		handleNewUserCouponDefaultShow(state, data) {
			state.newUserCouponDefaultShow = data;
		},
		// 清空vuex数据
		clearVuex(state) {
			state.cityArray = []; //城市
			state.userInfo = {}; //用户信息
			state.shoppingCart = 0; //购物车数量
			state.newUserCoupon = false; //新用户专享劵
			state.newUserCouponDefaultShow = true;
		},
	},

	actions: {
		// 清空数据
		clearData({ commit }) {
			commit('clearVuex');
		},
		// 定义处理异步操作的方法
		// 获取城市
		async handleDistrict({ commit }) {
			let response = await getDistrict();
			response.data.forEach((element) => {
				element.label = element.city;
				if (element.children) {
					element.children.forEach((item) => {
						item.label = item.district;
					});
				}
			});
			commit('handleGetCity', response.data);
		},

		// 获取购物车数量
		async handleGetShoppingCart({ commit }) {
			await getShoppingCart()
				.then((res) => {
					commit('handleShoppingCart', res.data.length);
				})
				.catch((err) => {
					ElMessage.error('获取购物车数量失败，请重新登录');
				});
		},
		// 获取用户信息
		async getUpdata({ commit }) {
			await getUserInfo()
				.then((res) => {
					commit('handleUpdata', res.data);
				})
				.catch((err) => {
					ElMessage.error('获取用户信息失败，请重新登录');
				});
		},
	},
	getters: {
		// 定义计算属性
	},
	plugins: [createPersistedState()],
});
export const useStore = defineStore('userInfo', {
	state: () => {
		return {
			map_key: '364076015b42375ffa7ee8ea61c9ae6b',
			imagePathPrefix: 'https://static.biaobiaozhun.com/', // 图片拼接前缀
			isLogin: window.localStorage.getItem('token') ? true : false, //是否登录
			http_oa: 'https://static.biaobiaozhun.com/',
			childMenuIndex3: null,
		};
	},
	getters: {},
	actions: {
		changeIsLogin(val) {
			this.isLogin = val;
		},
		//获取用户信息
		async getUserInfo() {
			vuexStore.dispatch('getUpdata');
		},
		//监听菜单状态
		changeChildMenuIndex3(val) {
			this.childMenuIndex3 = val;
		},
	},
});
export default () => ({
	useStore: useStore(),
	vuexStore: vuexStore,
});
