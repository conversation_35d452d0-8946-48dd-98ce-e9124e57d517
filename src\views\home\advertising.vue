<template>
	<div class="advertising_box">
		<div class="bzkjBox"><img src="@/assets/layout/logo.png" class="logo" alt="" /><span>标准空间</span></div>
		<div class="advertising">
			<div class="title_box">
				<p class="title">{{ titleList.title }}</p>
				<p class="area">{{ titleList.feature }}</p>
			</div>
			<div class="address_box">{{ titleList.city }}/{{ titleList.district }}/{{ titleList.street }}</div>
			<div class="img_boxone boxs">
				<!-- <div class="tab_box">
					<div class="tab_item" :class="selectIndex == 0 ? 'activeed' : ''" @click="onSelect(0)">视频</div>
					<div class="tab_item" :class="selectIndex == 1 ? 'activeed' : ''" @click="onSelect(1)">图片</div>
				</div> -->
				<!-- 轮播图 -->
				<div class="swiperBox">
					<swiper
						:modules="[Autoplay]"
						:speed="600"
						:slides-per-view="num"
						:loop="true"
						:autoplay="data.swiperAutoplay"
						class="swiperBox"
						@swiper="onSwiper"
					>
						<swiper-slide>
							<div class="redio_box">
								<video
									:src="`${proxyAddress_video}${titleList.displayVideo}`"
									controls
									playsinline="false"
									webkit-playsinline="false"
									:poster="proxyAddress + carouselArr[0]"
									style="width: 100%; height: 100%; object-fit: fill"
									ref="video_ref"
								/>
							</div>
						</swiper-slide>
						<swiper-slide v-for="(item, index) in carouselArr" :key="index">
							<div class="swiper_img"><img :src="`${proxyAddress}${item}`" alt="" /></div>
						</swiper-slide>
					</swiper>
				</div>
			</div>
			<div class="describe_box">
				<div class="zsld">
					<p class="title">招商亮点</p>
					<div v-html="titleList.investmentHighlight" class="content"></div>
				</div>
				<div :class="scrollTop > 566 ? 'card_box card_box2' : 'card_box'">
					<div class="card">
						<!-- 经纪人 -->
						<div class="card_jjr">
							<img class="jjrtx" :src="`${proxyAddress}${titleList.linkPic}`" alt="" />
							<div class="jjrBox">
								<span class="linkName">{{ titleList.linkName }}</span>
								<div class="jjrzw">
									<img class="jjrimg" src="@/assets/layout/logo.png" alt="" />
									{{ titleList.linkPosition }}
								</div>
							</div>
						</div>
						<!-- 公司 -->
						<div class="company">
							<p>{{ titleList.company }}</p>
						</div>
						<div class="phone">
							<img class="phone_img" src="@/assets/images/home/<USER>" alt="" />
							<span>{{ telephone }}</span>
						</div>
					</div>
				</div>
			</div>
			<div class="tenantable">
				<h5>可用空间</h5>
				<el-table :data="tableData" default-expand-all row-key="id" :header-cell-style="{ background: '#EBEBEB', color: '#000', fontWeight: '400' }">
					<el-table-column prop="floorNum" label="空间" />
					<el-table-column prop="rentArea" label="出租面积" />
					<el-table-column prop="minRentPeriod" label="最短租期" />
					<el-table-column prop="paymentMethod" label="支付方式" />
					<el-table-column prop="rentPrice" label="租金" />
					<el-table-column prop="propertyFee" label="物业费" />
					<el-table-column prop="waterFee" label="水费" />
					<el-table-column prop="electricityFee" label="电费" />
					<el-table-column prop="feeDescription" label="空调收费标准" :width="220" />
					<el-table-column prop="feeDescription" label="" type="expand">
						<template #default="props">
							<!-- {{props}} -->
							<div>
								<div v-for="item in props.row.standardSpaceFloorPicVOList" :key="item" style="float: left; width: 50%">
									<img style="width: 95%" alt="" :src="`${proxyAddress}${item.floorPic}`" />
								</div>
							</div>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<div class="map_box">
				<h4>配套服务</h4>
				<div v-html="titleList.supportServices" class="content"></div>
				<div class="gaode">
					<rat :coordinate="titleList.addressCoordinate" style="width: 100%; height: 400px"></rat>
				</div>
				<div class="facility_box">
					<div class="facility_item" v-for="item in facilityList" :key="item">
						<h4>{{ item.type }}</h4>
						<p>
							<span v-for="name in item.pois" :key="name">{{ name.name }}</span>
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import { ref, reactive, toRefs, onMounted, onUnmounted, nextTick } from 'vue';
import { AdvertDetail, getFacility } from '../../../src/api/home.js';
import { useRoute } from 'vue-router';
import rat from '@/RatMap.vue';
import http from '@/utils/http';
const proxyAddress = 'https://static.biaobiaozhun.com/';
const proxyAddress_video = ref('https://static.biaobiaozhun.com/'); //视频前缀
//获取新闻详情信息
const id = ref(0);
const num = ref(2);
const zb = ref('115.72,40.02');

const scrollTop = ref(0);
const updateScrollTop = () => {
	scrollTop.value = window.scrollY;
};

const route = useRoute();
const data = reactive({
	swiperIndex: 0,
	swiperAutoplay: {
		delay: 2000,
		disableOnInteraction: false,
	},
	defaultExpandedKeys: ['1813169742510694401'],
});
console.log(route.query, 7812);
const my_swiper = ref(null);
onMounted(async () => {
	let id = route.query.id;
	await getNewsDetail(id);
	window.innerWidth > 991 ? (num.value = 2) : (num.value = 1);
	window.addEventListener('resize', function (e) {
		e.target.innerWidth > 991 ? (num.value = 2) : (num.value = 1);
	});
	// 这是新增的代码
	window.addEventListener('scroll', updateScrollTop);
	changeAciton();
	// console.log(id,123121,route.query);
});
onUnmounted(() => {
	window.removeEventListener('scroll', updateScrollTop);
});
const titleList = ref([]);
const spacePic = ref('');
const spacePicArr = ref([]);
const carousel = ref('');
const carouselArr = ref([]);
const tableData = ref([]);
const coordinateValue = ref('');
const telephone = ref('');

const getNewsDetail = async (id) => {
	await AdvertDetail({
		spaceId: id,
	})
		.then((res) => {
			// console.log(spaceId,12312);
			console.log('新闻1231', res);
			titleList.value = res.data;
			spacePic.value = res.data.carousel;
			spacePicArr.value = spacePic.value.split(',');
			console.log(spacePicArr.value, 12312);
			carousel.value = res.data.carousel;
			carouselArr.value = carousel.value.split(',');
			console.log(carouselArr.value, 123121);
			tableData.value = res.data.standardSpaceFloorVOList;
			coordinateValue.value = res.data.addressCoordinate;
			telephone.value = res.data.linkTel;
			// 在第三个和第八个后面加-
			telephone.value = telephone.value.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
			console.log(telephone.value, 8876);

			ongetFacility(coordinateValue.value);
		})
		.catch((err) => {
			console.log('请求失败');
		});
};
const onSwiper = (swiper) => {
	nextTick(() => {
		my_swiper.value = swiper;
	});
};

const facilityList = ref([]);

const ongetFacility = async (coordinateValue) => {
	// console.log();
	const res = getFacility({
		coordinate: coordinateValue,
	})
		.then((res) => {
			facilityList.value = res.data;
			console.log(facilityList.value[1], 12345);
		})
		.catch((err) => {
			console.log(err, 12345);
			return err;
		});
};
const selectIndex = ref(0);
const onSelect = (tab) => {
	selectIndex.value = tab;
};
const video_ref = ref(null);
//判断是否播放视频

const changeAciton = () => {
	video_ref.value.addEventListener('play', function () {
		console.log('视频开始播放');
		my_swiper.value.autoplay.stop();
	});
	video_ref.value.addEventListener('pause', function () {
		console.log('视频暂停播放');
		my_swiper.value.autoplay.start();
	});
	video_ref.value.addEventListener('ended', function () {
		console.log('视频结束播放');
		my_swiper.value.autoplay.start();
	});
};
</script>
<style scoped lang="less">
.advertising_box {
	width: 100%;
	height: 100%;
	min-height: 100vh;
	background: #fff;

	// padding-top: 20px;
	.bzkjBox {
		width: 150px;
		height: 40px;
		margin: 0 auto;
		display: flex;
		align-items: center;
		line-height: 40px;

		.logo {
			width: 30px;
			height: 30px;
			margin-left: 20px;
		}
	}
}

.advertising {
	width: 80%;
	margin: 0 auto;

	.title_box {
		height: 73px;
		// box-shadow: 0px 4px 10px 2px rgba(0, 0, 0, 0.5);
		background: rgba(255, 255, 255, 0.38);
		// opacity: 0.38;
		// margin-top: 39px;
		text-align: center;

		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 24px;
			font-weight: 400;

			// line-height: 32px;
			letter-spacing: 0px;
			text-align: center;
			// margin-top: 8px;
		}

		.area {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			// line-height: 21px;
			letter-spacing: 0px;
			text-align: center;
		}
	}

	.address_box {
		margin: 16px 16px;
	}

	.boxs {
		width: 100%;
		height: 376px;
		margin-top: 20px;
		display: flex;
		justify-content: space-between;
		position: relative;

		.swiper_box,
		.redio_box {
			width: 100%;
			height: 370px;

			img {
				width: 100%;
				height: 100%;
			}

			// .video {
			//     background-size: cover;
			// }
		}

		.swiperBox {
			width: 100%;

			.swiper-slide {
				width: 100%;
				height: 370px;

				.swiper_img {
					height: 370px;

					img {
						width: 100%;
						height: 370px;
					}
				}
			}
		}
	}

	.tab_box {
		width: 127px;
		height: 29px;
		display: flex;
		justify-content: space-around;
		border-radius: 15px;
		background: rgba(0, 0, 0, 0.45);
		line-height: 29px;
		color: #fff;
		// text-align: center;
		position: absolute;
		top: 15px;
		left: 50%;
		transform: translateX(-50%);
		z-index: 99;

		.tab_item {
			padding: 0 10px;
			text-align: center;
		}
	}

	.activeed {
		border-radius: 15px;
		width: 40%;
		background: rgba(0, 0, 0, 0.5);
	}

	.describe_box {
		// width: 70%;
		display: flex;
		justify-content: space-between;

		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 20px;
			font-weight: 400;
			padding: 10px;
			letter-spacing: 0px;
			text-align: left;
		}

		.content {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			padding: 10px;
			letter-spacing: 0px;
			// text-align: center;
		}

		.card_box {
			width: 238px;
			height: 280px;
			box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.5);
			background: rgb(255, 255, 255);
			// opacity: 0.38;
			margin-right: 4px;

			.card {
				.card_jjr {
					display: flex;
					margin-top: 20px;
					margin-left: 15px;

					.jjrtx {
						width: 68px;
						height: 68px;
					}

					.jjrBox {
						margin-left: 14px;
						margin-top: 10px;

						.linkName {
							font-size: 16px;
							font-weight: 700;
						}

						.jjrzw {
							display: flex;
							align-items: center;
							margin-top: 10px;

							.jjrimg {
								width: 20px;
								height: 20px;
							}
						}
					}
				}

				.company {
					font-size: 18px;
					text-align: center;
					color: #38609a;
					font-weight: 700;
					margin-top: 30px;
					margin-bottom: 30px;
				}

				// 手机号
				.phone {
					display: flex;
					margin-top: 20px;
					width: 200px;
					color: rgb(0, 0, 0);
					font-family: 微软雅黑;
					font-size: 16px;
					font-weight: 700;
					line-height: 21px;
					letter-spacing: 0px;
					text-align: center;
					display: flex;
					justify-content: center;
					align-items: center;

					.phone_img {
						width: 24px;
						height: 24px;
					}
				}
			}
		}

		.card_box2 {
			position: fixed;
			z-index: 100;
			top: 10px;
			margin-left: 1118px;
		}
	}

	.tenantable {
		h5 {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 20px;
			font-weight: 400;
			padding: 10px;
			letter-spacing: 0px;
			text-align: left;
		}
	}
}

.map_box {
	// width: 1100px;
	// height: 400px;
	// margin: 0 auto;
	margin-top: 20px;
	margin-bottom: 20px;

	// background: rgb(246, 246, 246);
	// position: relative;
	h4 {
		color: rgb(0, 0, 0);
		font-family: 微软雅黑;
		font-size: 20px;
		font-weight: 400;
		padding: 10px;
		letter-spacing: 0px;
		text-align: left;
		margin: 0;
	}

	.gaode {
		// width: 1200px;
		height: 400px;
		display: flex;
	}
	.content {
		font-size: 16px;
		padding: 10px;
	}
}

.facility_box {
	// height: 400px;
	// margin: 0 auto;
	margin-top: 50px;
	margin-bottom: 20px;

	// background: rgb(246, 246, 246);
	.facility_item {
		display: flex;
		justify-content: space-around;

		h4 {
			width: 85px;
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 21px;
			letter-spacing: 0px;
			text-align: left;
		}

		p {
			width: 912px;
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 21px;
			letter-spacing: 0px;
			text-align: left;
			margin-left: 80px;
		}
	}
}
@media screen and (max-width: 991px) {
	.advertising {
		width: 80%;
	}
}
@media screen and (max-width: 768px) {
	.advertising {
		width: 98%;

		.describe_box {
			.content {
				font-size: 13px;
			}
		}
	}
	.card_box {
		display: none;
	}
	.map_box {
		.content {
			font-size: 13px;
		}
	}
}
</style>
