<template>
	<div class="comparison_box">
		<div class="common_wrap">
			<div class="left_empty_wrap" v-if="tableDataLeft.length == 0">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择资产
				</arco-button>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0" class="left_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('left')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="buildingUniqueCode"
						:columns="tableColumns"
						:data="tableDataLeft"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
			<div v-if="tableDataLeft && tableDataLeft.length > 0 && tableDataRight.length == 0" class="right_empty_wrap">
				<img :src="add" class="icon" />
				<arco-button type="primary" @click="dialogTableVisible = true">
					<template #icon> <icon-plus /> </template>选择对比资产
				</arco-button>
			</div>
			<div v-if="tableDataRight && tableDataRight.length > 0" class="right_content_wrap">
				<div class="title_wrap">
					<div class="left">
						<arco-button type="primary" @click="dialogTableVisible = true">
							<template #icon> <icon-plus /> </template>选择资产
						</arco-button>
					</div>
					<div class="right">
						<arco-button @click="clear('right')"> 清除 </arco-button>
					</div>
				</div>
				<div class="table_wrap">
					<arco-table
						row-key="buildingUniqueCode"
						:columns="tableColumns"
						:data="tableDataRight"
						:pagination="false"
						:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
					>
						<template #columns>
							<arco-table-column title="资产名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
							<arco-table-column title="资产类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
							<arco-table-column title="地址" data-index="street" ellipsis tooltip>
								<template #cell="{ record }">
									{{
										record?.city && record?.district
											? record?.city + record?.district + record?.street
											: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
									}}
								</template>
							</arco-table-column>
							<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
								<template #cell="{ record }">
									{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
								</template>
							</arco-table-column>
							<arco-table-column title="维护情况" :width="90" align="center">
								<template #cell="{ record }">
									<arco-tag style="color: #1868f1" color="#E8F3FF">
										{{ record.maintenance }}
									</arco-tag>
								</template>
							</arco-table-column>
							<arco-table-column title="单价" :width="100" ellipsis tooltip>
								<template #cell="{ record }"> {{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }} </template>
							</arco-table-column>
						</template>
					</arco-table>
				</div>
			</div>
		</div>

		<div
			class="chart_wrap"
			v-if="
				(historyData &&
					historyData.length == 1 &&
					(historyData[0].dealInstance.total > 0 || !$utils.isEmpty(historyData[0].occupancyRate) || !$utils.isEmpty(historyData[0].tradeTips))) ||
				(historyData &&
					historyData.length == 2 &&
					(historyData[0].dealInstance.total > 0 ||
						!$utils.isEmpty(historyData[0].occupancyRate) ||
						!$utils.isEmpty(historyData[0].tradeTips) ||
						historyData[1].dealInstance.total > 0 ||
						!$utils.isEmpty(historyData[1].occupancyRate) ||
						!$utils.isEmpty(historyData[1].tradeTips)))
			"
		>
			<div class="header_wrap">
				<span class="line"></span>
				<span class="title">交易历史</span>
			</div>
			<template v-if="historyData && historyData.length > 1">
				<div class="double_box_wrap">
					<div class="single_wrap">
						<div class="title1">
							<div class="title">出租率与交易实例</div>
						</div>
						<div class="content_wrap" v-if="historyData[0]">
							<div class="content_flex_top">
								<div class="content_left_text">出租率</div>
								<div class="content_right_text">{{ historyData[0].occupancyRate }}%</div>
							</div>
							<arco-table
								:data="leftHisList"
								:scroll="{ x: 1080 }"
								:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
								:pagination="leftPagination"
								@page-change="leftPageSizeChange"
							>
								<template #columns>
									<arco-table-column
										resizable
										v-for="column in columns"
										:key="column.dataIndex"
										:data-index="column.dataIndex"
										:title="column.title"
										:fixed="column.fixed"
										:width="column.width"
									>
										<template #cell="scope">
											<div v-if="column.dataIndex == 'makeBargain'">
												{{ $utils.isEmpty(scope.record.makeBargain) ? '-' : scope.record.makeBargain }}
											</div>
											<div v-else-if="column.dataIndex == 'propertyFee'">
												{{ $utils.isEmpty(scope.record.propertyFee) ? '-' : scope.record.propertyFee }}
											</div>

											<div v-else>
												{{ scope.record[column.dataIndex] ? scope.record[column.dataIndex] : '-' }}
											</div>
										</template>
									</arco-table-column>
								</template>
							</arco-table>
						</div>
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap">
						<div class="title1">
							<div class="title">出租率与交易实例</div>
						</div>
						<div class="content_wrap" v-if="historyData[1]">
							<div class="content_flex_top">
								<div class="content_left_text">出租率</div>
								<div class="content_right_text">{{ historyData[1].occupancyRate }}%</div>
							</div>
							<arco-table
								:data="rightHisList"
								:scroll="{ x: 1080 }"
								:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
								:pagination="rightPagination"
								@page-change="rightPageSizeChange"
							>
								<template #columns>
									<arco-table-column
										resizable
										v-for="column in columns"
										:key="column.dataIndex"
										:data-index="column.dataIndex"
										:title="column.title"
										:fixed="column.fixed"
										:width="column.width"
									>
										<template #cell="scope">
											<div v-if="column.dataIndex == 'makeBargain'">
												{{ $utils.isEmpty(scope.record.makeBargain) ? '-' : scope.record.makeBargain }}
											</div>
											<div v-else-if="column.dataIndex == 'propertyFee'">
												{{ $utils.isEmpty(scope.record.propertyFee) ? '-' : scope.record.propertyFee }}
											</div>

											<div v-else>
												{{ scope.record[column.dataIndex] ? scope.record[column.dataIndex] : '-' }}
											</div>
										</template>
									</arco-table-column>
								</template>
							</arco-table>
						</div>
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
				<div class="double_box_wrap" style="margin-top: 16px">
					<div class="single_wrap" style="min-height: auto">
						<div class="title1">
							<div class="title">交易贴士</div>
						</div>
						<div class="content_wrap" v-if="historyData[0].tradeTips">
							<div class="tips_wrap" v-html="historyData[0].tradeTips.replace(/\n/g, '<br>')"></div>
						</div>
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
					<div class="single_wrap" style="min-height: auto">
						<div class="title1">
							<div class="title">交易贴士</div>
						</div>
						<div class="content_wrap" v-if="historyData[1].tradeTips">
							<div class="tips_wrap" v-html="historyData[1].tradeTips.replace(/\n/g, '<br>')"></div>
						</div>
						<div class="empty_wrap" v-else>
							<img :src="empty" />
							<div>暂无数据</div>
						</div>
					</div>
				</div>
			</template>
			<template v-else>
				<div class="box_wrap">
					<div class="left">
						<div class="single_wrap">
							<div class="title1">
								<div class="title">出租率与交易实例</div>
							</div>
							<div class="content_wrap" v-if="historyData[0]">
								<div class="content_flex_top">
									<div class="content_left_text">出租率</div>
									<div class="content_right_text">{{ historyData[0].occupancyRate }}%</div>
								</div>
								<arco-table
									:data="leftHisList"
									:scroll="{ x: 1080 }"
									:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
									:pagination="leftPagination"
									@page-change="leftPageSizeChange"
								>
									<template #columns>
										<arco-table-column
											resizable
											v-for="column in columns"
											:key="column.dataIndex"
											:data-index="column.dataIndex"
											:title="column.title"
											:fixed="column.fixed"
											:width="column.width"
										>
											<template #cell="scope">
												<div v-if="column.dataIndex == 'makeBargain'">
													{{ $utils.isEmpty(scope.record.makeBargain) ? '-' : scope.record.makeBargain }}
												</div>
												<div v-else-if="column.dataIndex == 'propertyFee'">
													{{ $utils.isEmpty(scope.record.propertyFee) ? '-' : scope.record.propertyFee }}
												</div>

												<div v-else>
													{{ scope.record[column.dataIndex] ? scope.record[column.dataIndex] : '-' }}
												</div>
											</template>
										</arco-table-column>
									</template>
								</arco-table>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
					<div class="right">
						<div class="single_wrap">
							<div class="title1">
								<div class="title">交易贴士</div>
							</div>
							<div class="content_wrap" v-if="historyData[0].tradeTips">
								<div class="tips_wrap" v-html="historyData[0].tradeTips.replace(/\n/g, '<br>')"></div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
				</div>
			</template>
		</div>
		<div class="chart_wrap">
			<div class="header_wrap">
				<span class="line"></span>
				<span class="title">租户分析</span>
			</div>
			<template v-if="buildType && ['写字楼', '产业园区'].includes(buildType)">
				<template v-if="leftData && Object.keys(leftData).length > 0 && rightData && Object.keys(rightData).length > 0">
					<div class="double_box_wrap" style="margin-bottom: 16px">
						<div class="single_wrap">
							<div class="title1">
								<div class="title">租户普查与构成占比</div>
								<div v-if="!left_pie_data" class="download">
									<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
								</div>
								<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.single_wrap'))">
									<icon-download class="download_icon" size="16" />
								</div>
							</div>
							<div class="content_wrap" v-if="left_pie_data">
								<div class="desc_wrap">
									{{ leftText?.text1 }}
									{{ leftText?.text2 }}
									{{ leftText?.text3 }}
								</div>
								<div class="pie_wrap">
									<echartPie ref="leftPieRef" :pieData="left_pie_data" :otherOpts="pieOpts"></echartPie>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
						<div class="single_wrap">
							<div class="title1">
								<div class="title">租户普查与构成占比</div>
								<div v-if="!right_pie_data" class="download">
									<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
								</div>
								<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.single_wrap'))">
									<icon-download class="download_icon" size="16" />
								</div>
							</div>
							<div class="content_wrap" v-if="right_pie_data">
								<div class="desc_wrap">
									{{ rightText?.text1 }}
									{{ rightText?.text2 }}
									{{ rightText?.text3 }}
								</div>
								<div class="pie_wrap">
									<echartPie ref="rightPieRef" :pieData="right_pie_data" :otherOpts="pieOpts"></echartPie>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
					<div class="double_box_wrap">
						<div class="single_wrap">
							<div class="title1">
								<div class="title">详细租户构成</div>
								<div v-if="!left_pie_data" class="download">
									<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
								</div>
								<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.single_wrap'))">
									<icon-download class="download_icon" size="16" />
								</div>
							</div>
							<div class="content_wrap" v-if="left_pie_data">
								<div class="detail_item">
									<div class="item_title">{{ left_pie_data.data[0]?.name }}：{{ left_pie_data.data[0]?.value }}%</div>
									<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituInfoTech" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'telecomBroadcast'">电信广播</div>
										<div class="label_" v-else-if="index === 'internet'">互联网</div>
										<div class="label_" v-else-if="index === 'softInfoTech'">软件和信息技术</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#378EFF' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
								<div class="detail_item">
									<div class="item_title">{{ left_pie_data.data[1]?.name }}：{{ left_pie_data.data[1]?.value }}%</div>
									<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituFinance" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'monetaryFinance'">货币金融</div>
										<div class="label_" v-else-if="index === 'capitalMarket'">资本市场</div>
										<div class="label_" v-else-if="index === 'insurance'">保险业</div>
										<div class="label_" v-else-if="index === 'otherFinance'">其他金融</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#37B7FF' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
								<div class="detail_item">
									<div class="item_title">{{ left_pie_data.data[2].name }}：{{ left_pie_data.data[2].value }}%</div>
									<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituLeaseBusiness" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'lease'">租赁</div>
										<div class="label_" v-else-if="index === 'businessServices'">商务服务</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#39DDE8' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
								<div class="detail_item">
									<div class="item_title">{{ left_pie_data.data[3].name }}：{{ left_pie_data.data[3].value }}%</div>
									<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituWholesaleRetail" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'wholesale'">批发</div>
										<div class="label_" v-else-if="index === 'retail'">零售</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#2FE2AC' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
								<div class="detail_item">
									<div class="item_title">{{ left_pie_data.data[4].name }}：{{ left_pie_data.data[4].value }}%</div>
									<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituOther" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'realEstate'">房地产</div>
										<div class="label_" v-else-if="index === 'fabricate'">制造</div>
										<div class="label_" v-else-if="index === 'transportation'">交通运输</div>
										<div class="label_" v-else-if="index === 'scienceResearch'">科学研究</div>
										<div class="label_" v-else-if="index === 'other'">其他</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#ADE369' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
						<div class="single_wrap">
							<div class="title1">
								<div class="title">详细租户构成</div>
								<div v-if="!right_pie_data" class="download">
									<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
								</div>
								<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.single_wrap'))">
									<icon-download class="download_icon" size="16" />
								</div>
							</div>
							<div class="content_wrap" v-if="right_pie_data">
								<div class="detail_item">
									<div class="item_title">{{ right_pie_data.data[0]?.name }}：{{ right_pie_data.data[0]?.value }}%</div>
									<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituInfoTech" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'telecomBroadcast'">电信广播</div>
										<div class="label_" v-else-if="index === 'internet'">互联网</div>
										<div class="label_" v-else-if="index === 'softInfoTech'">软件和信息技术</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#378EFF' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
								<div class="detail_item">
									<div class="item_title">{{ right_pie_data.data[1]?.name }}：{{ right_pie_data.data[1]?.value }}%</div>
									<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituFinance" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'monetaryFinance'">货币金融</div>
										<div class="label_" v-else-if="index === 'capitalMarket'">资本市场</div>
										<div class="label_" v-else-if="index === 'insurance'">保险业</div>
										<div class="label_" v-else-if="index === 'otherFinance'">其他金融</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#37B7FF' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
								<div class="detail_item">
									<div class="item_title">{{ right_pie_data.data[2].name }}：{{ right_pie_data.data[2].value }}%</div>
									<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituLeaseBusiness" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'lease'">租赁</div>
										<div class="label_" v-else-if="index === 'businessServices'">商务服务</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#39DDE8' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
								<div class="detail_item">
									<div class="item_title">{{ right_pie_data.data[3].name }}：{{ right_pie_data.data[3].value }}%</div>
									<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituWholesaleRetail" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'wholesale'">批发</div>
										<div class="label_" v-else-if="index === 'retail'">零售</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#2FE2AC' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
								<div class="detail_item">
									<div class="item_title">{{ right_pie_data.data[4].name }}：{{ right_pie_data.data[4].value }}%</div>
									<div class="progress_box" v-for="(item, index) in rightData?.tenantConstituOther" :key="item" v-show="index !== 'total'">
										<div class="label_" v-if="index === 'realEstate'">房地产</div>
										<div class="label_" v-else-if="index === 'fabricate'">制造</div>
										<div class="label_" v-else-if="index === 'transportation'">交通运输</div>
										<div class="label_" v-else-if="index === 'scienceResearch'">科学研究</div>
										<div class="label_" v-else-if="index === 'other'">其他</div>
										<div class="progress">
											<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#ADE369' }"></div>
										</div>
										<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
									</div>
								</div>
							</div>
							<div class="empty_wrap" v-else>
								<img :src="empty" />
								<div>暂无数据</div>
							</div>
						</div>
					</div>
					<div class="summary_wrap">
						<img :src="summary_bg" class="bg" />
						<img :src="summary_icon" class="icon" />
						<div class="summary">
							{{ handlerBtnBox() }}
						</div>
						<div class="copy" @click="handlerCopy">复制</div>
					</div>
				</template>
				<template v-else>
					<div class="box_wrap">
						<div class="left">
							<div class="single_wrap">
								<div class="title1">
									<div class="title">租户普查与构成占比</div>
									<div v-if="!left_pie_data" class="download">
										<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
									</div>
									<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.single_wrap'))">
										<icon-download class="download_icon" size="16" />
									</div>
								</div>
								<div class="content_wrap" v-if="left_pie_data">
									<div class="desc_wrap">
										{{ leftText?.text1 }}
										{{ leftText?.text2 }}
										{{ leftText?.text3 }}
									</div>
									<div class="pie_wrap">
										<echartPie ref="leftPieRef" :pieData="left_pie_data" :otherOpts="pieOpts"></echartPie>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>
						</div>
						<div class="right">
							<div class="single_wrap">
								<div class="title1">
									<div class="title">详细租户构成</div>
									<div v-if="!left_pie_data" class="download">
										<icon-download class="download_icon" :style="{ color: '#C9CDD4', cursor: 'not-allowed' }" size="16" />
									</div>
									<div v-else class="download" @click="$utils.downloadEcharts($event.target.closest('.single_wrap'))">
										<icon-download class="download_icon" size="16" />
									</div>
								</div>
								<div class="content_wrap" v-if="left_pie_data">
									<div class="detail_item">
										<div class="item_title">信息技术占比：{{ left_pie_data.data[0]?.value || 0 }}%</div>
										<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituInfoTech" :key="item" v-show="index !== 'total'">
											<div class="label_" v-if="index === 'telecomBroadcast'">电信广播</div>
											<div class="label_" v-else-if="index === 'internet'">互联网</div>
											<div class="label_" v-else-if="index === 'softInfoTech'">软件和信息技术</div>
											<div class="progress">
												<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#378EFF' }"></div>
											</div>
											<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
										</div>
									</div>
									<div class="detail_item">
										<div class="item_title">金融占比：{{ left_pie_data.data[1]?.value || 0 }}%</div>
										<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituFinance" :key="item" v-show="index !== 'total'">
											<div class="label_" v-if="index === 'monetaryFinance'">货币金融</div>
											<div class="label_" v-else-if="index === 'capitalMarket'">资本市场</div>
											<div class="label_" v-else-if="index === 'insurance'">保险业</div>
											<div class="label_" v-else-if="index === 'otherFinance'">其他金融</div>
											<div class="progress">
												<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#37B7FF' }"></div>
											</div>
											<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
										</div>
									</div>
									<div class="detail_item">
										<div class="item_title">租赁和商务服务占比：{{ left_pie_data.data[2]?.value || 0 }}%</div>
										<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituLeaseBusiness" :key="item" v-show="index !== 'total'">
											<div class="label_" v-if="index === 'lease'">租赁</div>
											<div class="label_" v-else-if="index === 'businessServices'">商务服务</div>
											<div class="progress">
												<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#39DDE8' }"></div>
											</div>
											<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
										</div>
									</div>
									<div class="detail_item">
										<div class="item_title">批发和零售占比：{{ left_pie_data.data[3]?.value || 0 }}%</div>
										<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituWholesaleRetail" :key="item" v-show="index !== 'total'">
											<div class="label_" v-if="index === 'wholesale'">批发</div>
											<div class="label_" v-else-if="index === 'retail'">零售</div>
											<div class="progress">
												<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#2FE2AC' }"></div>
											</div>
											<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
										</div>
									</div>
									<div class="detail_item">
										<div class="item_title">其他占比：{{ left_pie_data.data[4]?.value || 0 }}%</div>
										<div class="progress_box" v-for="(item, index) in leftData?.tenantConstituOther" :key="item" v-show="index !== 'total'">
											<div class="label_" v-if="index === 'realEstate'">房地产</div>
											<div class="label_" v-else-if="index === 'fabricate'">制造</div>
											<div class="label_" v-else-if="index === 'transportation'">交通运输</div>
											<div class="label_" v-else-if="index === 'scienceResearch'">科学研究</div>
											<div class="label_" v-else-if="index === 'other'">其他</div>
											<div class="progress">
												<div class="line" :style="{ width: (isNaN(item) ? 0 : Number(item)) + '%', backgroundColor: '#ADE369' }"></div>
											</div>
											<div class="num">{{ isNaN(item) ? 0 : Number(item) }}%</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>
						</div>
					</div>
				</template>
			</template>
			<template v-if="buildType == '零售'">
				<lingshou ref="lingshouRef" :buildData="multipleSelection"></lingshou>
			</template>
			<template v-if="buildType == '酒店'">
				<jiudian ref="jiudianRef" :buildData="multipleSelection"></jiudian>
			</template>
			<template v-if="buildType == '仓储物流'">
				<cangchuwuliu ref="cangchuRef" :buildData="multipleSelection"></cangchuwuliu>
			</template>
			<template v-if="buildType == '医疗'">
				<medical ref="medicalRef" :buildData="multipleSelection"></medical>
			</template>
			<template v-if="buildType == '综合市场'">
				<integratedMarket ref="integratedMarketRef" :buildData="multipleSelection"></integratedMarket>
			</template>
			<template v-if="buildType == '长租公寓'">
				<longApartment ref="longApartmentRef" :buildData="multipleSelection"></longApartment>
			</template>
		</div>
	</div>
	<buildSelect
		key="all"
		v-model="dialogTableVisible"
		:selectedData="multipleSelection"
		:maxSelectNum="2"
		:isTenant="true"
		@confirm="handleBuildConfirm"
	></buildSelect>
	<!-- <buildSelect key="single_left" v-model="dialogSingleLeftVisible" :maxSelectNum="1" @confirm="handleBuildLeftConfirm"></buildSelect>
	<buildSelect key="single_right" v-model="dialogSingleRightVisible" :maxSelectNum="1" @confirm="handleBuildRightConfirm"></buildSelect> -->
	<getReport :dialogVisible="downloadReport" :buildingId="buildingId" ref="getReportRef" @handleRightsClose="handleRightsClose"></getReport>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import { getComparativeTenant, getDictList, getTransactionHistory } from '@/api/syt.js';
import getReport from '../../../../component/getReport/index.vue';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import descBg from '@/assets/images/shangYutong/buildInfo/desc_bg.png';
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import people_icon from '@/assets/images/shangYutong/buildInfo/people_icon.png';
import vs from '@/assets/images/shangYutong/buildInfo/vs.png';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import buildSelect from '@/component/buildSelect/index.vue';
import echartPie from './echart/pie.vue';
import summary_bg from '@/assets/images/shangYutong/buildInfo/summary_bg.png';
import summary_icon from '@/assets/images/shangYutong/buildInfo/summary_icon.png';
import { useStore } from '../../../../store';
import lingshou from './lesseeComponents/lingshou.vue';
import jiudian from './lesseeComponents/jiudian.vue';
import cangchuwuliu from './lesseeComponents/cangchuwuliu.vue';
import medical from './lesseeComponents/medical.vue';
import integratedMarket from './lesseeComponents/integratedMarket.vue';
import longApartment from './lesseeComponents/longApartment.vue';

import rat from '@/RatMap.vue'; //地图
const emit = defineEmits(['handleBuildingId']);
const props = defineProps({
	assetsIds: {
		type: String,
		default: '',
	},
});

const columns = ref([
	{
		title: '公司名称',
		dataIndex: 'companyName',
		width: 220,
		fixed: 'left',
	},
	{
		title: '交易类型',
		dataIndex: 'dealTypeDesc',
		width: 92,
	},
	{
		title: '承租方/买方行业',
		dataIndex: 'purchaserIndustry',
		width: 148,
	},
	{
		title: '人员规模',
		dataIndex: 'staffSize',
		slotName: 'staffSize',
		width: 124,
	},
	{
		title: '交易面积',
		dataIndex: 'floorSpace',
		slotName: 'floorSpace',
		width: 124,
	},
	{
		title: '成交单价',
		dataIndex: 'makeBargain',
		slotName: 'makeBargain',
		width: 124,
	},
	{
		title: '物业费',
		dataIndex: 'propertyFee',
		slotName: 'propertyFee',
		width: 124,
	},
	{
		title: '租期',
		dataIndex: 'rentPeriod',
		width: 124,
	},
	{
		title: '成交时间',
		dataIndex: 'makeTime',
		fixed: 'right',
		width: 124,
	},
]);
const historyData = ref([]);
const leftHisList = ref([]);
const rightHisList = ref([]);
const leftCurrentPage = ref(1);
const rightCurrentPage = ref(1);
const leftPagination = ref({ total: 0 });
const rightPagination = ref({ total: 0 });
const store = useStore();
const getReportRef = ref();
const lingshouRef = ref();
const jiudianRef = ref();
const cangchuRef = ref();
const medicalRef = ref();
const integratedMarketRef = ref();
const longApartmentRef = ref();
const loading = ref();
const dialogTableVisible = ref(false); //对话框显示
const dialogSingleLeftVisible = ref(false); //对话框显示
const dialogSingleRightVisible = ref(false); //对话框显示
const rate = ref([]);
const buildingTypes = ref([]);
const box_copyObj = ref('');
const box_copyObjTwo = ref('');
const leftPieRef = ref();
const rightPieRef = ref();
const multipleSelection = ref([]);
// const multipleSelection = ref([
// 	{
// 		id: '1833701603456532482',
// 	},
// 	// { id: '1833706846806294530' },
// ]);
const leftData = ref({});
const rightData = ref({});
const tableDataLeft = ref([]);
const tableDataRight = ref([]);
const downloadReport = ref(false);
const buildingId = ref(''); //建筑id
const tableColumns = [
	{
		title: '资产名称',
		dataIndex: 'buildingName',
		width: '150',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '资产类型',
		dataIndex: 'buildingType',
		width: '100',
	},
	{
		title: '地址',
		dataIndex: 'street',
		width: '300',
		ellipsis: true,
		tooltip: true,
	},
	{
		title: '建筑面积',
		dataIndex: 'buildingSize',
		width: '100',
	},
	{
		title: '维护情况',
		dataIndex: 'maintenance',
		width: '100',
	},
	{
		title: '单价',
		dataIndex: 'absoluteValue',
		width: '100',
	},
];
let pieOpts = ref({
	grid: [
		{
			width: '50%', // 饼图占 50% 宽度
			left: '0%', // 饼图靠左
			top: '10%', // 饼图顶部距离
			bottom: '10%', // 饼图底部距离
		},
		{
			width: '40%', // 图例占 40% 宽度
			left: '55%', // 图例靠右，距离饼图 5% 的间距
			top: '10%', // 图例顶部距离
			bottom: '10%', // 图例底部距离
		},
	],
	legend: {
		show: true, // 显示 legend
		orient: 'vertical', // 图例排列方向，horizontal 为水平，vertical 为垂直
		left: '60%', // 图例靠右
		top: 'center', // 图例垂直居中
		icon: 'circle', // 将图例小图标设置为圆形
		itemWidth: 8, // 更小的圆点
		itemHeight: 8, // 更小的圆点
		textStyle: {
			color: '#4E5969', // 文字颜色
			fontSize: 14, // 文字大小
		},
	},
});

onMounted(() => {
	if (props.assetsIds) {
		handleOpenFullScreen(); //加载
		handleAssetsIds(props.assetsIds);
		getTransactionHistoryData();
	}
});
//关闭弹出框
function handleRightsClose() {
	downloadReport.value = false;
}

//下载报告
function handleDownload(id, value) {
	buildingId.value = id; //建筑id
	downloadReport.value = true;
	getReportRef.value.hanldeGetReport(value);
}
//加载
const handleOpenFullScreen = () => {
	loading.value = ElLoading.service({
		lock: true,
		text: '加载中',
		customClass: 'loadingComparison',
		background: 'rgba(0, 0, 0, 0.7)',
	});
};
const totalCount = ref(null);
const totalCountTwo = ref(null);
const leftText = ref(null);
const rightText = ref(null);
//获取对比人口
function handleAssetsIds(obj) {
	if (!obj.ids || obj.arr.length == 0) {
		loading.value.close();
		return;
	}
	emit('handleBuildingId', { ids: obj.ids, arr: obj.arr });
	if (obj.arr.length == 2) {
		if (obj.arr[0].buildingType == obj.arr[1].buildingType) {
			multipleSelection.value = obj.arr;
			nextTick(() => {
				if (buildType.value == '零售') {
					if (lingshouRef.value) {
						lingshouRef.value.getData();
					}
				} else if (buildType.value == '酒店') {
					if (jiudianRef.value) {
						jiudianRef.value.getData();
					}
				} else if (buildType.value == '仓储物流') {
					if (cangchuRef.value) {
						cangchuRef.value.getData();
					}
				} else if (buildType.value == '医疗') {
					if (medicalRef.value) {
						medicalRef.value.getData();
					}
				} else if (buildType.value == '综合市场') {
					if (integratedMarketRef.value) {
						integratedMarketRef.value.getData();
					}
				} else if (buildType.value == '长租公寓') {
					if (longApartmentRef.value) {
						longApartmentRef.value.getData();
					}
				}
			});
		} else {
			multipleSelection.value = [];
			loading.value.close();
			return;
		}
	} else {
		multipleSelection.value = obj.arr;
		nextTick(() => {
			lingshouRef.value.getData();
		});
	}
	tableDataLeft.value = multipleSelection.value[0] ? [multipleSelection.value[0]] : [];
	tableDataRight.value = multipleSelection.value[1] ? [multipleSelection.value[1]] : [];
	if (['产业园区', '写字楼'].includes(buildType.value)) {
		getComparativeTenant({
			buildingIds: obj.ids,
		})
			.then((res) => {
				if (res.data && res.data.length == 1) {
					leftText.value = res.data[0].tenantCensusText;
					leftData.value = res.data[0];
					totalCount.value = res.data[0].totalCount;
					rightText.value = null;
					rightData.value = null;
					totalCountTwo.value = null;
					nextTick(() => {
						setData_pie(res.data[0], null);
					});
				} else if (res.data && res.data.length == 2) {
					leftText.value = res.data[0].tenantCensusText;
					leftData.value = res.data[0];
					totalCount.value = res.data[0].totalCount;
					rightText.value = res.data?.[1]?.tenantCensusText;
					rightData.value = res.data?.[1];
					totalCountTwo.value = res.data?.[1]?.totalCount;
					nextTick(() => {
						setData_pie(res.data[0], res.data?.[1]);
					});
				}
			})
			.catch((err) => {
				console.log(err, 'err');
			});
	}
	loading.value.close();
}
// 确定
const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
		tableDataLeft.value = [];
		tableDataRight.value = [];
		left_pie_data.value = null;
		right_pie_data.value = null;
		emit('handleBuildingId', { ids: null, arr: [] });
		return;
	} else {
		tableDataLeft.value = multipleSelection.value[0] ? [multipleSelection.value[0]] : [];
		tableDataRight.value = multipleSelection.value[1] ? [multipleSelection.value[1]] : [];
		comparison();
	}
};
const left_pie_data = ref(null);
const right_pie_data = ref(null);
const comparison = () => {
	left_pie_data.value = null;
	right_pie_data.value = null;
	const idarray = [];
	multipleSelection.value.map((item) => idarray.push(item.id));
	const ids = idarray.join(',');
	getComparativeTenant({
		buildingIds: ids,
	})
		.then((res) => {
			emit('handleBuildingId', { ids: ids, arr: multipleSelection.value });
			if (res.data && res.data.length == 1) {
				leftText.value = res.data[0].tenantCensusText;
				leftData.value = res.data[0];
				totalCount.value = res.data[0].totalCount;
				rightText.value = null;
				rightData.value = null;
				totalCountTwo.value = null;
				nextTick(() => {
					setData_pie(res.data[0], null);
				});
			} else if (res.data && res.data.length == 2) {
				leftText.value = res.data[0].tenantCensusText;
				leftData.value = res.data[0];
				totalCount.value = res.data[0].totalCount;
				rightText.value = res.data?.[1]?.tenantCensusText;
				rightData.value = res.data?.[1];
				totalCountTwo.value = res.data?.[1]?.totalCount;
				nextTick(() => {
					setData_pie(res.data[0], res.data?.[1]);
				});
			}
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
//插入数据
const setData_pie = (left, right) => {
	if (left) {
		let data = [
			{
				value: isNaN(left.tenantConstituInfoTech.total) ? 0 : left.tenantConstituInfoTech.total,
				name: '信息技术占比',
			},
			{
				value: isNaN(left.tenantConstituFinance.total) ? 0 : left.tenantConstituFinance.total,
				name: '金融占比',
			},
			{
				value: isNaN(left.tenantConstituLeaseBusiness.total) ? 0 : left.tenantConstituLeaseBusiness.total,
				name: '租赁和商务服务占比',
			},
			{
				value: isNaN(left.tenantConstituWholesaleRetail.total) ? 0 : left.tenantConstituWholesaleRetail.total,
				name: '批发和零售占比',
			},
			{
				value: isNaN(left.tenantConstituOther.total) ? 0 : left.tenantConstituOther.total,
				name: '其他占比',
			},
		];
		left_pie_data.value = {
			name: multipleSelection.value[0]?.buildingName || '',
			data,
		};
		nextTick(() => {
			leftPieRef.value.init({ center: ['35%', '50%'] });
		});
	}

	if (right) {
		let data = [
			{
				value: isNaN(right.tenantConstituInfoTech.total) ? 0 : right.tenantConstituInfoTech.total,
				name: '信息技术占比',
			},
			{
				value: isNaN(right.tenantConstituFinance.total) ? 0 : right.tenantConstituFinance.total,
				name: '金融占比',
			},
			{
				value: isNaN(right.tenantConstituLeaseBusiness.total) ? 0 : right.tenantConstituLeaseBusiness.total,
				name: '租赁和商务服务占比',
			},
			{
				value: isNaN(right.tenantConstituWholesaleRetail.total) ? 0 : right.tenantConstituWholesaleRetail.total,
				name: '批发和零售占比',
			},
			{
				value: isNaN(right.tenantConstituOther.total) ? 0 : right.tenantConstituOther.total,
				name: '其他占比',
			},
		];
		right_pie_data.value = {
			name: multipleSelection.value[1]?.buildingName || '',
			data,
		};
		nextTick(() => {
			rightPieRef.value.init({ center: ['35%', '50%'] });
		});
	}
};

async function getTransactionHistoryData() {
	const res = await getTransactionHistory({
		buildingIds: multipleSelection.value.map((item) => item.id).join(),
		currentPage: `${leftCurrentPage.value},${rightCurrentPage.value}`,
		pageSize: '10,10',
	});
	if (res.code == 200 && res.data.length > 0) {
		historyData.value = res.data;
		if (res.data.length == 1) {
			leftHisList.value = res.data[0].dealInstance.rows;
			leftPagination.value.total = res.data[0].dealInstance.total;
		} else if (res.data.length == 2) {
			leftHisList.value = res.data[0].dealInstance.rows;
			rightHisList.value = res.data[1].dealInstance.rows;
			leftPagination.value.total = res.data[0].dealInstance.total;
			rightPagination.value.total = res.data[1].dealInstance.total;
		}
	} else {
		historyData.value = [];
		leftHisList.value = [];
		rightHisList.value = [];
		leftPagination.value.total = 0;
		rightPagination.value.total = 0;
	}
}
function leftPageSizeChange(e) {
	leftCurrentPage.value = e;
	getTransactionHistoryData();
}
function rightPageSizeChange(e) {
	rightCurrentPage.value = e;
	getTransactionHistoryData();
}
// 获取字典
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'building_rate' })
		.then((res) => {
			rate.value = res.data;
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	leftCurrentPage.value = 1;
	rightCurrentPage.value = 1;
	save();
	getTransactionHistoryData();
	nextTick(() => {
		if (buildType.value == '零售') {
			if (lingshouRef.value) {
				lingshouRef.value.getData();
			}
		} else if (buildType.value == '酒店') {
			if (jiudianRef.value) {
				jiudianRef.value.getData();
			}
		} else if (buildType.value == '仓储物流') {
			if (cangchuRef.value) {
				cangchuRef.value.getData();
			}
		} else if (buildType.value == '医疗') {
			if (medicalRef.value) {
				medicalRef.value.getData();
			}
		} else if (buildType.value == '综合市场') {
			if (integratedMarketRef.value) {
				integratedMarketRef.value.getData();
			}
		} else if (buildType.value == '长租公寓') {
			if (longApartmentRef.value) {
				longApartmentRef.value.getData();
			}
		}
	});
}
function handleBuildLeftConfirm(data) {
	multipleSelection.value[0] = data[0];
	save();
}
function handleBuildRightConfirm(data) {
	multipleSelection.value[1] = data[0];
	save();
}
function clear(type) {
	leftCurrentPage.value = 1;
	rightCurrentPage.value = 1;
	if (type == 'left') {
		tableDataLeft.value = [];
		multipleSelection.value.shift();
	} else {
		tableDataRight.value = [];
		multipleSelection.value.pop();
	}
	save();
	getTransactionHistoryData();
	nextTick(() => {
		if (buildType.value == '零售') {
			if (lingshouRef.value) {
				lingshouRef.value.getData();
			}
		} else if (buildType.value == '酒店') {
			if (jiudianRef.value) {
				jiudianRef.value.getData();
			}
		} else if (buildType.value == '仓储物流') {
			if (cangchuRef.value) {
				cangchuRef.value.getData();
			}
		} else if (buildType.value == '医疗') {
			if (medicalRef.value) {
				medicalRef.value.getData();
			}
		} else if (buildType.value == '综合市场') {
			if (integratedMarketRef.value) {
				integratedMarketRef.value.getData();
			}
		} else if (buildType.value == '长租公寓') {
			if (longApartmentRef.value) {
				longApartmentRef.value.getData();
			}
		}
	});
}
function handlerCopy() {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(handlerBtnBox())
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = handlerBtnBox();
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

function handlerBtnBox() {
	let obj = rightData.value;
	let obj1 = leftData.value;
	if (!obj || !obj1) return '';
	if (Object.keys(obj).length == 0 || Object.keys(obj1).length == 0) return '';
	if (tableDataRight.value.length == 0 || tableDataLeft.value.length == 0) return '';
	// if (!totalCountTwo.value || !totalCount.value) return '';
	if (totalCountTwo.value > totalCount.value) {
		let name = null;
		let lastName = '';
		let lastNames = '租户构成一级行业';

		name =
			tableDataRight.value[0].buildingName +
			'的累计租户数量较' +
			tableDataLeft.value[0].buildingName +
			'多' +
			(totalCountTwo.value - totalCount.value) +
			'户，';

		if (obj.tenantConstituInfoTech.total > obj1.tenantConstituInfoTech.total) {
			lastName += '招商写字楼租户构成信息技术占比，';
		}

		if (obj.tenantConstituFinance.total > obj1.tenantConstituFinance.total) {
			lastName += '招商写字楼租户构成金融占比，';
		}

		if (obj.tenantConstituLeaseBusiness.total > obj1.tenantConstituLeaseBusiness.total) {
			lastName += '招商写字楼租户构成租赁和商务服务占比，';
		}

		if (obj.tenantConstituWholesaleRetail.total > obj1.tenantConstituWholesaleRetail.total) {
			lastName += '招商写字楼租户构成批发和零售占比，';
		}

		if (obj.tenantConstituOther.total > obj1.tenantConstituOther.total) {
			lastName += '招商写字楼租户构成批发和零售占比';
		}

		if (lastName) {
			lastName += '高于' + tableDataRight.value[0].buildingName + '，租户构成分布更为均衡。';
			return name + lastNames + lastName;
		} else {
			lastName = '占比高于' + tableDataRight.value[0].buildingName + '，租户构成分布更为均衡。';
			return name + lastNames + lastName;
		}
	} else {
		if (totalCountTwo.value === totalCount.value) {
			let name = null;
			let lastName = '';
			let lastNames = '租户构成一级行业';
			name = tableDataLeft.value[0].buildingName + '的累计租户数量与' + tableDataRight.value[0].buildingName + '持平。';

			if (obj1.tenantConstituInfoTech.total > obj.tenantConstituInfoTech.total) {
				lastName += '招商写字楼租户构成信息技术占比，';
			}

			if (obj1.tenantConstituFinance.total > obj.tenantConstituFinance.total) {
				lastName += '招商写字楼租户构成金融占比，';
			}

			if (obj1.tenantConstituLeaseBusiness.total > obj.tenantConstituLeaseBusiness.total) {
				lastName += '招商写字楼租户构成租赁和商务服务占比，';
			}

			if (obj1.tenantConstituWholesaleRetail.total > obj.tenantConstituWholesaleRetail.total) {
				lastName += '招商写字楼租户构成批发和零售占比，';
			}

			if (obj1.tenantConstituOther.total > obj.tenantConstituOther.total) {
				lastName += '招商写字楼租户构成批发和零售占比';
			}

			if (lastName) {
				lastName += '高于' + tableDataRight.value[0].buildingName + '，租户构成分布更为均衡。';
				return name + lastNames + lastName;
			} else {
				lastName = '占比高于' + tableDataRight.value[0].buildingName + '，租户构成分布更为均衡。';
				return name + lastNames + lastName;
			}
		} else {
			let name = null;
			let lastName = '';
			let lastNames = '租户构成一级行业';

			name =
				tableDataLeft.value[0].buildingName +
				'的累计租户数量较' +
				tableDataRight.value[0].buildingName +
				'多' +
				(totalCount.value - totalCountTwo.value) +
				'户，';

			if (obj1.tenantConstituInfoTech.total > obj.tenantConstituInfoTech.total) {
				lastName += '招商写字楼租户构成信息技术占比，';
			}

			if (obj1.tenantConstituFinance.total > obj.tenantConstituFinance.total) {
				lastName += '招商写字楼租户构成金融占比，';
			}

			if (obj1.tenantConstituLeaseBusiness.total > obj.tenantConstituLeaseBusiness.total) {
				lastName += '招商写字楼租户构成租赁和商务服务占比，';
			}

			if (obj1.tenantConstituWholesaleRetail.total > obj.tenantConstituWholesaleRetail.total) {
				lastName += '招商写字楼租户构成批发和零售占比，';
			}

			if (obj1.tenantConstituOther.total > obj.tenantConstituOther.total) {
				lastName += '招商写字楼租户构成批发和零售占比';
			}

			if (lastName) {
				lastName += '高于' + tableDataRight.value[0].buildingName + '，租户构成分布更为均衡。';
				return name + lastNames + lastName;
			} else {
				lastName = '占比高于' + tableDataRight.value[0].buildingName + '，租户构成分布更为均衡。';
				return name + lastNames + lastName;
			}
		}
	}
}

const buildType = computed(() => {
	if (multipleSelection.value && multipleSelection.value.length > 0) {
		return multipleSelection.value[0].buildingType;
	} else {
		return null;
	}
});
</script>
<style lang="less" scoped>
.comparison_box {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	.common_wrap {
		padding: 20px 16px;
		background-color: #fff;
		display: flex;
		gap: 16px;
		border-radius: 0px 4px 4px 4px;
		.left_empty_wrap,
		.left_content_wrap,
		.right_empty_wrap,
		.right_content_wrap {
			flex: 1;
		}
		.left_empty_wrap,
		.right_empty_wrap {
			border: 1px solid #e5e6eb;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-radius: 4px;
			padding: 12px 0;
			.icon {
				width: 64px;
				height: 64px;
			}
		}
		.title_wrap {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px;
		}
	}
	.chart_wrap {
		flex: 1;
		border-radius: 4px;
		margin-top: 16px;
		padding: 20px 16px 16px 16px;
		background-color: #fff;
		.header_wrap {
			display: flex;
			align-items: center;
			margin-bottom: 16px;
			.line {
				width: 4px;
				height: 14px;
				background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
				border-radius: 4px;
			}
			.title {
				color: #1d2129;
				font-size: 20px;
				font-weight: 600;
				margin-left: 8px;
				margin-right: 20px;
				line-height: 28px;
			}
		}
		.double_box_wrap {
			display: flex;
			gap: 16px;
			.single_wrap {
				width: calc(50% - 8px);
				flex: 1;
				box-sizing: border-box;
				min-height: 300px;
				border: 1px solid #e5e6eb;
				border-radius: 4px;
				display: flex;
				flex-direction: column;
				.title1 {
					box-sizing: border-box;
					padding: 0 20px;
					width: 100%;
					height: 48px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #f7f8fa;
					border-bottom: 1px solid #e5e6eb;
					.title {
						font-size: 16px;
						font-weight: 600;
						color: #1d2129;
					}
					.download {
						color: #1868f1;
						.download_icon {
							font-size: 16px;
							cursor: pointer;
						}
					}
				}
				.content_wrap {
					padding: 20px 16px;
					.desc_wrap {
						padding: 16px 20px;
						font-size: 14px;
						color: #4e5969;
						line-height: 22px;
						background: #f7f8fa;
						border-radius: 4px;
					}
					.pie_wrap {
						width: 100%;
						height: 296px;
					}
					.detail_item {
						padding: 16px 20px;
						background-color: #f7f8fa;
						border-radius: 4px;
						display: flex;
						flex-direction: column;
						gap: 12px;
						.item_title {
							font-size: 14px;
							font-weight: 600;
							line-height: 22px;
						}
						.progress_box {
							display: flex;
							align-items: center;
							gap: 16px;
							height: 22px;
							.label_ {
								width: 100px;
								text-align: right;
								font-size: 14px;
								color: #4e5969;
								line-height: 22px;
							}
							.progress {
								flex: 1;
								border: 1px solid #e5e6eb;
								border-radius: 6px;
								height: 12px;
								overflow: hidden;
								.line {
									height: 100%;
								}
							}
							.num {
								width: 64px;
								font-size: 14px;
								font-weight: 600;
								color: #4e5969;
								line-height: 22px;
							}
						}
					}
					.tips_wrap {
						border-top-right-radius: 4px;
						border-bottom-right-radius: 4px;
						padding: 9px 16px;
						font-weight: 400;
						font-size: 14px;
						line-height: 22px;
						color: #4e5969;
						background-color: #f7f8fa;
					}
				}
				.empty_wrap {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: 400;
					color: #86909c;
					img {
						width: 80px;
						height: 80px;
					}
				}
			}
		}
		.box_wrap {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			gap: 16px;
			.left,
			.right {
				width: calc(50% - 8px);
				display: flex;
				flex-direction: column;
				gap: 16px;
			}
			.single_wrap {
				box-sizing: border-box;
				min-height: 300px;
				border: 1px solid #e5e6eb;
				border-radius: 4px;
				display: flex;
				flex-direction: column;
				.title1 {
					box-sizing: border-box;
					padding: 0 20px;
					width: 100%;
					height: 48px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #f7f8fa;
					border-bottom: 1px solid #e5e6eb;
					.title {
						font-size: 16px;
						font-weight: 600;
						color: #1d2129;
					}
					.download {
						color: #1868f1;
						.download_icon {
							font-size: 16px;
							cursor: pointer;
						}
					}
				}
				.content_wrap {
					padding: 20px 16px;
					.desc_wrap {
						padding: 16px 20px;
						font-size: 14px;
						color: #4e5969;
						line-height: 22px;
						background: #f7f8fa;
						border-radius: 4px;
					}
					.pie_wrap {
						width: 100%;
						height: 296px;
					}
					.detail_item {
						padding: 16px 20px;
						background-color: #f7f8fa;
						border-radius: 4px;
						display: flex;
						flex-direction: column;
						gap: 12px;
						.item_title {
							font-size: 14px;
							font-weight: 600;
							line-height: 22px;
						}
						.progress_box {
							display: flex;
							align-items: center;
							gap: 16px;
							height: 22px;
							.label_ {
								width: 100px;
								text-align: right;
								font-size: 14px;
								color: #4e5969;
								line-height: 22px;
							}
							.progress {
								flex: 1;
								border: 1px solid #e5e6eb;
								border-radius: 6px;
								height: 12px;
								overflow: hidden;
								.line {
									height: 100%;
								}
							}
							.num {
								width: 64px;
								font-size: 14px;
								font-weight: 600;
								color: #4e5969;
								line-height: 22px;
							}
						}
					}
					.tips_wrap {
						border-top-right-radius: 4px;
						border-bottom-right-radius: 4px;
						padding: 9px 16px;
						font-weight: 400;
						font-size: 14px;
						line-height: 22px;
						color: #4e5969;
						background-color: #f7f8fa;
					}
				}
				.empty_wrap {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: 400;
					color: #86909c;
					img {
						width: 80px;
						height: 80px;
					}
				}
			}
		}
		.summary_wrap {
			margin-top: 16px;
			position: relative;
			display: flex;
			align-items: center;
			height: 62px;
			padding: 0 20px;
			.bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}
			.icon {
				width: 40px;
				height: 40px;
				z-index: 99;
				margin-right: 12px;
			}
			.summary {
				font-size: 14px;
				font-weight: 400;
				line-height: 22px;
				color: #1d2129;
				z-index: 99;
				margin-right: 70px;
			}
			.copy {
				z-index: 99;
				padding: 5px 16px;
				border: 1px solid #1868f1;
				color: #1868f1;
				background: #e8f3ff;
				border-radius: 4px;
				cursor: pointer;
				position: absolute;
				right: 20px;
			}
		}
	}

	.arco-btn-size-medium {
		border-radius: 4px;
	}
}

.content_flex_top {
	display: flex;
	align-items: center;
	height: 40px;
	width: 100%;
	margin-bottom: 12px;
	.content_left_text {
		width: 116px;
		height: 40px;
		font-weight: 500;
		line-height: 40px;
		text-align: center;
		font-size: 14px;
		color: #1868f1;
		border-top-left-radius: 4px;
		border-bottom-left-radius: 4px;
		background: #e8f3ff;
		margin-right: 2px;
	}
	.content_right_text {
		width: calc(100% - 118px);
		height: 40px;
		background: #f7f8fa;
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
		box-sizing: border-box;
		padding: 9px 16px;
		color: #1d2129;
		font-weight: 500;
		font-size: 14px;
		line-height: 22px;
	}
}
</style>
