<template>
	<div class="mian">
		<div class="search">
			<div class="input_box">
				<input type="text" class="textInput" v-model="searchValue" placeholder="请输入关键字" />
			</div>
			<div class="area_box">
				<div class="title">城市</div>
				<div class="city">
					<span v-for="(city, index) in $vuexStore.state.cityArray" :key="index" @click="onChangeCity(city, index)" class="active">{{
						city.label
					}}</span>
				</div>
				<div class="county">
					<span v-for="(county, index) in provinceList" :key="index" @click="onChangeCounty(county, index)" :class="{ active: activeed == index }">{{
						county.label
					}}</span>
				</div>
			</div>
			<div class="property_box">
				<div class="title">资产</div>
				<div class="property">
					<span
						v-for="(property, index) in buildingTypes"
						:key="index"
						@click="onChangeProperty(property, index)"
						:class="{ active: activeIndex == property }"
						>{{ property }}</span
					>
				</div>
			</div>
			<!-- <div class="type_box">
				<div class="title">顾问类型</div>
				<div class="type">
					<span v-for="(type,index) in counselorList" :key="index" @click="onChangeType(type,index)" :class="{active:activedIndex == type}">{{type}}</span>
				</div>
			</div> -->
			<button @click="onSubmit">确认</button>
		</div>
		<div class="content">
			<el-card v-for="item in data" :key="item.id" class="box-card" @click="onpropertyHome(item)">
				<div class="text item">
					<img :src="`${proxyAddress}${item.companyLogo}`" alt="" />
					<div class="name">
						{{ item.companyName }}
					</div>
				</div>
				<!-- <div class="text item">{{ item.propertyOwner }}</div> -->
			</el-card>
		</div>
		<el-pagination
			:current-page="currentPage"
			:page-size="pageSize"
			@current-change="handlePageChange"
			@size-change="pageSizeChange"
			:page-sizes="[16, 32, 48, 64]"
			:total="total"
			layout="->,prev, pager, next, jumper, sizes,total"
		/>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http';
import { getPropertyList } from '@/api/participant.js';
//  import { provinceAndCityData } from 'element-china-area-data';
import { useRouter } from 'vue-router';
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const activeIndex = ref('');
const rate = ['S', 'A+', 'A', 'B+', 'B', 'C'];
const buildingTypes = ['写字楼', '零售', '产业园区', '仓储物流', '酒店', '长租公寓', '医疗', '综合市场'];
const buildingTypesValue = ref('');
const data = ref([]);
const currentPage = ref(1);
const pageSize = ref(15);
const handlePageChange = (page) => {
	console.log(page, 76);
	currentPage.value = page;
	initData();
};
const essential = ref('');
const router = useRouter();
const total = ref();
onMounted(() => {
	initData();
});
const province = ref('');
const provinceList = ref([]);
const city = ref('');
const activeed = ref(0);

const searchValue = ref('');
const onChangeCity = (val, index) => {
	provinceList.value = val.children;
	city.value = val.label;
	console.log(province.value, 'province.value', city.value, 'city.value');
	console.log(val, 'val23567', index);
	activeed.value = index;
};

const onChangeCounty = (val, index) => {
	console.log(val, 'val23567', index);
	activeed.value = index;
	province.value = val.label;
};
const onChangeProperty = (val, index) => {
	console.log(val, 'val23567', index);
	activeIndex.value = val;
	buildingTypesValue.value = val;
	console.log(buildingTypesValue.value, 'buildingTypesValue1');
};
onChangeProperty(buildingTypes[0], 0);
const onSubmit = () => {
	console.log(123);
	initData();
};
const pageSizeChange = (val) => {
	console.log(val, 'val');
	size.value = val;
	initData(val);
};
const initData = async (val) => {
	const queryParams = {
		city: city.value,
		county: province.value,
		deType: buildingTypesValue.value,
		keywords: searchValue.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		type: 0,
	};
	console.log(buildingTypesValue.value, 'buildingTypesValue1');
	console.log(queryParams.deType, 'queryParams.value1');
	if (buildingTypesValue.value == '零售') {
		queryParams.deType = '购物中心,百货大楼';
		console.log(1);
	}
	if (buildingTypesValue.value == '医疗') {
		queryParams.deType = '医疗康养';
	}
	if (buildingTypesValue.value == '长租公寓') {
		queryParams.deType = '公寓';
	}
	await getPropertyList(queryParams)
		.then((res) => {
			console.log(res, 'ss');
			data.value = res.result.records;
			total.value = res.result.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const onpropertyHome = (item) => {
	const type = 0;
	console.log(123345, item);
	router.push({
		path: '/propertyHome',
		query: {
			companyName: item.companyName,
			type: type,
		},
	});
	// 0产权人
};
</script>

<style lang="less" scoped>
.mian {
	width: 100%;
	height: 30%;
	box-sizing: border-box;
	text-align: center;
	.content {
		padding: 40px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-wrap: wrap;
		.box-card {
			width: 30%;
			height: 150px;
			margin: 15px;
			border-radius: 25px;
			// display: flex;
			align-content: center;
			box-sizing: border-box;
			border: 1px solid rgb(205, 205, 205);

			background: rgb(250, 250, 250);
			// padding:  10px 0;
			padding: 0;
			.item {
				display: flex;
				align-items: center;
				img {
					width: 100px;
					height: 100px;
				}
				.name {
					height: 107px;
					display: flex;
					flex-direction: column;
					justify-content: space-around;
					// align-items: flex-start;

					text-align: left;
					margin-left: 16px;
				}
			}
		}
	}
}
.el-row {
	margin-bottom: 20px;
}
.el-row:last-child {
	margin-bottom: 0;
}
.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 36px;
}
.text {
	font-size: 14px;
}

.item {
	padding: 18px 0;
}

.box-card {
	width: 480px;
}
.search {
	width: 100%;
	height: 235px;
	background: rgb(250, 250, 250);
	padding: 30px 0;
	position: relative;
	button {
		width: 85px;
		height: 36px;
		border-radius: 2px;
		background: rgb(56, 96, 154);
		color: rgb(255, 255, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 21px;
		letter-spacing: 0px;
		// text-align: center;
		position: absolute;
		right: 20px;
		border: none;
	}
	.active {
		box-sizing: border-box;
		border: 1px solid rgb(64, 158, 255);
		border-radius: 2px;

		background: rgb(241, 248, 255);
		color: rgb(64, 158, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 23px;
		letter-spacing: 0px;
		text-align: left;
	}
	.area_box {
		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}
		.city {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin: 15px 0;
			margin-bottom: 15px;
			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
		.county {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin: 15px 0;
			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}
	.property_box {
		margin-top: 15px;
		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}
		.property {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}
	.type_box {
		margin-top: 15px;
		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}
		.type {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}
}
.input_box {
	display: flex;
	align-items: center;
	width: 300px !important;
	margin-right: 20px;
	.textInput {
		width: 228px;
		height: 36px;
	}
	.searchBtn {
		width: 67px;
		height: 36px;
		border-radius: 2px;
		border: none;
		background: rgb(64, 158, 255);
		margin-left: 10px;
		color: rgb(255, 255, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 21px;
		letter-spacing: 0px;
		// text-align: left;
	}
}
</style>
