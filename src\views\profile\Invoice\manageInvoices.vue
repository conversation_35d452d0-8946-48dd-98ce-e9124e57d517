<template>
	<el-dialog
		v-model="props.dialogInvoiceRowVisible"
		:close-on-click-modal="false"
		:show-close="false"
		align-center="center"
		:before-close="handleClose"
		style="width: 560px"
	>
		<template #header>
			<div class="dialogHeader">
				<div class="dialogHeaderLeft">
					<el-icon><ArrowLeftBold @click="handleClose" /></el-icon>
					<div>管理发票抬头</div>
				</div>

				<div class="dialogHeaderRight">
					<el-icon><CloseBold @click="handleClose" /></el-icon>
				</div>
			</div>
		</template>
		<div class="form_content">
			<div class="content_Flex" style="margin-bottom: 16px">
				<el-select v-model="formLabelAlign.headerType" @change="handleSearch" placeholder="全部抬头类型" style="width: 130px">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>

				<div class="content_search">
					<el-dropdown trigger="click">
						<span class="el-dropdown-link" style="cursor: pointer; font-size: 12px">
							{{ searchValue }}<el-icon><CaretBottom /></el-icon>
						</span>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item @click="handleItemClick('搜索发票抬头')">搜索发票抬头</el-dropdown-item>
								<el-dropdown-item @click="handleItemClick('搜索税号')">搜索税号</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>

					<el-input
						v-show="searchValue === '搜索发票抬头'"
						v-model="formLabelAlign.headerName"
						style="max-width: 160px; margin-left: 16px"
						clearable
						@clear="handleSearch"
						placeholder="搜索"
						class="input_select"
					>
						<template #append
							><el-icon style="cursor: pointer"><Search @click="handleSearch" /></el-icon>
						</template>
					</el-input>

					<el-input
						v-show="searchValue !== '搜索发票抬头'"
						v-model="formLabelAlign.taxNum"
						style="max-width: 160px; margin-left: 16px"
						placeholder="搜索"
						@clear="handleSearch"
						clearable
						class="input_select"
					>
						<template #append
							><el-icon style="cursor: pointer"><Search @click="handleSearch" /></el-icon>
						</template>
					</el-input>
				</div>
			</div>

			<div class="container_bottom_content" @scroll="handleScroll">
				<div
					v-for="(item, index) in lookUpData"
					:key="index"
					:class="item.defaultHeader === '1' ? 'active_container content_container' : 'content_container'"
				>
					<div class="content_Flex content_top">
						<div class="content_name" :title="item.headerName">{{ item.headerName }}</div>
						<el-divider direction="vertical" />
						<div class="content_title">{{ item.headerType_dictText }}</div>
						<el-divider direction="vertical" />
						<div class="content_end" :title="item.taxNum">税号 {{ item.taxNum }}</div>
						<div class="content_more">
							<el-popover placement="bottom" :teleported="false" width="110" trigger="hover" ref="popoverRef">
								<template #reference>
									<div class="head">
										<el-icon><MoreFilled /></el-icon>
									</div>
								</template>
								<div class="popover_box">
									<div class="userName" @click="handleSetDefault(item)">
										设为默认抬头<img v-if="item.defaultHeader === '1'" src="@/assets/faciltyImg.png" alt="" />
									</div>
									<div class="personal" @click="handleClose(2, item.id)">编辑</div>
									<div class="exitlogon" @click="handleDelete(item.id)">删除</div>
								</div>
							</el-popover>
						</div>
					</div>
					<div class="content_Flex">
						<div class="content_Flexs">
							<div class="content_img"><img src="../../../assets/bankAccount.png" alt="" /></div>
							<div class="content_titleT">银行账户</div>
							<div class="content_group">{{ item.bankAccount }}</div>
						</div>
						<div class="content_bank" :title="item.openBank">开户行:{{ item.openBank }}</div>
					</div>
					<div class="content_Flex">
						<div class="content_Flexs">
							<div class="content_img"><img src="../../../assets/phoneNumber.png" alt="" /></div>
							<div class="content_titleT">手机号</div>
							<div class="content_group">{{ item.registerPhone }}</div>
						</div>
						<div class="content_bank" :title="item.address">地址:{{ item.address }}</div>
					</div>
				</div>
			</div>
		</div>
		<template #footer>
			<div class="dialog_footer">
				<div>
					<el-button @click="handleClose(1)"> 新增 </el-button>
				</div>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';
import { getInvoicePage, editInvoice, deleteInvoice } from '@/api/equityTerm.js';
import { ElMessage } from 'element-plus';
const emit = defineEmits();
const props = defineProps({
	dialogInvoiceRowVisible: {
		type: Boolean,
		default: false,
	},
	activeObj: {
		type: Object,
		default: () => {},
	},
});

//抬头
const lookUpData = ref([]);
//搜索条件切换
const searchValue = ref('搜索发票抬头');
const options = [
	{
		value: '',
		label: '全部',
	},
	{
		value: '1',
		label: '企业单位',
	},
	{
		value: '2',
		label: '个人/非企业单位',
	},
];
const formLabelAlign = ref({
	headerType: null,
	headerName: null,
	taxNum: null,
});

let tableDataTerm = ref({
	total: 0,
	page: {
		pageNo: 1,
		pageSize: 10,
	},
});

watch(
	() => props.dialogInvoiceRowVisible,
	(newVal) => {
		if (newVal) {
			tableDataTerm.value.page.pageNo = 1;
			handlelookUpList();
		}
	}
);

function handleScroll(event) {
	const { scrollTop, clientHeight, scrollHeight } = event.target;
	if (scrollTop + clientHeight >= scrollHeight) {
		if (tableDataTerm.value.page.pageNo * tableDataTerm.value.page.pageSize >= tableDataTerm.value.total) {
			return;
		}
		tableDataTerm.value.page.pageNo++;
		handlelookUpList('1');
		// 滚动条到达底部，触发懒加载
	}
}
function handlelookUpList(type) {
	getInvoicePage({ ...tableDataTerm.value.page, userId: props.activeObj.userId, ...formLabelAlign.value }).then((res) => {
		if (res.code === 200) {
			if (type) {
				lookUpData.value = [...lookUpData.value, ...res.result.records];
			} else {
				lookUpData.value = res.result.records;
			}
			tableDataTerm.value.total = res.result.total;
		}
	});
}
// 设置默认抬头
function handleSetDefault(params) {
	editInvoice({
		...params,
		defaultHeader: 1,
	}).then((res) => {
		if (res.code === 200) {
			ElMessage({
				message: '修改成功',
				type: 'success',
			});
			tableDataTerm.value.page.pageNo = 1;
			handlelookUpList();
		}
	});
}

// 关闭对话框
function handleClose(type, id) {
	emit('handleInvoiceClose', { type: type === 1 || type === 2 ? type : 0, id: id });
}
// 删除
function handleDelete(id) {
	deleteInvoice({ id: id }).then((res) => {
		if (res.code === 200) {
			//成功提示
			ElMessage({
				message: '删除成功',
				type: 'success',
			});
			tableDataTerm.value.page.pageNo = 1;
			handlelookUpList();
		}
	});
}

// 搜索条件
function handleItemClick(value) {
	searchValue.value = value;
	if (value === '搜索发票抬头') {
		formLabelAlign.value.taxNum = null;
	} else {
		formLabelAlign.value.headerName = null;
	}
	handleSearch();
}

// 搜索区监听
function handleSearch() {
	if (!formLabelAlign.value.headerType) {
		formLabelAlign.value.headerType = null;
	}
	tableDataTerm.value.page.pageNo = 1;
	handlelookUpList();
}
</script>

<style lang="scss" scoped>
.dialogHeader {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
	height: 56px;
	line-height: 56px;
	padding: 0 16px;
	font-size: 16px;
	color: rgba(29, 33, 41, 1);
	border-bottom: 1px solid rgba(231, 231, 231, 1);
	.el-icon {
		cursor: pointer;
	}

	.dialogHeaderLeft {
		display: flex;
		align-items: center;
	}
}

.form_content {
	padding: 0 16px;
	margin-bottom: -16px;
	.content_top {
		background: #f5f6f7;
		padding: 0 10px;
		border-top-left-radius: 6px;
		border-top-right-radius: 6px;
	}

	.container_bottom_content {
		overflow: scroll;
		height: 400px;
	}
	.content_container {
		width: calc(100% - 2px);
		height: 128pxpx;
		border-radius: 6px;
		border: 1px solid #e7e7e7;
		margin-bottom: 10px;
	}
	.active_container {
		border: 1px solid #1868f1;
	}
	.content_Flexs {
		display: flex;
		align-items: center;
		padding-left: 10px;
		width: 292px;
		.content_img {
			line-height: 40px;
			display: flex;
			align-items: center;
			margin-top: -0.2px;
		}
		.content_titleT {
			width: 48px;
			margin: 0 4px;
			font-size: 12px;
			font-weight: 400;
			line-height: 40px;
			color: #c9cdd4;
		}

		.content_group {
			font-size: 14px;
			font-weight: 400;
			line-height: 40px;
			color: #4e5969;
		}
	}

	.content_bank {
		font-size: 12px;
		font-weight: 400;
		line-height: 40px;
		color: #86909c;
		padding-right: 10px;
		width: calc(100% - 313px);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.content_Flex {
		display: flex;
		align-items: center;
		justify-content: space-between;
		.content_search {
			display: flex;
			align-items: center;
		}

		.content_name {
			width: 133px;
			height: 40px;
			font-size: 14px;
			font-weight: 500;
			line-height: 40px;
			color: #1d2129;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.content_title {
			width: 133px;
			height: 40px;
			font-size: 12px;
			font-weight: 400;
			line-height: 40px;
			color: #86909c;
		}

		.content_end {
			width: 180px;
			height: 40px;
			font-size: 12px;
			font-weight: 400;
			line-height: 40px;
			color: #86909c;
			margin-right: 10px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.content_more {
			width: 32px;
			height: 40px;
			display: flex;
			justify-content: center;
			align-items: center;
			.el-icon {
				cursor: pointer;
			}
		}
	}
}

.dialog_footer {
	height: 74px;
	width: calc(100% - 16px);
	border-top: 1px solid rgba(231, 231, 231, 1);
	display: flex;
	align-items: center;
	justify-content: end;
	padding-right: 16px;
	button {
		height: 34px;
		width: 131px;
	}
}

.popover_box {
	display: flex;
	flex-direction: column;
	border-radius: 15px;

	.userName {
		cursor: pointer;
		font-size: 12px;
		font-weight: 500;
		line-height: 24px;
		color: #1d2129;
		padding: 8px;
		display: flex;
		align-items: center;
		&:hover {
			background: #f5f6f7;
		}
		img {
			margin-left: 6px;
		}
	}

	.personal {
		cursor: pointer;
		margin-top: 8px;
		height: 20px;
		padding: 8px;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: left;
		background: #fff;
		border-radius: 4px;
		color: #1d2129;
		&:hover {
			background: #f5f6f7;
		}
	}

	.exitlogon {
		cursor: pointer;
		margin-top: 8px;
		height: 20px;
		padding: 8px;
		font-size: 12px;
		font-weight: 400;
		line-height: 20px;
		text-align: left;
		background: #fff;
		border-radius: 4px;
		color: #ec655f;
		&:hover {
			background: #f5f6f7;
		}
	}
}
::v-deep .el-popper {
	min-width: 130px !important;
	padding: 8px !important;
}
</style>
