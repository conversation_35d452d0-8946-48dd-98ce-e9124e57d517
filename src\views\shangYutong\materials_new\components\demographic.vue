<template>
	<div class="comparison_box">
		<div class="container_box">
			<div class="common_wrap">
				<div class="left_empty_wrap" v-if="tableDatao.length == 0">
					<img :src="add" class="icon" />
					<arco-button type="primary" @click="dialogTableVisible = true">
						<template #icon> <icon-plus /> </template>选择项目
					</arco-button>
				</div>
				<div v-if="tableDatao && tableDatao.length > 0" class="left_content_wrap">
					<div class="title_wrap">
						<div class="left">
							<arco-button type="primary" @click="dialogTableVisible = true">
								<template #icon> <icon-plus /> </template>选择项目
							</arco-button>
						</div>
						<div class="right">
							<arco-button @click="clear('left')"> 清除 </arco-button>
						</div>
					</div>
					<div class="table_wrap">
						<arco-table
							row-key="id"
							:data="tableDatao"
							:pagination="false"
							:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
						>
							<template #columns>
								<arco-table-column title="项目名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
								<arco-table-column title="项目类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
								<arco-table-column title="地址" data-index="street" ellipsis tooltip>
									<template #cell="{ record }">
										{{
											record?.city && record?.district
												? record?.city + record?.district + record?.street
												: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
										}}
									</template>
								</arco-table-column>
								<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
									<template #cell="{ record }">
										{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
									</template>
								</arco-table-column>
								<arco-table-column title="维护情况" :width="90" align="center">
									<template #cell="{ record }">
										<arco-tag style="color: #1868f1" color="#E8F3FF">
											{{ record.maintenance }}
										</arco-tag>
									</template>
								</arco-table-column>
								<arco-table-column title="单价" :width="100" ellipsis tooltip>
									<template #cell="{ record }">
										{{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }}
									</template>
								</arco-table-column>
							</template>
						</arco-table>
					</div>
				</div>
				<div v-if="tableDatao && tableDatao.length > 0 && tableDatat.length == 0" class="right_empty_wrap">
					<img :src="add" class="icon" />
					<arco-button type="primary" @click="dialogTableVisible = true">
						<template #icon> <icon-plus /> </template>选择项目对比
					</arco-button>
				</div>
				<div v-if="tableDatat && tableDatat.length > 0" class="right_content_wrap">
					<div class="title_wrap">
						<div class="left">
							<arco-button type="primary" @click="dialogTableVisible = true">
								<template #icon> <icon-plus /> </template>选择项目
							</arco-button>
						</div>
						<div class="right">
							<arco-button @click="clear('right')"> 清除 </arco-button>
						</div>
					</div>
					<div class="table_wrap">
						<arco-table
							row-key="id"
							:data="tableDatat"
							:pagination="false"
							:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
						>
							<template #columns>
								<arco-table-column title="项目名称" data-index="buildingName" ellipsis tooltip :width="110"></arco-table-column>
								<arco-table-column title="项目类型" data-index="buildingType" ellipsis tooltip :width="90"></arco-table-column>
								<arco-table-column title="地址" data-index="street" ellipsis tooltip>
									<template #cell="{ record }">
										{{
											record?.city && record?.district
												? record?.city + record?.district + record?.street
												: record?.buildingCity + record?.buildingDistrict + record?.buildingStreet
										}}
									</template>
								</arco-table-column>
								<arco-table-column title="建筑面积" :width="100" ellipsis tooltip>
									<template #cell="{ record }">
										{{ record?.buildingSize ? formattedMoney(record.buildingSize, 2) + '㎡' : '' }}
									</template>
								</arco-table-column>
								<arco-table-column title="维护情况" :width="90" align="center">
									<template #cell="{ record }">
										<arco-tag style="color: #1868f1" color="#E8F3FF">
											{{ record.maintenance }}
										</arco-tag>
									</template>
								</arco-table-column>
								<arco-table-column title="单价" :width="100" ellipsis tooltip>
									<template #cell="{ record }">
										{{ record?.absoluteValue ? formattedMoney(handleNumber(record.absoluteValue)) + '元' : '' }}
									</template>
								</arco-table-column>
							</template>
						</arco-table>
					</div>
				</div>
			</div>

			<!-- 对比图 -->
			<div class="echars_box">
				<div class="echars_main">
					<div class="box_ss">
						<div class="title1">重叠客群画像</div>
					</div>
				</div>
				<div class="summary_wrap" style="margin-bottom: 16px" v-if="tableDatao.length > 0 && tableDatat.length > 0">
					<img :src="summary_bg" class="bg" />
					<img :src="summary_icon" class="icon" />
					<div class="summary">
						{{ dataObject.formatDescription }}
					</div>
					<div class="copy" @click="handlerCopy(dataObject.formatDescription)">复制</div>
				</div>
				<div class="population_wrap">
					<div class="box_wrap">
						<div class="left">
							<div class="single_wrap">
								<div class="title1">
									<div class="title">重叠客群占比</div>
								</div>
								<div class="content_wrap" v-if="tableDatao.length > 0 && tableDatat.length > 0">
									<div class="venn-diagram-container">
										<div class="venn-diagram">
											<!-- 左圆代表本项目 -->
											<div class="circle left-circle" :style="leftCircleStyle">
												<span class="circle-text">本项目</span>
											</div>
											<!-- 右圆代表竞品 -->
											<div class="circle right-circle" :style="rightCircleStyle">
												<span class="circle-text">竞品</span>
											</div>
											<div class="overlap-text">重 叠 客 群</div>
										</div>

										<div class="statistics">
											<div class="stat-item">
												<div class="percentage">{{ leftPercentage }}<span class="percentage-symbol">%</span></div>
												<div class="description">重叠客群占本项目比例</div>
											</div>
											<div class="stat-item">
												<div class="percentage">{{ rightPercentage }}<span class="percentage-symbol">%</span></div>
												<div class="description">重叠客群占竞品比例</div>
											</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>
						</div>
						<div class="right">
							<div class="single_wrap">
								<div class="title1">
									<div class="title">重叠客群业态偏好</div>
								</div>
								<div class="content_wrap" v-if="tableDatao.length > 0 && tableDatat.length > 0">
									<div class="progress_wrap_box">
										<div class="progress_wrap_box_item" v-for="(item, index) in businessFormatList" :key="index">
											<div class="progress_wrap_box_item_left">{{ item.name }}</div>
											<div class="progress_wrap_box_item_center">
												<div
													class="progress_wrap_box_item_center_item_left"
													:style="{ width: Number(item.value) + '%', background: item.color }"
												></div>
												<div
													class="progress_wrap_box_item_center_item_right"
													:style="{
														width: 100 - Number(item.value) + '%',
														borderTopLeftRadius: Number(item.value) > 0 ? '0px' : '10px',
														borderBottomLeftRadius: Number(item.value) > 0 ? '0px' : '10px',
													}"
												></div>
											</div>
											<div class="progress_wrap_box_item_right">{{ item.value + '%' }}</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 对比图 -->
			<div class="echars_box">
				<div class="echars_main">
					<div class="box_ss">
						<div class="title1">详细客群画像</div>
					</div>
				</div>
				<div class="population_wrap">
					<div class="box_wrap">
						<div class="left">
							<div class="single_wrap">
								<div class="title1">
									<div class="title">性别分布</div>
								</div>
								<div class="content_wrap" v-if="tableDatao.length > 0 && tableDatat.length > 0">
									<div class="gender_wrap">
										<div class="gender_wrap_top">
											<div style="width: 132px; height: 50px"></div>
											<div class="gender_wrap_top_right">
												<div class="gender_wrap_top_right_item">
													<img src="@/assets/genderMan.png" alt="" />
												</div>
												<div class="gender_wrap_top_right_item">
													<img src="@/assets/genderWuman.png" alt="" />
												</div>
											</div>
										</div>
										<div class="gender_wrap_bottom">
											<div class="gender_wrap_bottom_item" v-if="tableDatao.length > 0">
												<div class="gender_wrap_bottom_item_left">{{ tableDatao[0].buildingName }}</div>
												<div class="gender_wrap_bottom_item_right">
													<div class="gender_item">
														<div class="gender_item_left" :style="{ width: Number(dataObject.selfGenderMale) + '%' }"></div>
														<div class="gender_item_right" :style="{ width: Number(dataObject.selfGenderFemale) + '%' }"></div>
													</div>
													<div class="gender_item_bottom">
														<div class="gender_item_bottom_left">{{ Number(dataObject.selfGenderMale).toFixed(2) }}%</div>
														<div class="gender_item_bottom_right">{{ Number(dataObject.selfGenderFemale).toFixed(2) }}%</div>
													</div>
												</div>
											</div>

											<div class="gender_wrap_bottom_item" v-if="tableDatat.length > 0">
												<div class="gender_wrap_bottom_item_left">{{ tableDatat[0].buildingName }}</div>
												<div class="gender_wrap_bottom_item_right">
													<div class="gender_item">
														<div class="gender_item_left" :style="{ width: Number(dataObject.otherGenderMale) + '%' }"></div>
														<div class="gender_item_right" :style="{ width: Number(dataObject.otherGenderFemale) + '%' }"></div>
													</div>
													<div class="gender_item_bottom">
														<div class="gender_item_bottom_left">{{ Number(dataObject.otherGenderMale).toFixed(2) }}%</div>
														<div class="gender_item_bottom_right">{{ Number(dataObject.otherGenderFemale).toFixed(2) }}%</div>
													</div>
												</div>
											</div>

											<div class="gender_wrap_bottom_item">
												<div class="gender_wrap_bottom_item_left">两者重叠客群</div>
												<div class="gender_wrap_bottom_item_right">
													<div class="gender_item">
														<div class="gender_item_left" :style="{ width: Number(dataObject.overlapGenderMale) + '%' }"></div>
														<div class="gender_item_right" :style="{ width: Number(dataObject.overlapGenderFemale) + '%' }"></div>
													</div>
													<div class="gender_item_bottom">
														<div class="gender_item_bottom_left">{{ Number(dataObject.overlapGenderMale).toFixed(2) }}%</div>
														<div class="gender_item_bottom_right">{{ Number(dataObject.overlapGenderFemale).toFixed(2) }}%</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>

							<div class="single_wrap">
								<div class="title1">
									<div class="title">是否有房</div>
								</div>
								<div class="content_wrap" v-if="tableDatao.length > 0 && tableDatat.length > 0">
									<div class="echart_wrap_box">
										<div class="echart_wrap">
											<echartPie ref="echartPie1" :pieData="pieData"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie2" :pieData="pieData2"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie3" :pieData="pieData3"></echartPie>
										</div>
									</div>

									<div class="legend_wrap">
										<div class="legend_item" v-for="(item, index) in pieData.data" :key="index">
											<div class="circle_wrap" :style="{ background: item.color }"></div>
											<div class="name">{{ item.name }}</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>
							<div class="single_wrap">
								<div class="title1">
									<div class="title">教育情况</div>
								</div>
								<div class="content_wrap" v-if="tableDatao.length > 0 && tableDatat.length > 0">
									<div class="echart_wrap_box">
										<div class="echart_wrap">
											<echartPie ref="echartPie4" :pieData="pieData4"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie5" :pieData="pieData5"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie6" :pieData="pieData6"></echartPie>
										</div>
									</div>

									<div class="legend_wrap">
										<div class="legend_item" v-for="(item, index) in pieData4.data" :key="index">
											<div class="circle_wrap" :style="{ background: item.color }"></div>
											<div class="name">{{ item.name }}</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>
							<div class="single_wrap">
								<div class="title1">
									<div class="title">客群结构</div>
								</div>
								<div class="content_wrap" v-if="tableDatao.length > 0 && tableDatat.length > 0">
									<div class="echart_wrap_box">
										<div class="echart_wrap">
											<echartPie ref="echartPie7" :pieData="pieData7"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie8" :pieData="pieData8"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie9" :pieData="pieData9"></echartPie>
										</div>
									</div>

									<div class="legend_wrap">
										<div class="legend_item" v-for="(item, index) in pieData7.data" :key="index">
											<div class="circle_wrap" :style="{ backgroundColor: item.color }"></div>
											<div class="name">{{ item.name }}</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>
						</div>
						<div class="right">
							<div class="single_wrap">
								<div class="title1">
									<div class="title">家庭收入</div>
								</div>
								<div class="content_wrap" v-show="tableDatao.length > 0 && tableDatat.length > 0">
									<div id="myChart" class="charts"></div>
									<div class="chart_content">
										<div class="legend_item" v-for="(item, index) in reitsTypeList" :key="index">
											<div class="circle_wrap" :style="{ background: item.color }"></div>
											<div class="name">{{ item.name }}</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-show="tableDatao.length == 0 || tableDatat.length == 0">
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>

							<div class="single_wrap">
								<div class="title1">
									<div class="title">年龄情况</div>
								</div>
								<div class="content_wrap" v-if="tableDatao.length > 0 && tableDatat.length > 0">
									<div class="echart_wrap_box">
										<div class="echart_wrap">
											<echartPie ref="echartPie10" :pieData="pieData10"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie11" :pieData="pieData11"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie12" :pieData="pieData12"></echartPie>
										</div>
									</div>

									<div class="legend_wrap">
										<div class="legend_item" v-for="(item, index) in pieData10.data" :key="index">
											<div class="circle_wrap" :style="{ background: item.color }"></div>
											<div class="name">{{ item.name }}</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>

							<div class="single_wrap">
								<div class="title1">
									<div class="title">职业情况</div>
								</div>
								<div class="content_wrap" v-if="tableDatao.length > 0 && tableDatat.length > 0">
									<div class="echart_wrap_box">
										<div class="echart_wrap">
											<echartPie ref="echartPie13" :pieData="pieData13"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie14" :pieData="pieData14"></echartPie>
										</div>
										<div class="echart_wrap">
											<echartPie ref="echartPie15" :pieData="pieData15"></echartPie>
										</div>
									</div>

									<div class="legend_wrap">
										<div class="legend_item" v-for="(item, index) in pieData13.data" :key="index">
											<div class="circle_wrap" :style="{ background: item.color }"></div>
											<div class="name">{{ item.name }}</div>
										</div>
									</div>
								</div>
								<div class="empty_wrap" v-else>
									<img :src="empty" />
									<div>暂无数据</div>
								</div>
							</div>
						</div>
					</div>
					<div class="summary_wrap" style="margin-top: 16px" v-if="tableDatao.length > 0 && tableDatat.length > 0">
						<img :src="summary_bg" class="bg" />
						<img :src="summary_icon" class="icon" />
						<div class="summary">
							{{ dataObject.structDescription }}
						</div>
						<div class="copy" @click="handlerCopy(dataObject.structDescription)">复制</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<buildSelect
		key="all"
		v-model="dialogTableVisible"
		:maxSelectNum="2"
		:selectedData="multipleSelection"
		buildingTypelt="2"
		title="选择项目"
		:searchTypes="['position', 'rate', 'keyword']"
		@confirm="handleBuildConfirm"
	></buildSelect>
</template>

<script setup>
import empty from '@/assets/images/shangYutong/buildInfo/empty.png';
import echartPie from '@/views/shangYutong/materials_new/components/echart/pie.vue';
import buildSelect from '@/component/buildSelect/index.vue';
import { formattedMoney } from 'UTILS'; // 千分符
import { handleNumber } from '../../../../utils/index';
import add from '@/assets/images/shangYutong/buildInfo/add.png';
import summary_bg from '@/assets/images/shangYutong/buildInfo/summary_bg.png';
import summary_icon from '@/assets/images/shangYutong/buildInfo/summary_icon.png';
import { ref, onMounted, computed, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getPopulationTrend } from '@/api/syt.js';
let myChart1;

// 数据对象
const dataObject = ref({});

const dialogTableVisible = ref(false); //对话框显示

const multipleSelection = ref([]); //选中的数据
const echartPie1 = ref();
const echartPie2 = ref();
const echartPie3 = ref();
const echartPie4 = ref();
const echartPie5 = ref();
const echartPie6 = ref();
const echartPie7 = ref();
const echartPie8 = ref();
const echartPie9 = ref();
const echartPie10 = ref();
const echartPie11 = ref();
const echartPie12 = ref();
const echartPie13 = ref();
const echartPie14 = ref();
const echartPie15 = ref();

const businessFormatList = ref([
	{ name: '餐饮', color: '#378EFF', key: 'catering', value: 49.1 },
	{ name: '零售', color: '#37B7FF', key: 'retail', value: 21.58 },
	{ name: '服务配套', color: '#39DDE8', key: 'facility', value: 21.57 },
	{ name: '休闲娱乐', color: '#2FE2AC', key: 'leisure', value: 11.2 },
	{ name: '亲子', color: '#63DC81', key: 'parenting', value: 4.35 },
]);

const pieData1 = ref({
	name: '机构特色',
	data: [
		{ name: '有房', key: 'selfHasProperty', value: 20 },
		{ name: '无房', key: 'selfNoProperty', value: 40 },
	],
});

const pieData = ref({});
const pieData2 = ref({});
const pieData3 = ref({});
const pieData4 = ref({});
const pieData5 = ref({});
const pieData6 = ref({});
const pieData7 = ref({});
const pieData8 = ref({});
const pieData9 = ref({});
const pieData10 = ref({});
const pieData11 = ref({});
const pieData12 = ref({});
const pieData13 = ref({});
const pieData14 = ref({});
const pieData15 = ref({});

const reitsTypeList = ref([
	{
		color: '#378EFF',
		name: '周浦商务中心',
	},
	{
		color: '#39DDE8',
		name: '塞万提斯学院大厦',
	},
	{
		color: '#63DC81',
		name: '两者重叠客群',
	},
]);

const reitsTypeList1 = [
	{
		color: '#63DC81',
		name: '消费基础设施',
	},
	{
		color: '#ADE369',
		name: '水利设施',
	},
];

const tableDatao = ref([]); //商圈一
const tableDatat = ref([]); //商圈二

// 重叠比例数据
const leftPercentage = ref(50.72); // 重叠客群占本项目比例
const rightPercentage = ref(21.58); // 重叠客群占竞品比例

// 计算圆的位置
const circleRadius = 106; // 圆的半径
const maxOffset = circleRadius * 2; // 最大偏移距离

// 根据重叠比例计算圆的位置
const overlap = computed(() => {
	// 取两个百分比的平均值作为重叠程度
	const avgPercentage = (leftPercentage.value + rightPercentage.value) / 2;
	// 重叠程度越大,偏移量越小
	return maxOffset * (1 - avgPercentage / 100);
});

// 计算左圆的样式
const leftCircleStyle = computed(() => ({
	left: `calc(50% - ${circleRadius + overlap.value / 2}px)`,
}));

// 计算右圆的样式
const rightCircleStyle = computed(() => ({
	right: `calc(50% - ${circleRadius + overlap.value / 2}px)`,
}));

function clear(type) {
	if (type == 'left') {
		tableDatao.value = [];
		multipleSelection.value.shift();
	} else {
		tableDatat.value = [];
		multipleSelection.value.pop();
	}
	// save();
}

// 复制
function handlerCopy(name) {
	if (navigator.clipboard && navigator.clipboard.writeText) {
		navigator.clipboard
			.writeText(name)
			.then(() => {
				ElMessage.success('复制成功');
			})
			.catch((err) => {
				ElMessage.warning('复制失败');
			});
	} else {
		const textarea = document.createElement('textarea');
		textarea.value = name;
		document.body.appendChild(textarea);
		textarea.select();
		document.execCommand('copy');
		document.body.removeChild(textarea);
		ElMessage.success('复制成功');
	}
}

onMounted(() => {});

function handleBuildConfirm(data) {
	multipleSelection.value = data;
	if (data.length === 2) {
		save();
	}
	if (data.length === 1) {
		multipleSelection.value = data;
		tableDatao.value = [multipleSelection.value[0]];
		tableDatat.value = [];
	}
	if (data.length === 0) {
		tableDatao.value = [];
		tableDatat.value = [];
	}
}

// 确定
const save = () => {
	if (multipleSelection.value.length > 2 || multipleSelection.value.length == 0) {
	} else {
		let ids = multipleSelection.value.map((item) => item.id).join(',');
		getPopulationTrend({ buildingIds: ids }).then((res) => {
			tableDatao.value = [];
			tableDatat.value = [];
			if (res.code === 200) {
				tableDatao.value.push(multipleSelection.value[0]);
				tableDatat.value.push(multipleSelection.value[1]);
				dataObject.value = res.data;

				dataObject.value.selfIncome = [
					res.data.selfIncome.householdIncomeLt15000,
					res.data.selfIncome.householdIncome25000,
					res.data.selfIncome.householdIncome35000,
					res.data.selfIncome.householdIncome65000,
					res.data.selfIncome.householdIncome75000,
					res.data.selfIncome.householdIncome110000,
					res.data.selfIncome.householdIncome150000,
					res.data.selfIncome.householdIncome175000,
					res.data.selfIncome.householdIncomeGt200000,
				];
				dataObject.value.otherIncome = [
					res.data.otherIncome.householdIncomeLt15000,
					res.data.otherIncome.householdIncome25000,
					res.data.otherIncome.householdIncome35000,
					res.data.otherIncome.householdIncome65000,
					res.data.otherIncome.householdIncome75000,
					res.data.otherIncome.householdIncome110000,
					res.data.otherIncome.householdIncome150000,
					res.data.otherIncome.householdIncome175000,
					res.data.otherIncome.householdIncomeGt200000,
				];
				dataObject.value.overlapIncome = [
					res.data.overlapIncome.householdIncomeLt15000,
					res.data.overlapIncome.householdIncome25000,
					res.data.overlapIncome.householdIncome35000,
					res.data.overlapIncome.householdIncome65000,
					res.data.overlapIncome.householdIncome75000,
					res.data.overlapIncome.householdIncome110000,
					res.data.overlapIncome.householdIncome150000,
					res.data.overlapIncome.householdIncome175000,
					res.data.overlapIncome.householdIncomeGt200000,
				];
				leftPercentage.value = Number(res.data.selfProportion); // 重叠客群占本项目比例
				rightPercentage.value = Number(res.data.otherProportion); // 重叠客群占竞品比例

				pieData.value = {
					name: tableDatao.value[0].buildingName,
					data: [
						{ name: '有房', color: '#378EFF', value: dataObject.value?.selfHasProperty || 0 },
						{ name: '无房', color: '#37B7FF', value: dataObject.value?.selfNoProperty || 0 },
					],
				};
				pieData2.value = {
					name: tableDatat.value[0].buildingName,
					data: [
						{ name: '有房', value: dataObject.value?.otherHasProperty || 0 },
						{ name: '无房', value: dataObject.value?.otherNoProperty || 0 },
					],
				};
				pieData3.value = {
					name: '两者重叠客群',
					data: [
						{ name: '有房', value: dataObject.value?.overlapHasProperty || 0 },
						{ name: '无房', value: dataObject.value?.overlapNoProperty || 0 },
					],
				};

				pieData4.value = {
					name: tableDatao.value[0].buildingName,
					data: [
						{ name: '高中以下', color: '#378EFF', value: dataObject.value?.selfEducationNoHigh || 0 },
						{ name: '高中', color: '#37B7FF', value: dataObject.value?.selfEducationHigh || 0 },
						{ name: '专科', color: '#39DDE8', value: dataObject.value?.selfEducationJuniorCollege || 0 },
						{ name: '学士/硕士/博士', color: '#2FE2AC', value: dataObject.value?.selfEducationBachelorMasterDoctor || 0 },
					],
				};

				pieData5.value = {
					name: tableDatat.value[0].buildingName,
					data: [
						{ name: '高中以下', color: '#378EFF', value: dataObject.value?.otherEducationNoHigh || 0 },
						{ name: '高中', color: '#37B7FF', value: dataObject.value?.otherEducationHigh || 0 },
						{ name: '专科', color: '#39DDE8', value: dataObject.value?.otherEducationJuniorCollege || 0 },
						{ name: '学士/硕士/博士', color: '#2FE2AC', value: dataObject.value?.otherEducationBachelorMasterDoctor || 0 },
					],
				};

				pieData6.value = {
					name: '两者重叠客群',
					data: [
						{ name: '高中以下', color: '#378EFF', value: dataObject.value?.overlapEducationNoHigh || 0 },
						{ name: '高中', color: '#37B7FF', value: dataObject.value?.overlapEducationHigh || 0 },
						{ name: '专科', color: '#39DDE8', value: dataObject.value?.overlapEducationJuniorCollege || 0 },
						{ name: '学士/硕士/博士', color: '#2FE2AC', value: dataObject.value?.overlapEducationBachelorMasterDoctor || 0 },
					],
				};

				pieData7.value = {
					name: tableDatao.value[0].buildingName,
					data: [
						{ name: '个人', color: '#378EFF', value: dataObject.value?.selfIndividualProportion || 0 },
						{ name: '情侣', color: '#37B7FF', value: dataObject.value?.selfCoupleProportion || 0 },
						{ name: '家庭', color: '#39DDE8', value: dataObject.value?.selfFamilyProportion || 0 },
						{ name: '其他', color: '#2FE2AC', value: dataObject.value?.selfOtherProportion || 0 },
					],
				};

				pieData8.value = {
					name: tableDatat.value[0].buildingName,
					data: [
						{ name: '个人', color: '#378EFF', value: dataObject.value?.otherIndividualProportion || 0 },
						{ name: '情侣', color: '#37B7FF', value: dataObject.value?.otherCoupleProportion || 0 },
						{ name: '家庭', color: '#39DDE8', value: dataObject.value?.otherFamilyProportion || 0 },
						{ name: '其他', color: '#2FE2AC', value: dataObject.value?.otherOtherProportion || 0 },
					],
				};

				pieData9.value = {
					name: '两者重叠客群',
					data: [
						{ name: '个人', color: '#378EFF', value: dataObject.value?.overlapIndividualProportion || 0 },
						{ name: '情侣', color: '#37B7FF', value: dataObject.value?.overlapCoupleProportion || 0 },
						{ name: '家庭', color: '#39DDE8', value: dataObject.value?.overlapFamilyProportion || 0 },
						{ name: '其他', color: '#2FE2AC', value: dataObject.value?.overlapOtherProportion || 0 },
					],
				};

				pieData10.value = {
					name: tableDatao.value[0].buildingName,
					data: [
						{ name: '0-14岁', color: '#378EFF', value: dataObject.value?.selfAgeZeroFourteen || 0 },
						{ name: '15-64岁', color: '#37B7FF', value: dataObject.value?.selfAgeFifteenSixtyfour || 0 },
						{ name: '65岁以上', color: '#39DDE8', value: dataObject.value?.selfAgeSixtyfiveAbove || 0 },
					],
				};

				pieData11.value = {
					name: tableDatat.value[0].buildingName,
					data: [
						{ name: '0-14岁', color: '#378EFF', value: dataObject.value?.otherAgeZeroFourteen || 0 },
						{ name: '15-64岁', color: '#37B7FF', value: dataObject.value?.otherAgeFifteenSixtyfour || 0 },
						{ name: '65岁以上', color: '#39DDE8', value: dataObject.value?.otherAgeSixtyfiveAbove || 0 },
					],
				};

				pieData12.value = {
					name: '两者重叠客群',
					data: [
						{ name: '0-14岁', color: '#378EFF', value: dataObject.value?.overlapAgeZeroFourteen || 0 },
						{ name: '15-64岁', color: '#37B7FF', value: dataObject.value?.overlapAgeFifteenSixtyfour || 0 },
						{ name: '65岁以上', color: '#39DDE8', value: dataObject.value?.overlapAgeSixtyfiveAbove || 0 },
					],
				};

				pieData13.value = {
					name: tableDatao.value[0].buildingName,
					data: [
						{ name: '蓝领', color: '#378EFF', value: dataObject.value?.selfBlueCollar || 0 },
						{ name: '白领', color: '#37B7FF', value: dataObject.value?.selfWhiteCollar || 0 },
						{ name: '公共事业', color: '#39DDE8', value: dataObject.value?.selfPublicUtilities || 0 },
					],
				};

				pieData14.value = {
					name: tableDatat.value[0].buildingName,
					data: [
						{ name: '蓝领', color: '#378EFF', value: dataObject.value?.otherBlueCollar || 0 },
						{ name: '白领', color: '#37B7FF', value: dataObject.value?.otherWhiteCollar || 0 },
						{ name: '公共事业', color: '#39DDE8', value: dataObject.value?.otherPublicUtilities || 0 },
					],
				};

				pieData15.value = {
					name: '两者重叠客群',
					data: [
						{ name: '蓝领', color: '#378EFF', value: dataObject.value?.overlapBlueCollar || 0 },
						{ name: '白领', color: '#37B7FF', value: dataObject.value?.overlapWhiteCollar || 0 },
						{ name: '公共事业', color: '#39DDE8', value: dataObject.value?.overlapPublicUtilities || 0 },
					],
				};

				reitsTypeList.value = [
					{
						color: '#378EFF',
						name: tableDatao.value[0].buildingName,
					},
					{
						color: '#39DDE8',
						name: tableDatat.value[0].buildingName,
					},
					{
						color: '#63DC81',
						name: '两者重叠客群',
					},
				];

				businessFormatList.value.forEach((element) => {
					if (res.data[element.key]) {
						element.value = res.data[element.key];
					}
				});
        businessFormatList.value = businessFormatList.value.filter((item) => !!Number(item.value));
				handleMyChart();
				initEchart();
			}
			dialogTableVisible.value = false;
		});
	}
};

function initEchart() {
	nextTick(() => {
		echartPie1.value.init();
		echartPie2.value.init();
		echartPie3.value.init();
		echartPie4.value.init();
		echartPie5.value.init();
		echartPie6.value.init();
		echartPie7.value.init();
		echartPie8.value.init();
		echartPie9.value.init();
		echartPie10.value.init();
		echartPie11.value.init();
		echartPie12.value.init();
		echartPie13.value.init();
		echartPie14.value.init();
		echartPie15.value.init();
	});
}

function handleMyChart() {
	myChart1 = echarts.init(document.getElementById('myChart'));

	myChart1.resize({
		width: 800,
		height: 354,
	});
	myChart1.setOption({
		color: '#249EFF',
		grid: {
			left: '9%',
			right: '4%',
			bottom: '25%',
			top: '12%',
		},
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(244, 247, 252, .8)',
			borderColor: 'transparent',
			borderRadius: 4,
			formatter: function (params) {
				let str = '';
				params.forEach((item, index) => {
					str += `
      <div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
        <div style="width:8px;height:8px;background-color:${item.color};margin-right: 8px;border-radius: 4px;">
        </div>
        <div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
         ${index == 0 ? tableDatao.value[0].buildingName : index == 1 ? tableDatat.value[0].buildingName : '两者重叠客群'}
        </div>
        <div style="font-size: 14px; color: #1D2129; font-weight: 500">
        ${item.value}%
        </div>
      </div>
      `;
				});
				return `
    <div style="font-size: 14px; color: #1D2129; font-weight: 600;margin-bottom: 4px">${params[1].name}万</div>
    <div style="display: flex;
    flex-direction: column;
    gap: 4px;">
    ${str}
    </div>
    `;
			},
		},
		xAxis: {
			type: 'category',
			// data: echartsTwelve1,
			data: ['<1.5', '2.5', '3.5', '6.5', '7.5', '11', '15', '17.5', '>20'],
			axisLabel: {
				interval: 0, // 显示所有标签，如果想要更小的间隔可以设置为1或更大的数字
			},
		},
		yAxis: {
			type: 'value',
			splitLine: {
				show: true, // 虚拟线
				lineStyle: {
					color: '#E5E6EB',
					type: 'dashed',
				},
			},
			axisLabel: {
				formatter: function (value, index) {
					return value + '%';
				},
			},
		},

		series: [
			{
				color: '#378EFF',
				name: 'Food',
				// data: newarr.value,
				data: dataObject.value.selfIncome,
				itemStyle: {
					borderRadius: [5, 5, 0, 0], // 设置四个圆角的半径，顺序为左上、右上、右下、左下
				},
				type: 'bar',
				barWidth: 8,
			},
			{
				color: '#39DDE8',
				name: 'Cloth',
				// data: newarr.value,
				data: dataObject.value.otherIncome,
				itemStyle: {
					borderRadius: [5, 5, 0, 0], // 设置四个圆角的半径，顺序为左上、右上、右下、左下
				},
				type: 'bar',
				barWidth: 8,
			},
			{
				color: '#63DC81',
				name: 'Book',
				// data: newarr.value,
				data: dataObject.value.overlapIncome,
				itemStyle: {
					borderRadius: [5, 5, 0, 0], // 设置四个圆角的半径，顺序为左上、右上、右下、左下
				},
				type: 'bar',
				barWidth: 8,
			},
		],
	});
}
</script>
<style scoped lang="less">
.comparison_box {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);

	.container_box {
		width: 100%;
		height: 100%;
		box-sizing: border-box;

		.echars_box {
			width: calc(100% - 32px);
			background-color: rgba(255, 255, 255, 1);
			margin-top: 10px;
			padding: 4px 16px 16px 16px;
			border-radius: 6px;
			.echars_main {
				width: 100%;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				height: 100%;
				.box_ss {
					width: calc(100% - 0px);
					border-radius: 6px;
					box-sizing: border-box;
					height: 100%;
					display: flex;
					flex-wrap: wrap;

					.title1 {
						width: 100%;
						height: 60px;
						font-weight: 500;
						font-size: 20px;
						line-height: 60px;
						display: flex;
						justify-content: flex-start;
						align-items: center;
						color: #1d2129;
						&::before {
							content: '';
							width: 5px;
							height: 14px;
							background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
							border-radius: 10px;
							margin-right: 8px;
						}
					}
				}
			}
		}
	}
}

.common_wrap {
	padding: 20px 16px;
	background-color: #fff;
	display: flex;
	gap: 16px;
	border-radius: 0px 4px 4px 4px;
	.left_empty_wrap,
	.left_content_wrap,
	.right_empty_wrap,
	.right_content_wrap {
		flex: 1;
	}
	.left_empty_wrap,
	.right_empty_wrap {
		border: 1px solid #e5e6eb;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 4px;
		padding: 14px 0;
		.icon {
			width: 64px;
			height: 64px;
		}
	}
	.title_wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12px;
	}
}

.population_wrap {
	display: flex;
	flex-direction: column;
	.box_wrap {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		gap: 16px;
		.left,
		.right {
			width: calc(50% - 8px);
			display: flex;
			flex-direction: column;
			gap: 16px;
		}
	}
	.double_box_wrap {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 16px;
	}
}

.single_wrap {
	// flex: 1;
	box-sizing: border-box;
	// min-height: 300px;
	border: 1px solid #e5e6eb;
	border-radius: 4px;
	display: flex;
	flex-direction: column;
	.title1 {
		box-sizing: border-box;
		padding: 0 20px;
		width: 100%;
		height: 48px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #f7f8fa;
		border-bottom: 1px solid #e5e6eb;
		.title {
			font-size: 16px;
			font-weight: 600;
			color: #1d2129;
		}
	}
	.content_wrap {
		.gender_wrap {
			padding: 67px 80px 67px 60px;
			width: 100%;
			height: 354px;
			box-sizing: border-box;
			.gender_wrap_top {
				display: flex;
				height: 50px;
				align-items: center;
				margin-bottom: 8px;
				.gender_wrap_top_right {
					display: flex;
					justify-content: space-between;
					width: calc(100% - 132px);
					height: 50px;
					.gender_wrap_top_right_item {
						width: 46px;
						height: 50px;
						img {
							width: 46px;
							height: 50px;
						}
					}
				}
			}

			.gender_wrap_bottom {
				// width: 660px;
				height: 162px;
				display: flex;
				flex-direction: column;
				gap: 24px 0;
				.gender_wrap_bottom_item {
					width: 100%;
					height: 38px;
					display: flex;
					justify-content: space-between;
					align-items: center;
					.gender_wrap_bottom_item_left {
						width: 132px;
						height: 38px;
						line-height: 38px;
						font-weight: 400;
						font-size: 14px;
						text-align: center;
						color: #4e5969;
					}
					.gender_wrap_bottom_item_right {
						width: calc(100% - 132px);
						height: 38px;
						.gender_item {
							width: 100%;
							height: 12px;
							display: flex;
							margin-bottom: 4px;
							.gender_item_left {
								width: 62.76%;
								height: 12px;
								background: #378eff;
								border-top-left-radius: 10px;
								border-bottom-left-radius: 10px;
							}
							.gender_item_right {
								width: 37.24%;
								height: 12px;
								background: #f088ca;
								border-top-right-radius: 10px;
								border-bottom-right-radius: 10px;
							}
						}

						.gender_item_bottom {
							height: 22px;
							width: 100%;
							display: flex;
							justify-content: space-between;
							align-items: center;
							.gender_item_bottom_left {
								font-weight: 500;
								font-size: 14px;
								line-height: 22px;
								color: #378eff;
							}
							.gender_item_bottom_right {
								font-weight: 500;
								font-size: 14px;
								line-height: 22px;

								color: #f088ca;
							}
						}
					}
				}
			}
		}
		.echart_wrap_box {
			height: 264px;
			// width: 800px;
			padding: 0px 58px;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			gap: 22px;
		}
		.echart_wrap {
			width: 100%;
			// width: 28%;
		}

		.progress_wrap_box {
			width: 100%;
			height: 354px;
			display: flex;
			flex-direction: column;
			padding: 74px 40px;
			box-sizing: border-box;
			gap: 24px 0;
			.progress_wrap_box_item {
				width: 100%;
				height: 22px;
				display: flex;
				align-items: center;
				.progress_wrap_box_item_left {
					width: 56px;
					height: 22px;
					margin-right: 16px;
					font-weight: 400;
					font-size: 14px;
					line-height: 20px;
					color: #4e5969;
					text-align: right;
				}
				.progress_wrap_box_item_center {
					padding: 5px 0;
					box-sizing: border-box;
					display: flex;
					width: calc(100% - 144px);
					.progress_wrap_box_item_center_item_left {
						width: 62.76%;
						height: 12px;
						background: #378eff;
						border-top-left-radius: 10px;
						border-bottom-left-radius: 10px;
					}
					.progress_wrap_box_item_center_item_right {
						width: 100%;
						height: 12px;
						background: #fff;
						border: 1px solid #e5e6eb;
						border-left: 0px;
						border-top-right-radius: 10px;
						border-bottom-right-radius: 10px;
					}
				}

				.progress_wrap_box_item_right {
					width: 56px;
					height: 22px;
					margin-left: 16px;
					font-weight: 500;
					font-size: 14px;
					line-height: 18px;
					color: #1d2129;
					text-align: left;
				}
			}
		}
	}
	.empty_wrap {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-weight: 400;
		color: #86909c;
		height: 354px;
		img {
			width: 80px;
			height: 80px;
		}
	}
}

.charts {
	// height: 458px;
	> :nth-child(1) {
		width: 100% !important;
		> :nth-child(1) {
			width: 100% !important;
		}
	}
}
.chart_content {
	height: 71px;
	margin-top: -71px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20px;
	.legend_item {
		display: flex;
		align-items: center;
		height: 22px;
		gap: 8px;
		.circle_wrap {
			width: 8px;
			height: 8px;
			border-radius: 50%;
		}
		.name {
			font-size: 14px;
			font-weight: 600;
			line-height: 22px;
			color: #4e5969;
		}
	}
}

.legend_wrap {
	height: 90px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px 20px;
	.legend_item {
		display: flex;
		align-items: center;
		gap: 8px;
		.circle_wrap {
			width: 8px;
			height: 8px;
			border-radius: 50%;
		}
		.name {
			font-size: 14px;
			font-weight: 600;
			line-height: 22px;
			color: #4e5969;
		}
	}
}

.venn-diagram-container {
	padding: 32px;
	background: white;
	border-radius: 8px;
}
.venn-diagram {
	position: relative;
	height: 216px;
	display: flex;
	justify-content: center;
}

.circle {
	position: absolute;
	width: 212px;
	height: 212px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
	font-weight: 500;
	transition: all 0.3s ease;
}

.left-circle {
	background-color: rgba(0, 255, 204, 0.2);
	border: 2px solid #37e2e2;
	color: #00b89b;
}

.right-circle {
	background-color: rgba(57, 118, 255, 0.2);
	border: 2px solid #249eff;
	color: #1868f1;
}

.overlap-text {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	font-weight: 500;
	writing-mode: vertical-lr;
	z-index: 10;

	font-size: 16px;
	line-height: 24px;
	color: #1d2129;
}

.statistics {
	display: flex;
	justify-content: center;
	gap: 48px;
	margin-top: 16px;
}

.stat-item {
	text-align: center;
}

.percentage {
	font-weight: 500;
	font-size: 24px;
	line-height: 32px;
	color: #1d2129;
}

.percentage-symbol {
	font-size: 24px;
}

.description {
	color: #4e5969;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	margin-top: 4px;
}

.summary_wrap {
	position: relative;
	display: flex;
	align-items: center;
	height: 62px;
	padding: 0 20px;
	.bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}
	.icon {
		width: 40px;
		height: 40px;
		z-index: 99;
		margin-right: 12px;
	}
	.summary {
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		color: #1d2129;
		z-index: 99;
		width: calc(100% - 150px);
	}
	.copy {
		z-index: 99;
		padding: 5px 16px;
		border: 1px solid #1868f1;
		color: #1868f1;
		background: #e8f3ff;
		border-radius: 4px;
		cursor: pointer;
		position: absolute;
		right: 20px;
	}
}
</style>
