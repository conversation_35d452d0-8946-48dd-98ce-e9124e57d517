<template>
	<div>
		<myModal :width="902" :visible="dialogVisible" @cancel="dialogVisible = false" unmount-on-close mask-closable>
			<template #title>
				<div class="modal_title">{{ title }}</div>
			</template>
			<div class="table_wrap">
				<arco-table
					ref="buidlTableRef"
					row-key="id"
					:columns="tableColumns"
					:data="tableData"
					:pagination="pagination"
					@page-change="pageSizeChange"
					:bordered="{ wrapper: true, cell: true, headerCell: true, bodyCell: true }"
				/>
			</div>
		</myModal>
	</div>
</template>
<script setup>
import { getcontactorList } from '@/api/syt.js';
import { ref } from 'vue';
import myModal from '@/component/arcoComponents/modal/index.vue';

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	title: {
		type: String,
		default: '拓展区域负责人',
	},
	tableColumns: {
		type: Array,
		default: () => {
			return [
				{
					title: '姓名',
					width: '96',
					ellipsis: true,
					tooltip: true,
					dataIndex: 'name',
				},
				{
					title: '拓展区域',
					width: '96',
					ellipsis: true,
					tooltip: true,
					dataIndex: 'area',
				},
				{
					width: '96',
					ellipsis: true,
					tooltip: true,
					title: '省份',
					dataIndex: 'province',
				},
				{
					title: '城市',
					width: '102',
					dataIndex: 'city',
					ellipsis: true,
					tooltip: true,
				},
				{
					title: '联系电话',
					width: '132',
					ellipsis: true,
					tooltip: true,
					dataIndex: 'phone',
				},
				{
					title: '邮箱',
					ellipsis: true,
					tooltip: true,
					dataIndex: 'email',
				},
				{
					title: '微信',
					ellipsis: true,
					width: '130',
					tooltip: true,
					dataIndex: 'wechat',
				},
			];
		},
	},
	// 品牌id
	brandId: {
		type: String,
		default: '',
	},
});
const emit = defineEmits(['update:modelValue', 'confirm']);
const dialogVisible = ref(false);
const buidlTableRef = ref();
watch(
	() => props.modelValue,
	(newVal) => {
		search();
		dialogVisible.value = newVal;
	}
);
watch(dialogVisible, (newVal) => {
	emit('update:modelValue', newVal);
});
const tableData = ref([]);
const pagination = ref({ pageSize: 10, current: 1, total: 0 });

function pageSizeChange(e) {
	pagination.value.current = e;
	search();
}

async function search() {
	let queryParams = {};
	queryParams = {
		brandId: props.brandId,
		currentPage: pagination.value.current,
		pageSize: pagination.value.pageSize,
	};
	await getcontactorList(queryParams)
		.then((res) => {
			tableData.value = res.data.rows;
			pagination.value.total = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
}
</script>
<style lang="less">
.arco-cascader-option-active {
	color: #1868f1 !important;
}
.arco-select-view-single,
.arco-input-wrapper {
	background: none !important;
	border: 1px solid #e5e6eb !important;
	border-radius: 4px;
}
.arco-select-view-single:hover,
.arco-input-wrapper:hover {
	border: 1px solid #1868f1 !important;
}
</style>
<style lang="less" scoped>
.modal_title {
	text-align: left;
	width: 100%;
	font-size: 20px;
	font-weight: 500;
	color: #1d2129;
}
</style>
