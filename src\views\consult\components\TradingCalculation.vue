<template>
	<div class="common-layout">
		<el-container>
			<el-header style="height: 300px">
				<el-tabs v-model="activeName" type="border-card" style="height: 300px" @tab-click="handleClick">
					<el-tab-pane label="租赁" name="first">
						<el-row :gutter="20">
							<el-col :span="8">
								<p class="mx-1">起止日期：</p>
								<el-date-picker v-model="value1" type="datetimerange" start-placeholder="Start Date" end-placeholder="End Date"
							/></el-col>
							<el-col :span="8"
								><p class="mx-1">城市：</p>
								<el-cascader placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }">
								</el-cascader>
							</el-col>
							<el-col :span="8">
								<p>商圈：</p>
								<el-select v-model="business" placeholder="全部商圈" size="default" @change="Searchbuild">
									<el-option v-for="(item, index) in buildingTypes" :key="item.location" :label="item.location" :value="item.location" />
								</el-select>
							</el-col>
						</el-row>
						<el-row :gutter="20">
							<el-col :span="8">
								<p class="mx-1">建筑物：</p>
								<el-select v-model="selectedItem" @change="handleItemChange" placeholder="请选择建筑物">
									<el-option v-for="item in buildingTypes" :key="item.name" :label="item.name" :value="item.name"></el-option>
								</el-select>
							</el-col>
							<el-col :span="8">
								<p class="mx-1">面积(㎡)：</p>

								<el-input style="width: 36%" v-model="loanAmount" placeholder="请输入面积"></el-input>
							</el-col>
							<el-col :span="8"
								><p class="mx-1">总金额(元)：</p>
								<div v-if="selectedItem !== null">
									{{ total }}
								</div></el-col
							>
						</el-row>
					</el-tab-pane>
					<el-tab-pane label="购买" name="second">
						<el-row :gutter="20">
							<el-col :span="8">
								<p class="mx-1">起止日期：</p>
								<el-date-picker v-model="value2" type="datetimerange" start-placeholder="Start Date" end-placeholder="End Date"
							/></el-col>
							<el-col :span="8"
								><p class="mx-1">城市：</p>
								<el-cascader placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }">
								</el-cascader>
							</el-col>
							<el-col :span="8">
								<p>商圈：</p>
								<el-select v-model="business" placeholder="全部商圈" size="default" @change="Searchbuild">
									<el-option v-for="(item, index) in buildingTypes" :key="item.location" :label="item.location" :value="item.location" />
								</el-select>
							</el-col>
						</el-row>
						<el-row :gutter="20">
							<el-col :span="8">
								<p class="mx-1">建筑物：</p>
								<el-select v-model="selectedItem" @change="handleItemChange" placeholder="请选择建筑物">
									<el-option v-for="item in buildingTypes" :key="item.name" :label="item.name" :value="item.name"></el-option>
								</el-select>
							</el-col>
							<el-col :span="8">
								<p class="mx-1">面积(㎡)：</p>

								<el-input style="width: 36%" v-model="loanAmount" placeholder="请输入面积"></el-input>
							</el-col>
							<el-col :span="8"
								><p class="mx-1">总金额(元)：</p>
								<div v-if="selectedItem !== null">
									{{ total }}
								</div></el-col
							>
						</el-row>
					</el-tab-pane>

					<el-tab-pane label="抵押贷款" name="four"> 抵押贷款 </el-tab-pane>
				</el-tabs>
			</el-header>
			<el-main style="height: 300px; overflow: hidden">
				<el-card class="box-card" shadow="never">
					<template #header>
						<div class="card-header">
							<span>贷款计算器</span>
						</div>
					</template>
					<el-row :gutter="20">
						<el-col :span="8">
							<p>贷款总额(元)：</p>
							<el-input v-model="totalMoney" placeholder="请输入贷款总额"></el-input>
						</el-col>
						<el-col :span="8">
							<p>贷款期限(年)：</p>
							<el-input v-model="years" placeholder="请输入贷款期限"></el-input>
						</el-col>
						<!-- 贷款期限 -->
						<el-col :span="8">
							<p>基准利率：{{ interest }}%</p>
							<el-select v-model="interestRate" placeholder="基准利率">
								<el-option v-for="(rate, index) in interestRates" :key="index" :label="rate.label" :value="rate.value"></el-option>
							</el-select>
						</el-col>
					</el-row>
					<el-row :gutter="20">
						<!-- 基准利率 -->
						<el-col :span="8">
							<p>贷款方式：</p>
							<el-select v-model="loanMethod" placeholder="请选择贷款方式" @change="changeVal">
								<el-option label="等额本金" value="benjin"></el-option>
								<el-option label="等额本息" value="benxi"></el-option>
							</el-select>
						</el-col>
						<!-- 贷款总额 -->
						<el-col :span="8">
							<p>总应还款</p>

							<div>
								{{ totalValB }}
							</div>
						</el-col>
						<!-- 贷款期限 -->
						<el-col :span="8">
							<p>每月还款：</p>
							<div>{{ months }}</div>
						</el-col>
						<!-- 每月还款的明细 -->
					</el-row>
				</el-card>
			</el-main>
			<el-footer style="height: 800px; overflow: hidden">
				<el-card class="box-card" shadow="never">
					<el-text tag="p">还款计划</el-text>
					<el-table :data="tableData" style="width: 100%; height: 800px" :key="new Date()">
						<el-table-column v-for="item in cloumns" :prop="item.prop" :label="item.label" :key="item.prop" />
					</el-table>
				</el-card>
			</el-footer>
		</el-container>
	</div>
</template>

<script setup>
import { ref, watch, onMounted, computed, nextTick } from 'vue';
import { provinceAndCityData } from 'element-china-area-data';
import http from '@/utils/http';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
const years = ref();

const interestRate = ref('1'); //利率折扣，默认不打折
const loanAmount = ref('');
const totalMoney = ref(''); //// 贷款总额

const interest = ref('6.37');
const business = ref(); //商圈
const building = ref([]); //建筑物
const loanMethod = ref('benxi');
const months = ref(0);
let totalRepayment;
const totalValB = computed(() => {
	const yearsVal = parseFloat(years.value);
	const totalMoneyVal = parseFloat(totalMoney.value);
	const monthlyPayment = parseFloat(months.value);

	const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 12 / 100; //每月利率
	const totalMonths = yearsVal * 12; // 总还款月数

	// 计算总还款额
	let totalRepayment = 0;

	if (loanMethod.value === 'benxi') {
		// 计算等额本息方式的总还款额
		totalRepayment = monthlyPayment * totalMonths;
	} else if (loanMethod.value === 'benjin') {
		// 计算等额本金方式的总还款额
		totalRepayment = 0; // 初始化总还款额

		for (let i = 0; i < totalMonths; i++) {
			const monthbenjin = totalMoneyVal / totalMonths; // 每月本金
			const monthlyInterest = (totalMoneyVal - i * monthbenjin) * monthlyInterestRate; // 每月利息

			totalRepayment += monthbenjin + monthlyInterest; // 每月本金加上利息
		}
	}

	if (isNaN(totalRepayment)) {
		return 0; // 或者其他你认为合适的默认值
	}

	return totalRepayment.toFixed(2);
});

const changeVal = () => {
	if (loanMethod.value === 'benxi' || loanMethod.value === 'benjin') {
		updateTableData(); // 更新表格数据
	}
};

const total = computed(() => {
	const selectedBuilding = buildingTypes.value.find((item) => item.name === selectedItem.value);
	const price = selectedBuilding ? selectedBuilding.mm : 0;

	// 根据公式计算总金额
	const days = dayjs(value1.value[1]).diff(dayjs(value1.value[0]), 'day'); // 假设 value1 是日期范围
	const squareMeterValue = parseFloat(loanAmount.value) || 0;

	// 使用 Math.floor() 截断小数部分
	const totalAmount = Math.floor((price / 30) * days * squareMeterValue);

	return totalAmount;
});

const calculateMonthlyPayment = () => {
	// 将年利率转换为月利率
	const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;

	// 贷款总额
	const loanAmountValue = parseFloat(totalMoney.value);

	// 贷款期限（以月为单位）
	const loanTermMonths = parseInt(years.value) * 12;

	// 计算月还款额
	let monthlyPayment;
	if (loanMethod.value === 'benxi') {
		// 等额本息计算公式
		monthlyPayment = (loanAmountValue * monthlyInterestRate) / (1 - Math.pow(1 + monthlyInterestRate, -loanTermMonths));
	} else if (loanMethod.value === 'benjin') {
		// 等额本金计算公式
		monthlyPayment = loanAmountValue / loanTermMonths + loanAmountValue * monthlyInterestRate;
	}

	// 更新月还款的ref
	months.value = monthlyPayment.toFixed(2);
};

// 监听相关数据的变化，一旦变化就重新计算月还款
watch([totalMoney, years, interest, loanMethod, interestRate], () => {
	calculateMonthlyPayment();
});
const Type = ref('');
onMounted(() => {
	initdata();
});
const initdata = async () => {
	let queryParams = {
		type: Type.value || '租赁',
		province: province.value,
		city: city.value,
		business: business.value,
	};

	try {
		const res = await http.get('/api/api.do?act=b2_acall', { params: queryParams });
		buildingTypes.value = [...res];
		building.value = buildingTypes.value.map((item) => item.name);
	} catch (error) {
		console.error(error);
	}
};
const cloumns = [
	{
		label: '月数',
		prop: 'month',
	},
	{
		label: '期初余额',
		prop: 'initialbalance',
	},
	{
		label: '偿还本息',
		prop: 'Monthlypay',
	},

	{
		label: '利息',
		prop: 'accrual',
	},
	{
		label: '本金',
		prop: 'capital',
	},
	{
		label: '剩余本金',
		prop: 'terminal',
	},
];
const tableData = ref([]);

const updateTableData = () => {
	tableData.value = [];
	const loanAmountValue = parseFloat(totalMoney.value);
	const yearsVal = parseInt(years.value);
	const totalMonths = yearsVal * 12;

	if (isNaN(loanAmountValue) || isNaN(yearsVal)) {
		return; // 处理无效数据
	}

	// 初始化剩余本金
	let remainingPrincipal = loanAmountValue;

	if (loanMethod.value === 'benxi') {
		// 等额本息的表格数据
		for (let i = 1; i <= totalMonths; i++) {
			const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;

			// 计算每月还款额（等额本息方式）
			const monthlyPaymentBenxi = (loanAmountValue * monthlyInterestRate) / (1 - Math.pow(1 + monthlyInterestRate, -totalMonths));

			// 计算利息
			const interestAccrualBenxi = remainingPrincipal * monthlyInterestRate;

			// 计算本金还款
			const capitalRepaymentBenxi = monthlyPaymentBenxi - interestAccrualBenxi;

			// 计算期末余额
			remainingPrincipal -= capitalRepaymentBenxi;

			const rowDataBenxi = {
				month: i,
				initialbalance: loanAmountValue.toFixed(2),
				Monthlypay: monthlyPaymentBenxi.toFixed(2),
				capital: capitalRepaymentBenxi.toFixed(2),
				accrual: interestAccrualBenxi.toFixed(2),
				terminal: remainingPrincipal.toFixed(2),
			};

			// 将数据推入 tableData 数组
			tableData.value.push(rowDataBenxi);
		}
	} else {
		// 等额本金的还款数据
		for (let i = 1; i <= totalMonths; i++) {
			const totalMoneyVal = parseFloat(totalMoney.value);
			const monthlyInterestRate = parseFloat(interest.value * interestRate.value) / 100 / 12;
			const monthbenjin = totalMoneyVal / totalMonths; // 每月本金

			// 计算利息
			const interestAccrualBenjin = remainingPrincipal * monthlyInterestRate;

			// 计算本金还款
			const capitalRepaymentBenjin = monthbenjin;

			// 计算剩余本金
			remainingPrincipal -= monthbenjin;

			const rowDataBenjin = {
				month: i,
				initialbalance: loanAmountValue.toFixed(2),
				Monthlypay: (capitalRepaymentBenjin + interestAccrualBenjin).toFixed(2),
				capital: capitalRepaymentBenjin.toFixed(2),
				accrual: interestAccrualBenjin.toFixed(2),
				terminal: remainingPrincipal.toFixed(2),
			};
			tableData.value.push(rowDataBenjin);
		}
	}
};

watch([years, totalMoney, interest, loanMethod, interest, interestRate], () => {
	updateTableData();
});
// 通过循环生成基准利率的选项
const interestRates = [
	{ label: '不打折', value: '1' },
	{ label: '9折', value: '0.9' },
	{ label: '8.5折', value: '0.85' },
	{ label: '8折', value: '0.8' },
];
const selectedOptions = ref([]);
const province = ref('');
const city = ref('');
const handleChange = (val) => {
	province.value = val[0];
	city.value = val[1];
	business.value = null;
	building.value = [];
	selectedItem.value = null;
	loanAmount.value = '';
	initdata();
};
const Searchbuild = () => {
	initdata();
};
const activeName = ref('first');
const handleClick = (tab, event) => {
	Type.value = tab.props.label;
	province.value = val[0];
	city.value = val[1];
	business.value = [];
	building.value = [];
};
const value1 = ref([]);
const value2 = ref([]);

const buildingTypes = ref();
const selectedItem = ref(null);

const handleItemChange = () => {};

// 监听 selectedItem 的变化
watch(selectedItem, () => {
	handleItemChange();
});
</script>

<style lang="less" scoped>
.common-layout {
	padding: 20px 0 0 20px;
}

.item {
	padding: 18px 0;
}

.box-card {
	width: 100%;
	height: 99%;
}
</style>
