<template>
	<div class="body_box">
		<el-button plain @click="search()" class="getbtn"> 获取平面图 </el-button>
		<div style="display: flex; flex-wrap: wrap; justify-content: center; align-items: center">
			<div class="box">
				<el-carousel :interval="5000" arrow="always" height="500px" v-if="imgList.length > 0">
					<el-carousel-item v-for="item in imgList" :key="item">
						<el-image style="width: 900px; height: 500px" :src="`${proxyAddress}${item}`" fit="scale-down" />
					</el-carousel-item>
				</el-carousel>
				<div v-else style="width: 900px; height: 500px; line-height: 500px; text-align: center; font-size: 20px; color: #909399">暂无图片</div>
			</div>
		</div>
	</div>
	<!-- 对话框 -->
	<el-dialog v-model="dialogVisible" title="户型图" width="800" :before-close="handleClose">
		<el-row :gutter="20">
			<el-col :span="8">
				<el-cascader placeholder="请选择城市" :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }"> </el-cascader>
			</el-col>
			<el-col :span="8">
				<el-select style="margin-left: 20px; width: 200px" v-model="buildValue" placeholder="全部资产评级">
					<el-option v-for="item in buildingTypes" :key="item" :label="item" :value="item" />
				</el-select>
			</el-col>
			<el-col :span="6">
				<el-input v-model="essential" placeholder="请输入关键字"></el-input>
			</el-col>
			<el-col :span="2">
				<el-button @click="search">查询</el-button>
			</el-col>
		</el-row>
		<el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange" stripe>
			<el-table-column type="selection" width="55" ref="multipleTableRef" />
			<el-table-column v-for="(column, index) in tableColumns" :key="index" :label="column.label" :prop="column.prop" :width="column.width" />
		</el-table>
		<!-- 分页 -->
		<el-pagination @current-change="pageChange" :current-page="currentPage" small background layout="prev, pager, next" class="mt-4" :total="total" />

		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="onConfirm()">确认</el-button>
				<el-button @click="reset"> 取消 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref } from 'vue';
import http from '@/utils/http';
// import { pcaTextArr } from 'element-china-area-data';
import { getFloorList, getMapList } from '@/api/materials.js';
import { ElMessage } from 'element-plus';
const province = ref('');
const city = ref('');
const county = ref('');
const imgList = ref([]);
const imgLists = ref('');
const buildValue = ref('');
const keyword = ref('');
const total = ref(0);
const search = async () => {
	const queryParams = {
		city: city.value,
		county: county.value,
		deType: buildValue.value,
		keywords: keyword.value,
		currentPage: currentPage.value,
		pageNo: currentPage.value,
		pageSize: 10,
	};
	console.log(buildValue.value, 'buildingTypesValue1');
	console.log(queryParams.deType, 'queryParams.value1');
	if (buildValue.value == '零售') {
		queryParams.deType = '购物中心,百货大楼';
		console.log(1);
	}
	if (buildValue.value == '医疗') {
		queryParams.deType = '医疗康养';
	}
	if (buildValue.value == '长租公寓') {
		queryParams.deType = '公寓';
	}
	await getFloorList(queryParams)
		.then((res) => {
			console.log(res, 98877);
			tableData.value = res.result.records;
			dialogVisible.value = true;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const handleChange = (val) => {
	console.log(val, 'ss');
	province.value = val[0];
	city.value = val[1];
	county.value = val[2];
};
const rateValue = ref('');
const essential = ref('');
const proxyAddress = 'https://static.biaobiaozhun.com/';
// const proxyAddress = 'http://*************:8088/sm/sys/common/static/'
// const proxyAddress = 'http://**************:8086/'
const buildingTypes = ['写字楼', '零售', '产业园区', '仓储物流', '酒店', '长租公寓', '医疗', '综合市场'];
const dialogVisible = ref(false);
// const familyList = ref([])
const currentPage = ref(1);
// const total = ref(0)
const tableData = ref([]);
// 选中的值
const selectValue = ref([]);
const idss = ref(''); //id存放
const multipleSelection = ref([]);
const tableColumns = [
	{ label: '地图名字', prop: 'buildingName', width: '300' },
	{ label: '类型', prop: 'deType', width: '300' },
];
//
const pageChange = (val) => {
	console.log(11, val);
	currentPage.value = val;
	search();
};
const reset = () => {
	province.value = '';
	city.value = '';
	county.value = '';
	rateValue.value = '';
	essential.value = '';
	currentPage.value = 1;
	dialogVisible.value = false;
};
const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};

// 点击获取
// // 获取户型图url
// const getFloorPlanList = async () => {
// 	console.log('ids1', idss.value[0]);
// 	const ids = idss.value[0]
// 	await getFloorList({ids: ids}).then((res) => {
// 		console.log(res, 'res77');
// 		imgLists.value = res.data[0].fileList;
// 		console.log('imgList', imgList.value);

// 	}).catch((err) => {
// 		console.log(err, 'err');
// 	});

// };
// 点击确定
// const imgList = ref([]);
// const imgLists = ref('');
const onConfirm = () => {
	// 检查是否选择了多个项目
	if (multipleSelection.value.length !== 1) {
		ElMessage.warning('只能选择一个噢');
		return;
	}

	// 如果只选择了一个项目，则继续后续操作
	console.log('选中的值', multipleSelection.value);
	imgLists.value = multipleSelection.value[0].housePlanPic;
	imgList.value = imgLists.value.split(',');
	console.log(imgList.value, 'imgList');
	console.log(imgLists.value, 576213);
	dialogVisible.value = false;
	reset();
};
// 点击图片进行缩放
const imgClick = (event) => {
	console.log(event);
};
</script>
<style lang="less" scoped>
.body_box {
	text-align: center;
	width: 100%;
}

.box {
	width: 48%; /* 使每个 box 占据容器宽度的一半，留出一些间隔 */
	margin-bottom: 100px;
}

.getbtn {
	width: 500px;
	height: 40px;
	margin: 30px auto;
}
.el-carousel__item h3 {
	color: #475669;
	opacity: 0.75;
	line-height: 300px;
	margin: 0;
	text-align: center;
}

.el-carousel__item:nth-child(2n) {
	background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
	background-color: #d3dce6;
}
</style>
