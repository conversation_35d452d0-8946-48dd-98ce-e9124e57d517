// !vite.config.js
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import ElementPlus from 'unplugin-element-plus/vite';
import AutoImport from 'unplugin-auto-import/vite';

const { resolve } = require('path');
// import px2rem from "postcss-px2rem"

export default ({ mode }) =>
	defineConfig({
		build: {
			rollupOptions: {
				input: {
					index: resolve(__dirname, 'index.html'),
				},
				output: {
					chunkFileNames: 'static/js/[name]-[hash].js',
					entryFileNames: 'static/js/[name]-[hash].js',
					assetFileNames: (assetInfo) => {
						if (assetInfo.type === 'asset' && /\.(jpe?g|png|gif|svg)$/i.test(assetInfo.name)) {
							return 'static/img/[name].[hash][ext]';
						}
						if (assetInfo.type === 'asset' && /\.(ttf|woff|woff2|eot)$/i.test(assetInfo.name)) {
							return 'static/fonts/[name].[hash][ext]';
						}
						return 'static/[ext]/name1-[hash].[ext]';
					},
					manualChunks: {
						echarts: ['echarts'],
					},
					// 在这里添加生产模式的配置
					...(mode === 'production'
						? {
								chunkFileNames: 'static/js/[name].[hash].js',
								entryFileNames: 'static/js/[name].[hash].js',
								// ... 其他配置项
						  }
						: {}),
				},
			},
		},
		plugins: [
			vue(),
			Components({
				resolvers: [
					ElementPlusResolver({
						importStyle: 'less',
					}),
				],
			}),
			AutoImport({
				imports: ['vue'],
				dts: './auto-imports.d.ts', // 确保这个路径是有效的，并且文件夹存在
			}),
			ElementPlus(),
		],
		resolve: {
			alias: {
				'~': path.resolve(__dirname, './'),
				'@': path.resolve(__dirname, 'src'),
				REQUEST_API: resolve('src/api/index.js'),
				UTILS: resolve('src/utils/index.js'),
			},
		},
		base: '/',

		server: {
			hmr: true,
			host: true,
			proxy: {
				'/sm': {
					target: 'https://oa.biaobiaozhun.com/sm/', //服务器地址
					// target: 'http://*************:8080', //董工
					changeOrigin: true,
					ws: true,
					rewrite: (path) => path.replace(/^\/sm/, ''),
				},
				'/api': {
					// target: 'https://oa.biaobiaozhun.com/sm/', //服务器地址
					// target: 'http://************:8081', //董工
					target: 'https://bbzhun.com/', //崔工
					changeOrigin: true,
					ws: true,
				},
			},
		},
		css: {
			// postcss: {
			// 	plugins: [
			// 	  px2rem({
			// 		remUnit:192
			// 	  })
			// 	]
			// },
			preprocessorOptions: {
				less: {
					math: 'always',
				},
			},
		},
		// css: {

		// },
	});
