<template>
    <div class="fc" >
        <div class="title">{{ data.title}}</div>
        <div class="fcc">
            <!-- {{ JSON.stringify(data) }} -->
            <div   v-for="(item, key) of data" :key="key">
                <div v-if="dict[key]" class="fr">
                    <span  class="label">{{ dict[key] }}</span> <span class="val">{{ parseFloat(item)?parseFloat(item).toFixed(2):parseFloat(item) }}</span>

                </div>
        </div>
        </div>
       
    </div>
</template>
<style scoped>
    .fc {
        margin-top:30px ;
        margin-right: 15px;
        margin-left: 15px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-star;
        width: 100%;
        height: 100%;
        font-size: 13px;
        .title{
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        }
        .fcc{
            color: #343232;
        }
    }
    .fr {
        display: flex;
        height: 30px;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        }
        .val{
            font-weight: bold;
        }
</style>
<script setup>
import { ref,defineProps,onMounted} from 'vue';
const obj = ref("")
const props = defineProps({
  data: {
    type: Object,
    default: undefined
  },
  
})
const dict = {
    custodianFee:"托管费",
    fundFee:"基金管理费",
    guaranteeFee:"担保费",
    loan:"贷款",
    margin:"保证金",
    netCashFlow:"净现金流",
    planFee:"计划管理费",
    totalIssuance:"发行总规模",
    underwritingFee:"承销费"
}

</script>