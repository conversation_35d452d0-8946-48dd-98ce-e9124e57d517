<template>
	<div class="content_box">
		<div class="container_box">
			<div class="body_main">
				<div class="search_box">
					<div class="box_1">
						<div class="label">城市</div>
						<arco-cascader
							placeholder="请选择城市"
							v-model="cascaderCity"
							@change="handleChange"
							path-mode
							:fieldNames="{ label: 'label', value: 'label', children: 'children' }"
							ref="cascaderDom"
							:options="cityOptions"
							:style="{ width: '220px' }"
						/>
					</div>
					<div class="box_1">
						<div class="label">资产类型</div>
						<arco-select v-model="buildingTypesValue" style="width: 220px" placeholder="请选择资产类型" clearable>
							<arco-option
								v-for="item in buildingTypes"
								:style="{ color: item.value === buildingTypesValue ? '#1868F1' : '' }"
								:key="item"
								:label="item.label"
								:value="item.value"
							/>
						</arco-select>
					</div>
					<div class="box_1" style="margin-right: 16px">
						<div class="label">关键字</div>
						<arco-input v-model="searchValue" style="width: 220px" allow-clear placeholder="请输入关键字"></arco-input>
					</div>
					<div class="box_2">
						<el-button type="primary" @click="onSubmit()" color="#1868F1">查询</el-button>
						<el-button @click="onReset()" style="color: #4e5969; margin-left: 8px" color="#F2F3F5">重置</el-button>
					</div>
				</div>
				<div class="card_list">
					<div class="card_body" v-for="(item, index) in data" :key="index" @click="onpropertyHome(item)">
						<div class="avatar">
							<img src="@/assets/portraits.png" alt="" v-if="item.companyLogo == '' || item.companyLogo == '暂无' || item.companyLogo == null" />
							<img :src="`${proxyAddress}${item.companyLogo}`" alt="" v-else />
						</div>
						<div class="tips_box">
							<div class="nickName">{{ item.company }}</div>
						</div>
					</div>
				</div>
				<div class="pagination_content">
					<arco-pagination
						:total="total"
						@change="handlePageChange"
						:current="currentPage"
						:page-size-options="[24, 48, 96, 192]"
						:page-size="pageSize"
						@page-size-change="pageSizeChange"
						size="medium"
						show-total
						show-page-size
					/>
				</div>
			</div>
		</div>
		<arco-drawer
			:width="1180"
			@cancel="handlePropertyclose"
			:drawerStyle="{ overflow: 'unset' }"
			:visible="visible"
			body-class="drwer_content"
			:header="false"
			:footer="false"
		>
			<div class="propertyclose" @click="handlePropertyclose">
				<img src="@/assets/propertyclose.png" alt="" />
			</div>
			<div class="container_drwer">
				<div class="mainTitle1">{{ onpropertyObj.company }}</div>
				<div class="drcontent" @scroll="handleScroll">
					<div v-for="(item, index) in companyDataList" :key="index" class="contents">
						<div class="left_content">
							<img :src="`${proxyAddress}${item.buildingMainImg}`" alt="" />
						</div>
						<div class="right_content">
							<div class="top_tent">
								<div class="top_text">{{ item.buildingName }}</div>
								<div class="topt_text" :style="handlebuildingRateColor(item.buildingRate)">{{ item.buildingRate }}级</div>
								<!-- <div class="topth_text">写字楼</div> -->
							</div>
							<div class="center_tent">
								<div class="center_tent_left">
									<el-icon><Location /></el-icon>
									<div class="center_tent_left_text">{{ item.street }}</div>
								</div>
								<div class="center_tent_center">
									<div class="center_tent_center_lable">物业公司：</div>
									<div class="center_tent_left_text">{{ item.buildingManager }}</div>
								</div>
								<div class="center_tent_center">
									<div class="center_tent_center_lable">产权公司：</div>
									<div class="center_tent_left_text">{{ item.propertyOwner }}</div>
								</div>
							</div>
							<div class="footer_tent">
								<div v-for="(childItem, childIndex) in boxDetailsList" :key="childIndex" class="footer_tent_contents">
									<div class="footer_tent_contents_value" :style="handleKeyValue(childItem.key, item, childItem.name)">
										{{ handleshowKey(childItem.key, item) }}
									</div>
									<div class="footer_tent_contents_title">{{ childItem.name }}</div>
									<div class="footer_tent_line" v-if="childIndex !== boxDetailsList.length - 1"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</arco-drawer>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getManagerCompanyList, getDictList, getCompanyRelatedBuildingList } from '@/api/syt.js';
import {vuexStore} from "@/store";
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const buildingTypes = ref([]);
const buildingTypesValue = ref('');
const data = ref([]);
const currentPage = ref(1);
const pageSize = ref(24);
const cascaderCity = ref(['不限']);
const total = ref();
const province = ref('');
const city = ref('');
const searchValue = ref('');
const visible = ref(false);
const onpropertyObj = ref({});
const companyDataList = ref([]);
const drawerPageTotal = ref(0);
const drawerPageCurrentPage = ref(1);
const drawerPagePageSize = ref(10);
const boxDetailsList = ref([
	{
		key: 'buildYear',
		name: '建成年份',
	},
	{
		key: 'maintenance',
		name: '维护情况',
	},
	{
		key: 'regionalPotential',
		name: '区域潜力',
	},
	{
		key: 'businessDynamism',
		name: '商业活力',
	},
	{
		key: 'spendingPower',
		name: '人均消费(元)',
	},
	{
		key: 'evaluation',
		name: '单价(元)',
	},
	{
		key: 'evaComparableValueRank',
		name: '估值排名',
	},
	{
		key: 'secComparableValueRank',
		name: '证券化排名',
	},
]);

// 城市选择options
const cityOptions = computed(() => {
  if (vuexStore.state?.cityArray?.length) {
    return [{ label: '不限' }, ...vuexStore.state.cityArray];
  } else {
    return  [];
  }
})

onMounted(() => {
	getDict();
	initData();
});

const handlePageChange = (page) => {
	currentPage.value = page;
	initData();
};

const handleChange = (val) => {
  if (val && val[0] !== '不限') {
    city.value = val[0];
    province.value = val[1];
  } else {
    city.value = undefined;
    province.value = undefined;
  }
};

const onSubmit = () => {
	currentPage.value = 1;
	getDict();
	initData();
};

// 重置
function onReset() {
	currentPage.value = 1;
	cascaderCity.value = [];
	city.value = '';
	province.value = '';
	buildingTypesValue.value = null;
	searchValue.value = [];
	initData();
}

const pageSizeChange = (val) => {
	pageSize.value = val;
	initData(val);
};
const initData = async (val) => {
	const queryParams = {
		city: city.value,
		district: province.value,
		buildingType: buildingTypesValue.value,
		keyword: searchValue.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		type: 1,
	};
	await getManagerCompanyList(queryParams)
		.then((res) => {
			data.value = res.data.rows;
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};

function handleScroll(event) {
	const { scrollTop, clientHeight, scrollHeight } = event.target;
	if (scrollTop + clientHeight >= scrollHeight) {
		if (drawerPageCurrentPage.value * drawerPagePageSize.value >= drawerPageTotal.value) {
			return;
		}
		drawerPageCurrentPage.value++;
		handlegetCompanyRelatedBuildingList('1');
	}
}

function handlegetCompanyRelatedBuildingList(val) {
	const params = {
		companyName: onpropertyObj.value.company,
		companyType: onpropertyObj.value.companyType,
		currentPage: drawerPageCurrentPage.value,
		pageSize: drawerPagePageSize.value,
		type: 1,
	};
	getCompanyRelatedBuildingList(params).then((res) => {
		visible.value = true;
		if (val) {
			companyDataList.value = [...companyDataList.value, ...res.data.rows];
		} else {
			companyDataList.value = res.data.rows;
		}
		drawerPageTotal.value = res.data.total;
	});
}

const onpropertyHome = (item) => {
	// const type = 1;

	// router.push({
	// 	// path: '/propertyHome',
	// 	path: '/shangYutong/participator/manageDetails',
	// 	query: {
	// 		companyName: item.company,
	// 		companyType: item.companyType,
	// 		avatar: item.companyLogo,
	// 		type: type,
	// 	},
	// });

	onpropertyObj.value = item;
	drawerPageCurrentPage.value = 1;
	handlegetCompanyRelatedBuildingList();
};

function handleshowKey(param, item) {
	if (param == 'evaComparableValueRank') {
		return `${item.evaComparableValueRank}/${item.evaComparableValueRankTotal}`;
	} else if (param == 'secComparableValueRank') {
		return `${item.secComparableValueRank}/${item.secComparableValueRankTotal}`;
	} else {
		return item[param];
	}
}

function handleKeyValue(key, item, name) {
	if (name === '维护情况') {
		if (item[key] == '无维护') {
			return {
				color: `#9FD4FD`,
			};
		} else if (item[key] == '一般') {
			return {
				color: `#57A9FB`,
			};
		} else if (item[key] == '良好') {
			return {
				color: `#3491FA`,
			};
		} else if (item[key] == '优秀') {
			return {
				color: `#206CCF`,
			};
		} else if (item[key] == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
	if (name === '区域潜力') {
		if (item[key] == '上升') {
			return {
				color: `#89D178`,
			};
		} else if (item[key] == '维持') {
			return {
				color: `#0FC6C2`,
			};
		} else if (item[key] == '稳定') {
			return {
				color: `#3491FA`,
			};
		} else if (item[key] == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
	if (name === '商业活力') {
		if (item[key] == '初期') {
			return {
				color: `#89D178`,
			};
		} else if (item[key] == '活跃') {
			return {
				color: `#FF9A2E`,
			};
		} else if (item[key] == '繁荣') {
			return {
				color: `#F76560`,
			};
		} else if (item[key] == '暂无') {
			return {
				color: `#C9CDD4`,
			};
		}
	}
}

function handlePropertyclose() {
	visible.value = false;
}

function handlebuildingRateColor(level) {
	if (level == 'S') {
		return {
			background: `linear-gradient(90deg, #9D71DA 0%, #722ED1 100%)`,
		};
	}
	if (level == 'A+') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}
	if (level == 'A') {
		return {
			background: `linear-gradient(90deg, #77A9FF 0%, #1868F1 100%)`,
		};
	}

	if (level == 'B+') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}
	if (level == 'B') {
		return {
			background: `linear-gradient(90deg, #24D3CF 0%, #04AFAB 100%)`,
		};
	}

	if (level == 'C') {
		return {
			background: `linear-gradient(90deg, #FFA44D 0%, #FF7D00 100%)`,
		};
	}
}

const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
      buildingTypes.value = [{ label: '不限', value: ''}, ...res.data];
		})
		.catch((err) => {
			console.log(err);
		});
};
</script>
<style lang="scss">
.drwer_content {
	position: relative;
	background: #f7f8fa;
	overflow: unset;
	padding: 0px;
}
</style>
<style lang="less" scoped>
.content_box {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);
	.container_box {
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		.body_main {
			width: 100%;
			height: calc(100vh - 122px);
			background-color: rgba(255, 255, 255, 1);
			box-sizing: border-box;
			position: relative;
			.search_box {
				width: 100%;
				overflow: hidden;
				padding: 20px 16px;
				box-sizing: border-box;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.box_1 {
					height: 32px;
					margin-right: 40px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					box-sizing: border-box;
					::v-deep .arco-select-view-focus {
						border: 1px solid #409eff !important;
					}
					::v-deep .arco-select-view-single {
						border: 1px solid #e5e6eb;
						background-color: #fff;
						height: 32px;
						border-radius: 4px;
					}
					::v-deep .arco-input-wrapper {
						background: none !important;
						border: 1px solid #e5e6eb;
						border-radius: 4px;
					}

					::v-deep .arco-input-wrapper:hover {
						border: 1px solid #1868f1;
					}
					::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
						box-shadow: 0;
					}

					.label {
						color: #4e5969;
						margin-right: 16px;
						font-weight: 400;
						font-size: 14px;
						color: rgba(134, 144, 156, 1);
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
				.box_2 {
					height: 32px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					box-sizing: border-box;
				}
			}

			.card_list {
				width: 100%;
				height: 560px;
				padding: 0 0px 0 16px;
				display: flex;
				justify-content: flex-start;
				align-content: flex-start;
				flex-wrap: wrap;
				overflow: scroll;
				box-sizing: border-box;
				gap: 16px;
				.card_body {
					width: 391px;
					height: 80px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					background-color: rgba(245, 246, 247, 1);
					border-radius: 4px;
					padding: 12px 16px;
					cursor: pointer;
					box-sizing: border-box;
					.avatar {
						width: 56px;
						height: 56px;
						border-radius: 4px;
						overflow: hidden;
						margin-right: 12px;
						img {
							width: 100%;
							height: 100%;
						}
					}
					.tips_box {
						width: 292px;
						height: 50px;
						display: flex;
						justify-content: center;
						align-items: flex-start;
						flex-direction: column;
						.nickName {
							font-weight: 500;
							font-size: 14px;
							line-height: 22px;
							color: #1d2129;
						}
					}
				}
			}
		}
	}
}
.pagination_content {
	margin: 20px 16px 0 0;
	display: flex;
	justify-content: flex-end;
}
.propertyclose {
	position: absolute;
	top: 0px;
	left: -24px;
	img {
		cursor: pointer;
		width: 24px;
		height: 40px;
	}
}
.container_drwer {
	width: 100%;
	height: calc(100vh - 0px);
	box-sizing: border-box;
	background: url(../../../../assets/img_drwer.png);
	background-size: cover;
	background-repeat: no-repeat;
	padding: 30px 16px 16px 16px;
	.mainTitle1 {
		width: 100%;
		height: 28px;
		font-weight: 500;
		font-size: 20px;
		line-height: 28px;
		display: flex;
		padding-bottom: 16px;
		justify-content: flex-start;
		align-items: center;
		color: #1d2129;
		&::before {
			content: '';
			width: 5px;
			height: 14px;
			background: linear-gradient(180deg, #9b6ff7 0%, #1868f1 100%);
			border-radius: 10px;
			margin-right: 8px;
		}
	}
	.drcontent {
		height: calc(100vh - 74px);
		overflow: scroll;
		.contents {
			height: 168px;
			padding: 20px;
			border-radius: 4px;
			box-sizing: border-box;
			background: #ffffff;
			margin-bottom: 16px;
			width: 100%;
			display: flex;
			align-items: center;
			.left_content {
				width: 128px;
				height: 128px;
				margin-right: 16px;
				img {
					width: 100%;
					height: 100%;
					border-radius: 4px;
				}
			}
			.right_content {
				width: calc(100% - 134px);
				height: 100%;
				.top_tent {
					display: flex;
					height: 28px;
					align-items: center;
					gap: 8px;
					margin-bottom: 4px;
					.top_text {
						font-weight: 500;
						font-size: 18px;
						line-height: 28px;
						color: #1d2129;
					}
					.topt_text {
						background: linear-gradient(90deg, #77a9ff 0%, #1868f1 100%);
						height: 20px;
						border-radius: 2px;
						padding: 1px 8px;
						color: #ffffff;
						font-weight: 500;
						font-size: 12px;
						line-height: 20px;
					}
					.topth_text {
						background: #e8f3ff;
						height: 20px;
						border-radius: 2px;
						padding: 1px 8px;
						color: #1868f1;
						font-weight: 500;
						font-size: 12px;
						line-height: 20px;
					}
				}

				.center_tent {
					display: flex;
					height: 22px;
					align-items: center;
					gap: 24px;
					margin-bottom: 24px;
					.center_tent_left {
						display: flex;
						align-items: center;
					}
					.center_tent_left_text {
						font-weight: 400;
						font-size: 14px;
						line-height: 22px;
						margin-left: 4px;
						color: #4e5969;
					}
					.center_tent_center {
						display: flex;
						align-items: center;
						.center_tent_center_lable {
							font-weight: 400;
							font-size: 14px;
							line-height: 22px;
							color: #1d2129;
						}
					}
				}

				.footer_tent {
					display: flex;
					align-items: center;
					gap: 25px;
					.footer_tent_contents {
						position: relative;
						display: flex;
						flex-direction: column;
						align-content: center;
						width: 96px;
						height: 44px;
						gap: 4px;
						.footer_tent_contents_value {
							font-weight: 600;
							font-size: 18px;
							line-height: 18px;
							text-align: center;
							color: #1d2129;
						}
						.footer_tent_contents_title {
							font-weight: 400;
							font-size: 14px;
							line-height: 22px;
							text-align: center;
							color: #4e5969;
						}
						.footer_tent_line {
							position: absolute;
							right: -12.9px;
							top: 12px;
							width: 1px;
							height: 20px;
							background: #e5e6eb;
						}
					}
				}
			}
		}
	}
}
</style>
