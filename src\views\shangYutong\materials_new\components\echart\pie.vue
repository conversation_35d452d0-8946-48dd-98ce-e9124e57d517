<template>
	<div class="pie_boxEcharts" ref="echartsPie"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { nextTick, onMounted, ref, watch } from 'vue';
const echartsPie = ref(null);
let myChart = null;
const props = defineProps({
	pieData: {
		type: Object,
		default: () => {},
	},
	otherOpts: {
		type: Object,
		default: () => {},
	},
});
let chartOption = {
	color: [
		'#378EFF',
		'#37B7FF',
		'#39DDE8',
		'#2FE2AC',
		'#63DC6F',
		'#ADE369',
		'#FFCF63',
		'#FF9A2E',
		'#F76060',
		'#F088CA',
		'#B380F4',
		'#9EA7FF',
		'#637BFA',
	],
	title: {
		text: '',
		left: 'center',
		top: '45%',
		textStyle: {
			fontSize: 16, // 设置标题字体大小为20
			color: '#1D2129',
			width: 100, // 设置标题的最大宽度
			overflow: 'break', // 超出宽度时换行
		},
	},
	tooltip: {
		trigger: 'item',
		// appendToBody: true,
		backgroundColor: 'rgba(244, 247, 252, .8)',
		borderColor: 'transparent',
		borderRadius: 4,
		// borderWidth: 1,
		// padding: 6,
		formatter: function (params) {
			return `
			<div style="display: flex; align-items: center;background-color: #fff; padding: 9px 12px; border-radius: 4px;">
				<div style="width:8px;height:8px;background-color:${params.color};margin-right: 8px;border-radius: 50%;">
				</div>
				<div style="font-size: 14px; color: #4E5969; font-weight: 400;margin-right: 20px;">
					${params.name}
				</div>
				<div style="font-size: 14px; color: #1D2129; font-weight: 600">
					${params.percent}%
				</div>
			</div>
			`;
		},
	},
	series: [
		{
			type: 'pie',
			radius: ['50%', '70%'],

			label: {
				show: false,
				position: 'center',
			},
			avoidLabelOverlap: false,
			padAngle: 2,
			itemStyle: {
				borderRadius: 2,
			},
			data: [],
		},
	],
};
onMounted(() => {});
onBeforeUnmount(() => {
	// 在组件卸载时销毁图表
	if (myChart) {
		myChart.dispose();
	}
});
// watch(
// 	() => props.pieData,
// 	(newData, oldData) => {
// 		if (newData) {
// 			myChart = echarts.init(echartsPie.value);
// 			myChart.setOption(chartOption);
// 			setTimeout(() => {
// 				chartOption.title.text = newData.name;
// 				chartOption.series[0].data = newData.data;
// 				myChart.setOption(chartOption);
// 			}, 100);
// 		} else {
// 			if (myChart) {
// 				myChart.dispose();
// 			}
// 		}
// 	},
// 	{
// 		immediate: true,
// 	}
// );
onMounted(() => {});
function init(opts) {
	myChart = echarts.init(echartsPie.value);
	myChart.setOption(chartOption);
	setTimeout(() => {
		chartOption.title.text = props.pieData.name;
		chartOption.series[0].data = props.pieData.data;
		chartOption = { ...chartOption, ...props.otherOpts };
		if (opts) {
			chartOption.series[0].center = opts.center;
			chartOption.title.left = '34%';
			chartOption.title.top = '50%';
			chartOption.title.textVerticalAlign = 'middle';
			chartOption.title.textAlign = 'center';
		}
		myChart.setOption(chartOption);
	}, 100);
}
defineExpose({
	init,
});
</script>

<style lang="less" scoped>
.pie_boxEcharts {
	// width: 320px;
	width: 100%;
	height: 280px;
	box-sizing: border-box;
}
</style>
