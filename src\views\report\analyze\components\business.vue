<template>
	<div v-if="basic.lists2 && basic.lists2.length > 0" class="office_wrap">
		<div class="title">四、招商</div>
		<div class="desc_title">{{ basic.buildingName }}写字楼租户调查</div>
		<div class="desc">
			<p>
				2020年至今，新冠疫情使写字楼办公企业的商业活动收到不同程度的影响。{{ basic.city }}作为商业活动中心，展示出坚强的韧性，在需求端有较好表现。
			</p>
			<p>
				{{ basic.buildingName }}写字楼作为单一产权优质甲级写字楼，商业环境优越。术木智能对{{
					basic.buildingName
				}}写字楼进行了全面普查，对比2023年写字楼需求结构，并进一步对重点租户从租户普查、需求势能、租赁策略三个角度，综合得出租赁用户画像，对比{{
					basic.city
				}}与全国租赁需求差异，探寻{{ basic.buildingName }}写字楼租赁的未来趋势。
			</p>
		</div>
		<div class="sub_title">4.1租户普查</div>
		<div class="desc_title">
			{{ basic?.lists2?.[3]?.tenantCensusText.text1 }}
		</div>
		<div class="desc">
			<p>{{ basic?.lists2?.[3]?.tenantCensusText.text2 }}</p>
			<p>{{ basic?.lists2?.[3]?.tenantCensusText.text3 }}</p>
			<p>{{ basic?.lists2?.[3]?.tenantCensusText.text4 }}</p>
		</div>
		<table class="base_table table1" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th colspan="8">{{ getYear() }}年{{ basic.buildingName }}写字楼租户构成（按租户数量）</th>
				</tr>
				<tr>
					<th colspan="4">金融</th>
					<th colspan="2">租赁和商务服务</th>
					<th colspan="2">批发和零售</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>货币市场</td>
					<td>资本市场</td>
					<td>保险业</td>
					<td>其他金融</td>
					<td>租赁</td>
					<td>商务服务</td>
					<td>批发</td>
					<td>零售</td>
				</tr>
				<tr style="text-align: center">
					<th colspan="4">{{ basic?.lists2?.[0]?.finance?.total }}%</th>
					<th colspan="2">{{ basic?.lists2?.[0]?.businessServices?.total }}%</th>
					<th colspan="2">{{ basic?.lists2?.[0]?.wholesaleRetail?.total }}%</th>
				</tr>
			</tbody>
			<thead>
				<tr>
					<th colspan="3">信息技术</th>
					<th colspan="5">其他</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>电信广播</td>
					<td>互联网</td>
					<td>软件和信息技术</td>
					<td>制造</td>
					<td>房地产</td>
					<td>交通运输</td>
					<td>科学研究</td>
					<td>其他</td>
				</tr>
				<tr>
					<th colspan="3">{{ basic?.lists2?.[0]?.IT?.total }}%</th>
					<th colspan="5">{{ basic?.lists2?.[0]?.other?.total }}%</th>
				</tr>
			</tbody>
		</table>
		<div class="desc_title">{{ basic.leaseTop?.[0] }}与{{ basic.leaseTop?.[1] }}引领全年租赁需求</div>
		<div class="desc">
			<p>
				从2024年前半年看来， {{ basic.leaseTop?.[0] }}与{{ basic.leaseTop?.[1] }}仍是{{
					basic.city
				}}甲级写字楼新增租赁的主力行业来源。两大行业在国内经济发展大背景下，驱动租赁需求释放。房地产行业稳定发挥，除开发商升级搬迁外，第三方办公运营表现较好。
			</p>
			<p>
				尽管疫情在线下对{{ basic.leaseTop?.[0] }}造成负面影响，以{{ basic.leaseSecondaryTop?.[0]
				}}{{ basic.leaseSecondaryTop?.[1] ? '、' + basic.leaseSecondaryTop?.[1] : ''
				}}{{ basic.leaseSecondaryTop?.[2] ? '、' + basic.leaseSecondaryTop?.[2] : '' }}为代表的{{
					basic.leaseTop?.[0]
				}}企业仍在不确定因素下展现出强劲韧性，在上半年新租需求中位居前列。
			</p>
		</div>
		<table class="base_table table3" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th colspan="3">{{ basic.city }}甲级写字楼新增租赁行业占比</th>
				</tr>
			</thead>
			<tbody style="text-align: center;">
				<tr>
					<th rowspan="3">制造业</th>
					<td>轻纺工业</td>
					<td rowspan="3">{{basic?.lists2?.[1]?.fabricate}}%</td>
				</tr>
				<tr>
					<td>资源加工工业</td>
				</tr>
				<tr>
					<td>机械、电子制造业</td>
				</tr>
				<tr>
					<th rowspan="4">金融</th>
					<td>保险</td>
					<td rowspan="4">{{basic?.lists2?.[1]?.finance}}%</td>
				</tr>
				<tr>
					<td>证券</td>
				</tr>
				<tr>
					<td>基金</td>
				</tr>
				<tr>
					<td>银行</td>
				</tr>
				<tr>
					<th rowspan="3">信息技术</th>
					<td>电信广播</td>
					<td rowspan="3">{{basic?.lists2?.[1]?.infoTech}}%</td>
				</tr>
				<tr>
					<td>互联网</td>
				</tr>
				<tr>
					<td>软件和信息技术</td>
				</tr>
				<tr>
					<th rowspan="2">租赁和商务服务</th>
					<td>租赁</td>
					<td rowspan="2">{{basic?.lists2?.[1]?.leaseBusiness}}%</td>
				</tr>
				<tr>
					<td>商务服务</td>
				</tr>
				<tr>
					<th rowspan="3">房地产</th>
					<td>开发商</td>
					<td rowspan="3">{{basic?.lists2?.[1]?.realEstate}}%</td>
				</tr>
				<tr>
					<td>工程建筑</td>
				</tr>
				<tr>
					<td>设计</td>
				</tr>
				<tr>
					<th rowspan="3">科学研究</th>
					<td>基础研究</td>
					<td rowspan="3">{{basic?.lists2?.[1]?.scienceResearch}}%</td>
				</tr>
				<tr>
					<td>应用研究</td>
				</tr>
				<tr>
					<td>试验发展</td>
				</tr>
				<tr>
					<th rowspan="4">交通运输</th>
					<td>公路运输</td>
					<td rowspan="4">{{basic?.lists2?.[1]?.transportation}}%</td>
				</tr>
				<tr>
					<td>水路运输</td>
				</tr>
				<tr>
					<td>航空运输</td>
				</tr>
				<tr>
					<td>城市交通</td>
				</tr>
				<tr>
					<th rowspan="2">批发零售</th>
					<td>批发</td>
					<td rowspan="2">{{basic?.lists2?.[1]?.wholesaleRetail}}%</td>
				</tr>
				<tr>
					<td>零售</td>
				</tr>
				<tr>
					<th>其他</th>
					<td colspan="2">{{basic?.lists2?.[1]?.other}}%</td>
				</tr>
			</tbody>
		</table>
		<div class="sub_title">4.2需求势能</div>
		<div class="desc_title">超过两成企业计划增加办公室面积</div>
		<div class="desc">
			<p>
				30%的{{ basic.city }}受访企业预计将在2024年下半年增加办公面积，与全国调研数据几乎持平。而59%的受访企业表示维持现有办公面积，明显高于全国比例。
			</p>
			<p>有8%的受访企业计划缩减面积，明显低于全国平均水平。而3%的受访企业表示不确定。</p>
		</div>
		<table class="base_table table2" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th colspan="5">{{ basic.city }}与全国办公室面积变化预测</th>
				</tr>
			</thead>
			<tbody style="font-weight: 600">
				<tr style="font-weight: normal">
					<td></td>
					<td>增加</td>
					<td>保持不变</td>
					<td>减少</td>
					<td>不确定</td>
				</tr>
				<tr>
					<td style="font-weight: normal">青岛</td>
					<td>30%</td>
					<td>59%</td>
					<td>8%</td>
					<td>3%</td>
				</tr>
				<tr>
					<td style="font-weight: normal">全国</td>
					<td>32%</td>
					<td>38%</td>
					<td>24%</td>
					<td>6%</td>
				</tr>
			</tbody>
		</table>
		<div class="desc_title">{{ basic.expandTop }}扩张意愿明显</div>
		<div class="desc">
			<p>
				{{ basic.district }}受访企业中，{{ basic.expandTopPercentage }}的{{ basic.expandTop }}计划增加办公面积，扩张意愿最为明显。从2024年上半年{{
					basic.district
				}}甲级写字楼租赁需求看，{{ basic.expandTop }}连续两个季度占比第一。
			</p>
			<p>而其中的本地企业，一直是新增租赁市场主要资金来源。</p>
		</div>
		<table class="base_table table3" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th colspan="3">2024年下半年预期办公室面积增加的内外资占比</th>
				</tr>
			</thead>
			<tbody style="font-weight: 600">
				<tr>
					<td>内资企业</td>
					<td>外资企业</td>
					<td>本地企业</td>
				</tr>
				<tr style="font-weight: 600">
					<td>{{ basic?.lists2?.[6][0]?.domesticCapitalPercentage }}%</td>
					<td>{{ basic?.lists2?.[6][0]?.foreignCapitalPercentage }}%</td>
					<td>{{ basic?.lists2?.[6][0]?.localPercentage }}%</td>
				</tr>
			</tbody>
		</table>
		<div class="sub_title">4.3租赁策略</div>
		<div class="desc_title">租赁写字楼仍是企业办公的主要选择</div>
		<div class="desc">
			<p>
				58%的{{
					basic.district
				}}受访企业表示，在扩张办公室面积时，租赁写字楼是他们最倾向的物业类型。在各种办公物业类型中，租赁写字楼占据了绝对的首选位置。
			</p>
			<p>
				同时21%受访企业也表示，由于疫情带来的居家的办公形态的转变和对未来经济的不可预见性，开始愿意考虑使用灵活办公空间，这超过了其他物业类型位居第二。
			</p>
		</div>
		<table class="base_table table4" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th colspan="6">企业办公物业类型偏好</th>
				</tr>
				<tr>
					<td>租赁写字楼</td>
					<td>使用灵活办公空间</td>
					<td>租赁商务园区办公楼</td>
					<td>自建企业总部</td>
					<td>购置写字楼</td>
					<td>购置商务园区办公楼</td>
				</tr>
				<tr style="font-weight: 600">
					<td>58%</td>
					<td>21%</td>
					<td>16%</td>
					<td>9%</td>
					<td>7%</td>
					<td>3%</td>
				</tr>
			</thead>
		</table>
		<div class="desc_title">各行业不动产策略出现分化</div>
		<div class="desc">
			<p>
				行业特征决定了不动产的选择策略。例如消费和工业和商业服务将利用租金下降的窗口期对办公地点进行升级和扩租，而房地产行业由于线下销售部分前几年受到疫情和宏观经济环境预期的影响而被迫收缩。
			</p>
			<p>科技行业倾向于总部办公，而消费和原材料和通讯服务业 ，更多地选择分散办公。</p>
		</div>
		<table class="base_table" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th colspan="2">不同行业企业不动产策略选择差异</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="item in arr">
					<td style="width: 30%">{{ item.title }}：</td>
					<td>{{ item.des }}</td>
				</tr>
			</tbody>
		</table>
		<div class="desc_title">交通便利是关键因素</div>
		<div class="desc">
			<p>交通便利性仍是企业最关注因素，另外楼宇及商圈配套也在租赁决策中占重要位置。</p>
		</div>
		<table class="base_table" border="1" cellpadding="8" cellspacing="0">
			<thead>
				<tr>
					<th colspan="10">企业租赁决策的配套、服务偏好</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td style="width: 10%" v-for="item in arr2">{{ item.title }}</td>
				</tr>
				<tr>
					<td style="width: 10%; font-weight: 600" v-for="item in arr2">{{ item.percent }}</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div v-else-if="lingshow">
		<div class="lingshow">
			<div class="title">四、招商</div>
			<div class="sub_title">4.1品牌分布</div>
			<table class="custom-table" border="1" cellpadding="8" cellspacing="0">
				<thead>
					<tr>
						<th style="width: 50px">楼层</th>
						<th style="width: 80px">业态</th>
						<th>商户名称</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="(item, index) in lingshow.commerceRetailShoppingMallVoList" :key="index">
						<td v-if="index == 0 || item.floor != lingshow.commerceRetailShoppingMallVoList[index - 1].floor" :rowspan="item.floorCount">
							{{ item.floor }}
						</td>
						<td>{{ item.commercial }}</td>
						<td>{{ item.merchantName }}</td>
					</tr>
					<tr style="font-weight: 600">
						<td>出租率</td>
						<td v-if="lingshow.rent_rate" colspan="2">{{ lingshow.rent_rate }}%</td>
						<td v-else colspan="2">暂无数据</td>
					</tr>
				</tbody>
			</table>
			<div class="sub_title">4.2、品牌分析</div>
			<table class="custom-table2" border="1" cellpadding="8" cellspacing="0">
				<thead>
					<tr>
						<td colspan="7" style="font-weight: 600">业态组合</td>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td v-for="(item, index) in lingshow.commerceRetailAnalyzeTableData.commercial">{{ item }}</td>
					</tr>
					<tr>
						<td :style="{ 'font-weight': index != 0 ? '600' : '' }" v-for="(item, index) in lingshow.commerceRetailAnalyzeTableData.num">
							{{ item }}
						</td>
					</tr>
					<tr>
						<td :style="{ 'font-weight': index != 0 ? '600' : '' }" v-for="(item, index) in lingshow.commerceRetailAnalyzeTableData.percentage">
							{{ item }}{{ index == 0 ? '' : '%' }}
						</td>
					</tr>
					<!-- TODO 内容待定 -->
					<!-- <tr>
						<td colspan="7" style="font-weight: 600">业态配比</td>
					</tr>
					<tr>
						<td colspan="7">内容待定</td>
					</tr>
					<tr>
						<td colspan="7" style="font-weight: 600">品牌布局</td>
					</tr>
					<tr>
						<td colspan="7">内容待定</td>
					</tr>
					<tr>
						<td colspan="7" style="font-weight: 600">总体定位</td>
					</tr>
					<tr>
						<td colspan="7">内容待定</td>
					</tr> -->
					<tr>
						<td colspan="1">品牌定位</td>
						<td colspan="6">{{ lingshow?.commerceRetailPositionVo?.brandPositioning }}</td>
					</tr>
					<tr>
						<td colspan="1">客群定位</td>
						<td colspan="6">{{ lingshow?.commerceRetailPositionVo?.customerName }}</td>
					</tr>
					<tr>
						<td colspan="1">业态组合</td>
						<td colspan="6">{{ lingshow?.commerceRetailPositionVo?.businessMix }}</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>
<script setup>
import { ref } from 'vue';
import { business, businesstwo } from '@/api/baogao';
import { getYear } from '@/utils/time';

const props = defineProps({
	id: {
		type: String,
		default: null,
		required: true,
	},
});
const id = ref('');
const lingshow = ref(null);

const imgs = ref([]);
const basic = ref({});
const qiye = ref({});
const shu = ref({});
id.value = props.id;
getxie();
getling();
async function getxie() {
	const res = await business({ buildingId: id.value });
	if (res.code === 200) {
		basic.value = res.result;
		imgs.value = res.result.buildPicList;
		qiye.value = res.result?.lists2?.[5];
		shu.value = res.result?.lists2?.[4];
		arr2.value = [
			{
				title: '公共/城际交通',
				percent: shu.value?.publicIntercityTraffic + '%',
			},
			{
				title: '楼宇及商圈配套',
				percent: shu.value?.buildingDistrictAncillary + '%',
			},
			{
				title: '楼内租户结构',
				percent: shu.value?.tenantStructure + '%',
			},

			{
				title: '业主声誉',
				percent: shu.value?.ownersReputation + '%',
			},
			{
				title: '物业管理团队',
				percent: shu.value?.propertyManage + '%',
			},
			{
				title: '单一业权',
				percent: shu.value?.singleTitle + '%',
			},
			{
				title: '精装修服务',
				percent: shu.value?.flexibleOfficeConfig + '%',
			},
			{
				title: '灵活办公空间配置',
				percent: shu.value?.flexibleOfficeConfig + '%',
			},
			{
				title: '楼内共享办公室',
				percent: shu.value?.sharedMeetingRoom + '%',
			},
			{
				title: '智能楼宇管理系统',
				percent: shu.value?.intelligentBuildingManage + '%',
			},
		];
	}
}
async function getling() {
	const res = await businesstwo({ buildingId: id.value });
	if (res.code === 200) {
		lingshow.value = res.result;
		lingshow.value.commerceRetailShoppingMallVoList = addFloorCount(lingshow.value.commerceRetailShoppingMallVoList);
		lingshow.value.commerceRetailAnalyzeVoList.unshift({
			commercial: '业态',
			num: '数量',
			percentage: '比例',
		});
		lingshow.value.commerceRetailAnalyzeTableData = {
			commercial: [],
			num: [],
			percentage: [],
		};
		lingshow.value.commerceRetailAnalyzeVoList.forEach((item) => {
			lingshow.value.commerceRetailAnalyzeTableData.commercial.push(item.commercial);
			lingshow.value.commerceRetailAnalyzeTableData.num.push(item.num);
			lingshow.value.commerceRetailAnalyzeTableData.percentage.push(item.percentage);
		});
	}
}

// 统计每个 floor 的数量，并将统计结果放到每个 floor 的第一项
function addFloorCount(data) {
	const floorMap = new Map(); // 用 Map 分组以统计每个 floor 的数量

	data.forEach((item) => {
		const { floor } = item;

		// 如果 Map 中不存在该 floor，初始化为一个空数组
		if (!floorMap.has(floor)) {
			floorMap.set(floor, []);
		}

		// 将当前项添加到对应 floor 的数组中
		floorMap.get(floor).push(item);
	});

	// 遍历 floorMap，为每个 floor 的第一项添加 floorCount 属性
	floorMap.forEach((items, floor) => {
		const floorCount = items.length; // 获取当前 floor 的条目总数
		items[0].floorCount = floorCount; // 在第一项中增加 floorCount 属性
	});

	// 将分组后的数据重新组合为一个数组
	return Array.from(floorMap.values()).flat();
}

const arr2 = ref();
const arr = ref([
	{
		icon: 'UploadFilled',
		title: '升级扩租',
		des: '疫情过后，消费和工商业服务行业呈现复苏态势，商业活力增强，对于办公地点的需求也呈现上涨的需求态势。',
	},
	{
		icon: 'location',
		title: '区位搬迁',
		des: '专业服务、科技互联网、房地产建筑表现出去中心化特征;金融业则与之相反，表现出再中心化的特征;零售贸易、房地产为节约成本，意向搬迁至租金更低楼宇。',
	},
	{
		icon: 'Connection',
		title: '整合到更少的办公地点',
		des: '科技互联网行业出于优化办公效率和节约成本的考量，计划整合办公地点。',
	},
	{
		icon: 'OfficeBuilding',
		title: '分散到更多的办公地点',
		des: '消费服务行业出于临近客户的考量，更多选择分散办公地点;而房地产建筑行业更倾向项目团队办公而选择分散办公地点。',
	},
	{
		icon: 'coin',
		title: '将部分职能转移到较低成本城市',
		des: '房地产建筑倾向降低成本，选择将部分职能转移到成本更低的城市。',
	},
]);
</script>
<style lang="less" scoped>
.office_wrap {
	font-family: 'SimSun', '宋体', 'PingFang SC', 'Microsoft YaHei', serif;
	.title {
		font-size: 24px;
		font-weight: 600;
		margin: 16px 0;
	}
	.sub_title {
		font-size: 22px;
		font-weight: bold;
		margin-top: 16px;
		margin-bottom: 8px;
	}
	.desc_title {
		font-weight: 600;
		margin: 8px 0;
	}
	.base_table {
		width: 100%;
		/* 去掉重复边框 */
		border-collapse: collapse;
		margin-bottom: 16px;
	}
	.table1 {
		td {
			width: 100/9%;
		}
	}
	.table2 {
		td {
			width: 100/5%;
		}
	}
	.table3 {
		td {
			width: 100/3%;
		}
	}
	.table4 {
		td {
			width: 100/6%;
		}
	}
}
.lingshow {
	font-family: 'SimSun', '宋体', 'PingFang SC', 'Microsoft YaHei', serif;
	.title {
		font-size: 24px;
		font-weight: 600;
		margin: 16px 0;
	}
	.sub_title {
		font-size: 22px;
		font-weight: bold;
		margin-top: 16px;
		margin-bottom: 8px;
	}
	.custom-table,
	.custom-table2 {
		width: 100%;
		border-collapse: collapse;
		/* 去掉重复边框 */
		margin-bottom: 16px;
	}
	.custom-table th {
		background-color: #f2f2f2;
	}
	.custom-table2 {
		td {
			width: 100/7%;
		}
	}
}
</style>
