<template>
	<div class="previewReportContainer">
        <div class="header_wrap" @click="back">
            <el-icon style="margin-right: 4px;"><ArrowLeftBold /></el-icon>返回
        </div>
		<!-- 估值报告 -->
		<div ref="preview" class="preview" v-if="reportType === 'gaikuang'">
			<div v-for="(image, index) in gaikuangList" :key="index">
				<img :src="image" alt="" />
			</div>
		</div>
		<!-- 估值报告 -->
		<div ref="preview" class="preview" v-if="reportType === 'value'">
			<div v-for="(image, index) in valueList" :key="index">
				<img :src="image" alt="" />
			</div>
		</div>
		<!-- 半径人口报告 -->
		<div ref="preview" class="preview" v-if="reportType === 'population'">
			<div v-for="(image, index) in radiusList" :key="index">
				<img :src="image" alt="" />
			</div>
		</div>
		<!-- 客流量报告 -->
		<div ref="preview" class="preview" v-if="reportType === 'populationthree'">
			<div v-for="(image, index) in populationthree_demoList" :key="index">
				<img :src="image" alt="" />
			</div>
		</div>
		<!-- 租户 -->
		<div ref="preview" class="preview" v-if="reportType === 'business'">
			<div v-for="(image, index) in tenantList" :key="index">
				<img :src="image" alt="" />
			</div>
		</div>

		<!-- 市调报告 -->
		<div ref="preview" class="preview" v-if="reportType === 'location'">
			<div v-for="(image, index) in marketResearchList" :key="index">
				<img :src="image" alt="" />
			</div>
		</div>
		<!-- 社区人口报告 -->

		<div ref="preview" class="preview" v-if="reportType === 'populationtwo'">
			<div v-for="(image, index) in pdemoList" :key="index">
				<img :src="image" alt="" />
			</div>
		</div>

		<!-- 证券化报告 -->

		<div ref="preview" class="preview" v-if="reportType === 'security'">
			<div v-for="(image, index) in valuationSList" :key="index">
				<img :src="image" alt="" />
			</div>
		</div>
		<!-- 投资分析报告 -->
		<div ref="preview" class="preview" v-if="reportType === 'invest_analyze'">
			<div v-for="(image, index) in analyzeList" :key="index">
				<img :src="image" />
			</div>
		</div>
	</div>
</template>
<script setup>
import { useRoute,useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();

// 接收 query 参数
const reportType = route.query.reportType;
const reportTypeName = route.query.reportTypeName;

// 概况
const gaikuangList = ref([
	'https://static.biaobiaozhun.com/report/overviewdemo01.png',
	'https://static.biaobiaozhun.com/report/overviewdemo02.png',
	'https://static.biaobiaozhun.com/report/overviewdemo03.png',
	'https://static.biaobiaozhun.com/report/overviewdemo04.png',
]);

// 社区人口
const pdemoList = ref([
	'https://static.biaobiaozhun.com/report/populationtwodemo_page-0001.jpg',
	'https://static.biaobiaozhun.com/report/populationtwodemo_page-0002.jpg',
	'https://static.biaobiaozhun.com/report/populationtwodemo_page-0003.jpg',
]);
// 市调
const marketResearchList = ref([
	'https://static.biaobiaozhun.com/report/location_demo_01.jpg',
	'https://static.biaobiaozhun.com/report/location_demo_02.jpg',
	'https://static.biaobiaozhun.com/report/location_demo_03.jpg',
	'https://static.biaobiaozhun.com/report/location_demo_04.jpg',
	'https://static.biaobiaozhun.com/report/location_demo_05.jpg',
	'https://static.biaobiaozhun.com/report/location_demo_06.jpg',
	'https://static.biaobiaozhun.com/report/location_demo_07.jpg',
	'https://static.biaobiaozhun.com/report/location_demo_08.jpg',
	'https://static.biaobiaozhun.com/report/location_demo_09.jpg',
]);
// 租户
const tenantList = ref([
	'https://static.biaobiaozhun.com/report/office_demo01.png',
	'https://static.biaobiaozhun.com/report/office_demo02.png',
	'https://static.biaobiaozhun.com/report/office_demo03.png',
	'https://static.biaobiaozhun.com/report/office_demo04.png',
	'https://static.biaobiaozhun.com/report/office_demo05.png',
	'https://static.biaobiaozhun.com/report/office_demo06.png',
	'https://static.biaobiaozhun.com/report/office_demo07.png',
	'https://static.biaobiaozhun.com/report/office_demo08.png',
	'https://static.biaobiaozhun.com/report/office_demo09.png',
	'https://static.biaobiaozhun.com/report/office_demo10.png',
]);
// 半径人口
const radiusList = ref([
	'https://static.biaobiaozhun.com/report/population_demo_01.png',
	'https://static.biaobiaozhun.com/report/population_demo_02.png',
	'https://static.biaobiaozhun.com/report/population_demo_03.png',
]);
// 客流量
const populationthree_demoList = ref(['https://static.biaobiaozhun.com/report/populationthree_demo.png']);

// 价值报告
const valueList = ref([
	'https://static.biaobiaozhun.com/report/value_page-0001.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0002.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0003.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0004.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0005.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0006.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0007.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0008.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0009.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0010.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0011.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0012.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0013.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0014.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0015.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0016.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0017.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0018.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0019.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0020.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0021.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0022.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0023.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0024.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0025.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0026.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0027.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0028.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0029.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0030.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0031.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0032.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0033.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0034.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0035.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0036.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0037.jpg',
	'https://static.biaobiaozhun.com/report/value_page-0038.jpg',
]);
// 证券化
const valuationSList = ref([
	'https://static.biaobiaozhun.com/report/security-demo_page-0001.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0002.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0003.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0004.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0005.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0006.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0007.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0008.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0009.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0010.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0011.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0012.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0013.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0014.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0015.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0016.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0017.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0018.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0019.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0020.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0021.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0022.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0023.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0024.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0025.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0026.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0027.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0028.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0029.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0030.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0031.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0032.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0033.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0034.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0035.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0036.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0037.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0038.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0039.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0040.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0041.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0042.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0043.jpg',
	'https://static.biaobiaozhun.com/report/security-demo_page-0044.jpg',
]);

// 投资分析报告
const analyzeList = ref([]);
function getAnalyzeList() {
	for (let i = 1; i <= 62; i++) {
		const formattedNumber = String(i).padStart(4, '0'); // 格式化为 0001 到 0062
		const link = `https://static.biaobiaozhun.com/report/invest_analyze/demo/invest_analyze_page-${formattedNumber}.jpg`;
		analyzeList.value.push(link);
	}
}
function back(){
    // 返回上一级
    router.go(-1);
}
getAnalyzeList();
</script>
<style lang="less" scoped>
.previewReportContainer {
    .header_wrap {
        height: 32px;
        background-color: #fff;
        display: flex;
        align-items: center;
        padding: 0 12px;
        font-size: 14px;
    }
    .preview {
	img {
		width: 100%;
		object-fit: cover;
	}
}
}

</style>
