<template>
	<el-dialog v-model="visible" @close="handleClose" :close-on-click-modal="false" width="600" title="支付结果">
		<el-result icon="success" title="购买成功" :sub-title="subTitle">
			<template #sub-title>
				<div style="width: 500px">{{ subTitle }}</div>
				<div>如有问题请联系400-677-8895</div>
			</template>
			<template #extra>
				<div class="result_wrap">
					<div class="result_item">
						<div class="label">购买商品</div>
						<div class="value" v-if="payData.type == 'single'">单项报告</div>
						<div class="value" v-if="payData.type == 'analyze'">投资分析报告</div>
						<div class="value" v-if="payData.type == 'package'">报告套餐</div>
					</div>
					<div class="result_item" v-if="payData.type == 'single'">
						<div class="label">报告类型</div>
						<div class="value">{{ payData.name }}</div>
					</div>
					<div class="result_item" v-if="payData.type == 'analyze'">
						<div class="label">报告周期</div>
						<div class="value">最新季度报告</div>
					</div>
					<div class="result_item" v-if="payData.type == 'package'">
						<div class="label">套餐详情</div>
						<div class="value">包含已选择资产的【1份当前最新季度报告、3份后续三个季度的报告和8份单项报告】</div>
					</div>
					<div class="result_item">
						<div class="label">已选资产</div>
						<div class="value">{{ payData.buildingName }}</div>
					</div>
					<div class="result_item">
						<div class="label">实际支付</div>
						<div class="value">￥{{ payData.price }}</div>
					</div>
					<div class="result_item">
						<div class="label">支付方式</div>
						<div class="value">{{ payData.payType }}</div>
					</div>
					<div class="back_wrap" style="display: flex; justify-content: center; margin-top: 16px">
						<el-button type="primary" @click="handleClose">返回商报auto首页({{ countdown }}s)</el-button>
					</div>
				</div>
			</template>
		</el-result>
	</el-dialog>
</template>

<script setup>
const props = defineProps({
	// 控制弹窗
	modelValue: {
		type: Boolean,
		required: true,
	},
	payData: {
		type: Object,
		default: () => {},
	},
});

const emit = defineEmits(['update:modelValue']);

const visible = ref(false);
const subTitle = ref('');
const countdown = ref(5);
const setIntervalVal = ref(null);

watch(
	() => props.modelValue,
	(newVal) => {
		visible.value = newVal;
		if (visible.value) {
			clearInterval(setIntervalVal.value);
			setIntervalVal.value = setInterval(() => {
				if (countdown.value > 0 && countdown.value <= 5) {
					countdown.value--;
				} else {
					clearInterval(setIntervalVal.value);
					handleClose();
				}
			}, 1000);
		}
	}
);
watch(
	() => props.payData,
	(newVal) => {
		if (['single', 'analyze'].includes(newVal.type)) {
			subTitle.value = '报告会在30分钟左右发送至您预留的接收邮箱中';
		} else {
			subTitle.value =
				'当前最新季度报告及单项报告会在30分钟左右发送至您预留的接收邮箱中,后续每过一个季度我们会向您发送一份最新的季度报告，请注意查收';
		}
	}
);

const handleClose = () => {
	visible.value = false;
	emit('update:modelValue', false);
};
</script>

<style scoped lang="less">
:deep(.el-result__extra) {
	width: 100%;
}
.result_wrap {
	width: 100%;
	display: flex;
	flex-direction: column;
	gap: 14px;
	.result_item {
		display: flex;
		justify-content: space-between;
		gap: 96px;
		.label {
			min-width: 60px;
		}
		.value {
			text-align: right;
		}
	}
}
</style>
