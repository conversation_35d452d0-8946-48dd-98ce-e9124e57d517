<template>
	<div class="content_box">
		<!-- <div class="title">顾问</div> -->
		<div class="container_box">
			<div class="body_main">
				<div class="btn_box" @click="searchShow = searchShow ? false : true">
					<div class="title1">筛选条件</div>
					<div style="margin-top: 2px">{{ searchShow ? '收起' : '展开' }}</div>
					<span :style="{ transform: searchShow ? 'rotate(-90deg)' : 'rotate(90deg)' }"
						><el-icon> <DArrowRight /> </el-icon
					></span>
				</div>
				<div class="search_box" :style="{ height: searchShow ? '42px' : '0' }">
					<div class="box_1">
						<div class="label">城市</div>
						<el-cascader placeholder="请选择城市" clearable :options="$vuexStore.state.cityArray" @change="handleChange" :props="{ value: 'label' }">
						</el-cascader>
					</div>
					<div class="box_1">
						<div class="label">顾问类型</div>
						<el-select v-model="province" placeholder="全部资产评级" clearable>
							<el-option v-for="(item, value) in counselorList" :key="value" :label="item.label" :value="item.value" />
						</el-select>
					</div>
					<div class="box_1">
						<div class="label">资产类型</div>
						<el-select v-model="buildingTypesValue" placeholder="全部资产" clearable>
							<el-option v-for="item in buildingTypes" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</div>
					<div class="box_1">
						<div class="label">关键词</div>
						<el-input v-model="searchValue" clearable placeholder="请输入关键字"></el-input>
					</div>
					<div class="box_2">
						<el-button type="primary" @click="onSubmit()">查询</el-button>
					</div>
				</div>
				<div class="tag_box">顾问列表</div>
				<div class="card_list">
					<div class="card_body" v-for="(item, index) in data" :key="index" @click="goDetail(item)">
						<div class="avatar">
							<img src="@/assets/Default.png" alt="" v-if="item.avatar == '' || item.avatar == '暂无'" />
							<img :src="`${proxyAddress}${item.avatar}`" alt="" v-else />
						</div>
						<div class="tips_box">
							<div class="nickName">{{ item.realname }}</div>
							<div class="company_name">{{ item.company }}</div>
						</div>
					</div>
				</div>
				<el-pagination
					:current-page="currentPage"
					:page-size="pageSize"
					@current-change="handlePageChange"
					@size-change="pageSizeChange"
					:page-sizes="[20, 40, 80, 160]"
					:total="total"
					layout="->,prev, pager, next, jumper, sizes,total"
				/>
				<div class="total_box">
					共<span>{{ total }}</span
					>项
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import http from '@/utils/http';
//  import { provinceAndCityData } from 'element-china-area-data';
import { getActorList } from '@/api/participant.js';
import { getConsultantList, getDictList } from '@/api/syt.js';

import { useRouter } from 'vue-router';
// import { de } from 'element-plus/es/locale';
const rate = ['S', 'A+', 'A', 'B+', 'B', 'C'];
const proxyAddress = ref('https://static.biaobiaozhun.com/');
const buildingTypes = ref([]);
const counselorList = ref([]);
const counselor = ref('');
const buildingTypesValue = ref('');
const data = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);

const handlePageChange = (page) => {
	console.log(page, 76);
	currentPage.value = page;
	initData();
};
const searchValue = ref('');

const essential = ref('');
const route = useRouter();
const total = ref();

const searchShow = ref(true);

onMounted(() => {
	initData();
});

const province = ref('');
const provinceList = ref([]);
const city = ref('');
const county = ref('');
const activeed = ref(0);
const onChangeCity = (val, index) => {
	provinceList.value = val.children;
	city.value = val.label;
	console.log(val, 'val23567', index);
	activeed.value = index;
};
const handleChange = (val) => {
	console.log(val, 'ss');
	if (val) {
		city.value = val[0];
		county.value = val[1];
	} else {
		city.value = '';
		county.value = '';
	}
};

const activeIndex = ref('');
const onChangeProperty = (val, index) => {
	console.log(val, 'val23567', index);
	activeIndex.value = val;
	buildingTypesValue.value = val;
	console.log(buildingTypesValue.value, 'buildingTypesValue1');
};
onChangeProperty(buildingTypes.value[0], 0);
// onChangeType(counselorList[0],0)
const pageSizeChange = (val) => {
	console.log(val, 'val');
	pageSize.value = val;
	initData(val);
};
const onSubmit = () => {
	const queryParams = {
		city: city.value,
		district: county.value,
		userType: province.value,
		buildingType: buildingTypesValue.value,
		keyword: searchValue.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
	};
	console.log(queryParams, 'queryParams1243');
	initData();
};
const initData = async (val) => {
	const queryParams = {
		city: city.value,
		district: county.value,
		keyword: searchValue.value,
		buildingType: buildingTypesValue.value,
		currentPage: currentPage.value,
		pageSize: pageSize.value,
		userType: province.value,
	};
	await getConsultantList(queryParams)
		.then((res) => {
			console.log(res, 'ss');
			data.value = res.data.rows;
			total.value = res.data.total;
		})
		.catch((err) => {
			console.log(err, 'err');
		});
};
const goDetail = (item) => {
	console.log(item, 'val1234');
	const borkerName = item.realname;
	const companyName = item.company;
	const avatar = item.avatar;
	const userId = item.userId;
	const phone = item.phone;
	console.log(borkerName, 'borkerName');
	// route.push('/brokerDetail?borkerName='+borkerName+'&companyName='+companyName)
	route.push({
		path: '/shangYutong/participator/brokerDetail1',
		query: {
			borkerName: borkerName,
			companyName: companyName,
			phone: phone,
			avatar: avatar,
			userId: userId,
		},
	});
};
const getDict = async () => {
	await getDictList({ code: 'building_type' })
		.then((res) => {
			buildingTypes.value = res.data;
			console.log(res, 'shuju111');
		})
		.catch((err) => {
			console.log(err);
		});
	await getDictList({ code: 'sm_user_type' })
		.then((res) => {
			counselorList.value = res.data;
			console.log(res, 'shuju111');
		})
		.catch((err) => {
			console.log(err);
		});
};
getDict();
</script>

<style lang="less" scoped>
.content_box {
	width: 100%;
	height: 100%;
	background-color: rgba(245, 245, 245, 1);

	.title {
		width: 100%;
		height: 56px;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.container_box {
		width: 100%;
		height: 100%;
		padding-top: 10px;
		box-sizing: border-box;
		.body_main {
			width: 100%;
			min-height: 868px;
			background-color: rgba(255, 255, 255, 1);
			// margin-top: 15px;
			padding: 15px 0;
			box-sizing: border-box;
			border-radius: 6px;
			position: relative;
			.btn_box {
				width: 100%;
				height: 22px;
				padding: 0 15px;
				box-sizing: border-box;
				margin-bottom: 10px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				cursor: pointer;
				font-size: 12px;
				color: rgba(134, 144, 156, 1);
				.title1 {
					width: auto;
					font-size: 14px;
					color: rgba(60, 60, 60, 1);
					margin-right: 10px;
				}

				span {
					display: inline-block;
					transform: rotate(-90deg);
					margin-left: 5px;
				}
			}
			.search_box {
				width: 100%;
				overflow: hidden;
				padding: 0 15px;
				box-sizing: border-box;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				flex-wrap: wrap;

				.box_1 {
					width: 230px;
					height: 32px;
					margin: 10px 5px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					border: 1px solid rgba(231, 231, 231, 1);
					box-sizing: border-box;

					::v-deep .el-cascader .el-input.is-focus .el-input__wrapper {
						box-shadow: 0;
					}

					.label {
						width: 50%;
						height: 100%;
						font-size: 14px;
						color: rgba(134, 144, 156, 1);
						background-color: rgba(245, 246, 247, 1);
						display: flex;
						justify-content: center;
						align-items: center;
					}
				}
				.box_2 {
					// width: 230px;
					height: 32px;
					margin: 10px 5px;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					border-radius: 4px;
					box-sizing: border-box;
				}
			}
			.table_2 {
				width: 100%;
				height: 308px;
				&::v-deep .el-table--fit {
					border-radius: 8px;
				}

				&::v-deep .el-table th {
					background-color: rgba(245, 245, 245, 1);
				}
			}
			.tag_box {
				width: 100%;
				height: 16px;
				margin-top: 20px;
				font-size: 14px;
				font-weight: bold;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				&::before {
					content: '';
					width: 4px;
					height: 16px;
					background-color: rgba(24, 104, 241, 1);
					margin-right: 10px;
				}
			}
			.card_list {
				width: 100%;
				height: 471px;
				margin-top: 15px;
				display: flex;
				justify-content: flex-start;
				align-content: flex-start;
				flex-wrap: wrap;
				overflow: scroll;
				.card_body {
					width: 24%;
					height: 82px;
					margin: 0.5%;
					display: flex;
					justify-content: flex-start;
					align-items: center;
					background-color: rgba(245, 246, 247, 1);
					border-radius: 6px;
					padding: 0 15px;
					cursor: pointer;
					box-sizing: border-box;
					.avatar {
						width: 48px;
						height: 48px;
						border-radius: 8px;
						overflow: hidden;
						margin-right: 10px;
						img {
							width: 100%;
							height: 100%;
						}
					}
					.tips_box {
						width: 200px;
						height: 50px;
						display: flex;
						justify-content: center;
						align-items: flex-start;
						flex-direction: column;
						.nickName {
							font-weight: bold;
							margin-bottom: 5px;
						}
						.company_name {
							font-size: 12px;
						}
					}
				}
			}
			.total_box {
				width: auto;
				height: 24px;
				position: absolute;
				left: 15px;
				bottom: 205px;
				span {
					color: rgba(3, 93, 255, 1);
				}
			}
		}
	}
}

.el-card__body {
	display: flex !important;
}

.search {
	width: 100%;
	height: 285px;
	background: rgb(250, 250, 250);
	padding: 30px 0;
	position: relative;

	button {
		width: 85px;
		height: 36px;
		border-radius: 2px;
		background: rgb(56, 96, 154);
		color: rgb(255, 255, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 21px;
		letter-spacing: 0px;
		// text-align: center;
		position: absolute;
		right: 20px;
		border: none;
	}

	.active {
		box-sizing: border-box;
		border: 1px solid rgb(64, 158, 255);
		border-radius: 2px;

		background: rgb(241, 248, 255);
		color: rgb(64, 158, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 23px;
		letter-spacing: 0px;
		text-align: left;
	}

	.area_box {
		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}

		.city {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin: 15px 0;
			margin-bottom: 15px;

			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}

		.county {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin: 15px 0;

			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}

	.property_box {
		margin-top: 15px;

		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}

		.property {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;

			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}

	.type_box {
		margin-top: 15px;

		.title {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 700;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}

		.type {
			color: rgb(0, 0, 0);
			font-family: 微软雅黑;
			font-size: 16px;
			font-weight: 400;
			line-height: 23px;
			letter-spacing: 0px;
			text-align: left;

			span {
				margin: 0 30px;
				padding: 3px 12px;
			}
		}
	}
}

.mian {
	width: 100%;
	height: 30%;
	box-sizing: border-box;
	text-align: center;

	.content {
		// padding: 20px;
		display: flex;
		// justify-content: space-around;
		align-items: center;
		flex-wrap: wrap;

		.box-card {
			width: 30%;
			height: 150px;
			margin: 15px;
			border-radius: 25px;
			display: flex;
			align-content: center;
			box-sizing: border-box;
			border: 1px solid rgb(205, 205, 205);
			border-radius: 3px;

			background: rgb(250, 250, 250);
			padding: 20px 0;
			position: relative;

			img {
				// margin-top: 20px;
				margin-left: 20px;
				width: 100px;
				height: 100px;
				border-radius: 50%;
			}

			.text_box {
				width: 130px;
				height: 107px;
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				// align-items: flex-start;

				text-align: left;
				margin-left: 16px;

				// margin-top: 15px;
				p {
					width: 206px;
					padding: 0;
					margin: 0;
					color: rgb(0, 0, 0);
					font-family: 微软雅黑;
					font-size: 16px;
					font-weight: 400;
					line-height: 22px;
					letter-spacing: 0px;
					text-align: left;
				}

				.title {
					width: 206px;
					color: rgb(0, 0, 0);
					font-family: 微软雅黑;
					font-size: 24px;
					font-weight: 700;
					line-height: 35px;
					letter-spacing: 0px;
					text-align: left;
				}
			}

			button {
				width: 100px;
				height: 36px;
				border-radius: 2px;
				background: rgb(64, 158, 255);
				color: rgb(255, 255, 255);
				font-family: 微软雅黑;
				font-size: 16px;
				font-weight: 400;
				line-height: 21px;
				letter-spacing: 0px;
				text-align: center;
				border: none;
				position: absolute;
				right: 20px;
				bottom: 20px;
			}
		}
	}
}

.el-row {
	margin-bottom: 20px;
}

.el-row:last-child {
	margin-bottom: 0;
}

.el-col {
	border-radius: 4px;
}

.grid-content {
	border-radius: 4px;
	min-height: 36px;
}

.text {
	font-size: 14px;
}

.item {
	padding: 18px 0;
}

.box-card {
	width: 480px;
}

.el-pagination {
	margin-right: 20px;
}

.input_box {
	display: flex;
	align-items: center;
	width: 300px !important;
	margin-right: 20px;

	.textInput {
		width: 228px;
		height: 36px;
	}

	.searchBtn {
		width: 67px;
		height: 36px;
		border-radius: 2px;
		border: none;
		background: rgb(64, 158, 255);
		margin-left: 10px;
		color: rgb(255, 255, 255);
		font-family: 微软雅黑;
		font-size: 16px;
		font-weight: 400;
		line-height: 21px;
		letter-spacing: 0px;
		// text-align: left;
	}
}
</style>
