<template>
  <div class="participator_box">
    <!-- 父盒子 -->
    <div class="tab">
    
      <!-- 每个tab -->
      <div
        v-for="(item, idx) in arr"
        :key="item.id"
        class="tabs"
        :class="{ tbasBorder: activeName === idx }"
        @mouseleave="slideOut(idx)"
        @click="handleClick(idx)"
      >
        <div>{{ item.title }}</div>
      </div>
      <!-- 滑动块 -->
      <div class="slide" :style="move"></div>
    </div>
    <div v-if="activeName === 0"><calculate></calculate></div>
    <div v-if="activeName === 1">
      <comparison></comparison>
    </div>
    
    <div v-if="activeName === 2"><population></population></div>
    <div v-if="activeName === 3"><lessee></lessee></div>
    <div v-if="activeName === 4"><floor></floor></div>
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  import calculate from './calculate.vue'
  import comparison from './comparison.vue'
  import floor from './floor.vue'
  // import sixring from './sixring.vue'
  import population from './population.vue'
  import lessee from './lessee.vue'
    const searchValue = ref('')
    console.log(searchValue.value,3232);
    const activeName = ref(0);
    const MoveCurrent = ref(0);
    const move = ref('left:0px');
    const arr = ref([
      { id: 1, title: '交易计算' },
      { id: 2, title: '价值对比' },
      { id: 3, title: '人口对比' },
      { id: 4, title: '租户对比' },
      { id: 5, title: '户型图' },
    ]);

    const handleClick = (idx) => {
      activeName.value = idx;
      localStorage.setItem('activeIndex', activeName.value);
    };

    // 每次进入页面读取
    const getactive = () => {
      const active = localStorage.getItem('activeIndex');
      if (active) {
        activeName.value = Number(active);
      }
    }
    getactive();

    const slideOut = (idx) => {
      move.value = `left:${100 * idx}px;height:0px;`;
    };
    const onSearch = () => {
      console.log(searchValue.value, 111231);
    };
</script>
<style lang="less" scoped>
  /* tab切换 */
  .tab {
    position: relative;
    cursor: pointer;
    display: flex;
    /* 防止滑动时 产生保留上次效果 浏览器问题*/
    overflow: hidden;
    border-bottom: 1px solid rgb(56, 96, 154);
    padding-left: 20px;
  }
  .tabs {
    // padding: 10px 10px; 
    width: 117px;
    height: 51px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgb(0, 0, 0);
    font-family: 微软雅黑;
    font-size: 18px;
    font-weight: 400;
    line-height: 35px;
    letter-spacing: 0px;
    text-align: left;
    margin-left: 15px;
  }
  .tbasBorder {
    background: rgb(56, 96, 154);
    color: rgb(255, 255, 255);
    font-family: 微软雅黑;
    font-size: 20px;
    font-weight: 700;
    line-height: 35px;
    letter-spacing: 0px;
    text-align: left;
  }
  .slide {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100px;
    height: 0px;
    background: #023d99;
    transition: all .3s;
  }
  .input_box {
    position: absolute;
    right: 0;
    top: 0;
    margin-right: 20px;
    .textInput{
      width: 228px;
      height: 36px;
    }
    .searchBtn {
      width: 67px;
      height: 36px; 
      border-radius: 2px;
      border: none;
      background: rgb(64, 158, 255);
      margin-left: 10px;
      color: rgb(255, 255, 255);
      font-family: 微软雅黑;
      font-size: 16px;
      font-weight: 400;
      line-height: 21px;
      letter-spacing: 0px;
      // text-align: left;
    }
  }
</style>

<style lang="less" scoped>
.participator_box {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  background-color: #fff;
  padding: 43px 0;
}
</style>
