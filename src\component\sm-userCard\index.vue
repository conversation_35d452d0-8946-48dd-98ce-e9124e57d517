<template>
	<div class="container_user_card">
		<div
			class="card_content"
			:class="hoverStatus ? 'card_Hover' : ''"
			v-for="(item, index) in list.dataListCard"
			:key="index"
			:style="{ 'margin-right': props.styleMarginRight, 'margin-bottom': props.styleMarginBottom, width: props.width }"
		>
			<div class="card_Img">
				<img src="../../assets/usersCard.png" alt="" />
			</div>
			<div class="card_RightContent">
				<div class="topContent">
					<div>{{ item.userName }}</div>
					<span class="hovers" v-if="hoverStatus" @click="handleRemove(index)">移除</span>
				</div>
				<div class="bottomContent">{{ item.phone }}</div>
			</div>
		</div>

		<div class="addCard" @click="handleAddCrad" v-show="!team || list.dataListCard.length < team" :style="{ width: props.width }">
			<el-icon><Plus /></el-icon>
			<div class="addMan">添加使用人</div>
		</div>
	</div>
</template>
<script setup>
import { reactive } from 'vue';
const emit = defineEmits(['handleAddCrad']);
const props = defineProps({
	//卡片右侧边距 默认10
	styleMarginRight: {
		type: String,
		default: '10px',
	},
	//卡片下面边距 默认10
	styleMarginBottom: {
		type: String,
		default: '10px',
	},
	// 移除展示状态
	hoverStatus: {
		type: Boolean,
		default: true,
	},
	width: {
		type: String,
		default: '208px',
	},
	team: {
		type: Number,
		default: 0,
	},
});
let list = reactive({ dataListCard: [] });
/**
 * @function handleAddCrad 使用人
 */
function handleListCard(data) {
	list.dataListCard = data;
}

/**
 * @function handleAddCrad 添加使用人
 */
function handleAddCrad() {
	emit('handleAddCrad');
}
/**
 * @function handleRemove 移除使用人
 */
function handleRemove(index) {
	emit('handleRemove', index);
}

/**
 * @function handleSureRemoves 确认移除使用人
 */
function handleSureRemoves(index) {
	list.dataListCard.splice(index, 1);
}

defineExpose({ handleListCard, handleSureRemoves, list });
</script>

<style lang="scss" scoped>
@import url('./css.css');
</style>
