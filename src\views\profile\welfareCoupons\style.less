.rights-details-container {
  background: #FFFFFF;
  border-radius: 6px;
  height: 100%;
  overflow: hidden;
  .details-box {
    display: flex;
    height: 82px;
    width: 363px;
    margin: 20px 8px 32px 110px !important;
    position: relative;

    .operate {
      width: 251px;
      height: 82px;
      background-color: rgba(255, 255, 255, 1);
      border-radius: 6px;
      border: 1px solid rgba(201, 205, 212, 1);
      border-right: 0px solid rgba(201, 205, 212, 1);
      display: flex;
      // align-items: center;
      // flex-direction: column;
      justify-content: space-between;
      // border-color: rgba(24, 104, 241, 1);


      .unused {
        background-color: rgba(201, 205, 212, 1);

      }


      .xse {
        background-color: rgba(24, 104, 241, 1);
      }

      .invalid {
        color: rgba(201, 205, 212, 1);
        background-color: rgba(245, 246, 247, 1);
      }

      // .button-data :hover{
      //   background:rgba(245, 246, 247) ;
      // }
      .button-data {
        width: 232px;
        height: 74px;
        padding: 4px 16px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: start;
        justify-content: center;

        .button_top_contents>:nth-child(1) {
          color: rgba(134, 144, 156, 1);
        }

        .invalid_content {
          color: rgba(29, 33, 41, 1);
          font-size: 12px;
          height: 20px;
          line-height: 20px;
        }

        .button_content_twos {
          color: rgba(134, 144, 156, 1) !important;
        }

        .button_top_content {
          // margin-top: 2px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: end;
          margin-bottom: 4px;
        }

        .button_top_content>:nth-child(1) {
          font-size: 16px;
          font-weight: 700;
        }

        .button_top_content>:nth-child(2) {
          width: 36px;
          height: 20px;
          border-radius: 10px 2px;
          line-height: 20px;
          font-weight: 400;
          font-size: 12px;
          text-align: center;

        }

        .button_content_two {
          height: 22px;
          font-weight: 700;
          line-height: 22px;
          font-size: 14px;
          color: rgba(78, 89, 105, 1);
          // margin-bottom: 8px;
        }

        .button_contents {
          height: 20px;
          font-weight: 400;
          color: rgba(134, 144, 156, 1);
          line-height: 20px;
          font-size: 12px;
        }

        .button_contentts {
          color: rgba(24, 104, 241, 1);
        }

        // div {
        //   background-color: rgba(0, 121, 254, 0.24705882352941178);
        //   margin-bottom: 10px;
        //   color: #0079FE;
        //   width: 100px;
        //   height: 30px;
        //   border-radius: 18px;
        //   font-size: 14px;
        //   text-align: center;
        //   line-height: 30px;
        // }

      }
    }

    .center_line {
      position: absolute;
      top: 9px;
      right: 110px;
      width: 2px;
      height: 64px;
      background: linear-gradient(180deg, rgba(231, 231, 231, 0) 0%, #E7E7E7 50%, rgba(231, 231, 231, 0) 100%);
    }

    .right-content {
      width: 112px;
      height: 66px;
      border-radius: 6px;
      border: 1px solid rgba(201, 205, 212, 1);
      border-left: 0px solid rgba(201, 205, 212, 1);
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      // border-color: rgba(24, 104, 241, 1);
    

      .status_name {   
        font-size: 14px;
        font-weight: 700;}
    }
  }

  .details-box.status_1 {
    .status {
      background: rgba(24, 104, 241, 1);
      padding: 0 10px;
    }

    .operate {
      border-color: rgba(24, 104, 241, 1);
    }

    .occupied {
      color: #FFFFFF;
      background: rgba(24, 104, 241, 1);
    }

    .right-content {
      border-color: rgba(24, 104, 241, 1);
    }

  }

  .details-box.status_2 {
    .status {
      padding: 0 10px;
      background: rgba(201, 205, 212, 1);
    }

    .operate {
      border-color: rgba(201, 205, 212, 1);
    }

    .occupied {
      color: #FFFFFF;
      background: rgba(24, 104, 241, 1);
    }

    .right-content {
      border-color: rgba(201, 205, 212, 1);
    }

  }

  .details-box.status_3 {
    color: #86909C!important;
    .button_content_two{
      color: #86909C!important;
    }
    .operate {
      background-color: #f5f6f7;
      border-color: 1px solid #c9cdd4;
    }

    .occupied {
      color: #FFFFFF;
      background: rgba(24, 104, 241, 1);
    }

    .right-content {
      border-color: #c9cdd4;
      background-color: #f5f6f7;

    }
  }

  .details-box.newUser {
    .status {
      padding: 0 10px;
      color: #fff;
      background:linear-gradient(90deg, #FF504C 0%, #FE8042 100%);
      overflow: auto;
      border-radius: 6px 0 0 6px;
    }

    .operate {
      border-color:#FF514C;

    .occupied {
      color: #fff;
      background:linear-gradient(90deg, #FF504C 0%, #FE8042 100%);
    }
    }

    .right-content {
      color: #FF514C;
      border-color:#FF514C;
    }

  }

  .details-box.type_1 {
    .status {
      padding: 0 10px;
      color: #f9c29b;
      background: #1d2129;
      overflow: auto;
      border-radius: 6px 0 0 6px;
    }

    .operate {
      border-color: #1D2129;

      .button-data {
        .occupied {
          color: rgba(249, 194, 155, 1);
          background-color: rgba(29, 33, 41, 1);
        }
      }
    }

    .right-content {
      border-color: #1D2129;
    }
  }

  .details-box.type_2 {
    .status {
      background: rgba(24, 104, 241, 1);
      padding: 0 10px;
    }

    .operate {
      border-color: rgba(24, 104, 241, 1);
    }

    .button-data {
      .occupied {
        color: #FFFFFF;
        background: rgba(24, 104, 241, 1);
      }
    }

    .right-content {
      color: #1868F1;
      border-color: rgba(24, 104, 241, 1);
    }
  }

  .t-box {
    padding: 0 24px;
    .Rights-box {
      // width: calc(100% / 3);
      display: flex;
      height: 54px;
      line-height: 54px;
      .name {
        font-size: 14px;
        font-weight: 400;
        width: 80px;
        margin-right: 16px;
      }

      .value {
        color: #4E5969;
        font-size: 14px;
        font-weight: 400;

      }
    }
  }
  .title-box {
    display: flex;
    align-items: center;
    padding: 0 24px;
    height: 40px;

    .name {
      font-size: 14px;
      font-weight: 400;
      width: 110px;
    }

    .value {
      font-size: 12px;
      color: #86909C;

    }
  }

}

.container_top {
  height: 70px;
  display: flex;
  align-items: center;
  padding-left: 16px;
  border-bottom: 1px solid rgba(231, 231, 231, 1);

  .container_top_elion {
    color: rgba(24, 104, 241, 1);
    cursor: pointer;
  }

  .container_top_title {
    margin: 0 12px 0 8px;
    color: rgba(134, 144, 156, 1);
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
  }

  .container_top_line {
    height: 16px;
    width: 1px;
    margin-top: 2px;
    background: rgba(231, 231, 231, 1);
  }
  .container_top_prompt{
    margin-left: 12px;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #86909C;
  }
}

.contaner_content_ {
  height: 130px;
  width: 100%;
  display: flex;
  align-items: center;
  padding-left: 128px;
}

.details-box {
  &:hover .operate{
      background: rgba(245, 246, 247)!important;
  }
  &:hover .right-content{
    background: rgba(245, 246, 247)!important;
  }
}