import axios from 'axios';
import { ElMessage } from 'element-plus';

axios.defaults.headers['Content-Type'] = 'application/json;charset=UTF-8';
axios.defaults.headers['Cache-Control'] = 'no-cache';
axios.defaults.headers['Pragma'] = 'no-cache';
axios.defaults.headers['X-Access-Token'] = '123';
let host = '';
if (window.location.host.indexOf('localhost') > -1 || window.location.host.indexOf('*************') > -1) {
	host = 'https://biaobiaozhun.com'; //本地
} else if (window.location.host.indexOf('biaobiaozhun.com') > -1) {
	host = 'https://biaobiaozhun.com'; //线上
}
const http = axios.create({
	// baseURL: 'http://**************:8080',
	// baseURL: 'http://*************:8088',
	// baseURL: host,
	timeout: 10000,
	withCredentials: true,
	crossDomain: true,
});
// 请求拦截器
http.interceptors.request.use(
	(config) => {
		// 判断是否存在token，如果存在的话，则每个http header都加上token
		if (localStorage.getItem('token')) {
			config.headers.Authorization = localStorage.getItem('token');
		}

		return config;
	},
	(err) => {
		return Promise.reject(err);
	}
);

// 响应拦截器
http.interceptors.response.use(
	(response) => {
		debugger;
		// if (response.data.code == 401) {
		//   // 跳转登录页面，并将
		//   router.replace({
		//     path: '/login',
		//     query: {
		//       redirect: router.currentRoute.value.fullPath
		//     }
		//   });
		// }
		debugger;
		return response.data;
	},
	(error) => {
		ElMessage.error('网络异常');
		return Promise.reject(error);
	}
);
export default http;
