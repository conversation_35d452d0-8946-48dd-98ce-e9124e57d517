<template>
	<el-form :model="form" label-width="120px">
		<el-form-item label="关键词搜索">
			<!-- <span>关键词搜索</span> -->
			<el-input v-model="form.valuee" class="w-50 m-2" placeholder="搜索产品、证券、机构" style="width: 300px" />
		</el-form-item>

		<el-form-item label="发行日期">
			<!-- <span>发行日期</span> -->
			<el-date-picker
				v-model="form.data"
				type="daterange"
				range-separator="To"
				start-placeholder="Start date"
				end-placeholder="End date"
				style="width: 300px; flex: none"
			/>
		</el-form-item>

		<el-form-item label="当前状态">
			<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup3" size="large">
				<el-checkbox-button v-for="state in stateList" :key="state" :label="state">
					{{ state }}
				</el-checkbox-button>
			</el-checkbox-group>
		</el-form-item>

		<el-form-item label="基础资产">
			<!-- <span>基础资产</span> -->
			<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup1" size="large">
				<el-checkbox-button v-for="Basicassets in assets" :key="Basicassets" :label="Basicassets">
					{{ Basicassets }}
				</el-checkbox-button>
			</el-checkbox-group>
		</el-form-item>

		<el-form-item label="资产细分">
			<!-- <span>基础资产</span> -->
			<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup2" size="large" border="false" @change="oncheckzcxf">
				<el-checkbox-button v-for="subdivide in assetssub" :key="subdivide" :label="subdivide">
					{{ subdivide }}
				</el-checkbox-button>
			</el-checkbox-group>
		</el-form-item>
		<el-form-item label="监管机构">
			<el-checkbox-group fill="#126bae" v-model="form.checkboxGroup5" size="large" border="false">
				<el-checkbox-button v-for="supervise in superviseList" :key="supervise" :label="supervise">
					{{ supervise }}
				</el-checkbox-button>
			</el-checkbox-group>
		</el-form-item>
		<!-- 选择器 -->
		<el-form-item label="发起/原始权益人">
			<el-select v-model="value1" class="m-2" placeholder="Select" size="large">
				<el-option v-for="item in options1" :key="item.value" :label="item.key" :value="item.value" />
			</el-select>
		</el-form-item>
		<el-form-item label="发行/管理人">
			<el-select v-model="value2" class="m-2" placeholder="Select" size="large">
				<el-option v-for="item in options2" :key="item.value" :label="item.key" :value="item.value" />
			</el-select>
		</el-form-item>
		<el-form-item label="参与承销商">
			<el-select v-model="value3" class="m-2" placeholder="Select" size="large">
				<el-option v-for="item in options3" :key="item.value" :label="item.value" :value="item.value" />
			</el-select>
		</el-form-item>
		<!-- 确认取消按钮 -->
		<el-form-item>
			<el-button type="primary" @click="onSubmit">确认</el-button>
			<el-button @click="oncancel">取消</el-button>
		</el-form-item>
	</el-form>
	<!-- 表格 -->
	<el-table :data="AbsList" style="width: 100%" :header-cell-style="{ background: '#3170a7', color: '#ffffff ' }">
		<el-table-column prop="shortName" label="产品简称" />
		<el-table-column prop="fullName" label="产品名称" />
		<el-table-column prop="fullName" label="发起/原始收益人" />
		<el-table-column prop="totalOffering" label="发行规模(亿)" />
		<el-table-column prop="issueType" label="方式" />
		<el-table-column prop="regulator" label="监管机构" />
		<el-table-column prop="isCyclePurchase" label="循环池" />
		<el-table-column prop="exchange" label="交易场所" />
		<el-table-column prop="marketType" label="市场分类" />
		<el-table-column prop="dealType" label="产品分类" />
		<el-table-column prop="assetSubCategory" label="产品细分" />
		<el-table-column prop="currentStatus" label="状态" />
		<el-table-column prop="year" label="年份" />
		<el-table-column prop="closingDate" label="起息日" />
		<el-table-column prop="legalMaturityDate" label="法定到期日" />
		<el-table-column prop="lederUnderWriter" label="主承销商" />
		<el-table-column prop="issuer" label="发行/管理人" />
		<el-table-column prop="ratingAgency" label="评级机构" />
	</el-table>
	<el-pagination
		@current-change="handleCurrentChange"
		current-page="currentPage"
		layout="prev, pager, next,total, jumper"
		:page-size="10"
		class="mt-4"
		:total="total"
	/>
</template>

<script setup>
import { reactive, ref } from 'vue';
import http from '@/utils/http';
import { getAbsListApi, getSelector } from '@/api/finance.js';
const activeName = ref('first');

const form = reactive({
	valuee: '',
	data: '',
	checkboxGroup0: [],
	checkboxGroup1: [],
	checkboxGroup2: [],
	checkboxGroup3: [],
	checkboxGroup4: [],
	checkboxGroup5: [],
});
const currentPage = ref(1);
const total = ref(0);
const handleCurrentChange = (val) => {
	currentPage.value = val;
	getAbsList();
};
const options1 = ref([]);
const options2 = ref([]);
const options3 = ref([]);
const value1 = ref('');
const value2 = ref('');
const value3 = ref('');
const assets = ['类Reits', 'CMBS/CMBN', '保障房'];
const assetssub = ['全部', '办公物业', '零售物业', '混合', '酒店', '物流仓储', '混合类', '公寓', '基础设施'];
const onSubmit = () => {
	console.log('submit!');
	getAbsList();
};
const yearList = ['2024', '2023', '2022', '2021', '2020', '2019', '2018', '2017', '2016', '2015', '2014'];
const stateList = ['全部', '存续期', '已清算', '停售', '发行期'];
const labelList = ['不限', '房地产', '评级下调', '展期', '绿色', '汽车', '消费金融', '区块链', '知识产权', '公募REITs'];
const superviseList = ['全部', '证监会', '银保监', '交易商协会', '其他监管机构'];
//   表格
const tableData = [
	{
		date: '2016-05-03',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-02',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-04',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
	{
		date: '2016-05-01',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
	},
];
// 点击取消
const oncancel = () => {
	console.log('cancel!');
	// 清空表单
	form.valuee = '';
	form.data = '';
	form.checkboxGroup0 = [];
	form.checkboxGroup1 = [];
	form.checkboxGroup2 = [];
	form.checkboxGroup3 = [];
	form.checkboxGroup4 = [];
	form.checkboxGroup5 = [];
};
const oncheckzcxf = () => {
	console.log(form.checkboxGroup2, 332);
};
// 发请求
const AbsList = ref([]);
const getAbsList = async () => {
	console.log('getAbsList', form, 223);
	//
	const params = {
		keyword: form.valuee,
		issueDateStart: form.data[0],
		issueDateEnd: form.data[1],
		basicAssets: form.checkboxGroup1.toString(),
		assetsDetail: form.checkboxGroup2.toString(),
		regulatoryauthority: form.checkboxGroup5.toString(),
		productStatus: form.checkboxGroup3.toString(),
		originator: value1.value, //发起/原始权益人
		issuer: value2.value, //发行/管理人
		lederUnderWriter: value3.value, //参与承销商
		pageNo: currentPage.value,
		pageSize: 10,
	};
	const res = await getAbsListApi(params)
		.then((res) => {
			// console.log(res.result.records, );
			AbsList.value = res.result.records;
			total.value = res.result.total;
			return res;
		})
		.catch((err) => {
			console.log(err, 12345);
			return err;
		});
	// console.log(res,2346);
};
getAbsList();
// 获取选择器数据
const getSelectorData = async (type) => {
	const params = {
		selectType: type,
		keyword: '',
	};
	const res = await getSelector(params)
		.then((res) => {
			// console.log(res.result, 1244 );
			if (type === 0) {
				options1.value = res.result;
			} else if (type === 1) {
				options2.value = res.result;
			} else if (type === 2) {
				options3.value = res.result;
			}

			return res;
		})
		.catch((err) => {
			// console.log(err,12345);
			return err;
		});
};
getSelectorData(0); //发起/原始权益人
getSelectorData(1); //发起/原始权益人
getSelectorData(2); //发起/原始权益人
</script>

<style scoped lang="less">
.el-input__wrapper {
	flex: none;
	flex-grow: 0 !important;
}
.el-checkbox-button {
	margin: 5px;
	// border:1px solid #;
}
.el-table__header-wrapper {
	background-color: #000 !important;
}
</style>
