<template>
    
    <div class="body_box">
        <div class="reg_back" />
        <div class="reg_box">
        <h1>Sign up</h1>
       
        <div class="form_box">
            <div class="register">
                <el-form :model="form" label-width="60px" >
                    <el-form-item label="用户名">
                        <el-input v-model="form.account" />
                    </el-form-item>
                    <el-form-item label="手机号">
                        <el-input v-model="form.mobile"/>
                    </el-form-item>
                    <el-form-item label="验证码"  style="display: flex;">
                        <el-input v-model="form.code" show-password style="width: 110px;"/>
                        <el-button type="primary" class="getcode" style="margin-left: 50px;width: 80px;">获取验证码</el-button>
                    </el-form-item>
                    <el-form-item label="密码">
                        <el-input v-model="form.password" show-password/>
                    </el-form-item>
                    <el-form-item label="确认密码">
                        <el-input v-model="form.confirm" show-password/>
                    </el-form-item>
                    <div class="btn_box">
                        <el-button color="#3567ad" @click="home()" class="homeBtn btn" >返回登录</el-button>
                         <el-button color="#3567ad" @click="reg()" class="regBtn btn">注册</el-button>
                    </div>
                </el-form>
            </div>
        </div>
        
       
    </div>
    </div>
</template>

<script setup>
import { reactive,ref,onMounted } from 'vue'
import { useRouter } from 'vue-router';
const router = useRouter()
const form = reactive({
    account:'admin',
    password:'123123',
    confirm:'123123',
    mobile:'***********',
    code:'1234'
})
const reg = () => {
    console.log(111);
}
const home = () => {
    router.push('/login')
}


</script>

<style lang="less" scoped>
h1{
    text-align:center;
    font-family: "Sofia";
    font-size: 40px;
}
.body_box {
    height: 100vh;
    min-height: 100%;
  width: 100%;
//   background-color: $bg;
  overflow: hidden;
  display: flex;
}
.reg_back {
    flex: 5;
    border-top-right-radius: 60px;
    background-image: url('@/assets/homebj.png');
    background-size: 100% 100%;
	background-repeat: no-repeat;
  }
.reg_box{
    flex: 2;
    position: relative;
    width: 820px;
    max-width: 100%;
    padding: 160px 0px 0 80px;
    overflow: hidden;
    text-align: center;
}
.el-form-item__label {
    width: 20px !important;
}
.btn {
    // margin: 0;
    width: 120px;
    height: 30px;
    margin: 0 auto;
    text-align: center;
    margin-top: 40px;
    margin-right: 20px;
}
// 切换
.exchange{
    margin-top: 10px;
    position: absolute;
    top: 60px;
    right: 10px;
}
.form_box {
    margin-top: 70px;
    // margin-bottom: 100px;
}
.register {
    width: 300px;
    height: 30px;
    margin: 0 auto;
    text-align: center;
    margin-top: 20px;
}
</style>