<template>
	<div class="main">
		<div class="top">
			<el-image style="height: 500px" :src="url + dataOne?.buildingImage" fit="cover" />
			<p class="title" style="color: #be3a48; font-weight: 700; font-size: 18px">房地产建议评估报告</p>
			<p class="tit_basic">{{ dataOne?.buildingAddress }}</p>
			<p class="tit_basic" style="font-weight: bold">{{ dataOne?.buildingName }}</p>

			<p class="gongsi">术木智能信息技术（青岛）有限公司</p>
			<p style="padding-left: 40px; font-size: 16px; font-weight: 700" class="time">{{ dataOne?.applicationTime }}</p>
			<div style="background-color: #be3a48; height: 50px; padding-right: 40px">
				<a href="http://biaobiaozhun.com" style="float: right; line-height: 50px">www.biaobiaozhun.com</a>
			</div>
		</div>
		<div class="value">
			<p class="title">房地产估价报告</p>
			<p class="bold">估价项目名称：</p>
			<p style="padding-left: 40px">{{ dataOne?.buildingAddress + dataOne?.buildingName }}</p>
			<p style="padding-left: 40px">地下四层至地上十层商业裙楼房地产之市场价值</p>
			<p class="bold">估价建议评估人：</p>
			<p style="padding-left: 40px">术木智能信息技术（青岛）有限公司</p>
			<p class="bold">估价报告出具日期：</p>
			<p style="padding-left: 40px">{{ dataOne?.applicationCnTime }}</p>
			<p class="bold">
				估价报告编号: <span>{{ dataOne?.parkingNum }}</span>
			</p>
		</div>
		<div class="value">
			<p class="title">致用户函</p>
			<p class="bold">术木智能信息技术（青岛）有限公司</p>
			<p>致用户：</p>
			<p>关于：{{ dataOne?.buildingAddress + dataOne?.buildingName }}地下四层至地上十层商业裙楼房地产(以下简称「估价对象」或「该物业」)</p>
			<p>
				术木智能信息技术（青岛）有限公司(以下简称「术木智能」或「本公司」)，于价值时点{{ dataOne?.applicationTime }}，为您在进行{{
					dataOne?.buildingName
				}}项目中提供物业价值参考依据。我们经过实地勘察，并查询、收集评估所需的市场资讯等相关资料，遵循《房地产估价规范》、《房地产估价基本术语标准》及《房地产投资信托基金物业评估指引(试行)》所载的规定，选用科学合理的估价方法，评估估价对象的房地产市场价值。估价结果如下：
			</p>
			<p>
				中国{{ dataOne?.buildingAddress + dataOne?.buildingName }}地下四层至地上十层商业裙楼房地产，总建筑面积为{{
					dataOne?.buildingArea
				}}平方米，于{{ dataOne?.applicationTime }}之房地产市场价值为人民币{{ dataOne?.evaluateDetails?.marketPrice }}元,楼面单价为{{
					dataOne?.evaluateDetails?.price
				}}元/平方米。
			</p>
			<span class="bold"> 备注：</span><span>①总估值取整至百万位。</span>
			<p>
				②本估价报告仅提供参考，市场存在波动风险，自{{
					dataOne?.applicationCnTime + '至' + dataOne?.oneYearAfterTime
				}}，若市场情况有较大波动或超过一年时需重新进行估价。
			</p>
			<p class="bold" style="padding-left: 60%">术木智能信息技术（青岛）有限公司</p>
			<p class="bold" style="padding-left: 70%">法定代表</p>
			<p class="bold" style="padding-left: 75%">{{ dataOne?.applicationCnTime }}</p>
		</div>
		<div class="value">
			<p class="bold">1.术木智能估价建议声明</p>
			<p style="text-indent: 2em">我们根据自己的专业知识和职业道德，在此郑重声明：</p>
			<p>1.1我们在本估价报告中陈述的事实是真实的和准确的，没有虚假记载、误导性陈述和重大遗漏。</p>
			<p>1.2本估价报告中的分析、意见和结论是我们独立、客观、公正的专业分析、意见和结论，但受到本估价报告中已经说明的估价假设和限制条件的限制。</p>
			<p>
				1.3我们与本估价报告中的估价对象没有现实或潜在的利益，与估价委托人及估价利害关系人没有利害关系，我们对本估价报告中的估价对象、估价委托人及估价利害关系人没有偏见。
			</p>
			<p>
				1.4我们依照中华人民共和国国家标准《房地产估价规范》、《房地产估价基本术语标准》和《房地产投资信托基金物业评估指引(试行)进行分析，形成意见和结论，撰写本估价报告。
			</p>
			<p class="bold">2.估价的假设和限制条件</p>
			<p class="bold">2.1一般假设</p>
			<p class="bold ident">本次估价的假设前提</p>
			<div class="pl-2 ident">
				<p>产权人依法拥有估价对象的合法产权，并有权连同其剩余土地使用年期转让估价对象，而不必向政府缴付额外地价或其它重大费用；</p>
				<p>
					估价对象所占宗地的所有地价、建筑工程款及市政配套费已全数缴清；估价对象的设计及建设均符合当地规划条例并已得到有关部门的批准；估价对象可自由出售；
				</p>
				<p>同时，我们假设在土地使用权的法定年期内，估价对象的产权人，对估价对象享有自由及不受干预的使用、转让、收益、处分等合法权益。</p>
				<p>在估价过程中，我们假设宗地内没有影响开发和土地规划利用的地质因素。</p>
				<p>
					估价对象实地查勘之日为2024年1月5日，价值时点为{{ dataOne?.applicationTime }}，本次估价假设估价对象于价值时点的状况与现场查勘日状况保持一致。
				</p>
				<p>除另有说明外，我们假定估价对象概无附带可能影响其价值之他项权利、限制及其他繁重支销。</p>
			</div>
			<p class="bold">未经调查确认或无法调查确认的资料数据</p>
			<div class="pl-2 ident">
				<p>
					所以在本次估价过程中，我们将以该物业公开数据作为估价依据，并不对其真实性、合法性和完整性负责，我们亦已获得评估对象的其它相关事项。我们并无理由怀疑这些资料的真实性和准确性，在估价过程中我们很大程度上接受并依赖于这些资料。同时我们获知所有相关可能影响估价的重要事实已提供给我们，并无任何遗漏。
				</p>
				<p>
					我公司估价人员曾于2024年1月18日进行实地查勘估价对象外部，并于可能情况下视察其内部，但我们并未进行结构测量及设备测试，因此无法确认估价对象是否确无腐朽、虫蛀或任何其它结构损坏，我们假设其结构及设备均可以正常使用。同时，我们未曾进行详细的实地量度以便查证估价对象之楼面面积，估价报告内所载之尺寸、量度及面积乃依据公开数据。
				</p>
			</div>
			<p class="bold">估价中未考虑的因素</p>
			<div class="pl-2 ident">
				<p>
					在估价中，我们没有考虑估价对象已经存在或将来可能承担的市场、担保、按揭或其他债项对其市场价值产生的影响，也没有考虑估价对象转让时可能发生的任何税项或费用。在估价中，我们没有考虑假设估价对象在公开市场上自由转让时，产权人凭借递延条件合约、售后租回、合作经营、管理协议等附加条件对其市场价值产生的影响。
				</p>
				<p>
					在估价中，我们没有考虑国家宏观经济政策发生重大变化以及遇有不可抗力等不可预见的因素，也没有考虑特殊的交易方式、特殊的买家偏好、未来的处置风险等对估价对象市场价值产生的影响。
				</p>
			</div>
			<p class="bold">2.2背离实际情况假设</p>

			<p class="pl-2 ident">本次估价的估价对象不存在背离事实事项，故无背离事实事项假设。</p>
			<p class="bold">2.3依据不足假设</p>

			<p class="pl-2 ident">本次估价的估价对象不存在依据不足事项，故无依据不足假设。</p>
			<p class="bold">2.4估价报告使用限制</p>

			<p class="pl-2 ident">
				本估价报告在房地产市场情况无较大波动时的有效使用期限为一年，自{{
					dataOne?.applicationCnTime + '至' + dataOne?.oneYearAfterTime
				}}，，若市场情况有较大波动或超过一年时需重新进行估价。
				如未获得我们的书面许可，本估价报告的全文或任何部份内容或任何注释，均不能以任何形式刊载于任何文件、通函或声明。
			</p>
			<p class="pl-2 ident">
				根据我们的一贯做法，我们必须申明，本估价报告仅供授权用户使用，我们不承担对任何第三者对本估价报告的全文或任何部份内容的任何责任。
			</p>
			<p class="pl-2 ident">欲了解估价对象的全面情况，应认真阅读本估价报告全文。</p>
			<p class="bold">3.估价结果报告</p>
			<p class="bold">3.1估价委托人 {{ weituip }}</p>
			<p class="bold">3.2房地产评估机构</p>
			<div class="pl-2">
				<p>机构名称:术木智能信息技术（青岛）有限公司</p>
				<p>法定代表人：</p>
				<p>地址：山东省青岛市黄岛区中联和府</p>
			</div>
			<p class="bold">3.3估价对象</p>
			<p class="ident bold">3.3.1 区域 位置</p>
			<p class="ident bold">*******估价对象坐落位置</p>
			<p class="pl-3">估价对象坐落于{{ dataOne?.buildingAddress }}，地处{{ dataOne?.businessName }}，周边商业氛围成熟。</p>
			<div style="height: 400px; width: 100%; margin-bottom: 20px">
				<baogaomap v-if="locadata.length > 0" :containerId="'map-container-1'" :zoom="13.5" :locadata="locadata" :lat="lat"> </baogaomap>
			</div>
			<p class="bold ident">*******青岛市位置及概况</p>
			<div class="pl-3 ident">
				<p>
					青岛市，山东省辖地级市、副省级市、计划单列市、特大城市，青岛都市圈核心城市，地处山东半岛南部，东濒黄海，是国务院批复确定的中国沿海重要中心城市和滨海度假旅游城市、国际性港口城市，总面积11293平方千米。青岛市是山东省经济中心、山东半岛蓝色经济区核心区龙头城市，国家重要的现代海洋产业发展先行区、东北亚国际航运枢纽、海上体育运动基地。一带一路新亚欧大陆桥经济走廊主要节点城市和海上合作战略支点。青岛也是国家海洋科研和教育中心，拥有全国30%海洋科研机构。2023年，青岛市实现地区生产总值15760.34亿元，比上年增长5.9%，三次产业结构为3.1:33.4:63.5。
				</p>
				<p>
					青岛市地处山东半岛南部，位于东经119°30′～121°00′、北纬35°35′～37°09′，东、南濒临黄海，东北与烟台市毗邻，西与潍坊市相连，西南与日照市接壤。全市总面积为11293平方千米。其中，市区（市南、市北、李沧、崂山、青岛西海岸新区、城阳、即墨等七区）为5226平方千米，胶州、平度、莱西等三市为6067平方千米。
				</p>
				<p>
					青岛地处北温带季风区，又濒临黄海，兼备季风气候与海洋气候特点，冬季气温偏高，春季回暖缓慢，夏季炎热天气较少，秋季降温迟缓。空气湿润，降水适中，雨热同季，气候宜人。
				</p>
				<p>
					青岛市年平均气温12.7℃，最热月出现在8月，月平均气温为25.3℃，最冷月出现在1月，月平均气温为-0.5℃。青岛市年平均降水量为662.1mm，全年降水量大部分集中在夏季,6—8月份的降水量为377.2mm，约占全年总降水量的57%；其中8月份降水量最多为151.1mm；出现在1970年9月4日。1月份降水量最少为11.3mm。有的月份无降水。
				</p>
				<p>
					青岛市辖7个市辖区（市南、市北、李沧、崂山、青岛西海岸新区、城阳、即墨），代管3个县级市（胶州、平度、莱西）。有街道（镇）145个、社区（村）6626个。截至2023年末，全市总人口1037.15万人，七区人口743.89万人
				</p>
				<p>
					户籍人口增量看，机械增长规模为32．81万人，占户籍人口总增量的60．4％，户籍政策改革对促进户籍人口机械增长效果显著。自然增长规模为21．54万人，年均出生率12．85‰，死亡率7．58‰，自然增长率5．28‰，二孩出生人口占比达到50．3％，生育政策有效带动人口自然增长。从人口素质看，全市人口健康素质和受教育水平进一步提升。人均预期寿命达到81．5岁，学前三年毛入学率和九年义务教育巩固率均达到99％，高中阶段毛入学率达到99％，普通高等教育在校生人数达到43万人。从人口结构看，总人口性别比更趋合理，2020年全市常住人口性别比为103．9。与第六次全国人口普查相比，15－64岁劳动力人口规模增长44万人，劳动力资源总量较为充沛。人才结构不断优化，2020年全市人才资源总量突破230万人，其中专业技术人3才总量达86万人，高技能人才总量达27.2万人。
				</p>
			</div>

			<div class="AdminSketch">
				<el-image :src="AdminSketch[dataOne?.city]?.url" />
				<div class="center bold">{{ dataOne?.city }}行政示意图</div>
			</div>
			<p class="ident bold">*******物业邻近环境及建筑物</p>
			<div class="pl-3 ident">
				<p>估价对象坐落于{{ dataOne?.buildingAddress }}，地处{{ dataOne?.businessName }}，{{ dataOne?.businessIntroduce }}</p>
				<p>
					周边分布的学校有{{ kejiao }}；医院有青岛{{ yiliao }}；休闲娱乐场所有{{ xiuxian }}等；金融机构有{{
						jinrong
					}}、中国民生银行、中国光大银行等；餐饮品种丰富，区域内均设有中西式餐馆，公共服务配套设施完善。
				</p>
			</div>
			<p class="center" style="font-weight: 700">估价对象周边环境现状照片</p>

			<div class="basic_pic" style="display: flex; justify-content: space-around; align-items: center">
				<el-image :src="url + (dataTwo?.nearbyBuildingsImages?.[0]?.coverUrl || '')" style="width: 40%" fit="cover" />
				<el-image :src="url + (dataTwo?.nearbyBuildingsImages?.[1]?.coverUrl || '')" style="width: 40%" fit="cover" />
			</div>
			<div style="display: flex; justify-content: space-around">
				<p>{{ dataTwo?.nearbyBuildingsImages?.[0]?.name }}</p>
				<p>{{ dataTwo?.nearbyBuildingsImages?.[1]?.name }}</p>
			</div>
			<p class="bold">3.3.2物业之交通条件</p>
			<div class="pl-2 ident">
				<p>估价对象交通非常便捷，地处{{ dataOne?.district }}的核心地段，公共交通发达。</p>
				<p>
					距离{{ dataTwo?.nearbyAirport?.name }}约{{ (dataTwo?.nearbyAirport?.distance / 1000)?.toFixed(1) }}公里，距{{
						dataTwo?.nearbyTrainStation?.name
					}}{{ (dataTwo?.nearbyTrainStation?.distance / 1000)?.toFixed(1) }}公里。
				</p>
				<p>周边毗邻{{ mainroad }}等多条交通主干道，自驾出行便利。</p>
				<p>
					周边公共交通设施便利临近{{ dataTwo?.nearbySubwayStation }}，邻近公交站点有{{ busname }}公交站。同行上述公交站点的公交线路有{{
						buslu
					}}交通便捷程度高。
				</p>
			</div>
			<p class="bold">3.3.3地块资料</p>
			<div class="ident">
				<p>{{ dataOne?.evaluateObjects }}</p>
				<!-- <div class="box">
					<div class="item"></div>
					<div class="item"><el-image :src="url" style="width: 100%" fit="cover" /></div>
					<div class="item"></div>
					<div class="item"><el-image :src="url" style="width: 100%" fit="cover" /></div>

					<div class="item">
						<el-image :src="url" style="width: 100%" fit="cover" />
					</div>
					<div class="item"><el-image :src="url" style="width: 100%" fit="cover" /></div>
					<div class="item"></div>
					<div class="item"><el-image :src="url" style="width: 100%" fit="cover" /></div>
					<div class="item"></div>
				</div> -->
			</div>
			<p class="bold">3.3.4产权状况</p>
			<div class="ident">
				<p class="bold">(1)中华人民共和国土地使用制度</p>
				<p class="pl-2 ident">
					根据《中华人民共和国宪法》(2004年修订案)第十条，我国建立了土地使用权与土地所有权两权分离制度。自此，有偿取得的有限年期的土地使用权均可在中国转让、赠予、出租、抵押。市级地方政府可通过协议、招标或拍卖方式等三种方式将有限年期的土地使用权出让给国内及国外机构。一般情况下，土地使用权出让金将按一次性支付，土地使用者在支付全部土地使用权出让金后，可领取《国有土地使用证》。土地使用者同时需要支付其它配套公用设施费用、开发费及拆迁补偿费用予原居民。物业建成后，当地的房地产管理部门将颁发《房屋所有权证》或《房地产权证》,以证明估价对象的土地使用权及房屋所有权。
				</p>
				<p class="bold">(2)国有土地性质</p>
				<p class="pl-2 ident">根据术木智能收录的资产数据及其他资料，估价对象的土地用途为{{ dataOne?.evaluateBuildingType }}。</p>
			</div>
			<p class="bold">3.3.5估价对象概况</p>
			<div class="ident">
				<p class="bold">(1)概况</p>
				<p class="pl-2 ident">
					估价对象所在项目为{{ dataOne?.buildingName }}，地处青岛市{{ dataOne?.district }}核心商圈—{{ dataOne?.businessName }}，是青岛市重要项目，{{ dataOne?.buildingIntroduce }}
				</p>
				<p class="bold">(2)物业占用情况</p>
				<p class="pl-2 ident">
					估价对象总建筑面积为{{ dataOne?.buildingArea }}平方米，签约租户共计{{ dataOne?.tenantTotal }}个，出租率约为{{
						dataOne?.lettingRate
					}}%。共计{{ dataOne?.parkingNum }}个车位。
				</p>
			</div>
			<p class="bold">3.4估价目的</p>
			<p class="pl-2 ident">为用户提供估价对象的房地产市场价值评估建议</p>
			<p class="bold">3.5价值时点</p>
			<p class="pl-2 ident">{{ dataOne?.applicationTime }}(用户授权日期)</p>
			<p class="bold">3.6价值类型</p>
			<div class="pl-2">
				<p class="bold">价值类型</p>
				<p class="ident">本次估价的价值类型为市场价值。</p>
				<p class="ident">
					市场价值，是指在进行了适当的市场推销(其中各方均以知晓行情、谨慎的方式参与，且无强制因素)后，自愿买家和自愿卖家以公平交易的方式，在价值时点对物业进行交易的金额。
				</p>
				<p class="bold">价值内涵</p>
				<p class="ident">
					本次估价的市场价值，是指估价对象在保持现状条件下，于价值时点{{
						dataOne?.applicationTime
					}}，房屋规划用途为商业服务，土地用途为批发零售用地，且满足本估价报告中“估价假设和限制条件”下的房地产市场价值。
				</p>
			</div>
			<p class="bold">3.7估价依据</p>
			<p class="ident">(1)国家和地方的有关法律、法规：</p>
			<p class="ident1">(a)《中华人民共和国物权法》</p>
			<p class="ident1">(b)《中华人民共和国土地管理法》</p>
			<p class="ident1">(c)《中华人民共和国城市房地产管理法》</p>
			<p class="ident1">(d)《中华人民共和国担保法》</p>
			<p class="ident">(2)技术标准：</p>
			<p class="ident1">(a)《房地产估价规范》GB/T50291-2015</p>
			<p class="ident1">(b)《房地产估价基本术语标准》GB/T50899-2013(c)《房地产投资信托基金物业评估指引(试行)》</p>
			<p class="ident1">(3)估价人员实地查勘、摄影和记录</p>
			<p class="bold">3.8估价原则</p>
			<p class="ident">我们在本次估价时除独立、客观、公正原则外还遵循了以下原则：</p>
			<p class="bold">合法原则</p>
			<p class="ident">房地产估价遵循合法原则，应当以估价对象的合法产权、合法使用、合法交易为前提进行。且估价对象具有合法的产权且用途合法。</p>
			<p class="bold">最高最佳利用原则</p>
			<p class="ident">
				由于房地产具有用途的多样性，不同的利用方式能为权利人带来不同的收益，且房地产权利人都期望从其占有的房地产上获得更多的收益，并以能满足这一目的为确定房地产利用方式的依据。所以，房地产价格是在法律上可行、技术上可能、经济上可行，经过充分合理的论证，能使估价对象价值达到最大、最可能的使用。估价对象已取得《房屋所有权证》,房屋规划用途为商业服务，符合最高最佳使用原则。
			</p>
			<p class="bold">替代原则</p>
			<p class="ident">
				替代原则的理论依据是同一市场上相同物品具有相同市场价值的经济学原理。替代原则是保证房地产估价能够通过运用市场资料进行和完成的重要理论前提：只有承认同一市场上相同物品具有相同的市场价值，才有可能根据市场资料对估价对象进行估价。
			</p>
			<p class="ident">
				替代原则也反映了房地产估价的基本原理和最一般的估价过程：房地产估价所要确定的估价结论是估价对象的客观合理价格或价值。对于房地产交易目的而言，该客观合理价格或价值应当是在公开市场上最可能形成或者成立的价格，房地产估价就是参照公开市场上足够数量的类似房地产的近期成交价格来确定估价对象的客观合理价格或者价值的。
			</p>
			<p class="bold">价值时点原则</p>
			<p class="ident">
				估价结论首先具有很强的时间相关性，这主要是考虑到资金的时间价值，在不同的时间点上发生的现金流量对其价值影响是不同的。所以，在房地产估价时统一规定：如果一些款项的发生时点与价值时点不一致，应当折算为价值时点的现值。
			</p>
			<p class="ident">
				估价结论同时具有很强的时效性，这主要是考虑到房地产市场价格的波动性，同一估价对象在不同时点会具有不同的市场价格。所以强调：估价结果是估价对象在价值时点的价格，不能将估价结果作为估价对象在其他时点的价格。
			</p>
			<p class="bold">一致性原则</p>
			<p class="ident">
				为同一估价目的，对同类物业在同一价值时点的价值进行评估，应当采用相同的估价方法。估价方法如有不同，应当在估价报告中说明理由。
			</p>
			<p class="bold">一贯性原则</p>
			<p class="ident">
				为同一估价目的，对同一物业在不同价值时点的价值进行评估，应当采用相同的估价方法。估价方法如有改变，应当在估价报告中说明理由。
			</p>
			<p class="bold">3.9估价方法</p>
			<p class="ident">房地产估价方法主要有比较法、现金流量折现法、成本法、假设开发法。</p>
			<p class="ident">
				估价对象作为收益性物业，其收益情况及相关费用均具有可预测性和持续性，符合现金流量折现法的应用条件及适用范围，因此可以采用现金流量折现法作为本次房地产价值估价的基本方法；估价对象为{{
					dataOne?.evaluateBuildingType
				}}项目， 青岛市缺乏{{
					dataOne?.lackBuildingType
				}}交易案例，根据产权人提供《项目情况说明》估价对象可进行类似“商铺式”精细产权分割，该种分割不存在法律、规划、测绘等方面的障碍，且如果在进行产权拆分变现后，由产权方进行统一经营管理，因此本次评估采用比较法作为本次房地产价值估价的辅助方法；成本法主要适用于很少发生交易而限制了比较法运用，
				又没有经济收益或没有潜在经济收益而限制了收益法运用的房地产，如学校、医院、图书馆、体育场馆、公园、行政办公楼等以公用为目的的房地产，所以不适宜采用成本法；同时估价对象已建成，所以不适宜采用假设开发法。
			</p>
			<p class="bold">比较法</p>
			<p class="ident">
				比较法是将估价对象房地产价格与在价值时点近期已经发生了交易的类似房地产进行比较，对这些类似房地产的已知价格作适当修正，以此估算出估价对象的客观合理价格的一种估价方法。
			</p>
			<p class="bold">现金流量折现法</p>
			<p class="ident">
				现金流量折现法(DCF)是预计估价对象未来的正常净收益(净现金流量),选用适当的资本化率将其折现到价值时点后累加，以此评估为估价对象的客观合理价格或价值的方法。
			</p>
			<p class="bold">3.10估价结果</p>
			<p class="ident">
				我们采用了比较法和现金流量折现法对估价对象的房地产公开市场价格进行了测算，采用现金流量折现法首先确定出一连串定期现金流量，并就该一连串现金流量采用适当贴现率，
				以制订关于估价对象租金收入现值之指标，该方法用于衡量于假设投资年期内之租金及资本增长，让投资者或业主可对物业可能带来之长期回报作出估价。
				比较法主要是通过选取实际交易案例，进行各项因素比较后，所得出的价格水平，反映了该区域同类房地产的市场价格。根据估价经验，综合分析影响房地产价格的因素，
				并结合估价对象的具体情况，估价对象作为收益性物业，其收益情况及相关费用均具有可预测性和持续性，符合现金流量折现法的应用条件及适用范围，
				采用现金流量折现法作为本次房地产价值估价的基本方法，且对其测算结果取70%的权重；估价对象为{{ dataOne?.evaluateBuildingType }}项目，青岛市缺乏{{
					dataOne?.lackBuildingType
				}}交易案例，
				根据产权人提供《项目情况说明》估价对象可进行类似“商铺式”精细产权分割，该种分割不存在法律、规划、测绘等方面的障碍，且如果在进行产权拆分变现后，
				由产权方进行统一经营管理，本次评估采用比较法作为本次房地产价值估价的辅助方法，并对其采选结果取30%的权重，两种方法测算结果加权平均作为估价对象的最终估价结果。
				于{{ dataOne?.applicationTime }}，估价对象之市场价值为人民币{{ dataOne?.evaluateDetails?.marketPrice }}元。明细如下：
			</p>
			<el-table :data="valuetable" style="width: 100%" class="table_header_bg" border>
				<el-table-column prop="yongtu" label="用途" />
				<el-table-column prop="area" label="建筑面积(平方米)" />
				<el-table-column prop="comparativeLaw" label="比较法(元)(权重30%)" />
				<el-table-column prop="discountedCashFlow" label="现金流一折现法(元)(权重70%)" />
				<el-table-column prop="marketPrice" label="市场价值(元)" />
				<el-table-column prop="price" label="单价(元/平方米)" />
			</el-table>
			<p class="ident">
				中国{{ dataOne?.buildingAddress + dataOne?.buildingName }}地下四层至地上十层商业裙楼房地产，总建筑面积为{{
					dataOne?.evaluateDetails?.area
				}}平方米，于{{ dataOne?.applicationTime }}之房地产市场价值为人民币{{ dataOne?.evaluateDetails?.marketPrice }}元,楼面单价为{{
					dataOne?.evaluateDetails?.price
				}}元/平方米。
			</p>
			<p class="bold">4.估价技术报告</p>
			<p class="bold">4.1估价对象描述与分析</p>
			<p class="bold ident">4.1.1土地实物状况分析</p>
			<p class="ident">{{ dataOne?.evaluateObjects }}</p>
			<p class="ident">
				根据估价人员的实地查勘，估价对象所占用地块呈规则四边形，地势平坦。该地块市政基础设施齐全，宗地红线外已达到七通一平(通路、通电、通信、通上水、通下水、通燃气、通热力),宗地周边商业气氛成熟，有住宅、商业、办公等物业。
			</p>
			<p class="bold ident">4.1.2建筑物实物状况分析</p>
			<p class="ident bold">(1)概况</p>
			<p class="ident">
				估价对象所在项目为{{ dataOne?.buildingName }}，地处{{ dataOne?.city + dataOne?.district }}核心商圈——{{ dataOne?.businessName }}，是{{
					dataOne?.city
				}}重要项目，{{ dataOne?.buildingIntroduce }} 
				根据委托方指示估价对象是总层数为{{
					dataThr.buildingFloors
				}}层的物业，总建筑面积{{ dataOne?.evaluateDetails?.area }}平方米，估价对象的物业管理由产权方自行管理。
			</p>
			<p class="ident bold">(2)物业占用情况</p>
			<p class="ident">{{ dataOne?.evaluateObjects }}。签约租户共计{{ dataOne?.tenantTotal }}个，出租率约为{{ dataOne?.lettingRate }}%。</p>
			<p class="ident bold">(3)配套设施</p>
			<p class="ident">安防系统：智能视频监控系统及安防报警系统；门禁对讲一卡通系统；电子巡更系统；消防应急广播。</p>
			<p class="ident">电话及网络系统：包括完备的数字语音通信系统；高可靠性的极速宽带网络；先进、高效、智能的楼宇自控系统。</p>
			<p class="ident">
				消防系统：消防报警系统，首层以上公共区域设置消火栓系统，地下车库采用自动喷淋及消火栓系统，公共部位设火灾报警与联动控制及紧急广播系统。
			</p>
			<p class="bold ident">4.1.3权益状况分析</p>
			<p class="ident">同结果报告3.3.4</p>
			<p class="bold ident">4.1.4区位状况分析</p>
			<p class="ident">估价对象坐落{{ dataOne?.buildingAddress }}，地处{{ dataOne?.businessName }}，{{ dataOne?.businessIntroduce }}</p>
			<p class="ident">
				周边分布的学校有{{ kejiao }}；医院有{{ yiliao }}；休闲娱乐场所有{{ xiuxian }}；金融机构有{{
					jinrong
				}}等；餐饮品种丰富，区域内均设有中西式餐馆，公共服务配套设施完善。
			</p>
			<p class="ident">
				估价对象所在商圈拥 有{{
					mainroad
				}}等多条城市交通主干道及多条公交线路，已构建成以高速路、快速路和主干道为支撑的立体式综合交通体系，故交通便捷度较好。
			</p>
			<p class="bold">4.2估价对象租赁现状描述与分析</p>
			<p class="ident">
				估价对象是地下四层至地上十层(结构楼层)的商业裙楼，总建筑面积{{ dataOne?.evaluateDetails?.area }}平方米，商业可出租面积为{{
					dataThr?.leasableArea
				}}平方米，已出租面积为{{ dataThr?.leasedArea }}平方米，出租率约为{{ dataOne?.lettingRate }}%,已签约租户共计{{ dataOne?.tenantTotal }}个。
			</p>
			<p class="ident bold">4.2.1收入分析</p>
			<p class="ident bold">4.2.1.1租金分析</p>
			<p class="ident">
				估价对象是地下四层至地上十层(结构楼层)的商业裙楼，总建筑面积{{ dataOne?.evaluateDetails?.area }}平方米，商业可出租面积为{{
					dataThr?.leasableArea
				}}平方米，已出租面积为{{ dataThr?.leasedArea }}平方米，出租率约为{{ dataOne?.lettingRate }}%,已签约租户共计{{
					dataOne?.tenantTotal
				}}个。截止至价值时点{{ dataOne?.applicationTime }}，估价对象平均签约租金为{{ dataThr?.rent }}元/平方米/月(使用面积),当期年租金收入约{{
					dataThr?.currentAnnualRent
				}}元。根据租期长短，部分租户有1-13个月的免租期，部分租约在租期内有不同程度的递增。
			</p>
			<p class="ident">其详细租约情况见附件一《项目情况说明》。</p>
			<p class="ident bold">4.2.1.2物业管理收入</p>
			<p class="ident">
				根据术木智能调查,估价对象由{{ dataThr?.propertyCompany }}进行物业管理并向租户收取物业管理费，由于{{
					dataThr?.propertyCompany
				}}跟产权方为关联公司，故{{
					dataThr?.propertyCompany
				}}涉及本次估价对象的物业收入和成本应按照产权方的收入和成本考量。目前估价对象租户物业管理费为{{
					dataThr?.propertyFee
				}}元/平方米/月，2024年物业管理费拟收入约为{{ dataThr?.propertyFeePseudoRevenue }}元。
			</p>
			<p class="ident bold">4.2.1.3车位收入</p>
			<p class="ident">
				根据产权人提供的《项目情况说明》,估价对象可出租地下车位数量为{{
					dataThr?.parkingNum
				}}个，主要收入形式为对外包月出租或按小时收费临时停车。根据产权方提供的资料，{{ dataThr?.year }}年车位拟收入金额约为{{
					dataThr?.propertyFeePseudoRevenue
				}}万元。
			</p>
			<p class="ident bold">4.2.2租户结构分析</p>
			<p class="ident">
				根据业主提供的资料，本项目已出租面积为{{ dataThr?.leasedArea }}平方米，出租率约为{{
					dataOne?.lettingRate
				}}%。本项目租户涉及行业范围广泛，面积占比较大的行业有{{ fenxiname }},分别占比{{ fenxibilie }},租户涉及行业比例请详见下表：
			</p>
			<p class="center bold">本项目租户行业配比</p>
			<div class="matching">
				<div class="matching-box">
					<el-table :data="zuhutable" class="zuhutable table_header_bg" border>
						<el-table-column prop="commercialForm" label="业态" />
						<el-table-column prop="merchantPercentage" label="租户占比" />
					</el-table>

					<tenantMatching :pieData="zuhutable"></tenantMatching>
				</div>
			</div>

			<p class="bold">4.3市场背景分析</p>
			<p class="bold">4.3.1青岛市经济运行状况</p>
			<p class="bold ident">4.3.1.1生产总值</p>
			<p class="ident">
				青岛市2023年的生产总值达到了15760.34亿元，按不变价格计算，比上年增长5.9%。其中，第一产业增加值492.75亿元，增加4.1%；第二产业增加值5268.39亿元，增加5.6%；第三产业增加值9999.20亿元，增长6.1%。
			</p>
			<p class="bold ident">4.3.1.2固定资产投资和房地产开发</p>
			<p class="ident">
				固定资产投资：2024年，青岛市政府设定的主要预期目标中，包括固定资产投资增长5%以上。在过去的年份中，青岛市的固定资产投资已经取得了显著增长。例如，有数据显示，2023年青岛市的固定资产投资增长达到了5%，并且有多达423个省、市重点项目完成投资超过3300亿元，显示出强劲的投资支撑力
			</p>
			<p class="ident">
				房地产开发：据统计，截止到2023年12月30日，青岛全年新建商品房网签成交112168套（不含退房），成交面积1268.83万㎡，成交总金额1855.26亿。与去年相比，成交套数上涨1.8%，但成交面积和总金额分别下降2.6%和8.8%。这些数据表明，虽然市场成交量有所波动，但整体仍保持稳定。
			</p>
			<p class="bold ident">4.3.1.3产业结构</p>
			<p class="ident">
				从产业上看，第一产业增长4.1%，第二产业增长5.6%，第三产业增长6.1%。农林牧渔业总产值955.9亿元，比上年增长5.0%。粮食播种面积、单产、总产实现“三增”，全年粮食总产量达到318.6万吨，增长2.4%。规模以上工业增加值同比增长5.8%，显示出工业经济的稳定增长态势。青岛市工业体系强健，涵盖全部31个制造业大类，规上工业企业达到4800多家，规上工业营业收入居全省首位。2023年，规模以上装备制造业增加值增长10.1%，对规模以上工业增加值增长的贡献率达到80.6%。规模上高技术制造业增加值同比增长16.3%，连续10个月保持两位数增长，对规上工业的贡献率达到32.8%。现代服务业增势良好，批发和零售业、金融业、信息传输、软件和信息技术服务业等行业均实现了快速增长。
			</p>
			<p class="bold ident">4.3.1.4居民收入水平</p>
			<p class="ident">
				青岛市2023年全体居民人均可支配收入为56961元，比上年增长6.0%。其中，城镇居民人均可支配收入为65751元，增长5.1%；农村居民人均可支配收入为29736元，增长7.3%。2023年青岛市单位就业人员年平均工资为94915元，同比增长4.7%。其中，单位在岗职工年平均工资为95020元，同比增长4.5%。城镇非私营单位就业人员年平均工资：为129241元，比上年增加5180元，同比增长4.2%。城镇私营单位就业人员年平均工资：为67944元，比上年增加3595元，同比增长5.6%。
			</p>
			<p class="bold ident">4.3.1.5人口规模和城镇化水平</p>
			<p class="ident">
				2023年末青岛全市常住人口为1037.15万人，较上一年末增长了0.28%。其中城镇常住人口812.10万人，常住人口城镇化率为78.30%，比上年末提高0.98个百分点。
			</p>
			<p class="bold ident">4.3.1.6社会消费品零售总额和居民消费价格指数</p>
			<p class="ident">
				2023年青岛市全年社会消费品零售总额为6318.9亿元，比上年增长7.3%。从消费结构来看，青岛市的网络零售额在2023年继续保持高速增长态势。前10个月青岛网络零售额达1990亿元，增长26.4%，占全省31.7%，增速高于全国网络零售额增速9个百分点。此外，青岛市在新能源汽车、家具、家用电器和音像器材等消费领域也呈现出快速增长的态势。新能源汽车零售额达到107亿元，同比增长40.2%；家具类商品增长14.7%；家用电器和音像器材类商品增长12.5%。
			</p>
			<p class="ident">
				2023年青岛市居民消费价格（CPI）同比上涨0.5%。其中，服务价格上涨1.5%，消费品价格下降0.2%。分类别看，食品烟酒价格上涨0.5%，衣着价格上涨0.7%，居住价格上涨0.6%，生活用品及服务价格持平，交通通信价格下降2.1%，教育文化娱乐价格上涨3.0%，医疗保健价格上涨0.3%，其他用品及服务价格上涨4.4%。扣除食品和能源价格后的核心CPI上涨0.9%。12月份，居民消费价格同比下降0.1%，环比上涨0.4%。
			</p>
			<p class="bold">4.3.2政策环境</p>
			<p class="bold ident">4.3.2.1宏观金融政策</p>
			<p class="center">中央人民银行下调存款准备金率</p>
			<p class="ident">
				为推动经济实现质的有效提升和量的合理增长，打好宏观政策组合拳，提高服务实体经济水平，保持银行体系流动性合理充裕，中国人民银行决定于2023年3月27日降低金融机构存款准备金率0.25个百分点（不含已执行5%存款准备金率的金融机构）。本次下调后，金融机构加权平均存款准备金率约为7.6%。
			</p>
			<p class="center">中央人民银行存贷款基准利率</p>
			<p class="ident">存持续打压存款利率确认是中长期方向。</p>
			<p class="ident">
				2023年二季度推动内地银行业利差稳中略有升的原因是存款利率大幅下行。目前存款利率已经进行两轮明显调整（即2022年9月和2023年4月）。
			</p>
			<p class="ident">2023年6月活期和定期存款利率分别降至0.23%和2.22%（同比分别下降9BP和12BP），下行方向比较明确。</p>
			<p class="ident">2024年贷款市场报价利率（LPR）为：1年期LPR为3.45%，5年期以上LPR为3.95%。</p>

			<p class="bold ident">4.3.2.2地产相关政策</p>
			<p class="ident">2024年青岛市出台了一系列房地产相关政策，主要包括以下方面：</p>
			<p class="ident">一、优化产权型人才住房政策:</p>

			<p class="ident">
				上市交易政策：按照销售时点定价的产权型人才住房，取消5年内不得上市交易的限制。自销售合同网签备案之日起5年内上市交易的，购房人才需按成交价格与购买时限定价格差价的50%缴纳土地收益；超过5年上市交易的，则不需缴纳土地收益。
			</p>
			<p class="ident">
				销售价格调整：已取得预销售许可且整盘未售出的项目，开发企业可申请重新确定销售价格并据实结算；已分配仍有剩余房源的项目，可根据楼层、朝向、位置等因素在销售价格上下20%的幅度内确定剩余房源一房一价，但总平均价格应与已确定的销售价格保持基本一致。
			</p>
			<p class="ident">二、土地供应与开发利用</p>
			<p class="ident">合理把控供地：结合区（市）商品房库存、去化及土地市场需求等情况，合理把控供地规模、结构和节奏。</p>
			<p class="ident">
				提前交付土地开发建设：对分期缴纳土地出让价款的已出让地块，受让人在签订《国有建设用地使用权出让合同》并缴纳50%出让价款后，可申请办理建设用地规划许可证、建设工程规划许可证、建设工程施工许可证等手续；付清剩余50%的出让价款后，可申请核发不动产权证书和新建商品房预售许可证。
			</p>

			<p class="ident">三、住房贷款与金融支持</p>
			<p class="ident">
				公积金贷款额度上浮：对使用公积金贷款购买首套房和二套房的贷款额度上限保持一致，多子女家庭及购买高品质住宅的家庭可享受贷款额度上浮20%的优惠。
			</p>

			<p class="ident">贷款利率调整：取消新发放首套住房商业性个人住房贷款利率下限，公积金贷款利率也进行了相应下调。</p>
			<p class="bold ident">4.3.3城市规划</p>
			<p class="ident">
				青岛作为山东省的重要城市，其城市规划的总体发展目标包括提升城市功能、优化空间布局、增强城市综合竞争力等。根据《青岛都市圈发展规划》等文件，青岛将加快基础设施互联互通、现代产业协作共兴、开放合作协同共进、生态环境共保共治、公共服务便利共享，大力提升都市圈高质量发展水平。
			</p>
			<p class="ident">
				青岛将构建核心引领、轴线展开、多点支撑的网络化、多层次发展格局。优化城市核心功能，增强高端产业引领、科技创新策源、开放枢纽门户、绿色生态宜居等功能。
			</p>
			<p class="ident">重点发展领域</p>
			<p class="ident">
				城市更新：青岛将深入实施城市更新和城市建设行动，包括城中村改造、老旧小区改造等。例如，2024年青岛计划新启动20个城中村改造，建成安置房3.1万套，并计划完成433个老旧小区改造。
			</p>
			<p class="ident">住房保障：青岛加大保障性住房建设和供给，2024年新增保障住房2.1万套左右，以满足不同层次、不同需求的居民住房需求。</p>
			<p class="ident">
				交通建设：加快轨道交通、公路、港口等基础设施建设，形成便捷高效的交通网络。例如，青岛将加快形成一小时通勤圈，提升市际公路通达能力。
			</p>
			<p class="ident">
				生态环境：青岛注重生态环境保护和治理，推动绿色低碳发展。例如，实施李村河（张村河）生态修复和环境整治提升工程，新建城市绿道100公里等。
			</p>
			<p class="ident">
				青岛市政府将制定和完善相关政策和规划，为城市规划提供指导和支持。例如，《青岛市城市更新专项规划(2021—2035年)》、《青岛市国土空间总体规划(2021—2035年)》等文件的制定和实施。
			</p>
			<!-- 没东西 -->
			<p class="ident bold">4.3.3.1地产市场调研</p>
			<p class="ident bold">4.3.3.2青岛市地产市场</p>
			<p class="ident bold">4.3.3.3房地产市场建设情况</p>
			<p class="ident">
				成交量与价格：全年新房成交量为112,184套，同比上涨约2%，显示出市场的一定活跃度。成交均价为14,681元/㎡，同比下降6%，反映了市场“以价换量”的策略。
			</p>
			<p class="ident">
				区域分布：西海岸、城阳、即墨在成交套数、成交面积上均居前三，特别是西海岸共成交37,173套，成交面积3,812,874㎡，居区域首位。从板块成交看，即墨城西、城阳高新中心及西海岸辛安成交套数居前三，成交均价在1.2万元/㎡上下。
			</p>
			<p class="ident">
				市场特点：结构性成交分化趋势明显，改善及高端项目成交占比增加，项目流速聚集度高。新房市场“以价换量”迎合供求关系变化，价格梯度线重塑，部分区域价格回到2018年。
			</p>
			<p class="ident bold">4.3.3.4房地产市场销售情况</p>
			<p class="ident">
				成交量：全年新房和二手房共成交约177,863套，其中新房成交112,184套，同比上涨约2%；二手房成交65,679套，较2022年提升约24%。新房成交均价为14,681元/㎡，同比下降6%；二手房成交均价为16,461元/㎡，也呈下降走势。
			</p>
			<p class="ident">房地产开发企业到位资金情况</p>
			<p class="ident">
				在2024年，1—4月份，全国房地产开发投资30928亿元，同比下降9.8%；其中，住宅投资23392亿元，下降10.5%。2024年5月，青岛市房地产开发投资累计614.0亿元，下降13%，其中，住宅投资503.3亿元，下降8.6%。
			</p>
			<p class="bold">4.4青岛市优质商业物业市场</p>
			<p class="ident">
				青岛商业发展萌芽于二十世纪七十年代。二十世纪九十年代，传统百货陆续涌现。进入2005年，百货业态档次及时尚度均有较大提升。2008年，购物中心类型商业成为了商业发展主流，百货业出现了购物中心化并逐渐增强体验型商业业态。目前，青岛已形成以新百-东购商圈、唐岛湾商圈为核心商圈，怀特-万达商圈为新兴商圈，建华商圈等小型商圈为区域商圈的商业布局。其中新百-东购商圈、唐岛湾商圈及建华商圈均沿中山、裕华东西中心轴分布，中心轴附近集中了青岛大多数零售商业项目。由于具有国企背景，北人集团在青岛优质商业市场占有重要地位，但是，非本地开发商和新项目的开业已经造成对青岛市传统商业的冲击，并且在青岛商业市场中，购物中心逐步成为商业开发的主要趋势。
			</p>
			<div class="AdminSketch">
				<el-image :src="tradingArea" />
				<div class="center bold">{{ dataOne?.city }}主要商圈分布图</div>
			</div>
			<p class="ident bold">{{ dataThr?.businessIntroduceList?.[0]?.businessName }}</p>

			<p class="ident">{{ dataThr?.businessIntroduceList?.[0]?.supportingProjects }}</p>
			<p class="ident bold">{{ dataThr?.businessIntroduceList?.[1]?.businessName }}</p>

			<p class="ident">{{ dataThr?.businessIntroduceList?.[1]?.supportingProjects }}</p>
			<p class="ident bold">{{ dataOne?.businessName }}</p>

			<p class="ident">{{ dataOne?.businessIntroduce }}</p>

			<p class="bold ident">4.4.1{{ dataOne?.city + dataOne?.businessName }}商业市场分析</p>
			<p class="bold ident">4.4.1.1区域概述</p>
			<div style="height: 400px; width: 100%; margin-bottom: 20px">
				<baogaomap v-if="locadata.length > 0" :containerId="'map-container-2'" :zoom="13.5" :locadata="locadata" :lat="lat"> </baogaomap>
			</div>
			<p class="bold ident">区域发展历程</p>
			<p class="ident">{{ dataOne?.businessIntroduce }}</p>
			<p class="center bold">区域内主要商业情况</p>
			<div class="matching">
				<el-table :data="shangyetable" class="shangyetable table_header_bg" border>
					<el-table-column type="index" width="50" />
					<el-table-column prop="buildingName" :width="250" label="项目名称" />
					<el-table-column prop="buildingType" :width="250" label="业态" />
					<el-table-column prop="totalArea" :width="250" label="建筑面积（平方米）" />
					<el-table-column prop="openingDate" :width="250" label="开业时间" />
				</el-table>
			</div>

			<p class="bold ident">4.4.2最高最佳利用分析</p>
			<p class="ident">
				所谓最高最佳使用是指房地产估价要以房地产的最高最佳使用为前提。最高最佳使用是估价对象的一种最可能的使用，这种最可能的使用是法律上允许、技术上可能、经济上可行，经过充分合理的论证，并能给估价对象带来最高价值的使用。它主要体现在以下几个方面：
			</p>
			<p class="ident bold">法律上允许</p>
			<p class="ident">
				即不受现时使用状况的限制，而依照法律规章、规划发展方向，按照其可能的最优用途进行估价，截至价值时点，估价对象已经取得《房屋所有权证》及《国有土地使用证》,估价对象具有合法的产权且用途合法。
			</p>

			<p class="ident bold">经济上可行</p>
			<p class="ident">
				即估价价格应是各种可能的使用方式中，以经济上有限的投入而能获得最大的收益的使用方式的估价结果。估价对象目前规划用途、产权合法、建造标准技术上能满足要求，并且其收入现值大于支出现值，具有经济可行性。
			</p>
			<p class="ident bold">价值最大化</p>
			<p class="ident">
				即在所有具有经济可行性的使用方式中，能使估价对象价值达到最大的使用方式，才是最高最佳的使用方式。估价对象规划用途为商业，上盖建筑物规划用途为商业服务，其使用方式以满足法律上许可、技术上可能、经济上可行为前提条件，经过论证可使估价对象价值得到最大化。
			</p>
			<p class="bold ident">外部环境分析</p>
			<p class="ident">
				即房地产及其内部构成要素与外部环境是否均衡或协调的问题。估价对象地处青岛长安区中山东路39号，是青岛主要商圈唐岛湾商圈核心位置，商业氛围浓厚，公共配套设施完备，周边办公及居住区集聚，对商业物业有一定的市场需求，估价对象周边对外、对内交通较好，房屋证载用途为商业服务，故符合最高最佳使用原则。
			</p>
			<p class="bold ident">估价方法中的应用</p>
			<p class="ident">
				即在房地产估价过程中应遵循最高最佳使用原则。本次估价拟采用现金流量折现法进行测算及比较法，在确定重要的参数过程中，均依据最高最佳使用原则，以使估价对象价值得到充分体现。
			</p>
			<p class="bold ident">使用前提说明与分析</p>
			<p class="ident">
				估价对象作为已建成建筑物，应以保持现状作为前提，即认为保持现状最为有利时，应以保持现状为前提条件进行估价，现状应予保持的是：现状房地产的价值大于重新立项新建房地产的价值减去拆除现有建筑物的费用及建造新建筑物的费用之后的余额。根据目前房地产市场状况并结合开发经验，以保持现状最为有利。
			</p>
			<p class="ident">
				综上所述，估价对象房屋用途为商业服务，现状用途为商业及地下车位，估价对象符合最高最佳使用原则，即保持现状继续使用为前提估价。
			</p>
			<p class="bold">4.5估价方法适用性分析</p>
			<p class="ident">
				估价人员在认真分析所掌握的资料并进行了实地查勘之后认为，估价对象为收益性物业，其收益情况及相关费用均具有可预测性和持续性，符合现金流量折现法的应用条件及适用范围，因此可以采用现金流量折现法作为本次房地产价值估价的基本方法；估价对象为{{
					dataOne?.evaluateBuildingType
				}}项目，青岛市目前缺乏{{
					dataOne?.lackBuildingType
				}}交易案例，根据产权人提供《项目情况说明》估价对象可进行类似“商铺式”精细产权分割，该种分割不存在法律、规划、测绘等方面的障碍，且如果在进行产权拆分变现后，由产权方进行统一经营管理，因此可以采用比较法作为本次房地产价值估价的辅助方法。具体步骤如下：
			</p>
			<p class="ident">
				1.比较法主要是根据市场类似成交案例的搜集，通过对区位状况、实体状况、权益状况、市场状况、交易情况等不同因素的修正，计算得出估价对象的市场价格。此方法常用于在取得可靠交易案例价格时对物业进行估值。
			</p>
			<p class="ident">
				2.现金流量折现法主要是先预测估价对象日后产生的定期现金流量，再就该预测现金流量采用适当折现率，以确定该物业收入现值之指标。对于经营性物业而言，定期现金流量一般指收入总额减空置、经营开支及其他支销。该定期经营收入净额、连同预计于预测期终时之终值估计金额，按折现率贴现至现值，该折现率为资本成本或用以转换日后应付或应收货币金额的回报率。此方法常用于对提供收入的物业进行估值。
			</p>
			<p class="ident">
				本次估价，估价人员对估价对象十年投资年期每年进行现金流量贴现分析，第十一年的收入净额及剩余土地使用年期按适当折现率资本化。估价人员在进行测算时未扣除任何收购成本及出售成本，认为此应为未来买家之考虑因素。同时假设该物业于第十年底售出，售价依据第十一年预测的年收入净额计算。此方法乃假设估价对象以现金购买，并无涉及利息及其他融资成本。
			</p>
			<p class="ident">3.采用比较法和现金流量折现法两种方法测算，并将估价结果进行加权平均，最终确定估价对象房地产价值。</p>
			<p class="bold">4.6估价测算过程</p>
			<p class="bold ident">4.6.1估价测算过程之比较法</p>
			<p class="ident bold">商业部分</p>

			<p class="ident">可比实例选取原则：区域类似、用途一致、个别条件相近、交易类型相同、成交价格正常、交易时间与价值时点接近等原则。</p>

			<p class="ident">
				确定商业房地产市场价值时，我们采用比较法，由于估价对象为大型综合购物中心项目，青岛市目前类似大型购物中心交易情况较少，故首先选择两个类似项目散售案例的比较实例作为参照，经过充分考虑各物业的差异，作出修正后得出估价物业的市场价值。具体计算过程如下：
			</p>
			<p class="ident bold">■选取可比实例</p>
			<p class="ident">经过市场调查与研究，我们最终确定了2个类似商业作为估价对象的可比实例。可比实例详情概述如下：</p>
			<p class="ident">
				可比实例选取原则：区域类似、用途一致、个别条件相近、交易类型相同、成交价格正常、交易时间与估值基准日接近等原则。确定商业房地产市场价值时，我们采用比较法，首先选择其中三个较为接近估价对象情况的比较实例作为参照，经过充分考虑各物业的差异，作出修正后得出评估物业的市场价值。具体计算过程如下：
			</p>
			<!-- <div class="" style="height: 200">计算表格</div> -->
			<div style="width: 100% !important">
				<table class="MsoNormalTable">
					<thead>
						<tr>
							<td valign="center" colspan="3" style="width: 25%" class>因素</td>
							<td valign="center" style="width: 25%" class>待估物业</td>
							<td valign="center" style="width: 25%" class>可比实例一</td>
							<td valign="center" style="width: 25%" class>可比实例二</td>
						</tr>
					</thead>

					<tbody v-if="kebitable.length > 0">
						<tr>
							<td valign="center" colspan="3" rowspan="3" class>项目名称</td>
							<td v-for="(item, index) in kebitable" :key="index" valign="center" rowspan="3" class>
								{{ item.buildingName }}
							</td>
						</tr>
						<tr></tr>
						<tr></tr>
						<tr>
							<td valign="center" colspan="3" rowspan="6" class>物业照片</td>
							<td valign="center" rowspan="6" v-for="(item, index) in kebitable" :key="index" class>
								<img :src="url + item.mainImage" v-if="isImage(item.mainImage)" style="width: 100px; height: auto" />
							</td>
						</tr>
						<tr></tr>
						<tr></tr>
						<tr></tr>
						<tr></tr>
						<tr></tr>
						<tr>
							<td valign="center" colspan="3" class>案例来源</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.caseSource }}</td>
						</tr>
						<tr>
							<td valign="center" colspan="3" class>交易时间</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.tradingHour }}</td>
						</tr>
						<tr>
							<td valign="center" colspan="3" class>交易价格(元/平方米)</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.transactionPrice }}</td>
						</tr>
						<tr>
							<td valign="center" colspan="3" class>交易情况</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.tradingSituation }}</td>
						</tr>
						<tr>
							<td valign="center" rowspan="15" style="writing-mode: vertical-lr" class>房地产状况</td>
							<td valign="center" rowspan="6" class>区位状况</td>
							<td valign="center" class>商业繁华度</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.businessProsperity }}</td>
						</tr>
						<tr>
							<td valign="center" class>交通便捷度</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.trafficConvenience }}</td>
						</tr>
						<tr>
							<td valign="center" class>基础设施完善度</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.infrastructurePerfection }}</td>
						</tr>
						<tr>
							<td valign="center" class>白然及人文环境</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.humanisticEnvironment }}</td>
						</tr>
						<tr>
							<td valign="center" class>公共服务设施状况</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.serviceConditionFacilities }}</td>
						</tr>
						<tr>
							<td valign="center" class>楼层</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.buildingFloors }}</td>
						</tr>
						<tr>
							<td valign="center" rowspan="6" class>实体状况</td>
							<td valign="center" class>商业类型</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.buildingType }}</td>
						</tr>
						<tr>
							<td valign="center" class>建筑面积(平方米)</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.buildingArea }}</td>
						</tr>
						<tr>
							<td valign="center" class>进深比</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.depthRatio }}</td>
						</tr>
						<tr>
							<td valign="center" class>配套设施设备</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.supportingFacilities }}</td>
						</tr>
						<tr>
							<td valign="center" class>内部装修</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.interiorDecoration }}</td>
						</tr>
						<tr>
							<td valign="center" class>楼龄及保养</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.buildingAgeMaintenance }}</td>
						</tr>
						<tr>
							<td valign="center" rowspan="3" class>权益状况</td>
							<td valign="center" class>土地剩余年限</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.groundRemainingAge }}</td>
						</tr>
						<tr>
							<td valign="center" class>租约限制</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.leaseRestriction }}</td>
						</tr>
						<tr>
							<td valign="center" class>规划限制条件(如容积率)</td>
							<td valign="center" v-for="(item, index) in kebitable" :key="index" class>{{ item.planningConstraint }}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div style="height: 400px; width: 100%; margin: 20px 0">
				<baogaomap
					v-if="coordinates.length > 0"
					:containerId="'map-container-3'"
					:zoom="13.3"
					:locadata="locadata"
					:coordinates="coordinates"
					:lat="lat"
				>
				</baogaomap>
			</div>
			<div class="center bold">估价对象位置</div>

			<p class="ident">对上述案例，我们从区域因素和个别因素两大方面进行了相应的调整，主要有以下调整因素：</p>
			<p class="ident bold">交易时间</p>
			<p class="ident">
				虽然我们已经尽量选取与价值时点接近的交易案例，但房地产市场在交易时点和价值时点已不完全相同，根据期间房地产市场的变化趋势，已对时间因素作出了相应调整。
			</p>
			<p class="ident bold">交易情况</p>

			<p class="ident">各可比实例均为正常交易，估价对象交易情况指数设为1,则各可比实例的交易情况指数均为1。</p>
			<p class="ident bold">商业繁华度</p>
			<p class="ident">通过比较可比实例与估价对象周边的商业氛围等做相应调整。</p>
			<p class="ident bold">交通便捷度</p>

			<p class="ident">通过考察距周边交通主干道、轨道交通、机场、火车站的距离、公交线路的数量等来考察可比实例与估价对象的优劣，并做出相应调整。</p>
			<p class="ident bold">基础设施完善度</p>
			<p class="ident">通过比较可比实例与估价对象周边的市政基础设施完善程度做相应调整。</p>
			<p class="ident bold">自然及人文环境</p>

			<p class="ident">通过比较可比实例与估价对象周边的自然景观、人文景观、商务办公氛围等做相应调整。</p>
			<p class="ident bold">公共服务设施状况</p>
			<p class="ident">通过比较可比实例与估价对象周边的商务设施及生活设施等的完备程度做相应调整。</p>
			<p class="ident bold">楼层</p>

			<p class="ident">
				对于商业物业，通常情况下，向上楼层越高，价格逐渐递减。为此，需要根据可比实例楼层情况进行相应的修正。可比实例均为首层商业，故不作调整。
			</p>
			<p class="ident bold">临路状况</p>

			<p class="ident">通过比较可比实例与估价对象所临道路情况做相应调整。</p>
			<p class="ident bold">商业类型</p>

			<p class="ident">通过比较可比实例与估价对象所在楼宇的商业类型做相应调整。</p>
			<p class="ident bold">建筑面积</p>

			<p class="ident">根据估价对象的建筑面积对各可比实例的建筑面积进行相应的修正，以反映面积大小对单价的影响。</p>
			<p class="ident bold">进深比</p>

			<p class="ident">通过比较可比实例与估价对象的进深状况做相应调整。</p>
			<p class="ident bold">配套设施设备</p>

			<p class="ident">通过对房屋空调系统、电梯、高科技通讯系统、保安系统等设施的完备程度、档次等进行对比分析，进行系数调整以反映孰优孰劣。</p>
			<p class="ident bold">内部装修</p>

			<p class="ident">通过对物业外立面、大堂、电梯厅、公共区域、内部的装修标准进行调整。</p>
			<p class="ident bold">层高</p>

			<p class="ident">通过比较可比实例与估价对象的层高高低做相应调整。</p>
			<p class="ident bold">楼龄及保养</p>

			<p class="ident">通过对房屋的使用年限、新旧程度、维护状况的考察，进行相应调整。</p>
			<p class="ident bold">使用率</p>

			<p class="ident">通过比较可比实例与估价对象的可使用建筑面积占比情况进行相应调整。</p>
			<p class="ident bold">土地剩余年限</p>

			<p class="ident">通过比较可比实例与估价对象土地的剩余使用年限长短进行相应调整。</p>
			<p class="ident bold">规划限制条件</p>

			<p class="ident">通过比较可比实例与估价对象规划限制条件(如容积率等)进行相应调整。</p>
			<p class="ident">根据估价对象与可比实例上述因素具体情况，编制可比因素修正系数表，详见下表：</p>
			<div style="width: 100%">
				<p class="center bold">可比因素修正</p>
				<table class="revisetable" style="width: 100%">
					<thead>
						<tr>
							<td valign="top" style="width: 28%" colspan="3" class>因素</td>
							<td valign="top" style="width: 24%" class>待估物业</td>
							<td valign="top" style="width: 24%" class>可比实例一</td>
							<td valign="top" style="width: 24%" colspan="2" class>可比实例二</td>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td valign="top" colspan="3" class>交易价格(元/平方米)</td>
							<td valign="top" class></td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.tradingPrice }}</td>
						</tr>
						<tr>
							<td valign="top" colspan="3" class>交易时间</td>
							<td valign="top" class>1.00</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.tradingHours }}</td>
						</tr>
						<tr>
							<td valign="top" colspan="3" class>交易情况</td>
							<td valign="top" class>1.00</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.transactionSituation }}</td>
						</tr>
						<tr>
							<td valign="top" rowspan="12" style="writing-mode: vertical-lr" class>房地产状况</td>
							<td valign="top" rowspan="7" style="writing-mode: vertical-lr" class>1.00</td>
							<td valign="top" class>商业繁华度</td>
							<td valign="top" class>1.0</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.businessProsperity }}</td>
						</tr>
						<tr>
							<td valign="top" class>交通便捷度</td>
							<td valign="top" class>1.0</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.peripheralProsperity }}</td>
						</tr>
						<tr>
							<td valign="top" class>基础设施完善度</td>
							<td valign="top" class>1.00</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.facilityConvenient }}</td>
						</tr>
						<tr>
							<td valign="top" class>自然及人文环境</td>
							<td valign="top" class>1.00</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.humanEnvironment }}</td>
						</tr>
						<tr>
							<td valign="top" class>公共服务设施状况</td>
							<td valign="top" class>1.00</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.conditionFacilities }}</td>
						</tr>
						<tr>
							<td valign="top" class>楼层</td>
							<td valign="top" class>1.00</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.floor }}</td>
						</tr>
						<tr>
							<td valign="top" class>临路状况</td>
							<td valign="top" class>1.0</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.roadConditions }}</td>
						</tr>
						<tr>
							<td valign="top" rowspan="4" style="writing-mode: vertical-lr" class>1.00</td>
							<td valign="top" class>建筑面积</td>
							<td valign="top" class>1.0</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.buildingArea }}</td>
						</tr>
						<tr>
							<td valign="top" class>配套设施设备</td>
							<td valign="top" class>1.0</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.ancillaryFacilities }}</td>
						</tr>
						<tr>
							<td valign="top" class>内部装修</td>
							<td valign="top" class>1.00</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.interiorDecoration }}</td>
						</tr>
						<tr>
							<td valign="top" class>楼龄及保养</td>
							<td valign="top" class>1.0</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.buildingAge }}</td>
						</tr>
						<tr>
							<td valign="top" rowspan="1" style="writing-mode: vertical-lr" class>1.00</td>
							<td valign="top" class>土地剩余年限</td>
							<td valign="top" class>1.00</td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.remainingYears }}</td>
						</tr>
						<tr>
							<td valign="top" colspan="3" class>修正因素合计</td>
							<td valign="top" class></td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.modifiersTotal }}</td>
						</tr>
						<tr>
							<td valign="top" colspan="3" class>权重</td>
							<td valign="top" class></td>
							<td valign="top" v-for="(item, index) in revisetable" :key="index" class>{{ item.weight }}</td>
						</tr>
						<tr>
							<td valign="top" colspan="3" class>评估单价取算数平均数 (元/平方米)</td>
							<td valign="top" colspan="3" class>{{ dataFiv?.unitPriceAvg }}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<p class="">由于可比实例一、可比实例二、与估价对象业态相似，区域因素类似，故取两个比较价值的算术平均数作为估价结果13,849元/平方米。</p>
			<p class="ident">
				综上，比较法计算估价对象总价为人民币{{ dataFiv.evaluateObjectsTotalPrice }}元，单价为人民币{{ dataFiv.unitPrice }}元/平方米。
			</p>
			<p class="bold">4.6.2估价测算过程之现金流折现法</p>
			<p class="ident">
				现金流量折现法(DCF)是预计估价对象未来的正常净收益(净现金流量),选用适当的资本化率将其折现到价值时点后累加，以此估价为估价对象的客观合理价格或价值的方法。
			</p>
			<p class="bold ident">4.6.2.1出租计划</p>
			<p class="ident">
				估价对象为经营性物业，可出租面积为{{
					dataOne?.lettingRate
				}}%。首先，对于已出租部分房地产，租赁期限内的租金采用租赁合同中约定(租约限制)的租金(即实际租金),租赁期满后，假设该部分物业会按照当时的市场租金租赁，且每次租赁租期长度为十二个月。
			</p>
			<p class="bold ident">租约限制</p>
			<p class="ident">
				根据产权人提供的《房屋租赁合同》及《项目情况说明》,估价对象总建筑面积为{{ dataOne?.buildingArea }}平方米，商业可出租面积{{
					dataThr?.leasableArea
				}}平方米，已出租面积为{{ dataThr?.leasedArea }}平方米，出租率约为{{ dataOne?.lettingRate }}%,已签约租户共计{{
					dataOne?.tenantTotal
				}}个。截止至价值时点{{ dataOne?.applicationTime }}，估价对象平均签约租金为{{ dataThr?.rent }}元/平方米/月(使用面积),当期年租金收入约{{
					dataThr?.currentAnnualRent
				}}元。
			</p>
			<p class="ident">租赁期内采用租约租金计算租赁收入，租赁期外按照市场租金水平计算租赁收入，空置部分均按照市场租金水平计算租赁收入。</p>
			<p class="bold ident">有效出租面积</p>
			<p class="ident">
				根据租赁合同约定，估价对象总建筑面积为{{ dataThr?.buildingArea }}平方米，总可出租面积为{{ dataThr?.leasableArea }}平方米，已出租面积为{{
					dataThr?.leasedArea
				}}平方米，于价值时点出租率{{ dataOne?.lettingRate }}%。
			</p>
			<p class="bold ident">出租率</p>
			<p class="ident">
				经参考戴德梁行提供的估价对象之《市场调研报告》,并结合我们对该商圈商业市场供需情况的研究，我们认为该商圈周边商业氛围成熟，长远来说估价对象{{
					dataOne?.lettingRate
				}}%出租率属于合理水平。
			</p>
			<p class="bold ident">租金增长率</p>
			<p class="ident">
				估价对象由{{ dataSix?.currentYearCnDate }}至{{
					dataSix?.tenYearAfterCnDate
				}}的市场租金的预测年度增长率，乃以戴德梁行提供之《市场调研报告》为依据，具体增长幅度如下表。
			</p>
			<div style="width: 100%">
				<table class="revisetable" style="width: 100%">
					<thead>
						<tr>
							<td valign="top" style="width: 28%" colspan="3" class>年份</td>
							<td valign="top" style="width: 24%" class>非主力店增长率</td>
							<td valign="top" style="width: 24%" class>主力店增长率</td>
							<td valign="top" style="width: 24%" colspan="2" class>备注</td>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td valign="top" colspan="3" class>第一年</td>
							<td valign="top" class></td>
							<td valign="top" class></td>
							<td valign="top" class>价值时点租金水平</td>
						</tr>
						<tr>
							<td valign="top" colspan="3" class>第二年至第十年</td>
							<td valign="top" class>{{ dataSix?.notMainRentalGrowth }}</td>
							<td valign="top" class>{{ dataSix?.mainRentalGrowth }}</td>
							<td valign="top" class></td>
						</tr>
					</tbody>
				</table>
			</div>
			<p class="ident">
				{{ dataSix?.tenYearAfterCnDate }}至收益期届满的长期年度增长率为{{
					dataSix?.mainRentalGrowth
				}},乃根据类似商业物业的发展经验及该区域商业的市场状况综合分析得出。
			</p>
			<p class="bold ident">4.6.2.2项目损益分析年总收入</p>
			<p class="ident">■商业年租金收入</p>
			<p class="ident">详见4.6.2.1出租计划分析</p>
			<p class="ident">■物业管理收入</p>
			<p class="ident">
				根据产权人介绍及提供的《物业管理合同》及2024年物业费收入统计，估价对象目前由产权方自行管理，其物业管理费属由产权方向租户收取，目前估价对象的租户物业管理费为{{
					dataThr?.propertyFee
				}}元/平方米/月，2024年物业管理费拟收入约为{{ dataThr?.propertyFeePseudoRevenue }}元。
			</p>
			<p class="ident">■运营成本</p>
			<p class="ident">指业主对商业项目进行管理及运营所需支出的费用，一般按年租金收入的一定比例计取，本次估价取8.0%。详见4.6.2.1出租计划分析</p>
			<p class="ident bold">4.6.2.3收益年期</p>
			<p class="ident">
				根据产权人提供的资料显示，该宗地的土地使用权终止日期为2049年1月10日，于价值时点{{ dataOne?.applicationTime }}，其土地剩余使用年期为{{
					dataSix?.groundRemainingAge
				}}年；估价对象于{{ dataSix?.buildingAgeMaintenance }}年建成，钢混结构，钢混结构非生产用房最高经济耐用年限为60年，至价值时点{{
					dataThr?.year
				}}年，已使用约{{ dataSix?.useAge }}年，估价对象剩余经济耐用年限为{{ dataSix?.durableAge }}年；根孰短原则，估价对象的收益年期为{{
					dataSix?.groundRemainingAge
				}}年。
			</p>
			<p class="ident bold">4.6.2.4贴现率的求取</p>
			<p class="ident">
				贴现率是用以转换日后应付或应收货币金额至现值之回报率，理论上反映资本之机会成本。估价人员通过对青岛市商业市场的物业投资者所需回报率的分析，再结合估价对象所处商圈的市场状况、租金收入及租户组合等情况确定其贴现率，其理论上与净报酬率的关系为贴现率等于净报酬率与稳定期增长率之和。
			</p>
			<p class="ident">
				采用市场提取法求取估价对象的净报酬率。市场提取法是利用与估价对象房地产具有类似收益特征的可比实例房地产的价格、净收益等资料，选用相应的报酬资本化法公式，反求出净报酬率的方法。净报酬率的求取过程见下表（备注：1、毛报酬率=年收益/房产价格=(①×12)/房地产价格
				2、净报酬率=毛报酬率×(1-②)）。
			</p>
			<div style="width: 100%">
				<table class="revisetable" style="width: 100%">
					<thead>
						<tr>
							<td valign="top" style="width: 33.33%" class>项目</td>
							<td valign="top" style="width: 33.33%" class>实例一</td>
							<td valign="top" style="width: 33.33%" class>实例二</td>
						</tr>
					</thead>
					<tbody>
						<tr v-for="(item, index) in tiexiantable" :key="index">
							<td valign="top" class>{{ item.factor }}</td>
							<td valign="top" class>{{ item.comparable1 }}</td>
							<td valign="top" class>{{ item.comparable2 }}</td>
						</tr>
						<tr>
							<td valign="top" rowspan="2" class>备注</td>
							<td valign="top" colspan="2" class>1、毛报酬率=年收益/房产价格=(①×12)/房地产价格</td>
						</tr>
						<tr>
							<td valign="top" colspan="2" class>2、净报酬率=毛报酬率×(1-②)</td>
						</tr>
					</tbody>
				</table>
			</div>
			<p class="ident">
				本次估价取各实例毛报酬率的简单算术平均数作为估计对象的毛报酬率，即
				(6.4%+6.5%)÷2=6.45%。综合考虑计算结果，本次估价采取最终毛报酬率为6.45%。故本次估价最终的净报酬率为6.45%×(1-30%)=4.52%
			</p>
			<p class="ident">估价对象稳定期所采用之增长率为3.0%,乃根据类似商业物业的发展经验及该区域商业的市场状况综合分析得出。</p>
			<p class="ident">则贴现率=净报酬率+稳定期增长率=4.52%+3.0%=7.52%</p>
			<p class="bold ident">4.6.2.6现金流量折现法计算结果</p>
			<p class="ident">
				通过现金流量折现法计算得出，「估价对象」于价值时点的房地产总价为{{ dataSix?.realEstateTotalPrice }}元，单价为{{ dataSix?.unitPrice }}元/平方米
			</p>
			<p class="bold">4.6.3估价结果确定</p>
			<p class="ident">
				我们采用了比较法和现金流量折现法对估价对象的房地产公开市场价格进行了测算，采用现金流量折现法首先确定出一连串定期现金流量，并就该一连串现金流量采用适当贴现率，以制订关于估价对象租金收入现值之指标，该方法用于衡量于假设投资年期内之租金及资本增长，让投资者或业主可对物业可能带来之长期回报作出估价。比较法主要是通过选取实际交易案例，进行各项因素比较后，所得出的价格水平，反映了该区域同类房地产的市场价格。
			</p>
			<p class="ident">
				根据估价经验，综合分析影响房地产价格的因素，并结合估价对象的具体情况，采用现金流量折现法作为本次房地产价值估价的基本方法，对其测算结果取70%的权重；并采用比较法作为本次房地产价值估价的辅助方法，对其采选结果取30%的权重，两种方法测算结果加权平均作为估价对象的最终估价结果。于{{
					dataOne?.applicationTime
				}}，估价对象之市场价值为人民币{{ dataSix?.evaluateDetails?.marketPrice }}元。
			</p>
			<p class="ident">明细如下：</p>
			<div style="width: 100%">
				<table class="revisetable" style="width: 100%">
					<thead>
						<tr>
							<td valign="top" style="width: 15%" class>用途</td>
							<td valign="top" style="width: 15%" class>建筑面积(平方米)</td>
							<td valign="top" style="width: 15%" class>比较法(元)(权重30%)</td>
							<td valign="top" style="width: 25%" class>现金流一折现法(元)(权重70%)</td>
							<td valign="top" style="width: 15%" class>市场价值(元)</td>
							<td valign="top" style="width: 15%" class>单价(元/平方米)</td>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td valign="top" class>商业</td>
							<td valign="top" class>{{ restable.area }}</td>
							<td valign="top" class>{{ restable.comparativeLaw }}</td>
							<td valign="top" class>{{ restable.discountedCashFlow }}</td>
							<td valign="top" class>{{ restable.marketPrice }}</td>
							<td valign="top" class>{{ restable.price }}</td>
						</tr>
					</tbody>
				</table>
			</div>
			<p class="bold">4.7估价结果确定</p>
			<p class="ident">
				中国{{ dataOne?.buildingAddress }}{{ dataOne?.buildingName }}地下四层至地上十层商业裙楼房地产，总建筑面积为{{
					dataOne?.evaluateDetails?.area
				}}平方米，于{{ dataOne?.applicationTime }}之房地产市场价值为人民币{{ dataSix?.realEstateTotalPrice }}元,楼面单价为{{
					dataSix?.unitPrice
				}}元/平方米。
			</p>
		</div>
	</div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { valueOne, valueTwo, valueThr, valueFor, valueFiv, valueSix } from '@/api/baogao';
import baogaomap from '../../Mapbaogao.vue';
import tenantMatching from './components/tenantMatching.vue';
import Qingdao from '@/assets/images/AdminSketch/Qingdao.png';
import Beijing from '@/assets/images/AdminSketch/Beijing.png';
import Shanghai from '@/assets/images/AdminSketch/Shanghai.png';

import tradingArea from '@/assets/images/tradingArea/Qingdao.png';
const AdminSketch = ref({
	青岛市: {
		url: Qingdao,
		title: '青岛',
	},
	北京市: {
		url: Beijing,
		title: '北京',
	},
	上海市: {
		url: Shanghai,
		title: '上海',
	},
});
const coordinates = ref([]);
const lat = ref('');
const locadata = ref([]);
const valuetable = ref([]);
const router = useRoute();
const dataOne = ref({});
const kebitable = ref([]);
const dataTwo = ref({});
const dataFor = ref({});
const dataFiv = ref({});
const dataSix = ref({});
const kejiao = ref();
const yiliao = ref();
const jinrong = ref();
const xiuxian = ref();
const buslu = ref('');
const busname = ref('');
const mainroad = ref('');
const dataThr = ref({});
const fenxiname = ref('');
const fenxibilie = ref('');
const zuhutable = ref([]);
const revisetable = ref([]);
const tiexiantable = ref([]);
const restable = ref({});
const shangyetable = ref([]);
const isImage = (value) => {
	return value && (value.endsWith('.jpg') || value.endsWith('.JPG') || value.endsWith('.jpeg') || value.endsWith('.png') || value.endsWith('.gif'));
};
const mergeLastRow = ({ row, column, rowIndex, columnIndex }) => {
	const lastRowIndex = revisetable.value.length - 1;
	if (rowIndex === lastRowIndex) {
		if (columnIndex === 1) {
			return [2, 3]; // 合并整行 (跨 4 列)
		} else {
			return [1, 2]; // 合并后的单元格不显示
		}
	}
};
const getdata = async () => {
	// 请求第一个数据源
	const res = await valueOne({ buildingId: router.query.id });
	if (res.code === 200) {
		// 设置数据
		dataOne.value = res.result;
		restable.value = res.result.evaluateDetails;
		// 初始化 valuetable 的结构并确保 evaluateDetails 存在
		if (res.result && res.result.evaluateDetails) {
			valuetable.value = [res.result.evaluateDetails];

			// 为 valuetable.value[0] 添加新的属性 yongtu
			valuetable.value[0].yongtu = '商业';
			lat.value = res.result.buildingCoordinate;
			locadata.value = [];
			res.result.bussinessScopre.forEach((item) => {
				locadata.value.push([Number(item.lng), Number(item.lat)]);
			});
		} else {
			console.error('evaluateDetails 不存在或未定义');
		}
	}
	// 请求第二个数据源
	const restwo = await valueTwo({ buildingId: router.query.id });
	if (restwo.code === 200) {
		dataTwo.value = restwo.result;
		mainroad.value = restwo.result.nearbyMainRoad
			.map((item) => {
				return item;
			})
			.join(',');
		buslu.value = restwo.result.busStopsInfo.busNumber
			.map((item) => {
				return item;
			})
			.join(',');
		busname.value = restwo.result.busStopsInfo.busStopName
			.map((item) => {
				return item;
			})
			.join(',');
		restwo.result.buildingSurroundingSupporting.map((item) => {
			if (item.type == '科教文化服务') {
				kejiao.value = item.pois
					.map((item) => {
						return item.name;
					})
					.join(',');
			}
			if (item.type == '医疗保健服务') {
				yiliao.value = item.pois
					.map((item) => {
						return item.name;
					})
					.join(',');
			}
			if (item.type == '金融保险服务') {
				jinrong.value = item.pois
					.map((item) => {
						return item.name;
					})
					.join(',');
			}
			if (item.type == '体育休闲服务') {
				xiuxian.value = item.pois
					.map((item) => {
						return item.name;
					})
					.join(',');
			}
		});
	}
	const resthr = await valueThr({ buildingId: router.query.id });
	if (resthr.code === 200) {
		dataThr.value = resthr.result;

		shangyetable.value = resthr.result.mainBusinessSituationList;
		if (resthr.result.tenantIndustryRatio) {
			fenxiname.value = resthr?.result?.tenantIndustryRatio
				?.map((item) => {
					return item.commercialForm;
				})
				.join(',');
		}

		fenxibilie.value = resthr.result.tenantIndustryRatio
			.map((item) => {
				return item.merchantPercentage;
			})
			.join('%,');
		zuhutable.value = resthr.result.tenantIndustryRatio;
	}
	const resfor = await valueFor({ buildingId: router.query.id });
	if (resfor.code === 200) {
		dataFor.value = resfor.result;
		kebitable.value = resfor.result.comparableInstancesList;
		coordinates.value = resfor.result.coordinate;
	}
	const resfiv = await valueFiv({ buildingId: router.query.id });
	if (resfiv.code === 200) {
		dataFiv.value = resfiv.result;
		revisetable.value = resfiv.result.comparableFactorCorrectionsList;
	}
	const ressix = await valueSix({ buildingId: router.query.id });
	if (ressix.code === 200) {
		dataSix.value = ressix.result;
		tiexiantable.value = ressix.result.evaluateNetInterestRateList;
		tiexiantable.value.unshift({
			buildingName: '名称',
			tradingHour: '交易日期',
			rent: '租金价格(元/平方米/月)',
			realEstatePrice: '房地产价格(元/平方米)',
			grossProfitMargin: '毛报酬率',
			yearOperatingRate: '年运营费率',
			netInterestRate: '净报酬率',
		});
		const keys = Object.keys(tiexiantable.value[0]);
		tiexiantable.value = keys.map((key) => {
			return {
				factor: tiexiantable.value[0][key],
				// subjectProperty: tiexiantable.value?.[1]?.[key],
				comparable1: tiexiantable.value?.[1]?.[key],
				comparable2: tiexiantable.value?.[2]?.[key],
			};
		});
	}
};

onMounted(() => {
	console.log(router);
	getdata();
});
const url = ref('https://static.biaobiaozhun.com/');
</script>
<style lang="less" scoped>
.matching {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-content: center;
	flex-wrap: wrap;
	.matching-box {
		width: 720px;
	}
	.shangyetable {
		width: 1050px;
	}
}
.table_header_bg {
	::v-deep .el-table__header-wrapper th {
		background-color: rgb(62, 90, 140);
		color: #fff;
	}
}
.MsoNormalTable,
.revisetable {
	border-top: 1px solid #333;
	border-left: 1px solid #333;
	border-spacing: 0;
	background-color: #fff;
	width: 100%;
	thead {
		background-color: rgb(62, 90, 140);
		color: #fff;
		font-weight: 700;
	}
	td {
		border-bottom: 1px solid #333;
		border-right: 1px solid #333;
		font-size: 13px;
		padding: 5px;
		text-align: center;
	}
}
.AdminSketch {
	width: 100%;

	display: flex;
	flex-direction: column;
	align-content: center;
	flex-wrap: wrap;
	.el-image {
		width: 100%;
		height: 300px;
		margin-bottom: 10px;
	}
}
.tit_basic {
	padding-left: 20px;
}
.main {
	width: 100%;
	height: 100%;
	padding: 20px 300px;
	background-color: #fff;
	overflow: hidden;
	box-sizing: border-box;
}
.ident {
	text-indent: 2em;
}
.ident1 {
	text-indent: 3em;
}
.pl-2 {
	padding-left: 2em;
}
.pl-3 {
	padding-left: 3em;
}
.center {
	text-align: center;
}
.value {
	width: 100%;
	height: 100%;
	background-color: #fff;
	padding: 20px 30px;
	box-sizing: border-box;
	.title {
		font-size: 20px;
		font-weight: 700;
		text-align: center;
		margin-bottom: 20px;
	}

	.bold {
		font-weight: 700;
	}
}
.box {
	// display: flex;
	// flex-wrap: wrap;
	// justify-content: space-around;
	// align-items: center;
	display: grid;
	grid-template-columns: repeat(3, 1fr); /* 创建3列，每列宽度相等 */
	grid-template-rows: repeat(3, 1fr); /* 创建3行，每行高度相等 */
	.item {
		padding: 10px; /* 设置网格项的内边距 */
		text-align: center; /* 设置文本居中 */
		.el-image {
			width: 100%;
			height: 200px;
			// margin-bottom: 20px;
		}
	}
}
.top {
	width: 100%;
	height: 100%;
	background-color: #fff;
	padding: 20px 30px;
	box-sizing: border-box;
	.el-image {
		width: 100%;
		height: 100%;
	}
}
@media screen and (max-width: 1280px) {
	.main {
		padding: 20px 200px;
	}
	.AdminSketch {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-content: center;
		flex-wrap: wrap;
		.el-image {
			width: 100%;
			height: 300px;
			margin-bottom: 10px;
		}
		.shangyetable {
			width: 100%;
		}
	}
	.matching {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-content: center;
		flex-wrap: wrap;
		.matching-box {
			width: 100%;
		}
	}
}
@media screen and (max-width: 1024px) {
	.main {
		padding: 20px 150px;
	}
	.AdminSketch {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-content: center;
		flex-wrap: wrap;
		.el-image {
			width: 100%;
			height: 300px;
			margin-bottom: 10px;
		}
		.shangyetable {
			width: 100%;
		}
	}
	.matching {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-content: center;
		flex-wrap: wrap;
		.matching-box {
			width: 100%;
		}
	}
}
@media screen and (max-width: 768px) {
	.main {
		padding: 20px 100px;
	}
}
@media screen and (max-width: 640px) {
	.main {
		padding: 20px 20px;
	}
}
</style>
