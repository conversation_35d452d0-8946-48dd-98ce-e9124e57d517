<template>
    <div style="display: flex;justify-content: center; margin-top: 30px;">
        <el-table :data="data" style="width: 90%" :stripe="true" :lazy="true">
            <el-table-column
                resizable
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              style="height: 100px"
              :formatter="formatterAmount"
            >
            </el-table-column>
        </el-table>
    </div>
</template>
<script setup>
import { defineProps } from 'vue';
const props = defineProps({
  data: {
    type: Array,
    default: []
  },
  
})
const formatterAmount=(row, column, cellValue)=> {
    if(parseFloat(cellValue) &&column.property !== 'year'){
        return parseFloat(cellValue).toFixed(2);
    }
      return cellValue;
    }
    const tableColumns = [
  // { prop: 'index', label: '序号' },
  { prop: 'year', label: '年份' },
  { prop: 'totalRevenue', label: '收入合计' },
  { prop: 'mangeExpense', label: '物业及运营管理支出' },
  { prop: 'totalTax', label: '税金合计' },
  { prop: 'borrowCost', label: '借款成本' },
  { prop: 'ebitda', label: 'EBITDA' },

]
const secured=[

]
</script>