import { fetch } from './http';

export function getCode(mobile) {
	//获取验证码
	return fetch({
		url: '/api/users/sms/code/' + mobile,
		method: 'get',
		params: mobile,
		loading: true,
	});
}
export function userPhoneLogin(params) {
	//用户登录（手机号
	return fetch({
		url: '/api/users/login',
		method: 'post',
		data: params,
		loading: true,
	});
}

export function getRegister(params) {
	//用户注册
	return fetch({
		url: '/api/user/register',
		method: 'post',
		data: params,
	});
}

export function getLogin(params) {
	//用户名登录
	return fetch({
		url: '/api/user/login',
		method: 'post',
		data: params,
	});
}

export function getForget(params) {
	//忘记密码
	return fetch({
		url: '/api/user/forget',
		method: 'post',
		data: params,
	});
}

export function getCodes(params) {
	//验证码
	return fetch({
		url: '/api/user/send-sms',
		method: 'get',
		params: params,
	});
}

export function getUserInfo() {
	//获取用户信息
	return fetch({
		url: '/api/user/info',
		method: 'get',
	});
}
