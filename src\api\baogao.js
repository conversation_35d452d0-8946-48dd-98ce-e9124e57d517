import { fetch } from './http';

export function baogao(params) {
	//概况报告
	return fetch({
		url: '/sm/v1/report/overview',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function getPopulationData(params) {
	// const base = "http://192.168.0.114:8088"
	//概况报告
	return fetch({
		url: '/sm/v1/report/population/surrounding',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function zuobiao(params) {
	//半径一公里
	return fetch({
		url: '/sm/v1/ratingPass/buildingSurroundingFacility',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function location(params) {
	//概况报告
	return fetch({
		url: '/sm/v1/report/location',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function valueOne(params) {
	//估值报告1
	return fetch({
		url: '/sm/v1/report/evaluate/basic',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function valueTwo(params) {
	//估值报告1
	return fetch({
		url: '/sm/v1/report/evaluate/location',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function valueThr(params) {
	//估值报告1
	return fetch({
		url: '/sm/v1/report/evaluate/valuation',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function valueFor(params) {
	//估值报告1
	return fetch({
		url: '/sm/v1/report/evaluate/comparableInstances',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function valueFiv(params) {
	//估值报告1
	return fetch({
		url: '/sm/v1/report/evaluate/comparableFactorCorrections',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function valueSix(params) {
	//估值报告1
	return fetch({
		url: '/sm/v1/report/evaluate/discountedCashFlow',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function business(params) {
	//招商写字楼
	return fetch({
		url: '/sm/v1/report/commerce/office',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function businesstwo(params) {
	//招商零售
	return fetch({
		url: '/sm/v1/report/commerce/retail',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function perponOwn(params) {
	//人口客流量报告
	return fetch({
		url: '/sm/v1/report/population/passengerFlow',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function perponStreet(params) {
	//人口客流量报告
	return fetch({
		url: '/sm/v1/report/population/street',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function securitybaisc(params) {
	//基础信息
	return fetch({
		url: '/sm/v1/report/securitization/basic',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function securityinterestRateEstimate(params) {
	//利率计算
	return fetch({
		url: '/sm/v1/report/securitization/interestRateEstimate',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function securitybaiscCalculation(params) {
	//分层测算
	return fetch({
		url: '/sm/v1/report/securitization/productCalculation',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function securityVenueProcess(params) {
	//发行场所
	return fetch({
		url: '/sm/v1/report/securitization/issuanceVenueProcess',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function securityBackground(params) {
	//发行背景
	return fetch({
		url: '/sm/v1/report/securitization/projectBackground',
		method: 'get',
		params: params,
		loading: true,
	});
}
export function securityArrangement(params) {
	//交易结构
	return fetch({
		url: '/sm/v1/report/securitization/tradeArrangement',
		method: 'get',
		params: params,
		loading: true,
	});
}

export function creditRisk(params) {
	//信用风险报告
	return fetch({
		url: '/api/credit-risk/credit-risk-report?ids=' + params.ids,
		method: 'post',
		data: params,
		loading: true,
	});
}
